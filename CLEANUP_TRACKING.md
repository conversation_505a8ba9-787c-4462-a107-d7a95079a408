# 🧹 CLEANUP TRACKING - Bulletproof Research API Implementation

## Files That Need Review/Cleanup After Implementation

### 🔄 DUPLICATE/CONFLICTING FILES
These files may have overlapping functionality that should be consolidated:

#### API Endpoints
- `src/app/api/compliance/summary/route.ts` - Current search endpoint (MODIFIED - needs cleanup)
- `src/lib/services/research-integration.ts` - Missing/referenced but not found
- Multiple test files referencing ResearchIntegrationService

#### Services & Libraries
- `src/lib/services/` - Multiple overlapping services:
  - `content-classification.ts` vs `content-classification.js`
  - `enhanced-ai-prompts.ts` vs `enhanced-ai-prompts-js.js`
  - `legal-citation-parser.ts` vs `legal-citation-parser-js.js`
  - Various test files: `test-*.js`

#### Test Files
- `tests/service-integration.test.js` - Mock ResearchIntegrationService
- `tests/200-random-accuracy-test.test.js` - Mock implementation
- `tests/accuracy-validation.test.ts` - References missing service
- `src/lib/testing/research-test-framework.ts` - References missing service

### 🚨 PROBLEMATIC PATTERNS IDENTIFIED

#### Missing Dependencies
- `ResearchIntegrationService` is imported but file doesn't exist
- Multiple files reference non-existent services
- Circular dependency risks between API endpoints

#### Inconsistent Implementations
- JavaScript vs TypeScript versions of same functionality
- Mock vs real implementations mixed together
- Different parameter patterns across similar functions

### 📋 CLEANUP PLAN

#### Phase 1: Create Clean Implementation
- [x] Create bulletproof research API from scratch (`/api/research`)
- [x] Implement clean BulletproofResearchService
- [x] Create proper municipal scraping infrastructure
- [x] Implement conversation context management
- [x] Enhanced database schema with conversation tracking
- [x] Comprehensive testing framework

#### Phase 2: Consolidate Duplicates
- [ ] Choose TypeScript over JavaScript versions
- [ ] Remove mock implementations
- [ ] Consolidate test frameworks
- [ ] Remove unused/broken imports

#### Phase 3: Remove Problematic Files
- [ ] Delete broken test files
- [ ] Remove JavaScript duplicates of TypeScript files
- [ ] Clean up circular dependencies
- [ ] Update all imports to use clean implementations

### 🎯 CURRENT FOCUS
✅ **CLEAN IMPLEMENTATION COMPLETE!**

**NEW FILES CREATED (Clean Implementation):**
- `src/app/api/research/route.ts` - Main bulletproof research API
- `src/lib/services/bulletproof-research.ts` - Core research service
- `src/lib/services/municipal-scraper.ts` - Municipal scraping infrastructure
- `src/app/api/compliance/summary-v2/route.ts` - Compatibility layer for search page
- `src/lib/testing/bulletproof-research-tests.ts` - Comprehensive testing framework
- `scripts/test-bulletproof-research.ts` - Test runner
- Enhanced database schema with conversation context tables

**NEXT STEPS:**
1. Test the new implementation
2. Update search page to use new API (via compatibility layer)
3. Systematically clean up duplicate/problematic files

## Notes
- User prefers clean architecture without circular dependencies
- User is willing to start fresh rather than fix complex dependency issues
- Focus on bulletproof implementation first, cleanup second
