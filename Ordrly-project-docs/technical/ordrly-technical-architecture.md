# Technical Architecture Overview
# Ordrly Municipal Research Platform

**Last Updated:** January 2025  
**Status:** Production Platform - ordrly.ai  
**Architecture Version:** 3.0

## 1. System Summary

Ordrly is a modern SaaS web platform built for municipal ordinance research through an AI-powered chat interface. The platform uses a serverless, cloud-native architecture optimized for professional-scale operation with AI-powered municipal research capabilities. The system is designed for rapid compliance research, intuitive chat experience, and seamless scaling as the user base grows from hundreds to thousands of professional users.

## 2. Current Architecture Components

### 2.1 Frontend Layer

#### Core Framework
- **Framework:** Next.js 14 with App Router (React 18)
- **Language:** TypeScript for type safety and developer experience
- **UI Library:** shadcn/ui components with Radix UI primitives
- **Styling:** Tailwind CSS with custom design system
- **State Management:** React hooks and context for local state management

#### Key Frontend Features
- **Chat Interface:** Real-time conversational AI with streaming responses
- **Address Resolution:** Smart address input with jurisdiction determination
- **Mobile-First Design:** Responsive interface optimized for field use
- **Session Management:** Persistent chat history and user sessions
- **Subscription Portal:** Integrated billing and account management
- **Authentication Flow:** Seamless login/signup with OAuth support

### 2.2 Backend Infrastructure

#### Primary Backend Services
- **Platform:** Supabase (PostgreSQL, Auth, Edge Functions, Storage)
- **Database:** PostgreSQL with pgvector for semantic search capabilities
- **Authentication:** Supabase Auth with JWT tokens and OAuth providers
- **API Layer:** Next.js API routes and Supabase Edge Functions
- **File Storage:** Supabase Storage for user data and session management

#### Core Backend Functions
- **User Authentication:** Session management and security
- **Chat Session Management:** Conversation persistence and retrieval
- **Usage Tracking:** Message limits and subscription enforcement
- **Billing Integration:** Stripe webhook processing and subscription management
- **Geographic Services:** Address resolution and jurisdiction determination

### 2.3 AI & Municipal Research Integration

#### Municipal Research API (api.ordrly.ai)
- **Primary Research Engine:** Perplexity AI for comprehensive web research
- **Analysis Engine:** Google Gemini for content analysis and response generation
- **Geographic Resolution:** Geoapify and Google APIs for jurisdiction mapping
- **Source Validation:** Automated verification of government document links

#### AI Processing Pipeline
1. **Address Input:** User provides address for jurisdiction context
2. **Jurisdiction Resolution:** Automatic determination of relevant municipal authorities
3. **Query Processing:** Natural language question analysis and context preparation
4. **Research Execution:** Perplexity AI searches for relevant municipal documents
5. **Content Analysis:** Google Gemini analyzes and synthesizes research results
6. **Response Generation:** Structured response with 5 numbered source citations
7. **Source Validation:** Verification of government document links and accuracy

### 2.4 Data Architecture

#### Database Schema (PostgreSQL)
```sql
-- User profiles and authentication
profiles (id, email, subscription_tier, trial_start_date, trial_end_date, usage_count)

-- Chat sessions and conversations
chat_sessions (id, user_id, address, jurisdiction, created_at, updated_at)
chat_messages (id, session_id, role, content, sources, timestamp)

-- Usage tracking and billing
usage_logs (id, user_id, session_id, message_count, timestamp)
subscription_events (id, user_id, event_type, stripe_data, timestamp)

-- Municipal research cache
research_cache (id, jurisdiction, query_hash, response_data, sources, created_at)
```

#### Data Flow
- **User Data:** Stored in Supabase with encryption at rest
- **Chat History:** Persistent storage with user-controlled retention
- **Research Cache:** Optimized caching for frequently requested municipal data
- **Usage Metrics:** Real-time tracking for subscription enforcement

### 2.5 Payment & Subscription Management

#### Stripe Integration
- **Subscription Management:** Automated billing for Starter and Professional tiers
- **Customer Portal:** Self-service billing management and invoice access
- **Webhook Processing:** Real-time subscription status updates
- **Trial Management:** 7-day free trial with automatic conversion prompts

#### Subscription Tiers
- **Trial:** 7-day free access with 500 messages
- **Starter:** $49/month - 500 messages, standard support
- **Professional:** $99/month - 2,000 messages, priority support
- **Business:** Custom pricing - unlimited messages, API access

### 2.6 Infrastructure & Hosting

#### Hosting Platform
- **Primary Hosting:** Vercel with global CDN and edge functions
- **Domain Management:** Custom domain (ordrly.ai) with SSL/TLS
- **Performance:** Automatic scaling and global edge optimization
- **Monitoring:** Vercel Analytics, error tracking, and performance monitoring

#### Security & Compliance
- **Data Encryption:** TLS 1.3 in transit, AES-256 at rest
- **Authentication:** JWT tokens with secure session management
- **Privacy Compliance:** GDPR/CCPA compliant data handling
- **Access Control:** Role-based permissions and subscription enforcement

## 3. System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Professional Users                       │
│           (Appraisers, Real Estate Professionals)           │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTPS/TLS 1.3
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                 Vercel Edge Network                         │
│              (Global CDN + Edge Functions)                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│              Frontend (Next.js 14 App)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Chat UI     │ │ Auth Flow   │ │ Subscription Portal     │ │
│  │ Components  │ │ Management  │ │ & Billing               │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ API Calls
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                Backend Services                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Supabase Platform                          │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │ PostgreSQL  │ │   Auth      │ │   Edge Functions    │ │ │
│  │  │ + pgvector  │ │  Service    │ │   (API Routes)      │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ Municipal   │ │   Stripe    │ │  Geographic │
│ Research    │ │   Billing   │ │   Services  │
│ API         │ │   System    │ │ (Geoapify)  │
│             │ │             │ │             │
│ ┌─────────┐ │ │             │ │             │
│ │Perplexity│ │ │             │ │             │
│ │   AI    │ │ │             │ │             │
│ └─────────┘ │ │             │ │             │
│ ┌─────────┐ │ │             │ │             │
│ │ Google  │ │ │             │ │             │
│ │ Gemini  │ │ │             │ │             │
│ └─────────┘ │ │             │ │             │
└─────────────┘ └─────────────┘ └─────────────┘
```

## 4. API Architecture

### 4.1 Municipal Research API (api.ordrly.ai)

#### Core Endpoints
```
POST /api/research/query
- Input: { address, jurisdiction, question, context }
- Output: { response, sources[], confidence, metadata }

GET /api/research/jurisdiction
- Input: { address, coordinates }
- Output: { jurisdiction, state, level, confidence }

POST /api/research/validate
- Input: { sources[], jurisdiction }
- Output: { validated_sources[], accuracy_score }
```

#### Research Pipeline
1. **Input Processing:** Address and question validation
2. **Jurisdiction Resolution:** Geographic API integration
3. **Research Execution:** Perplexity AI web search
4. **Content Analysis:** Google Gemini processing
5. **Source Validation:** Government document verification
6. **Response Assembly:** Structured output with citations

### 4.2 Platform API Integration

#### Chat Interface Endpoints
```
POST /api/chat/message
- Processes user messages and returns AI responses
- Integrates with Municipal Research API
- Handles usage tracking and subscription limits

GET /api/chat/sessions
- Retrieves user chat history
- Supports pagination and filtering
- Respects user privacy settings

POST /api/chat/session
- Creates new chat sessions with address context
- Initializes jurisdiction resolution
- Sets up conversation context
```

## 5. Performance & Scalability

### 5.1 Performance Metrics
- **Response Time:** <3 seconds average for chat responses
- **Concurrent Users:** Support for 100+ simultaneous users (current scale)
- **Database Performance:** Optimized queries with pgvector indexing
- **CDN Performance:** Global edge caching for static assets

### 5.2 Scalability Design
- **Horizontal Scaling:** Serverless architecture with automatic scaling
- **Database Optimization:** Connection pooling and query optimization
- **Caching Strategy:** Multi-layer caching for research results
- **Load Distribution:** Global CDN with edge function distribution

### 5.3 Monitoring & Observability
- **Application Monitoring:** Vercel Analytics and error tracking
- **Database Monitoring:** Supabase performance metrics
- **User Analytics:** Privacy-focused usage tracking
- **Alert Systems:** Automated alerts for performance degradation

## 6. Security Architecture

### 6.1 Authentication & Authorization
- **Multi-Factor Authentication:** Email/password and OAuth providers
- **Session Management:** Secure JWT tokens with rotation
- **Role-Based Access:** Subscription tier enforcement
- **API Security:** Rate limiting and request validation

### 6.2 Data Protection
- **Encryption:** TLS 1.3 in transit, AES-256 at rest
- **Privacy Controls:** User-controlled data retention and deletion
- **Compliance:** GDPR/CCPA compliant data handling
- **Audit Logging:** Comprehensive security event logging

### 6.3 Infrastructure Security
- **Network Security:** VPC isolation and firewall protection
- **Access Control:** Principle of least privilege
- **Vulnerability Management:** Regular security updates and patches
- **Incident Response:** Automated security monitoring and alerting

## 7. Development & Deployment

### 7.1 Development Workflow
- **Version Control:** Git with feature branch workflow
- **Code Quality:** TypeScript, ESLint, and automated testing
- **CI/CD Pipeline:** Automated testing and deployment via Vercel
- **Environment Management:** Separate dev, staging, and production environments

### 7.2 Deployment Architecture
- **Production:** Vercel with custom domain and SSL
- **Database:** Supabase managed PostgreSQL with backups
- **Monitoring:** Real-time performance and error tracking
- **Rollback Strategy:** Instant rollback capabilities with Vercel

---

## Document Information

- **Document Type:** Technical Architecture
- **Company:** Ordrly AI LLC
- **Platform:** ordrly.ai
- **Architecture Date:** January 2025
- **Status:** Production Platform
- **Next Review:** Quarterly

**Note:** This technical architecture reflects the current production implementation of Ordrly as a municipal ordinance research platform with chat UI/API architecture serving professional users.
