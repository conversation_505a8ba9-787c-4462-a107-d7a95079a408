# Ordrly Project Structure

This document outlines the organized structure of the Ordrly.ai project after cleanup.

## 📁 Root Directory Structure

```
ordrly/
├── README.md                    # Main project documentation
├── package.json                 # Node.js dependencies and scripts
├── package-lock.json           # Locked dependency versions
├── next.config.js              # Next.js configuration
├── tailwind.config.ts          # Tailwind CSS configuration
├── tsconfig.json               # TypeScript configuration
├── playwright.config.ts        # Playwright testing configuration
├── postcss.config.mjs          # PostCSS configuration
├── eslint.config.mjs           # ESLint configuration
├── next-env.d.ts               # Next.js TypeScript declarations
├── tsconfig.tsbuildinfo        # TypeScript build cache
│
├── 📁 src/                     # Source code
│   ├── app/                    # Next.js app directory
│   ├── components/             # React components
│   ├── contexts/               # React contexts
│   ├── lib/                    # Utility libraries
│   └── middleware.ts           # Next.js middleware
│
├── 📁 public/                  # Static assets
│   ├── favicon files           # Website icons
│   ├── logo files              # Ordrly branding
│   └── manifest.json           # PWA manifest
│
├── 📁 supabase/               # Database and backend
│   ├── migrations/             # Database migrations
│   ├── seed.sql               # Database seed data
│   └── setup-ordinance-cache.sql # Cache setup
│
├── 📁 scripts/                # Utility scripts
│   ├── setup-*.js             # Setup scripts
│   ├── test-*.js              # Testing scripts
│   └── verify-*.js            # Verification scripts
│
├── 📁 testing/                # Comprehensive test suites
│   ├── 00-master-plan/        # Testing strategy
│   ├── 01-epic1-*/            # Epic-specific tests
│   ├── ...                    # More epic tests
│   └── 99-integration-tests/  # Integration tests
│
├── 📁 docs/                   # Documentation
│   ├── analysis/              # Analysis documents
│   ├── debug-images/          # Debug screenshots
│   ├── implementation-guides/ # Implementation docs
│   ├── system-guides/         # System documentation
│   ├── testing-guides/        # Testing documentation
│   └── *.md                   # Business documents
│
├── 📁 marketing/              # Marketing materials
│   ├── blog/                  # Blog content
│   ├── n8n-workflows/         # Automation workflows
│   ├── n8n-files/             # n8n configuration
│   ├── DAY1README.md          # Day 1 completion
│   ├── DAY2README.md          # Day 2 completion
│   └── *.md                   # Marketing content
│
├── 📁 test-files/             # Test utilities
│   ├── test-*.js              # Test scripts
│   ├── epic5-*.txt            # Epic 5 results
│   └── *.html                 # Test HTML files
│
├── 📁 test-results/           # Test output
├── 📁 playwright-report/      # Playwright test reports
└── 📁 node_modules/           # Dependencies
```

## 📋 Directory Purposes

### `/src/` - Application Source Code
- **`app/`** - Next.js 13+ app router pages and API routes
- **`components/`** - Reusable React components
- **`contexts/`** - React context providers
- **`lib/`** - Utility functions and configurations
- **`middleware.ts`** - Next.js middleware for auth/routing

### `/docs/` - Documentation Hub
- **`analysis/`** - Performance analysis, model comparisons
- **`implementation-guides/`** - Epic implementation documentation
- **`system-guides/`** - Stripe, email, security guides
- **`testing-guides/`** - Testing strategies and guides
- **`debug-images/`** - Screenshots for debugging
- **Business docs** - Plans, agreements, policies

### `/marketing/` - Marketing & Growth Engine
- **`blog/`** - SEO blog content and outlines
- **`n8n-workflows/`** - Automation workflow configurations
- **`DAY1README.md`** - Day 1 marketing implementation
- **`DAY2README.md`** - Day 2 usage alerts system
- **Marketing content** - Ad hooks, testimonials, headlines

### `/testing/` - Comprehensive Test Suite
- **Epic-specific folders** - Tests organized by development epic
- **`00-master-plan/`** - Overall testing strategy
- **`99-integration-tests/`** - End-to-end integration tests
- **Playwright specs** - Automated browser testing

### `/scripts/` - Utility Scripts
- **Setup scripts** - Database, OAuth, environment setup
- **Test scripts** - API testing, functionality verification
- **Verification scripts** - Environment and configuration checks

### `/supabase/` - Database & Backend
- **`migrations/`** - Database schema changes
- **`seed.sql`** - Initial data setup
- **Cache setup** - Ordinance caching configuration

### `/test-files/` - Testing Utilities
- **Test scripts** - Individual feature testing
- **HTML test files** - UI component testing
- **Epic results** - Test output and results

## 🎯 Key Benefits of This Structure

### ✅ **Organized by Purpose**
- Clear separation between source code, documentation, testing, and marketing
- Easy to find relevant files for specific tasks

### ✅ **Scalable**
- New features can be added to appropriate directories
- Documentation grows alongside implementation

### ✅ **Developer Friendly**
- Standard Next.js structure maintained
- Clear separation of concerns
- Easy onboarding for new developers

### ✅ **Marketing Focused**
- Dedicated marketing directory for growth engine
- Day-by-day implementation tracking
- Automation workflows organized

### ✅ **Testing Comprehensive**
- Epic-based test organization
- Multiple testing approaches supported
- Clear test result tracking

## 🔄 Maintenance Guidelines

### Adding New Features
1. **Source code** → `/src/app/` or `/src/components/`
2. **Documentation** → `/docs/implementation-guides/`
3. **Tests** → `/testing/` in appropriate epic folder
4. **Marketing content** → `/marketing/`

### Documentation Updates
- **Implementation guides** → `/docs/implementation-guides/`
- **System changes** → `/docs/system-guides/`
- **Testing procedures** → `/docs/testing-guides/`
- **Business updates** → `/docs/` root level

### Marketing Growth Engine
- **Daily progress** → `/marketing/DAY[X]README.md`
- **Automation workflows** → `/marketing/n8n-workflows/`
- **Content creation** → `/marketing/blog/`
- **Campaign materials** → `/marketing/` root level

This structure supports the rapid development and marketing growth of Ordrly.ai while maintaining organization and clarity for all team members.
