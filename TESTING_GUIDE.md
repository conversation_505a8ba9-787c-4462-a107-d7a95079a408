# 🧪 MUNICIPAL API INTEGRATION TESTING GUIDE

## Prerequisites

Before testing, ensure you have:

1. **Municipal Research API** cloned and ready
2. **API Keys** configured
3. **Environment variables** set up
4. **Dependencies** installed

## Step 1: Set Up Municipal Research API

```bash
# Navigate to the reference API
cd reference/municipal-research-api

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
```

Edit `.env` with your API keys:
```env
OPENAI_API_KEY=your_openai_api_key
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
GOOGLE_GEOCODING_API_KEY=your_geocoding_api_key

# Supabase (use your Ordrly project)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

PORT=3001
```

## Step 2: Start Municipal API

```bash
# In reference/municipal-research-api directory
npm run dev
```

The API should start on `http://localhost:3001`

Verify it's running:
```bash
curl http://localhost:3001/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-XX...",
  "version": "1.0.0",
  "environment": "development"
}
```

## Step 3: Configure Ordrly Environment

In your main Ordrly project, update `.env`:

```env
# Add these lines
MUNICIPAL_API_KEY=your_municipal_api_key_here
MUNICIPAL_API_URL=http://localhost:3001
```

**Note:** You'll need to create an API key in the municipal API first.

## Step 4: Create Municipal API Key

The municipal API needs an API key. You can either:

### Option A: Use Test Key (for testing)
```env
MUNICIPAL_API_KEY=test-api-key-ordrly-2024
```

### Option B: Create Real API Key
1. Use the municipal API's auth endpoints
2. Or add a test key directly to the database

## Step 5: Run Integration Tests

```bash
# In your main Ordrly project
npm run test:municipal

# Or run the test script directly
npx tsx scripts/test-municipal-integration.ts
```

## Step 6: Manual Testing

### Test 1: Municipal API Direct
```bash
curl -X POST http://localhost:3001/api/v1/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "address": "123 Main St, Georgetown, MI",
    "query": "What is the maximum height for a fence?"
  }'
```

### Test 2: Ordrly Research Endpoint
```bash
# Start your Ordrly server
npm run dev

# Test the endpoint (in another terminal)
curl -X POST http://localhost:3000/api/research \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_ordrly_api_key" \
  -d '{
    "query": "What is the maximum fence height?",
    "address": "123 Test St, Georgetown, MI"
  }'
```

## Expected Test Results

### ✅ Success Indicators:
- Municipal API health check passes
- Direct municipal API calls return research results
- Ordrly client can call municipal API
- Adapter transforms responses correctly
- Full integration through `/api/research` works
- Auth/middleware validates users properly

### ❌ Common Issues:

**Municipal API not starting:**
- Check API keys in `.env`
- Verify Supabase connection
- Check port 3001 is available

**API key authentication fails:**
- Verify `MUNICIPAL_API_KEY` is set
- Check API key exists in municipal API database
- Ensure correct header format (`X-API-Key`)

**Network connection issues:**
- Verify `MUNICIPAL_API_URL=http://localhost:3001`
- Check both services are running
- Test with `curl` first

**Response format errors:**
- Check municipal API returns expected structure
- Verify adapter transforms correctly
- Validate Ordrly response format

## Step 7: Validate Full Flow

Test the complete user flow:

1. **User Authentication** - Session or API key
2. **Usage Limits** - Check user can make requests
3. **Location Resolution** - Address to jurisdiction
4. **Municipal API Call** - Research request
5. **Response Transformation** - Municipal → Ordrly format
6. **Quality Metrics** - Logging and tracking

## Step 8: Performance Testing

Test with various scenarios:
- **Cache hits** - Same query twice
- **Different jurisdictions** - Various cities
- **Complex queries** - Multi-part questions
- **Error handling** - Invalid addresses, API failures

## Next Steps

Once all tests pass:
1. ✅ Municipal API integration is working
2. ✅ Auth/middleware flow is validated
3. ✅ Ready to update Chat UI
4. ✅ Ready to update Search Page

## Troubleshooting

### Debug Mode
Set environment variable for detailed logging:
```env
DEBUG=municipal-api:*
```

### Check Logs
- Municipal API logs (port 3001 terminal)
- Ordrly API logs (port 3000 terminal)
- Browser network tab for client requests

### Common Commands
```bash
# Check if municipal API is running
lsof -i :3001

# Check if Ordrly is running  
lsof -i :3000

# Test municipal API health
curl http://localhost:3001/health

# Test Ordrly API health
curl http://localhost:3000/api/health
```
