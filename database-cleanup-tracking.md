# 🗄️ DATABASE CLEANUP TRACKING DOCUMENT

## 📊 Overview
- **Total Tables:** 97 (after Phase 1 cleanup of 6 tables)
- **Validated:** 4
- **Remaining:** 93
- **Safe to Delete:** 1
- **Must Keep:** 3

## 🎯 Validation Status Legend
- ✅ **KEEP** - Active usage confirmed in current codebase
- ❌ **DELETE** - No active usage, safe to remove
- 🔍 **PENDING** - Not yet validated
- ⚠️ **REVIEW** - Needs additional investigation

---

## 📋 COMPLETE TABLE VALIDATION LIST

### A
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| admin_access_log | ✅ KEEP | Active admin logging system | Admin chat interface, export API, Epic 10 |
| admin_actions | ✅ KEEP | Active admin audit logging | Reindex system, document uploads, Epic 11 |
| api_keys | ✅ KEEP | Active API authentication system | Business API, research API, Epic 5 |
| article_ratings | ✅ KEEP | Active rating system for knowledge base | Full API, Epic 7, RLS policies |
| automation_jobs | ✅ KEEP | Has RLS policies, cron job references | Security files, Epic 7 implementation |
| automation_logs | ✅ KEEP | Active logging system for chat and automation | Edge functions, chat logger, Epic 13 |

### B
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| behavior_email_templates | ✅ KEEP | RLS policies, email system integration | Security files, email automation |
| blog_posts | ✅ KEEP | Full API + admin interface | /api/blog/posts, /admin/blog |
| boundary_changes | ❌ DELETE | Only in docs, no active code | Documentation only, no implementation |

### C
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| chat_conversations | ✅ KEEP | Core chat system table | Central to entire chat functionality |
| chat_feedback | ✅ KEEP | Active feedback system for chat messages | Full API, Epic 10, admin analytics |
| chat_generated_notes | ✅ KEEP | Active note generation system | Full API, Epic 9, note management |
| chat_messages | 🔍 PENDING | | |
| compliance_knowledge | 🔍 PENDING | | |
| content_approvals | 🔍 PENDING | | |
| content_calendar | 🔍 PENDING | | |
| content_change_log | 🔍 PENDING | | |
| content_drafts | 🔍 PENDING | | |
| content_ideas | 🔍 PENDING | | |
| content_items | 🔍 PENDING | | |
| content_outlines | 🔍 PENDING | | |
| content_syndication_queue | 🔍 PENDING | | |
| content_versions | 🔍 PENDING | | |
| contribution_files | 🔍 PENDING | | |
| conversation_sessions | 🔍 PENDING | | |

### D-F
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| data_exports | 🔍 PENDING | | |
| document_staleness_tracking | 🔍 PENDING | | |
| email_campaigns | 🔍 PENDING | | |
| email_events | 🔍 PENDING | | |
| email_templates | 🔍 PENDING | | |
| faqs | 🔍 PENDING | | |
| feature_usage | 🔍 PENDING | | |
| feedback | 🔍 PENDING | | |

### G-K
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| jurisdiction_filters | 🔍 PENDING | | |
| jurisdiction_hierarchy | 🔍 PENDING | | |
| jurisdiction_precedence | 🔍 PENDING | | |
| knowledge_base_articles | 🔍 PENDING | | |
| knowledge_performance_alerts | 🔍 PENDING | | |
| knowledge_performance_metrics | 🔍 PENDING | | |
| knowledge_refresh_jobs | 🔍 PENDING | | |

### L-O
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| launch_announcements | 🔍 PENDING | | |
| marketing_settings | 🔍 PENDING | | |
| migration_history | 🔍 PENDING | | |
| municipal_api_usage | 🔍 PENDING | | |
| municipal_research_analytics | 🔍 PENDING | | |
| municipal_research_cache | 🔍 PENDING | | |
| municipal_sources | 🔍 PENDING | | |
| ordinance_clauses | 🔍 PENDING | | |
| ordinance_sources | 🔍 PENDING | | |
| ordinance_versions | 🔍 PENDING | | |
| overlay_conflicts | 🔍 PENDING | | |

### P-R
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| pending_updates | 🔍 PENDING | | |
| privacy_exports | 🔍 PENDING | | |
| profiles | 🔍 PENDING | | |
| prompt_templates | 🔍 PENDING | | |
| quality_alerts | 🔍 PENDING | | |
| red_flags | 🔍 PENDING | | |
| referrals | 🔍 PENDING | | |
| regions | 🔍 PENDING | | |
| research_quality_metrics | 🔍 PENDING | | |
| research_sessions | 🔍 PENDING | | |
| research_test_cases | 🔍 PENDING | | |
| research_topics | 🔍 PENDING | | |

### S
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| saved_search_folders | 🔍 PENDING | | |
| saved_searches | 🔍 PENDING | | |
| search_history | 🔍 PENDING | | |
| source_quality_metrics | 🔍 PENDING | | |
| source_verification_log | 🔍 PENDING | | |
| spatial_ref_sys | 🔍 PENDING | | |
| support_tickets | 🔍 PENDING | | |
| syndication_metrics | 🔍 PENDING | | |

### T-Z
| Table Name | Status | Validation Notes | Evidence |
|------------|--------|------------------|----------|
| test_execution_results | 🔍 PENDING | | |
| testimonials | 🔍 PENDING | | |
| trial_codes | 🔍 PENDING | | |
| unanswered_queries | 🔍 PENDING | | |
| usage_alerts | 🔍 PENDING | | |
| user_contributions | 🔍 PENDING | | |
| user_preferences | 🔍 PENDING | | |
| user_shortcuts | 🔍 PENDING | | |
| video_assets | 🔍 PENDING | | |

---

## 📝 VALIDATION METHODOLOGY

### For Each Table:
1. **Codebase Search** - Search entire codebase for table references
2. **API Endpoints** - Check for any API routes using the table
3. **Database Queries** - Look for .from() calls or SQL queries
4. **TypeScript Interfaces** - Check for type definitions
5. **Migration Files** - Verify table creation and usage
6. **Documentation** - Distinguish between docs and actual implementation

### Evidence Categories:
- **Active API** - Has working API endpoints
- **Database Queries** - Used in active database operations  
- **RLS Policies** - Has security policies implemented
- **Migration Usage** - Referenced in active migrations
- **Documentation Only** - Only mentioned in docs/comments
- **No References** - No usage found anywhere

---

## 🎯 NEXT STEPS

1. **Continue Validation** - Work through tables alphabetically
2. **Update Status** - Mark each table as KEEP/DELETE/REVIEW
3. **Document Evidence** - Record specific files/lines where usage found
4. **Phase Planning** - Group safe deletions into phases
5. **Execute Cleanup** - Delete confirmed dead tables in batches

---

## 📊 PROGRESS TRACKING

### Completed Validations:
- [x] automation_jobs (KEEP)
- [x] behavior_email_templates (KEEP)
- [x] blog_posts (KEEP)
- [x] boundary_changes (DELETE)
- [x] admin_access_log (KEEP)
- [x] admin_actions (KEEP)
- [x] api_keys (KEEP)
- [x] article_ratings (KEEP)
- [x] automation_logs (KEEP)
- [x] chat_conversations (KEEP)
- [x] chat_feedback (KEEP)
- [x] chat_generated_notes (KEEP)

### Current Focus:
- [ ] chat_messages
- [ ] compliance_knowledge
- [ ] content_approvals
- [ ] content_calendar

### Deletion Phases:
**Phase 1:** ✅ COMPLETED (6 tables deleted)
**Phase 2:** TBD (after full validation)
**Phase 3:** TBD (after full validation)
