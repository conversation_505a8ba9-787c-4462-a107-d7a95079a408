# New Pricing Structure Implementation Guide

## 🎯 Overview

We've successfully implemented Option 2 with automatic 7-day trials and a strategic pricing structure designed to get users in at $49 and push them to $99.

## 💰 New Pricing Structure

### **Starter Plan - $49/month ($490/year)**
- 500 messages per month
- AI chat assistance
- Municipal compliance research
- Save & organize searches
- Source links to ordinances
- Standard email support
- Mobile-friendly interface

### **Professional Plan - $99/month ($990/year)**
- 2,000 messages per month
- AI chat assistance
- Municipal compliance research
- Save & organize searches
- Priority email support
- Faster response times
- Enhanced source analysis
- Mobile-friendly interface

### **Business Plan - Custom Pricing**
- Everything in Professional
- API access & documentation
- White-label solutions
- Custom integrations
- Dedicated account manager
- 99.9% SLA guarantee
- Custom data exports
- Priority feature requests
- Contact: <EMAIL>

### **7-Day Free Trial**
- Automatic assignment to new users
- Full Starter plan access (500 messages)
- No credit card required
- Clear trial countdown and usage tracking
- Seamless conversion prompts

## 🔧 Required Stripe Setup

### 1. Create New Stripe Products

You need to create these products in your Stripe Dashboard:

**Starter Plan:**
- Product Name: "Ordrly Starter"
- Monthly Price: $49.00 USD
- Annual Price: $490.00 USD (17% savings)

**Professional Plan:**
- Product Name: "Ordrly Professional" 
- Monthly Price: $50.00 USD
- Annual Price: $500.00 USD (17% savings)

### 2. Environment Variables to Add

Add these to your `.env.local` and production environment:

```bash
# New Pricing Structure
STRIPE_PRICE_STARTER_MONTHLY=price_your_starter_monthly_id
STRIPE_PRICE_STARTER_ANNUAL=price_your_starter_annual_id
STRIPE_PRICE_PROFESSIONAL_MONTHLY=price_your_professional_monthly_id
STRIPE_PRICE_PROFESSIONAL_ANNUAL=price_your_professional_annual_id

# Public versions for client-side
NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY=price_your_starter_monthly_id
NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL=price_your_starter_annual_id
NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_MONTHLY=price_your_professional_monthly_id
NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_ANNUAL=price_your_professional_annual_id
```

### 3. Database Schema Updates

Add these columns to your `profiles` table:

```sql
-- Add trial tracking columns
ALTER TABLE profiles 
ADD COLUMN trial_start_date TIMESTAMPTZ,
ADD COLUMN trial_end_date TIMESTAMPTZ;

-- Update subscription_tier enum to include new tiers
ALTER TYPE subscription_tier ADD VALUE 'trial';
ALTER TYPE subscription_tier ADD VALUE 'trial_expired';
ALTER TYPE subscription_tier ADD VALUE 'starter';
ALTER TYPE subscription_tier ADD VALUE 'professional';
```

## 🚀 What's Been Implemented

### ✅ Completed Features

1. **Updated Pricing Page**
   - Beautiful new design with glass morphism effects
   - Annual/monthly billing toggle with 17% savings
   - Clear trial messaging
   - Improved FAQ section

2. **Tier Configuration System**
   - New trial, starter, and professional tiers
   - Trial expiration handling
   - Usage limits and feature access controls

3. **Trial Management System**
   - Automatic trial assignment for new users
   - Trial status tracking and expiration
   - Usage monitoring (500 message limit)
   - Conversion to paid plans

4. **UI Components**
   - TrialBanner component with multiple variants
   - Updated UpgradeButton for new plan types
   - Trial countdown and usage indicators

5. **Stripe Integration Updates**
   - Support for new pricing structure
   - Updated checkout flows
   - Backward compatibility with legacy plans

### 🔄 Next Steps Required

1. **Create Stripe Products** (Manual Step)
   - Log into Stripe Dashboard
   - Create the 4 new price objects
   - Copy the price IDs to environment variables

2. **Database Migration** (Manual Step)
   - Run the SQL commands above
   - Update existing users if needed

3. **Test the Flow**
   - Sign up new user → should get trial automatically
   - Test trial countdown and usage tracking
   - Test upgrade flow to paid plans
   - Verify billing works correctly

## 🎨 UI/UX Features for Stickiness

### Trial Experience
- **Prominent trial countdown** in header/dashboard
- **Usage dashboard** showing "247/1,000 calls used"
- **Smart upgrade prompts** when approaching limits
- **Value demonstration** during trial period

### Conversion Strategy
- **$49 entry point** removes price objection
- **500 call limit** creates natural upgrade pressure
- **Clear value proposition** for Professional tier
- **No free tier** ensures all users are invested

## 📊 Business Logic

### Trial Assignment
- New users automatically get 7-day trial
- Full Starter plan access during trial
- No credit card required initially
- Trial expires to "trial_expired" state

### Upgrade Triggers
- Usage approaching 1,000 calls
- Trial expiration countdown
- Feature limitations in Starter plan
- Clear ROI messaging for Professional

### Pricing Psychology
- **$15 Starter**: Low barrier to entry, gets users paying
- **$50 Professional**: 3.3x price jump encourages quick upgrade
- **Annual discount**: 17% savings encourages longer commitment

## 🔒 Security Considerations

- Trial users have limited access after expiration
- Usage tracking prevents abuse
- Proper tier validation on all API calls
- Secure trial-to-paid conversion flow

## 📈 Success Metrics to Track

1. **Trial Conversion Rate**: % of trials that convert to paid
2. **Starter → Professional Upgrade Rate**: % that upgrade within 30 days
3. **Usage Patterns**: How quickly users hit 1,000 call limit
4. **Churn Rate**: Monthly/annual retention by tier
5. **Revenue Per User**: Average revenue across tiers

This implementation creates a sticky product with clear upgrade paths and removes the "freeloader" problem while maintaining low barrier to entry.
