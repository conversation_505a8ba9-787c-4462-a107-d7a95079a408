Epic,Story ID,Story Title,Context & Implementation Details,Acceptance Criteria
EPIC 1: Address Lookup & Compliance Engine,1.1,Basic Address Search,"User enters address, system geocodes it and pulls local, state, federal sources",Address input retrieves property location and source mapping
EPIC 1: Address Lookup & Compliance Engine,1.2,Regulation Aggregation,"Pull from Municode, eCode360, LegiScan, and other public APIs",Each address yields a list of applicable regulations with source links
EPIC 1: Address Lookup & Compliance Engine,1.3,AI Compliance Summary,Use OpenAI to summarize key ordinances per property,Summary is returned and shown in user-friendly format
EPIC 1: Address Lookup & Compliance Engine,1.4,Tag-Based Rule Classification,"Tag rules by property type: mailbox, fence, chickens, etc.",Each rule has metadata for homeowner relevance
EPIC 1: Address Lookup & Compliance Engine,1.5,Show Proof (Compliance Card),Generate mobile-view card with key rules,"Card displays main rule, authority, and link"
EPIC 2: UI/UX & Public Landing Pages,2.1,Consumer Landing Page,Home page with meme-friendly copy and CTA,"Visitors can enter address, see how it works, and start free check"
EPIC 2: UI/UX & Public Landing Pages,2.2,Search Results Page,Friendly UI with compliance summary per address,"Simple, scannable layout with icons and quick links"
EPIC 2: UI/UX & Public Landing Pages,2.3,Mobile Optimization,Ensure layout works on all screen sizes,Tested and responsive from iPhone SE to desktop
EPIC 2: UI/UX & Public Landing Pages,2.4,Pricing Page,Compare Free vs Pro vs Appraiser tiers,Clear breakdown of monthly plans and benefits
EPIC 2: UI/UX & Public Landing Pages,2.5,Karen Meme Section,Fun and thematic element for virality,Karen warning and viral-ready quotes/images included
"EPIC 3: Authentication, Billing & Limits",3.1,Supabase Auth Setup,Use Supabase for secure auth with email login,Sign up/login/logout works smoothly
"EPIC 3: Authentication, Billing & Limits",3.2,Stripe Billing Setup,Stripe integrated for paid plans,Free tier and upgrade path implemented
"EPIC 3: Authentication, Billing & Limits",3.3,Usage Limits (Freemium),Limit free users to 3 searches/month,Billing logic blocks usage after 3 free queries
"EPIC 3: Authentication, Billing & Limits",3.4,Plan Management Page,Users can view and manage plans,Account area to manage payment/subscription
EPIC 4: Viral Launch Prep (Marketing & Content),4.1,TikTok/YouTube Karen Skits,"Write, record, and post viral video series","3+ videos posted, each with Ordrly CTA"
EPIC 4: Viral Launch Prep (Marketing & Content),4.2,Referral Hook,Bonus searches for sharing with friends,Trackable code link gives rewards
EPIC 4: Viral Launch Prep (Marketing & Content),4.3,Onboarding Email Sequence,"Send welcome, tips, upgrade nudges",Email system sends post-signup flow
EPIC 4: Viral Launch Prep (Marketing & Content),4.4,Testimonials Section,Early users leave fun feedback,"Quotes visible on site, posted via admin tool"
EPIC 5: Business Setup & Compliance,5.1,Buy Domain,Secure domain from Namecheap/GoDaddy,Domain registered and DNS pointed
EPIC 5: Business Setup & Compliance,5.2,Register LLC,File paperwork in Michigan,LLC docs submitted and confirmed
EPIC 5: Business Setup & Compliance,5.3,EIN & Banking,"Apply for EIN, set up business checking",EIN issued and bank account opened
EPIC 5: Business Setup & Compliance,5.4,"Terms, Privacy, TOS",Draft privacy policy and TOS,Posted on footer and covers AI/data use
EPIC 5: Business Setup & Compliance,5.5,Stripe Verified Business,Stripe identity verification done,Can accept real payments