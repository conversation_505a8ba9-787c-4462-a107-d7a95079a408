Ordrly Pro-tier Chat UI – Development Epics
Below is a comprehensive breakdown of epics, features, and user stories for the new Pro-tier Chat UI focused on the 1004 real estate appraisal form use case. Each epic is organized into features and detailed stories with technical implementation notes and acceptance criteria. Core MVP functionalities are marked (MVP), while nice-to-have enhancements are marked (Nice-to-have).
Epic 1: Address & Jurisdiction Context Handling
Epic Description: Enable users to input an address and automatically determine the correct jurisdiction (city/county) and applicable code context for that location. This ensures all compliance answers are grounded in the proper local regulations
file-3andax7xbf65ud3icfud5q
. The chat session will be tied to the address, loading the relevant codes (e.g. building, plumbing, safety) for that jurisdiction as context.
Feature: Address Input & Validation
Story CHUI-Addr-1: Address Autocomplete Input (MVP) – Technical Implementation: Integrate an address autocomplete field (e.g. Google Places API or Mapbox) in the “New Chat” flow. As the user types an address, fetch suggestions and allow selection. On selection, retrieve the full formatted address and basic metadata (city, state, zip, etc.). If a third-party API is not available, provide a structured input form (Street, City, State) with client-side validation of required fields. Acceptance Criteria:
When the user begins typing an address, a dropdown of suggested completions appears (if online autocomplete is enabled).
Selecting a suggestion fills the address form with the complete address. If manual entry is used, the user can fill out required fields (e.g. City, State) and proceed.
If an invalid or incomplete address is entered, the user is prompted to correct it before proceeding (e.g. “Please enter a valid address”).
Story CHUI-Addr-2: Address Submission & Jurisdiction Lookup (MVP) – Technical Implementation: Upon submitting a new address (via selecting an autocomplete suggestion or manual entry), call a Next.js API route (or Supabase Edge Function) to resolve the jurisdiction. This might involve querying a Supabase table of jurisdictions or using a geocoding API to get city/county, then mapping that to known jurisdictions in Ordrly’s database. The system should identify the jurisdiction ID and relevant code set (e.g. “City of Springfield, using 2018 IBC + local amendments”). Store the resolved jurisdiction in the chat session metadata. Acceptance Criteria:
Given a valid address, the backend successfully determines the jurisdiction (city/county) and associates it with that address.
The system records the jurisdiction information (e.g. jurisdiction ID, name, code edition) in the new chat session’s data.
If the address is outside supported jurisdictions or cannot be resolved, the user is notified (e.g. “Address not found in our coverage area”) and cannot proceed with chat creation.
Story CHUI-Addr-3: Jurisdiction Confirmation Display (MVP) – Technical Implementation: After resolving the jurisdiction, display a confirmation in the UI (e.g. in the chat header or a system message) indicating the determined location and code context. For example: “Jurisdiction: Springfield, IL – using 2018 International Residential Code + local ordinances.” This gives the user confidence that the AI will reference the correct regulations. Acceptance Criteria:
Upon chat start, the UI clearly shows which jurisdiction and code baseline is being used for that chat (either in the chat header, subtitle, or initial system message).
The displayed jurisdiction matches the address provided and the data retrieved (city/county name is correct).
If any uncertainty in jurisdiction (e.g. address on a border), the system prompts the user to confirm or clarify (e.g. choose between City vs. County if needed).
Feature: Jurisdiction-Specific Context Initialization
Story CHUI-Addr-4: Load Jurisdiction Knowledge Base (MVP) – Technical Implementation: Once a jurisdiction is identified for the new chat, load or filter the relevant compliance documents for that area. The system will use the jurisdiction ID to scope all RAG (Retrieval-Augmented Generation) searches to that locale’s dataset
file-3andax7xbf65ud3icfud5q
. For example, set a context filter so that the vector search (pgVector) only retrieves code snippets from that city/county’s regulations. Acceptance Criteria:
The chat backend limits its document search to the identified jurisdiction’s data (e.g. doesn’t pull rules from the wrong city).
When the user asks a question, the sources found in the answer are from the correct jurisdiction (verified by source labels or content).
If the jurisdiction has a parent (e.g. a city that uses county codes), the system appropriately includes those relevant sources as well.
Story CHUI-Addr-5: Jurisdiction Context Persistence (MVP) – Technical Implementation: Tie the resolved jurisdiction to the chat session record in the database. Every message in that chat will carry this context so that even if the session is reloaded or accessed later, it “remembers” the address and jurisdiction. Leverage Supabase to store chat metadata (address string, jurisdiction ID, etc.) and use this for all subsequent queries. Acceptance Criteria:
The chat session database entry includes the full address and a reference to the jurisdiction.
On reloading or returning to an existing chat, the system still knows the context (jurisdiction) without asking the user again.
If the user switches between chats of different addresses, each chat uses its own stored jurisdiction context seamlessly.
Epic 2: Chat Session Lifecycle Management
Epic Description: Allow Pro users to manage multiple chat sessions, each associated with a property address. Chats (also referred to as “addresses” in the UI) can be created, persisted, listed, and revisited. This epic covers creating new chats, saving conversation history to the database, switching between sessions, and basic management like renaming or deleting chats.
Feature: Chat Creation & Initialization
Story CHUI-Chat-1: New Chat Session Creation (MVP) – Technical Implementation: Implement a “New Chat” button (e.g. in the left sidebar). When clicked, it triggers the address entry flow (Epic 1). After the user submits an address, create a new chat session record in Supabase (e.g. chats table) with a unique Chat ID, the user’s ID, the address & jurisdiction info, and timestamp. Reuse existing components for creating entries (if the monorepo has similar flows for creating projects or records). Acceptance Criteria:
Clicking “New Chat” opens a prompt or modal for entering an address (as per Epic 1).
Upon submitting a valid address, a new chat session is created in the backend with the correct user association and address details.
The new chat immediately appears in the chat list (left pane) with the address as its label, and the main dialog area is cleared/prepared for a new conversation.
Story CHUI-Chat-2: Initialize Chat Session State (MVP) – Technical Implementation: After creating the chat record, initialize the front-end state for the conversation. This might include adding an entry in the React state or context for the new chat, setting it as the active chat, and preparing an empty messages list. Also, consider seeding the chat with a welcome or context message (optional). Leverage existing state management (if any) from the current chat widget, adapting it to support multiple sessions. Acceptance Criteria:
After creation, the new chat session is active and ready to use without requiring a page reload.
The conversation view shows no prior messages (or an optional welcome message) for the new chat.
The address/jurisdiction context is correctly attached so that the first user question will use the right context.
Feature: Conversation Persistence
Story CHUI-Chat-3: Persist Messages to Database (MVP) – Technical Implementation: Every user query and AI answer should be saved in a chat_messages table (or similar) in Supabase. Each message record includes chat_id, role (user or assistant), message content, timestamp, and any metadata (e.g. reference IDs for AI answers). Use Supabase client libraries or RPC calls to insert messages after they are sent or received. Ensure write operations occur asynchronously so as not to block the UI (e.g. insert after streaming is done). This aligns with the blueprint guidance that logged-in users can have their chats stored
file-3andax7xbf65ud3icfud5q
. Acceptance Criteria:
After a user sends a message, a record is created in the database with the correct content, user ID, and chat ID.
After the AI responds, the answer is also stored with role=assistant and linked to the same chat ID, including any citations (see Audit Trail epic) stored in metadata.
Data integrity: if the user closes the app and returns, all past messages for that address chat are loaded from the database (not just cached in the browser).
Story CHUI-Chat-4: Privacy & Access Control (MVP) – Technical Implementation: Ensure that chat session data is secured so only the owning user (and an admin, if necessary) can access it. Use Supabase Row-Level Security policies or similar to enforce that each chats and chat_messages record belongs only to the creator’s user_id
file-3andax7xbf65ud3icfud5q
. This will prevent other users from ever accessing someone else’s chat transcripts. Acceptance Criteria:
Only authenticated users with an active Pro subscription (see Epic 12) can create and load chat sessions.
A user can only retrieve chats that they created – attempting to access another user’s chat (via ID manipulation) is blocked by the backend.
Admin users (with a special role) may bypass this for support purposes, but all access is logged (as per Audit Trail).
Feature: Chat List & Multi-Chat Management
Story CHUI-Chat-5: Display Chat Sessions List (MVP) – Technical Implementation: In the left sidebar, list all chats (addresses) the user has created, sorted by last active or creation time. Each entry shows the address (or a user-defined name if available). Use a scrollable list UI (Tailwind UI list components) that fits the design. Implement retrieval of this list via Supabase query on user’s chats. The component should update in real-time if new chats are added (Supabase subscriptions or revalidation) so the list stays current. Acceptance Criteria:
Upon opening the chat UI, the left panel shows all existing chat sessions for that user.
Each chat entry is labeled with the property address (e.g. “123 Main St, Springfield”) or a default name.
The list is updated to include new chats immediately after creation, and reflects deletions or renames (if those features are used).
Story CHUI-Chat-6: Switch Between Chats (MVP) – Technical Implementation: Allow the user to click on an address in the left pane to load that conversation. Implement a click handler that sets the active chat context: fetch that chat’s messages from the database (paged or full load) and update the main dialog view. If the current chat has an in-progress answer, optionally warn or cancel it before switching. Ensure the jurisdiction context is swapped according to the selected chat’s stored data. Acceptance Criteria:
Clicking on a different chat in the list immediately loads that chat’s message history in the dialog window.
The header or context info updates to the new chat’s address and jurisdiction.
The previously active chat’s state (if any unsent input or ongoing response) is handled gracefully (e.g. cleared or stored) and does not bleed into the new chat.
Story CHUI-Chat-7: Rename Chat Session (Nice-to-have) – Technical Implementation: Allow users to rename a chat (e.g. give a nickname or property identifier instead of the full address). This could be done via an “Edit” icon on the chat list item. Provide a text input for the new name and save it to the chats table (new field for custom name). Acceptance Criteria:
The user can rename a chat session via the UI (e.g. clicking a pencil icon toggles an editable field).
After saving, the new name appears in the chat list and maybe as the header, while still retaining the underlying address in the data.
Renaming does not break any linkage – the chat continues to function normally with its messages intact.
Story CHUI-Chat-8: Delete Chat Session (Nice-to-have) – Technical Implementation: Provide an option to delete a chat (e.g. a trash icon on the chat list item or a context menu). On delete, remove the chat and all associated messages from the database (or mark them as deleted for potential recovery) using a Supabase RPC or transaction to maintain integrity. Prompt the user for confirmation (“Are you sure you want to delete this chat? This cannot be undone.”). Acceptance Criteria:
The user can delete a chat session and its history via the UI with a confirmation step.
After deletion, the chat is removed from the list and the UI switches to either no chat selected or the next available chat.
The underlying data is deleted/hidden on the backend, and the user can no longer access that chat. (Ensure a deleted chat’s data is not searchable or included in RAG results anymore.)
Epic 3: Chat Conversation UI & Interaction
Epic Description: Create an intuitive chat interface similar to ChatGPT, consisting of the main dialog window where user and AI messages appear. This epic focuses on the message display format, input controls, streaming responses, and overall user interaction within the chat. It ensures that messages are clearly differentiated by speaker, that the user can easily submit questions, and that responses are shown in a user-friendly way (with streaming and markdown support). Basic error handling and the inclusion of compliance disclaimers are also covered.
Feature: Message Display & Formatting
Story CHUI-UI-1: User vs. AI Message Styling (MVP) – Technical Implementation: Display conversation messages in a chat bubble format with distinct styling for user and AI. For example, user messages might be right-aligned or have a different background color, while AI responses are left-aligned. Use Tailwind CSS (possibly Tailwind UI pre-built chat components) for consistent design. The component should support long text wrapping and be mobile-responsive. Acceptance Criteria:
User messages and AI messages are visually distinct (different colors or alignment) so the conversation flow is easy to follow.
The chat window auto-scrolls to the latest message when a new message is added.
The design matches the app’s style theme and is consistent with other Ordrly UI elements (reusing existing CSS classes or components where possible).
Story CHUI-UI-2: Markdown & Rich Text Support (MVP) – Technical Implementation: Ensure the chat can render basic rich text in AI responses, since answers may include lists, bold terms (e.g. code section numbers), or other formatting. Use a React markdown renderer or sanitize and insert HTML for known formatting from the AI. Also handle URLs in answers (e.g. if an answer references an external site or document link). Acceptance Criteria:
If an AI answer contains bullet points, numbered lists, or other Markdown (like - or 1. prefixes), the chat renders them as HTML lists properly.
Bold or italic text in answers is displayed with proper styling.
Any hyperlink included in an answer is clickable and opens in a new tab (with proper security like rel="noopener").
Feature: Message Input & Submission
Story CHUI-UI-3: Query Input Box & Send (MVP) – Technical Implementation: Provide a text input area at the bottom of the chat for the user to type questions. The input should allow multi-line entry (e.g. auto-expand or a toggle to enlarge, since some questions may be detailed) but hitting Enter (or clicking a “Send” button) will submit the question. Implement the send action to call the Next.js API route (e.g. /api/chat) with the user message, current chat ID, and other needed context. Clear or disable the input while the question is being processed to prevent duplicate sends. Acceptance Criteria:
The user has an obvious place to type their question (a text box with placeholder like “Ask a question about the property’s compliance…”).
Hitting “Enter” or clicking the send icon submits the question. The input is then cleared (or disabled) and the user’s message appears in the chat immediately.
The backend API receives the message with the correct chat/session identifier, and the UI is ready to display the incoming answer.
Story CHUI-UI-4: Input Validation & Multi-line Handling (MVP) – Technical Implementation: Handle edge cases in the input: if the user tries to send an empty question or only whitespace, do not call the API and maybe show a gentle prompt (“Please enter a question”). If the user input is very long or contains special characters, ensure they are transmitted safely (proper encoding). Also support multi-line questions: allow the user to press Shift+Enter for a new line without sending. Acceptance Criteria:
Trying to send an empty query does nothing except maybe an error tooltip – it should not crash the app.
Users can add line breaks in their question (e.g. listing multiple points) and those line breaks are preserved in the message sent to the AI.
The input box can handle at least a few hundred characters of text without breaking layout (to accommodate detailed questions).
Feature: Streaming Responses & Feedback
Story CHUI-UI-5: Streaming AI Response Display (MVP) – Technical Implementation: Stream the assistant’s answer in real-time, token by token, to mimic ChatGPT’s interactive feel
file-3andax7xbf65ud3icfud5q
. Utilize OpenAI’s streaming API capabilities via the backend – the Next.js API route can flush chunks of text as they arrive. On the front-end, update the AI message as a typing animation. If using SSE or similar, ensure the frontend concatenates incoming parts. Reuse any existing streaming logic from the older chatbot if available. Acceptance Criteria:
When the AI is formulating an answer, the response text begins appearing within a second or two, rather than waiting for the full answer completion.
The user can see the answer “typing out” progressively, which improves perceived performance.
The streaming stops cleanly when the answer is done, and the final answer is stored/presented as a normal message (no partial text left unrendered).
Story CHUI-UI-6: Loading Indicator and Cancel (Nice-to-have) – Technical Implementation: Provide the user feedback that the system is working on an answer. This could be a subtle “…” animation or spinner in the chat window while streaming is in progress (possibly at the bottom or in the title bar). Optionally, allow the user to cancel an in-progress response (e.g. an “Stop” button like ChatGPT) if the answer is lengthy or the question was mistaken. Implement cancel by aborting the request on the client and instructing the backend to stop the OpenAI stream (if possible). Acceptance Criteria:
While waiting for an answer, the UI clearly indicates that a response is loading (e.g. a loading spinner or placeholder message).
If implemented, clicking a “Stop Response” control will halt the streaming and no further tokens are shown. The partial answer may either disappear or remain with an indication that it was stopped.
Cancelled or interrupted answers are not saved to the chat history (or are marked appropriately) to avoid confusion.
Feature: Errors & Disclaimers
Story CHUI-UI-7: Error Handling Message (MVP) – Technical Implementation: Anticipate possible errors (network issues, server errors, AI model errors). If the API returns an error (non-200 or a specific error payload), catch it and display an error message in the chat. For example, if the OpenAI call fails or times out, show a system-style message: “Error: Sorry, something went wrong. Please try again.” Possibly include a retry button on that message. Ensure this error message is styled distinctly (e.g. red text or warning icon) and does not break the chat flow. Acceptance Criteria:
Simulate an API failure (e.g. by disconnecting network or forcing an error) – the user sees an error message in the chat window informing them of the failure.
The error message does not disrupt the existing messages (it appears as a chat entry at the appropriate place in sequence).
After an error, the user can continue the conversation (send a new question or retry the previous one) normally.
Story CHUI-UI-8: Compliance Disclaimer in UI (MVP) – Technical Implementation: Include a visible disclaimer about AI-generated answers. This can be a static text in the chat interface (for example, a small note below the input or above the chat) and/or appended automatically to each answer. According to policy, something like: “Answers are generated by AI based on regulations. Always verify with local authorities.” should be shown
file-3andax7xbf65ud3icfud5q
. A possible implementation: automatically append a sentence to the end of every AI answer via post-processing, or include it as part of the prompt so the model outputs it. Alternatively, display a persistent note in the UI. Acceptance Criteria:
The interface clearly communicates a disclaimer about the AI answers (either in each answer or in a fixed location).
Example: Every AI answer ends with a line “(Always verify with local authorities.)” in italic or lighter text, as per compliance guidelines
file-3andax7xbf65ud3icfud5q
.
The disclaimer text is present for all users and cannot be disabled, ensuring legal/ethical compliance with Ordrly’s accuracy policies.
Epic 4: Citations & Source Panel
Epic Description: Display the sources (code sections, ordinances, etc.) used to generate each AI answer. The right-hand source panel will show the documents and snippets cited, giving users transparency and an ability to verify the answer against authoritative references. This epic includes organizing sources by category (e.g. building, plumbing, safety codes), viewing details of each source, and keeping the source list in sync with the Q&A.
Feature: Source Panel User Interface
Story CHUI-Source-1: Source Panel Layout & Toggle (MVP) – Technical Implementation: Implement a right sidebar panel that can show a list of sources for the current AI response. The panel can be always visible in desktop view (mimicking ChatGPT’s citation panel) and toggleable on mobile. Use Tailwind UI components (e.g. a two-column layout) to position this panel. Initially, the panel may be empty or have a placeholder (“Sources will appear here”). Once the AI responds with citations, populate this panel. Acceptance Criteria:
The chat interface includes a right-side area reserved for citations. On smaller screens, this panel can collapse or be shown via a “Show Sources” button.
By default (before any question is asked, or if an answer has no citations), the panel displays a helpful placeholder or remains hidden.
The layout is responsive and does not overlap or hide the main chat text (content area adjusts when panel is shown).
Story CHUI-Source-2: Display Cited Documents (MVP) – Technical Implementation: When the AI provides an answer with citations, list each unique source document in the source panel. Each entry should include the document title or shorthand (e.g. “2018 IRC §R314 – Smoke Alarms”) and possibly an icon or label for its category (e.g. [🏠 Building], [🔥 Fire], [💧 Plumbing]). If the model provides section numbers or titles, use those; otherwise map the snippet to the document metadata stored. Show a short snippet or summary of the part used (a few relevant lines) under each source title so the user sees context. Acceptance Criteria:
After an AI response is received, the source panel populates with an entry for each cited source.
Each source entry shows at minimum the name of the code or document and the specific section referenced (if available).
The user can see a brief quote or context from the source (e.g. the sentence containing the answer) beneath the title, giving immediate insight into what the source says.
Story CHUI-Source-3: Clickable Source Links (MVP) – Technical Implementation: Make each source entry interactive. For instance, clicking on a source could open the full document or the specific section in a modal or new tab. If Ordrly hosts the document text, it might open a modal viewer showing the entire section or page. If not, it might link to an external site or PDF at the correct section (if possible). Use the stored reference (like URL or document ID) in the citation metadata to locate the source. Acceptance Criteria:
Each source in the panel is clickable or has a clear “Open” action (like a link or button).
Upon clicking, the user either sees the full text of that code section in a pop-up/overlay without leaving the app, or is taken to an official source (new browser tab) for that code section.
The solution should allow the user to read the broader context of the citation, not just the one line snippet, for verification purposes.
Feature: Source Categorization & Navigation
Story CHUI-Source-4: Categorize Sources by Code Type (Nice-to-have) – Technical Implementation: If multiple sources are cited, group them by domain/category for clarity. For example, if an answer cites the Building Code and the Fire Code, visually separate those (perhaps collapse under headings “Building” and “Fire”). Use metadata from the documents (each document in the knowledge base can have a category tag like “Building”, “Electrical”, “Zoning”, etc.). The panel could list categories as sub-headers with sources underneath. Acceptance Criteria:
When sources from different categories are present, the panel groups them under category labels (e.g. “Mechanical Code (MC)” with all mechanical sources listed under it).
If sources are all of one type, no grouping header is needed (or only one group is shown).
Category labels are derived from document metadata (e.g. a field in the Supabase documents table) and reflect domains relevant to real estate appraisal (e.g. Safety, Structural, Plumbing).
Story CHUI-Source-5: Highlight Sources on Hover (Nice-to-have) – Technical Implementation: Improve the correlation between answer text and sources. When the user hovers over or taps a citation number in the AI answer (e.g. “[1]”), highlight the corresponding source entry in the panel (and vice versa: hovering on the source entry could highlight the citation in the text). This can be done by assigning IDs to citation references in the answer text and adding onHover events. Acceptance Criteria:
If the user moves their cursor over a citation marker in the answer (like [2]), the relevant source entry in the panel is visually emphasized (e.g. background highlight or scroll into view).
Conversely, hovering on a source entry could underline or highlight the citation number in the answer text for a moment.
This linkage helps the user understand which part of the answer came from which source, improving transparency.
Epic 5: Retrieval-Augmented Answering (RAG) Integration
Epic Description: Power the chat’s Q&A with a Retrieval-Augmented Generation pipeline to ensure answers are grounded in authoritative code references
file-3andax7xbf65ud3icfud5q
. This epic covers the backend process of embedding documents, searching for relevant snippets, constructing prompts for the GPT-4.1-nano model with those snippets, and returning a cited answer. The focus is on leveraging Ordrly’s existing knowledge base (with pgVector in Postgres) and ensuring the AI’s responses are accurate, location-specific, and compliant with content policies (e.g. no full verbatim quotes of copyrighted codes
file-3andax7xbf65ud3icfud5q
).
Feature: Knowledge Base Embeddings
Story CHUI-RAG-1: Document Embedding Pipeline (MVP) – Technical Implementation: Prepare all relevant compliance documents (building codes, ordinances, etc.) by splitting them into sections/chunks and creating vector embeddings for each chunk. Use the pgVector extension in Supabase (already part of Ordrly’s DB
file-3andax7xbf65ud3icfud5q
) to store these embeddings along with text and metadata (jurisdiction, document name, section ref, category). This can be done ahead of time via a script or Supabase Edge Function for ingestion. Ensure that copyrighted code text is handled per policy – e.g., for non-public-domain codes, store only summaries or keywords and not full text
file-3andax7xbf65ud3icfud5q
. Acceptance Criteria:
All relevant code documents for supported jurisdictions are stored in the database with vector embeddings, ready for semantic search.
Each vector entry includes metadata: which jurisdiction it belongs to, which document and section, and possibly a category tag (for use in the source panel grouping).
Proprietary code texts (if any) are not stored verbatim beyond what is legally permissible – the system either omits them or stores a short reference, in line with Ordrly’s compliance approach
file-3andax7xbf65ud3icfud5q
.
Story CHUI-RAG-2: Query Vector Search (MVP) – Technical Implementation: When a user asks a question, perform a semantic similarity search against the embeddings for that chat’s jurisdiction. Use an efficient DB query or Supabase RPC to get the top N relevant snippets (e.g. top 3-5)
file-3andax7xbf65ud3icfud5q
. This involves embedding the user’s question with the same model (OpenAI embedding endpoint or a local model) and running a vector_similarity query in the database. Only retrieve snippets from the specific jurisdiction’s data (and possibly any generic national code references if applicable). Acceptance Criteria:
For a given sample question, the system successfully retrieves a ranked list of relevant text snippets from the knowledge base (with their source info).
The results are filtered to the correct jurisdiction (e.g. asking about “smoke detectors” in Springfield does not return snippets from New York’s code).
Performance is acceptable: the vector search should execute quickly (a few hundred ms ideally) to keep chat response time low.
Story CHUI-RAG-3: Prompt Construction with Sources (MVP) – Technical Implementation: Build the final prompt for the GPT-4.1-nano model using the retrieved snippets. The prompt should include the user’s question and the context snippets in a formatted way, instructing the model to use them for the answer and to cite sources. For example:
sql
Copy
Edit
User question: "Do I need hardwired smoke alarms in a two-story house?"  
Context:  
(1) 2018 IRC R314.3 – Smoke alarms shall be installed in each sleeping room... (City of Springfield)  
(2) Springfield Fire Code Sec. 9 – All new residential units must have hardwired smoke detectors...  
Instructions: Answer the question using **only** the above information, and cite each fact with the source number.
Use Ordrly’s existing prompt templates or utilities if available to ensure consistency. Acceptance Criteria:
The generated prompt sent to OpenAI’s API contains the user’s query and multiple relevant snippets labeled (e.g. with numbers or bullet points) that the model can reference.
The prompt explicitly instructs the model to ground its answer in those snippets and to provide citations like “[1]” or “[2]”.
No irrelevant or unrelated information is included in the prompt – it’s concise and focused to maximize the model’s useful output.
Story CHUI-RAG-4: Incorporate Conversation Context (Nice-to-have) – Technical Implementation: If the user’s question is a follow-up that references prior discussion (“What about the garage?”), include recent conversation context in the retrieval and prompt. One approach is to append the last user question and/or AI answer to the query before embedding, or maintain a running summary of the conversation. Also possibly re-run vector search for new terms that appear. This ensures continuity in multi-turn chats. Acceptance Criteria:
In a scenario where the user asks a follow-up that omits key details mentioned earlier, the system still understands it by having included the prior Q&A context in the model input.
The AI’s answer to the follow-up remains accurate and relevant, indicating it “remembered” what was discussed (without the user having to repeat information).
This context inclusion does not bring unrelated text that could confuse the model – it’s limited to a few recent turns or a summary, and still accompanied by a fresh vector search for any new query components.
Story CHUI-RAG-5: OpenAI Answer Generation (MVP) – Technical Implementation: Call the OpenAI API with the constructed prompt (choose GPT-4.1-nano model) to generate the answer. Use Ordrly’s common OpenAI API wrapper (if existing) which may handle retries, rate limiting, and logging
file-3andax7xbf65ud3icfud5q
file-3andax7xbf65ud3icfud5q
. The model’s parameters should be set for compliance use: e.g. a temperature low enough to keep answers factual. Implement the call within the Next.js API route or an Edge Function, and ensure the response (streamed or full) is captured. Acceptance Criteria:
The system successfully receives a completion from the OpenAI model for the query. For example, asking a known question returns a relevant answer with citations in the text.
The cost and performance are considered – using GPT-4.1-nano stays within budget and latency requirements (document if average response time is a few seconds).
Errors from the model (rate limits, etc.) are handled gracefully (coupled with UI error story), e.g. with retries or fallback to GPT-3.5 if configured as a backup.
Story CHUI-RAG-6: Answer with In-Text Citations (MVP) – Technical Implementation: Ensure the AI’s answer includes citations referencing the provided sources. The prompt and perhaps few-shot examples should guide the model to format citations as [1], [2], etc., corresponding to the snippet indexes given. Verify this by testing a few prompts. On receiving the answer, parse out the cited source identifiers (if needed for the source panel). Acceptance Criteria:
The AI’s answer text consistently contains citation markers that refer to the source documents (e.g. “...must have hardwired detectors [2]”).
Each citation number in the answer matches one of the sources retrieved (no stray numbers). The content of the answer aligns with those sources (no unsupported statements without a reference).
If the model fails to include citations or invents a source, adjust the prompt or implementation until it reliably does so – the final system should have nearly every factual statement backed by a source reference
file-3andax7xbf65ud3icfud5q
.
Feature: Answer Quality & Policy Compliance
Story CHUI-RAG-7: Truthfulness and Scope Control (MVP) – Technical Implementation: The system should restrict answers to only use the provided context and not speculate beyond it
file-3andax7xbf65ud3icfud5q
. Achieve this by prompt instructions (e.g. “If the answer is not in the provided context, say you don’t know or advise consultation”) and by the confidence checks (Epic 6). Also, handle cases where the jurisdiction uses a model code that is copyrighted: the answer should cite the requirement rather than quoting text verbatim (e.g. “According to Section R314 of the 2018 IRC… [reference]” without copying the exact copyrighted text)
file-3andax7xbf65ud3icfud5q
. Acceptance Criteria:
The AI does not hallucinate answers: when asked something outside the knowledge base (or if context is insufficient), it refrains from making up code and instead provides a fallback or disclaimer (see Epic 6 for the exact behavior)
file-3andax7xbf65ud3icfud5q
.
Answers adhere to content policy: no large verbatim quotes from non-public-domain sources. Instead, the answer paraphrases or refers to the code section number with a citation.
In testing, any attempt to get the AI to answer with unsupported info results in either a refusal or a generic safe response, not a confident-sounding lie.
Epic 6: Answer Confidence & Fallback Mechanism
Epic Description: Implement safeguards for answer quality by evaluating the confidence of the retrieved knowledge and the AI’s ability to answer. If the system is unsure or lacks authoritative info, it should gracefully decline or provide a cautious response rather than a misleading one
file-3andax7xbf65ud3icfud5q
. This epic covers using similarity scores to gauge confidence, and defining fallback behaviors (like a refusal message or a suggestion to consult a human).
Feature: Confidence Evaluation
Story CHUI-Fallback-1: Similarity Score Threshold (MVP) – Technical Implementation: Utilize the vector search similarity scores to assess how well the knowledge base covers the query. Establish a threshold (configurable) such that if the top result’s score is below this value (meaning the query is likely not answered by any known document), the system flags the query as low-confidence. Implement this check in the API route after retrieving snippets. Optionally, consider the number of snippets above a secondary threshold (if none or only irrelevant ones, confidence is low). Acceptance Criteria:
The system computes a confidence metric for each query (e.g. highest cosine similarity from pgVector, or aggregate of top 3).
A threshold (e.g. 0.8 on cosine similarity, adjustable) is set and documented. In test scenarios, a clearly out-of-scope question (like “Who won the football game last night?”) results in confidence below the threshold.
If confidence is low, this is logged/flagged internally to trigger the fallback response instead of a normal answer.
Story CHUI-Fallback-2: AI Model Uncertainty (Optional) – Technical Implementation: (If using advanced model features) Leverage the AI model to double-check if it should answer. For instance, use OpenAI’s function calling or a secondary prompt where the model evaluates “Do we have sufficient info to answer?” based on the snippets. This is a complement to raw similarity scores. This story is a nice-to-have if fine-tuning the fallback – it can reduce false positives/negatives by analyzing the question semantics. Acceptance Criteria:
(If implemented) The system occasionally passes the question and snippets to a utility function or model prompt asking for answerability. The model returns a boolean or rationale.
The additional check helps in edge cases (for example, high similarity score but irrelevant context could be caught).
The complexity added by this step does not significantly degrade performance; if it does, it can be toggled off.
Feature: Fallback Response Handling
Story CHUI-Fallback-3: Graceful Refusal Message (MVP) – Technical Implementation: Define and generate a standardized fallback response when confidence is below threshold or the AI otherwise should not answer. For example: “I’m sorry, but I’m not sure about that. You may need to consult your local building department for guidance.”
file-3andax7xbf65ud3icfud5q
. This can be a static template or an AI-generated refusal with a specific prompt (but static is safer for consistency). Implement logic in the API route: if low-confidence, skip the normal answer generation and return this fallback message (with no sources, or perhaps a generic source if available like a link to a general resource). Acceptance Criteria:
When the system decides not to answer due to low confidence, the user receives a polite, brief message indicating the inability to help with that query.
The message encourages consulting a human or authority, aligning with compliance tone (no hallucinated answer is given).
This fallback response itself is not counted as an error – it appears as a normal assistant message in the chat (possibly with a special style or icon to denote it’s a guidance message).
Story CHUI-Fallback-4: Fallback Logging & Metrics (Nice-to-have) – Technical Implementation: Whenever a fallback occurs, log this event (in a Supabase table or analytics service) with details: query asked, time, and maybe which part of the pipeline triggered the fallback (no snippets vs. model flagged unsolvable). This helps developers/admin identify gaps in the knowledge base. Optionally surface a metric like “unanswered questions this week” for internal review. Acceptance Criteria:
Each fallback invocation creates a record (e.g. in a unanswered_queries table or logs) with the user’s question and context data.
Admin can review these logs to find frequently asked questions that were not answerable, and take action (like adding new content or adjusting thresholds).
The logging does not expose user identity beyond an ID (for privacy) and can be toggled if needed for compliance, but is by default on for quality improvement.
Story CHUI-Fallback-5: Partial Answer with Caution (Nice-to-have) – Technical Implementation: In cases where some info is available but not complete, the AI could answer with a heavy disclaimer or partial suggestion. For instance, if the question is somewhat covered by sources but not fully, the answer might say “There’s limited information on that; from what I found... [partial info] ... but you should verify with the local authority.” Achieving this may involve setting a lower threshold for partial answer vs. complete refusal. This is complex to tune, so consider it only if straightforward. Acceptance Criteria:
(If implemented) The system differentiates between “no info at all” and “some info but uncertain”. In the latter case, the AI’s answer includes a cautionary note within the answer rather than a full refusal.
Users receiving such an answer can glean some insight but are clearly warned that the information may be incomplete or uncertain.
This behavior is consistent and does not appear randomly – it’s based on defined confidence ranges or rules.
Epic 7: Data Freshness & Knowledge Base Maintenance
Epic Description: Keep the underlying code and reference documents up-to-date so that answers remain accurate over time. This epic deals with detecting stale data (e.g. if a new code edition is adopted), refreshing the vector embeddings when documents change, and providing mechanisms for both automated and manual updates. It ensures the system’s knowledge is never too out-of-date for critical compliance queries.
Feature: Document Versioning & Staleness Tracking
Story CHUI-Data-1: Track Document Metadata & Versions (MVP) – Technical Implementation: Expand the documents/ordinances table in the database to include metadata like “publish_date”, “version/edition”, or a “last_updated” timestamp for each document. When the system loads context for a chat’s jurisdiction, compare today’s date and the code edition date. If a document is nearing or past a known update cycle, mark it as potentially stale. Acceptance Criteria:
Each knowledge base document entry includes a clear identifier of its version (e.g. “2020 NEC” or “County Ordinances updated Jan 2022”).
If an admin updates a document to a new version, the version field is updated and can trigger re-embedding (in other stories).
The system can identify if a code is outdated (for instance, if the jurisdiction is still on a 2015 code and it’s now 2025, that might be noted for admin to update).
Story CHUI-Data-2: Staleness Warning Indicator (Nice-to-have) – Technical Implementation: If the knowledge base for a jurisdiction is known to be outdated, optionally inform the user or admin. For example, if a user opens a chat for an address where codes changed recently and the system hasn’t updated, show a small icon or message “Data update in progress – recent code changes may not be reflected.” This requires knowing that an update is pending (from admin input or an automated feed). Acceptance Criteria:
In a scenario where codes have changed and the system hasn’t ingested the new version yet, the UI or system has a way to communicate this (could be only on admin dashboard or user-facing if critical).
If user-facing, the message is subtle and factual, not alarming – e.g. “Note: The building code was updated on Jan 2025; this chat currently references the 2018 code edition.”
This indicator is accurate and based on actual data (avoid false warnings or missing warnings due to lack of metadata).
Feature: Automated Knowledge Updates
Story CHUI-Data-3: Scheduled Ordinance Update Check (Nice-to-have) – Technical Implementation: Implement a background job or scheduled Supabase Edge Function to periodically (e.g. monthly) check for new versions of code or ordinances
file-3andax7xbf65ud3icfud5q
. This could be done by scraping known sources or reading an “update calendar” maintained by Ordrly. For simplicity, perhaps maintain a JSON of expected code adoption dates and have the function compare current date. If updates are found, flag those jurisdictions for re-ingestion. Acceptance Criteria:
A scheduled process runs at defined intervals (cron job) without manual intervention. For example, on the first of each month it executes an update-check routine.
The routine identifies at least known changes (for instance, it logs “Austin – new ordinance package available as of June 2025” if such info is present).
The outcome of the check is logged or stored, so that the system knows which documents need refreshing (e.g. inserts a record in a pending_updates table for each out-of-date document).
Story CHUI-Data-4: Automated Re-ingestion of New Data (MVP) – Technical Implementation: When a document update is detected (either via the above check or via admin input), automatically fetch and embed the new content. For example, if a new PDF of the city code is available, an automated process (Edge Function or background worker) downloads the text, splits it, and updates the embeddings in the database. To avoid downtime, consider versioning: keep the old version until the new one is fully processed, then atomically switch which is “active”. Also update the document metadata (version/date). Acceptance Criteria:
The system is capable of ingesting a new or updated document without developer intervention – triggered by a detected update or an admin action, the pipeline from fetching text to storing vectors runs end-to-end.
The new information becomes available to the chat: e.g. after an update, asking about a provision that changed now yields the updated answer (with the new source).
The old data is either archived or removed to avoid confusion, unless needed for historical record. The transition does not cause erroneous answers (e.g. avoid mixing old and new snippets in search results).
Feature: Manual Refresh Controls
Story CHUI-Data-5: Admin Triggered Vector Refresh (MVP) – Technical Implementation: Provide an admin-accessible mechanism to manually trigger a re-embedding of documents. This could be a button in an internal admin dashboard (e.g. “Re-index now”) which calls a secure Next.js API route or directly invokes a Supabase function to reprocess documents. For instance, after uploading a new ordinance file, the admin clicks “Re-index” to generate embeddings and update the vector store. Ensure this action is protected (admin-only) and perhaps requires specifying scope (all data vs. a particular jurisdiction). Acceptance Criteria:
An admin user can initiate a knowledge base refresh via the UI or a command – for example, selecting a jurisdiction and clicking “Re-index code data”.
The system performs the re-index: existing embeddings are updated or replaced with the latest content. Progress or completion can be logged or indicated (e.g. a toast “Re-index complete”).
There is validation to prevent misuse – only authorized admin can trigger, and not too frequently (maybe disable the button if a job is already running).
Story CHUI-Data-6: Partial Update & Merge (Nice-to-have) – Technical Implementation: In cases where only a part of the knowledge base changes (e.g. one chapter of code updated), allow updating just that portion instead of reprocessing everything. This might involve tracking document sections by an ID and only replacing those. The admin UI could list documents or sections with checkboxes to reprocess. Acceptance Criteria:
The admin can choose to refresh a specific document (like “Re-ingest Plumbing Code only”) rather than all data.
The system only updates embeddings for that selection, which is faster and avoids overwriting other data.
The search results remain correct – e.g. a refreshed section doesn’t create duplicate or conflicting entries in the vector index.
Epic 8: Prebuilt Prompt Templates & Appraisal Mode
Epic Description: Provide a set of pre-defined prompts to guide appraisers or reviewers in asking the right questions. In “Appraisal Mode”, the chat UI can offer a menu of common queries or tasks related to the 1004 appraisal process (e.g. safety checks, code compliance checks for typical home features). This epic covers designing the prompt template menu and the underlying system to manage these templates.
Feature: Prompt Suggestion Menu UI
Story CHUI-Prompt-1: Appraisal Prompt Menu (MVP) – Technical Implementation: Add a UI element (e.g. a dropdown menu or a list of buttons) in the chat interface labeled “🔖 Appraisal Templates” or similar. When clicked or hovered, it shows a list of suggested questions/prompts relevant to property appraisal compliance. Examples might include: “Check safety code compliance for this property”, “List required smoke and CO alarms for a house”, “Are there any building code issues with a finished basement?”, etc. Implement this list as part of the chat input area or sidebar. Acceptance Criteria:
The UI clearly presents a set of prebuilt question templates accessible in one click.
The templates are worded in a way that is generic but relevant to the address context (they might be filled in or interpreted in context of the current chat’s jurisdiction when used).
The user can open/close the menu easily, and it does not clutter the interface when not in use.
Story CHUI-Prompt-2: Insert Template into Chat (MVP) – Technical Implementation: When the user selects one of the suggested prompts, the system should either: (a) directly send it as a question to the chat, triggering the AI response, or (b) populate the input box with the prompt text for the user to edit or confirm. The choice depends on UX preference – a safe approach is to populate the input field so user can tweak it. Implement the click handler accordingly. Acceptance Criteria:
Clicking a prompt from the menu results in that prompt appearing as the next question (either immediately asked or ready in the input box).
If directly sending, the prompt shows up as a user message in the chat and the AI begins answering it just like a normal question. If populating the input, the user can then hit send.
The inserted prompt can include dynamic placeholders (if any) resolved to the current context. For MVP, likely the prompts are general and don’t need placeholders, but if one said “[ADDRESS]” it should replace with actual address or property type, etc.
Feature: Template Management
Story CHUI-Prompt-3: Template Library Storage (MVP) – Technical Implementation: Store the list of prompt templates in a maintainable way. This could be a JSON file or, better, a database table (e.g. prompt_templates) with fields like id, text, category (like “Appraisal”). Using a DB allows adding/editing templates without code changes. Load these templates on the client or via an API call when the chat UI initializes in Pro mode. Acceptance Criteria:
The set of available prompt templates is not hard-coded into the UI; it’s data-driven. For example, adding a new row in prompt_templates would make a new option appear in the menu (after refresh).
Templates can be categorized if needed (for instance “Appraisal Mode” vs “General”), and the UI could be filtered to appraisal ones for Pro users.
There is no noticeable performance hit when loading the templates (a dozen or so prompt texts should be fine to fetch).
Story CHUI-Prompt-4: Admin Template Editor (Nice-to-have) – Technical Implementation: Provide a simple admin interface to create, edit, or remove prompt templates. This could be as basic as a secured CRUD interface on a Supabase table (since Supabase offers a quick web UI for tables) or a custom form in an admin page. Ensures that non-developers (or the founder) can tweak the suggested questions as they learn what users need. Acceptance Criteria:
An admin user can add a new prompt template (with text and maybe a category/tag) through an interface without deploying new code.
Changes to templates (edits or deletions) are reflected in the user interface for subsequent sessions (or immediately if we fetch fresh each time).
Only admins have access to this editing capability, and it’s protected by auth (accidental or malicious edits by normal users prevented).
Feature: Mode-specific Behavior
Story CHUI-Prompt-5: Appraisal Mode Context (MVP) – Technical Implementation: If “Appraisal Mode” is a distinct mode, ensure that the chat knows the user’s intent is appraisal-related. This could be as simple as all Pro-tier chats implicitly being appraisal-focused (given the target users), or a toggle the user can set. If needed, prepend a system message or use a slightly different prompt for the AI that sets the tone (e.g. “You are assisting with a real estate appraisal compliance check.”). Acceptance Criteria:
The AI’s answers are framed in a way useful for appraisers. For example, it might be slightly more explanatory about implications (this can be achieved via prompt engineering if necessary).
(If a toggle exists) Toggling appraisal mode on/off changes the available templates and possibly the system prompt used. If all Pro chats are inherently appraisal mode, then this is always on.
No matter what, the presence of appraisal-specific templates and possibly context ensures the feature is tailored to appraisal reviewers or appraisers in the field.
Epic 9: 1004-Specific Note Generation & Export
Epic Description: (Nice-to-have Epic) Enable users to easily generate summary notes or report content for the 1004 appraisal form based on the chat discussion. For example, after identifying compliance issues or important findings via Q&A, the user might want a concise narrative to include in their appraisal report. This epic covers using AI to compile such a note and providing ways to export or copy it.
Feature: Appraisal Note Generation
Story CHUI-Note-1: Generate Compliance Summary Note (Nice-to-have) – Technical Implementation: Add an option in the chat UI to “Generate Appraisal Note” – perhaps a button that appears after an answer or at the end of a conversation. When clicked, this triggers an AI prompt that summarizes the key compliance information from the chat (or from the last answer) into a short paragraph suitable for the “observations” or “conditions” section of an appraisal report. The system can send a tailored prompt to GPT-4.1-nano, for example: “Summarize the above findings into a note for a real estate appraisal report.” Use conversation history or the most relevant points (possibly by reusing the snippets found). Acceptance Criteria:
After a user has asked some questions, clicking “Generate Note” produces a coherent paragraph or bulleted list highlighting the compliance issues or confirmations related to the property.
The generated note is factual and drawn from the sources discussed (it should not introduce new info). It might say, for example: “Note: The property’s smoke alarm setup meets 2018 IRC R314 requirements, with hardwired detectors on each floor and in each bedroom [1]. The deck guardrail height of 34 inches is below the required 36 inches per local building code [2], which may be a safety concern.”
The note is concise (maybe 2-5 sentences or a short list) and uses a formal tone appropriate for an appraisal report.
Story CHUI-Note-2: Include Citations in Note (Nice-to-have) – Technical Implementation: Decide whether the generated note should include citations or not. Appraisal notes might not usually include code citations, so perhaps the AI should output a clean narrative without bracketed references, or with minimal references. If we want citations for internal verification, we could include them lightly or as footnotes. Implement prompt instructions accordingly (e.g. “provide a concise note without inline citations”). Acceptance Criteria:
The output note does not overwhelm the text with citations, unless desired. Ideally, it reads like a normal note. (We may allow one general reference if needed, but test with and without.)
The content of the note can be traced to the sources if needed (perhaps internally we keep track, but the user sees a clean version).
The user finds the note suitable to copy-paste into their 1004 form narrative without much editing.
Feature: Note Export & Sharing
Story CHUI-Note-3: Copy or Download Note (Nice-to-have) – Technical Implementation: Provide a convenient way to export the generated note. This could be as simple as a “Copy to Clipboard” button that copies the text, or a “Download as .docx” for a more polished output. Use browser APIs for copying text, and a library (or server function) to generate a Word/PDF if going that route. Acceptance Criteria:
The user can easily take the generated note out of the app: e.g. clicking “Copy” flashes a confirmation that text is copied, and indeed they can paste it into another document.
(If download offered) Clicking download provides a file (e.g. ComplianceNote.txt or .docx) containing the note. The file is properly formatted (UTF-8 text, simple formatting if docx).
The export does not include any hidden or extraneous content (no extra code metadata), just the note (and perhaps a header if needed).
Story CHUI-Note-4: Attach Note to Chat Record (Optional) – Technical Implementation: Save the generated note in the database, linked to the chat (maybe in a notes table or as a special message type). This way the user can retrieve previously generated notes or the system can display it later. Acceptance Criteria:
When a note is generated, it’s saved so that if the user comes back to the chat, they can see/regenerate it or have a history of notes for that address.
Only one note per chat might be stored (or multiple versions if generated multiple times – could keep the latest).
This stored note can be included if the user exports the entire chat transcript (see Audit Trail epic).
Epic 10: Audit Trail & Document Source Logging
Epic Description: Ensure there is a robust audit trail of the Q&A interactions for compliance and transparency. This epic focuses on capturing what information was provided to the user and which sources were cited, in a way that can be reviewed later by auditors or admin. This also helps users trust the output because every answer’s provenance is recorded. Additionally, it covers the ability to export or review the conversation for record-keeping.
Feature: Conversation and Source Logging
Story CHUI-Audit-1: Store Answer Sources in DB (MVP) – Technical Implementation: Extend the chat message persistence (from Epic 2) to also record the sources used for each AI answer. For example, have a chat_message_references join table, or store a JSON array of source document IDs/sections in the chat_messages row for the answer. This way, each answer in the database is linked to the exact citations shown to the user. Acceptance Criteria:
In the database, for every assistant message, there is a record of which source documents and sections were cited. This data should include identifiers that map to the actual documents (e.g. document ID and section number or a stable reference key).
If an answer had no citations (e.g. a fallback “I’m not sure” message), that can be recorded as such (empty references or a flag).
The data model allows reconstruction of the sources for any given answer later, even if documents are updated (could store a version reference too, to know which version of code was cited).
Story CHUI-Audit-2: Full Chat Export with Sources (Nice-to-have) – Technical Implementation: Provide users or admins the ability to export an entire chat session, including questions, answers, and citations. This could be a formatted PDF or text file that shows the dialogue and, for each answer, lists the source references (maybe as footnotes or endnotes). Use a server-side PDF generation library or client-side conversion. Ensure any sensitive info is handled (likely not an issue since it’s mostly public code references and the user’s own questions). Acceptance Criteria:
A user (or admin) can click an “Export Chat” button to download a transcript of the conversation. The exported file clearly delineates user questions, AI answers, and includes the citations (for example, numbered references linking to a source list at the end of the document).
The export captures the conversation accurately as seen on screen, including any disclaimers or notes.
The format is clean and readable (for PDF, use simple styling; for text/Markdown, ensure formatting is consistent).
Story CHUI-Audit-3: Admin Conversation Review (MVP) – Technical Implementation: Allow an admin user to review chat histories for support or compliance purposes. This could be done through a secured admin interface where they can search for a user or address and load the transcript. Since chats are already stored in the DB, this is a matter of providing a UI to retrieve them. Possibly reuse the chat UI component in a read-only mode for admin. Acceptance Criteria:
An admin with proper credentials can access any chat session by ID or user, through an internal tool or direct DB query.
The admin can see exactly what the user saw: the questions, the answers given, and the sources cited.
This capability is strictly admin-only (enforced via auth), and all admin access of user chats could be logged (for privacy audit) – e.g. record which admin viewed which chat and when.
Story CHUI-Audit-4: Usage Analytics & Feedback Loop (Nice-to-have) – Technical Implementation: Collect analytics on chat usage and content for continuous improvement. For example, log events for each question answered (timestamp, user, maybe categories of sources used). This can help identify which jurisdictions get the most questions or which code topics are frequently asked. If feasible, implement a simple feedback mechanism: the user could thumbs-up/thumbs-down an answer. That feedback would be stored to flag answers or sessions for review. Acceptance Criteria:
The system gathers basic stats such as number of questions asked per day, distribution by jurisdiction, etc., without impacting performance (e.g. insert small records asynchronously).
If a feedback mechanism is present, user ratings on answers are captured. For example, a thumbs-down might prompt “Thank you, we’ll review this answer.”
The admin has a way to view these analytics (could be a dashboard or even just querying the DB and using external tools initially). This ensures a feedback loop to improve the knowledge base as suggested in the blueprint
file-3andax7xbf65ud3icfud5q
.
Epic 11: Admin Tools for Chat & Data Management
Epic Description: Develop internal admin-facing tools to manage the Pro-tier Chat features. This includes capabilities like uploading new documents, triggering re-indexing (from Epic 7), editing prompt templates (from Epic 8), and monitoring system health. These tools are for Ordrly’s internal use to keep the system content updated and troubleshoot user issues. They should integrate into the existing admin panel (Next.js pages protected by Supabase Auth roles).
Feature: Knowledge Base Management
Story CHUI-Admin-1: Ordinance Document Upload (Nice-to-have) – Technical Implementation: Provide an interface where an admin can add a new source document to the knowledge base. For example, a form to upload a PDF or paste text of a new ordinance or code, select the jurisdiction and category, and submit. On submission, the system processes the document: extracting text, chunking it, and calling the embedding pipeline (this could hook into the same process as Story RAG-1). Use file storage if needed (Supabase Storage or an S3 bucket) to keep the original file. Acceptance Criteria:
An admin can successfully add a new code document for a jurisdiction through the UI (upload or link).
The system confirms the addition and begins processing; after a short time, the new document’s content is available for Q&A (e.g. ask a question related to it and get an answer).
Proper validations: if the document already exists or if the format is unsupported, the admin is informed. Also, ensure large documents are handled (perhaps by background jobs due to time).
Story CHUI-Admin-2: Document List & Status (Nice-to-have) – Technical Implementation: In the admin dashboard, display a list of all documents in the knowledge base with key info (name, jurisdiction, last updated, number of embeddings, etc.). This helps the admin see what’s loaded and if anything needs attention (e.g. a flag if embedding is out-of-date or if last update check failed). Could reuse a simple table view component. Acceptance Criteria:
Admin can navigate to a “Documents” section and see all source documents currently in the system.
Each entry shows data like which version of code it is (edition/year) and when it was last ingested.
There are indicators or filters to quickly identify stale or missing data (for example, highlight documents that might need an update as identified in Epic 7).
Story CHUI-Admin-3: One-Click Re-index from Admin UI (MVP) – Technical Implementation: Tie into Epic 7’s manual refresh capability by placing a button in the admin UI. For example, on the Documents list or on a jurisdiction page, an admin can click “Re-index” next to a specific document or “Re-index All” for an entire jurisdiction. This triggers the backend routine to regenerate embeddings (same as Story Data-5). Provide feedback in the UI (like a loading spinner or a status update such as “Re-indexing in progress…”). Acceptance Criteria:
The admin UI has controls for initiating re-index of documents, either individually or in bulk.
When clicked, the system acknowledges the command (e.g. disables the button and shows “Processing…”). Once done (which might be a few seconds to minutes), the UI could refresh the document’s status (last indexed timestamp updates).
If re-index fails (error in embedding, etc.), the admin is notified with an error message so they can take action (check logs, retry, etc.).
Feature: Prompt & Chat Management
Story CHUI-Admin-4: Manage Prompt Templates (Nice-to-have) – Technical Implementation: Build an interface to manage the prompt templates (as noted in Story Prompt-4). This would present the list of templates from the DB, allow editing text or adding new entries. Could simply reuse a generic table editor or create a small form. Ensure only admin can access. Acceptance Criteria:
Admins can view all prompt templates and their contents via the admin panel.
They can create a new template or edit an existing one, and the changes persist to the database.
The changes reflect for end users in their chat UI (new prompts available, edited text updated) by the next session or immediately if we fetch templates live.
Story CHUI-Admin-5: Chat Session Lookup (MVP) – Technical Implementation: Simplify support by letting admin search for a user or address and pull up the associated chat. Implement a search bar in admin panel: input an address or user email, and show matching chats. Admin can click to view the conversation (could navigate to a page that uses the same Chat UI component but loaded with that chat’s ID in read-only mode). Acceptance Criteria:
Admin can enter a user identifier (email/ID) or an address and retrieve a list of relevant chat sessions.
Selecting a session shows the full conversation, including all messages and sources, in a format similar to the user view. It's read-only (no ability for admin to post messages as user, unless we explicitly allow an “impersonation” feature which is not in scope).
This helps in troubleshooting if a user reports an incorrect answer – the admin can see what question was asked and what answer was given along with sources.
Story CHUI-Admin-6: Usage Dashboard (Optional) – Technical Implementation: Provide a simple dashboard showing key metrics: e.g. number of active Pro users, number of chats created, total questions asked in the last week, etc. This can query aggregated data from the chat and message tables or use any analytics logs. It gives the founder insight into feature usage. Acceptance Criteria:
Admin can see at a glance how the chat feature is being used (e.g. “25 chats this week, 100 questions asked, 5 fallbacks triggered”).
The data is presented in a clean format (could be just text or basic charts if easy).
This page is also admin-only and updates either in real-time or on page load with current stats.
Epic 12: Pro-tier Access Control & Billing Integration
Epic Description: Restrict the Chat UI feature to Pro-tier users and integrate with Ordrly’s subscription system (Stripe + Supabase). This epic ensures that only paying users (or those on a trial flagged as Pro) can use the new chat, and handles the user experience around upgrading/downgrading with respect to chat access.
Feature: Subscription Gatekeeping
Story CHUI-Access-1: Feature Flag for Pro Users (MVP) – Technical Implementation: Use a condition in the app to show or hide the Pro Chat UI based on the user’s subscription status. For instance, Supabase might have the user’s role or a is_pro flag in the JWT or profile table (populated via Stripe webhooks). Wrap the chat UI component in a check: if not Pro, do not render the interface (or render a teaser/disabled state). This likely leverages existing Stripe integration in the monorepo. Acceptance Criteria:
A non-subscribed (free-tier) user does not see the full Chat UI in the app’s navigation. For example, the “Chat” or “Addresses” panel might be hidden or greyed out.
If a free user somehow navigates to the chat page, it either redirects them to upgrade or shows a message that this feature is Pro-only.
A Pro-tier user (with active subscription) sees the chat interface normally and can use all its functions.
Story CHUI-Access-2: Upgrade Prompt for Free Users (MVP) – Technical Implementation: Encourage free users to upgrade by showing an enticing prompt. For example, in place of the chat interface (or on hovering a locked chat icon), display a message: “🔒 Interactive Code Compliance Chat is available on Ordrly Pro. Upgrade now to ask compliance questions and get instant answers with code citations.” Provide a link/button to the billing upgrade page. Ensure this marketing is only shown to non-Pro users. Acceptance Criteria:
The UI clearly communicates that the chat feature is a premium offering to users who don’t have access.
The upgrade button/link takes the user to the existing billing page or Stripe checkout flow.
After upgrading (and upon return or webhook processing), the user should immediately gain access (the UI should update to show the chat, possibly requiring a refresh or triggered via real-time update).
Feature: Subscription State Changes
Story CHUI-Access-3: Post-Upgrade Access Activation (MVP) – Technical Implementation: Integrate with the Stripe-Supabase webhook or subscription listener so that when a user upgrades to Pro, their account is marked accordingly (e.g. Supabase auth.users or a join table shows Pro status). Ensure that as soon as this status is set, the app recognizes it (perhaps by re-fetching user profile or via a Realtime subscription on the profile). The user should not have to log out/in to see the chat feature enabled. Acceptance Criteria:
Test scenario: a user on free tier goes through the upgrade flow. Upon successful payment, within a short time (seconds), the Chat UI unlocks for them. For example, if they were on a placeholder screen, it now switches to the actual chat interface automatically or after a refresh.
The system correctly updates the database for the user’s subscription (this may already be implemented in the monorepo; we ensure to tie into that).
If any issue occurs (e.g. webhook delay), the user can manually refresh or log out/in to sync, and then the chat is accessible. But ideally, it’s seamless.
Story CHUI-Access-4: Downgrade or Expiration Handling (MVP) – Technical Implementation: Ensure that if a user’s Pro subscription expires or is cancelled, their access to the chat feature is revoked. This can be handled by the same gating logic: once the subscription end-date passes (as recorded in the DB), the feature flag turns off. Any existing chats the user had might become read-only or inaccessible (perhaps still let them view past chats, but not ask new questions). Provide messaging if they attempt to use it after downgrade (“Your Pro access has ended. Renew to continue using the chat.”). Acceptance Criteria:
When a Pro user’s subscription ends, the next time they use the app (or in real-time if we listen to events), the chat UI is locked again. They should be notified appropriately rather than just seeing it disappear.
The user’s past chat data is preserved in the database, but access to ask new questions is restricted. Possibly they can still view old Q&A (this detail can be decided – minimal approach: hide everything if not Pro, or nice approach: show history but disable input).
Re-upgrading reactivates everything normally, and the old chats are still there when they regain access.
Story CHUI-Access-5: Free Trial Limitations (Optional) – Technical Implementation: If Ordrly offers a free trial or limited access version of the chat (e.g. first 1-2 questions free, or a limited knowledge base), implement the logic for that. For example, allow a non-Pro user to create one chat and ask up to N questions before requiring upgrade. This could be done by counting messages or checking a trial flag on the user. Not critical for initial launch, but something to consider for growth. Acceptance Criteria:
(If implemented) A new user can try the chat feature briefly without payment, under predefined constraints (like only the national code, or only 3 questions).
The system tracks the usage and once the trial limits are hit, prompts the user to upgrade to continue.
Trial experience is smooth and doesn’t undermine the Pro value (it should give just enough taste). If no trial is offered, this story is skipped.
Epic 13: Monorepo Integration & Reusability
Epic Description: (Cross-cutting considerations) Ensure the new Chat UI and backend seamlessly integrate into Ordrly’s Next.js 14 + Supabase monorepo, reusing existing components and utilities where possible. While not a user-facing epic per se, these are technical stories to align with the project’s architecture and avoid reinventing the wheel. This includes using the established design system (Tailwind UI, etc.), database schemas, and internal frameworks (logging, error handling) described in the automation blueprint.
Feature: UI Integration & Reuse
Story CHUI-Integrate-1: Reuse Existing UI Components (MVP) – Technical Implementation: Leverage Ordrly’s existing frontend components for consistency. For example, if there’s already a component for list management or modals, use that for the chat list or address input modal. If the monorepo uses a common layout or theme context, ensure the chat pages conform to it (navigation, header, etc.). This reduces development effort and keeps the UI uniform. Acceptance Criteria:
The Chat UI pages and components are implemented within the Next.js app structure, not as an isolated app – they use the same routing, state management (if any global store), and styling as the rest of Ordrly.
Visual elements like buttons, inputs, and lists match the style from elsewhere in the app (the user shouldn’t feel a jarring transition using the chat vs other pages).
No duplicate UI code is introduced if a suitable component already exists in the codebase – e.g. the address autocomplete might reuse a map search component if one was used in another feature.
Story CHUI-Integrate-2: Consistent Theming with Tailwind (MVP) – Technical Implementation: Follow the Tailwind CSS design tokens and utility classes defined in the project. If using Tailwind UI library components, adapt them to the existing color scheme and breakpoints. Ensure dark mode or other global theming is respected if applicable. Acceptance Criteria:
The chat interface respects the global CSS (for instance, if the app supports dark mode, the chat UI should also support it gracefully).
All new styles are added in a maintainable way (e.g. extending the Tailwind config or using existing CSS modules) rather than hardcoded styles.
The UI has been tested across common browsers and devices, and looks cohesive within the overall application.
Feature: Backend & Services Integration
Story CHUI-Integrate-3: Use Shared API Utilities (MVP) – Technical Implementation: Build the chat API routes or serverless functions using the patterns in place. For example, if the project has a custom OpenAI wrapper (with logging, etc.)
file-3andax7xbf65ud3icfud5q
, use that for calling GPT-4.1-nano instead of raw fetch calls. Use the common database access layer (Supabase client) for queries. Also use the existing error handling and monitoring (console logs, or any Sentry integration) to capture issues. Acceptance Criteria:
The new backend code for the chat (such as /pages/api/chat.ts or equivalent in Next.js 14 app directory) follows the same structure as other API routes – including authentication checks, using Supabase SDK for DB operations, and proper error responses.
Any third-party calls (OpenAI) go through the central utility that might handle API keys and retry logic, ensuring consistency and secure usage of secrets (which remain in Vercel/Supabase secure storage, not hardcoded)
file-3andax7xbf65ud3icfud5q
.
Logging is in place for important events (like each question asked, each error) and integrates with the app’s logging system. For instance, significant actions could log to automation_logs or use console logging that ends up in Vercel logs, as described in the blueprint
file-3andax7xbf65ud3icfud5q
.
Story CHUI-Integrate-4: Edge Function & Queue Integration (MVP) – Technical Implementation: Offload heavy or long-running tasks to Supabase Edge Functions or job queues as appropriate, consistent with the blueprint’s approach of using serverless functions for automation
file-3andax7xbf65ud3icfud5q
. For example, the document ingestion (embedding) might be done in an Edge Function triggered by a new row, and not directly in a user request. If any step might exceed Vercel’s request timeout, make it an async job (e.g. using a Supabase function and polling or websockets to update the UI when done). Acceptance Criteria:
Identify operations that should be asynchronous (document re-indexing, large PDF parsing, etc.) and ensure they are implemented as background jobs rather than blocking user flows.
The system uses Supabase’s capabilities (such as pg_net for webhooks or scheduled functions) to handle these tasks internally without adding external services, aligning with the monorepo automation approach (no separate n8n or similar needed)
file-3andax7xbf65ud3icfud5q
.
Test that triggering these background tasks works end-to-end: e.g. admin clicks re-index, an Edge Function kicks off embedding, and on completion the new data is searchable. The user interface either waits with a loading state or informs the admin to check back, depending on expected duration.
Story CHUI-Integrate-5: Stripe & Supabase Sync (MVP) – Technical Implementation: Ensure the Stripe integration (likely already set up for subscription management) is correctly linked to enabling the chat features. For example, confirm that the webhook handler that updates user roles on payment is functioning and that our Pro-tier gating (Story Access-1) reads the correct flag (perhaps a profile.is_pro or a role claim in JWT). This story is essentially verifying and hooking up what exists to our new UI. Acceptance Criteria:
A test subscription event from Stripe (can use test mode) successfully triggers the Supabase update that marks a user as Pro. Our chat UI code recognizes this flag and allows access.
If any configuration is needed (like adding a claim to the JWT for “role: pro”), it is done and documented. The system uses that rather than implementing a redundant check.
No sensitive Stripe logic is on the front-end – it’s all handled in backend as it likely was; the front-end just responds to the final state (Pro or not).
By implementing these epics and stories, Ordrly’s Pro-tier Chat UI will deliver a robust, production-ready experience for appraisal professionals. The solution will allow users to query compliance requirements interactively with confidence, knowing answers are backed by authoritative references. At the same time, the design leverages Ordrly’s existing tech stack (Next.js, Supabase, pgVector, Stripe) and adheres to the internal automation blueprint for maintainability and scalability
file-3andax7xbf65ud3icfud5q
file-3andax7xbf65ud3icfud5q
. Each story above includes clear acceptance criteria to ensure the development meets the expected functionality and quality standards.