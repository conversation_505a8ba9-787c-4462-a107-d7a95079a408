Epic 1: Address & Jurisdiction Context Handling
Epic Description: Enable users to input an address and automatically determine the correct jurisdiction (city/county) and applicable code context for that location. This ensures all compliance answers are grounded in the proper local regulations
file-3andax7xbf65ud3icfud5q
. The chat session will be tied to the address, loading the relevant codes (e.g. building, plumbing, safety) for that jurisdiction as context.
Feature: Address Input & Validation
Story CHUI-Addr-1: Address Autocomplete Input (MVP) – Technical Implementation: Integrate an address autocomplete field (e.g. Google Places API or Mapbox) in the “New Chat” flow. As the user types an address, fetch suggestions and allow selection. On selection, retrieve the full formatted address and basic metadata (city, state, zip, etc.). If a third-party API is not available, provide a structured input form (Street, City, State) with client-side validation of required fields. Acceptance Criteria:
When the user begins typing an address, a dropdown of suggested completions appears (if online autocomplete is enabled).
Selecting a suggestion fills the address form with the complete address. If manual entry is used, the user can fill out required fields (e.g. City, State) and proceed.
If an invalid or incomplete address is entered, the user is prompted to correct it before proceeding (e.g. “Please enter a valid address”).
Story CHUI-Addr-2: Address Submission & Jurisdiction Lookup (MVP) – Technical Implementation: Upon submitting a new address (via selecting an autocomplete suggestion or manual entry), call a Next.js API route (or Supabase Edge Function) to resolve the jurisdiction. This might involve querying a Supabase table of jurisdictions or using a geocoding API to get city/county, then mapping that to known jurisdictions in Ordrly’s database. The system should identify the jurisdiction ID and relevant code set (e.g. “City of Springfield, using 2018 IBC + local amendments”). Store the resolved jurisdiction in the chat session metadata. Acceptance Criteria:
Given a valid address, the backend successfully determines the jurisdiction (city/county) and associates it with that address.
The system records the jurisdiction information (e.g. jurisdiction ID, name, code edition) in the new chat session’s data.
If the address is outside supported jurisdictions or cannot be resolved, the user is notified (e.g. “Address not found in our coverage area”) and cannot proceed with chat creation.
Story CHUI-Addr-3: Jurisdiction Confirmation Display (MVP) – Technical Implementation: After resolving the jurisdiction, display a confirmation in the UI (e.g. in the chat header or a system message) indicating the determined location and code context. For example: “Jurisdiction: Springfield, IL – using 2018 International Residential Code + local ordinances.” This gives the user confidence that the AI will reference the correct regulations. Acceptance Criteria:
Upon chat start, the UI clearly shows which jurisdiction and code baseline is being used for that chat (either in the chat header, subtitle, or initial system message).
The displayed jurisdiction matches the address provided and the data retrieved (city/county name is correct).
If any uncertainty in jurisdiction (e.g. address on a border), the system prompts the user to confirm or clarify (e.g. choose between City vs. County if needed).
Feature: Jurisdiction-Specific Context Initialization
Story CHUI-Addr-4: Load Jurisdiction Knowledge Base (MVP) – Technical Implementation: Once a jurisdiction is identified for the new chat, load or filter the relevant compliance documents for that area. The system will use the jurisdiction ID to scope all RAG (Retrieval-Augmented Generation) searches to that locale’s dataset
file-3andax7xbf65ud3icfud5q
. For example, set a context filter so that the vector search (pgVector) only retrieves code snippets from that city/county’s regulations. Acceptance Criteria:
The chat backend limits its document search to the identified jurisdiction’s data (e.g. doesn’t pull rules from the wrong city).
When the user asks a question, the sources found in the answer are from the correct jurisdiction (verified by source labels or content).
If the jurisdiction has a parent (e.g. a city that uses county codes), the system appropriately includes those relevant sources as well.
Story CHUI-Addr-5: Jurisdiction Context Persistence (MVP) – Technical Implementation: Tie the resolved jurisdiction to the chat session record in the database. Every message in that chat will carry this context so that even if the session is reloaded or accessed later, it “remembers” the address and jurisdiction. Leverage Supabase to store chat metadata (address string, jurisdiction ID, etc.) and use this for all subsequent queries. Acceptance Criteria:
The chat session database entry includes the full address and a reference to the jurisdiction.
On reloading or returning to an existing chat, the system still knows the context (jurisdiction) without asking the user again.
If the user switches between chats of different addresses, each chat uses its own stored jurisdiction context seamlessly.