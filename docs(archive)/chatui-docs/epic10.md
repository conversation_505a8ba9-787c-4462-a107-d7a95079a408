Epic 10: Audit Trail & Document Source Logging
Epic Description: Ensure there is a robust audit trail of the Q&A interactions for compliance and transparency. This epic focuses on capturing what information was provided to the user and which sources were cited, in a way that can be reviewed later by auditors or admin. This also helps users trust the output because every answer’s provenance is recorded. Additionally, it covers the ability to export or review the conversation for record-keeping.
Feature: Conversation and Source Logging
Story CHUI-Audit-1: Store Answer Sources in DB (MVP) – Technical Implementation: Extend the chat message persistence (from Epic 2) to also record the sources used for each AI answer. For example, have a chat_message_references join table, or store a JSON array of source document IDs/sections in the chat_messages row for the answer. This way, each answer in the database is linked to the exact citations shown to the user. Acceptance Criteria:
In the database, for every assistant message, there is a record of which source documents and sections were cited. This data should include identifiers that map to the actual documents (e.g. document ID and section number or a stable reference key).
If an answer had no citations (e.g. a fallback “I’m not sure” message), that can be recorded as such (empty references or a flag).
The data model allows reconstruction of the sources for any given answer later, even if documents are updated (could store a version reference too, to know which version of code was cited).
Story CHUI-Audit-2: Full Chat Export with Sources (Nice-to-have) – Technical Implementation: Provide users or admins the ability to export an entire chat session, including questions, answers, and citations. This could be a formatted PDF or text file that shows the dialogue and, for each answer, lists the source references (maybe as footnotes or endnotes). Use a server-side PDF generation library or client-side conversion. Ensure any sensitive info is handled (likely not an issue since it’s mostly public code references and the user’s own questions). Acceptance Criteria:
A user (or admin) can click an “Export Chat” button to download a transcript of the conversation. The exported file clearly delineates user questions, AI answers, and includes the citations (for example, numbered references linking to a source list at the end of the document).
The export captures the conversation accurately as seen on screen, including any disclaimers or notes.
The format is clean and readable (for PDF, use simple styling; for text/Markdown, ensure formatting is consistent).
Story CHUI-Audit-3: Admin Conversation Review (MVP) – Technical Implementation: Allow an admin user to review chat histories for support or compliance purposes. This could be done through a secured admin interface where they can search for a user or address and load the transcript. Since chats are already stored in the DB, this is a matter of providing a UI to retrieve them. Possibly reuse the chat UI component in a read-only mode for admin. Acceptance Criteria:
An admin with proper credentials can access any chat session by ID or user, through an internal tool or direct DB query.
The admin can see exactly what the user saw: the questions, the answers given, and the sources cited.
This capability is strictly admin-only (enforced via auth), and all admin access of user chats could be logged (for privacy audit) – e.g. record which admin viewed which chat and when.
Story CHUI-Audit-4: Usage Analytics & Feedback Loop (Nice-to-have) – Technical Implementation: Collect analytics on chat usage and content for continuous improvement. For example, log events for each question answered (timestamp, user, maybe categories of sources used). This can help identify which jurisdictions get the most questions or which code topics are frequently asked. If feasible, implement a simple feedback mechanism: the user could thumbs-up/thumbs-down an answer. That feedback would be stored to flag answers or sessions for review. Acceptance Criteria:
The system gathers basic stats such as number of questions asked per day, distribution by jurisdiction, etc., without impacting performance (e.g. insert small records asynchronously).
If a feedback mechanism is present, user ratings on answers are captured. For example, a thumbs-down might prompt “Thank you, we’ll review this answer.”
The admin has a way to view these analytics (could be a dashboard or even just querying the DB and using external tools initially). This ensures a feedback loop to improve the knowledge base as suggested in the blueprint
file-3andax7xbf65ud3icfud5q
.
