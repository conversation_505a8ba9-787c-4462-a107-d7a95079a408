Epic 11: Admin Tools for Chat & Data Management
Epic Description: Develop internal admin-facing tools to manage the Pro-tier Chat features. This includes capabilities like uploading new documents, triggering re-indexing (from Epic 7), editing prompt templates (from Epic 8), and monitoring system health. These tools are for Ordrly’s internal use to keep the system content updated and troubleshoot user issues. They should integrate into the existing admin panel (Next.js pages protected by Supabase Auth roles).
Feature: Knowledge Base Management
Story CHUI-Admin-1: Ordinance Document Upload (Nice-to-have) – Technical Implementation: Provide an interface where an admin can add a new source document to the knowledge base. For example, a form to upload a PDF or paste text of a new ordinance or code, select the jurisdiction and category, and submit. On submission, the system processes the document: extracting text, chunking it, and calling the embedding pipeline (this could hook into the same process as Story RAG-1). Use file storage if needed (Supabase Storage or an S3 bucket) to keep the original file. Acceptance Criteria:
An admin can successfully add a new code document for a jurisdiction through the UI (upload or link).
The system confirms the addition and begins processing; after a short time, the new document’s content is available for Q&A (e.g. ask a question related to it and get an answer).
Proper validations: if the document already exists or if the format is unsupported, the admin is informed. Also, ensure large documents are handled (perhaps by background jobs due to time).
Story CHUI-Admin-2: Document List & Status (Nice-to-have) – Technical Implementation: In the admin dashboard, display a list of all documents in the knowledge base with key info (name, jurisdiction, last updated, number of embeddings, etc.). This helps the admin see what’s loaded and if anything needs attention (e.g. a flag if embedding is out-of-date or if last update check failed). Could reuse a simple table view component. Acceptance Criteria:
Admin can navigate to a “Documents” section and see all source documents currently in the system.
Each entry shows data like which version of code it is (edition/year) and when it was last ingested.
There are indicators or filters to quickly identify stale or missing data (for example, highlight documents that might need an update as identified in Epic 7).
Story CHUI-Admin-3: One-Click Re-index from Admin UI (MVP) – Technical Implementation: Tie into Epic 7’s manual refresh capability by placing a button in the admin UI. For example, on the Documents list or on a jurisdiction page, an admin can click “Re-index” next to a specific document or “Re-index All” for an entire jurisdiction. This triggers the backend routine to regenerate embeddings (same as Story Data-5). Provide feedback in the UI (like a loading spinner or a status update such as “Re-indexing in progress…”). Acceptance Criteria:
The admin UI has controls for initiating re-index of documents, either individually or in bulk.
When clicked, the system acknowledges the command (e.g. disables the button and shows “Processing…”). Once done (which might be a few seconds to minutes), the UI could refresh the document’s status (last indexed timestamp updates).
If re-index fails (error in embedding, etc.), the admin is notified with an error message so they can take action (check logs, retry, etc.).
Feature: Prompt & Chat Management
Story CHUI-Admin-4: Manage Prompt Templates (Nice-to-have) – Technical Implementation: Build an interface to manage the prompt templates (as noted in Story Prompt-4). This would present the list of templates from the DB, allow editing text or adding new entries. Could simply reuse a generic table editor or create a small form. Ensure only admin can access. Acceptance Criteria:
Admins can view all prompt templates and their contents via the admin panel.
They can create a new template or edit an existing one, and the changes persist to the database.
The changes reflect for end users in their chat UI (new prompts available, edited text updated) by the next session or immediately if we fetch templates live.
Story CHUI-Admin-5: Chat Session Lookup (MVP) – Technical Implementation: Simplify support by letting admin search for a user or address and pull up the associated chat. Implement a search bar in admin panel: input an address or user email, and show matching chats. Admin can click to view the conversation (could navigate to a page that uses the same Chat UI component but loaded with that chat’s ID in read-only mode). Acceptance Criteria:
Admin can enter a user identifier (email/ID) or an address and retrieve a list of relevant chat sessions.
Selecting a session shows the full conversation, including all messages and sources, in a format similar to the user view. It's read-only (no ability for admin to post messages as user, unless we explicitly allow an “impersonation” feature which is not in scope).
This helps in troubleshooting if a user reports an incorrect answer – the admin can see what question was asked and what answer was given along with sources.
Story CHUI-Admin-6: Usage Dashboard (Optional) – Technical Implementation: Provide a simple dashboard showing key metrics: e.g. number of active Pro users, number of chats created, total questions asked in the last week, etc. This can query aggregated data from the chat and message tables or use any analytics logs. It gives the founder insight into feature usage. Acceptance Criteria:
Admin can see at a glance how the chat feature is being used (e.g. “25 chats this week, 100 questions asked, 5 fallbacks triggered”).
The data is presented in a clean format (could be just text or basic charts if easy).
This page is also admin-only and updates either in real-time or on page load with current stats.