Epic 12: Pro-tier Access Control & Billing Integration
Epic Description: Restrict the Chat UI feature to Pro-tier users and integrate with Ordrly’s subscription system (Stripe + Supabase). This epic ensures that only paying users (or those on a trial flagged as Pro) can use the new chat, and handles the user experience around upgrading/downgrading with respect to chat access.
Feature: Subscription Gatekeeping
Story CHUI-Access-1: Feature Flag for Pro Users (MVP) – Technical Implementation: Use a condition in the app to show or hide the Pro Chat UI based on the user’s subscription status. For instance, Supabase might have the user’s role or a is_pro flag in the JWT or profile table (populated via Stripe webhooks). Wrap the chat UI component in a check: if not Pro, do not render the interface (or render a teaser/disabled state). This likely leverages existing Stripe integration in the monorepo. Acceptance Criteria:
A non-subscribed (free-tier) user does not see the full Chat UI in the app’s navigation. For example, the “Chat” or “Addresses” panel might be hidden or greyed out.
If a free user somehow navigates to the chat page, it either redirects them to upgrade or shows a message that this feature is Pro-only.
A Pro-tier user (with active subscription) sees the chat interface normally and can use all its functions.
Story CHUI-Access-2: Upgrade Prompt for Free Users (MVP) – Technical Implementation: Encourage free users to upgrade by showing an enticing prompt. For example, in place of the chat interface (or on hovering a locked chat icon), display a message: “🔒 Interactive Code Compliance Chat is available on Ordrly Pro. Upgrade now to ask compliance questions and get instant answers with code citations.” Provide a link/button to the billing upgrade page. Ensure this marketing is only shown to non-Pro users. Acceptance Criteria:
The UI clearly communicates that the chat feature is a premium offering to users who don’t have access.
The upgrade button/link takes the user to the existing billing page or Stripe checkout flow.
After upgrading (and upon return or webhook processing), the user should immediately gain access (the UI should update to show the chat, possibly requiring a refresh or triggered via real-time update).
Feature: Subscription State Changes
Story CHUI-Access-3: Post-Upgrade Access Activation (MVP) – Technical Implementation: Integrate with the Stripe-Supabase webhook or subscription listener so that when a user upgrades to Pro, their account is marked accordingly (e.g. Supabase auth.users or a join table shows Pro status). Ensure that as soon as this status is set, the app recognizes it (perhaps by re-fetching user profile or via a Realtime subscription on the profile). The user should not have to log out/in to see the chat feature enabled. Acceptance Criteria:
Test scenario: a user on free tier goes through the upgrade flow. Upon successful payment, within a short time (seconds), the Chat UI unlocks for them. For example, if they were on a placeholder screen, it now switches to the actual chat interface automatically or after a refresh.
The system correctly updates the database for the user’s subscription (this may already be implemented in the monorepo; we ensure to tie into that).
If any issue occurs (e.g. webhook delay), the user can manually refresh or log out/in to sync, and then the chat is accessible. But ideally, it’s seamless.
Story CHUI-Access-4: Downgrade or Expiration Handling (MVP) – Technical Implementation: Ensure that if a user’s Pro subscription expires or is cancelled, their access to the chat feature is revoked. This can be handled by the same gating logic: once the subscription end-date passes (as recorded in the DB), the feature flag turns off. Any existing chats the user had might become read-only or inaccessible (perhaps still let them view past chats, but not ask new questions). Provide messaging if they attempt to use it after downgrade (“Your Pro access has ended. Renew to continue using the chat.”). Acceptance Criteria:
When a Pro user’s subscription ends, the next time they use the app (or in real-time if we listen to events), the chat UI is locked again. They should be notified appropriately rather than just seeing it disappear.
The user’s past chat data is preserved in the database, but access to ask new questions is restricted. Possibly they can still view old Q&A (this detail can be decided – minimal approach: hide everything if not Pro, or nice approach: show history but disable input).
Re-upgrading reactivates everything normally, and the old chats are still there when they regain access.
Story CHUI-Access-5: Free Trial Limitations (Optional) – Technical Implementation: If Ordrly offers a free trial or limited access version of the chat (e.g. first 1-2 questions free, or a limited knowledge base), implement the logic for that. For example, allow a non-Pro user to create one chat and ask up to N questions before requiring upgrade. This could be done by counting messages or checking a trial flag on the user. Not critical for initial launch, but something to consider for growth. Acceptance Criteria:
(If implemented) A new user can try the chat feature briefly without payment, under predefined constraints (like only the national code, or only 3 questions).
The system tracks the usage and once the trial limits are hit, prompts the user to upgrade to continue.
Trial experience is smooth and doesn’t undermine the Pro value (it should give just enough taste). If no trial is offered, this story is skipped.