Epic 13: Monorepo Integration & Reusability
Epic Description: (Cross-cutting considerations) Ensure the new Chat UI and backend seamlessly integrate into Ordrly’s Next.js 14 + Supabase monorepo, reusing existing components and utilities where possible. While not a user-facing epic per se, these are technical stories to align with the project’s architecture and avoid reinventing the wheel. This includes using the established design system (Tailwind UI, etc.), database schemas, and internal frameworks (logging, error handling) described in the automation blueprint.
Feature: UI Integration & Reuse
Story CHUI-Integrate-1: Reuse Existing UI Components (MVP) – Technical Implementation: Leverage Ordrly’s existing frontend components for consistency. For example, if there’s already a component for list management or modals, use that for the chat list or address input modal. If the monorepo uses a common layout or theme context, ensure the chat pages conform to it (navigation, header, etc.). This reduces development effort and keeps the UI uniform. Acceptance Criteria:
The Chat UI pages and components are implemented within the Next.js app structure, not as an isolated app – they use the same routing, state management (if any global store), and styling as the rest of Ordrly.
Visual elements like buttons, inputs, and lists match the style from elsewhere in the app (the user shouldn’t feel a jarring transition using the chat vs other pages).
No duplicate UI code is introduced if a suitable component already exists in the codebase – e.g. the address autocomplete might reuse a map search component if one was used in another feature.
Story CHUI-Integrate-2: Consistent Theming with Tailwind (MVP) – Technical Implementation: Follow the Tailwind CSS design tokens and utility classes defined in the project. If using Tailwind UI library components, adapt them to the existing color scheme and breakpoints. Ensure dark mode or other global theming is respected if applicable. Acceptance Criteria:
The chat interface respects the global CSS (for instance, if the app supports dark mode, the chat UI should also support it gracefully).
All new styles are added in a maintainable way (e.g. extending the Tailwind config or using existing CSS modules) rather than hardcoded styles.
The UI has been tested across common browsers and devices, and looks cohesive within the overall application.
Feature: Backend & Services Integration
Story CHUI-Integrate-3: Use Shared API Utilities (MVP) – Technical Implementation: Build the chat API routes or serverless functions using the patterns in place. For example, if the project has a custom OpenAI wrapper (with logging, etc.)
file-3andax7xbf65ud3icfud5q
, use that for calling GPT-4.1-nano instead of raw fetch calls. Use the common database access layer (Supabase client) for queries. Also use the existing error handling and monitoring (console logs, or any Sentry integration) to capture issues. Acceptance Criteria:
The new backend code for the chat (such as /pages/api/chat.ts or equivalent in Next.js 14 app directory) follows the same structure as other API routes – including authentication checks, using Supabase SDK for DB operations, and proper error responses.
Any third-party calls (OpenAI) go through the central utility that might handle API keys and retry logic, ensuring consistency and secure usage of secrets (which remain in Vercel/Supabase secure storage, not hardcoded)
file-3andax7xbf65ud3icfud5q
.
Logging is in place for important events (like each question asked, each error) and integrates with the app’s logging system. For instance, significant actions could log to automation_logs or use console logging that ends up in Vercel logs, as described in the blueprint
file-3andax7xbf65ud3icfud5q
.
Story CHUI-Integrate-4: Edge Function & Queue Integration (MVP) – Technical Implementation: Offload heavy or long-running tasks to Supabase Edge Functions or job queues as appropriate, consistent with the blueprint’s approach of using serverless functions for automation
file-3andax7xbf65ud3icfud5q
. For example, the document ingestion (embedding) might be done in an Edge Function triggered by a new row, and not directly in a user request. If any step might exceed Vercel’s request timeout, make it an async job (e.g. using a Supabase function and polling or websockets to update the UI when done). Acceptance Criteria:
Identify operations that should be asynchronous (document re-indexing, large PDF parsing, etc.) and ensure they are implemented as background jobs rather than blocking user flows.
The system uses Supabase’s capabilities (such as pg_net for webhooks or scheduled functions) to handle these tasks internally without adding external services, aligning with the monorepo automation approach (no separate n8n or similar needed)
file-3andax7xbf65ud3icfud5q
.
Test that triggering these background tasks works end-to-end: e.g. admin clicks re-index, an Edge Function kicks off embedding, and on completion the new data is searchable. The user interface either waits with a loading state or informs the admin to check back, depending on expected duration.
Story CHUI-Integrate-5: Stripe & Supabase Sync (MVP) – Technical Implementation: Ensure the Stripe integration (likely already set up for subscription management) is correctly linked to enabling the chat features. For example, confirm that the webhook handler that updates user roles on payment is functioning and that our Pro-tier gating (Story Access-1) reads the correct flag (perhaps a profile.is_pro or a role claim in JWT). This story is essentially verifying and hooking up what exists to our new UI. Acceptance Criteria:
A test subscription event from Stripe (can use test mode) successfully triggers the Supabase update that marks a user as Pro. Our chat UI code recognizes this flag and allows access.
If any configuration is needed (like adding a claim to the JWT for “role: pro”), it is done and documented. The system uses that rather than implementing a redundant check.
No sensitive Stripe logic is on the front-end – it’s all handled in backend as it likely was; the front-end just responds to the final state (Pro or not).
By implementing these epics and stories, Ordrly’s Pro-tier Chat UI will deliver a robust, production-ready experience for appraisal professionals. The solution will allow users to query compliance requirements interactively with confidence, knowing answers are backed by authoritative references. At the same time, the design leverages Ordrly’s existing tech stack (Next.js, Supabase, pgVector, Stripe) and adheres to the internal automation blueprint for maintainability and scalability
file-3andax7xbf65ud3icfud5q
file-3andax7xbf65ud3icfud5q
. Each story above includes clear acceptance criteria to ensure the development meets the expected functionality and quality standards.