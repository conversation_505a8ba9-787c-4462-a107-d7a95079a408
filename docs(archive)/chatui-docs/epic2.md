Epic 2: Chat Session Lifecycle Management
Epic Description: Allow Pro users to manage multiple chat sessions, each associated with a property address. Chats (also referred to as “addresses” in the UI) can be created, persisted, listed, and revisited. This epic covers creating new chats, saving conversation history to the database, switching between sessions, and basic management like renaming or deleting chats.
Feature: Chat Creation & Initialization
Story CHUI-Chat-1: New Chat Session Creation (MVP) – Technical Implementation: Implement a “New Chat” button (e.g. in the left sidebar). When clicked, it triggers the address entry flow (Epic 1). After the user submits an address, create a new chat session record in Supabase (e.g. chats table) with a unique Chat ID, the user’s ID, the address & jurisdiction info, and timestamp. Reuse existing components for creating entries (if the monorepo has similar flows for creating projects or records). Acceptance Criteria:
Clicking “New Chat” opens a prompt or modal for entering an address (as per Epic 1).
Upon submitting a valid address, a new chat session is created in the backend with the correct user association and address details.
The new chat immediately appears in the chat list (left pane) with the address as its label, and the main dialog area is cleared/prepared for a new conversation.
Story CHUI-Chat-2: Initialize Chat Session State (MVP) – Technical Implementation: After creating the chat record, initialize the front-end state for the conversation. This might include adding an entry in the React state or context for the new chat, setting it as the active chat, and preparing an empty messages list. Also, consider seeding the chat with a welcome or context message (optional). Leverage existing state management (if any) from the current chat widget, adapting it to support multiple sessions. Acceptance Criteria:
After creation, the new chat session is active and ready to use without requiring a page reload.
The conversation view shows no prior messages (or an optional welcome message) for the new chat.
The address/jurisdiction context is correctly attached so that the first user question will use the right context.
Feature: Conversation Persistence
Story CHUI-Chat-3: Persist Messages to Database (MVP) – Technical Implementation: Every user query and AI answer should be saved in a chat_messages table (or similar) in Supabase. Each message record includes chat_id, role (user or assistant), message content, timestamp, and any metadata (e.g. reference IDs for AI answers). Use Supabase client libraries or RPC calls to insert messages after they are sent or received. Ensure write operations occur asynchronously so as not to block the UI (e.g. insert after streaming is done). This aligns with the blueprint guidance that logged-in users can have their chats stored
file-3andax7xbf65ud3icfud5q
. Acceptance Criteria:
After a user sends a message, a record is created in the database with the correct content, user ID, and chat ID.
After the AI responds, the answer is also stored with role=assistant and linked to the same chat ID, including any citations (see Audit Trail epic) stored in metadata.
Data integrity: if the user closes the app and returns, all past messages for that address chat are loaded from the database (not just cached in the browser).
Story CHUI-Chat-4: Privacy & Access Control (MVP) – Technical Implementation: Ensure that chat session data is secured so only the owning user (and an admin, if necessary) can access it. Use Supabase Row-Level Security policies or similar to enforce that each chats and chat_messages record belongs only to the creator’s user_id
file-3andax7xbf65ud3icfud5q
. This will prevent other users from ever accessing someone else’s chat transcripts. Acceptance Criteria:
Only authenticated users with an active Pro subscription (see Epic 12) can create and load chat sessions.
A user can only retrieve chats that they created – attempting to access another user’s chat (via ID manipulation) is blocked by the backend.
Admin users (with a special role) may bypass this for support purposes, but all access is logged (as per Audit Trail).
Feature: Chat List & Multi-Chat Management
Story CHUI-Chat-5: Display Chat Sessions List (MVP) – Technical Implementation: In the left sidebar, list all chats (addresses) the user has created, sorted by last active or creation time. Each entry shows the address (or a user-defined name if available). Use a scrollable list UI (Tailwind UI list components) that fits the design. Implement retrieval of this list via Supabase query on user’s chats. The component should update in real-time if new chats are added (Supabase subscriptions or revalidation) so the list stays current. Acceptance Criteria:
Upon opening the chat UI, the left panel shows all existing chat sessions for that user.
Each chat entry is labeled with the property address (e.g. “123 Main St, Springfield”) or a default name.
The list is updated to include new chats immediately after creation, and reflects deletions or renames (if those features are used).
Story CHUI-Chat-6: Switch Between Chats (MVP) – Technical Implementation: Allow the user to click on an address in the left pane to load that conversation. Implement a click handler that sets the active chat context: fetch that chat’s messages from the database (paged or full load) and update the main dialog view. If the current chat has an in-progress answer, optionally warn or cancel it before switching. Ensure the jurisdiction context is swapped according to the selected chat’s stored data. Acceptance Criteria:
Clicking on a different chat in the list immediately loads that chat’s message history in the dialog window.
The header or context info updates to the new chat’s address and jurisdiction.
The previously active chat’s state (if any unsent input or ongoing response) is handled gracefully (e.g. cleared or stored) and does not bleed into the new chat.
Story CHUI-Chat-7: Rename Chat Session (Nice-to-have) – Technical Implementation: Allow users to rename a chat (e.g. give a nickname or property identifier instead of the full address). This could be done via an “Edit” icon on the chat list item. Provide a text input for the new name and save it to the chats table (new field for custom name). Acceptance Criteria:
The user can rename a chat session via the UI (e.g. clicking a pencil icon toggles an editable field).
After saving, the new name appears in the chat list and maybe as the header, while still retaining the underlying address in the data.
Renaming does not break any linkage – the chat continues to function normally with its messages intact.
Story CHUI-Chat-8: Delete Chat Session (Nice-to-have) – Technical Implementation: Provide an option to delete a chat (e.g. a trash icon on the chat list item or a context menu). On delete, remove the chat and all associated messages from the database (or mark them as deleted for potential recovery) using a Supabase RPC or transaction to maintain integrity. Prompt the user for confirmation (“Are you sure you want to delete this chat? This cannot be undone.”). Acceptance Criteria:
The user can delete a chat session and its history via the UI with a confirmation step.
After deletion, the chat is removed from the list and the UI switches to either no chat selected or the next available chat.
The underlying data is deleted/hidden on the backend, and the user can no longer access that chat. (Ensure a deleted chat’s data is not searchable or included in RAG results anymore.)