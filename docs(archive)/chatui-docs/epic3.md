Epic 3: Chat Conversation UI & Interaction
Epic Description: Create an intuitive chat interface similar to ChatGPT, consisting of the main dialog window where user and AI messages appear. This epic focuses on the message display format, input controls, streaming responses, and overall user interaction within the chat. It ensures that messages are clearly differentiated by speaker, that the user can easily submit questions, and that responses are shown in a user-friendly way (with streaming and markdown support). Basic error handling and the inclusion of compliance disclaimers are also covered.
Feature: Message Display & Formatting
Story CHUI-UI-1: User vs. AI Message Styling (MVP) – Technical Implementation: Display conversation messages in a chat bubble format with distinct styling for user and AI. For example, user messages might be right-aligned or have a different background color, while AI responses are left-aligned. Use Tailwind CSS (possibly Tailwind UI pre-built chat components) for consistent design. The component should support long text wrapping and be mobile-responsive. Acceptance Criteria:
User messages and AI messages are visually distinct (different colors or alignment) so the conversation flow is easy to follow.
The chat window auto-scrolls to the latest message when a new message is added.
The design matches the app’s style theme and is consistent with other Ordrly UI elements (reusing existing CSS classes or components where possible).
Story CHUI-UI-2: Markdown & Rich Text Support (MVP) – Technical Implementation: Ensure the chat can render basic rich text in AI responses, since answers may include lists, bold terms (e.g. code section numbers), or other formatting. Use a React markdown renderer or sanitize and insert HTML for known formatting from the AI. Also handle URLs in answers (e.g. if an answer references an external site or document link). Acceptance Criteria:
If an AI answer contains bullet points, numbered lists, or other Markdown (like - or 1. prefixes), the chat renders them as HTML lists properly.
Bold or italic text in answers is displayed with proper styling.
Any hyperlink included in an answer is clickable and opens in a new tab (with proper security like rel="noopener").
Feature: Message Input & Submission
Story CHUI-UI-3: Query Input Box & Send (MVP) – Technical Implementation: Provide a text input area at the bottom of the chat for the user to type questions. The input should allow multi-line entry (e.g. auto-expand or a toggle to enlarge, since some questions may be detailed) but hitting Enter (or clicking a “Send” button) will submit the question. Implement the send action to call the Next.js API route (e.g. /api/chat) with the user message, current chat ID, and other needed context. Clear or disable the input while the question is being processed to prevent duplicate sends. Acceptance Criteria:
The user has an obvious place to type their question (a text box with placeholder like “Ask a question about the property’s compliance…”).
Hitting “Enter” or clicking the send icon submits the question. The input is then cleared (or disabled) and the user’s message appears in the chat immediately.
The backend API receives the message with the correct chat/session identifier, and the UI is ready to display the incoming answer.
Story CHUI-UI-4: Input Validation & Multi-line Handling (MVP) – Technical Implementation: Handle edge cases in the input: if the user tries to send an empty question or only whitespace, do not call the API and maybe show a gentle prompt (“Please enter a question”). If the user input is very long or contains special characters, ensure they are transmitted safely (proper encoding). Also support multi-line questions: allow the user to press Shift+Enter for a new line without sending. Acceptance Criteria:
Trying to send an empty query does nothing except maybe an error tooltip – it should not crash the app.
Users can add line breaks in their question (e.g. listing multiple points) and those line breaks are preserved in the message sent to the AI.
The input box can handle at least a few hundred characters of text without breaking layout (to accommodate detailed questions).
Feature: Streaming Responses & Feedback
Story CHUI-UI-5: Streaming AI Response Display (MVP) – Technical Implementation: Stream the assistant’s answer in real-time, token by token, to mimic ChatGPT’s interactive feel
file-3andax7xbf65ud3icfud5q
. Utilize OpenAI’s streaming API capabilities via the backend – the Next.js API route can flush chunks of text as they arrive. On the front-end, update the AI message as a typing animation. If using SSE or similar, ensure the frontend concatenates incoming parts. Reuse any existing streaming logic from the older chatbot if available. Acceptance Criteria:
When the AI is formulating an answer, the response text begins appearing within a second or two, rather than waiting for the full answer completion.
The user can see the answer “typing out” progressively, which improves perceived performance.
The streaming stops cleanly when the answer is done, and the final answer is stored/presented as a normal message (no partial text left unrendered).
Story CHUI-UI-6: Loading Indicator and Cancel (Nice-to-have) – Technical Implementation: Provide the user feedback that the system is working on an answer. This could be a subtle “…” animation or spinner in the chat window while streaming is in progress (possibly at the bottom or in the title bar). Optionally, allow the user to cancel an in-progress response (e.g. an “Stop” button like ChatGPT) if the answer is lengthy or the question was mistaken. Implement cancel by aborting the request on the client and instructing the backend to stop the OpenAI stream (if possible). Acceptance Criteria:
While waiting for an answer, the UI clearly indicates that a response is loading (e.g. a loading spinner or placeholder message).
If implemented, clicking a “Stop Response” control will halt the streaming and no further tokens are shown. The partial answer may either disappear or remain with an indication that it was stopped.
Cancelled or interrupted answers are not saved to the chat history (or are marked appropriately) to avoid confusion.
Feature: Errors & Disclaimers
Story CHUI-UI-7: Error Handling Message (MVP) – Technical Implementation: Anticipate possible errors (network issues, server errors, AI model errors). If the API returns an error (non-200 or a specific error payload), catch it and display an error message in the chat. For example, if the OpenAI call fails or times out, show a system-style message: “Error: Sorry, something went wrong. Please try again.” Possibly include a retry button on that message. Ensure this error message is styled distinctly (e.g. red text or warning icon) and does not break the chat flow. Acceptance Criteria:
Simulate an API failure (e.g. by disconnecting network or forcing an error) – the user sees an error message in the chat window informing them of the failure.
The error message does not disrupt the existing messages (it appears as a chat entry at the appropriate place in sequence).
After an error, the user can continue the conversation (send a new question or retry the previous one) normally.
Story CHUI-UI-8: Compliance Disclaimer in UI (MVP) – Technical Implementation: Include a visible disclaimer about AI-generated answers. This can be a static text in the chat interface (for example, a small note below the input or above the chat) and/or appended automatically to each answer. According to policy, something like: “Answers are generated by AI based on regulations. Always verify with local authorities.” should be shown
file-3andax7xbf65ud3icfud5q
. A possible implementation: automatically append a sentence to the end of every AI answer via post-processing, or include it as part of the prompt so the model outputs it. Alternatively, display a persistent note in the UI. Acceptance Criteria:
The interface clearly communicates a disclaimer about the AI answers (either in each answer or in a fixed location).
Example: Every AI answer ends with a line “(Always verify with local authorities.)” in italic or lighter text, as per compliance guidelines
file-3andax7xbf65ud3icfud5q
.
The disclaimer text is present for all users and cannot be disabled, ensuring legal/ethical compliance with Ordrly’s accuracy policies.
