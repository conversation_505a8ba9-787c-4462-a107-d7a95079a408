Epic 4: Citations & Source Panel
Epic Description: Display the sources (code sections, ordinances, etc.) used to generate each AI answer. The right-hand source panel will show the documents and snippets cited, giving users transparency and an ability to verify the answer against authoritative references. This epic includes organizing sources by category (e.g. building, plumbing, safety codes), viewing details of each source, and keeping the source list in sync with the Q&A.
Feature: Source Panel User Interface
Story CHUI-Source-1: Source Panel Layout & Toggle (MVP) – Technical Implementation: Implement a right sidebar panel that can show a list of sources for the current AI response. The panel can be always visible in desktop view (mimicking ChatGPT’s citation panel) and toggleable on mobile. Use Tailwind UI components (e.g. a two-column layout) to position this panel. Initially, the panel may be empty or have a placeholder (“Sources will appear here”). Once the AI responds with citations, populate this panel. Acceptance Criteria:
The chat interface includes a right-side area reserved for citations. On smaller screens, this panel can collapse or be shown via a “Show Sources” button.
By default (before any question is asked, or if an answer has no citations), the panel displays a helpful placeholder or remains hidden.
The layout is responsive and does not overlap or hide the main chat text (content area adjusts when panel is shown).
Story CHUI-Source-2: Display Cited Documents (MVP) – Technical Implementation: When the AI provides an answer with citations, list each unique source document in the source panel. Each entry should include the document title or shorthand (e.g. “2018 IRC §R314 – Smoke Alarms”) and possibly an icon or label for its category (e.g. [🏠 Building], [🔥 Fire], [💧 Plumbing]). If the model provides section numbers or titles, use those; otherwise map the snippet to the document metadata stored. Show a short snippet or summary of the part used (a few relevant lines) under each source title so the user sees context. Acceptance Criteria:
After an AI response is received, the source panel populates with an entry for each cited source.
Each source entry shows at minimum the name of the code or document and the specific section referenced (if available).
The user can see a brief quote or context from the source (e.g. the sentence containing the answer) beneath the title, giving immediate insight into what the source says.
Story CHUI-Source-3: Clickable Source Links (MVP) – Technical Implementation: Make each source entry interactive. For instance, clicking on a source could open the full document or the specific section in a modal or new tab. If Ordrly hosts the document text, it might open a modal viewer showing the entire section or page. If not, it might link to an external site or PDF at the correct section (if possible). Use the stored reference (like URL or document ID) in the citation metadata to locate the source. Acceptance Criteria:
Each source in the panel is clickable or has a clear “Open” action (like a link or button).
Upon clicking, the user either sees the full text of that code section in a pop-up/overlay without leaving the app, or is taken to an official source (new browser tab) for that code section.
The solution should allow the user to read the broader context of the citation, not just the one line snippet, for verification purposes.
Feature: Source Categorization & Navigation
Story CHUI-Source-4: Categorize Sources by Code Type (Nice-to-have) – Technical Implementation: If multiple sources are cited, group them by domain/category for clarity. For example, if an answer cites the Building Code and the Fire Code, visually separate those (perhaps collapse under headings “Building” and “Fire”). Use metadata from the documents (each document in the knowledge base can have a category tag like “Building”, “Electrical”, “Zoning”, etc.). The panel could list categories as sub-headers with sources underneath. Acceptance Criteria:
When sources from different categories are present, the panel groups them under category labels (e.g. “Mechanical Code (MC)” with all mechanical sources listed under it).
If sources are all of one type, no grouping header is needed (or only one group is shown).
Category labels are derived from document metadata (e.g. a field in the Supabase documents table) and reflect domains relevant to real estate appraisal (e.g. Safety, Structural, Plumbing).
Story CHUI-Source-5: Highlight Sources on Hover (Nice-to-have) – Technical Implementation: Improve the correlation between answer text and sources. When the user hovers over or taps a citation number in the AI answer (e.g. “[1]”), highlight the corresponding source entry in the panel (and vice versa: hovering on the source entry could highlight the citation in the text). This can be done by assigning IDs to citation references in the answer text and adding onHover events. Acceptance Criteria:
If the user moves their cursor over a citation marker in the answer (like [2]), the relevant source entry in the panel is visually emphasized (e.g. background highlight or scroll into view).
Conversely, hovering on a source entry could underline or highlight the citation number in the answer text for a moment.
This linkage helps the user understand which part of the answer came from which source, improving transparency.