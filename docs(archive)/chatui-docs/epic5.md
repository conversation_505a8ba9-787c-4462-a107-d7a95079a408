Epic 5: Retrieval-Augmented Answering (RAG) Integration
Epic Description: Power the chat’s Q&A with a Retrieval-Augmented Generation pipeline to ensure answers are grounded in authoritative code references
file-3andax7xbf65ud3icfud5q
. This epic covers the backend process of embedding documents, searching for relevant snippets, constructing prompts for the GPT-4.1-nano model with those snippets, and returning a cited answer. The focus is on leveraging Ordrly’s existing knowledge base (with pgVector in Postgres) and ensuring the AI’s responses are accurate, location-specific, and compliant with content policies (e.g. no full verbatim quotes of copyrighted codes
file-3andax7xbf65ud3icfud5q
).
Feature: Knowledge Base Embeddings
Story CHUI-RAG-1: Document Embedding Pipeline (MVP) – Technical Implementation: Prepare all relevant compliance documents (building codes, ordinances, etc.) by splitting them into sections/chunks and creating vector embeddings for each chunk. Use the pgVector extension in Supabase (already part of Ordrly’s DB
file-3andax7xbf65ud3icfud5q
) to store these embeddings along with text and metadata (jurisdiction, document name, section ref, category). This can be done ahead of time via a script or Supabase Edge Function for ingestion. Ensure that copyrighted code text is handled per policy – e.g., for non-public-domain codes, store only summaries or keywords and not full text
file-3andax7xbf65ud3icfud5q
. Acceptance Criteria:
All relevant code documents for supported jurisdictions are stored in the database with vector embeddings, ready for semantic search.
Each vector entry includes metadata: which jurisdiction it belongs to, which document and section, and possibly a category tag (for use in the source panel grouping).
Proprietary code texts (if any) are not stored verbatim beyond what is legally permissible – the system either omits them or stores a short reference, in line with Ordrly’s compliance approach
file-3andax7xbf65ud3icfud5q
.
Story CHUI-RAG-2: Query Vector Search (MVP) – Technical Implementation: When a user asks a question, perform a semantic similarity search against the embeddings for that chat’s jurisdiction. Use an efficient DB query or Supabase RPC to get the top N relevant snippets (e.g. top 3-5)
file-3andax7xbf65ud3icfud5q
. This involves embedding the user’s question with the same model (OpenAI embedding endpoint or a local model) and running a vector_similarity query in the database. Only retrieve snippets from the specific jurisdiction’s data (and possibly any generic national code references if applicable). Acceptance Criteria:
For a given sample question, the system successfully retrieves a ranked list of relevant text snippets from the knowledge base (with their source info).
The results are filtered to the correct jurisdiction (e.g. asking about “smoke detectors” in Springfield does not return snippets from New York’s code).
Performance is acceptable: the vector search should execute quickly (a few hundred ms ideally) to keep chat response time low.
Story CHUI-RAG-3: Prompt Construction with Sources (MVP) – Technical Implementation: Build the final prompt for the GPT-4.1-nano model using the retrieved snippets. The prompt should include the user’s question and the context snippets in a formatted way, instructing the model to use them for the answer and to cite sources. For example:
sql
Copy
Edit
User question: "Do I need hardwired smoke alarms in a two-story house?"  
Context:  
(1) 2018 IRC R314.3 – Smoke alarms shall be installed in each sleeping room... (City of Springfield)  
(2) Springfield Fire Code Sec. 9 – All new residential units must have hardwired smoke detectors...  
Instructions: Answer the question using **only** the above information, and cite each fact with the source number.
Use Ordrly’s existing prompt templates or utilities if available to ensure consistency. Acceptance Criteria:
The generated prompt sent to OpenAI’s API contains the user’s query and multiple relevant snippets labeled (e.g. with numbers or bullet points) that the model can reference.
The prompt explicitly instructs the model to ground its answer in those snippets and to provide citations like “[1]” or “[2]”.
No irrelevant or unrelated information is included in the prompt – it’s concise and focused to maximize the model’s useful output.
Story CHUI-RAG-4: Incorporate Conversation Context (Nice-to-have) – Technical Implementation: If the user’s question is a follow-up that references prior discussion (“What about the garage?”), include recent conversation context in the retrieval and prompt. One approach is to append the last user question and/or AI answer to the query before embedding, or maintain a running summary of the conversation. Also possibly re-run vector search for new terms that appear. This ensures continuity in multi-turn chats. Acceptance Criteria:
In a scenario where the user asks a follow-up that omits key details mentioned earlier, the system still understands it by having included the prior Q&A context in the model input.
The AI’s answer to the follow-up remains accurate and relevant, indicating it “remembered” what was discussed (without the user having to repeat information).
This context inclusion does not bring unrelated text that could confuse the model – it’s limited to a few recent turns or a summary, and still accompanied by a fresh vector search for any new query components.
Story CHUI-RAG-5: OpenAI Answer Generation (MVP) – Technical Implementation: Call the OpenAI API with the constructed prompt (choose GPT-4.1-nano model) to generate the answer. Use Ordrly’s common OpenAI API wrapper (if existing) which may handle retries, rate limiting, and logging
file-3andax7xbf65ud3icfud5q
file-3andax7xbf65ud3icfud5q
. The model’s parameters should be set for compliance use: e.g. a temperature low enough to keep answers factual. Implement the call within the Next.js API route or an Edge Function, and ensure the response (streamed or full) is captured. Acceptance Criteria:
The system successfully receives a completion from the OpenAI model for the query. For example, asking a known question returns a relevant answer with citations in the text.
The cost and performance are considered – using GPT-4.1-nano stays within budget and latency requirements (document if average response time is a few seconds).
Errors from the model (rate limits, etc.) are handled gracefully (coupled with UI error story), e.g. with retries or fallback to GPT-3.5 if configured as a backup.
Story CHUI-RAG-6: Answer with In-Text Citations (MVP) – Technical Implementation: Ensure the AI’s answer includes citations referencing the provided sources. The prompt and perhaps few-shot examples should guide the model to format citations as [1], [2], etc., corresponding to the snippet indexes given. Verify this by testing a few prompts. On receiving the answer, parse out the cited source identifiers (if needed for the source panel). Acceptance Criteria:
The AI’s answer text consistently contains citation markers that refer to the source documents (e.g. “...must have hardwired detectors [2]”).
Each citation number in the answer matches one of the sources retrieved (no stray numbers). The content of the answer aligns with those sources (no unsupported statements without a reference).
If the model fails to include citations or invents a source, adjust the prompt or implementation until it reliably does so – the final system should have nearly every factual statement backed by a source reference
file-3andax7xbf65ud3icfud5q
.
Feature: Answer Quality & Policy Compliance
Story CHUI-RAG-7: Truthfulness and Scope Control (MVP) – Technical Implementation: The system should restrict answers to only use the provided context and not speculate beyond it
file-3andax7xbf65ud3icfud5q
. Achieve this by prompt instructions (e.g. “If the answer is not in the provided context, say you don’t know or advise consultation”) and by the confidence checks (Epic 6). Also, handle cases where the jurisdiction uses a model code that is copyrighted: the answer should cite the requirement rather than quoting text verbatim (e.g. “According to Section R314 of the 2018 IRC… [reference]” without copying the exact copyrighted text)
file-3andax7xbf65ud3icfud5q
. Acceptance Criteria:
The AI does not hallucinate answers: when asked something outside the knowledge base (or if context is insufficient), it refrains from making up code and instead provides a fallback or disclaimer (see Epic 6 for the exact behavior)
file-3andax7xbf65ud3icfud5q
.
Answers adhere to content policy: no large verbatim quotes from non-public-domain sources. Instead, the answer paraphrases or refers to the code section number with a citation.
In testing, any attempt to get the AI to answer with unsupported info results in either a refusal or a generic safe response, not a confident-sounding lie.