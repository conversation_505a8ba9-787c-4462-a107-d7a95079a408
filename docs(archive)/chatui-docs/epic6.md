Epic 6: Answer Confidence & Fallback Mechanism
Epic Description: Implement safeguards for answer quality by evaluating the confidence of the retrieved knowledge and the AI’s ability to answer. If the system is unsure or lacks authoritative info, it should gracefully decline or provide a cautious response rather than a misleading one
file-3andax7xbf65ud3icfud5q
. This epic covers using similarity scores to gauge confidence, and defining fallback behaviors (like a refusal message or a suggestion to consult a human).
Feature: Confidence Evaluation
Story CHUI-Fallback-1: Similarity Score Threshold (MVP) – Technical Implementation: Utilize the vector search similarity scores to assess how well the knowledge base covers the query. Establish a threshold (configurable) such that if the top result’s score is below this value (meaning the query is likely not answered by any known document), the system flags the query as low-confidence. Implement this check in the API route after retrieving snippets. Optionally, consider the number of snippets above a secondary threshold (if none or only irrelevant ones, confidence is low). Acceptance Criteria:
The system computes a confidence metric for each query (e.g. highest cosine similarity from pgVector, or aggregate of top 3).
A threshold (e.g. 0.8 on cosine similarity, adjustable) is set and documented. In test scenarios, a clearly out-of-scope question (like “Who won the football game last night?”) results in confidence below the threshold.
If confidence is low, this is logged/flagged internally to trigger the fallback response instead of a normal answer.
Story CHUI-Fallback-2: AI Model Uncertainty (Optional) – Technical Implementation: (If using advanced model features) Leverage the AI model to double-check if it should answer. For instance, use OpenAI’s function calling or a secondary prompt where the model evaluates “Do we have sufficient info to answer?” based on the snippets. This is a complement to raw similarity scores. This story is a nice-to-have if fine-tuning the fallback – it can reduce false positives/negatives by analyzing the question semantics. Acceptance Criteria:
(If implemented) The system occasionally passes the question and snippets to a utility function or model prompt asking for answerability. The model returns a boolean or rationale.
The additional check helps in edge cases (for example, high similarity score but irrelevant context could be caught).
The complexity added by this step does not significantly degrade performance; if it does, it can be toggled off.
Feature: Fallback Response Handling
Story CHUI-Fallback-3: Graceful Refusal Message (MVP) – Technical Implementation: Define and generate a standardized fallback response when confidence is below threshold or the AI otherwise should not answer. For example: “I’m sorry, but I’m not sure about that. You may need to consult your local building department for guidance.”
file-3andax7xbf65ud3icfud5q
. This can be a static template or an AI-generated refusal with a specific prompt (but static is safer for consistency). Implement logic in the API route: if low-confidence, skip the normal answer generation and return this fallback message (with no sources, or perhaps a generic source if available like a link to a general resource). Acceptance Criteria:
When the system decides not to answer due to low confidence, the user receives a polite, brief message indicating the inability to help with that query.
The message encourages consulting a human or authority, aligning with compliance tone (no hallucinated answer is given).
This fallback response itself is not counted as an error – it appears as a normal assistant message in the chat (possibly with a special style or icon to denote it’s a guidance message).
Story CHUI-Fallback-4: Fallback Logging & Metrics (Nice-to-have) – Technical Implementation: Whenever a fallback occurs, log this event (in a Supabase table or analytics service) with details: query asked, time, and maybe which part of the pipeline triggered the fallback (no snippets vs. model flagged unsolvable). This helps developers/admin identify gaps in the knowledge base. Optionally surface a metric like “unanswered questions this week” for internal review. Acceptance Criteria:
Each fallback invocation creates a record (e.g. in a unanswered_queries table or logs) with the user’s question and context data.
Admin can review these logs to find frequently asked questions that were not answerable, and take action (like adding new content or adjusting thresholds).
The logging does not expose user identity beyond an ID (for privacy) and can be toggled if needed for compliance, but is by default on for quality improvement.
Story CHUI-Fallback-5: Partial Answer with Caution (Nice-to-have) – Technical Implementation: In cases where some info is available but not complete, the AI could answer with a heavy disclaimer or partial suggestion. For instance, if the question is somewhat covered by sources but not fully, the answer might say “There’s limited information on that; from what I found... [partial info] ... but you should verify with the local authority.” Achieving this may involve setting a lower threshold for partial answer vs. complete refusal. This is complex to tune, so consider it only if straightforward. Acceptance Criteria:
(If implemented) The system differentiates between “no info at all” and “some info but uncertain”. In the latter case, the AI’s answer includes a cautionary note within the answer rather than a full refusal.
Users receiving such an answer can glean some insight but are clearly warned that the information may be incomplete or uncertain.
This behavior is consistent and does not appear randomly – it’s based on defined confidence ranges or rules.
