Epic 7: Data Freshness & Knowledge Base Maintenance
Epic Description: Keep the underlying code and reference documents up-to-date so that answers remain accurate over time. This epic deals with detecting stale data (e.g. if a new code edition is adopted), refreshing the vector embeddings when documents change, and providing mechanisms for both automated and manual updates. It ensures the system’s knowledge is never too out-of-date for critical compliance queries.
Feature: Document Versioning & Staleness Tracking
Story CHUI-Data-1: Track Document Metadata & Versions (MVP) – Technical Implementation: Expand the documents/ordinances table in the database to include metadata like “publish_date”, “version/edition”, or a “last_updated” timestamp for each document. When the system loads context for a chat’s jurisdiction, compare today’s date and the code edition date. If a document is nearing or past a known update cycle, mark it as potentially stale. Acceptance Criteria:
Each knowledge base document entry includes a clear identifier of its version (e.g. “2020 NEC” or “County Ordinances updated Jan 2022”).
If an admin updates a document to a new version, the version field is updated and can trigger re-embedding (in other stories).
The system can identify if a code is outdated (for instance, if the jurisdiction is still on a 2015 code and it’s now 2025, that might be noted for admin to update).
Story CHUI-Data-2: Staleness Warning Indicator (Nice-to-have) – Technical Implementation: If the knowledge base for a jurisdiction is known to be outdated, optionally inform the user or admin. For example, if a user opens a chat for an address where codes changed recently and the system hasn’t updated, show a small icon or message “Data update in progress – recent code changes may not be reflected.” This requires knowing that an update is pending (from admin input or an automated feed). Acceptance Criteria:
In a scenario where codes have changed and the system hasn’t ingested the new version yet, the UI or system has a way to communicate this (could be only on admin dashboard or user-facing if critical).
If user-facing, the message is subtle and factual, not alarming – e.g. “Note: The building code was updated on Jan 2025; this chat currently references the 2018 code edition.”
This indicator is accurate and based on actual data (avoid false warnings or missing warnings due to lack of metadata).
Feature: Automated Knowledge Updates
Story CHUI-Data-3: Scheduled Ordinance Update Check (Nice-to-have) – Technical Implementation: Implement a background job or scheduled Supabase Edge Function to periodically (e.g. monthly) check for new versions of code or ordinances
file-3andax7xbf65ud3icfud5q
. This could be done by scraping known sources or reading an “update calendar” maintained by Ordrly. For simplicity, perhaps maintain a JSON of expected code adoption dates and have the function compare current date. If updates are found, flag those jurisdictions for re-ingestion. Acceptance Criteria:
A scheduled process runs at defined intervals (cron job) without manual intervention. For example, on the first of each month it executes an update-check routine.
The routine identifies at least known changes (for instance, it logs “Austin – new ordinance package available as of June 2025” if such info is present).
The outcome of the check is logged or stored, so that the system knows which documents need refreshing (e.g. inserts a record in a pending_updates table for each out-of-date document).
Story CHUI-Data-4: Automated Re-ingestion of New Data (MVP) – Technical Implementation: When a document update is detected (either via the above check or via admin input), automatically fetch and embed the new content. For example, if a new PDF of the city code is available, an automated process (Edge Function or background worker) downloads the text, splits it, and updates the embeddings in the database. To avoid downtime, consider versioning: keep the old version until the new one is fully processed, then atomically switch which is “active”. Also update the document metadata (version/date). Acceptance Criteria:
The system is capable of ingesting a new or updated document without developer intervention – triggered by a detected update or an admin action, the pipeline from fetching text to storing vectors runs end-to-end.
The new information becomes available to the chat: e.g. after an update, asking about a provision that changed now yields the updated answer (with the new source).
The old data is either archived or removed to avoid confusion, unless needed for historical record. The transition does not cause erroneous answers (e.g. avoid mixing old and new snippets in search results).
Feature: Manual Refresh Controls
Story CHUI-Data-5: Admin Triggered Vector Refresh (MVP) – Technical Implementation: Provide an admin-accessible mechanism to manually trigger a re-embedding of documents. This could be a button in an internal admin dashboard (e.g. “Re-index now”) which calls a secure Next.js API route or directly invokes a Supabase function to reprocess documents. For instance, after uploading a new ordinance file, the admin clicks “Re-index” to generate embeddings and update the vector store. Ensure this action is protected (admin-only) and perhaps requires specifying scope (all data vs. a particular jurisdiction). Acceptance Criteria:
An admin user can initiate a knowledge base refresh via the UI or a command – for example, selecting a jurisdiction and clicking “Re-index code data”.
The system performs the re-index: existing embeddings are updated or replaced with the latest content. Progress or completion can be logged or indicated (e.g. a toast “Re-index complete”).
There is validation to prevent misuse – only authorized admin can trigger, and not too frequently (maybe disable the button if a job is already running).
Story CHUI-Data-6: Partial Update & Merge (Nice-to-have) – Technical Implementation: In cases where only a part of the knowledge base changes (e.g. one chapter of code updated), allow updating just that portion instead of reprocessing everything. This might involve tracking document sections by an ID and only replacing those. The admin UI could list documents or sections with checkboxes to reprocess. Acceptance Criteria:
The admin can choose to refresh a specific document (like “Re-ingest Plumbing Code only”) rather than all data.
The system only updates embeddings for that selection, which is faster and avoids overwriting other data.
The search results remain correct – e.g. a refreshed section doesn’t create duplicate or conflicting entries in the vector index.