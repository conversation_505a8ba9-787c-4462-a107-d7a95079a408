Epic 8: Prebuilt Prompt Templates & Appraisal Mode
Epic Description: Provide a set of pre-defined prompts to guide appraisers or reviewers in asking the right questions. In “Appraisal Mode”, the chat UI can offer a menu of common queries or tasks related to the 1004 appraisal process (e.g. safety checks, code compliance checks for typical home features). This epic covers designing the prompt template menu and the underlying system to manage these templates.
Feature: Prompt Suggestion Menu UI
Story CHUI-Prompt-1: Appraisal Prompt <PERSON>u (MVP) – Technical Implementation: Add a UI element (e.g. a dropdown menu or a list of buttons) in the chat interface labeled “🔖 Appraisal Templates” or similar. When clicked or hovered, it shows a list of suggested questions/prompts relevant to property appraisal compliance. Examples might include: “Check safety code compliance for this property”, “List required smoke and CO alarms for a house”, “Are there any building code issues with a finished basement?”, etc. Implement this list as part of the chat input area or sidebar. Acceptance Criteria:
The UI clearly presents a set of prebuilt question templates accessible in one click.
The templates are worded in a way that is generic but relevant to the address context (they might be filled in or interpreted in context of the current chat’s jurisdiction when used).
The user can open/close the menu easily, and it does not clutter the interface when not in use.
Story CHUI-Prompt-2: Insert Template into Cha<PERSON> (MVP) – Technical Implementation: When the user selects one of the suggested prompts, the system should either: (a) directly send it as a question to the chat, triggering the AI response, or (b) populate the input box with the prompt text for the user to edit or confirm. The choice depends on UX preference – a safe approach is to populate the input field so user can tweak it. Implement the click handler accordingly. Acceptance Criteria:
Clicking a prompt from the menu results in that prompt appearing as the next question (either immediately asked or ready in the input box).
If directly sending, the prompt shows up as a user message in the chat and the AI begins answering it just like a normal question. If populating the input, the user can then hit send.
The inserted prompt can include dynamic placeholders (if any) resolved to the current context. For MVP, likely the prompts are general and don’t need placeholders, but if one said “[ADDRESS]” it should replace with actual address or property type, etc.
Feature: Template Management
Story CHUI-Prompt-3: Template Library Storage (MVP) – Technical Implementation: Store the list of prompt templates in a maintainable way. This could be a JSON file or, better, a database table (e.g. prompt_templates) with fields like id, text, category (like “Appraisal”). Using a DB allows adding/editing templates without code changes. Load these templates on the client or via an API call when the chat UI initializes in Pro mode. Acceptance Criteria:
The set of available prompt templates is not hard-coded into the UI; it’s data-driven. For example, adding a new row in prompt_templates would make a new option appear in the menu (after refresh).
Templates can be categorized if needed (for instance “Appraisal Mode” vs “General”), and the UI could be filtered to appraisal ones for Pro users.
There is no noticeable performance hit when loading the templates (a dozen or so prompt texts should be fine to fetch).
Story CHUI-Prompt-4: Admin Template Editor (Nice-to-have) – Technical Implementation: Provide a simple admin interface to create, edit, or remove prompt templates. This could be as basic as a secured CRUD interface on a Supabase table (since Supabase offers a quick web UI for tables) or a custom form in an admin page. Ensures that non-developers (or the founder) can tweak the suggested questions as they learn what users need. Acceptance Criteria:
An admin user can add a new prompt template (with text and maybe a category/tag) through an interface without deploying new code.
Changes to templates (edits or deletions) are reflected in the user interface for subsequent sessions (or immediately if we fetch fresh each time).
Only admins have access to this editing capability, and it’s protected by auth (accidental or malicious edits by normal users prevented).
Feature: Mode-specific Behavior
Story CHUI-Prompt-5: Appraisal Mode Context (MVP) – Technical Implementation: If “Appraisal Mode” is a distinct mode, ensure that the chat knows the user’s intent is appraisal-related. This could be as simple as all Pro-tier chats implicitly being appraisal-focused (given the target users), or a toggle the user can set. If needed, prepend a system message or use a slightly different prompt for the AI that sets the tone (e.g. “You are assisting with a real estate appraisal compliance check.”). Acceptance Criteria:
The AI’s answers are framed in a way useful for appraisers. For example, it might be slightly more explanatory about implications (this can be achieved via prompt engineering if necessary).
(If a toggle exists) Toggling appraisal mode on/off changes the available templates and possibly the system prompt used. If all Pro chats are inherently appraisal mode, then this is always on.
No matter what, the presence of appraisal-specific templates and possibly context ensures the feature is tailored to appraisal reviewers or appraisers in the field.