Epic 9: 1004-Specific Note Generation & Export
Epic Description: (Nice-to-have Epic) Enable users to easily generate summary notes or report content for the 1004 appraisal form based on the chat discussion. For example, after identifying compliance issues or important findings via Q&A, the user might want a concise narrative to include in their appraisal report. This epic covers using AI to compile such a note and providing ways to export or copy it.
Feature: Appraisal Note Generation
Story CHUI-Note-1: Generate Compliance Summary Note (Nice-to-have) – Technical Implementation: Add an option in the chat UI to “Generate Appraisal Note” – perhaps a button that appears after an answer or at the end of a conversation. When clicked, this triggers an AI prompt that summarizes the key compliance information from the chat (or from the last answer) into a short paragraph suitable for the “observations” or “conditions” section of an appraisal report. The system can send a tailored prompt to GPT-4.1-nano, for example: “Summarize the above findings into a note for a real estate appraisal report.” Use conversation history or the most relevant points (possibly by reusing the snippets found). Acceptance Criteria:
After a user has asked some questions, clicking “Generate Note” produces a coherent paragraph or bulleted list highlighting the compliance issues or confirmations related to the property.
The generated note is factual and drawn from the sources discussed (it should not introduce new info). It might say, for example: “Note: The property’s smoke alarm setup meets 2018 IRC R314 requirements, with hardwired detectors on each floor and in each bedroom [1]. The deck guardrail height of 34 inches is below the required 36 inches per local building code [2], which may be a safety concern.”
The note is concise (maybe 2-5 sentences or a short list) and uses a formal tone appropriate for an appraisal report.
Story CHUI-Note-2: Include Citations in Note (Nice-to-have) – Technical Implementation: Decide whether the generated note should include citations or not. Appraisal notes might not usually include code citations, so perhaps the AI should output a clean narrative without bracketed references, or with minimal references. If we want citations for internal verification, we could include them lightly or as footnotes. Implement prompt instructions accordingly (e.g. “provide a concise note without inline citations”). Acceptance Criteria:
The output note does not overwhelm the text with citations, unless desired. Ideally, it reads like a normal note. (We may allow one general reference if needed, but test with and without.)
The content of the note can be traced to the sources if needed (perhaps internally we keep track, but the user sees a clean version).
The user finds the note suitable to copy-paste into their 1004 form narrative without much editing.
Feature: Note Export & Sharing
Story CHUI-Note-3: Copy or Download Note (Nice-to-have) – Technical Implementation: Provide a convenient way to export the generated note. This could be as simple as a “Copy to Clipboard” button that copies the text, or a “Download as .docx” for a more polished output. Use browser APIs for copying text, and a library (or server function) to generate a Word/PDF if going that route. Acceptance Criteria:
The user can easily take the generated note out of the app: e.g. clicking “Copy” flashes a confirmation that text is copied, and indeed they can paste it into another document.
(If download offered) Clicking download provides a file (e.g. ComplianceNote.txt or .docx) containing the note. The file is properly formatted (UTF-8 text, simple formatting if docx).
The export does not include any hidden or extraneous content (no extra code metadata), just the note (and perhaps a header if needed).
Story CHUI-Note-4: Attach Note to Chat Record (Optional) – Technical Implementation: Save the generated note in the database, linked to the chat (maybe in a notes table or as a special message type). This way the user can retrieve previously generated notes or the system can display it later. Acceptance Criteria:
When a note is generated, it’s saved so that if the user comes back to the chat, they can see/regenerate it or have a history of notes for that address.
Only one note per chat might be stored (or multiple versions if generated multiple times – could keep the latest).
This stored note can be included if the user exports the entire chat transcript (see Audit Trail epic).