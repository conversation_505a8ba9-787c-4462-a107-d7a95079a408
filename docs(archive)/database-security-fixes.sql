-- Database Security Fixes for Ordrly
-- Addresses Supabase linter security issues
-- Run these commands in Supabase SQL Editor

-- =============================================================================
-- 1. FIX SECURITY DEFINER VIEWS
-- =============================================================================

-- Drop and recreate views without SECURITY DEFINER
DROP VIEW IF EXISTS public.compliance_knowledge_stats;
DROP VIEW IF EXISTS public.recent_automation_errors;

-- Recreate compliance_knowledge_stats without SECURITY DEFINER
CREATE VIEW public.compliance_knowledge_stats AS
SELECT 
  COUNT(*) as total_entries,
  COUNT(DISTINCT jurisdiction_id) as unique_jurisdictions,
  AVG(confidence_score) as avg_confidence,
  MAX(updated_at) as last_updated
FROM public.compliance_knowledge;

-- Recreate recent_automation_errors without SECURITY DEFINER
CREATE VIEW public.recent_automation_errors AS
SELECT 
  id,
  job_type,
  error_message,
  created_at,
  metadata
FROM public.automation_logs 
WHERE status = 'error' 
  AND created_at > NOW() - INTERVAL '7 days'
ORDER BY created_at DESC
LIMIT 100;

-- =============================================================================
-- 2. ENABLE RLS ON CRITICAL USER-FACING TABLES
-- =============================================================================

-- Enable RLS on tables that contain user data
ALTER TABLE public.ordinance_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.compliance_knowledge ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.newsletter_subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_alerts ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- 3. CREATE RLS POLICIES FOR USER-FACING TABLES
-- =============================================================================

-- Ordinance Cache - Allow read access to all authenticated users
CREATE POLICY "Allow read access to ordinance cache" ON public.ordinance_cache
  FOR SELECT TO authenticated
  USING (true);

-- Compliance Knowledge - Allow read access to all authenticated users
CREATE POLICY "Allow read access to compliance knowledge" ON public.compliance_knowledge
  FOR SELECT TO authenticated
  USING (true);

-- Newsletter Subscribers - Users can only see their own subscription
CREATE POLICY "Users can view own newsletter subscription" ON public.newsletter_subscribers
  FOR SELECT TO authenticated
  USING (email = auth.jwt() ->> 'email');

CREATE POLICY "Users can insert own newsletter subscription" ON public.newsletter_subscribers
  FOR INSERT TO authenticated
  WITH CHECK (email = auth.jwt() ->> 'email');

CREATE POLICY "Users can update own newsletter subscription" ON public.newsletter_subscribers
  FOR UPDATE TO authenticated
  USING (email = auth.jwt() ->> 'email')
  WITH CHECK (email = auth.jwt() ->> 'email');

-- Leads - Only admin access (no user access)
CREATE POLICY "Admin only access to leads" ON public.leads
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

-- Usage Alerts - Users can only see their own alerts
CREATE POLICY "Users can view own usage alerts" ON public.usage_alerts
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

-- =============================================================================
-- 4. ENABLE RLS ON ADMIN/SYSTEM TABLES (ADMIN ACCESS ONLY)
-- =============================================================================

-- Enable RLS on admin tables
ALTER TABLE public.automation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.automation_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.migration_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.behavior_email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_settings ENABLE ROW LEVEL SECURITY;

-- Create admin-only policies
CREATE POLICY "Admin only access to automation_logs" ON public.automation_logs
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to automation_jobs" ON public.automation_jobs
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to migration_history" ON public.migration_history
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to email_templates" ON public.email_templates
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to email_events" ON public.email_events
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to behavior_email_templates" ON public.behavior_email_templates
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to marketing_settings" ON public.marketing_settings
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

-- =============================================================================
-- 5. CONTENT MANAGEMENT TABLES (ADMIN ACCESS ONLY)
-- =============================================================================

-- Enable RLS on content tables
ALTER TABLE public.drip_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.drip_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.syndication_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.syndication_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.syndication_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_syndication_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.optimal_content_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_content_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_configurations ENABLE ROW LEVEL SECURITY;

-- Create admin-only policies for content tables
CREATE POLICY "Admin only access to drip_campaigns" ON public.drip_campaigns
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

-- User events - users can see their own events
CREATE POLICY "Users can view own events" ON public.user_events
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Admin can view all events" ON public.user_events
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

-- Drip subscriptions - users can manage their own subscriptions
CREATE POLICY "Users can manage own drip subscriptions" ON public.drip_subscriptions
  FOR ALL TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- All other content tables - admin only
CREATE POLICY "Admin only access to syndication_schedule" ON public.syndication_schedule
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to syndication_metrics" ON public.syndication_metrics
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to syndication_variants" ON public.syndication_variants
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to content_syndication_queue" ON public.content_syndication_queue
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to optimal_content_schedule" ON public.optimal_content_schedule
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to master_content_schedule" ON public.master_content_schedule
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to content_approvals" ON public.content_approvals
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

CREATE POLICY "Admin only access to workflow_configurations" ON public.workflow_configurations
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

-- =============================================================================
-- 6. HANDLE SYSTEM TABLES
-- =============================================================================

-- For PostGIS system table, we can either:
-- Option 1: Enable RLS with admin-only access
ALTER TABLE public.spatial_ref_sys ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Admin only access to spatial_ref_sys" ON public.spatial_ref_sys
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

-- Regions table - allow read access to authenticated users (geographic data)
ALTER TABLE public.regions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow read access to regions" ON public.regions
  FOR SELECT TO authenticated
  USING (true);

CREATE POLICY "Admin only write access to regions" ON public.regions
  FOR INSERT, UPDATE, DELETE TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

-- =============================================================================
-- 7. VERIFICATION QUERIES
-- =============================================================================

-- Run these to verify the fixes worked:
-- SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public';
-- SELECT * FROM pg_policies WHERE schemaname = 'public';
-- SELECT viewname, definition FROM pg_views WHERE schemaname = 'public' AND viewname IN ('compliance_knowledge_stats', 'recent_automation_errors');
