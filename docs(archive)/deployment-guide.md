# Production Deployment Guide

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Integration**: Connect your GitHub account to Vercel
3. **Domain Setup**: Configure your domains (ordrly.ai and api.ordrly.ai)
4. **Stripe Live Keys**: Set up production Stripe account and get live keys

## Step 1: Deploy Main Ordrly App (ordrly.ai)

### 1.1 Create Vercel Project
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import `Chreezoh/ordrly` repository
4. Configure project:
   - **Framework Preset**: Next.js
   - **Root Directory**: `./` (leave default)
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
   - **Install Command**: `npm install`

### 1.2 Configure Environment Variables
1. In Vercel project settings, go to "Environment Variables"
2. Add all variables from `docs/production-env-template.md` (Main App section)
3. **Important**: Update these with production values:
   - `STRIPE_SECRET_KEY`: Your live Stripe secret key
   - `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`: Your live Stripe publishable key
   - `STRIPE_WEBHOOK_SECRET`: Your production webhook secret
   - All Stripe Price IDs: Your live price IDs

### 1.3 Configure Domain
1. In Vercel project settings, go to "Domains"
2. Add `ordrly.ai` and `www.ordrly.ai`
3. Configure DNS records as instructed by Vercel
4. Set `www.ordrly.ai` as primary domain (for SEO)

## Step 2: Deploy Municipal Research API (api.ordrly.ai)

### 2.1 Create Vercel Project
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import `Chreezoh/municipal-research-api` repository
4. Configure project:
   - **Framework Preset**: Other
   - **Root Directory**: `./` (leave default)
   - **Build Command**: `npm run build` (if you have one, otherwise leave empty)
   - **Output Directory**: Leave empty
   - **Install Command**: `npm install`

### 2.2 Configure Environment Variables
1. In Vercel project settings, go to "Environment Variables"
2. Add all variables from `docs/production-env-template.md` (Municipal API section)
3. **Important**: Update Stripe keys with production values

### 2.3 Configure Domain
1. In Vercel project settings, go to "Domains"
2. Add `api.ordrly.ai`
3. Configure DNS records as instructed by Vercel

## Step 3: Update Stripe Configuration

### 3.1 Create Live Products
1. Go to [Stripe Dashboard](https://dashboard.stripe.com) (live mode)
2. Create products and prices:
   - **Starter Monthly**: $49/month
   - **Starter Annual**: $490/year (save $98)
   - **Professional Monthly**: $99/month
   - **Professional Annual**: $990/year (save $198)
3. Copy the live price IDs and update environment variables

### 3.2 Configure Webhooks
1. In Stripe Dashboard, go to "Webhooks"
2. Create endpoint: `https://ordrly.ai/api/webhooks/stripe`
3. Select events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
4. Copy webhook secret and update `STRIPE_WEBHOOK_SECRET`

## Step 4: DNS Configuration

Configure your DNS provider with these records:

```
# Main domain
ordrly.ai        A     *********** (Vercel IP)
www.ordrly.ai    CNAME cname.vercel-dns.com

# API subdomain
api.ordrly.ai    CNAME cname.vercel-dns.com
```

**Note**: Use the actual IPs/CNAMEs provided by Vercel in your project settings.

## Step 5: Test Production Deployment

### 5.1 Main App Tests
1. Visit `https://ordrly.ai`
2. Test user registration/login
3. Test chat functionality
4. Test billing/subscription flow
5. Verify all pages load correctly

### 5.2 API Tests
1. Test API endpoint: `https://api.ordrly.ai/health`
2. Test municipal research functionality
3. Verify CORS configuration works with main app

### 5.3 Integration Tests
1. Test chat integration with municipal API
2. Test Stripe webhook processing
3. Test email notifications
4. Verify usage tracking and limits

## Step 6: Monitoring and Maintenance

### 6.1 Set Up Monitoring
1. **Vercel Analytics**: Enable in project settings
2. **Stripe Dashboard**: Monitor payments and subscriptions
3. **Supabase Dashboard**: Monitor database performance
4. **Error Tracking**: Consider adding Sentry or similar

### 6.2 Regular Maintenance
1. Monitor API usage and costs
2. Review Stripe transaction logs
3. Check Supabase database health
4. Update dependencies regularly

## Troubleshooting

### Common Issues
1. **Environment Variables**: Ensure all required variables are set
2. **CORS Errors**: Check municipal API CORS configuration
3. **Stripe Webhooks**: Verify webhook endpoint and secret
4. **Domain Issues**: Check DNS propagation (can take 24-48 hours)

### Support Resources
- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [Stripe Integration Guide](https://stripe.com/docs/webhooks)
- [Supabase Production Guide](https://supabase.com/docs/guides/platform/going-into-prod)

## Security Checklist

- [ ] All environment variables use production values
- [ ] Stripe webhook endpoints use HTTPS
- [ ] Database RLS policies are enabled
- [ ] API rate limiting is configured
- [ ] CORS is properly configured
- [ ] Security headers are set (via vercel.json)
- [ ] No debug endpoints are accessible in production
