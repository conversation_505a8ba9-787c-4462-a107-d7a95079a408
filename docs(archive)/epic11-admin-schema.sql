-- Epic 11: Admin Tools for Chat & Data Management - Database Schema
-- Created: 2025-01-19
-- Purpose: Create tables and policies for admin chat management tools

-- =============================================================================
-- 1. PROMPT TEMPLATES TABLE
-- =============================================================================

-- Create prompt_templates table for managing chat prompt templates
CREATE TABLE IF NOT EXISTS prompt_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  template_text text NOT NULL,
  category text NOT NULL DEFAULT 'general',
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Constraints
  CONSTRAINT prompt_templates_name_check CHECK (length(name) > 0),
  CONSTRAINT prompt_templates_template_text_check CHECK (length(template_text) > 0),
  CONSTRAINT prompt_templates_category_check CHECK (category IN ('general', 'appraisal', 'safety', 'building', 'zoning', 'fire', 'electrical', 'plumbing'))
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_prompt_templates_category ON prompt_templates(category);
CREATE INDEX IF NOT EXISTS idx_prompt_templates_active ON prompt_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_prompt_templates_created_by ON prompt_templates(created_by);

-- =============================================================================
-- 2. ADMIN ACTIONS AUDIT LOG TABLE
-- =============================================================================

-- Create admin_actions table for audit logging
CREATE TABLE IF NOT EXISTS admin_actions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_user_id uuid NOT NULL REFERENCES auth.users(id),
  action_type text NOT NULL,
  target_id text,
  target_type text,
  metadata jsonb DEFAULT '{}',
  ip_address inet,
  user_agent text,
  created_at timestamptz DEFAULT now(),
  
  -- Constraints
  CONSTRAINT admin_actions_action_type_check CHECK (
    action_type IN (
      'chat_lookup', 'chat_view', 'reindex_triggered', 'reindex_completed',
      'document_uploaded', 'document_deleted', 'template_created', 
      'template_updated', 'template_deleted', 'analytics_viewed'
    )
  )
);

-- Create indexes for performance and querying
CREATE INDEX IF NOT EXISTS idx_admin_actions_admin_user_id ON admin_actions(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_action_type ON admin_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_actions_created_at ON admin_actions(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_actions_target_id ON admin_actions(target_id);

-- =============================================================================
-- 3. ROW LEVEL SECURITY POLICIES
-- =============================================================================

-- Enable RLS on new tables
ALTER TABLE prompt_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_actions ENABLE ROW LEVEL SECURITY;

-- Admin-only access policy for prompt_templates
CREATE POLICY "Admin only access to prompt_templates" ON prompt_templates
  FOR ALL TO authenticated
  USING (public.is_admin_user());

-- Admin-only access policy for admin_actions
CREATE POLICY "Admin only access to admin_actions" ON admin_actions
  FOR ALL TO authenticated
  USING (public.is_admin_user());

-- =============================================================================
-- 4. HELPER FUNCTIONS FOR ADMIN OPERATIONS
-- =============================================================================

-- Function to log admin actions
CREATE OR REPLACE FUNCTION public.log_admin_action(
  p_action_type text,
  p_target_id text DEFAULT NULL,
  p_target_type text DEFAULT NULL,
  p_metadata jsonb DEFAULT '{}'
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  action_id uuid;
BEGIN
  -- Only allow if user is admin
  IF NOT public.is_admin_user() THEN
    RAISE EXCEPTION 'Admin access required';
  END IF;
  
  INSERT INTO admin_actions (
    admin_user_id,
    action_type,
    target_id,
    target_type,
    metadata
  ) VALUES (
    auth.uid(),
    p_action_type,
    p_target_id,
    p_target_type,
    p_metadata
  ) RETURNING id INTO action_id;
  
  RETURN action_id;
END;
$$;

-- Function to get chat session summary for admin
CREATE OR REPLACE FUNCTION public.get_chat_session_summary(p_conversation_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result jsonb;
BEGIN
  -- Only allow if user is admin
  IF NOT public.is_admin_user() THEN
    RAISE EXCEPTION 'Admin access required';
  END IF;
  
  SELECT jsonb_build_object(
    'conversation_id', c.id,
    'user_email', p.email,
    'address', c.address,
    'jurisdiction_name', c.jurisdiction_name,
    'rule_type', c.rule_type,
    'created_at', c.created_at,
    'updated_at', c.updated_at,
    'message_count', (
      SELECT COUNT(*) FROM chat_messages 
      WHERE conversation_id = c.id
    ),
    'last_message_at', (
      SELECT MAX(created_at) FROM chat_messages 
      WHERE conversation_id = c.id
    )
  ) INTO result
  FROM chat_conversations c
  JOIN profiles p ON p.id = c.user_id
  WHERE c.id = p_conversation_id;
  
  RETURN result;
END;
$$;

-- =============================================================================
-- 5. VIEWS FOR ADMIN ANALYTICS
-- =============================================================================

-- View for chat usage analytics
CREATE OR REPLACE VIEW admin_chat_analytics AS
SELECT 
  DATE_TRUNC('day', created_at) as date,
  COUNT(*) as conversations_created,
  COUNT(DISTINCT user_id) as unique_users,
  AVG((
    SELECT COUNT(*) FROM chat_messages 
    WHERE conversation_id = chat_conversations.id
  )) as avg_messages_per_conversation
FROM chat_conversations
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;

-- View for document status
CREATE OR REPLACE VIEW admin_document_status AS
SELECT 
  jurisdiction,
  document_type,
  COUNT(*) as total_chunks,
  COUNT(DISTINCT source_document_id) as unique_documents,
  MAX(last_updated_at) as last_updated,
  MIN(last_updated_at) as oldest_update,
  AVG(
    CASE 
      WHEN last_updated_at > NOW() - INTERVAL '30 days' THEN 1 
      ELSE 0 
    END
  ) as freshness_score
FROM compliance_knowledge
GROUP BY jurisdiction, document_type
ORDER BY jurisdiction, document_type;

-- =============================================================================
-- 6. GRANT PERMISSIONS
-- =============================================================================

-- Grant necessary permissions to authenticated users (admin check in RLS)
GRANT ALL ON prompt_templates TO authenticated;
GRANT ALL ON admin_actions TO authenticated;
GRANT SELECT ON admin_chat_analytics TO authenticated;
GRANT SELECT ON admin_document_status TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.log_admin_action TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_chat_session_summary TO authenticated;

-- =============================================================================
-- 7. INITIAL DATA
-- =============================================================================

-- Insert default prompt templates
INSERT INTO prompt_templates (name, description, template_text, category, created_by) VALUES
(
  'General Property Compliance Check',
  'Basic compliance check for any property type',
  'Please provide a comprehensive compliance overview for this property, including any building code requirements, safety regulations, and permit considerations.',
  'general',
  (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
),
(
  'Appraisal Safety Requirements',
  'Safety-focused questions for appraisal purposes',
  'What are the key safety code requirements for this property that would affect its appraisal value? Include smoke detectors, carbon monoxide alarms, electrical safety, and structural requirements.',
  'appraisal',
  (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
),
(
  'Building Code Compliance',
  'Comprehensive building code check',
  'Please review all applicable building codes for this property, including structural requirements, accessibility compliance, and any recent code updates that may affect this property.',
  'building',
  (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
),
(
  'Fire Safety Requirements',
  'Fire safety and prevention requirements',
  'What are the fire safety requirements for this property? Include fire alarm systems, sprinkler requirements, egress requirements, and fire-resistant materials.',
  'fire',
  (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
)
ON CONFLICT DO NOTHING;

-- Log the schema creation
SELECT public.log_admin_action(
  'schema_created',
  'epic11_admin_schema',
  'database_schema',
  '{"tables": ["prompt_templates", "admin_actions"], "views": ["admin_chat_analytics", "admin_document_status"], "functions": ["log_admin_action", "get_chat_session_summary"]}'::jsonb
);

-- =============================================================================
-- SCHEMA CREATION COMPLETE
-- =============================================================================
