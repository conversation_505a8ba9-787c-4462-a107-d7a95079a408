-- Epic 7: Data Freshness & Knowledge Base Maintenance
-- Database Schema Updates
-- Created: 2025-01-19

-- =====================================================
-- PHASE 1: Database Foundation & Version Tracking
-- =====================================================

-- 1. Extend compliance_knowledge table with version tracking
-- Add version tracking columns to existing compliance_knowledge table
ALTER TABLE compliance_knowledge 
ADD COLUMN IF NOT EXISTS document_version TEXT,
ADD COLUMN IF NOT EXISTS publish_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS adoption_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS superseded_by UUID REFERENCES compliance_knowledge(id);

-- Add comments for documentation
COMMENT ON COLUMN compliance_knowledge.document_version IS 'Version identifier (e.g., "2020 NEC", "2018 IBC")';
COMMENT ON COLUMN compliance_knowledge.publish_date IS 'Official publication date of the document';
COMMENT ON COLUMN compliance_knowledge.adoption_date IS 'Date when jurisdiction adopted this version';
COMMENT ON COLUMN compliance_knowledge.is_active IS 'Whether this version is currently active for searches';
COMMENT ON COLUMN compliance_knowledge.superseded_by IS 'Reference to newer version if this one is superseded';

-- 2. Create document staleness tracking table
CREATE TABLE IF NOT EXISTS document_staleness_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  jurisdiction TEXT NOT NULL,
  document_type TEXT NOT NULL,
  current_version TEXT,
  latest_available_version TEXT,
  staleness_score NUMERIC DEFAULT 0 CHECK (staleness_score >= 0 AND staleness_score <= 1),
  last_checked_at TIMESTAMPTZ DEFAULT now(),
  update_available BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Ensure unique tracking per jurisdiction/document_type combination
  UNIQUE(jurisdiction, document_type)
);

-- Add comments
COMMENT ON TABLE document_staleness_tracking IS 'Tracks staleness of documents by jurisdiction and type';
COMMENT ON COLUMN document_staleness_tracking.staleness_score IS 'Calculated staleness metric (0=fresh, 1=very stale)';
COMMENT ON COLUMN document_staleness_tracking.metadata IS 'Additional tracking data (thresholds, sources, etc.)';

-- 3. Create pending updates table
CREATE TABLE IF NOT EXISTS pending_updates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  jurisdiction TEXT NOT NULL,
  document_type TEXT NOT NULL,
  update_type TEXT NOT NULL CHECK (update_type IN ('version_update', 'new_document', 'amendment', 'correction')),
  source_url TEXT,
  detected_at TIMESTAMPTZ DEFAULT now(),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
  metadata JSONB DEFAULT '{}',
  error_message TEXT,
  processed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add comments
COMMENT ON TABLE pending_updates IS 'Queue of detected document updates awaiting processing';
COMMENT ON COLUMN pending_updates.priority IS 'Update priority (1=highest, 10=lowest)';
COMMENT ON COLUMN pending_updates.metadata IS 'Update details (version info, change summary, etc.)';

-- 4. Create knowledge refresh jobs table
CREATE TABLE IF NOT EXISTS knowledge_refresh_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  triggered_by UUID REFERENCES auth.users(id),
  job_type TEXT NOT NULL CHECK (job_type IN ('manual', 'scheduled', 'automated', 'emergency')),
  scope JSONB DEFAULT '{}', -- jurisdiction, document_type, specific_ids filters
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
  progress JSONB DEFAULT '{}', -- current step, percentage, processed items
  total_items INTEGER DEFAULT 0,
  processed_items INTEGER DEFAULT 0,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  result_summary JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add comments
COMMENT ON TABLE knowledge_refresh_jobs IS 'Tracks knowledge base refresh operations';
COMMENT ON COLUMN knowledge_refresh_jobs.scope IS 'Defines what to refresh (jurisdiction, document_type filters)';
COMMENT ON COLUMN knowledge_refresh_jobs.progress IS 'Real-time progress tracking data';
COMMENT ON COLUMN knowledge_refresh_jobs.result_summary IS 'Summary of refresh results (items updated, errors, etc.)';

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for compliance_knowledge version queries
CREATE INDEX IF NOT EXISTS idx_compliance_knowledge_version 
ON compliance_knowledge(jurisdiction, document_type, is_active);

CREATE INDEX IF NOT EXISTS idx_compliance_knowledge_dates 
ON compliance_knowledge(publish_date, adoption_date) WHERE is_active = true;

-- Indexes for staleness tracking
CREATE INDEX IF NOT EXISTS idx_staleness_tracking_jurisdiction 
ON document_staleness_tracking(jurisdiction, document_type);

CREATE INDEX IF NOT EXISTS idx_staleness_tracking_score 
ON document_staleness_tracking(staleness_score DESC) WHERE update_available = true;

CREATE INDEX IF NOT EXISTS idx_staleness_tracking_checked 
ON document_staleness_tracking(last_checked_at);

-- Indexes for pending updates
CREATE INDEX IF NOT EXISTS idx_pending_updates_status 
ON pending_updates(status, priority, detected_at);

CREATE INDEX IF NOT EXISTS idx_pending_updates_jurisdiction 
ON pending_updates(jurisdiction, document_type, status);

-- Indexes for refresh jobs
CREATE INDEX IF NOT EXISTS idx_refresh_jobs_status 
ON knowledge_refresh_jobs(status, created_at);

CREATE INDEX IF NOT EXISTS idx_refresh_jobs_user 
ON knowledge_refresh_jobs(triggered_by, created_at);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE document_staleness_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE pending_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_refresh_jobs ENABLE ROW LEVEL SECURITY;

-- Admin-only access policies for staleness tracking
CREATE POLICY "Admin can view staleness tracking" ON document_staleness_tracking
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin can manage staleness tracking" ON document_staleness_tracking
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Admin-only access policies for pending updates
CREATE POLICY "Admin can view pending updates" ON pending_updates
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin can manage pending updates" ON pending_updates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Refresh jobs: Admin can see all, users can see their own
CREATE POLICY "Users can view own refresh jobs" ON knowledge_refresh_jobs
  FOR SELECT USING (
    triggered_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin can manage all refresh jobs" ON knowledge_refresh_jobs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Users can create refresh jobs" ON knowledge_refresh_jobs
  FOR INSERT WITH CHECK (
    triggered_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to calculate staleness score
CREATE OR REPLACE FUNCTION calculate_staleness_score(
  publish_date TIMESTAMPTZ,
  adoption_date TIMESTAMPTZ,
  document_type TEXT DEFAULT 'general'
) RETURNS NUMERIC AS $$
DECLARE
  base_date TIMESTAMPTZ;
  age_months NUMERIC;
  threshold_months NUMERIC;
  staleness_score NUMERIC;
BEGIN
  -- Use adoption date if available, otherwise publish date
  base_date := COALESCE(adoption_date, publish_date);
  
  -- If no date available, return maximum staleness
  IF base_date IS NULL THEN
    RETURN 1.0;
  END IF;
  
  -- Calculate age in months
  age_months := EXTRACT(EPOCH FROM (now() - base_date)) / (30.44 * 24 * 3600);
  
  -- Set threshold based on document type
  threshold_months := CASE document_type
    WHEN 'building_code' THEN 36  -- 3 years
    WHEN 'fire_code' THEN 36     -- 3 years
    WHEN 'electrical_code' THEN 36 -- 3 years
    WHEN 'plumbing_code' THEN 36  -- 3 years
    WHEN 'ordinance' THEN 24      -- 2 years
    WHEN 'zoning' THEN 60         -- 5 years
    ELSE 36                       -- Default 3 years
  END;
  
  -- Calculate staleness score (0 = fresh, 1 = very stale)
  staleness_score := LEAST(age_months / threshold_months, 1.0);
  
  RETURN ROUND(staleness_score, 3);
END;
$$ LANGUAGE plpgsql;

-- Function to update staleness tracking
CREATE OR REPLACE FUNCTION update_staleness_tracking() RETURNS void AS $$
BEGIN
  INSERT INTO document_staleness_tracking (
    jurisdiction,
    document_type,
    current_version,
    staleness_score,
    last_checked_at
  )
  SELECT DISTINCT
    jurisdiction,
    document_type,
    document_version,
    calculate_staleness_score(publish_date, adoption_date, document_type),
    now()
  FROM compliance_knowledge
  WHERE is_active = true
  ON CONFLICT (jurisdiction, document_type) 
  DO UPDATE SET
    current_version = EXCLUDED.current_version,
    staleness_score = EXCLUDED.staleness_score,
    last_checked_at = EXCLUDED.last_checked_at,
    updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL DATA POPULATION
-- =====================================================

-- Backfill existing compliance_knowledge records with default values
UPDATE compliance_knowledge 
SET 
  document_version = COALESCE(
    metadata->>'version',
    metadata->>'edition',
    'Unknown'
  ),
  publish_date = COALESCE(
    (metadata->>'publish_date')::TIMESTAMPTZ,
    created_at
  ),
  adoption_date = COALESCE(
    (metadata->>'adoption_date')::TIMESTAMPTZ,
    created_at
  ),
  is_active = true
WHERE document_version IS NULL;

-- Initial staleness tracking population
SELECT update_staleness_tracking();

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to update staleness when compliance_knowledge changes
CREATE OR REPLACE FUNCTION trigger_staleness_update() RETURNS TRIGGER AS $$
BEGIN
  -- Update staleness tracking for the affected jurisdiction/document_type
  INSERT INTO document_staleness_tracking (
    jurisdiction,
    document_type,
    current_version,
    staleness_score,
    last_checked_at
  )
  VALUES (
    NEW.jurisdiction,
    NEW.document_type,
    NEW.document_version,
    calculate_staleness_score(NEW.publish_date, NEW.adoption_date, NEW.document_type),
    now()
  )
  ON CONFLICT (jurisdiction, document_type) 
  DO UPDATE SET
    current_version = EXCLUDED.current_version,
    staleness_score = EXCLUDED.staleness_score,
    last_checked_at = EXCLUDED.last_checked_at,
    updated_at = now();
    
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS compliance_knowledge_staleness_update ON compliance_knowledge;
CREATE TRIGGER compliance_knowledge_staleness_update
  AFTER INSERT OR UPDATE ON compliance_knowledge
  FOR EACH ROW
  EXECUTE FUNCTION trigger_staleness_update();

-- Trigger to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update triggers to all new tables
CREATE TRIGGER update_staleness_tracking_updated_at
  BEFORE UPDATE ON document_staleness_tracking
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pending_updates_updated_at
  BEFORE UPDATE ON pending_updates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_refresh_jobs_updated_at
  BEFORE UPDATE ON knowledge_refresh_jobs
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify schema updates
DO $$
BEGIN
  RAISE NOTICE 'Epic 7 Schema Updates Applied Successfully';
  RAISE NOTICE 'Tables created: document_staleness_tracking, pending_updates, knowledge_refresh_jobs';
  RAISE NOTICE 'Columns added to compliance_knowledge: document_version, publish_date, adoption_date, is_active, superseded_by';
  RAISE NOTICE 'Indexes created: % total', (
    SELECT COUNT(*) FROM pg_indexes 
    WHERE indexname LIKE 'idx_%staleness%' 
    OR indexname LIKE 'idx_%pending%' 
    OR indexname LIKE 'idx_%refresh%'
    OR indexname LIKE 'idx_compliance_knowledge_%'
  );
END $$;
