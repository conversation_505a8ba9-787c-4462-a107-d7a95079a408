# Product Requirements Document (PRD)
# Ordrly – Current Platform & Future Development

**Owner:** <PERSON>  
**Last Updated:** [Date]  
**Status:** Live Platform - Enhancement Phase

## 1. Product Overview

Ordrly is a SaaS platform that enables homeowners, contractors, and real estate professionals to instantly retrieve, analyze, and understand property compliance requirements for any U.S. address. Using the tagline "Know Before You Build," the platform leverages AI to streamline permit research, identify potential compliance issues, and provide actionable guidance for construction projects.

## 2. Objectives & Success Criteria

### Current Objectives
- **Operational Excellence:** Maintain 99%+ uptime for the live platform at ordrly.ai
- **User Growth:** Scale from current user base to 1,000+ registered users
- **Revenue Growth:** Achieve $5,000 MRR through Pro subscription conversions
- **Coverage Expansion:** Increase regulatory coverage to 100+ major metropolitan areas
- **User Experience:** Maintain high user satisfaction and engagement metrics

### Success Criteria
- ✅ **MVP Launched:** Platform operational at ordrly.ai with core features
- ✅ **User Authentication:** Google OAuth and email/password registration implemented
- ✅ **Payment Processing:** Stripe integration with Pro tier subscriptions
- 🎯 **User Acquisition:** 100 Pro subscribers within 6 months
- 🎯 **Platform Performance:** <5 second average response time for searches
- 🎯 **User Engagement:** 70%+ of users perform multiple searches

## 3. User Stories & Use Cases

### Primary User Stories (Current)
- **User Story 1:** As a homeowner planning a renovation, I want to enter my address and project description to understand what permits and regulations apply, so I can plan my project properly and avoid violations.

- **User Story 2:** As a contractor, I want to quickly check compliance requirements for a client's property before providing a quote, so I can include accurate permit costs and timeline estimates.

- **User Story 3:** As a DIY enthusiast, I want to ask specific questions about building codes and get AI-powered answers, so I can understand what's allowed on my property without hiring a consultant.

- **User Story 4:** As a real estate investor, I want to identify potential red flags and restrictions before purchasing a property, so I can make informed investment decisions.

### Secondary User Stories (Future)
- **User Story 5:** As a real estate agent, I want to provide compliance information to clients during property showings, so I can add value and differentiate my services.

- **User Story 6:** As a property manager, I want to understand ongoing compliance requirements for multiple properties, so I can maintain regulatory compliance across my portfolio.

## 4. Key Features & Requirements

### 4.1. Current Features (Live Platform)
- ✅ **Address Search:** Intelligent address lookup with autocomplete and validation
- ✅ **Project Description:** Free-text input for specific project types and details
- ✅ **AI-Powered Analysis:** Compliance analysis with project-specific recommendations
- ✅ **Red Flag Detection:** Automated identification of potential compliance issues
- ✅ **Interactive AI Chat:** Embedded assistant for follow-up questions and clarification
- ✅ **User Authentication:** Google OAuth and email/password registration
- ✅ **Subscription Management:** Stripe-powered Pro tier with unlimited searches
- ✅ **Responsive Design:** Mobile-optimized interface for on-site use
- ✅ **Search History:** Optional saving and management of previous searches
- ✅ **Privacy Controls:** User-configurable data retention and privacy settings

### 4.2. Technical Implementation (Current)
- **Frontend:** Next.js 14, React 18, shadcn/ui, Tailwind CSS
- **Backend:** Supabase (Postgres, pgvector, Edge Functions, Auth)
- **AI Integration:** OpenAI GPT-4.1-nano for analysis and chat functionality
- **Data Sources:** Municipal websites, public ordinance databases, GIS APIs
- **Infrastructure:** Vercel hosting with custom domain (ordrly.ai)
- **Payment Processing:** Stripe with subscription management
- **Analytics:** Privacy-focused usage tracking and performance monitoring

### 4.3. Planned Enhancements (Next 6 Months)
- **Enhanced Coverage:** Expand to 50+ additional metropolitan areas
- **Permit Guidance:** Step-by-step permit application assistance
- **Document Templates:** Pre-filled permit applications and compliance checklists
- **Contractor Directory:** Vetted contractor recommendations by project type
- **API Access:** Developer API for third-party integrations
- **Mobile App:** Native iOS and Android applications
- **Advanced Analytics:** Detailed project cost and timeline estimates

### 4.4. Non-Functional Requirements
- **Performance:** <5 second response time for 95% of searches
- **Availability:** 99.9% uptime with monitoring and alerting
- **Security:** SOC 2 Type II compliance, data encryption, secure authentication
- **Scalability:** Auto-scaling infrastructure to handle traffic spikes
- **Compliance:** GDPR/CCPA compliance, copyright policy adherence
- **Accessibility:** WCAG 2.1 AA compliance for inclusive design

## 5. User Experience & Interface

### 5.1. Current User Flow
1. **Landing Page** → Learn about Ordrly and value proposition
2. **Address Entry** → Input property address with autocomplete
3. **Project Description** → Describe planned construction/renovation
4. **Analysis Results** → View compliance analysis and red flags
5. **AI Chat** → Ask follow-up questions and get clarification
6. **Account Creation** → Sign up for free or Pro account
7. **Search Management** → View history and manage saved searches

### 5.2. Design Principles
- **Simplicity:** Clean, intuitive interface accessible to non-professionals
- **Speed:** Fast loading and responsive interactions
- **Mobile-First:** Optimized for on-site use during project planning
- **Trust:** Clear disclaimers and professional presentation
- **Accessibility:** Inclusive design for all users

## 6. Data & Content Strategy

### 6.1. Data Sources (Current)
- **Public Ordinances:** Municipal building codes and zoning regulations
- **Government APIs:** Planning department and permit office data
- **GIS Data:** Property boundaries, zoning maps, and geographic information
- **Public Records:** Permit history and violation records where available

### 6.2. Copyright Compliance
- **Public Domain Only:** Display full text only for publicly available regulations
- **Reference Approach:** Cite but don't reproduce proprietary codes (IBC, NEC)
- **Clear Attribution:** Proper sourcing and links to official publishers
- **User Education:** Clear disclaimers about information limitations

### 6.3. Data Quality Assurance
- **Regular Updates:** Automated monitoring for regulation changes
- **Accuracy Verification:** Spot-checking against official sources
- **User Feedback:** Reporting mechanism for data issues
- **Coverage Transparency:** Clear indication of data availability by jurisdiction

## 7. Business Model & Pricing

### 7.1. Current Pricing Structure
- **Free Tier:** 3 searches (unauthenticated), 10 searches (authenticated)
- **Pro Tier:** $19/month - unlimited searches, AI analysis, red flag detection
- **Future Enterprise:** Custom pricing for contractors and real estate professionals

### 7.2. Revenue Projections
- **Current:** [Insert current MRR and subscriber count]
- **6 Months:** $5,000 MRR (~263 Pro subscribers)
- **12 Months:** $10,000 MRR (~526 Pro subscribers)
- **18 Months:** $25,000 MRR with enterprise tier launch

## 8. Development Roadmap

### 8.1. Current Quarter (Q1 2025)
- **Coverage Expansion:** Add 25 new metropolitan areas
- **Performance Optimization:** Improve search response times
- **User Experience:** Enhanced onboarding and tutorial flows
- **Analytics Implementation:** Detailed user behavior tracking

### 8.2. Next Quarter (Q2 2025)
- **Permit Assistance:** Document preparation and application guidance
- **API Development:** Beta API for developer partners
- **Mobile Optimization:** Enhanced mobile experience
- **Contractor Integration:** Partner directory and referral system

### 8.3. Future Quarters (Q3-Q4 2025)
- **Mobile Apps:** Native iOS and Android applications
- **Enterprise Features:** Multi-user accounts and team management
- **Advanced Analytics:** Cost estimation and project timeline tools
- **International Expansion:** Canadian market entry (pilot)

## 9. Risks & Mitigations

### 9.1. Technical Risks
- **Data Coverage Gaps:** Clearly communicate coverage limitations to users
- **API Dependencies:** Diversify data sources and implement fallback systems
- **Scalability Challenges:** Monitor performance and scale infrastructure proactively

### 9.2. Business Risks
- **Competition:** Focus on user experience and unique value proposition
- **Regulatory Changes:** Implement automated monitoring for regulation updates
- **User Adoption:** Continuous user research and product iteration

### 9.3. Legal Risks
- **Copyright Compliance:** Strict adherence to established copyright policy
- **Liability Exposure:** Comprehensive disclaimers and professional consultation guidance
- **Data Privacy:** Robust privacy controls and regulatory compliance

## 10. Success Metrics & KPIs

### 10.1. User Metrics
- **Monthly Active Users (MAU):** Target 1,000+ by end of year
- **Conversion Rate:** Free to Pro conversion target of 5%+
- **User Retention:** 80%+ monthly retention for Pro subscribers
- **Search Volume:** 10,000+ searches per month

### 10.2. Business Metrics
- **Monthly Recurring Revenue (MRR):** Target $5,000 by mid-year
- **Customer Acquisition Cost (CAC):** <$50 per Pro subscriber
- **Customer Lifetime Value (CLV):** >$200 per Pro subscriber
- **Churn Rate:** <5% monthly churn for Pro tier

### 10.3. Platform Metrics
- **Response Time:** <5 seconds for 95% of searches
- **Uptime:** 99.9% availability
- **Error Rate:** <1% of searches result in errors
- **User Satisfaction:** 4.5+ star average rating

---

## Document Information

- **Document Type:** Product Requirements Document
- **Company:** Ordrly AI LLC
- **Platform:** ordrly.ai
- **Document Date:** [Current Date]
- **Status:** Live Platform - Active Development
- **Next Review:** Monthly

**Note:** This PRD reflects the current operational status of Ordrly and serves as a roadmap for continued development and enhancement of the platform.
