# Ordrly User Personas & Customer Journey

**Last Updated:** [Current Date]  
**Platform:** ordrly.ai  
**Focus:** Consumer Property Compliance - "Know Before You Build"

## 1. Primary User Personas

### A. The DIY Homeowner
**"I want to renovate my kitchen but don't know what permits I need"**

- **Demographics:**
  - Age: 28–55
  - Income: $50K–$150K household
  - Tech comfort: Moderate to high (uses smartphones, online research)
  - Location: Suburban and urban homeowners across the U.S.

- **Pain Points:**
  - Confused by complex permit requirements and building codes
  - Worried about starting a project and getting shut down by the city
  - Doesn't know where to find reliable information about local regulations
  - Concerned about costly mistakes and code violations

- **Goals:**
  - Understand what permits are required before starting a project
  - Avoid surprises, fines, and project delays
  - Save money by planning properly from the start
  - Feel confident about compliance without hiring expensive consultants

- **How Ordrly Solves:**
  - Enter address and project description to get instant compliance analysis
  - AI-powered red flag detection for potential issues
  - Plain-English explanations of complex regulations
  - Interactive chat for specific questions about their project

### B. The Small Contractor/Builder
**"I need to quickly check compliance requirements for client quotes"**

- **Demographics:**
  - Age: 30–60
  - Business size: 1–10 employees
  - Tech comfort: Moderate (uses smartphones, basic software)
  - Location: Local and regional contractors across the U.S.

- **Pain Points:**
  - Time-consuming research for each new project location
  - Risk of underbidding due to unknown permit requirements
  - Clients expect quick turnaround on quotes and project timelines
  - Liability concerns about missing compliance requirements

- **Goals:**
  - Quickly assess compliance requirements for accurate bidding
  - Provide professional, informed service to clients
  - Avoid project delays and cost overruns
  - Build reputation as knowledgeable and reliable

- **How Ordrly Solves:**
  - Fast compliance checks for multiple properties
  - Project-specific analysis for accurate cost estimation
  - Professional reports to share with clients
  - Unlimited searches with Pro subscription for business use

### C. The Real Estate Investor
**"I want to know about restrictions before I buy a property"**

- **Demographics:**
  - Age: 35–65
  - Investment focus: Fix-and-flip, rental properties, development
  - Tech comfort: High (uses multiple online tools and platforms)
  - Location: Active in multiple markets across the U.S.

- **Pain Points:**
  - Hidden compliance issues that affect property value
  - Renovation restrictions that impact investment returns
  - Time pressure during due diligence periods
  - Need to evaluate multiple properties quickly

- **Goals:**
  - Identify potential red flags before making offers
  - Understand renovation possibilities and restrictions
  - Make informed investment decisions quickly
  - Avoid costly surprises after purchase

- **How Ordrly Solves:**
  - Rapid property compliance analysis during due diligence
  - Red flag detection for investment-killing restrictions
  - Historical compliance data and violation records
  - Bulk analysis capabilities for portfolio evaluation

## 2. Secondary User Personas

### D. The Real Estate Agent
**"I want to provide added value to my clients during property tours"**

- **Demographics:**
  - Age: 25–60
  - Tech comfort: High (uses CRM, mobile apps, social media)
  - Client focus: Residential buyers and sellers

- **Pain Points:**
  - Clients ask questions about renovation possibilities during showings
  - Wants to differentiate services from other agents
  - Limited knowledge about local building codes and restrictions

- **Goals:**
  - Provide immediate answers about property compliance
  - Add value to client relationships
  - Close deals faster with comprehensive property information

### E. The Property Manager
**"I need to ensure ongoing compliance across multiple properties"**

- **Demographics:**
  - Age: 30–55
  - Portfolio size: 10–500+ properties
  - Tech comfort: Moderate to high

- **Pain Points:**
  - Tracking compliance requirements across different jurisdictions
  - Tenant improvement requests and permit requirements
  - Liability for code violations and safety issues

- **Goals:**
  - Maintain compliance across property portfolio
  - Respond quickly to tenant requests and city notices
  - Minimize liability and violation risks

## 3. Customer Journey Map

### Stage 1: Problem Recognition
**"I have a construction project but don't know the rules"**

- **Triggers:**
  - Planning a home renovation or addition
  - Receiving a quote that mentions permits
  - Seeing a neighbor get cited for code violations
  - Buying a property and considering improvements

- **Emotions:** Confusion, anxiety, overwhelm
- **Needs:** Clear, reliable information about requirements

### Stage 2: Information Seeking
**"How do I find out what permits and codes apply?"**

- **Research Behavior:**
  - Google searches for local building codes
  - Visiting city/county websites (often confusing)
  - Asking friends, neighbors, or contractors
  - Calling city offices (long wait times, unclear answers)

- **Pain Points:**
  - Information scattered across multiple sources
  - Technical language difficult to understand
  - Conflicting or outdated information
  - Time-consuming research process

### Stage 3: Discovery of Ordrly
**"Finally, a simple way to get answers!"**

- **Discovery Channels:**
  - Google search for "building permits [city name]"
  - Social media ads targeting homeowners
  - Referral from contractor or real estate agent
  - Content marketing (blog posts, guides)
  - Word-of-mouth recommendations

- **First Impression:** Clean, professional interface with clear value proposition

### Stage 4: Trial & Onboarding
**"Let me try this with my address"**

- **Free Trial Experience:**
  - Enter address and project description
  - Receive instant compliance analysis
  - See red flags and requirements clearly explained
  - Try AI chat for follow-up questions

- **Onboarding Elements:**
  - Welcome email with tips and resources
  - In-app guidance for first search
  - Tutorial highlighting key features
  - Success metrics: Completes first search, asks AI question

### Stage 5: Value Realization
**"This is exactly what I needed!"**

- **Key Moments:**
  - Discovers a permit requirement they didn't know about
  - Gets clear answer to specific compliance question
  - Saves hours compared to manual research
  - Feels confident about project planning

- **Conversion Triggers:**
  - Hits free search limit
  - Needs unlimited access for multiple projects
  - Wants to save search history
  - Requires detailed analysis for contractor discussions

### Stage 6: Active Usage (Pro Subscriber)
**"I use this for every project now"**

- **Usage Patterns:**
  - Regular searches for new projects
  - Sharing results with contractors and family
  - Using AI chat for specific questions
  - Referring friends and neighbors

- **Value Reinforcement:**
  - Successful project completion without violations
  - Time savings on research
  - Confidence in compliance decisions
  - Professional credibility with contractors

### Stage 7: Advocacy & Retention
**"Everyone should know about this tool"**

- **Advocacy Behaviors:**
  - Recommending to friends and family
  - Sharing on social media
  - Leaving positive reviews
  - Participating in referral program

- **Retention Factors:**
  - Ongoing home improvement projects
  - Platform improvements and new features
  - Excellent customer support experience
  - Competitive pricing and value

## 4. Customer Journey Touchpoints

### Pre-Purchase Touchpoints
- **Website:** ordrly.ai landing page and content
- **Search Results:** SEO-optimized content for local building codes
- **Social Media:** Educational content and user testimonials
- **Referrals:** Word-of-mouth from satisfied users

### Purchase & Onboarding
- **Free Trial:** Immediate value demonstration
- **Sign-up Process:** Simple Google OAuth or email registration
- **Welcome Sequence:** Email series with tips and best practices
- **In-App Guidance:** Interactive tutorials and feature highlights

### Active Usage
- **Search Interface:** Clean, intuitive address and project input
- **Results Display:** Clear, actionable compliance information
- **AI Chat:** Responsive assistant for follow-up questions
- **Account Dashboard:** Search history and subscription management

### Support & Success
- **Help Documentation:** Comprehensive guides and FAQs
- **Email Support:** Responsive customer service
- **Feature Updates:** Regular platform improvements
- **Community:** User testimonials and success stories

## 5. Customer Journey Diagram

```
Problem Recognition → Information Seeking → Discovery
        ↓                    ↓              ↓
    (Anxiety)          (Frustration)    (Hope)
        ↓                    ↓              ↓
Free Trial → Value Realization → Pro Subscription
    ↓              ↓                  ↓
(Curiosity)   (Relief)         (Confidence)
    ↓              ↓                  ↓
Active Usage → Advocacy → Long-term Retention
    ↓              ↓              ↓
(Satisfaction) (Enthusiasm) (Loyalty)
```

## 6. Key Experience Principles

### Instant Value
- First search delivers comprehensive, actionable results
- Clear identification of potential issues and requirements
- Plain-English explanations of complex regulations

### Simplicity
- Intuitive interface accessible to non-professionals
- Minimal steps from question to answer
- Mobile-optimized for on-site use

### Trust & Transparency
- Clear disclaimers about information limitations
- Proper attribution and sourcing
- Guidance about professional consultation when needed

### Continuous Value
- Regular platform improvements and feature additions
- Responsive customer support and feedback incorporation
- Educational content and compliance tips

---

## Document Information

- **Document Type:** User Personas & Customer Journey
- **Company:** Ordrly AI LLC
- **Platform:** ordrly.ai
- **Document Date:** [Current Date]
- **Status:** Live Platform - Consumer Focus
- **Next Review:** Quarterly

**Note:** These personas and journey maps reflect the current consumer-focused positioning of Ordrly and should be updated based on user research and platform analytics.
