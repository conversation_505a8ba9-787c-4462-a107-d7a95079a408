# Policy: Handling of Paywalled and Copyrighted Model Codes

## Background

Certain building codes, standards, and regulations—such as the International Building Code (IBC), National Electrical Code (NEC), and other model codes—are published by private organizations and are only accessible behind paywalls or via paid subscriptions. Unauthorized reproduction, distribution, or display of the full text of these codes is prohibited by copyright law.

## Ordrly AI LLC Policy and Implementation

### 1. Reference Only, No Reproduction

- Ordrly will not store, display, or otherwise distribute the full text of any paywalled or copyrighted code.
- Instead, the system will reference these codes by official name, edition/year, and section number (e.g., "See IBC 2021 Section 1010.1 for door requirements").
- When possible, Ordrly will provide direct links to the official publisher's page or authorized distributors.

### 2. Display of Public Domain or Freely Available Content

- Ordrly will fully ingest and display the text of laws, regulations, or codes only if they are confirmed to be public domain or published as open data by government entities.
- No attempt will be made to bypass paywalls or acquire proprietary code through unauthorized means.
- All publicly available local ordinances, zoning regulations, and municipal codes will be displayed in full when legally permissible.

### 3. Summaries and Metadata

- For paywalled or proprietary codes, <PERSON>drly may display metadata (title, section reference, edition/year) and, where appropriate, a short summary or commentary—not the full text.
- AI-generated summaries will be clearly marked as interpretations and not official legal text.
- A disclaimer will clarify that users must consult the official source for the full legal code.

### 4. User Transparency

- Ordrly will clearly disclose in user-facing materials (Terms of Service, FAQ, and product messaging) that it does not provide full text of any proprietary or paywalled codes.
- **Example user message:**
  > "Some regulations, such as the International Building Code, require a paid subscription for access. Ordrly displays all available public regulations in full, but only provides references for proprietary codes. For full legal text, please consult the official publisher."

### 5. Compliance and Future Expansion

- As additional codes become publicly available, Ordrly will update its database to ingest and display these texts.
- This approach ensures full compliance with copyright law while maximizing the value provided to users.
- Regular reviews will be conducted to identify newly available public domain codes and regulations.

### 6. AI Analysis and Interpretation

- Ordrly's AI system may provide analysis and interpretation of publicly available regulations and codes.
- All AI-generated content will be clearly marked as analysis, not official legal text.
- Users will be directed to verify all information with local authorities and official sources.

### 7. Local Ordinance Priority

- Ordrly prioritizes local municipal codes, zoning ordinances, and publicly available regulations over proprietary model codes.
- When conflicts exist between local ordinances and model codes, local regulations take precedence in Ordrly's analysis.

## Implementation Guidelines

### For Development Team

1. **Code Ingestion Process:**
   - Verify copyright status before ingesting any new code or regulation
   - Maintain a whitelist of confirmed public domain sources
   - Document the source and copyright status of all ingested content

2. **User Interface Requirements:**
   - Display clear attribution for all referenced codes
   - Include disclaimers for proprietary code references
   - Provide links to official sources when available

3. **AI Training and Responses:**
   - Train AI models to distinguish between public and proprietary sources
   - Ensure AI responses include appropriate disclaimers
   - Implement safeguards against reproducing copyrighted text

### For Legal Compliance

1. **Regular Audits:**
   - Quarterly review of all ingested content for copyright compliance
   - Annual review of this policy for updates based on legal changes
   - Documentation of all compliance measures

2. **Incident Response:**
   - Immediate removal of any content identified as copyright violation
   - Clear process for handling takedown requests
   - Legal consultation for complex copyright questions

## Contact Information

For questions about this policy or to report potential copyright violations:

- **Legal Team:** <EMAIL>
- **General Support:** <EMAIL>
- **Website:** https://ordrly.ai/contact

## Document Information

- **Document Type:** Corporate Policy
- **Company:** Ordrly AI LLC
- **Effective Date:** [Date of Implementation]
- **Last Updated:** [Current Date]
- **Review Schedule:** Annual
- **Approved By:** Brandon Allen, Member

---

**Note:** This policy will be reviewed and updated as necessary to reflect changes in copyright law, code availability, user needs, and business requirements. All team members and contractors working with Ordrly must comply with this policy.
