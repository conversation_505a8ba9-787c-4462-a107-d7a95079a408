-- Phase 3: RAG Database Enhancement for Research-Level Accuracy
-- Database Schema Updates for Chat-Search Standard Implementation
-- Created: January 19, 2025

-- =====================================================
-- ENHANCED RAG SCHEMA: Research-Level Accuracy Fields
-- =====================================================

-- Add research-level accuracy fields to compliance_knowledge table
ALTER TABLE compliance_knowledge 
ADD COLUMN IF NOT EXISTS copyright_status TEXT CHECK (copyright_status IN ('public_domain', 'proprietary', 'model_code', 'unknown')) DEFAULT 'unknown',
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS legal_citation TEXT,
ADD COLUMN IF NOT EXISTS verification_status TEXT CHECK (verification_status IN ('verified', 'unverified', 'broken_link', 'pending')) DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS source_classification TEXT CHECK (source_classification IN ('municipal_ordinance', 'model_code', 'government_document', 'commentary', 'proprietary')) DEFAULT 'unknown',
ADD COLUMN IF NOT EXISTS research_quality_score DECIMAL(3,2) CHECK (research_quality_score >= 0 AND research_quality_score <= 1) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS verification_attempts INTEGER DEFAULT 0;

-- Add comments for documentation
COMMENT ON COLUMN compliance_knowledge.copyright_status IS 'Copyright classification: public_domain (full quotes allowed), proprietary (reference only), model_code (IBC/IRC/NEC), unknown';
COMMENT ON COLUMN compliance_knowledge.confidence_score IS 'AI confidence in content accuracy (0.0-1.0, 0.9+ required for responses)';
COMMENT ON COLUMN compliance_knowledge.legal_citation IS 'Exact legal citation format: "Per [City] Zoning Ordinance Chapter X, Section Y"';
COMMENT ON COLUMN compliance_knowledge.verification_status IS 'Source link verification status: verified (working), unverified, broken_link, pending';
COMMENT ON COLUMN compliance_knowledge.source_classification IS 'Content type classification from Phase 1 source classification engine';
COMMENT ON COLUMN compliance_knowledge.research_quality_score IS 'Overall research quality score combining confidence, verification, and citation accuracy';
COMMENT ON COLUMN compliance_knowledge.last_verified_at IS 'Last time source URL was verified as accessible';
COMMENT ON COLUMN compliance_knowledge.verification_attempts IS 'Number of verification attempts (for retry logic)';

-- =====================================================
-- CONFIDENCE-BASED CACHING TABLES
-- =====================================================

-- Create confidence threshold configuration table
CREATE TABLE IF NOT EXISTS confidence_thresholds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  threshold_type TEXT NOT NULL CHECK (threshold_type IN ('response_minimum', 'cache_use', 'real_time_trigger', 'quality_gate')),
  threshold_value DECIMAL(3,2) NOT NULL CHECK (threshold_value >= 0 AND threshold_value <= 1),
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(threshold_type)
);

-- Insert default confidence thresholds
INSERT INTO confidence_thresholds (threshold_type, threshold_value, description) VALUES
('response_minimum', 0.90, 'Minimum confidence required to provide a response to users'),
('cache_use', 0.90, 'Minimum confidence to use cached RAG data instead of real-time research'),
('real_time_trigger', 0.89, 'Confidence below this triggers real-time research'),
('quality_gate', 0.95, 'High-quality threshold for premium responses')
ON CONFLICT (threshold_type) DO NOTHING;

-- Create research session tracking table
CREATE TABLE IF NOT EXISTS research_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_type TEXT NOT NULL CHECK (session_type IN ('rag_lookup', 'real_time_research', 'hybrid')),
  jurisdiction TEXT NOT NULL,
  rule_type TEXT NOT NULL,
  user_query TEXT,
  rag_confidence DECIMAL(3,2),
  research_confidence DECIMAL(3,2),
  final_confidence DECIMAL(3,2),
  sources_found INTEGER DEFAULT 0,
  sources_verified INTEGER DEFAULT 0,
  response_generated BOOLEAN DEFAULT false,
  processing_time_ms INTEGER,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- JURISDICTION-SPECIFIC KNOWLEDGE FILTERING
-- =====================================================

-- Create jurisdiction hierarchy table for filtering
CREATE TABLE IF NOT EXISTS jurisdiction_hierarchy (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  jurisdiction_name TEXT NOT NULL,
  parent_jurisdiction TEXT,
  jurisdiction_level TEXT CHECK (jurisdiction_level IN ('federal', 'state', 'county', 'city', 'township')),
  state_code TEXT,
  county_name TEXT,
  aliases TEXT[] DEFAULT '{}', -- Alternative names for the jurisdiction
  coverage_area JSONB DEFAULT '{}', -- Geographic coverage details
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(jurisdiction_name, state_code)
);

-- Create jurisdiction filtering rules table
CREATE TABLE IF NOT EXISTS jurisdiction_filters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  jurisdiction TEXT NOT NULL,
  filter_type TEXT CHECK (filter_type IN ('include_parent', 'exclude_child', 'alias_match', 'geographic_overlap')),
  filter_value TEXT NOT NULL,
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- SOURCE VERIFICATION TRACKING
-- =====================================================

-- Create source verification log table
CREATE TABLE IF NOT EXISTS source_verification_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  compliance_knowledge_id BIGINT REFERENCES compliance_knowledge(id) ON DELETE CASCADE,
  verification_type TEXT CHECK (verification_type IN ('url_check', 'content_validation', 'citation_accuracy', 'copyright_check')),
  verification_result TEXT CHECK (verification_result IN ('pass', 'fail', 'warning', 'error')),
  verification_details JSONB DEFAULT '{}',
  verified_at TIMESTAMPTZ DEFAULT now(),
  verified_by TEXT -- 'system' or user ID
);

-- Create source quality metrics table
CREATE TABLE IF NOT EXISTS source_quality_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  compliance_knowledge_id BIGINT REFERENCES compliance_knowledge(id) ON DELETE CASCADE,
  accessibility_score DECIMAL(3,2) DEFAULT 0.0, -- Can the source be accessed?
  accuracy_score DECIMAL(3,2) DEFAULT 0.0, -- Is the citation accurate?
  completeness_score DECIMAL(3,2) DEFAULT 0.0, -- Does it contain full legal text?
  freshness_score DECIMAL(3,2) DEFAULT 0.0, -- How recent is the information?
  authority_score DECIMAL(3,2) DEFAULT 0.0, -- Is it from an official source?
  overall_quality_score DECIMAL(3,2) DEFAULT 0.0, -- Weighted average of all scores
  last_calculated_at TIMESTAMPTZ DEFAULT now(),
  calculation_details JSONB DEFAULT '{}'
);

-- =====================================================
-- RESEARCH QUALITY ASSURANCE
-- =====================================================

-- Create test case validation table for 100-test suite
CREATE TABLE IF NOT EXISTS research_test_cases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  test_category TEXT CHECK (test_category IN ('common_projects', 'geographic_diversity', 'complexity_levels', 'edge_cases')),
  test_name TEXT NOT NULL,
  jurisdiction TEXT NOT NULL,
  rule_type TEXT NOT NULL,
  expected_citation TEXT,
  expected_confidence DECIMAL(3,2),
  test_query TEXT NOT NULL,
  success_criteria JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(test_name)
);

-- Create test execution results table
CREATE TABLE IF NOT EXISTS test_execution_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  test_case_id UUID REFERENCES research_test_cases(id) ON DELETE CASCADE,
  execution_date TIMESTAMPTZ DEFAULT now(),
  passed BOOLEAN,
  actual_confidence DECIMAL(3,2),
  actual_citation TEXT,
  response_time_ms INTEGER,
  sources_found INTEGER,
  error_message TEXT,
  execution_details JSONB DEFAULT '{}'
);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Indexes for enhanced compliance_knowledge queries
CREATE INDEX IF NOT EXISTS idx_compliance_knowledge_confidence 
ON compliance_knowledge(confidence_score DESC, jurisdiction, document_type) 
WHERE confidence_score >= 0.90;

CREATE INDEX IF NOT EXISTS idx_compliance_knowledge_verification 
ON compliance_knowledge(verification_status, last_verified_at DESC) 
WHERE verification_status = 'verified';

CREATE INDEX IF NOT EXISTS idx_compliance_knowledge_copyright 
ON compliance_knowledge(copyright_status, source_classification);

CREATE INDEX IF NOT EXISTS idx_compliance_knowledge_quality 
ON compliance_knowledge(research_quality_score DESC, is_active) 
WHERE research_quality_score >= 0.90 AND is_active = true;

-- Indexes for research session tracking
CREATE INDEX IF NOT EXISTS idx_research_sessions_jurisdiction_rule 
ON research_sessions(jurisdiction, rule_type, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_research_sessions_confidence 
ON research_sessions(final_confidence DESC, created_at DESC);

-- Indexes for jurisdiction filtering
CREATE INDEX IF NOT EXISTS idx_jurisdiction_hierarchy_name 
ON jurisdiction_hierarchy(jurisdiction_name, state_code);

CREATE INDEX IF NOT EXISTS idx_jurisdiction_hierarchy_aliases 
ON jurisdiction_hierarchy USING GIN(aliases);

-- Indexes for source verification
CREATE INDEX IF NOT EXISTS idx_source_verification_log_knowledge_id 
ON source_verification_log(compliance_knowledge_id, verified_at DESC);

CREATE INDEX IF NOT EXISTS idx_source_quality_metrics_knowledge_id 
ON source_quality_metrics(compliance_knowledge_id, overall_quality_score DESC);

-- =====================================================
-- TRIGGERS AND FUNCTIONS
-- =====================================================

-- Function to calculate research quality score
CREATE OR REPLACE FUNCTION calculate_research_quality_score(
  p_confidence_score DECIMAL,
  p_verification_status TEXT,
  p_copyright_status TEXT,
  p_legal_citation TEXT
) RETURNS DECIMAL AS $$
DECLARE
  quality_score DECIMAL := 0.0;
BEGIN
  -- Base score from confidence (40% weight)
  quality_score := COALESCE(p_confidence_score, 0.0) * 0.4;
  
  -- Verification status (30% weight)
  CASE p_verification_status
    WHEN 'verified' THEN quality_score := quality_score + 0.3;
    WHEN 'unverified' THEN quality_score := quality_score + 0.1;
    WHEN 'broken_link' THEN quality_score := quality_score + 0.0;
    ELSE quality_score := quality_score + 0.05;
  END CASE;
  
  -- Copyright compliance (20% weight)
  CASE p_copyright_status
    WHEN 'public_domain' THEN quality_score := quality_score + 0.2;
    WHEN 'model_code' THEN quality_score := quality_score + 0.15;
    WHEN 'proprietary' THEN quality_score := quality_score + 0.1;
    ELSE quality_score := quality_score + 0.0;
  END CASE;
  
  -- Legal citation presence (10% weight)
  IF p_legal_citation IS NOT NULL AND LENGTH(p_legal_citation) > 10 THEN
    quality_score := quality_score + 0.1;
  END IF;
  
  RETURN LEAST(quality_score, 1.0);
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-calculate research quality score
CREATE OR REPLACE FUNCTION trigger_research_quality_update() RETURNS TRIGGER AS $$
BEGIN
  NEW.research_quality_score := calculate_research_quality_score(
    NEW.confidence_score,
    NEW.verification_status,
    NEW.copyright_status,
    NEW.legal_citation
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to compliance_knowledge table
DROP TRIGGER IF EXISTS compliance_knowledge_quality_update ON compliance_knowledge;
CREATE TRIGGER compliance_knowledge_quality_update
  BEFORE INSERT OR UPDATE ON compliance_knowledge
  FOR EACH ROW
  EXECUTE FUNCTION trigger_research_quality_update();

-- =====================================================
-- INITIAL DATA MIGRATION
-- =====================================================

-- Backfill existing compliance_knowledge records with default values
UPDATE compliance_knowledge 
SET 
  copyright_status = CASE 
    WHEN source_url LIKE '%.gov%' OR source_url LIKE '%municipal%' THEN 'public_domain'
    WHEN metadata->>'source_type' = 'model_code' THEN 'model_code'
    ELSE 'unknown'
  END,
  confidence_score = COALESCE((metadata->>'confidence_score')::DECIMAL, 0.5),
  verification_status = CASE 
    WHEN source_url IS NOT NULL AND LENGTH(source_url) > 10 THEN 'pending'
    ELSE 'unverified'
  END,
  source_classification = CASE 
    WHEN source_url LIKE '%.gov%' THEN 'municipal_ordinance'
    WHEN metadata->>'source_type' = 'model_code' THEN 'model_code'
    ELSE 'unknown'
  END
WHERE copyright_status IS NULL;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify schema updates
DO $$
BEGIN
  RAISE NOTICE 'Phase 3 RAG Enhancement Schema Applied Successfully';
  RAISE NOTICE 'New columns added to compliance_knowledge: copyright_status, confidence_score, legal_citation, verification_status, source_classification, research_quality_score, last_verified_at, verification_attempts';
  RAISE NOTICE 'New tables created: confidence_thresholds, research_sessions, jurisdiction_hierarchy, jurisdiction_filters, source_verification_log, source_quality_metrics, research_test_cases, test_execution_results';
  RAISE NOTICE 'Indexes created: % total', (
    SELECT COUNT(*) FROM pg_indexes 
    WHERE indexname LIKE 'idx_%confidence%' 
    OR indexname LIKE 'idx_%verification%' 
    OR indexname LIKE 'idx_%quality%'
    OR indexname LIKE 'idx_%research%'
    OR indexname LIKE 'idx_%jurisdiction%'
  );
END $$;
