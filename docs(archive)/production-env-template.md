# Production Environment Variables

## Main Ordrly App (ordrly.ai)

Copy these environment variables to your Vercel deployment for the main app:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://qxiryfbdruydrofclmvz.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwNDgzNzMsImV4cCI6MjA2MzYyNDM3M30.HOPafUUWPiqUVjas4Bouwsynx0YsVZHQ1SPWsHbyARc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE

# AI and Search APIs
OPENAI_API_KEY=********************************************************************************************************************************************************************
BRAVE_SEARCH_API_KEY=BSAJvAqqFGvJqXZH7I7cit0pL3nPMYw
PERPLEXITY_API_KEY=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

# AI Model Configuration
OPENAI_MODEL=gpt-4.1-nano
OPENAI_MAX_TOKENS=32768
OPENAI_TEMPERATURE=0.1

# Google APIs
GOOGLE_SEARCH_API_KEY=AIzaSyC58qnhglLxqXbCZzVZ5l3cYkQgRfz5D4M
GOOGLE_SEARCH_ENGINE_ID=e03a4eb72855f4221
GOOGLE_GEOCODING_API_KEY=AIzaSyCGxu6yXu41qhnHjUAkcvRD5UDjsh3CKoI

# Geoapify
GEOAPIFY_API_KEY=********************************

# Production URLs
NEXT_PUBLIC_BASE_URL=https://ordrly.ai
NEXT_PUBLIC_SITE_URL=https://ordrly.ai

# Municipal Research API
MUNICIPAL_API_KEY=ordrly_a14dacb722b572748c33627b1ac0426a7cd03b07460214523250941e43b90f2e
MUNICIPAL_API_URL=https://api.ordrly.ai

# Stripe Configuration (PRODUCTION KEYS NEEDED)
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_KEY_HERE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET_HERE

# Production Stripe Price IDs (UPDATE WITH LIVE PRICES)
STRIPE_PRICE_STARTER_MONTHLY=price_LIVE_STARTER_MONTHLY
STRIPE_PRICE_STARTER_ANNUAL=price_LIVE_STARTER_ANNUAL
STRIPE_PRICE_PROFESSIONAL_MONTHLY=price_LIVE_PROFESSIONAL_MONTHLY
STRIPE_PRICE_PROFESSIONAL_ANNUAL=price_LIVE_PROFESSIONAL_ANNUAL

# Public Stripe Price IDs
NEXT_PUBLIC_STRIPE_PRICE_STARTER_MONTHLY=price_LIVE_STARTER_MONTHLY
NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL=price_LIVE_STARTER_ANNUAL
NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_MONTHLY=price_LIVE_PROFESSIONAL_MONTHLY
NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_ANNUAL=price_LIVE_PROFESSIONAL_ANNUAL

# Email Configuration
RESEND_API_KEY=re_eAFA86pF_C6AwqNf8D9CAPARnvz8VWznZ

# Feature Flags
NEXT_PUBLIC_EPIC6_ENABLED=true
NEXT_PUBLIC_CHAT_ENABLED=true
NEXT_PUBLIC_RED_FLAGS_ENABLED=true
NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED=true
NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED=true

# Chat Configuration
CHAT_RAG_CONFIDENCE_THRESHOLD=0.9
CHAT_VECTOR_SIMILARITY_THRESHOLD=0.8
CHAT_FALLBACK_ENABLED=true
CHAT_AI_UNCERTAINTY_CHECK=false
CHAT_PARTIAL_ANSWER_ENABLED=false
CHAT_FALLBACK_LOGGING=true
CHAT_MAX_RETRIEVED_SNIPPETS=5
CHAT_EMBEDDING_CACHE_SIZE=1000
CHAT_EMBEDDING_CACHE_TTL=3600000

# Production Environment
NODE_ENV=production
```

## Municipal Research API (api.ordrly.ai)

Copy these environment variables to your Vercel deployment for the API:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://qxiryfbdruydrofclmvz.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwNDgzNzMsImV4cCI6MjA2MzYyNDM3M30.HOPafUUWPiqUVjas4Bouwsynx0YsVZHQ1SPWsHbyARc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE

# AI APIs
OPENAI_API_KEY=********************************************************************************************************************************************************************
PERPLEXITY_API_KEY=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
GOOGLE_API_KEY=AIzaSyBoT9p4RFNk4-c-SCQyLc07-lGJ8-RxN5E

# Google APIs
GOOGLE_SEARCH_API_KEY=AIzaSyC58qnhglLxqXbCZzVZ5l3cYkQgRfz5D4M
GOOGLE_GEOCODING_API_KEY=AIzaSyCGxu6yXu41qhnHjUAkcvRD5UDjsh3CKoI

# Stripe Configuration (PRODUCTION KEYS NEEDED)
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET_HERE

# Production Environment
NODE_ENV=production
PORT=3001
```

## Important Notes:

1. **Replace Stripe Keys**: Update all Stripe keys with your live/production keys
2. **Create Live Stripe Products**: Set up your pricing plans in Stripe's live mode and update the price IDs
3. **Domain Configuration**: Make sure your domains are properly configured:
   - Main app: `ordrly.ai` (with www redirect)
   - API: `api.ordrly.ai`
4. **Webhook Endpoints**: Update Stripe webhook endpoints to point to production URLs
5. **CORS Configuration**: The municipal API is already configured to allow requests from production domains
