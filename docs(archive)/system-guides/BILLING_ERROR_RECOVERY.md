# 🚑 Billing Error Recovery Guide

## 🎯 Common Billing Errors & Fixes

### **Error: "Your billing information could not be found"**

**Root Cause:** Mismatch between Ordrly database and Stripe customer data

**Diagnosis Steps:**
1. **Check user's Stripe customer ID:**
   ```sql
   SELECT email, stripe_customer_id, subscription_tier, subscription_status
   FROM profiles 
   WHERE email = '<EMAIL>';
   ```

2. **Check if customer exists in Stripe:**
   - Stripe Dashboard → Customers → Search by customer ID
   - If not found: Customer was deleted from Stripe

3. **Check webhook logs:**
   - Look for failed webhook events
   - Check for signature verification errors

**Recovery Options:**

#### **Option A: Reset to Free Tier (Recommended)**
```sql
UPDATE profiles SET 
  stripe_customer_id = NULL,
  subscription_tier = 'free',
  subscription_status = 'active',
  subscription_period_end = NULL,
  cancel_at_period_end = false,
  pulls_this_month = 0,
  extra_credits = 0
WHERE email = '<EMAIL>';
```
**Result:** Customer can sign up for Pro again normally

#### **Option B: Recreate Stripe Customer (Advanced)**
1. Create new customer in Stripe with same email
2. Update database with new customer ID
3. Handle any active subscriptions manually

---

### **Error: "Unable to access billing portal"**

**Root Cause:** Stripe customer exists but portal configuration issues

**Diagnosis Steps:**
1. **Verify customer ID is valid:**
   ```sql
   SELECT stripe_customer_id FROM profiles WHERE email = '<EMAIL>';
   ```

2. **Test customer in Stripe:**
   - Stripe Dashboard → Customers → Find customer
   - Try creating a portal session manually

3. **Check billing portal configuration:**
   - Stripe Dashboard → Settings → Billing → Customer portal

**Recovery Steps:**
1. **Verify portal is enabled** in Stripe settings
2. **Check portal features** are configured
3. **Test portal creation** with test customer
4. **If still failing:** Reset customer to free tier

---

### **Error: "Subscription not found"**

**Root Cause:** Database shows subscription but Stripe doesn't have it

**Diagnosis Steps:**
1. **Check database subscription data:**
   ```sql
   SELECT subscription_tier, subscription_status, stripe_customer_id
   FROM profiles 
   WHERE email = '<EMAIL>';
   ```

2. **Check Stripe subscriptions:**
   - Stripe Dashboard → Subscriptions → Search by customer
   - Look for active, canceled, or past_due subscriptions

**Recovery Steps:**
1. **If no subscription in Stripe:**
   ```sql
   UPDATE profiles SET 
     subscription_tier = 'free',
     subscription_status = 'active',
     cancel_at_period_end = false,
     subscription_period_end = NULL
   WHERE email = '<EMAIL>';
   ```

2. **If subscription exists but different status:**
   - Update database to match Stripe status
   - Check why webhook didn't sync properly

---

## 🔧 Data Sync Recovery

### **Full Account Reset (Nuclear Option)**
Use when account is completely broken:

```sql
-- Backup current state first
CREATE TABLE profiles_backup AS 
SELECT * FROM profiles WHERE email = '<EMAIL>';

-- Reset to clean free tier
UPDATE profiles SET 
  stripe_customer_id = NULL,
  subscription_tier = 'free',
  subscription_status = 'active',
  subscription_period_end = NULL,
  cancel_at_period_end = false,
  is_subscribed = false,
  pulls_this_month = 0,
  extra_credits = 0,
  updated_at = NOW()
WHERE email = '<EMAIL>';
```

### **Partial Sync Fix**
Use when only some fields are wrong:

```sql
-- Fix subscription tier mismatch
UPDATE profiles SET 
  subscription_tier = 'pro',
  subscription_status = 'active',
  is_subscribed = true,
  updated_at = NOW()
WHERE email = '<EMAIL>' 
AND stripe_customer_id IS NOT NULL;

-- Fix cancellation status
UPDATE profiles SET 
  cancel_at_period_end = true,
  subscription_status = 'canceling',
  updated_at = NOW()
WHERE email = '<EMAIL>';
```

### **Stripe Customer Recreation**
Use when customer was accidentally deleted from Stripe:

1. **Create new customer in Stripe:**
   ```javascript
   // In Stripe Dashboard or via API
   const customer = await stripe.customers.create({
     email: '<EMAIL>',
     name: 'Customer Name',
     metadata: {
       ordrly_user_id: 'user_id_from_database'
     }
   });
   ```

2. **Update database with new customer ID:**
   ```sql
   UPDATE profiles SET 
     stripe_customer_id = 'cus_NEW_CUSTOMER_ID',
     updated_at = NOW()
   WHERE email = '<EMAIL>';
   ```

3. **If customer had active subscription:**
   - Create new subscription in Stripe
   - Update database with subscription details

---

## 🚨 Emergency Recovery Procedures

### **Multiple Customers Affected**
If webhook failures affected many customers:

1. **Stop webhook processing** temporarily:
   - Disable webhook in Stripe Dashboard
   - Or add maintenance mode to webhook handler

2. **Identify affected customers:**
   ```sql
   -- Find customers with potential sync issues
   SELECT email, stripe_customer_id, subscription_tier, updated_at
   FROM profiles 
   WHERE updated_at < NOW() - INTERVAL '24 hours'
   AND stripe_customer_id IS NOT NULL
   AND subscription_tier = 'free';
   ```

3. **Batch fix common issues:**
   ```sql
   -- Reset all broken accounts to free tier
   UPDATE profiles SET 
     stripe_customer_id = NULL,
     subscription_tier = 'free',
     subscription_status = 'active',
     subscription_period_end = NULL,
     cancel_at_period_end = false
   WHERE stripe_customer_id IN (
     'cus_deleted_customer_1',
     'cus_deleted_customer_2'
     -- Add all deleted customer IDs
   );
   ```

4. **Re-enable webhook processing**

5. **Notify affected customers**

### **Database Corruption**
If database has widespread corruption:

1. **Backup current state:**
   ```sql
   CREATE TABLE profiles_backup_[timestamp] AS SELECT * FROM profiles;
   ```

2. **Identify corruption patterns:**
   ```sql
   -- Find impossible states
   SELECT * FROM profiles 
   WHERE stripe_customer_id IS NULL 
   AND subscription_tier != 'free';
   
   -- Find sync issues
   SELECT * FROM profiles 
   WHERE subscription_status = 'active' 
   AND subscription_tier = 'free' 
   AND stripe_customer_id IS NOT NULL;
   ```

3. **Fix systematically:**
   - Start with clear cases (NULL customer_id = free tier)
   - Verify each fix with Stripe data
   - Test with affected customers

---

## 📊 Prevention & Monitoring

### **Daily Health Checks**
```sql
-- Check for potential sync issues
SELECT 
  COUNT(*) as total_users,
  COUNT(CASE WHEN stripe_customer_id IS NOT NULL THEN 1 END) as stripe_customers,
  COUNT(CASE WHEN subscription_tier = 'pro' THEN 1 END) as pro_users,
  COUNT(CASE WHEN subscription_tier = 'pro' AND stripe_customer_id IS NULL THEN 1 END) as broken_pro_users
FROM profiles;
```

### **Weekly Audits**
```sql
-- Find customers with mismatched data
SELECT email, stripe_customer_id, subscription_tier, subscription_status, updated_at
FROM profiles 
WHERE (
  -- Pro tier without Stripe customer
  (subscription_tier = 'pro' AND stripe_customer_id IS NULL)
  OR
  -- Free tier with Stripe customer (potential downgrade missed)
  (subscription_tier = 'free' AND stripe_customer_id IS NOT NULL)
  OR
  -- Very old update timestamps (potential webhook miss)
  (updated_at < NOW() - INTERVAL '7 days' AND subscription_tier = 'pro')
)
ORDER BY updated_at DESC;
```

### **Automated Alerts**
Set up monitoring for:
- Webhook failure rates > 5%
- Billing portal errors > 10 per day
- Database sync issues > 5 per day
- Customer support tickets mentioning billing

---

## 📞 Customer Communication

### **When Resetting Account to Free:**
```
Hi [Name],

I've resolved the billing error you were experiencing. Your account has been reset to the free tier, and you should now be able to access your account normally.

If you'd like to upgrade to Pro again, you can do so at: https://www.ordrly.ai/pricing

I apologize for the inconvenience, and please let me know if you have any other questions.

Best regards,
[Your name]
```

### **When Fixing Sync Issues:**
```
Hi [Name],

I've fixed the sync issue between your account and our billing system. Your Pro subscription is now properly activated and you should have full access to all Pro features.

Your Pro benefits include:
- Unlimited searches
- AI-powered insights  
- Red flag detection

Thank you for your patience while we resolved this issue.

Best regards,
[Your name]
```

### **When Manual Intervention Required:**
```
Hi [Name],

I'm working on resolving the billing issue with your account. This requires some manual coordination between our systems and Stripe.

I'll have this resolved within 24 hours and will update you as soon as it's fixed. In the meantime, if you need immediate access to Pro features, please let me know and I can provide temporary access.

Thank you for your patience.

Best regards,
[Your name]
```

---

## 🔗 Related Documentation

- `REFUND_PROCESSING_GUIDE.md` - How to process refunds properly
- `STRIPE_CUSTOMER_SUPPORT.md` - Customer support scenarios
- `WEBHOOK_TROUBLESHOOTING.md` - Webhook debugging guide
