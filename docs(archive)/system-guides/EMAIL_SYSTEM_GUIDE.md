# Ordrly Email System Guide

## 📧 **Complete Email System Overview**

Your Ordrly application now has a comprehensive email system that handles all user communications automatically.

### **✅ Email Types Implemented:**

#### **Authentication Emails**
1. **Account Verification** - Sent by Supa<PERSON> when user signs up
2. **Welcome Email** - Sent after email verification or immediate signup
3. **Password Reset** - Sent when user requests password reset

#### **Payment & Subscription Emails**
4. **Subscription Confirmed** - Sent when payment succeeds
5. **Payment Failed** - Sent when payment fails with retry info
6. **Subscription Canceled** - Sent when subscription is canceled
7. **Credits Added** - Sent when user purchases search credits

#### **Engagement Emails**
8. **Referral Credits Earned** - Sent when referral rewards are given
9. **Usage Limit Warning** - Sent when approaching search limits
10. **Abandonment Nudge** - Marketing email for inactive users
11. **Upgrade Reminder** - Marketing email for free users

## 🔧 **Configuration**

### **Environment Variables Required:**
```bash
# Resend API Configuration
RESEND_API_KEY=re_your_api_key_here

# Email Configuration
EMAIL_FROM=Ordrly <<EMAIL>>
EMAIL_REPLY_TO=<EMAIL>
```

### **Supabase Email Settings:**
1. **Go to Supabase Dashboard** → Authentication → Email Templates
2. **Configure these templates:**
   - Confirm signup
   - Magic Link
   - Change Email Address
   - Reset Password

3. **Customize with Ordrly branding:**
   - Use Ordrly logo
   - Match color scheme (#1DA1F2)
   - Update copy to match brand voice

## 🧪 **Testing**

### **Test All Email Types:**
```bash
# Test with your email
npm run test:emails <EMAIL>

# Test with default test email
npm run test:emails
```

### **Test Individual Flows:**
```bash
# Test password reset
npm run test:password-reset

# Test complete signup flow
# 1. Go to /signup
# 2. Create account
# 3. Check email for verification
# 4. Click verification link
# 5. Should receive welcome email
```

## 📊 **Email Triggers**

### **Automatic Triggers:**

| Event | Email Sent | Trigger Location |
|-------|------------|------------------|
| User signs up | Account verification | Supabase |
| Email verified | Welcome email | `/auth/confirm/route.ts` |
| Payment succeeds | Subscription confirmed | Stripe webhook |
| Payment fails | Payment failed | Stripe webhook |
| Subscription canceled | Cancellation notice | Stripe webhook |
| Credits purchased | Credits added | Stripe webhook |
| Referral signup | Credits earned | Email verification |

### **Manual Triggers:**
- Marketing emails via `/api/email-campaigns/process`
- Admin-triggered emails via dashboard

## 🎨 **Email Templates**

### **Template Structure:**
```javascript
templateName: {
  subject: 'Email Subject',
  text: (data) => `Email content with ${data.variables}`
}
```

### **Available Variables:**
- `name` - User's name or email prefix
- `email` - User's email address
- `planName` - Subscription plan (Pro, Appraiser)
- `amount` - Payment amount
- `credits` - Number of credits
- `endDate` - Subscription end date

## 🔒 **Security & Compliance**

### **Email Security:**
- ✅ SPF records configured for ordrly.ai
- ✅ DKIM signing enabled via Resend
- ✅ Unsubscribe links in marketing emails
- ✅ Rate limiting on email sending
- ✅ No sensitive data in email content

### **Privacy Compliance:**
- ✅ Clear opt-in for marketing emails
- ✅ Easy unsubscribe process
- ✅ Data retention policies
- ✅ GDPR-compliant email handling

## 📈 **Monitoring & Analytics**

### **Email Metrics to Track:**
1. **Delivery Rate** - Emails successfully delivered
2. **Open Rate** - Emails opened by recipients
3. **Click Rate** - Links clicked in emails
4. **Bounce Rate** - Failed deliveries
5. **Unsubscribe Rate** - Users opting out

### **Monitoring Setup:**
```bash
# Check Resend dashboard for:
# - Delivery status
# - Bounce reports
# - Spam complaints
# - API usage
```

## 🚀 **Production Checklist**

### **Before Going Live:**
- [ ] Verify RESEND_API_KEY is production key
- [ ] Test all email types with real email addresses
- [ ] Configure SPF/DKIM records for ordrly.ai domain
- [ ] Set up email monitoring and alerts
- [ ] Test email deliverability to major providers
- [ ] Configure Supabase email templates with Ordrly branding
- [ ] Set up email analytics tracking

### **Domain Configuration:**
1. **Add ordrly.ai to Resend** as verified domain
2. **Configure DNS records:**
   ```
   TXT record: v=spf1 include:_spf.resend.com ~all
   CNAME record: resend._domainkey.ordrly.ai → resend._domainkey.resend.com
   ```

## 🔧 **Troubleshooting**

### **Common Issues:**

#### **Emails Not Sending:**
1. Check RESEND_API_KEY is valid
2. Verify domain is verified in Resend
3. Check Resend credit balance
4. Review error logs in application

#### **Emails Going to Spam:**
1. Verify SPF/DKIM records
2. Check sender reputation
3. Review email content for spam triggers
4. Warm up sending domain gradually

#### **Template Errors:**
1. Check template syntax in `/lib/email/templates.ts`
2. Verify all required variables are passed
3. Test templates with sample data

### **Debug Commands:**
```bash
# Test email configuration
npm run test:emails

# Check environment variables
npm run verify:env

# Test specific email type
node -e "require('./src/lib/email/sender').sendWelcomeEmail('<EMAIL>')"
```

## 📞 **Support**

### **Email System Support:**
- **Resend Documentation**: https://resend.com/docs
- **Supabase Auth Emails**: https://supabase.com/docs/guides/auth/auth-email-templates
- **Email Deliverability**: https://resend.com/docs/knowledge-base/deliverability

### **Emergency Contacts:**
- **Email delivery issues**: Check Resend dashboard
- **Template issues**: Review application logs
- **Authentication emails**: Check Supabase dashboard

---

**Your email system is now production-ready!** 🎉

All user touchpoints are covered with professional, branded emails that enhance the user experience and drive engagement.
