# Production Stripe Keys Configuration

## 🚨 CRITICAL: Replace ALL Stripe keys with LIVE keys

### Current Status: TEST KEYS (Development)
```bash
STRIPE_SECRET_KEY=sk_test_51RSh98R0tpX4T5Iqzoayfh8qflSxcFFmzAW8UcqQC3zpRnRrQWQkttsbVoWfz1rXAZULGF7aPRRGl352MoqnkmmN000csKLwTz
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RSh98R0tpX4T5Iq8l1r3kyFbpijwQa8Kz46yJglXxn0VnqvKX71rXdO2eFMDNzVkofLWmqkunoI2Hrrs4Wl2yWQ00kGexLZBt
```

### Required: LIVE KEYS (Production)
```bash
# Replace with your LIVE keys from Stripe Dashboard
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_LIVE_WEBHOOK_SECRET

# Replace with LIVE price IDs
STRIPE_PRICE_PRO=price_YOUR_LIVE_PRO_PRICE_ID
STRIPE_PRICE_APPRAISER=price_YOUR_LIVE_APPRAISER_PRICE_ID
NEXT_PUBLIC_STRIPE_PRICE_PRO=price_YOUR_LIVE_PRO_PRICE_ID
NEXT_PUBLIC_STRIPE_PRICE_APPRAISER=price_YOUR_LIVE_APPRAISER_PRICE_ID
```

## 📋 Step-by-Step Instructions

### 1. Get Live Keys from Stripe Dashboard
1. Go to https://dashboard.stripe.com
2. **Switch to Live Mode** (toggle off "Test mode")
3. Go to **Developers > API keys**
4. Copy **Publishable key** (pk_live_...)
5. Reveal and copy **Secret key** (sk_live_...)

### 2. Create Live Products
1. Go to **Products** in Stripe Dashboard (Live mode)
2. Create "Ordrly Pro" - $19/month
3. Create "Ordrly Appraiser" - $59/month (if needed)
4. Copy the **Price IDs** for each

### 3. Set Up Live Webhook
1. Go to **Webhooks** in Stripe Dashboard (Live mode)
2. Add endpoint: `https://www.ordrly.ai/api/webhooks/stripe`
3. Select events:
   - checkout.session.completed
   - customer.subscription.created
   - customer.subscription.updated
   - customer.subscription.deleted
   - invoice.payment_succeeded
   - invoice.payment_failed
   - charge.refunded
   - invoice.payment_action_required
4. Copy the **webhook secret** (whsec_...)

### 4. Update Vercel Environment Variables
Go to Vercel Dashboard > ordrly project > Settings > Environment Variables

Replace these variables with LIVE values:
- STRIPE_SECRET_KEY
- NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
- STRIPE_WEBHOOK_SECRET
- STRIPE_PRICE_PRO
- STRIPE_PRICE_APPRAISER
- NEXT_PUBLIC_STRIPE_PRICE_PRO
- NEXT_PUBLIC_STRIPE_PRICE_APPRAISER

### 5. Deploy and Test
1. Deploy to Vercel
2. Test with a real credit card (use a small amount first)
3. Verify webhook events are received
4. Check subscription management works

## ⚠️ Important Notes

- **NEVER** commit live keys to git
- Test thoroughly with small amounts first
- Set up Stripe billing portal for subscription management
- Monitor webhook events in Stripe Dashboard
- Have a rollback plan ready

## 🧪 Testing Checklist

- [ ] Subscription signup works
- [ ] Payment processing works
- [ ] Webhooks are received
- [ ] Billing portal works
- [ ] Subscription cancellation works
- [ ] Failed payment handling works
