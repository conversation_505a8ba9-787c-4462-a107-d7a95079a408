# 🔄 Refund Processing Guide

## ⚠️ IMPORTANT: Never Delete Customers from Stripe

**❌ DON'T:** Delete customers from Stripe Dashboard
**✅ DO:** Follow the proper refund workflows below

Deleting customers breaks the connection between Ordrly and Stripe, causing billing errors.

## 🔧 Proper Refund Workflows

### **Scenario 1: Partial Refund (Keep Subscription Active)**

**When to use:** Customer had an issue but wants to keep their subscription

**Steps:**
1. **In Stripe Dashboard:**
   - Go to **Payments** → Find the payment
   - Click **"Refund"** → Enter partial amount
   - Add reason: `"Partial refund - keeping subscription active"`
   - Click **"Refund"**

2. **What happens:**
   - ✅ Customer gets partial refund
   - ✅ Subscription remains active
   - ✅ Customer keeps Pro access
   - ✅ Webhook logs the refund event

**Result:** Customer happy, subscription continues normally.

---

### **Scenario 2: Full Refund + Graceful Cancellation**

**When to use:** Customer wants full refund but you want to be generous with access

**Steps:**
1. **Cancel Subscription First:**
   - Go to **Subscriptions** → Find customer
   - Click **"Cancel subscription"**
   - Select **"Cancel at the end of the billing period"**
   - Add reason: `"Customer requested refund - graceful cancellation"`

2. **Process Refund:**
   - Go to **Payments** → Find the payment
   - Click **"Refund"** → Full amount
   - Add reason: `"Full refund - access until period end"`

3. **What happens:**
   - ✅ Customer gets full refund
   - ✅ Customer keeps access until period end
   - ✅ Subscription auto-downgrades to free after period
   - ✅ Webhooks handle both events properly

**Result:** Customer gets refund + keeps access until period end.

---

### **Scenario 3: Full Refund + Immediate Revocation**

**When to use:** Fraud, abuse, or immediate access revocation needed

**Steps:**
1. **Cancel Subscription Immediately:**
   - Go to **Subscriptions** → Find customer
   - Click **"Cancel subscription"**
   - Select **"Cancel immediately"**
   - Add reason: `"Immediate cancellation - refund requested"`

2. **Process Refund:**
   - Go to **Payments** → Find the payment
   - Click **"Refund"** → Full amount
   - Add reason: `"Full refund - immediate access revocation"`

3. **What happens:**
   - ✅ Customer gets full refund
   - ✅ Customer loses access immediately
   - ✅ Account downgrades to free tier
   - ✅ Webhooks handle both events properly

**Result:** Customer gets refund, loses access immediately.

---

## 🔍 Webhook Events Handled

Our system now properly handles these refund-related events:

- ✅ `charge.refunded` - Logs refund for audit trail
- ✅ `customer.subscription.deleted` - Handles access revocation
- ✅ `customer.subscription.updated` - Handles cancellation scheduling
- ✅ `invoice.payment_action_required` - Handles payment issues

## 🚨 What NOT to Do

### ❌ Never Delete Customers
```bash
# DON'T DO THIS:
Stripe Dashboard → Customers → [Customer] → Delete
```
**Why:** Breaks the connection between Ordrly and Stripe, causing billing errors.

### ❌ Never Refund Without Handling Subscription
```bash
# DON'T DO THIS:
1. Process refund in Stripe
2. Leave subscription active
3. Hope it works out
```
**Why:** Customer gets refund but keeps paid access indefinitely.

### ❌ Never Edit Database Directly
```bash
# DON'T DO THIS:
UPDATE profiles SET stripe_customer_id = NULL WHERE email = '<EMAIL>';
```
**Why:** Bypasses webhook system, creates data inconsistencies.

## ✅ Recovery from Broken Accounts

If you accidentally break an account (like we just fixed):

1. **Reset the account in database:**
   ```sql
   UPDATE profiles SET 
     stripe_customer_id = NULL,
     subscription_tier = 'free',
     subscription_status = 'active',
     subscription_period_end = NULL,
     cancel_at_period_end = false,
     pulls_this_month = 0,
     extra_credits = 0
   WHERE email = '<EMAIL>';
   ```

2. **Customer can sign up for Pro again normally**

## 📊 Monitoring Refunds

Check these places after processing refunds:

1. **Stripe Dashboard:**
   - Payments → Refunds tab
   - Webhooks → Event logs

2. **Ordrly Database:**
   - Check user's subscription status
   - Verify webhook events were processed

3. **User Account:**
   - Verify correct tier and access
   - Check for billing errors

## 🎯 Best Practices

1. **Always handle subscription first, then refund**
2. **Use descriptive reasons in Stripe**
3. **Monitor webhook logs for errors**
4. **Test the user's account after processing**
5. **Document the reason for future reference**

---

## 📞 Emergency Recovery

If something goes wrong:

1. **Check webhook logs** in Stripe Dashboard
2. **Check user's account page** for errors
3. **Reset account to free tier** using SQL above
4. **Contact user** to explain and offer re-signup

Remember: It's better to be conservative and give customers the benefit of the doubt than to break their accounts!

---

## 📋 Quick Reference Checklist

### Before Processing Any Refund:
- [ ] Identify the refund scenario (partial, graceful, immediate)
- [ ] Locate the customer in Stripe Dashboard
- [ ] Note the subscription ID and payment ID
- [ ] Decide on access timeline (keep until period end vs immediate revocation)

### For Partial Refunds:
- [ ] Go to Payments → Find payment → Refund (partial amount)
- [ ] Add descriptive reason
- [ ] Verify customer still has Pro access
- [ ] Check webhook logs for `charge.refunded` event

### For Full Refunds (Graceful):
- [ ] Go to Subscriptions → Cancel "at period end"
- [ ] Go to Payments → Full refund
- [ ] Verify customer keeps access until period end
- [ ] Check webhook logs for both events

### For Full Refunds (Immediate):
- [ ] Go to Subscriptions → Cancel "immediately"
- [ ] Go to Payments → Full refund
- [ ] Verify customer loses access immediately
- [ ] Check webhook logs for both events

### After Any Refund:
- [ ] Check customer's account page for errors
- [ ] Verify correct subscription tier
- [ ] Monitor webhook event processing
- [ ] Document the reason and outcome

---

## 🔗 Related Documentation

- `STRIPE_CUSTOMER_SUPPORT.md` - Customer support scenarios
- `WEBHOOK_TROUBLESHOOTING.md` - Webhook debugging guide
- `BILLING_ERROR_RECOVERY.md` - Fixing broken accounts
