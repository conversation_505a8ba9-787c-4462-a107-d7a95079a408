# 🔒 Security & Rate Limiting Checklist

## 📋 **IMMEDIATE SECURITY ACTIONS REQUIRED**

### 🚨 **CRITICAL - Must Fix Before Production**

#### **1. Remove Development Bypasses**
- [ ] **Remove business route bypass** in `src/lib/supabase/middleware.ts`
- [ ] **Remove enterprise route bypass** in `src/lib/supabase/middleware.ts`
- [ ] **Remove development auth bypasses** in all business/enterprise pages

```typescript
// REMOVE THESE LINES:
if (isDevelopment && isBusinessRoute) {
  console.log('🧪 Middleware: Bypassing auth for business route in development:', request.nextUrl.pathname)
  return NextResponse.next({ request })
}
```

#### **2. Disable Debug/Test Endpoints**
- [ ] **Block `/api/test-env`** - Exposes environment variables
- [ ] **Block `/api/debug-users`** - Exposes user data  
- [ ] **Block `/api/setup-epic9-schema`** - Database manipulation

```typescript
// ADD TO MIDDLEWARE:
const debugEndpoints = ['/api/test-env', '/api/debug-users', '/api/setup-epic9-schema']
if (process.env.NODE_ENV === 'production' && debugEndpoints.some(endpoint => 
  request.nextUrl.pathname.startsWith(endpoint))) {
  return NextResponse.json({ error: 'Not found' }, { status: 404 })
}
```

#### **3. Enforce Admin Role Checks**
- [ ] **Verify admin endpoints** require proper role validation
- [ ] **Test admin page access** without admin role
- [ ] **Validate API admin endpoints** return 403 for non-admins

---

## 📊 **CURRENT RATE LIMITS (Confirmed)**

### **Usage Limits:**
- **🚫 Unauthenticated**: 2 searches total (lifetime)
- **🆓 Free Users**: 5 searches per month  
- **⭐ Pro Users**: Unlimited searches
- **🏢 Appraiser**: Unlimited (tier disabled)

### **API Rate Limits:**
- **General API**: 100 requests per 15 minutes
- **Address Autocomplete**: 60 requests per minute
- **Compliance Summary**: 10 requests per minute

---

## 🛡️ **PAGES REQUIRING SECURITY REVIEW**

### **✅ Public Pages (Should remain open):**
- `/` - Homepage
- `/search` - Main search page
- `/pricing` - Pricing page
- `/about` - About page
- `/contact` - Contact page
- `/privacy` - Privacy policy
- `/terms` - Terms of service
- `/help` - Help center
- `/faq` - FAQ page
- `/tutorials` - Video tutorials
- `/auth/login` - Login page
- `/auth/signup` - Sign up page
- `/auth/forgot-password` - Password reset

### **🔒 Protected User Pages (Require authentication):**
- `/account` - User account settings
- `/billing` - Billing management
- `/dashboard` - User dashboard
- `/profile` - User profile
- `/preferences` - User preferences
- `/history` - Search history
- `/saved-searches` - Saved searches

### **🚨 Admin-Only Pages (Require admin role):**
- `/admin/*` - All admin pages
- `/admin/analytics` - Analytics dashboard
- `/admin/content` - Content management
- `/admin/faq` - FAQ management
- `/admin/leads` - Lead management
- `/admin/marketing` - Marketing dashboard

### **🏢 Business/Enterprise Pages (Need proper auth):**
- `/business/*` - All business pages (currently bypassed in dev)
- `/enterprise/*` - All enterprise pages (currently bypassed in dev)

### **🚫 Debug/Test Endpoints (MUST BE DISABLED):**
- `/api/test-env` - Environment variables
- `/api/debug-users` - User data
- `/api/setup-epic9-schema` - Database schema

### **🔐 Admin-Only API Endpoints:**
- `/api/ab-tests` - A/B test management
- `/api/analytics/marketing` - Marketing analytics
- `/api/analytics/campaigns` - Campaign analytics
- `/api/leads/capture` (GET) - Lead data access
- `/api/blog/posts` (admin features)

---

## 🧪 **TESTING COMMANDS**

### **Run All Rate Limiting Tests:**
```bash
npm run test:rate-limits
```

### **Run Security Endpoint Tests:**
```bash
npm run test:security
```

### **Individual Test Categories:**
```bash
# Test unauthenticated limits (2 searches)
npx playwright test testing/rate-limiting/comprehensive-rate-limit-tests.spec.js --grep="Unauthenticated User Limits"

# Test free user limits (5 searches/month)
npx playwright test testing/rate-limiting/comprehensive-rate-limit-tests.spec.js --grep="Free User Monthly Limits"

# Test pro user unlimited access
npx playwright test testing/rate-limiting/comprehensive-rate-limit-tests.spec.js --grep="Pro User Unlimited Access"

# Test API rate limiting
npx playwright test testing/rate-limiting/comprehensive-rate-limit-tests.spec.js --grep="API Rate Limiting"

# Test security endpoints
npx playwright test testing/security/endpoint-security-tests.spec.js
```

---

## ✅ **SECURITY VALIDATION CHECKLIST**

### **Before Production Deployment:**

#### **Authentication & Authorization:**
- [ ] All admin pages require admin role
- [ ] All business pages require proper authentication
- [ ] All enterprise pages require proper authentication
- [ ] Protected user pages redirect to login when unauthenticated
- [ ] API endpoints return 401 for unauthenticated requests
- [ ] API endpoints return 403 for insufficient permissions

#### **Rate Limiting:**
- [ ] Unauthenticated users limited to 2 searches
- [ ] Free users limited to 5 searches per month
- [ ] Pro users have unlimited access
- [ ] API rate limits are enforced
- [ ] Rate limit headers are returned
- [ ] Rate limits reset after time window

#### **Debug/Test Endpoints:**
- [ ] `/api/test-env` returns 404 in production
- [ ] `/api/debug-users` returns 404 in production
- [ ] `/api/setup-epic9-schema` returns 404 in production
- [ ] No development bypasses active in production

#### **Security Headers:**
- [ ] X-Frame-Options header present
- [ ] X-Content-Type-Options header present
- [ ] X-XSS-Protection header present
- [ ] Strict-Transport-Security header present (production)
- [ ] HTTPS enforcement active (production)

#### **Error Handling:**
- [ ] No sensitive data in error messages
- [ ] Proper error codes returned (401, 403, 404, 429)
- [ ] Rate limit errors include retry information
- [ ] Usage limit errors include upgrade prompts

---

## 🚀 **DEPLOYMENT CHECKLIST**

### **Pre-Production:**
1. [ ] Run full security test suite
2. [ ] Verify all rate limits working
3. [ ] Test admin role enforcement
4. [ ] Validate debug endpoints blocked
5. [ ] Check security headers
6. [ ] Test HTTPS enforcement

### **Post-Production:**
1. [ ] Monitor rate limiting effectiveness
2. [ ] Check for unauthorized access attempts
3. [ ] Validate security headers in production
4. [ ] Test admin access controls
5. [ ] Monitor API usage patterns

---

## 📞 **EMERGENCY CONTACTS**

If security issues are discovered:
1. **Immediately disable affected endpoints**
2. **Review access logs for unauthorized access**
3. **Update security measures**
4. **Re-run security test suite**
5. **Document incident and resolution**

---

## 📝 **TEST USER REQUIREMENTS**

For testing, ensure these users exist:
- **<EMAIL>** - Free tier user
- **<EMAIL>** - Pro tier user
- **<EMAIL>** - Admin role user

Each should have appropriate tier settings and known passwords for testing.
