# Stripe Billing Portal Setup

The billing management functionality on `/account` requires the Stripe billing portal to be configured in your Stripe Dashboard.

## Current Issue

❌ **Billing portal is not configured** - Users cannot manage their subscriptions

## Quick Fix

1. **Go to Stripe Dashboard**
   - Visit: https://dashboard.stripe.com/settings/billing/portal
   - Or navigate: Settings → Billing → Customer portal

2. **Activate Customer Portal**
   - Click "Activate customer portal" button
   - This enables the billing portal for your account

3. **Configure Portal Settings** (recommended)
   - **Business information**: Add your business name and support details
   - **Functionality**: Enable/disable features like:
     - Update payment method
     - Update billing address
     - View invoice history
     - Download invoices
     - Cancel subscription
     - Update subscription (upgrade/downgrade)
   - **Appearance**: Customize colors and branding to match your app

4. **Test the Integration**
   ```bash
   # Run the test script to verify configuration
   node test-billing-portal.js
   ```

## What This Enables

Once configured, users with paid subscriptions will see a "Manage Billing" button on their account page that allows them to:

- Update payment methods
- View billing history
- Download invoices
- Cancel or modify subscriptions
- Update billing address

## Error Handling

The app now includes improved error handling:

- **portal-not-configured**: Shows when billing portal isn't set up
- **no-customer**: User has subscription but no Stripe customer ID
- **customer-not-found**: Stripe customer ID is invalid
- **portal-failed**: General portal creation error

## Testing

After setup, test with a user who has a paid subscription:

1. Log in as a user with `subscription_tier` = 'pro' or 'appraiser'
2. Go to `/account` page
3. Click "Manage Billing" button
4. Should redirect to Stripe billing portal

## Support

If users encounter billing issues, they should contact support as the error messages suggest.
