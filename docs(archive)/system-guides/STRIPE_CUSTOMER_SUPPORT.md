# 🎧 Stripe Customer Support Scenarios

## 📞 Common Customer Issues & Solutions

### **1. "I can't access the billing portal"**

**Symptoms:**
- Error: "Your billing information could not be found"
- Error: "Unable to access billing portal"

**Diagnosis:**
```sql
-- Check customer's Stripe connection
SELECT email, stripe_customer_id, subscription_tier, subscription_status 
FROM profiles 
WHERE email = '<EMAIL>';
```

**Solutions:**
- **If `stripe_customer_id` is NULL:** Customer needs to upgrade to Pro first
- **If `stripe_customer_id` exists:** Check if customer was deleted from Stripe
- **If customer deleted:** Reset account using recovery process

---

### **2. "I was charged but don't have Pro access"**

**Symptoms:**
- Payment succeeded in Stripe
- User still shows "Free" tier in Ordrly

**Diagnosis:**
1. **Check Stripe Dashboard:**
   - Payments → Find successful payment
   - Note the customer ID and subscription ID

2. **Check webhook logs:**
   - Webhooks → Events → Look for `checkout.session.completed`
   - Check if webhook failed or wasn't processed

3. **Check database:**
   ```sql
   SELECT subscription_tier, subscription_status, stripe_customer_id, updated_at
   FROM profiles 
   WHERE email = '<EMAIL>';
   ```

**Solutions:**
- **If webhook failed:** Manually trigger subscription update
- **If no webhook received:** Check webhook endpoint configuration
- **If database not updated:** Run manual update query

---

### **3. "I cancelled but I'm still being charged"**

**Symptoms:**
- Customer thinks they cancelled
- New charge appeared

**Diagnosis:**
1. **Check Stripe subscription:**
   - Subscriptions → Find customer
   - Check `cancel_at_period_end` status
   - Check actual cancellation date

2. **Check Ordrly database:**
   ```sql
   SELECT subscription_status, cancel_at_period_end, subscription_period_end
   FROM profiles 
   WHERE email = '<EMAIL>';
   ```

**Solutions:**
- **If not actually cancelled:** Help customer cancel properly
- **If cancelled but charged:** Process refund for erroneous charge
- **If timing confusion:** Explain billing cycle and period end

---

### **4. "My card was declined but I have money"**

**Symptoms:**
- Payment failed in Stripe
- Customer insists card is valid

**Diagnosis:**
1. **Check Stripe payment attempt:**
   - Payments → Failed payments
   - Check decline reason code

2. **Common decline reasons:**
   - `insufficient_funds` - Not enough money
   - `card_declined` - Bank declined
   - `expired_card` - Card expired
   - `incorrect_cvc` - Wrong security code

**Solutions:**
- **Bank decline:** Customer needs to contact their bank
- **Expired card:** Customer needs to update payment method
- **Technical issue:** Try different payment method
- **International card:** May need to enable international payments

---

### **5. "I want to change my plan"**

**Current limitation:** Ordrly only has Free and Pro tiers

**Solutions:**
- **Free to Pro:** Direct to upgrade page
- **Pro to Free:** Cancel subscription (keeps access until period end)
- **Pro to different Pro:** Not applicable (only one Pro tier)

---

## 🔧 Manual Account Fixes

### **Reset Broken Account to Free Tier:**
```sql
UPDATE profiles SET 
  stripe_customer_id = NULL,
  subscription_tier = 'free',
  subscription_status = 'active',
  subscription_period_end = NULL,
  cancel_at_period_end = false,
  pulls_this_month = 0,
  extra_credits = 0
WHERE email = '<EMAIL>';
```

### **Manually Upgrade Account to Pro:**
```sql
-- Only use if webhook failed and payment succeeded
UPDATE profiles SET 
  subscription_tier = 'pro',
  subscription_status = 'active',
  is_subscribed = true,
  updated_at = NOW()
WHERE email = '<EMAIL>';
```

### **Force Subscription Sync:**
```sql
-- Check current Stripe status and update accordingly
-- Use this when database is out of sync with Stripe
```

---

## 📊 Diagnostic Queries

### **Check Customer Status:**
```sql
SELECT 
  email,
  subscription_tier,
  subscription_status,
  stripe_customer_id,
  subscription_period_end,
  cancel_at_period_end,
  pulls_this_month,
  extra_credits,
  created_at,
  updated_at
FROM profiles 
WHERE email = '<EMAIL>';
```

### **Find Customers with Issues:**
```sql
-- Customers with Stripe ID but Free tier (potential sync issue)
SELECT email, stripe_customer_id, subscription_tier, subscription_status
FROM profiles 
WHERE stripe_customer_id IS NOT NULL 
AND subscription_tier = 'free';
```

### **Recent Subscription Changes:**
```sql
SELECT email, subscription_tier, subscription_status, updated_at
FROM profiles 
WHERE updated_at > NOW() - INTERVAL '24 hours'
ORDER BY updated_at DESC;
```

---

## 🚨 Escalation Guidelines

### **When to Process Refunds:**
- Payment succeeded but no Pro access (our fault)
- Billing portal errors preventing cancellation
- Double charges due to system issues
- Customer charged after proper cancellation

### **When to Deny Refunds:**
- Customer used service for full billing period
- Customer cancelled properly and got expected access
- Customer wants refund for buyer's remorse after using service

### **When to Contact Stripe Support:**
- Webhook endpoints not receiving events
- Billing portal not working for multiple customers
- Unusual payment failures across multiple customers
- Suspected fraud or security issues

---

## 📋 Customer Communication Templates

### **Billing Portal Issue:**
```
Hi [Name],

I see you're having trouble accessing the billing portal. This appears to be a temporary sync issue between our systems.

I've reset your account and you should now be able to access billing management at: https://www.ordrly.ai/account

If you continue to have issues, please let me know and I'll assist you directly.

Best regards,
[Your name]
```

### **Payment Succeeded, No Pro Access:**
```
Hi [Name],

I see your payment went through successfully, but there was a delay in activating your Pro features. I've manually activated your Pro subscription and you should now have full access.

Your Pro features include:
- Unlimited searches
- AI-powered insights
- Red flag detection

Thank you for your patience, and welcome to Ordrly Pro!

Best regards,
[Your name]
```

### **Refund Processed:**
```
Hi [Name],

I've processed your refund of $[amount] for your Ordrly Pro subscription. You should see the refund on your statement within 5-10 business days.

[If graceful cancellation:]
You'll continue to have Pro access until [date], after which your account will return to the free tier.

[If immediate cancellation:]
Your account has been returned to the free tier with 5 searches per month.

Thank you for trying Ordrly Pro. If you have any feedback on how we can improve, I'd love to hear it.

Best regards,
[Your name]
```
