# 🚨 Stripe Emergency Reference Card

## 🔥 EMERGENCY: Customer Can't Access Account

### **Quick Fix (90% of cases):**
```sql
-- Reset broken account to free tier
UPDATE profiles SET 
  stripe_customer_id = NULL,
  subscription_tier = 'free',
  subscription_status = 'active',
  subscription_period_end = NULL,
  cancel_at_period_end = false,
  pulls_this_month = 0,
  extra_credits = 0
WHERE email = 'CUSTOMER_EMAIL_HERE';
```

### **Tell Customer:**
"I've reset your account. You can now access it normally and upgrade to Pro again if needed."

---

## 💳 EMERGENCY: Process Refund Without Breaking Account

### **Graceful Refund (Recommended):**
1. **Stripe Dashboard** → **Subscriptions** → Cancel "at period end"
2. **Stripe Dashboard** → **Payments** → Full refund
3. **Tell Customer:** "Refunded. You keep Pro access until [period end date]."

### **Immediate Refund:**
1. **Stripe Dashboard** → **Subscriptions** → Cancel "immediately"  
2. **Stripe Dashboard** → **Payments** → Full refund
3. **Tell Customer:** "Refunded. Account returned to free tier."

### **❌ NEVER DO:**
- Delete customer from Stripe Dashboard
- Edit database without handling subscription first

---

## 🔧 EMERGENCY: Webhook Down

### **Quick Check:**
1. **Stripe Dashboard** → **Webhooks** → Check status (green/yellow/red)
2. **Vercel Dashboard** → **Functions** → Check for errors

### **Quick Fix:**
1. **Get webhook secret:** Stripe Dashboard → Webhooks → "Reveal"
2. **Update Vercel:** `STRIPE_WEBHOOK_SECRET=whsec_...`
3. **Redeploy:** Push any change to GitHub

### **If Still Broken:**
- Disable webhook in Stripe temporarily
- Fix underlying issue
- Re-enable webhook
- Manually sync missed events

---

## 📊 EMERGENCY: Check Customer Status

### **Quick Diagnosis:**
```sql
SELECT 
  email,
  subscription_tier,
  subscription_status,
  stripe_customer_id,
  subscription_period_end,
  cancel_at_period_end,
  updated_at
FROM profiles 
WHERE email = 'CUSTOMER_EMAIL_HERE';
```

### **Common Issues:**
- **`stripe_customer_id` is NULL + Pro tier** → Reset to free
- **`stripe_customer_id` exists + Free tier** → Check if subscription cancelled
- **Very old `updated_at` + Pro tier** → Webhook missed, check Stripe

---

## 🚑 EMERGENCY: Multiple Customers Affected

### **Stop the Bleeding:**
1. **Disable webhook:** Stripe Dashboard → Webhooks → Disable
2. **Identify scope:** How many customers affected?
3. **Triage:** Critical (paying customers) vs non-critical

### **Quick Batch Fix:**
```sql
-- Reset all accounts with deleted Stripe customers
UPDATE profiles SET 
  stripe_customer_id = NULL,
  subscription_tier = 'free',
  subscription_status = 'active',
  subscription_period_end = NULL,
  cancel_at_period_end = false
WHERE stripe_customer_id IN (
  'cus_deleted_1',
  'cus_deleted_2'
  -- Add all deleted customer IDs
);
```

### **Communication:**
- Email affected customers immediately
- Explain the issue briefly
- Offer free Pro access as compensation if needed

---

## 📞 EMERGENCY: Contact Information

### **Stripe Support:**
- **Dashboard:** Stripe Dashboard → Help → Contact Support
- **Phone:** Available for paid accounts
- **Priority:** Mark as "Production Issue" if revenue-affecting

### **Vercel Support:**
- **Dashboard:** Vercel Dashboard → Help
- **Discord:** Vercel community for quick questions
- **Priority:** Mark as "Production Down" if site affected

### **Internal Escalation:**
- **Revenue Impact:** Immediate escalation
- **Multiple Customers:** Immediate escalation  
- **Security Concerns:** Immediate escalation
- **Data Loss:** Immediate escalation

---

## 🔍 EMERGENCY: Quick Diagnostics

### **Is Webhook Working?**
```bash
curl -X POST https://www.ordrly.ai/api/webhooks/stripe
# Expected: 400 (missing signature)
# Bad: 404 (endpoint down)
```

### **Is Customer in Stripe?**
- Stripe Dashboard → Customers → Search by email
- If not found: Customer was deleted

### **Is Database Corrupted?**
```sql
-- Find impossible states
SELECT COUNT(*) FROM profiles 
WHERE subscription_tier = 'pro' AND stripe_customer_id IS NULL;
-- Should be 0
```

### **Are Webhooks Recent?**
- Stripe Dashboard → Webhooks → Recent events
- Check timestamps and success rates

---

## 📋 EMERGENCY: Decision Tree

### **Customer Reports Billing Error:**
1. **Check database** → If `stripe_customer_id` is NULL → Reset to free
2. **Check Stripe** → If customer deleted → Reset to free  
3. **Check webhooks** → If failing → Fix webhook, then sync
4. **Still broken?** → Manual investigation needed

### **Customer Paid But No Pro Access:**
1. **Check Stripe payment** → If succeeded → Check webhook logs
2. **Webhook failed?** → Fix webhook, manually upgrade customer
3. **Webhook succeeded?** → Check database update, manual fix
4. **Payment failed?** → Help customer with payment method

### **Customer Wants Refund:**
1. **Partial refund?** → Just refund in Stripe
2. **Full refund + keep access?** → Cancel at period end, then refund
3. **Full refund + immediate revocation?** → Cancel immediately, then refund
4. **Never delete customer from Stripe**

---

## ⚡ EMERGENCY: One-Liners

### **Reset Broken Account:**
```sql
UPDATE profiles SET stripe_customer_id=NULL, subscription_tier='free', subscription_status='active', subscription_period_end=NULL, cancel_at_period_end=false WHERE email='EMAIL';
```

### **Manually Upgrade Customer:**
```sql
UPDATE profiles SET subscription_tier='pro', subscription_status='active', is_subscribed=true, updated_at=NOW() WHERE email='EMAIL';
```

### **Find All Broken Accounts:**
```sql
SELECT email FROM profiles WHERE subscription_tier='pro' AND stripe_customer_id IS NULL;
```

### **Check Webhook Health:**
- Stripe Dashboard → Webhooks → Success rate should be >95%

---

## 🎯 EMERGENCY: Success Criteria

### **Customer Issue Resolved When:**
- ✅ Customer can access their account without errors
- ✅ Billing portal works (if Pro customer)
- ✅ Subscription tier matches what customer paid for
- ✅ No error messages on account page

### **System Health Restored When:**
- ✅ Webhook success rate >95%
- ✅ No billing errors in past hour
- ✅ Database sync issues <5 per day
- ✅ All recent payments properly processed

### **Prevention Measures:**
- ✅ Monitor webhook health daily
- ✅ Never delete customers from Stripe
- ✅ Always handle subscription before refund
- ✅ Test changes in staging first

---

## 📚 Full Documentation Links

- **Detailed Refund Guide:** `REFUND_PROCESSING_GUIDE.md`
- **Customer Support Scenarios:** `STRIPE_CUSTOMER_SUPPORT.md`
- **Webhook Troubleshooting:** `WEBHOOK_TROUBLESHOOTING.md`
- **Billing Error Recovery:** `BILLING_ERROR_RECOVERY.md`

**Remember:** When in doubt, reset to free tier. Customer can always upgrade again!
