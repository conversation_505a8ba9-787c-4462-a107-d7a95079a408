# Stripe Billing Portal Branding Configuration

This guide will help you configure the Stripe billing portal to match Ordrly's branding and provide a better user experience.

## 🎯 Quick Setup

1. **Go to Stripe Dashboard** → Settings → Billing → Customer Portal
2. **Follow the configuration sections below**

## 🏢 Business Information

Configure these settings in the "Business Information" section:

```
Business Name: Ordrly
Support Email: <EMAIL>
Support Phone: (Optional - leave blank for now)
Support URL: https://ordrly.ai/contact
Privacy Policy URL: https://ordrly.ai/privacy
Terms of Service URL: https://ordrly.ai/tos
```

## 🎨 Branding & Appearance

### Colors
Use these Ordrly brand colors:

```
Primary Color: #1DA1F2 (Ordrly Blue)
Background Color: #FFFFFF (White)
Text Color: #1F2937 (Dark Gray)
Link Color: #1DA1F2 (Ordrly Blue)
```

### Logo
- Upload the Ordrly logo (SVG or PNG format recommended)
- Recommended size: 200x50px or similar aspect ratio
- Ensure logo works on both light and dark backgrounds

### Favicon
- Upload Ordrly favicon (32x32px PNG or ICO)

## ⚙️ Functionality Settings

Enable these features for the best user experience:

### ✅ Enabled Features
- **Update payment method** ✓
- **Update billing address** ✓
- **View invoice history** ✓
- **Download invoices** ✓
- **Cancel subscription** ✓
- **Update subscription** ✓ (for plan changes)

### ❌ Disabled Features
- **Pause subscription** ❌ (not needed for Ordrly)
- **Update tax ID** ❌ (not needed for most users)

## 🔗 Custom Domain (Optional - Paid Feature)

If you have a paid Stripe plan, you can set up a custom domain:

```
Custom Domain: billing.ordrly.ai
```

This will make the portal URL look like `https://billing.ordrly.ai/...` instead of `https://billing.stripe.com/...`

## 📧 Email Customization

Configure email templates to match Ordrly branding:

### Email Settings
```
From Name: Ordrly
From Email: <EMAIL> (or <EMAIL>)
```

### Email Templates
Customize these email templates with Ordrly branding:
- Invoice payment succeeded
- Invoice payment failed
- Subscription canceled
- Subscription updated

## 🛡️ Security & Compliance

### Session Settings
```
Session Duration: 24 hours (default)
Return URL: https://ordrly.ai/account
```

### Allowed Features
Only enable features that Ordrly users need:
- Payment method updates
- Billing address updates
- Subscription cancellation
- Invoice downloads

## 🧪 Testing the Configuration

After configuring the portal:

1. **Test with your account**:
   - Go to `/account` page
   - Click "Manage Billing"
   - Verify branding looks correct
   - Test all enabled features

2. **Check mobile responsiveness**:
   - Test on mobile devices
   - Ensure logo and colors display correctly

3. **Verify email templates**:
   - Trigger test emails
   - Check branding consistency

## 🚨 Current Issues to Address

### Multiple Subscriptions Problem
Your account currently has 3 active subscriptions. To clean this up:

1. **Use the cleanup utility**:
   - Go to `/admin/subscriptions`
   - Enter your customer ID
   - Click "Cancel Duplicates"

2. **Manual cleanup in Stripe Dashboard**:
   - Go to Customers → Find your customer
   - View subscriptions
   - Cancel the older/duplicate subscriptions
   - Keep only the most recent one

### Customer ID Lookup
To find your customer ID:
1. Go to Stripe Dashboard → Customers
2. Search by your email: `<EMAIL>`
3. Copy the customer ID (starts with `cus_`)

## 📋 Configuration Checklist

- [ ] Business information updated
- [ ] Ordrly colors configured
- [ ] Logo uploaded
- [ ] Favicon uploaded
- [ ] Functionality settings configured
- [ ] Email templates customized
- [ ] Portal tested with real account
- [ ] Mobile responsiveness verified
- [ ] Duplicate subscriptions cleaned up

## 🔧 Advanced Configuration

### Webhook Configuration
Ensure these webhooks are configured for proper subscription management:
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

### Portal Session Configuration
The portal session creation is already properly configured in the code with:
- Correct return URL
- Customer validation
- Error handling

## 📞 Support

If you need help with any of these configurations:
1. Check Stripe's documentation
2. Contact Stripe support
3. Test thoroughly before going live

Remember: These changes will affect all customers using the billing portal, so test everything carefully!
