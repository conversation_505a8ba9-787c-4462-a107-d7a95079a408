# Vercel Production Environment Checklist

## 🔐 **Critical Security Environment Variables**

### **Supabase Configuration**
```bash
# Production Supabase (Custom Domain)
NEXT_PUBLIC_SUPABASE_URL=https://api.ordrly.ai
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_key
```

### **Stripe Configuration (PRODUCTION KEYS ONLY)**
```bash
# ⚠️ CRITICAL: Must use sk_live_ and pk_live_ keys
STRIPE_SECRET_KEY=sk_live_your_production_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_production_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_production_webhook_secret

# Production Price IDs
STRIPE_PRICE_PRO=price_your_production_pro_price_id
STRIPE_PRICE_APPRAISER=price_your_production_appraiser_price_id
NEXT_PUBLIC_STRIPE_PRICE_PRO=price_your_production_pro_price_id
NEXT_PUBLIC_STRIPE_PRICE_APPRAISER=price_your_production_appraiser_price_id
```

### **Application Settings**
```bash
NEXT_PUBLIC_BASE_URL=https://www.ordrly.ai
NEXT_PUBLIC_SITE_URL=https://www.ordrly.ai
NODE_ENV=production
```

### **API Keys**
```bash
OPENAI_API_KEY=your_production_openai_key
BRAVE_SEARCH_API_KEY=your_production_brave_key
PERPLEXITY_API_KEY=your_production_perplexity_key
GOOGLE_SEARCH_API_KEY=your_production_google_search_key
GOOGLE_SEARCH_ENGINE_ID=your_production_search_engine_id
GOOGLE_GEOCODING_API_KEY=your_production_geocoding_key
GEOAPIFY_API_KEY=your_production_geoapify_key
RESEND_API_KEY=your_production_resend_key
```

### **Feature Flags**
```bash
NEXT_PUBLIC_EPIC6_ENABLED=true
NEXT_PUBLIC_CHAT_ENABLED=true
NEXT_PUBLIC_RED_FLAGS_ENABLED=true
NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED=true
NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED=true
```

## 🚀 **Deployment Steps**

### **1. Verify Environment Variables**
```bash
# Run verification script
npm run verify:vercel

# Or check in production
npm run verify:production
```

### **2. Stripe Configuration**
- [ ] Switch Stripe Dashboard to Live Mode
- [ ] Update webhook endpoints to point to production URLs
- [ ] Test payment flows in live mode
- [ ] Verify billing portal branding is configured

### **3. Supabase Configuration**
- [ ] Verify custom domain (api.ordrly.ai) is working
- [ ] Check OAuth providers are configured for production URLs
- [ ] Verify RLS policies are enabled
- [ ] Test authentication flows

### **4. Security Verification**
- [ ] All environment variables use production values
- [ ] No test keys in production
- [ ] Security headers are configured
- [ ] Debug endpoints are disabled

### **5. Final Testing**
- [ ] Test complete user signup flow
- [ ] Test payment processing
- [ ] Test subscription management
- [ ] Test all critical user journeys

## 🔍 **Verification Commands**

### **Check Environment Variables**
```bash
# Verify all required variables are set
npm run verify:vercel

# Check for test keys in production
npm run verify:production
```

### **Test Production Endpoints**
```bash
# Test security headers
curl -I https://www.ordrly.ai

# Verify debug endpoints are disabled
curl https://www.ordrly.ai/api/test-env

# Test health endpoint
curl https://www.ordrly.ai/api/health
```

## 🚨 **Security Warnings**

### **Never Do This:**
- ❌ Use test Stripe keys in production
- ❌ Commit secrets to git
- ❌ Leave debug endpoints enabled
- ❌ Use localhost URLs in production

### **Always Do This:**
- ✅ Use live Stripe keys for production
- ✅ Store all secrets in Vercel environment variables
- ✅ Test payment flows before going live
- ✅ Monitor for security events

## 📊 **Monitoring Setup**

### **Vercel Analytics**
- [ ] Enable Vercel Analytics
- [ ] Set up error monitoring
- [ ] Configure performance monitoring

### **Stripe Monitoring**
- [ ] Set up webhook monitoring
- [ ] Configure payment failure alerts
- [ ] Monitor subscription metrics

### **Supabase Monitoring**
- [ ] Monitor database performance
- [ ] Set up auth failure alerts
- [ ] Track API usage

## 🔄 **Post-Deployment Verification**

### **Immediate Checks (within 1 hour)**
- [ ] User signup works
- [ ] Payment processing works
- [ ] Email notifications work
- [ ] All pages load correctly

### **24-Hour Checks**
- [ ] No critical errors in logs
- [ ] Performance metrics are good
- [ ] Security monitoring is working
- [ ] All integrations are functioning

### **Weekly Checks**
- [ ] Review security logs
- [ ] Check for failed payments
- [ ] Monitor user feedback
- [ ] Update dependencies if needed

## 📞 **Emergency Contacts**

### **If Something Goes Wrong:**
1. **Vercel Issues**: Check Vercel dashboard and logs
2. **Stripe Issues**: Check Stripe dashboard and webhook logs
3. **Supabase Issues**: Check Supabase dashboard and logs
4. **DNS Issues**: Check domain registrar and Vercel DNS settings

### **Rollback Plan:**
1. Revert to previous Vercel deployment
2. Check environment variables haven't changed
3. Verify all external services are working
4. Test critical user flows

---

**Remember**: Production deployment is irreversible. Test everything thoroughly before going live!
