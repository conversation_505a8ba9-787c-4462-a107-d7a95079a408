# 🔧 Webhook Troubleshooting Guide

## 🎯 Quick Webhook Health Check

### **1. Check Webhook Status in Stripe**
1. Go to **Stripe Dashboard** → **Webhooks**
2. Find your endpoint: `https://www.ordrly.ai/api/webhooks/stripe`
3. Check status indicators:
   - ✅ **Green dot:** Webhook is healthy
   - ⚠️ **Yellow dot:** Some failures
   - ❌ **Red dot:** Webhook is failing

### **2. Required Webhook Events**
Ensure these events are selected:
- ✅ `checkout.session.completed`
- ✅ `customer.subscription.created`
- ✅ `customer.subscription.updated`
- ✅ `customer.subscription.deleted`
- ✅ `invoice.payment_succeeded`
- ✅ `invoice.payment_failed`
- ✅ `charge.refunded`
- ✅ `invoice.payment_action_required`

---

## 🚨 Common Webhook Issues

### **Issue 1: Webhook Signature Verification Failed**

**Symptoms:**
- Webhook events show "400 Bad Request"
- Error: "Invalid signature"

**Causes:**
- Wrong webhook secret in environment variables
- Webhook secret not updated after regeneration

**Fix:**
1. **Get correct webhook secret:**
   - Stripe Dashboard → Webhooks → Your endpoint → "Reveal" secret
2. **Update Vercel environment variable:**
   - `STRIPE_WEBHOOK_SECRET=whsec_your_secret_here`
3. **Redeploy application**

---

### **Issue 2: Webhook Endpoint Not Found (404)**

**Symptoms:**
- Webhook events show "404 Not Found"
- Error: "Endpoint not found"

**Causes:**
- Wrong webhook URL configured
- Application not deployed
- Route handler not working

**Fix:**
1. **Verify webhook URL:** `https://www.ordrly.ai/api/webhooks/stripe`
2. **Test endpoint manually:**
   ```bash
   curl -X POST https://www.ordrly.ai/api/webhooks/stripe
   # Should return 400 (missing signature), not 404
   ```
3. **Check deployment status in Vercel**

---

### **Issue 3: Webhook Timeout (500/timeout)**

**Symptoms:**
- Webhook events show "500 Internal Server Error" or timeout
- Events are retried multiple times

**Causes:**
- Database connection issues
- Slow external API calls
- Infinite loops in webhook code

**Fix:**
1. **Check webhook logs in Vercel:**
   - Vercel Dashboard → Functions → View logs
2. **Check database connectivity**
3. **Add timeout handling to webhook code**

---

### **Issue 4: Database Update Failures**

**Symptoms:**
- Webhook returns 200 but user data not updated
- Stripe shows successful webhook but Ordrly data unchanged

**Causes:**
- Database connection issues
- Invalid user ID in webhook data
- Database constraint violations

**Diagnosis:**
```sql
-- Check recent webhook processing
SELECT * FROM profiles 
WHERE updated_at > NOW() - INTERVAL '1 hour'
ORDER BY updated_at DESC;

-- Check for users with Stripe data but wrong tier
SELECT email, stripe_customer_id, subscription_tier, subscription_status
FROM profiles 
WHERE stripe_customer_id IS NOT NULL 
AND subscription_tier = 'free';
```

---

## 🔍 Debugging Webhook Events

### **Step 1: Find the Event in Stripe**
1. **Stripe Dashboard** → **Webhooks** → **Your endpoint**
2. Click on a recent event
3. Note the **Event ID** and **Status**

### **Step 2: Check Event Details**
Look for these key fields:
- **Event Type:** (e.g., `checkout.session.completed`)
- **HTTP Status:** Should be `200`
- **Response Body:** Should be `{"received": true}`
- **Retry Count:** Should be `0` for successful events

### **Step 3: Check Application Logs**
1. **Vercel Dashboard** → **Functions** → **View Function Logs**
2. Search for the Event ID
3. Look for error messages or stack traces

### **Step 4: Verify Database Changes**
```sql
-- Check if user was updated around the webhook time
SELECT email, subscription_tier, subscription_status, updated_at
FROM profiles 
WHERE updated_at BETWEEN '[webhook_time - 5min]' AND '[webhook_time + 5min]';
```

---

## 🛠️ Manual Webhook Testing

### **Test Webhook Endpoint Locally:**
```bash
# Test that the endpoint exists and handles missing signature
curl -X POST https://www.ordrly.ai/api/webhooks/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Expected response: 400 Bad Request (missing signature)
# NOT expected: 404 Not Found
```

### **Trigger Test Events in Stripe:**
1. **Stripe Dashboard** → **Webhooks** → **Your endpoint**
2. Click **"Send test webhook"**
3. Select event type (e.g., `customer.subscription.updated`)
4. Click **"Send test webhook"**
5. Check the response and logs

---

## 🔄 Webhook Recovery Procedures

### **Scenario 1: Missed Checkout Event**
**Problem:** Customer paid but didn't get Pro access

**Recovery:**
1. **Find the payment in Stripe:**
   - Payments → Search by customer email
2. **Get session details:**
   - Note customer ID, subscription ID, metadata
3. **Manually update database:**
   ```sql
   UPDATE profiles SET 
     subscription_tier = 'pro',
     subscription_status = 'active',
     stripe_customer_id = 'cus_XXXXXXXXXX',
     is_subscribed = true,
     updated_at = NOW()
   WHERE email = '<EMAIL>';
   ```

### **Scenario 2: Missed Cancellation Event**
**Problem:** Customer cancelled but still shows as Pro

**Recovery:**
1. **Check Stripe subscription status:**
   - Subscriptions → Find customer → Check status
2. **Update database to match:**
   ```sql
   UPDATE profiles SET 
     subscription_tier = 'free',
     subscription_status = 'canceled',
     cancel_at_period_end = false,
     subscription_period_end = NULL,
     updated_at = NOW()
   WHERE email = '<EMAIL>';
   ```

### **Scenario 3: Multiple Failed Events**
**Problem:** Webhook has been failing for hours/days

**Recovery:**
1. **Fix the underlying issue** (signature, endpoint, etc.)
2. **Get list of missed events:**
   - Stripe Dashboard → Webhooks → Failed events
3. **For each critical missed event:**
   - Note the customer and event type
   - Manually sync the data using SQL queries above

---

## 📊 Webhook Monitoring

### **Daily Health Checks:**
- [ ] Check webhook success rate in Stripe Dashboard
- [ ] Verify recent subscription changes are reflected in database
- [ ] Check for any 4xx/5xx errors in webhook logs

### **Weekly Reviews:**
- [ ] Review failed webhook events and patterns
- [ ] Check for customers with sync issues
- [ ] Update webhook event handling if needed

### **Monthly Audits:**
- [ ] Compare Stripe subscription data with database
- [ ] Identify and fix any data inconsistencies
- [ ] Review webhook performance and optimization

---

## 🚨 Emergency Webhook Fixes

### **If Webhooks Are Completely Down:**
1. **Disable webhook temporarily** in Stripe to stop retries
2. **Fix the underlying issue** (deployment, secrets, etc.)
3. **Re-enable webhook**
4. **Manually sync critical missed events**

### **If Database Is Corrupted:**
1. **Stop webhook processing** temporarily
2. **Backup current database state**
3. **Run data recovery queries**
4. **Test with a few customers**
5. **Re-enable webhook processing**

### **If Stripe Account Issues:**
1. **Contact Stripe support** immediately
2. **Document the issue** and timeline
3. **Implement temporary manual processing**
4. **Communicate with affected customers**

---

## 📞 When to Contact Support

### **Contact Stripe Support When:**
- Webhook endpoint receives no events for >1 hour
- Multiple customers report billing issues simultaneously
- Webhook signature verification fails after confirming correct secret
- Suspected security or fraud issues

### **Contact Vercel Support When:**
- Function timeouts despite optimized code
- Deployment issues affecting webhook endpoint
- Environment variable issues
- Regional connectivity problems

### **Internal Escalation When:**
- Data corruption affecting multiple customers
- Webhook issues causing revenue loss
- Security concerns with webhook handling
- Need for emergency manual processing
