# Epic 10: Audit Trail & Document Source Logging - Testing Guide

## Overview
This guide provides comprehensive testing procedures for Epic 10 features including chat export, admin conversation review, feedback mechanisms, and analytics dashboard.

## Prerequisites
- Admin user account with `role = 'admin'` in profiles table
- At least one chat conversation with messages
- Test user account for feedback testing

## Test Scenarios

### 1. Chat Export Functionality (CHUI-Audit-2)

#### Test 1.1: Export Button Visibility
**Steps:**
1. Navigate to `/chat`
2. Open an existing conversation with messages
3. Verify export button appears in chat header
4. Verify button is disabled when no messages exist

**Expected Results:**
- Export button visible in chat header
- But<PERSON> disabled for empty conversations
- <PERSON><PERSON> enabled for conversations with messages

#### Test 1.2: PDF Export Generation
**Steps:**
1. Open a conversation with multiple messages
2. Click "Export Chat" button
3. Wait for download to complete
4. Open downloaded PDF file

**Expected Results:**
- PDF downloads successfully
- Filename format: `chat-export-{conversationId}.pdf`
- PDF contains conversation metadata (address, jurisdiction, date)
- All messages displayed in correct order
- User and AI messages clearly differentiated
- Citations included as footnotes
- Compliance disclaimer present

#### Test 1.3: Export Access Control
**Steps:**
1. As regular user, try to access `/api/chat/export/{otherUserConversationId}`
2. As admin, try to export any conversation
3. Verify admin access is logged

**Expected Results:**
- Regular users can only export their own conversations
- Admins can export any conversation
- Admin access logged in `admin_access_log` table

### 2. Admin Conversation Review (CHUI-Audit-3)

#### Test 2.1: Admin Access Control
**Steps:**
1. As regular user, navigate to `/admin/chat/conversations`
2. As admin user, navigate to `/admin/chat/conversations`
3. Verify access restrictions

**Expected Results:**
- Regular users redirected with access denied message
- Admin users can access conversation list
- Proper authentication checks in place

#### Test 2.2: Conversation List Display
**Steps:**
1. As admin, navigate to `/admin/chat/conversations`
2. Verify conversation list displays correctly
3. Test search functionality
4. Test export buttons

**Expected Results:**
- All conversations displayed with metadata
- Search works for address, jurisdiction, user email
- Export buttons functional
- Summary statistics accurate

#### Test 2.3: Individual Conversation View
**Steps:**
1. Click "View" on a conversation
2. Verify conversation details display
3. Test export functionality
4. Check admin access logging

**Expected Results:**
- Conversation metadata displayed correctly
- All messages shown in chronological order
- Citations and metadata visible
- Admin access logged with 'view' type

#### Test 2.4: Admin Access Logging
**Steps:**
1. Perform various admin actions (view, export)
2. Check `admin_access_log` table
3. Verify log entries

**Expected Results:**
- All admin actions logged
- Correct admin_user_id, conversation_id, access_type
- Timestamps accurate
- IP address and user agent captured

### 3. Feedback Mechanism (CHUI-Audit-4)

#### Test 3.1: Feedback UI Display
**Steps:**
1. Open chat conversation
2. Send message and receive AI response
3. Verify feedback buttons appear
4. Test button interactions

**Expected Results:**
- Thumbs up/down buttons visible on AI messages only
- No feedback buttons on user messages
- Buttons responsive to clicks
- Visual feedback for selected state

#### Test 3.2: Positive Feedback Submission
**Steps:**
1. Click thumbs up on AI message
2. Verify feedback stored in database
3. Check visual feedback state
4. Try clicking again to remove feedback

**Expected Results:**
- Feedback stored with type 'thumbs_up'
- Button shows selected state
- Clicking again removes feedback
- Database updated correctly

#### Test 3.3: Negative Feedback with Text
**Steps:**
1. Click thumbs down on AI message
2. Enter feedback text in popup
3. Submit feedback
4. Verify storage and display

**Expected Results:**
- Popup appears for text input
- Feedback stored with type 'thumbs_down'
- Text feedback saved
- Character limit enforced (500 chars)
- Visual feedback state updated

#### Test 3.4: Feedback Access Control
**Steps:**
1. Try to submit feedback on another user's conversation
2. Verify only conversation owners can provide feedback
3. Test admin feedback capabilities

**Expected Results:**
- Users can only feedback on their own conversations
- Proper access control enforced
- Admins can provide feedback on any conversation

### 4. Admin Analytics Dashboard

#### Test 4.1: Analytics Access Control
**Steps:**
1. As regular user, navigate to `/admin/chat/analytics`
2. As admin user, navigate to `/admin/chat/analytics`
3. Verify access restrictions

**Expected Results:**
- Regular users denied access
- Admin users can view analytics
- Proper authentication checks

#### Test 4.2: Metrics Accuracy
**Steps:**
1. Count actual conversations, messages, users in database
2. Compare with analytics dashboard numbers
3. Verify feedback metrics
4. Check unanswered queries count

**Expected Results:**
- All metrics match database counts
- Feedback percentages calculated correctly
- Unanswered queries count accurate
- Top jurisdictions and rule types correct

#### Test 4.3: Feedback Tab Functionality
**Steps:**
1. Navigate to Feedback tab in analytics
2. Verify feedback breakdown display
3. Check recent unanswered queries
4. Test rule types display

**Expected Results:**
- Feedback summary shows correct counts
- Percentages calculated accurately
- Recent unanswered queries displayed
- Rule types ranked correctly

### 5. Database Schema Validation

#### Test 5.1: Table Structure
**Steps:**
1. Verify `chat_feedback` table exists with correct columns
2. Verify `admin_access_log` table exists with correct columns
3. Check RLS policies are enabled
4. Test constraints and indexes

**Expected Results:**
- Tables created with correct schema
- RLS policies active and working
- Unique constraint on (message_id, user_id) for feedback
- Indexes created for performance

#### Test 5.2: Data Integrity
**Steps:**
1. Test foreign key constraints
2. Verify cascade deletes work correctly
3. Test RLS policy enforcement
4. Check data validation

**Expected Results:**
- Foreign keys enforce referential integrity
- Deleting messages/conversations cascades properly
- RLS prevents unauthorized access
- Check constraints enforce valid values

## Performance Testing

### Load Testing
1. Test export with large conversations (100+ messages)
2. Test admin dashboard with large datasets
3. Verify feedback submission performance
4. Check analytics query performance

### Expected Performance:
- PDF export completes within 10 seconds for 100 messages
- Admin dashboard loads within 3 seconds
- Feedback submission responds within 1 second
- Analytics queries complete within 5 seconds

## Security Testing

### Access Control
1. Verify all admin endpoints require admin role
2. Test feedback access restrictions
3. Check export access controls
4. Validate RLS policy enforcement

### Data Protection
1. Verify admin access logging captures all actions
2. Check that sensitive data is not exposed
3. Test that users can only access their own data
4. Validate proper authentication throughout

## Error Handling Testing

### API Error Responses
1. Test invalid conversation IDs
2. Test unauthorized access attempts
3. Test malformed feedback submissions
4. Verify proper error messages

### UI Error Handling
1. Test network failures during export
2. Test feedback submission failures
3. Verify graceful degradation
4. Check error message display

## Acceptance Criteria Validation

### CHUI-Audit-1: ✅ Already Complete
- Source storage verified in existing implementation

### CHUI-Audit-2: Export Functionality
- [ ] User can click "Export Chat" button
- [ ] Downloaded file includes questions, answers, and citations
- [ ] Format is clean and readable (PDF)
- [ ] Export captures conversation accurately

### CHUI-Audit-3: Admin Review
- [ ] Admin can access any chat session by ID or user
- [ ] Admin sees exactly what user saw
- [ ] Admin-only access enforced via auth
- [ ] All admin access logged for privacy audit

### CHUI-Audit-4: Analytics & Feedback
- [ ] System gathers basic stats
- [ ] Feedback mechanism captures user ratings
- [ ] Admin can view analytics and feedback
- [ ] Feedback loop enables knowledge base improvement

## Test Data Setup

### Required Test Data:
1. Admin user with role='admin'
2. Regular users with chat conversations
3. Chat messages with various metadata
4. Some unanswered queries in database
5. Mix of positive and negative feedback

### SQL for Test Data:
```sql
-- Create test admin user (update existing user)
UPDATE profiles SET role = 'admin' WHERE email = '<EMAIL>';

-- Create test feedback
INSERT INTO chat_feedback (message_id, user_id, feedback_type, feedback_text)
SELECT 
  cm.id,
  cc.user_id,
  CASE WHEN random() > 0.7 THEN 'thumbs_up' ELSE 'thumbs_down' END,
  CASE WHEN random() > 0.5 THEN 'Test feedback text' ELSE NULL END
FROM chat_messages cm
JOIN chat_conversations cc ON cm.conversation_id = cc.id
WHERE cm.role = 'assistant'
LIMIT 10;
```

## Completion Checklist

- [ ] All test scenarios executed successfully
- [ ] Performance requirements met
- [ ] Security tests passed
- [ ] Error handling validated
- [ ] Acceptance criteria confirmed
- [ ] Database schema verified
- [ ] Admin access logging working
- [ ] Feedback mechanism functional
- [ ] Export functionality complete
- [ ] Analytics dashboard accurate

## Known Issues & Limitations

1. PDF export may be slow for very large conversations
2. Analytics dashboard requires manual refresh
3. Feedback text limited to 500 characters
4. Admin access logging doesn't capture IP for client-side actions

## Post-Implementation Monitoring

1. Monitor export usage and performance
2. Track feedback submission rates
3. Review admin access logs regularly
4. Monitor analytics query performance
5. Check for any RLS policy violations
