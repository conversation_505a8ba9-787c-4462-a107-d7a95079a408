# Epic 6 Testing Guide

## 🚀 **Quick Start Testing**

### **1. Environment Setup**
```bash
# Copy environment variables
cp .env.example .env.local

# Enable Epic 6 features
echo "NEXT_PUBLIC_EPIC6_ENABLED=true" >> .env.local
echo "NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED=true" >> .env.local
echo "NEXT_PUBLIC_RED_FLAGS_ENABLED=true" >> .env.local
echo "NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED=true" >> .env.local
echo "NEXT_PUBLIC_CHAT_ENABLED=true" >> .env.local

# Start development server
npm run dev
```

### **2. Test Epic 6 Status**
Visit: `http://localhost:3000/api/test-epic6`

Expected response:
```json
{
  "timestamp": "2024-01-XX...",
  "testType": "all",
  "results": {
    "database": { "overall": "pass" },
    "features": { "overall": "pass" },
    "environment": { "overall": "pass" }
  }
}
```

## 🧪 **Feature Testing Checklist**

### **Feature 6.1: Custom Project Input (Pro Only)**

#### **Free Tier Test:**
1. Go to `/search`
2. Verify dropdown shows with upgrade prompt
3. Click "Upgrade to Pro" → should redirect to `/pricing`

#### **Pro Tier Test:**
1. Upgrade user to Pro tier in Supabase:
   ```sql
   UPDATE profiles SET subscription_tier = 'pro' WHERE email = '<EMAIL>';
   ```
2. Refresh `/search` page
3. Verify custom text area appears
4. Test input: "I want to build a 6-foot privacy fence along my back property line"
5. Click "Analyze" → should parse to "fence" project type
6. Verify API call to `/api/parse-project-description`

### **Feature 6.3: Red Flag Detection**

#### **Test Red Flags Display:**
1. Complete a compliance search
2. Verify red flags section appears below main card
3. For Free users: should show upgrade prompt
4. For Pro users: should show actual red flags (if any detected)

#### **Test Red Flag API:**
```bash
# Test red flag detection directly
curl -X POST http://localhost:3000/api/compliance/summary \
  -H "Content-Type: application/json" \
  -d '{
    "lat": 42.3314,
    "lng": -83.0458,
    "ruleType": "fence",
    "address": "123 Test St, Detroit, MI"
  }'
```

### **Feature 6.4: Clause Browser (Appraiser Tier)**

#### **Test Clause Browser:**
1. Set user to Appraiser tier:
   ```sql
   UPDATE profiles SET subscription_tier = 'appraiser' WHERE email = '<EMAIL>';
   ```
2. Complete compliance search
3. Verify clause browser appears with accordion/table toggle
4. Test search functionality
5. Test tag filtering

### **Feature 6.5: Chat Interface**

#### **Test Chat Creation:**
1. Complete compliance search as Pro/Appraiser user
2. Verify chat interface appears at bottom
3. Send test message: "What permits do I need?"
4. Verify AI response appears
5. Check database for conversation and message records

#### **Test Chat API:**
```bash
# Create conversation
curl -X POST http://localhost:3000/api/chat/conversations \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Test St",
    "rule_type": "fence",
    "jurisdiction_name": "Detroit, MI",
    "context_data": {"summary": "Test context"}
  }'

# Send message
curl -X POST http://localhost:3000/api/chat/messages \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "uuid-from-above",
    "content": "What permits do I need for a fence?"
  }'
```

## 🔧 **Database Validation**

### **Check Epic 6 Tables:**
```sql
-- Verify tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('chat_conversations', 'chat_messages', 'red_flags', 'ordinance_clauses', 'feature_usage');

-- Check sample data
SELECT COUNT(*) FROM chat_conversations;
SELECT COUNT(*) FROM chat_messages;
SELECT COUNT(*) FROM red_flags;
SELECT COUNT(*) FROM ordinance_clauses;
```

### **Test User Tier Access:**
```sql
-- Create test users with different tiers
INSERT INTO auth.users (id, email) VALUES 
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>'),
  ('*************-2222-2222-************', '<EMAIL>'),
  ('*************-3333-3333-************', '<EMAIL>');

INSERT INTO profiles (id, email, subscription_tier) VALUES 
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'free'),
  ('*************-2222-2222-************', '<EMAIL>', 'pro'),
  ('*************-3333-3333-************', '<EMAIL>', 'appraiser');
```

## 🚨 **Common Issues & Solutions**

### **Issue: "analyzeOrdinanceWithTier is not defined"**
**Solution:** Ensure `src/lib/enhanced-ordinance-analysis.ts` exists and is properly exported.

### **Issue: "getTierConfig is not defined"**
**Solution:** Ensure `src/lib/tier-config.ts` exists and is properly exported.

### **Issue: Chat not working**
**Solution:** 
1. Check OpenAI API key is set
2. Verify user has Pro/Appraiser tier
3. Check feature flags are enabled

### **Issue: Red flags not appearing**
**Solution:**
1. Verify `NEXT_PUBLIC_RED_FLAGS_ENABLED=true`
2. Check user tier has `enableRedFlags: true`
3. Ensure AI analysis is working

## 📈 **Performance Testing**

### **Load Test Chat Interface:**
```bash
# Test multiple concurrent chat messages
for i in {1..10}; do
  curl -X POST http://localhost:3000/api/chat/messages \
    -H "Content-Type: application/json" \
    -d "{\"conversation_id\": \"test-conv\", \"content\": \"Test message $i\"}" &
done
```

### **Monitor Database Performance:**
```sql
-- Check for slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
WHERE query LIKE '%chat_%' OR query LIKE '%red_flags%'
ORDER BY mean_exec_time DESC;
```

## ✅ **Success Criteria**

- [ ] All Epic 6 tables accessible
- [ ] Feature flags working correctly
- [ ] Tier-based access control functioning
- [ ] Custom project input parsing correctly
- [ ] Red flags displaying for Pro+ users
- [ ] Clause browser working for Appraiser users
- [ ] Chat interface functional for Pro+ users
- [ ] Upgrade prompts showing for restricted features
- [ ] No console errors in browser
- [ ] API endpoints responding correctly
- [ ] Database queries performing well

## 🔄 **Rollback Plan**

If issues occur, disable features gradually:

```bash
# Disable all Epic 6 features
echo "NEXT_PUBLIC_EPIC6_ENABLED=false" >> .env.local

# Or disable individual features
echo "NEXT_PUBLIC_CHAT_ENABLED=false" >> .env.local
echo "NEXT_PUBLIC_RED_FLAGS_ENABLED=false" >> .env.local
echo "NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED=false" >> .env.local
echo "NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED=false" >> .env.local

# Restart application
npm run dev
```
