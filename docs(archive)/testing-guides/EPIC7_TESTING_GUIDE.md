# Epic 7: Onboarding & Help Resources - Testing Guide

## 🎯 **Overview**

Epic 7 implements comprehensive onboarding and help resources including:
- **Feature 7.1**: Guided Onboarding Tutorial
- **Feature 7.2**: Searchable Knowledge Base (FAQ)
- **Feature 7.3**: In-App Feedback & Issue Reporting

## 🧪 **Testing Checklist**

### **Feature 7.1: Guided Onboarding Tutorial**

#### **7.1.1: 3-Step Walkthrough on First Login**
- [ ] **First-time user detection**: New users see tutorial automatically
- [ ] **Tutorial modal display**: <PERSON><PERSON> appears with proper styling and animations
- [ ] **Step navigation**: Can navigate between 3 steps (address, compliance, upgrade)
- [ ] **Progress indicator**: Progress bar shows current step
- [ ] **Tutorial completion**: Completing tutorial sets `first_time_user = false`
- [ ] **Skip functionality**: Users can skip tutorial and it won't show again

**Test Steps:**
1. Create a new user account
2. Navigate to `/search` page
3. Verify tutorial modal appears automatically
4. Test navigation through all 3 steps
5. Complete tutorial and verify it doesn't show again

#### **7.1.2: Replay Tutorial from Help Menu**
- [ ] **Help menu visibility**: Help menu appears in header navigation
- [ ] **Replay tutorial option**: "Replay Tutorial" option visible in help menu
- [ ] **Tutorial reset**: Clicking replay resets `first_time_user = true`
- [ ] **Tutorial display**: Tutorial shows again after replay

**Test Steps:**
1. Click Help menu in header
2. Click "Replay Tutorial"
3. Verify tutorial modal appears
4. Complete tutorial again

### **Feature 7.2: Searchable Knowledge Base (FAQ)**

#### **7.2.1: Public FAQ Page**
- [ ] **Page accessibility**: `/faq` page loads without errors
- [ ] **FAQ display**: All published FAQs are visible
- [ ] **Search functionality**: Search input filters FAQs in real-time
- [ ] **Category filtering**: Category dropdown filters FAQs correctly
- [ ] **Expandable answers**: Clicking questions expands/collapses answers
- [ ] **Responsive design**: Page works on mobile and desktop

**Test Steps:**
1. Navigate to `/faq`
2. Verify FAQs are displayed
3. Test search with various keywords
4. Test category filtering
5. Click questions to expand/collapse answers

#### **7.2.2: Admin CMS for FAQs**
- [ ] **Admin page access**: `/admin/faq` page loads for authenticated users
- [ ] **FAQ creation**: Can create new FAQs with all fields
- [ ] **FAQ editing**: Can edit existing FAQs
- [ ] **FAQ deletion**: Can delete FAQs with confirmation
- [ ] **Publishing control**: Can toggle published status
- [ ] **Real-time updates**: Changes reflect on public FAQ page

**Test Steps:**
1. Navigate to `/admin/faq`
2. Create a new FAQ with all fields
3. Edit an existing FAQ
4. Delete an FAQ (with confirmation)
5. Toggle published status
6. Verify changes on public FAQ page

### **Feature 7.3: In-App Feedback & Issue Reporting**

#### **7.3.1: Feedback Widget Integration**
- [ ] **Widget visibility**: Feedback widget appears on all pages
- [ ] **Widget positioning**: Widget is positioned correctly (bottom-right)
- [ ] **Modal functionality**: Clicking widget opens feedback modal
- [ ] **Category selection**: Can select feedback category (bug, feature, general)
- [ ] **Form validation**: Required fields are validated
- [ ] **Submission success**: Feedback submits successfully with confirmation
- [ ] **Widget minimization**: Can minimize/expand widget

**Test Steps:**
1. Verify feedback widget appears on multiple pages
2. Click widget to open modal
3. Test all feedback categories
4. Submit feedback with valid data
5. Test form validation with invalid data
6. Test widget minimize/expand functionality

#### **7.3.2: Sync Feedback to Project Board**
- [ ] **Database storage**: Feedback is stored in `feedback` table
- [ ] **User association**: Feedback is associated with correct user
- [ ] **Metadata capture**: User agent, URL, and timestamp are captured
- [ ] **GitHub integration**: (Optional) Feedback creates GitHub issues
- [ ] **Status tracking**: Feedback status can be updated

**Test Steps:**
1. Submit feedback through widget
2. Verify feedback appears in database
3. Check user association and metadata
4. (If configured) Verify GitHub issue creation
5. Test feedback status updates

## 🔧 **Database Verification**

### **Check New Tables**
```sql
-- Verify feedback table
SELECT * FROM feedback ORDER BY created_at DESC LIMIT 5;

-- Verify faqs table
SELECT * FROM faqs WHERE is_published = true ORDER BY sort_order;

-- Verify first_time_user field
SELECT id, email, first_time_user FROM profiles WHERE first_time_user = true;
```

### **Check RLS Policies**
```sql
-- Test feedback RLS
SELECT * FROM feedback; -- Should only show user's own feedback

-- Test FAQ RLS
SELECT * FROM faqs; -- Should show all published FAQs
```

## 🌐 **Navigation Testing**

### **Header Navigation**
- [ ] **Help menu**: Help dropdown appears in header
- [ ] **FAQ link**: FAQ link in main navigation works
- [ ] **Help menu items**: All help menu items function correctly

### **Footer Navigation**
- [ ] **Help section**: Help & Support section appears in footer
- [ ] **FAQ link**: FAQ link in footer works
- [ ] **Contact link**: Contact support link works

## 📱 **Mobile Testing**

### **Responsive Design**
- [ ] **Tutorial modal**: Tutorial is mobile-friendly
- [ ] **FAQ page**: FAQ page works on mobile
- [ ] **Feedback widget**: Widget is accessible on mobile
- [ ] **Help menu**: Help menu works on mobile navigation

## 🚨 **Error Handling**

### **API Error Testing**
- [ ] **Network errors**: Graceful handling of network failures
- [ ] **Invalid data**: Proper validation error messages
- [ ] **Authentication**: Proper handling of unauthenticated requests
- [ ] **Database errors**: Graceful handling of database failures

## 🔒 **Security Testing**

### **Data Protection**
- [ ] **User isolation**: Users can only see their own feedback
- [ ] **Input sanitization**: User inputs are properly sanitized
- [ ] **XSS prevention**: No XSS vulnerabilities in user content
- [ ] **CSRF protection**: API endpoints are protected

## 📊 **Performance Testing**

### **Load Testing**
- [ ] **FAQ search**: Search performs well with many FAQs
- [ ] **Tutorial loading**: Tutorial loads quickly
- [ ] **Feedback submission**: Feedback submits without delays
- [ ] **Database queries**: Efficient database queries

## 🎨 **UI/UX Testing**

### **Visual Design**
- [ ] **Consistent styling**: All components match design system
- [ ] **Accessibility**: Proper ARIA labels and keyboard navigation
- [ ] **Loading states**: Appropriate loading indicators
- [ ] **Success states**: Clear success confirmations

## 🔧 **Environment Variables**

### **Required Configuration**
```env
# Optional: GitHub integration for feedback sync
GITHUB_TOKEN=your_github_token
GITHUB_REPO=owner/repo-name
```

## 📝 **Test Data**

### **Sample FAQ Data**
The system includes 5 sample FAQs covering:
- Accuracy and confidence
- Supported project types
- Permit requirements
- Subscription upgrades
- Download functionality

### **Test User Scenarios**
1. **New User**: First-time login, sees tutorial
2. **Returning User**: Has completed tutorial, can replay
3. **Admin User**: Can manage FAQs
4. **Feedback User**: Submits various types of feedback

## ✅ **Completion Criteria**

Epic 7 is complete when:
- [ ] All onboarding tutorial features work correctly
- [ ] FAQ system is fully functional (public + admin)
- [ ] Feedback collection and storage works
- [ ] All navigation links are functional
- [ ] Mobile experience is optimized
- [ ] Security and performance requirements are met

## 🐛 **Known Issues**

- GitHub integration requires manual configuration
- Admin FAQ access needs role-based permissions (currently any authenticated user)
- Tutorial images are placeholder paths (need actual images)

## 📞 **Support**

For issues with Epic 7 implementation:
1. Check browser console for JavaScript errors
2. Verify database tables and RLS policies
3. Test API endpoints directly
4. Check environment variable configuration
