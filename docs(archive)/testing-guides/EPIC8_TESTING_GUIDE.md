# Epic 8: UI Visual & Experience Overhaul - Testing Guide

## 🚀 **Quick Start Testing**

### **1. Environment Setup**
```bash
# Install dependencies (including new Framer Motion)
npm install

# Copy environment variables and add Google Maps API key
cp .env.example .env.local

# Add your Google Maps API key for map features
echo "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here" >> .env.local

# Start development server
npm run dev
```

### **2. Test Epic 8 Features**
Visit: `http://localhost:3000`

---

## 🎨 **Feature Testing Checklist**

### **Feature 8.1: Design System & Theming** ✅
- [ ] **Color Palette**: Check primary teal-blue colors throughout the app
- [ ] **Typography**: Verify Geist fonts are loading properly
- [ ] **Theme Toggle**: Test light/dark mode switching in header
- [ ] **Theme Persistence**: Refresh page and verify theme is remembered
- [ ] **CSS Variables**: Inspect elements to confirm CSS custom properties

**Test Steps:**
1. Click theme toggle in header (sun/moon/monitor icons)
2. Navigate between pages - theme should persist
3. Refresh browser - theme should be remembered
4. Check system preference detection

### **Feature 8.2: Layout Redesign for Clarity** ✅
- [ ] **Landing Page Layout**: Two-column grid on desktop, stacked on mobile
- [ ] **Search Page Layout**: Dashboard layout with 2/3 main content, 1/3 sidebar
- [ ] **Card Design**: Rounded corners (2xl), premium shadows, hover effects
- [ ] **Responsive Design**: Test on different screen sizes

**Test Steps:**
1. Visit landing page - check two-column layout
2. Resize browser window - verify mobile stacking
3. Go to search page - check dashboard layout
4. Hover over cards - verify shadow and scale animations

### **Feature 8.3: Map & Location Imagery** ✅
- [ ] **Mini Map Preview**: Static map appears on compliance cards
- [ ] **Street View**: Toggle between map and street view
- [ ] **Map Interactions**: Click to open in Google Maps
- [ ] **Loading States**: Verify loading animations and error handling
- [ ] **Responsive Maps**: Maps scale properly on different devices

**Test Steps:**
1. Search for an address (e.g., "123 Main St, San Francisco, CA")
2. Check compliance card for mini map
3. Click map toggle button (camera/map icon)
4. Click map to open in Google Maps
5. Test with invalid coordinates

### **Feature 8.4: Interactive Highlights & Animations** ✅
- [ ] **Key Rule Badges**: Pulse animation on load, interactive hover
- [ ] **Panel Animations**: Smooth collapse/expand with height transitions
- [ ] **Card Animations**: Scale and shadow effects on hover
- [ ] **Loading Animations**: Fade-in effects for content
- [ ] **Icon Animations**: Subtle rotations and movements

**Test Steps:**
1. Load a compliance result - watch for pulse animations
2. Click collapsible panels - verify smooth animations
3. Hover over cards and badges - check hover effects
4. Navigate between pages - observe loading animations

### **Feature 8.5: Accessibility & UX Polish** ✅
- [ ] **Keyboard Navigation**: Tab through all interactive elements
- [ ] **Screen Reader**: Test with screen reader (VoiceOver/NVDA)
- [ ] **Focus Management**: Visible focus indicators
- [ ] **ARIA Labels**: Proper labeling for interactive elements
- [ ] **Color Contrast**: Sufficient contrast ratios
- [ ] **Skip Links**: Skip to main content functionality

**Test Steps:**
1. Use Tab key to navigate entire page
2. Test Escape key to close modals/panels
3. Use Enter/Space on buttons and links
4. Check focus indicators are visible
5. Test with screen reader software

---

## 🔧 **Component Testing**

### **New Components Added:**
1. **MiniMap** (`/components/maps/MiniMap.tsx`)
2. **StreetView** (`/components/maps/StreetView.tsx`)
3. **LocationImagery** (`/components/maps/LocationImagery.tsx`)
4. **KeyRuleBadge** (`/components/ui/KeyRuleBadge.tsx`)
5. **CollapsiblePanel** (`/components/ui/CollapsiblePanel.tsx`)
6. **AccessibilityWrapper** (`/components/ui/AccessibilityWrapper.tsx`)
7. **LazyImage** (`/components/ui/LazyImage.tsx`)

### **Enhanced Components:**
1. **ComplianceCard** - Now includes maps and key rule badges
2. **ComplianceStatusPanel** - Enhanced with animations and collapsible panels
3. **Layout** - Improved accessibility structure

---

## 🚨 **Known Issues & Troubleshooting**

### **Maps Not Loading:**
- Verify `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` is set in `.env.local`
- Check browser console for API errors
- Ensure Google Maps Static API is enabled in Google Cloud Console

### **Animations Not Working:**
- Verify Framer Motion is installed: `npm list framer-motion`
- Check for JavaScript errors in browser console
- Ensure `motion` components are properly imported

### **Theme Issues:**
- Clear browser localStorage: `localStorage.clear()`
- Check for CSS conflicts in browser dev tools
- Verify CSS custom properties are defined

### **Accessibility Issues:**
- Test with multiple browsers
- Use browser accessibility dev tools
- Check ARIA attributes in DOM inspector

---

## 📊 **Performance Testing**

### **Lighthouse Audit:**
```bash
# Install Lighthouse CLI
npm install -g lighthouse

# Run audit
lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html
```

**Target Scores:**
- Performance: ≥ 90
- Accessibility: ≥ 90
- Best Practices: ≥ 90
- SEO: ≥ 90

### **Image Optimization:**
- [ ] Maps load lazily when scrolled into view
- [ ] Images have proper alt text
- [ ] Loading states prevent layout shift
- [ ] Error states handle failed loads gracefully

---

## 🎯 **Success Criteria**

Epic 8 is considered complete when:

1. ✅ **All theme features work** (light/dark mode, persistence)
2. ✅ **Layout is responsive** (desktop/mobile layouts)
3. ✅ **Maps display correctly** (static maps + street view)
4. ✅ **Animations are smooth** (no jank or performance issues)
5. ✅ **Accessibility score ≥ 90** (Lighthouse audit)
6. ✅ **Performance score ≥ 90** (Lighthouse audit)
7. ✅ **Keyboard navigation works** (all interactive elements)
8. ✅ **Screen reader compatible** (proper ARIA labels)

---

## 📝 **Reporting Issues**

When reporting issues, please include:
1. Browser and version
2. Screen size/device
3. Steps to reproduce
4. Expected vs actual behavior
5. Console errors (if any)
6. Screenshots/videos (if applicable)

---

## 🔄 **Next Steps**

After Epic 8 validation:
1. Run full regression testing
2. Performance optimization if needed
3. Accessibility audit with real users
4. Cross-browser compatibility testing
5. Mobile device testing
