# Phase 1 Testing Guide: Address Lookup History (Epic 9.1)

## 🎯 What We're Testing

- ✅ Search history is automatically saved when users perform compliance searches
- ✅ Only authenticated users get search history saved
- ✅ RLS policies prevent users from seeing each other's history
- ✅ API endpoints work correctly for retrieving and deleting history

## 📋 Manual Testing Steps

### Step 1: Prepare for Testing
1. **Start the development server** (if not already running):
   ```bash
   npm run dev
   ```
2. **Open browser** to http://localhost:3000
3. **Open browser dev tools** (F12) and go to Network tab to monitor API calls

### Step 2: Test Unauthenticated User (Should NOT save history)
1. **Without signing in**, go to the search page
2. **Perform a compliance search**:
   - Enter address: "123 Main St, Detroit, MI"
   - Select project type: "fence"
   - Click search and wait for results
3. **Expected**: Search works but NO history should be saved (since user is not authenticated)

### Step 3: Test Authenticated User (SHOULD save history)
1. **Sign up or sign in** to the application
2. **Perform multiple compliance searches** with different parameters:
   
   **Search 1:**
   - Address: "456 Oak Ave, Grand Rapids, MI"
   - Project type: "shed"
   - Wait for results
   
   **Search 2:**
   - Address: "789 Pine St, Ann Arbor, MI"
   - Project type: "deck"
   - Wait for results
   
   **Search 3:**
   - Address: "321 Elm Dr, Lansing, MI"
   - Project type: "pool"
   - Wait for results

3. **Monitor Network Tab**: Look for calls to `/api/compliance/summary` and verify they complete successfully

### Step 4: Verify History is Being Saved
1. **Check browser dev tools console** for any errors
2. **Look for log messages** like "✓ Saved search history for user..."
3. **Test the history API directly** (see API Testing section below)

### Step 5: Test Different User Isolation
1. **Sign out** and **create a different user account**
2. **Perform a search** with the new user
3. **Verify** that each user only sees their own history (when we implement the UI)

## 🌐 API Testing

### Test History Retrieval
Open browser dev tools console and run:

```javascript
// Test getting search history (must be signed in)
fetch('/api/history/searches')
  .then(response => response.json())
  .then(data => {
    console.log('Search History:', data);
    console.log(`Found ${data.searches?.length || 0} searches`);
  })
  .catch(error => console.error('Error:', error));
```

### Test History Deletion
```javascript
// Test deleting a specific search (replace 'search-id' with actual ID)
fetch('/api/history/searches?id=search-id', { method: 'DELETE' })
  .then(response => response.json())
  .then(data => console.log('Delete result:', data))
  .catch(error => console.error('Error:', error));

// Test deleting all history
fetch('/api/history/searches?all=true', { method: 'DELETE' })
  .then(response => response.json())
  .then(data => console.log('Delete all result:', data))
  .catch(error => console.error('Error:', error));
```

### Test Unauthenticated Access (Should Fail)
```javascript
// This should return 401 Unauthorized
fetch('/api/history/searches')
  .then(response => {
    if (response.status === 401) {
      console.log('✅ Correctly blocked unauthenticated access');
    } else {
      console.log('❌ Security issue: unauthenticated access allowed');
    }
  });
```

## 🔍 Expected Results

### Successful Search History Save
- **Console logs**: "✓ Saved search history for user [user-id]: [address] - [project-type]"
- **No errors** in browser console
- **API calls complete** with 200 status codes
- **History API returns** the saved searches

### Successful RLS Protection
- **Unauthenticated users**: Cannot access `/api/history/searches` (401 error)
- **Different users**: Cannot see each other's search history
- **Database queries**: Only return data for the authenticated user

### Database Verification
Run the database verification script (see next section) to confirm data is actually saved.

## 🚨 Common Issues & Troubleshooting

### Issue: No search history being saved
- **Check**: User is actually signed in
- **Check**: No errors in server console
- **Check**: Compliance search completed successfully
- **Check**: Database connection is working

### Issue: API returns 401 errors
- **Check**: User session is valid
- **Check**: Supabase authentication is working
- **Try**: Sign out and sign back in

### Issue: Empty search history
- **Check**: Searches were performed while authenticated
- **Check**: No errors during search process
- **Run**: Database verification script to see raw data

## ✅ Success Criteria

Phase 1 is working correctly when:
- ✅ Authenticated users can perform searches and history is automatically saved
- ✅ Unauthenticated users cannot access history APIs
- ✅ Users can retrieve their search history via API
- ✅ Users can delete their search history via API
- ✅ RLS policies prevent cross-user data access
- ✅ No errors in console during normal operation
