# Homepage Hero Headline Variations - Day 1 Testing

## Current Messaging (Baseline)
**Headline:** "Don't Get Hassled"  
**Subheadline:** "Know your property rules before you build"  
**Description:** "Avoid costly mistakes and compliance issues. Get quick access to local building rules and permit requirements for any U.S. address."

---

## Option A: Fine Prevention Focus
**Headline:** "Don't Get Fined – Check Your Building Rules Instantly"  
**Subheadline:** "Enter any U.S. address and project type. Get a plain-English compliance summary in under 30 seconds—no surprises, no hidden fees."

**Why test this:** Direct fear appeal (fines) + speed promise + transparency

---

## Option B: Permit Clarity Focus  
**Headline:** "Know Your Permits Before You Build"  
**Subheadline:** "Avoid costly code violations. Ordrly finds local ordinances, zoning rules, and permit requirements in seconds—so you can build with confidence."

**Why test this:** Professional tone + confidence building + comprehensive solution

---

## Option C: Speed & Simplicity Focus
**Headline:** "Stop Guessing Codes – Start Complying Fast"  
**Subheadline:** "Ordrly tells you exactly which permits you need—enter an address and project, and get your compliance check in 30 seconds. 3 free searches, no credit card."

**Why test this:** Action-oriented + specificity + clear trial offer

---

## Decision Framework
Read each option out loud and consider:

1. **Clarity Test:** Which is easiest to understand in 3 seconds?
2. **Pain Point Test:** Which speaks most directly to your target customer's biggest fear?
3. **Action Test:** Which makes you most likely to click "Try It Free"?
4. **Brand Test:** Which best represents the "Don't Get Hassled" brand voice?

## Recommendation Process:
1. Show all three to 2-3 contractors or homeowners
2. Ask: "Which headline would make you most likely to try this tool?"
3. Track which gets the strongest immediate reaction
4. Choose the winner and implement in `/src/app/page.tsx`

## A/B Testing Notes:
- Once you choose a primary, consider A/B testing the runner-up
- Test headline + subheadline as a unit (they work together)
- Monitor conversion rates for 1-2 weeks before making final decision
