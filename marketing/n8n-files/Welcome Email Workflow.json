{"name": "Welcome Email → New User", "nodes": [{"parameters": {"httpMethod": "POST", "path": "welcome-email", "responseMode": "onReceived", "responseData": "noContent"}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"functionCode": "// Extract user data from webhook\nconst userData = $input.first().json;\nconst userEmail = userData.email || userData.record?.email;\nconst userName = userData.name || userData.record?.name || userData.record?.full_name || userEmail?.split('@')[0] || 'there';\n\n// Create HTML email template\nconst emailHTML = `\n<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <div style=\"background: #1DA1F2; color: white; padding: 20px; text-align: center;\">\n    <h1 style=\"margin: 0;\">Welcome to Ordrly!</h1>\n  </div>\n  \n  <div style=\"padding: 30px 20px;\">\n    <p>Hi ${userName},</p>\n    \n    <p>Thanks for signing up for Ordrly! 🎉</p>\n    \n    <p><strong>Here's how to run your first compliance check:</strong></p>\n    \n    <ol style=\"line-height: 1.6;\">\n      <li>Go to <a href=\"https://ordrly.ai/search\" style=\"color: #1DA1F2;\">ordrly.ai/search</a></li>\n      <li>Enter any U.S. property address</li>\n      <li>Describe your project (e.g., \"Deck in my backyard\")</li>\n      <li>Hit \"Search\" and get an instant compliance summary</li>\n    </ol>\n    \n    <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n      <p style=\"margin: 0;\"><strong>💡 TIP:</strong> You get <strong>3 free searches</strong> to start. Need unlimited? \n      <a href=\"https://ordrly.ai/pricing\" style=\"color: #1DA1F2;\">Upgrade to Pro</a> for unlimited searches, AI chat assistance, and red-flag detection.</p>\n    </div>\n    \n    <blockquote style=\"background: #f2f2f2; padding: 15px; border-left: 4px solid #1DA1F2; margin: 20px 0; font-style: italic;\">\n      \"I was about to start building my deck without checking permits. Ordrly showed I needed a $150 permit, which saved me from a potential $850 violation fine. The search took literally 30 seconds.\"<br>\n      <strong>— Jennifer M., Phoenix, AZ</strong>\n    </blockquote>\n    \n    <div style=\"text-align: center; margin: 30px 0;\">\n      <a href=\"https://ordrly.ai/search\" \n         style=\"background: #1DA1F2; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;\">\n        Start Your First Search →\n      </a>\n    </div>\n    \n    <p style=\"color: #666; font-size: 14px;\">\n      Need help? Reply to this email or visit \n      <a href=\"https://ordrly.ai/faq\" style=\"color: #1DA1F2;\">our FAQ page</a>.\n    </p>\n    \n    <p>Happy building,<br>\n    <strong>The Ordrly Team</strong></p>\n  </div>\n  \n  <div style=\"background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;\">\n    <p>You're receiving this because you signed up for Ordrly at ordrly.ai</p>\n    <p>Ordrly AI LLC | <a href=\"https://ordrly.ai\" style=\"color: #666;\">ordrly.ai</a></p>\n  </div>\n</div>\n`;\n\nreturn {\n  json: {\n    ...userData,\n    userEmail: userEmail,\n    userName: userName,\n    emailHTML: emailHTML\n  }\n};"}, "name": "<PERSON>ail Te<PERSON>late", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"url": "https://api.resend.com/emails", "method": "POST", "authentication": "none", "headerParametersUi": {"parameter": [{"name": "Authorization", "value": "Bearer re_eAFA86pF_C6AwqNf8D9CAPARnvz8VWznZ"}, {"name": "Content-Type", "value": "application/json"}]}, "bodyParametersJson": "={\"from\": \"Ordrly <<EMAIL>>\", \"to\": [\"{{$json[\"userEmail\"]}}\"], \"subject\": \"Welcome to Ordrly – Let's Get You Started! 🎉\", \"html\": \"{{$json[\"emailHTML\"]}}\"}"}, "name": "Send Email", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [650, 300]}], "connections": {"Webhook": {"main": [[{"node": "<PERSON>ail Te<PERSON>late", "type": "main", "index": 0}]]}, "Email Template": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}}}