# n8n Setup Guide for Ordrly - Day 1 Automation

## Overview
This guide sets up n8n automation platform to create your first "Welcome Email" workflow that triggers when new users sign up in Supabase and sends emails via Resend.

**Adapted for your setup:** Using Resend instead of SendGrid, configured for your existing Supabase database.

---

## Step 1: Docker Installation (WSL2)

Since you're comfortable with Docker on WSL, here's the quick setup:

```bash
# Update WSL2 Ubuntu
sudo apt update
sudo apt install -y docker.io
sudo usermod -aG docker $USER

# Restart WSL (run in PowerShell)
wsl --shutdown
# Then reopen Ubuntu terminal

# Verify Docker
docker --version
```

---

## Step 2: Create n8n Project Structure

```bash
# Create dedicated folder
mkdir ~/ordrly-automation
cd ~/ordrly-automation

# Create environment file
touch .env
chmod 600 .env
```

---

## Step 3: Configure Environment Variables

Edit `~/ordrly-automation/.env` with your credentials:

```bash
# ────────── n8n General Settings ──────────
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=ordrly_admin
N8N_BASIC_AUTH_PASSWORD=your_secure_password_here

# ────────── Supabase Settings ──────────
SUPABASE_URL=https://api.ordrly.ai
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE

# ────────── Resend Email Settings ──────────
RESEND_API_KEY=re_eAFA86pF_C6AwqNf8D9CAPARnvz8VWznZ
EMAIL_FROM=Ordrly <<EMAIL>>

# ────────── Future API Keys (Optional) ──────────
# TWITTER_API_KEY=<for_future_social_automation>
# INSTAGRAM_APP_ID=<for_future_social_automation>
```

---

## Step 4: Launch n8n Container

```bash
# Run n8n with your environment
docker run -d \
  --name ordrly-n8n \
  --env-file .env \
  -p 5678:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  n8nio/n8n

# Check if running
docker logs ordrly-n8n
```

**Access n8n:** Open http://localhost:5678
- Username: `ordrly_admin`  
- Password: `your_secure_password_here`

---

## Step 5: Verify Supabase Connection

Before building workflows, test your Supabase connection:

1. In n8n, create a test workflow
2. Add a "Supabase" node
3. Configure with your credentials
4. Test connection to `profiles` table

---

## Troubleshooting

### If n8n won't start:
```bash
# Check logs
docker logs ordrly-n8n

# Common issues:
# - Missing .env file
# - Incorrect environment variables
# - Port 5678 already in use
```

### If Supabase connection fails:
- Verify `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY`
- Check that Realtime is enabled on `profiles` table
- Ensure service role key has proper permissions

### If Resend emails fail:
- Verify `RESEND_API_KEY` is correct
- Ensure `<EMAIL>` is verified in Resend dashboard
- Check Resend sending domain configuration

---

## Next Steps

Once n8n is running:
1. ✅ Create "Welcome Email" workflow (Supabase → Resend)
2. 🔄 Test with dummy user signup
3. 📧 Verify email delivery
4. 🚀 Activate workflow for production

**Ready for workflow creation?** Proceed to the Welcome Email Workflow setup!
