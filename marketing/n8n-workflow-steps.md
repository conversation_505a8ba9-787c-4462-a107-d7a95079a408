# n8n Welcome Email Workflow - Step-by-Step Instructions

## 🎯 Goal
Create a workflow that automatically sends a welcome email when a new user signs up in your Supabase database.

---

## Step 1: Create New Workflow

1. **Open n8n** at http://localhost:5678
2. **Login** with:
   - Username: `ordrly_admin`
   - Password: `OrdrlyAdmin2024!`
3. **Click "New Workflow"** (top right)
4. **Name your workflow:** "Welcome Email → New User"

---

## Step 2: Add Webhook Trigger (Easier than Supabase Trigger)

Since Supabase triggers can be complex, let's use a webhook approach:

1. **Click the "+" button** on the canvas
2. **Search for "Webhook"** and select it
3. **Configure the Webhook:**
   - HTTP Method: `POST`
   - Path: `welcome-email`
   - Leave other settings as default
4. **Click "Execute Node"** to get the webhook URL
5. **Copy the webhook URL** (you'll need this for Supabase)

---

## Step 3: Add Function Node (Email Template)

1. **Click the "+" button** after the Webhook node
2. **Search for "Function"** and select it
3. **Paste this JavaScript code:**

```javascript
// Extract user data from webhook
const userData = $input.first().json;
const userEmail = userData.email || userData.record?.email;
const userName = userData.name || userData.record?.name || userData.record?.full_name || userEmail?.split('@')[0] || 'there';

// Create HTML email template
const emailHTML = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <div style="background: #1DA1F2; color: white; padding: 20px; text-align: center;">
    <h1 style="margin: 0;">Welcome to Ordrly!</h1>
  </div>
  
  <div style="padding: 30px 20px;">
    <p>Hi ${userName},</p>
    
    <p>Thanks for signing up for Ordrly! 🎉</p>
    
    <p><strong>Here's how to run your first compliance check:</strong></p>
    
    <ol style="line-height: 1.6;">
      <li>Go to <a href="https://ordrly.ai/search" style="color: #1DA1F2;">ordrly.ai/search</a></li>
      <li>Enter any U.S. property address</li>
      <li>Describe your project (e.g., "Deck in my backyard")</li>
      <li>Hit "Search" and get an instant compliance summary</li>
    </ol>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <p style="margin: 0;"><strong>💡 TIP:</strong> You get <strong>3 free searches</strong> to start. Need unlimited? 
      <a href="https://ordrly.ai/pricing" style="color: #1DA1F2;">Upgrade to Pro</a> for unlimited searches, AI chat assistance, and red-flag detection.</p>
    </div>
    
    <blockquote style="background: #f2f2f2; padding: 15px; border-left: 4px solid #1DA1F2; margin: 20px 0; font-style: italic;">
      "I was about to start building my deck without checking permits. Ordrly showed I needed a $150 permit, which saved me from a potential $850 violation fine. The search took literally 30 seconds."<br>
      <strong>— Jennifer M., Phoenix, AZ</strong>
    </blockquote>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="https://ordrly.ai/search" 
         style="background: #1DA1F2; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
        Start Your First Search →
      </a>
    </div>
    
    <p style="color: #666; font-size: 14px;">
      Need help? Reply to this email or visit 
      <a href="https://ordrly.ai/faq" style="color: #1DA1F2;">our FAQ page</a>.
    </p>
    
    <p>Happy building,<br>
    <strong>The Ordrly Team</strong></p>
  </div>
  
  <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
    <p>You're receiving this because you signed up for Ordrly at ordrly.ai</p>
    <p>Ordrly AI LLC | <a href="https://ordrly.ai" style="color: #666;">ordrly.ai</a></p>
  </div>
</div>
`;

return {
  json: {
    ...userData,
    userEmail: userEmail,
    userName: userName,
    emailHTML: emailHTML
  }
};
```

4. **Click "Execute Node"** to test the function

---

## Step 4: Add HTTP Request Node (Resend API)

1. **Click the "+" button** after the Function node
2. **Search for "HTTP Request"** and select it
3. **Configure the HTTP Request:**
   - Method: `POST`
   - URL: `https://api.resend.com/emails`
   - Authentication: `Header Auth`
     - Name: `Authorization`
     - Value: `Bearer re_eAFA86pF_C6AwqNf8D9CAPARnvz8VWznZ`
   - Headers: Click "Add Header"
     - Name: `Content-Type`
     - Value: `application/json`

4. **Body (JSON):**
```json
{
  "from": "Ordrly <<EMAIL>>",
  "to": ["{{$json.userEmail}}"],
  "subject": "Welcome to Ordrly – Let's Get You Started! 🎉",
  "html": "{{$json.emailHTML}}"
}
```

---

## Step 5: Test the Workflow

1. **Save the workflow** (Ctrl+S or Cmd+S)
2. **Activate the workflow** (toggle in top right)
3. **Test with sample data:**
   - Go back to the Webhook node
   - Click "Execute Node"
   - Copy the webhook URL
   - Use a tool like Postman or curl to send test data:

```bash
curl -X POST [YOUR_WEBHOOK_URL] \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "name": "Test User"}'
```

4. **Check your email** to see if the welcome email arrived

---

## Step 6: Connect to Supabase (Optional Advanced Step)

Once the workflow is working, you can set up a Supabase database trigger to call your webhook:

1. **In Supabase SQL Editor**, run:
```sql
CREATE OR REPLACE FUNCTION notify_new_user()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM net.http_post(
    url := 'YOUR_N8N_WEBHOOK_URL_HERE',
    headers := '{"Content-Type": "application/json"}'::jsonb,
    body := json_build_object(
      'email', NEW.email,
      'name', NEW.name,
      'id', NEW.id,
      'created_at', NEW.created_at
    )::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER new_user_trigger
  AFTER INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION notify_new_user();
```

2. **Replace `YOUR_N8N_WEBHOOK_URL_HERE`** with your actual webhook URL

---

## ✅ Success Criteria

- [ ] Workflow created and saved
- [ ] Webhook URL generated
- [ ] Test email sent successfully
- [ ] Email formatting looks good
- [ ] All links in email work correctly

**Next:** Set up the Supabase trigger to make it fully automated!
