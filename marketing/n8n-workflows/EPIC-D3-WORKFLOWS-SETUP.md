# Epic D3 n8n Workflows Setup Guide

## 🎯 Overview

This guide covers the setup and configuration of three n8n workflows that complete Epic D3:

1. **Referral Credit Workflow** - Automates referral credit awarding and email notifications
2. **Blog → Social Syndication** - Auto-posts blog content to Twitter, Facebook, LinkedIn, Instagram
3. **Video → Multi-Platform** - Auto-uploads videos to TikTok, YouTube Shorts, Instagram Reels

## 📁 Workflow Files

- `referral-credit-workflow.json` - Referral automation
- `blog-to-social-workflow.json` - Blog syndication
- `video-to-multiplatform-workflow.json` - Video syndication

## 🔧 Required Environment Variables

### Core Services
```bash
# Supabase
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# OpenAI (for AI-generated captions)
OPENAI_API_KEY=your_openai_api_key

# Resend (for email notifications)
RESEND_API_KEY=your_resend_api_key
```

### Social Media APIs
```bash
# Twitter/X
TWITTER_BEARER_TOKEN=your_twitter_bearer_token

# Facebook/Meta (for Facebook & Instagram)
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token
FACEBOOK_PAGE_ID=your_facebook_page_id
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token
INSTAGRAM_ACCOUNT_ID=your_instagram_account_id

# LinkedIn
LINKEDIN_ACCESS_TOKEN=your_linkedin_access_token
LINKEDIN_COMPANY_ID=your_linkedin_company_id

# TikTok
TIKTOK_ACCESS_TOKEN=your_tiktok_access_token

# YouTube
YOUTUBE_ACCESS_TOKEN=your_youtube_access_token
```

## 🚀 Setup Instructions

### Step 1: Import Workflows

1. Open n8n Cloud dashboard
2. Go to **Workflows** → **Import from file**
3. Import each JSON file:
   - `referral-credit-workflow.json`
   - `blog-to-social-workflow.json`
   - `video-to-multiplatform-workflow.json`

### Step 2: Configure Environment Variables

1. Go to **Settings** → **Environment Variables**
2. Add all required variables listed above
3. Save configuration

### Step 3: Set Up Supabase Webhooks

#### For Referral Credits:
```sql
-- Create webhook in Supabase Dashboard
-- Table: referrals
-- Events: INSERT
-- Webhook URL: https://your-n8n-instance.app.n8n.cloud/webhook/referral-credits-webhook
```

#### For Blog Syndication:
```sql
-- Create webhook for blog posts
-- Table: content_items (or your blog table)
-- Events: INSERT, UPDATE
-- Webhook URL: https://your-n8n-instance.app.n8n.cloud/webhook/blog-social-webhook
```

#### For Video Syndication:
```sql
-- Create webhook for video uploads
-- Table: video_assets (or your video table)
-- Events: INSERT
-- Webhook URL: https://your-n8n-instance.app.n8n.cloud/webhook/video-multiplatform-webhook
```

### Step 4: Test Workflows

#### Test Referral Credits:
```bash
# Manually insert a referral record
INSERT INTO referrals (referrer_id, referee_id, referral_code, status)
VALUES ('user1-uuid', 'user2-uuid', 'TEST123', 'pending');
```

#### Test Blog Syndication:
```bash
# Send test webhook
curl -X POST https://your-n8n-instance.app.n8n.cloud/webhook/blog-social-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "type": "blog_post",
    "status": "published",
    "title": "Test Blog Post",
    "summary": "This is a test blog post about property compliance.",
    "url": "https://ordrly.ai/blog/test-post"
  }'
```

#### Test Video Syndication:
```bash
# Send test webhook
curl -X POST https://your-n8n-instance.app.n8n.cloud/webhook/video-multiplatform-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "type": "video_upload",
    "status": "ready",
    "file_extension": "mp4",
    "title": "Quick Tip: Check Permits First",
    "description": "Always check permit requirements before starting your project",
    "video_type": "tip",
    "video_url": "https://ordrly.ai/videos/tip1.mp4",
    "file_path": "media/videos/day3/tip1.mp4"
  }'
```

## 🔍 Workflow Details

### 1. Referral Credit Workflow

**Trigger:** Supabase webhook on `referrals` table INSERT
**Process:**
1. Filter for new referral records with "pending" status
2. Call `award_referral_credits` RPC function
3. Fetch both user profiles (referrer & referee)
4. Send branded email notifications to both parties
5. Return success response

**Email Templates:**
- **Referrer:** "🎉 You've earned 5 bonus searches!"
- **Referee:** "🎉 Welcome bonus: 5 extra searches!"

### 2. Blog → Social Syndication

**Trigger:** Webhook for new published blog posts
**Process:**
1. Filter for published blog posts
2. Generate AI-powered captions for each platform
3. Post simultaneously to:
   - Twitter (280 chars, hashtags)
   - Facebook (link preview, engaging copy)
   - LinkedIn (professional tone, article share)
   - Instagram (visual focus, multiple hashtags)

**AI Prompt:** Optimized for each platform's audience and character limits

### 3. Video → Multi-Platform

**Trigger:** Webhook for new video uploads
**Process:**
1. Filter for ready MP4 video files
2. Generate platform-specific captions with AI
3. Upload simultaneously to:
   - TikTok (trendy, engaging captions)
   - YouTube Shorts (SEO-optimized descriptions)
   - Instagram Reels (visual storytelling focus)

**Video Requirements:**
- Format: MP4
- Duration: 15-60 seconds (optimal for all platforms)
- Resolution: 1080x1920 (vertical)

## 📊 Monitoring & Analytics

### Workflow Execution Logs
- Check n8n execution history for each workflow
- Monitor success/failure rates
- Review error logs for troubleshooting

### Key Metrics to Track
- **Referral Credits:** Credits awarded, emails sent
- **Blog Syndication:** Posts published, engagement rates
- **Video Syndication:** Upload success rates, view counts

### Error Handling
- All workflows include error handling and logging
- Failed executions will show detailed error messages
- Webhook responses indicate success/failure status

## 🛠️ Troubleshooting

### Common Issues

**Referral Credits Not Awarding:**
- Check Supabase webhook configuration
- Verify `award_referral_credits` RPC function exists
- Confirm user profiles have valid email addresses

**Social Posts Not Publishing:**
- Verify API tokens are valid and not expired
- Check platform-specific rate limits
- Ensure content meets platform guidelines

**Video Uploads Failing:**
- Confirm video URLs are publicly accessible
- Check file format and size requirements
- Verify platform API credentials

### Testing Individual Nodes
1. Use n8n's "Execute Node" feature
2. Test with sample data
3. Check node outputs and error messages
4. Verify API responses

## 🎉 Success Criteria

✅ **Referral Workflow:**
- New referrals trigger credit awarding
- Both users receive 5 extra searches
- Branded emails sent to both parties

✅ **Blog Syndication:**
- New blog posts auto-publish to 4 platforms
- AI-generated captions are platform-appropriate
- Links drive traffic back to Ordrly

✅ **Video Syndication:**
- New videos auto-upload to 3 platforms
- Captions include relevant hashtags
- Videos maintain quality across platforms

## 📞 Support

For issues with these workflows:
1. Check n8n execution logs
2. Verify environment variables
3. Test individual webhook endpoints
4. Review platform API documentation

The workflows are designed to fail gracefully - if one platform fails, others will still process successfully.
