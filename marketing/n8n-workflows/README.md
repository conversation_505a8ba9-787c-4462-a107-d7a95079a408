# n8n Usage Alert Workflows

This directory contains everything you need to set up automated usage alerts for your Ordrly.ai application using n8n.

## 📁 Files Overview

### Workflow Files
- **`usage-alert-workflow.json`** - Complete workflow with Slack logging and error handling
- **`simple-usage-alert-workflow.json`** - Simplified workflow for quick testing
- **`SETUP-GUIDE.md`** - Comprehensive setup instructions
- **`test-workflow.js`** - Test script to verify your setup

## 🚀 Quick Start (Choose Your Path)

### Option A: Simple Setup (5 minutes)
Perfect for testing and getting started quickly:

1. Import `simple-usage-alert-workflow.json` into n8n
2. Copy the webhook URL from n8n
3. Set up Supabase webhook pointing to your n8n URL
4. Test with the provided test script

### Option B: Production Setup (15 minutes)
Full-featured workflow with logging and monitoring:

1. Import `usage-alert-workflow.json` into n8n
2. Configure environment variables in n8n
3. Set up Supabase webhook
4. Configure Slack notifications (optional)
5. Test and monitor

## 🎯 What This System Does

**Trigger:** When a free user performs their 8th or 9th search
**Action:** Sends personalized email encouraging upgrade to Pro
**Result:** Increased conversion from free to paid users

### Email Content Preview
```
Subject: You're almost out of free searches!

Hi [Name],

You've used 8 of your 10 free Ordrly searches this month.

Don't get stuck when you need permit information most! 
Upgrade to Pro now for unlimited lookups.

[Upgrade to Pro – $19/mo]

Pro benefits:
✅ Unlimited searches
✅ AI-powered analysis
✅ Red flag detection
✅ Save searches for later
```

## 🔧 Technical Architecture

```
User Search → Supabase profiles.pulls_this_month++ → 
Webhook (if free + ≥8 searches) → n8n Workflow → 
Filter & Validate → HTTP Request to /api/usage-alerts → 
Send Email via Resend → Log Success/Failure
```

## 📊 Expected Results

Based on typical SaaS conversion rates:
- **Email open rate:** 25-35%
- **Click-through rate:** 3-8%
- **Conversion rate:** 2-5% of recipients upgrade
- **Revenue impact:** $38-190 additional MRR per 100 alerts sent

## 🛠️ Setup Requirements

### n8n Requirements
- n8n Cloud account or self-hosted instance
- Webhook capabilities enabled
- Environment variables support

### Supabase Requirements
- Webhook functionality enabled
- Service role access
- Database trigger capabilities

### Application Requirements
- `/api/usage-alerts` endpoint (✅ already implemented)
- Resend API integration (✅ already configured)
- Usage tracking system (✅ already implemented)

## 📋 Setup Checklist

- [ ] n8n instance ready
- [ ] Workflow imported and configured
- [ ] Supabase webhook created
- [ ] Environment variables set
- [ ] Test script executed successfully
- [ ] Email delivery verified
- [ ] Monitoring/logging configured

## 🧪 Testing Your Setup

### Manual Test
```bash
# 1. Update a test user to trigger alert
UPDATE profiles 
SET pulls_this_month = 8 
WHERE id = 'your-test-user-id' 
AND subscription_tier = 'free';

# 2. Check if webhook fired
# 3. Verify email was sent
# 4. Check n8n execution logs
```

### Automated Test
```bash
# Run the test script
node test-workflow.js

# Or test specific scenarios
node -e "require('./test-workflow.js').testDifferentScenarios()"
```

## 📈 Monitoring & Analytics

### Key Metrics to Track
- **Webhook delivery rate** (Supabase → n8n)
- **Email delivery rate** (n8n → Resend)
- **Email open/click rates** (Resend dashboard)
- **Conversion rate** (alerts → upgrades)
- **Revenue attribution** (upgrade source tracking)

### Monitoring Tools
- **n8n:** Execution history and error logs
- **Supabase:** Webhook delivery logs
- **Resend:** Email delivery and engagement metrics
- **Slack:** Real-time notifications (if configured)

## 🔍 Troubleshooting

### Common Issues

**Webhook not triggering:**
- Check Supabase webhook configuration
- Verify filter conditions
- Test webhook manually

**Email not sending:**
- Verify Resend API key
- Check email template formatting
- Review application logs

**n8n workflow failing:**
- Check environment variables
- Verify HTTP request configuration
- Review execution logs

### Debug Commands
```bash
# Test webhook directly
curl -X POST https://your-n8n-instance.com/webhook/usage-alert-webhook \
  -H "Content-Type: application/json" \
  -d '{"type":"UPDATE","table":"profiles","record":{"id":"test","subscription_tier":"free","pulls_this_month":8}}'

# Test API endpoint directly
curl -X POST https://ordrly.ai/api/usage-alerts \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test-user-id","force_check":true}'
```

## 🚀 Next Steps

After setting up usage alerts, consider implementing:

1. **Referral system automation** (Day 3)
2. **Abandoned cart emails** for incomplete upgrades
3. **Win-back campaigns** for churned users
4. **Usage milestone celebrations** (first search, etc.)

## 📞 Support

If you need help:
1. Check the troubleshooting section
2. Review n8n execution logs
3. Test individual components
4. Verify all configuration steps

The system is designed to fail gracefully - if alerts fail, user searches continue normally.
