# n8n Usage Alert Workflow Setup Guide

This guide will walk you through setting up the automated usage alert system using n8n to trigger emails when free users approach their 10-search limit.

## 🚀 Quick Start (5 Minutes)

**For immediate testing, use the simple workflow:**

1. Import `simple-usage-alert-workflow.json` into n8n
2. Copy the webhook URL from the "Webhook Trigger" node
3. Set up Supabase webhook (Step 4 below) pointing to your n8n webhook
4. Test by updating a user's `pulls_this_month` to 8 in Supabase

**For production with logging, use the full workflow:**
- Import `usage-alert-workflow.json` and follow the complete guide below

## Prerequisites

- n8n instance running (cloud or self-hosted)
- Supabase project with webhook capabilities
- Slack webhook URL (optional, for logging)
- Access to your Ordrly.ai application

## Step 1: Environment Variables Setup

In your n8n instance, set up these environment variables:

```bash
# Required
ORDRLY_APP_URL=https://ordrly.ai  # Your production URL
SUPABASE_PROJECT_URL=https://qxiryfbdruydrofclmvz.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Optional (for logging)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

## Step 2: Import the Workflow

1. **Open n8n Dashboard**
   - Navigate to your n8n instance
   - Click "Workflows" in the sidebar

2. **Import Workflow**
   - Click "Import from File" or "Import from URL"
   - Upload the `usage-alert-workflow.json` file
   - Or copy/paste the JSON content directly

3. **Verify Import**
   - Ensure all 7 nodes are connected properly:
     - Webhook → Filter → HTTP Request → Check Success → Log Success/Error → Respond

## Step 3: Configure the Webhook Node

1. **Click on the "Webhook" node**
2. **Set Parameters:**
   - HTTP Method: `POST`
   - Path: `usage-alert-webhook`
   - Response Mode: `Using 'Respond to Webhook' Node`

3. **Copy the Webhook URL**
   - After saving, n8n will generate a webhook URL
   - It will look like: `https://your-n8n-instance.com/webhook/usage-alert-webhook`
   - **Save this URL** - you'll need it for Supabase

## Step 4: Configure Supabase Database Webhook

1. **Open Supabase Dashboard**
   - Go to your Ordrly project: https://supabase.com/dashboard/project/qxiryfbdruydrofclmvz

2. **Navigate to Database → Webhooks**
   - Click "Create a new webhook"

3. **Configure Webhook Settings:**
   ```
   Name: Usage Alert Trigger
   Table: profiles
   Events: UPDATE
   Type: HTTP Request
   HTTP Method: POST
   URL: [Your n8n webhook URL from Step 3]
   HTTP Headers: 
     Content-Type: application/json
   ```

4. **Set Webhook Filters (Important!):**
   ```sql
   -- Only trigger when pulls_this_month changes for free users
   OLD.pulls_this_month IS DISTINCT FROM NEW.pulls_this_month 
   AND NEW.subscription_tier = 'free'
   AND NEW.pulls_this_month >= 8
   ```

5. **Test the Webhook:**
   - Click "Send test webhook"
   - Verify it reaches your n8n workflow

## Step 5: Configure HTTP Request Node

1. **Click on "Send Usage Alert" node**
2. **Verify Settings:**
   - URL: `{{ $env.ORDRLY_APP_URL }}/api/usage-alerts`
   - Method: `POST`
   - Headers: `Content-Type: application/json`
   - Body: 
     ```json
     {
       "user_id": "{{ $json.record.id }}",
       "force_check": false
     }
     ```

## Step 6: Configure Slack Logging (Optional)

If you want Slack notifications:

1. **Create Slack Webhook:**
   - Go to https://api.slack.com/apps
   - Create new app → Incoming Webhooks
   - Copy webhook URL

2. **Update Environment Variable:**
   ```bash
   SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
   ```

3. **Configure Slack Nodes:**
   - Both "Log Success" and "Log Error" nodes should use `{{ $env.SLACK_WEBHOOK_URL }}`

## Step 7: Test the Complete Workflow

### Manual Test via Supabase

1. **Find a Test User:**
   ```sql
   SELECT id, email, pulls_this_month, subscription_tier 
   FROM profiles 
   WHERE subscription_tier = 'free' 
   AND pulls_this_month < 8 
   LIMIT 1;
   ```

2. **Simulate Usage Alert Trigger:**
   ```sql
   UPDATE profiles 
   SET pulls_this_month = 8 
   WHERE id = 'your-test-user-id';
   ```

3. **Verify Workflow Execution:**
   - Check n8n execution log
   - Verify email was sent to test user
   - Check Slack for success notification

### Test via Application

1. **Use Test Account:**
   - Log in as a free tier user
   - Perform 8 searches through the application
   - Verify alert email is received

## Step 8: Monitor and Troubleshoot

### n8n Monitoring

1. **Check Execution History:**
   - Go to "Executions" tab in n8n
   - Look for failed executions
   - Review error logs

2. **Common Issues:**
   - **Webhook not triggering:** Check Supabase webhook configuration
   - **HTTP request failing:** Verify ORDRLY_APP_URL environment variable
   - **Email not sending:** Check Resend API key in your application

### Supabase Monitoring

1. **Check Webhook Logs:**
   - Database → Webhooks → View logs
   - Look for failed webhook deliveries

2. **Verify Database Triggers:**
   ```sql
   -- Check recent profile updates
   SELECT id, email, pulls_this_month, updated_at 
   FROM profiles 
   WHERE subscription_tier = 'free' 
   AND updated_at > NOW() - INTERVAL '1 hour'
   ORDER BY updated_at DESC;
   ```

### Application Monitoring

1. **Check Usage Alert API:**
   ```bash
   # Test the endpoint directly
   curl -X POST https://ordrly.ai/api/usage-alerts \
     -H "Content-Type: application/json" \
     -d '{"user_id": "test-user-id"}'
   ```

2. **Check Email Logs:**
   - Monitor Resend dashboard for email delivery
   - Check application logs for email sending errors

## Step 9: Production Deployment

### Security Considerations

1. **Webhook Security:**
   - Consider adding webhook signature validation
   - Use HTTPS for all endpoints
   - Limit webhook access to Supabase IPs if possible

2. **Rate Limiting:**
   - Monitor for webhook spam
   - Implement rate limiting if needed

### Performance Optimization

1. **Batch Processing:**
   - For high-volume applications, consider batching alerts
   - Use the batch endpoint: `POST /api/usage-alerts` (without user_id)

2. **Monitoring:**
   - Set up alerts for workflow failures
   - Monitor email delivery rates
   - Track conversion rates from alerts to upgrades

## Workflow Logic Summary

```
User performs search → 
Supabase updates pulls_this_month → 
Webhook triggers if (free tier + pulls_this_month >= 8) → 
n8n filters for valid conditions → 
HTTP request to /api/usage-alerts → 
Email sent via Resend → 
Success/failure logged to Slack → 
Webhook response sent
```

## Troubleshooting Checklist

- [ ] n8n workflow is active and saved
- [ ] Supabase webhook is enabled and configured correctly
- [ ] Environment variables are set in n8n
- [ ] ORDRLY_APP_URL points to correct domain
- [ ] Resend API key is valid in application
- [ ] Test user has valid email address
- [ ] Webhook URL is accessible from Supabase

## Support

If you encounter issues:

1. Check n8n execution logs first
2. Verify Supabase webhook delivery logs
3. Test the `/api/usage-alerts` endpoint directly
4. Check application logs for email sending errors

The system is designed to fail gracefully - if alerts fail to send, it won't break the user's search experience.
