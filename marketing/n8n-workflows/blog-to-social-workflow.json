{"name": "Blog → Social Media Syndication", "nodes": [{"parameters": {"httpMethod": "POST", "path": "blog-social-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-blog-trigger", "name": "Blog <PERSON> Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "blog-social-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "filter-blog-posts", "leftValue": "={{ $json.type }}", "rightValue": "blog_post", "operator": {"type": "string", "operation": "equals"}}, {"id": "filter-published", "leftValue": "={{ $json.status }}", "rightValue": "published", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "filter-published-posts", "name": "Filter Published Posts", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "https://api.openai.com/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.OPENAI_API_KEY }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"model\": \"gpt-4\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a social media expert for Ordrly, a property compliance checking tool. Create platform-specific posts from blog content. Return JSON with: twitter (280 chars max, 3-5 hashtags), linkedin (professional tone, 1300 chars max, 3-5 hashtags), facebook (engaging, 500 chars max, 2-4 hashtags), instagram (visual focus, 2200 chars max, 5-10 hashtags). Include relevant emojis and CTAs.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Blog Title: {{ $json.title }}\\n\\nBlog Summary: {{ $json.summary }}\\n\\nBlog URL: {{ $json.url }}\\n\\nCreate social media posts for all platforms.\"\n    }\n  ],\n  \"max_tokens\": 1000,\n  \"temperature\": 0.7\n}", "options": {}}, "id": "generate-social-content", "name": "Generate Social Content", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Parse the AI response and prepare platform-specific content\nconst aiResponse = JSON.parse($input.item.json.choices[0].message.content);\nconst blogData = $('Blog Post Trigger').item.json;\n\nreturn [\n  {\n    platform: 'twitter',\n    content: aiResponse.twitter,\n    url: blogData.url,\n    title: blogData.title\n  },\n  {\n    platform: 'linkedin', \n    content: aiResponse.linkedin,\n    url: blogData.url,\n    title: blogData.title\n  },\n  {\n    platform: 'facebook',\n    content: aiResponse.facebook, \n    url: blogData.url,\n    title: blogData.title\n  },\n  {\n    platform: 'instagram',\n    content: aiResponse.instagram,\n    url: blogData.url,\n    title: blogData.title\n  }\n];"}, "id": "parse-social-content", "name": "Parse Social Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "https://api.twitter.com/2/tweets", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.TWITTER_BEARER_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"text\": \"{{ $json.content }}\"\n}", "options": {}}, "id": "post-to-twitter", "name": "Post to Twitter", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/{{ $vars.FACEBOOK_PAGE_ID }}/feed", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"message\": \"{{ $json.content }}\",\n  \"link\": \"{{ $json.url }}\",\n  \"access_token\": \"{{ $vars.FACEBOOK_ACCESS_TOKEN }}\"\n}", "options": {}}, "id": "post-to-facebook", "name": "Post to Facebook", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"url": "https://api.linkedin.com/v2/ugcPosts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.LINKEDIN_ACCESS_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-Restli-Protocol-Version", "value": "2.0.0"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"author\": \"urn:li:organization:{{ $vars.LINKEDIN_COMPANY_ID }}\",\n  \"lifecycleState\": \"PUBLISHED\",\n  \"specificContent\": {\n    \"com.linkedin.ugc.ShareContent\": {\n      \"shareCommentary\": {\n        \"text\": \"{{ $json.content }}\"\n      },\n      \"shareMediaCategory\": \"ARTICLE\",\n      \"media\": [\n        {\n          \"status\": \"READY\",\n          \"description\": {\n            \"text\": \"{{ $json.title }}\"\n          },\n          \"originalUrl\": \"{{ $json.url }}\"\n        }\n      ]\n    }\n  },\n  \"visibility\": {\n    \"com.linkedin.ugc.MemberNetworkVisibility\": \"PUBLIC\"\n  }\n}", "options": {}}, "id": "post-to-linkedin", "name": "Post to LinkedIn", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 400]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/{{ $vars.INSTAGRAM_ACCOUNT_ID }}/media", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"image_url\": \"{{ $json.image_url || 'https://ordrly.ai/images/og-image.png' }}\",\n  \"caption\": \"{{ $json.content }}\",\n  \"access_token\": \"{{ $vars.INSTAGRAM_ACCESS_TOKEN }}\"\n}", "options": {}}, "id": "create-instagram-media", "name": "Create Instagram Media", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 500]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/{{ $vars.INSTAGRAM_ACCOUNT_ID }}/media_publish", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"creation_id\": \"{{ $json.id }}\",\n  \"access_token\": \"{{ $vars.INSTAGRAM_ACCESS_TOKEN }}\"\n}", "options": {}}, "id": "publish-instagram-post", "name": "Publish Instagram Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"success\", \"blog_title\": $('Blog Post Trigger').item.json.title, \"platforms_posted\": [\"twitter\", \"facebook\", \"linkedin\", \"instagram\"], \"timestamp\": $now } }}"}, "id": "respond-success", "name": "Respond Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}], "connections": {"Blog Post Trigger": {"main": [[{"node": "Filter Published Posts", "type": "main", "index": 0}]]}, "Filter Published Posts": {"main": [[{"node": "Generate Social Content", "type": "main", "index": 0}]]}, "Generate Social Content": {"main": [[{"node": "Parse Social Content", "type": "main", "index": 0}]]}, "Parse Social Content": {"main": [[{"node": "Post to Twitter", "type": "main", "index": 0}, {"node": "Post to Facebook", "type": "main", "index": 0}, {"node": "Post to LinkedIn", "type": "main", "index": 0}, {"node": "Create Instagram Media", "type": "main", "index": 0}]]}, "Post to Twitter": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}, "Post to Facebook": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}, "Post to LinkedIn": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}, "Create Instagram Media": {"main": [[{"node": "Publish Instagram Post", "type": "main", "index": 0}]]}, "Publish Instagram Post": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"id": "social-automation", "name": "Social Automation"}, {"id": "blog-syndication", "name": "Blog Syndication"}], "triggerCount": 0, "updatedAt": "2025-01-27T00:00:00.000Z", "versionId": "1"}