{"name": "Referral Credit Workflow (External Config)", "nodes": [{"parameters": {"httpMethod": "POST", "path": "referral-credits-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-referral-trigger", "name": "Supabase Referral Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "referral-credits-webhook"}, {"parameters": {"url": "https://ordrly.ai/api/n8n/config?source=n8n-workflow", "options": {}}, "id": "load-config", "name": "<PERSON><PERSON> Config", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [380, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "filter-new-referrals", "leftValue": "={{ $('Supabase Referral Trigger').item.json.table }}", "rightValue": "referrals", "operator": {"type": "string", "operation": "equals"}}, {"id": "filter-insert-events", "leftValue": "={{ $('Supabase Referral Trigger').item.json.type }}", "rightValue": "INSERT", "operator": {"type": "string", "operation": "equals"}}, {"id": "filter-pending-status", "leftValue": "={{ $('Supabase Referral Trigger').item.json.record.status }}", "rightValue": "pending", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "filter-referral-inserts", "name": "Filter New Referrals", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [520, 300]}, {"parameters": {"url": "={{ $('Load Config').item.json.config.supabase.url }}/rest/v1/rpc/award_referral_credits", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Load Config').item.json.config.supabase.service_key }}"}, {"name": "Content-Type", "value": "application/json"}, {"name": "apikey", "value": "={{ $('Load Config').item.json.config.supabase.service_key }}"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"referee_id_param\": \"{{ $('Supabase Referral Trigger').item.json.record.referee_id }}\"\n}", "options": {}}, "id": "award-credits-rpc", "name": "Award Referral Credits", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [740, 300]}, {"parameters": {"url": "={{ $('Load Config').item.json.config.supabase.url }}/rest/v1/profiles", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Load Config').item.json.config.supabase.service_key }}"}, {"name": "Content-Type", "value": "application/json"}, {"name": "apikey", "value": "={{ $('Load Config').item.json.config.supabase.service_key }}"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "select", "value": "id,email,name,referral_code,extra_credits"}, {"name": "or", "value": "=(id.eq.{{ $('Supabase Referral Trigger').item.json.record.referrer_id }},id.eq.{{ $('Supabase Referral Trigger').item.json.record.referee_id }})"}]}, "options": {}}, "id": "fetch-user-profiles", "name": "Fetch User Profiles", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [960, 300]}, {"parameters": {"url": "https://api.resend.com/emails", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Load Config').item.json.config.resend.api_key }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"from\": \"Ordrly <<EMAIL>>\",\n  \"to\": [\"{{ $json.email }}\"],\n  \"subject\": \"🎉 You've earned 5 bonus searches!\",\n  \"html\": \"<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'><div style='text-align: center; margin-bottom: 30px;'><h1 style='color: #1DA1F2; margin: 0;'>Ordrly</h1><p style='color: #666; margin: 5px 0 0 0;'>Don't Get Hassled</p></div><h2 style='color: #333;'>Great news, {{ $json.name || 'there' }}! 🎉</h2><p style='color: #555; line-height: 1.6;'>You've earned <strong>5 bonus searches</strong> through our referral program! Your current balance is now <strong>{{ $json.extra_credits || 0 }} extra searches</strong>.</p><div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'><h3 style='color: #333; margin-top: 0;'>Keep sharing, keep earning!</h3><p style='color: #555; margin-bottom: 15px;'>Share your referral link and earn 5 more searches for each friend who signs up:</p><p style='background: white; padding: 10px; border-radius: 4px; font-family: monospace; word-break: break-all; margin: 0;'>{{ $('Load Config').item.json.config.site.url }}/signup?ref={{ $json.referral_code }}</p></div><div style='text-align: center; margin: 30px 0;'><a href='{{ $('Load Config').item.json.config.site.url }}/search' style='background: #1DA1F2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Start Searching →</a></div><p style='color: #888; font-size: 14px; text-align: center; margin-top: 30px;'>Thanks for being part of the Ordrly community!<br>The Ordrly Team</p></div>\"\n}", "options": {}}, "id": "send-referrer-email", "name": "Send Referrer Email", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1180, 200]}, {"parameters": {"url": "https://api.resend.com/emails", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Load Config').item.json.config.resend.api_key }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"from\": \"Ordrly <<EMAIL>>\",\n  \"to\": [\"{{ $json.email }}\"],\n  \"subject\": \"🎉 Welcome bonus: 5 extra searches!\",\n  \"html\": \"<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'><div style='text-align: center; margin-bottom: 30px;'><h1 style='color: #1DA1F2; margin: 0;'>Ordrly</h1><p style='color: #666; margin: 5px 0 0 0;'>Don't Get Hassled</p></div><h2 style='color: #333;'>Welcome to Ordrly, {{ $json.name || 'there' }}! 🎉</h2><p style='color: #555; line-height: 1.6;'>Thanks for joining through a referral! As a welcome bonus, you've received <strong>5 extra searches</strong> to get you started.</p><div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'><h3 style='color: #333; margin-top: 0;'>Get started with Ordrly:</h3><ol style='color: #555; line-height: 1.6; padding-left: 20px;'><li>Enter any U.S. address</li><li>Describe your project</li><li>Get instant compliance results</li><li>Avoid costly mistakes!</li></ol></div><div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'><h3 style='color: #333; margin-top: 0;'>💡 Pro Tip</h3><p style='color: #555; margin: 0;'>Upgrade to Pro for unlimited searches, AI analysis, and red flag detection. Perfect for contractors and frequent builders!</p></div><div style='text-align: center; margin: 30px 0;'><a href='{{ $('Load Config').item.json.config.site.url }}/search' style='background: #1DA1F2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-right: 10px;'>Start Your First Search →</a><a href='{{ $('Load Config').item.json.config.site.url }}/pricing' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Upgrade to Pro →</a></div><p style='color: #888; font-size: 14px; text-align: center; margin-top: 30px;'>Questions? Reply to this email - we're here to help!<br>The Ordrly Team</p></div>\"\n}", "options": {}}, "id": "send-referee-email", "name": "Send Referee Email", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1180, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"success\", \"referrer_id\": $('Supabase Referral Trigger').item.json.record.referrer_id, \"referee_id\": $('Supabase Referral Trigger').item.json.record.referee_id, \"credits_awarded\": 5, \"emails_sent\": 2, \"timestamp\": $now } }}"}, "id": "respond-success", "name": "Respond Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1400, 300]}], "connections": {"Supabase Referral Trigger": {"main": [[{"node": "<PERSON><PERSON> Config", "type": "main", "index": 0}]]}, "Load Config": {"main": [[{"node": "Filter New Referrals", "type": "main", "index": 0}]]}, "Filter New Referrals": {"main": [[{"node": "Award Referral Credits", "type": "main", "index": 0}]]}, "Award Referral Credits": {"main": [[{"node": "Fetch User Profiles", "type": "main", "index": 0}]]}, "Fetch User Profiles": {"main": [[{"node": "Send Referrer Email", "type": "main", "index": 0}, {"node": "Send Referee Email", "type": "main", "index": 0}]]}, "Send Referrer Email": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}, "Send Referee Email": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"id": "referral-automation", "name": "Referral Automation"}], "triggerCount": 0, "updatedAt": "2025-01-27T00:00:00.000Z", "versionId": "1"}