{"name": "Referral Credits Workflow - Clean Version", "nodes": [{"parameters": {"httpMethod": "POST", "path": "referral-credits-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-node-001", "name": "Supabase Referral Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "referral-credits-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "filter-condition-001", "leftValue": "={{ $json.body.record.status }}", "rightValue": "pending", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "filter-node-001", "name": "Filter Pending Status", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "https://qxiryfbdruydrofclmvz.supabase.co/rest/v1/rpc/award_referral_credits", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzQxMzI5NCwiZXhwIjoyMDUyOTg5Mjk0fQ.CZAhmd_ZaKwmRBRDlcAzMkwrVXKmZ"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzQxMzI5NCwiZXhwIjoyMDUyOTg5Mjk0fQ.CZAhmd_ZaKwmRBRDlcAzMkwrVXKmZ"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"referral_id\": \"{{ $json.body.record.id }}\"\n}", "options": {}}, "id": "award-credits-node-001", "name": "Award Credits", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"url": "https://qxiryfbdruydrofclmvz.supabase.co/rest/v1/profiles", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzQxMzI5NCwiZXhwIjoyMDUyOTg5Mjk0fQ.CZAhmd_ZaKwmRBRDlcAzMkwrVXKmZ"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzQxMzI5NCwiZXhwIjoyMDUyOTg5Mjk0fQ.CZAhmd_ZaKwmRBRDlcAzMkwrVXKmZ"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "=eq.{{ $('Supabase Referral Trigger').item.json.body.record.referrer_id }}"}, {"name": "select", "value": "id,email,full_name"}]}, "options": {}}, "id": "fetch-referrer-node-001", "name": "Fetch Referrer Profile", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"url": "https://qxiryfbdruydrofclmvz.supabase.co/rest/v1/profiles", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzQxMzI5NCwiZXhwIjoyMDUyOTg5Mjk0fQ.CZAhmd_ZaKwmRBRDlcAzMkwrVXKmZ"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzQxMzI5NCwiZXhwIjoyMDUyOTg5Mjk0fQ.CZAhmd_ZaKwmRBRDlcAzMkwrVXKmZ"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "=eq.{{ $('Supabase Referral Trigger').item.json.body.record.referee_id }}"}, {"name": "select", "value": "id,email,full_name"}]}, "options": {}}, "id": "fetch-referee-node-001", "name": "Fetch Referee Profile", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 400]}, {"parameters": {"url": "https://api.resend.com/emails", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer re_123456789_PLACEHOLDER_RESEND_API_KEY"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"from\": \"<EMAIL>\",\n  \"to\": [\"{{ $json[0].email }}\"],\n  \"subject\": \"🎉 Your referral earned you 5 extra searches!\",\n  \"html\": \"<h2>Great news!</h2><p>Someone just signed up using your referral code <strong>{{ $('Supabase Referral Trigger').item.json.body.record.referral_code }}</strong>!</p><p>You've earned <strong>5 extra searches</strong> on Ordrly. Happy searching!</p><p>Best regards,<br>The Ordrly Team</p>\"\n}", "options": {}}, "id": "send-referrer-email-node-001", "name": "Send Referrer Email", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200]}, {"parameters": {"url": "https://api.resend.com/emails", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer re_123456789_PLACEHOLDER_RESEND_API_KEY"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"from\": \"<EMAIL>\",\n  \"to\": [\"{{ $json[0].email }}\"],\n  \"subject\": \"🎉 Welcome! You've earned 5 extra searches\",\n  \"html\": \"<h2>Welcome to Ordrly!</h2><p>Thanks for signing up with referral code <strong>{{ $('Supabase Referral Trigger').item.json.body.record.referral_code }}</strong>!</p><p>You've earned <strong>5 extra searches</strong> to get started. Enjoy exploring!</p><p>Best regards,<br>The Ordrly Team</p>\"\n}", "options": {}}, "id": "send-referee-email-node-001", "name": "Send Referee Email", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"success\", \"referral_id\": $('Supabase Referral Trigger').item.json.body.record.id, \"referral_code\": $('Supabase Referral Trigger').item.json.body.record.referral_code, \"timestamp\": $now, \"credits_awarded\": true, \"emails_sent\": true } }}"}, "id": "respond-success-node-001", "name": "Respond Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}], "connections": {"Supabase Referral Trigger": {"main": [[{"node": "Filter Pending Status", "type": "main", "index": 0}]]}, "Filter Pending Status": {"main": [[{"node": "Award Credits", "type": "main", "index": 0}]]}, "Award Credits": {"main": [[{"node": "Fetch Referrer Profile", "type": "main", "index": 0}, {"node": "Fetch Referee Profile", "type": "main", "index": 0}]]}, "Fetch Referrer Profile": {"main": [[{"node": "Send Referrer Email", "type": "main", "index": 0}]]}, "Fetch Referee Profile": {"main": [[{"node": "Send Referee Email", "type": "main", "index": 0}]]}, "Send Referrer Email": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}, "Send Referee Email": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"id": "referral-credits-clean", "name": "Referral Credits Clean"}], "triggerCount": 0, "updatedAt": "2025-06-08T01:15:00.000Z", "versionId": "1"}