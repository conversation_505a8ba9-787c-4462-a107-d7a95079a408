{"name": "Simple Usage Alert Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "simple-usage-alert", "responseMode": "responseNode", "options": {}}, "id": "webhook-node", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "free-tier-check", "leftValue": "={{ $json.record.subscription_tier }}", "rightValue": "free", "operator": {"type": "string", "operation": "equals"}}, {"id": "usage-threshold", "leftValue": "={{ $json.record.pulls_this_month }}", "rightValue": 8, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}}, "id": "filter-node", "name": "Filter Free Users 8+ Searches", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "https://ordrly.ai/api/usage-alerts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"user_id\": \"{{ $json.record.id }}\",\n  \"force_check\": false\n}", "options": {}}, "id": "http-request-node", "name": "Send <PERSON><PERSON> Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"processed\",\n  \"user_id\": \"{{ $('Webhook Trigger').item.json.record.id }}\",\n  \"user_email\": \"{{ $('Webhook Trigger').item.json.record.email }}\",\n  \"usage_count\": {{ $('Webhook Trigger').item.json.record.pulls_this_month }},\n  \"alert_sent\": {{ $json.success || false }},\n  \"timestamp\": \"{{ $now }}\"\n}"}, "id": "response-node", "name": "Send Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Filter Free Users 8+ Searches", "type": "main", "index": 0}]]}, "Filter Free Users 8+ Searches": {"main": [[{"node": "Send <PERSON><PERSON> Request", "type": "main", "index": 0}]]}, "Send Alert Request": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"id": "simple-alerts", "name": "Simple Alerts"}], "triggerCount": 0, "updatedAt": "2025-01-27T00:00:00.000Z", "versionId": "1"}