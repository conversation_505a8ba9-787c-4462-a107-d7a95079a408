/**
 * Test Script for n8n Usage Alert Workflow
 * 
 * This script helps you test the n8n workflow setup by simulating
 * Supabase webhook calls and verifying the complete flow.
 */

// Configuration - Update these values
const CONFIG = {
  // Your n8n webhook URL (get this from the webhook node in n8n)
  N8N_WEBHOOK_URL: 'https://your-n8n-instance.com/webhook/usage-alert-webhook',
  
  // Your Ordrly app URL
  ORDRLY_APP_URL: 'https://ordrly.ai',
  
  // Test user data (use a real user ID from your database for testing)
  TEST_USER: {
    id: '75eb3236-448e-4197-ad2b-461e28ee2635', // Replace with real user ID
    email: '<EMAIL>',
    subscription_tier: 'free',
    pulls_this_month: 8
  }
}

/**
 * Test 1: Simulate Supabase webhook call to n8n
 */
async function testN8nWebhook() {
  console.log('🧪 Testing n8n webhook...')
  
  const webhookPayload = {
    type: 'UPDATE',
    table: 'profiles',
    record: CONFIG.TEST_USER,
    old_record: {
      ...CONFIG.TEST_USER,
      pulls_this_month: 7 // Previous value
    }
  }
  
  try {
    const response = await fetch(CONFIG.N8N_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(webhookPayload)
    })
    
    const result = await response.json()
    
    if (response.ok) {
      console.log('✅ n8n webhook test successful!')
      console.log('Response:', result)
      return true
    } else {
      console.log('❌ n8n webhook test failed!')
      console.log('Status:', response.status)
      console.log('Response:', result)
      return false
    }
  } catch (error) {
    console.log('❌ n8n webhook test error:', error.message)
    return false
  }
}

/**
 * Test 2: Direct API call to usage alerts endpoint
 */
async function testUsageAlertsAPI() {
  console.log('🧪 Testing usage alerts API...')
  
  const apiPayload = {
    user_id: CONFIG.TEST_USER.id,
    force_check: true // Force check even if alert was already sent
  }
  
  try {
    const response = await fetch(`${CONFIG.ORDRLY_APP_URL}/api/usage-alerts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(apiPayload)
    })
    
    const result = await response.json()
    
    if (response.ok) {
      console.log('✅ Usage alerts API test successful!')
      console.log('Response:', result)
      return true
    } else {
      console.log('❌ Usage alerts API test failed!')
      console.log('Status:', response.status)
      console.log('Response:', result)
      return false
    }
  } catch (error) {
    console.log('❌ Usage alerts API test error:', error.message)
    return false
  }
}

/**
 * Test 3: Check if user needs alert (GET endpoint)
 */
async function testAlertStatus() {
  console.log('🧪 Testing alert status check...')
  
  try {
    const response = await fetch(`${CONFIG.ORDRLY_APP_URL}/api/usage-alerts`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    const result = await response.json()
    
    if (response.ok) {
      console.log('✅ Alert status check successful!')
      console.log('Response:', result)
      return true
    } else {
      console.log('❌ Alert status check failed!')
      console.log('Status:', response.status)
      console.log('Response:', result)
      return false
    }
  } catch (error) {
    console.log('❌ Alert status check error:', error.message)
    return false
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting n8n Usage Alert Workflow Tests\n')
  
  // Validate configuration
  if (!CONFIG.N8N_WEBHOOK_URL.includes('webhook')) {
    console.log('❌ Please update N8N_WEBHOOK_URL in the CONFIG section')
    return
  }
  
  if (!CONFIG.TEST_USER.id || CONFIG.TEST_USER.id.includes('replace')) {
    console.log('❌ Please update TEST_USER.id with a real user ID from your database')
    return
  }
  
  const results = []
  
  // Test 1: n8n webhook
  console.log('--- Test 1: n8n Webhook ---')
  results.push(await testN8nWebhook())
  console.log('')
  
  // Test 2: Usage alerts API
  console.log('--- Test 2: Usage Alerts API ---')
  results.push(await testUsageAlertsAPI())
  console.log('')
  
  // Test 3: Alert status
  console.log('--- Test 3: Alert Status Check ---')
  results.push(await testAlertStatus())
  console.log('')
  
  // Summary
  const passed = results.filter(r => r).length
  const total = results.length
  
  console.log('📊 Test Summary:')
  console.log(`✅ Passed: ${passed}/${total}`)
  
  if (passed === total) {
    console.log('🎉 All tests passed! Your n8n workflow is ready.')
  } else {
    console.log('⚠️  Some tests failed. Check the setup guide and try again.')
  }
}

/**
 * Helper function to test with different user scenarios
 */
async function testDifferentScenarios() {
  console.log('🧪 Testing different user scenarios...\n')
  
  const scenarios = [
    {
      name: 'User with 8 searches (should trigger alert)',
      user: { ...CONFIG.TEST_USER, pulls_this_month: 8 }
    },
    {
      name: 'User with 9 searches (should trigger alert)',
      user: { ...CONFIG.TEST_USER, pulls_this_month: 9 }
    },
    {
      name: 'User with 10 searches (limit reached)',
      user: { ...CONFIG.TEST_USER, pulls_this_month: 10 }
    },
    {
      name: 'User with 7 searches (should not trigger)',
      user: { ...CONFIG.TEST_USER, pulls_this_month: 7 }
    },
    {
      name: 'Pro user with 8 searches (should not trigger)',
      user: { ...CONFIG.TEST_USER, pulls_this_month: 8, subscription_tier: 'pro' }
    }
  ]
  
  for (const scenario of scenarios) {
    console.log(`--- ${scenario.name} ---`)
    
    const webhookPayload = {
      type: 'UPDATE',
      table: 'profiles',
      record: scenario.user,
      old_record: {
        ...scenario.user,
        pulls_this_month: scenario.user.pulls_this_month - 1
      }
    }
    
    try {
      const response = await fetch(CONFIG.N8N_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(webhookPayload)
      })
      
      const result = await response.json()
      console.log('Response:', result)
    } catch (error) {
      console.log('Error:', error.message)
    }
    
    console.log('')
  }
}

// Export functions for use in Node.js or browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testN8nWebhook,
    testUsageAlertsAPI,
    testAlertStatus,
    testDifferentScenarios,
    CONFIG
  }
}

// Auto-run if called directly
if (typeof window === 'undefined' && require.main === module) {
  runAllTests()
}
