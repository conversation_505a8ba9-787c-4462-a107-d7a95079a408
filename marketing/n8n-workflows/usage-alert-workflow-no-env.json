{"name": "Free Usage Alert Workflow (No Environment Variables)", "nodes": [{"parameters": {"httpMethod": "POST", "path": "usage-alert-webhook", "responseMode": "responseNode", "options": {}}, "id": "f8b0c5e1-8c2a-4d3e-9f1a-2b3c4d5e6f7g", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "usage-alert-webhook"}, {"parameters": {"url": "https://ordrly.ai/api/usage-alerts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"user_id\": \"{{ $json.record.id }}\",\n  \"force_check\": false\n}", "options": {}}, "id": "b2c3d4e5-6f7g-8h9i-0j1k-2l3m4n5o6p7q", "name": "Send Usage Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"processed\", \"user_id\": $('Webhook').item.json.record.id, \"timestamp\": $now, \"success\": $json.success || false } }}"}, "id": "f6g7h8i9-0j1k-2l3m-4n5o-6p7q8r9s0t1u", "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook": {"main": [[{"node": "Send Usage Alert", "type": "main", "index": 0}]]}, "Send Usage Alert": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"id": "usage-alerts-simple", "name": "Usage Alerts Simple"}], "triggerCount": 0, "updatedAt": "2025-01-27T00:00:00.000Z", "versionId": "1"}