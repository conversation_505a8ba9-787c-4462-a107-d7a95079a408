{"name": "Free Usage Alert Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "usage-alert-webhook", "responseMode": "responseNode", "options": {}}, "id": "f8b0c5e1-8c2a-4d3e-9f1a-2b3c4d5e6f7g", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "usage-alert-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition1", "leftValue": "={{ $json.record.subscription_tier }}", "rightValue": "free", "operator": {"type": "string", "operation": "equals"}}, {"id": "condition2", "leftValue": "={{ $json.record.pulls_this_month }}", "rightValue": 8, "operator": {"type": "number", "operation": "gte"}}, {"id": "condition3", "leftValue": "={{ $json.record.pulls_this_month }}", "rightValue": 10, "operator": {"type": "number", "operation": "lt"}}], "combinator": "and"}, "options": {}}, "id": "a1b2c3d4-5e6f-7g8h-9i0j-1k2l3m4n5o6p", "name": "Filter: Free Tier 8-9 Searches", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "={{ $env.ORDRLY_APP_URL }}/api/usage-alerts", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "user_id", "value": "={{ $json.record.id }}"}, {"name": "force_check", "value": false}]}, "options": {}}, "id": "b2c3d4e5-6f7g-8h9i-0j1k-2l3m4n5o6p7q", "name": "Send Usage Alert", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "success_condition", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}, {"id": "email_sent_condition", "leftValue": "={{ $json.email_sent }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}], "combinator": "and"}, "options": {}}, "id": "c3d4e5f6-7g8h-9i0j-1k2l-3m4n5o6p7q8r", "name": "Check Alert <PERSON>", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "={{ $env.SLACK_WEBHOOK_URL }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "✅ Usage Alert Sent Successfully\\n\\n*User:* {{ $('Webhook').item.json.record.email }}\\n*Usage:* {{ $('Webhook').item.json.record.pulls_this_month }}/10 searches\\n*Alert Type:* {{ $json.alert_type }}\\n*Time:* {{ $now.format('YYYY-MM-DD HH:mm:ss') }}"}]}, "options": {}}, "id": "d4e5f6g7-8h9i-0j1k-2l3m-4n5o6p7q8r9s", "name": "Log Success to Slack", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 240]}, {"parameters": {"url": "={{ $env.SLACK_WEBHOOK_URL }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "⚠️ Usage Alert Failed\\n\\n*User:* {{ $('Webhook').item.json.record.email }}\\n*Usage:* {{ $('Webhook').item.json.record.pulls_this_month }}/10 searches\\n*Error:* {{ $json.error || 'Unknown error' }}\\n*Time:* {{ $now.format('YYYY-MM-DD HH:mm:ss') }}"}]}, "options": {}}, "id": "e5f6g7h8-9i0j-1k2l-3m4n-5o6p7q8r9s0t", "name": "Log Error to Slack", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 360]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"processed\", \"user_id\": $('Webhook').item.json.record.id, \"timestamp\": $now } }}"}, "id": "f6g7h8i9-0j1k-2l3m-4n5o-6p7q8r9s0t1u", "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}], "connections": {"Webhook": {"main": [[{"node": "Filter: Free Tier 8-9 Searches", "type": "main", "index": 0}]]}, "Filter: Free Tier 8-9 Searches": {"main": [[{"node": "Send Usage Alert", "type": "main", "index": 0}]]}, "Send Usage Alert": {"main": [[{"node": "Check Alert <PERSON>", "type": "main", "index": 0}]]}, "Check Alert Success": {"main": [[{"node": "Log Success to Slack", "type": "main", "index": 0}], [{"node": "Log Error to Slack", "type": "main", "index": 0}]]}, "Log Success to Slack": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}, "Log Error to Slack": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-27T00:00:00.000Z", "updatedAt": "2025-01-27T00:00:00.000Z", "id": "usage-alerts", "name": "<PERSON><PERSON>"}], "triggerCount": 0, "updatedAt": "2025-01-27T00:00:00.000Z", "versionId": "1"}