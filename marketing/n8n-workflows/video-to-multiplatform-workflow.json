{"name": "Video → Multi-Platform Syndication", "nodes": [{"parameters": {"httpMethod": "POST", "path": "video-multiplatform-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-video-trigger", "name": "Video Upload Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "video-multiplatform-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "filter-video-files", "leftValue": "={{ $json.type }}", "rightValue": "video_upload", "operator": {"type": "string", "operation": "equals"}}, {"id": "filter-mp4-files", "leftValue": "={{ $json.file_extension }}", "rightValue": "mp4", "operator": {"type": "string", "operation": "equals"}}, {"id": "filter-ready-status", "leftValue": "={{ $json.status }}", "rightValue": "ready", "operator": {"type": "string", "operation": "equals"}}], "combineOperation": "all"}}, "id": "filter-video-uploads", "name": "Filter Video Uploads", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "https://api.openai.com/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.OPENAI_API_KEY }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"model\": \"gpt-4\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a social media expert for Ordrly, a property compliance checking tool. Create platform-specific video captions and hashtags. Return JSON with: tiktok (engaging, trendy, 150 chars max, 3-5 hashtags), youtube_shorts (descriptive, SEO-focused, 100 chars max, 3-5 hashtags), instagram_reels (visual storytelling, 125 chars max, 5-8 hashtags). Include relevant emojis and trending hashtags.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Video Title: {{ $json.title }}\\n\\nVideo Description: {{ $json.description }}\\n\\nVideo Type: {{ $json.video_type }}\\n\\nCreate captions for TikTok, YouTube Shorts, and Instagram Reels.\"\n    }\n  ],\n  \"max_tokens\": 800,\n  \"temperature\": 0.8\n}", "options": {}}, "id": "generate-video-captions", "name": "Generate Video Captions", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Parse the AI response and prepare platform-specific content\nconst aiResponse = JSON.parse($input.item.json.choices[0].message.content);\nconst videoData = $('Video Upload Trigger').item.json;\n\nreturn [\n  {\n    platform: 'tiktok',\n    caption: aiResponse.tiktok,\n    video_url: videoData.video_url,\n    title: videoData.title,\n    file_path: videoData.file_path\n  },\n  {\n    platform: 'youtube_shorts',\n    caption: aiResponse.youtube_shorts,\n    video_url: videoData.video_url,\n    title: videoData.title,\n    file_path: videoData.file_path\n  },\n  {\n    platform: 'instagram_reels',\n    caption: aiResponse.instagram_reels,\n    video_url: videoData.video_url,\n    title: videoData.title,\n    file_path: videoData.file_path\n  }\n];"}, "id": "parse-video-content", "name": "Parse Video Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "https://open-api.tiktok.com/share/video/upload/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.TIKTOK_ACCESS_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"video_url\": \"{{ $json.video_url }}\",\n  \"text\": \"{{ $json.caption }}\",\n  \"privacy_level\": \"PUBLIC_TO_EVERYONE\",\n  \"disable_duet\": false,\n  \"disable_comment\": false,\n  \"disable_stitch\": false,\n  \"video_cover_timestamp_ms\": 1000\n}", "options": {}}, "id": "upload-to-tiktok", "name": "Upload to TikTok", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200]}, {"parameters": {"url": "https://www.googleapis.com/upload/youtube/v3/videos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $vars.YOUTUBE_ACCESS_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "part", "value": "snippet,status"}, {"name": "uploadType", "value": "resumable"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"snippet\": {\n    \"title\": \"{{ $json.title }}\",\n    \"description\": \"{{ $json.caption }}\\n\\n🔗 Check property compliance at https://ordrly.ai\\n\\n#Shorts #PropertyCompliance #BuildingPermits #Ordrly\",\n    \"tags\": [\"shorts\", \"property\", \"compliance\", \"building\", \"permits\", \"ordrly\"],\n    \"categoryId\": \"26\",\n    \"defaultLanguage\": \"en\",\n    \"defaultAudioLanguage\": \"en\"\n  },\n  \"status\": {\n    \"privacyStatus\": \"public\",\n    \"selfDeclaredMadeForKids\": false\n  }\n}", "options": {}}, "id": "upload-to-youtube-shorts", "name": "Upload to YouTube Shorts", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/{{ $vars.INSTAGRAM_ACCOUNT_ID }}/media", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"video_url\": \"{{ $json.video_url }}\",\n  \"caption\": \"{{ $json.caption }}\",\n  \"media_type\": \"REELS\",\n  \"access_token\": \"{{ $vars.INSTAGRAM_ACCESS_TOKEN }}\"\n}", "options": {}}, "id": "create-instagram-reel", "name": "Create Instagram Reel", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 400]}, {"parameters": {"url": "https://graph.facebook.com/v18.0/{{ $vars.INSTAGRAM_ACCOUNT_ID }}/media_publish", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"creation_id\": \"{{ $json.id }}\",\n  \"access_token\": \"{{ $vars.INSTAGRAM_ACCESS_TOKEN }}\"\n}", "options": {}}, "id": "publish-instagram-reel", "name": "Publish Instagram Reel", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"success\", \"video_title\": $('Video Upload Trigger').item.json.title, \"platforms_posted\": [\"tiktok\", \"youtube_shorts\", \"instagram_reels\"], \"timestamp\": $now } }}"}, "id": "respond-success", "name": "Respond Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}], "connections": {"Video Upload Trigger": {"main": [[{"node": "Filter Video Uploads", "type": "main", "index": 0}]]}, "Filter Video Uploads": {"main": [[{"node": "Generate Video Captions", "type": "main", "index": 0}]]}, "Generate Video Captions": {"main": [[{"node": "Parse Video Content", "type": "main", "index": 0}]]}, "Parse Video Content": {"main": [[{"node": "Upload to TikTok", "type": "main", "index": 0}, {"node": "Upload to YouTube Shorts", "type": "main", "index": 0}, {"node": "Create Instagram Reel", "type": "main", "index": 0}]]}, "Upload to TikTok": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}, "Upload to YouTube Shorts": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}, "Create Instagram Reel": {"main": [[{"node": "Publish Instagram Reel", "type": "main", "index": 0}]]}, "Publish Instagram Reel": {"main": [[{"node": "Respond Success", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"id": "video-automation", "name": "Video Automation"}, {"id": "multiplatform-syndication", "name": "Multiplatform Syndication"}], "triggerCount": 0, "updatedAt": "2025-01-27T00:00:00.000Z", "versionId": "1"}