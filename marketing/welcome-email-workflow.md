# Welcome Email Workflow - n8n Configuration

## Workflow Overview
**Trigger:** New user signup in Supabase `profiles` table  
**Action:** Send welcome email via Resend API  
**Goal:** Onboard new users with quick start guide and upgrade nudge

---

## Step-by-Step n8n Workflow Setup

### 1. Create New Workflow
- In n8n dashboard, click "New Workflow"
- Name: "Welcome Email → New User"

### 2. Add Supabase Trigger Node
**Node Type:** Supabase Trigger
**Configuration:**
- Resource: `Realtime`
- Operation: `Listen for Changes`
- Table: `profiles`
- Events: `INSERT`
- Supabase URL: `https://api.ordrly.ai`
- Supabase Service Role Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE`
- Schema: `public`

**Note:** If Supabase Trigger isn't available, use "Webhook" node instead and we'll set up a database trigger.

### 3. Add HTTP Request Node (Resend API)
**Node Type:** HTTP Request
**Configuration:**
- Method: `POST`
- URL: `https://api.resend.com/emails`
- Authentication: `Header Auth`
  - Name: `Authorization`
  - Value: `Bearer re_eAFA86pF_C6AwqNf8D9CAPARnvz8VWznZ`
- Headers:
  - `Content-Type`: `application/json`

**Body (JSON):**
```json
{
  "from": "Ordrly <<EMAIL>>",
  "to": ["{{$json["record"]["email"]}}"],
  "subject": "Welcome to Ordrly – Let's Get You Started! 🎉",
  "html": "{{$json.emailHTML}}"
}
```

**Note:** The data structure may be `$json["record"]` instead of `$json["new"]` depending on the trigger type.

### 4. Add Function Node (Email Template)
**Node Type:** Function
**Position:** Between Supabase Trigger and HTTP Request

**JavaScript Code:**
```javascript
// Extract user data from Supabase trigger
const userData = $input.first().json.record || $input.first().json.new || $input.first().json;
const userEmail = userData.email;
const userName = userData.name || userData.full_name || userData.email.split('@')[0] || 'there';

// Create HTML email template
const emailHTML = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <div style="background: #1DA1F2; color: white; padding: 20px; text-align: center;">
    <h1 style="margin: 0;">Welcome to Ordrly!</h1>
  </div>
  
  <div style="padding: 30px 20px;">
    <p>Hi ${userName},</p>
    
    <p>Thanks for signing up for Ordrly! 🎉</p>
    
    <p><strong>Here's how to run your first compliance check:</strong></p>
    
    <ol style="line-height: 1.6;">
      <li>Go to <a href="https://ordrly.ai/search" style="color: #1DA1F2;">ordrly.ai/search</a></li>
      <li>Enter any U.S. property address</li>
      <li>Describe your project (e.g., "Deck in my backyard")</li>
      <li>Hit "Search" and get an instant compliance summary</li>
    </ol>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <p style="margin: 0;"><strong>💡 TIP:</strong> You get <strong>10 free searches</strong> per month. Need unlimited? 
      <a href="https://ordrly.ai/pricing" style="color: #1DA1F2;">Upgrade to Pro</a> for unlimited searches, AI chat assistance, and red-flag detection.</p>
    </div>
    
    <blockquote style="background: #f2f2f2; padding: 15px; border-left: 4px solid #1DA1F2; margin: 20px 0; font-style: italic;">
      "Ordrly saved me $750 on my deck permit—no more confusing municipal websites!"<br>
      <strong>— John S., Grand Rapids</strong>
    </blockquote>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="https://ordrly.ai/search" 
         style="background: #1DA1F2; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
        Start Your First Search →
      </a>
    </div>
    
    <p style="color: #666; font-size: 14px;">
      Need help? Reply to this email or visit 
      <a href="https://ordrly.ai/faq" style="color: #1DA1F2;">our FAQ page</a>.
    </p>
    
    <p>Happy building,<br>
    <strong>The Ordrly Team</strong></p>
  </div>
  
  <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
    <p>You're receiving this because you signed up for Ordrly at ordrly.ai</p>
    <p>Ordrly AI LLC | <a href="https://ordrly.ai/unsubscribe" style="color: #666;">Unsubscribe</a></p>
  </div>
</div>
`;

return {
  json: {
    ...userData,
    emailHTML: emailHTML
  }
};
```

---

## Testing the Workflow

### 1. Activate Workflow
- Toggle "Workflow Active" in top right
- Status should show "Waiting for new rows in profiles..."

### 2. Test with Dummy User
Run this SQL in Supabase SQL Editor:
```sql
INSERT INTO profiles (email, name) 
VALUES ('<EMAIL>', 'Test User');
```

### 3. Verify Results
- Check n8n "Executions" panel for successful run
- Verify email <NAME_EMAIL>
- Check Resend dashboard for delivery status

---

## Troubleshooting

**Supabase Trigger Issues:**
- Ensure Realtime is enabled on `profiles` table
- Verify service role key permissions
- Check table schema matches configuration

**Resend API Issues:**
- Verify API key is correct
- Ensure sending domain is verified
- Check rate limits in Resend dashboard

**Email Template Issues:**
- Test HTML rendering in email client
- Verify all links work correctly
- Check mobile responsiveness

---

## Next Steps

Once working:
1. ✅ Monitor email delivery rates
2. 🔄 Create "Usage Alert" workflow (Day 2)
3. 📊 Set up email analytics tracking
4. 🎯 A/B test email subject lines
