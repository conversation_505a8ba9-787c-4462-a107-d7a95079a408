{"name": "ordrly", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:municipal": "cd reference/municipal-research-api && npm run dev", "build": "next build", "build:dev": "cross-env NODE_OPTIONS=--no-deprecation next build", "start": "next start", "start:dev": "cross-env NODE_OPTIONS=--no-deprecation next start", "lint": "next lint", "lint:dev": "cross-env NODE_OPTIONS=--no-deprecation next lint", "test": "jest", "test:dev": "cross-env NODE_OPTIONS=--no-deprecation jest", "test:municipal": "tsx scripts/test-municipal-integration.ts", "test:watch": "jest --watch", "test:watch:dev": "cross-env NODE_OPTIONS=--no-deprecation jest --watch", "test:coverage": "jest --coverage", "test:coverage:dev": "cross-env NODE_OPTIONS=--no-deprecation jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:setup": "node testing/scripts/setup-test-data.js", "test:cleanup": "node testing/scripts/cleanup-test-data.js", "test:verify": "node testing/scripts/verify-setup.js", "test:ensure-server": "node testing/scripts/ensure-server-running.js", "test:all": "./testing/scripts/run-all-tests.sh", "ui:analyze": "node testing/ui-critique/advanced-ui-analyzer.js", "ui:screenshots": "node testing/ui-critique/screenshot-all-pages.js", "test:report": "node testing/scripts/generate-report.js", "verify:env": "node scripts/verify-env.js", "verify:production": "NODE_ENV=production node scripts/verify-vercel-env.js", "test:performance": "node testing/99-integration-tests/performance-tests.js", "test:epic1": "node testing/01-epic1-address-compliance/api-tests.js", "test:epic2": "npx playwright test testing/02-epic2-ui-public/", "test:epic3": "npx playwright test testing/03-epic3-auth-billing/", "test:epic4": "node testing/04-epic4-marketing/marketing-api-tests.js && npx playwright test testing/04-epic4-marketing/", "test:epic5": "npx playwright test testing/05-epic5-business/", "test:epic6": "npx playwright test testing/06-epic6-tiered-features/", "test:epic7": "npx playwright test testing/07-epic7-onboarding-help/", "test:epic8": "npx playwright test testing/08-epic8-ui-overhaul/", "test:epic9": "npx playwright test testing/09-epic9-user-history/", "test:epic10": "npx playwright test testing/10-epic10-enhancements/", "test:integration": "npx playwright test testing/99-integration-tests/", "test:rate-limits": "./testing/scripts/run-rate-limit-tests.sh", "test:security": "npx playwright test testing/security/", "setup:rate-limit-users": "node testing/scripts/setup-rate-limit-test-users.js", "test:comprehensive": "./testing/comprehensive-page-testing/scripts/run-comprehensive-tests.sh", "test:pages:all": "npx playwright test testing/comprehensive-page-testing/tests/", "test:pages:unauthenticated": "npx playwright test testing/comprehensive-page-testing/tests/01-unauthenticated-pages.spec.ts", "test:pages:free": "npx playwright test testing/comprehensive-page-testing/tests/02-free-user-pages.spec.ts", "test:pages:pro": "npx playwright test testing/comprehensive-page-testing/tests/03-pro-user-pages.spec.ts", "test:pages:priority1": "npx playwright test testing/comprehensive-page-testing/tests/ --grep 'Priority 1'", "test:pages:priority2": "npx playwright test testing/comprehensive-page-testing/tests/ --grep 'Priority 2'", "test:pages:priority3": "npx playwright test testing/comprehensive-page-testing/tests/ --grep 'Priority 3'", "test:pages:priority4": "npx playwright test testing/comprehensive-page-testing/tests/ --grep 'Priority 4'", "test:account-consolidation": "npx playwright test testing/comprehensive-page-testing/tests/04-account-consolidation.spec.ts", "test:search-functionality": "npx playwright test testing/comprehensive-page-testing/tests/05-search-functionality.spec.ts", "test:setup-auth-fixtures": "node testing/comprehensive-page-testing/scripts/setup-test-users.js", "verify:vercel": "node scripts/verify-vercel-env.js", "test:password-reset": "node scripts/test-password-reset.js", "test:emails": "node scripts/test-email-system.js"}, "dependencies": {"@googlemaps/addressvalidation": "^3.0.1", "@googlemaps/google-maps-services-js": "^3.4.1", "@headlessui/react": "^2.2.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/typography": "^0.5.16", "@types/jspdf": "^1.3.3", "@types/lodash": "^4.17.17", "@types/qrcode": "^1.5.5", "@vercel/analytics": "^1.5.0", "autoprefixer": "^10.4.21", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.6.0", "framer-motion": "^12.14.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "next": "^14.2.29", "next-themes": "^0.4.6", "node-html-parser": "^7.0.1", "openai": "^4.103.0", "pdf-poppler": "^0.2.1", "pdf2json": "^3.1.6", "pdf2pic": "^3.1.4", "pdfjs-dist": "^5.2.133", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-select": "^5.10.1", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "resend": "^4.5.1", "sonner": "^2.0.3", "stripe": "^18.1.1", "tailwind-merge": "^3.3.0", "zod": "^3.25.28"}, "devDependencies": {"@axe-core/playwright": "^4.8.3", "@eslint/eslintrc": "^3", "@jest/globals": "^29.7.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "axe-core": "^4.8.3", "chrome-launcher": "^1.1.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "^14.2.29", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.4", "lighthouse": "^11.4.0", "postcss": "^8.5.6", "supertest": "^6.3.3", "tailwindcss": "^3.4.17", "typescript": "^5"}, "overrides": {"@testing-library/react": {"@types/react": "$@types/react"}}}