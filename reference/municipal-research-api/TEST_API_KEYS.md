# Test API Keys for Municipal Research API

## 🔑 Reusable Test Keys

### Florida Property Test Key
- **Purpose**: Testing Florida property data queries and Smart Property Agent
- **Key**: `florida-test-key-2025`
- **ID**: `58c2b81d-49fd-46d1-969c-3fa161c41046`
- **Name**: Florida Property Test Key - REUSABLE
- **Rate Limit**: 1000 requests/hour
- **Created**: 2025-01-18
- **Status**: Active ✅

## 🧪 Test Commands

### Basic Health Check
```bash
curl http://localhost:3001/health
```

### Test Florida Property Query
```bash
curl -X POST http://localhost:3001/api/v1/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: florida-test-key-2025" \
  -d '{
    "address": "123 Main St, Jacksonville, FL",
    "query": "What is the future land use for this address?"
  }'
```

### Test Duplex Development Query
```bash
curl -X POST http://localhost:3001/api/v1/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: florida-test-key-2025" \
  -d '{
    "address": "456 Ocean Ave, Miami, FL",
    "query": "Can I build a duplex here?"
  }'
```

### Test Agriculture Zoning Query
```bash
curl -X POST http://localhost:3001/api/v1/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: florida-test-key-2025" \
  -d '{
    "address": "789 Farm Road, Gainesville, FL",
    "query": "Is this parcel zoned for agriculture?"
  }'
```

## 📝 Notes

- This key is specifically for testing the new state-based property system
- It has access to the Florida property database with 311,000+ records
- The key is reusable and should not be deleted
- Rate limited to 1000 requests/hour for testing purposes
- All test queries should use Florida addresses to leverage the new dataset

## 🗺️ Supported States

Currently supported:
- **Florida (FL)**: 311,000+ property records from Future Land Use Database

Coming soon:
- California (CA)
- Texas (TX)
- New York (NY)
- Other states as data becomes available

## 🔧 Troubleshooting

If the API key doesn't work:
1. Check that the server is running on port 3001
2. Verify the key exists in the database: `SELECT * FROM api_keys WHERE key_hash = 'florida-test-key-2025';`
3. Ensure the key is active: `is_active = true`
4. Check server logs for authentication errors
