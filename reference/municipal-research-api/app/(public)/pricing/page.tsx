"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import {
  ArrowRight, Check, X, Zap, Shield, Clock, Database, Globe,
  Terminal, Code, Layers, Search, Cpu, Eye, BarChart3,
  CheckCircle, Star, Users, TrendingUp, Lock, Sparkles,
  Rocket, Crown, Building, Briefcase
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { PageLayout } from '@/components/ui/page-layout'
import { OrdrlyLogoSVG } from '@/components/ui/ordrly-logo'

// Sophisticated dark theme inspired by TJ logo (black/white/blue/green)
const theme = {
  container: "relative z-10 container mx-auto px-6",
  card: "glass-morphism hover-lift",
  text: {
    primary: "text-white",
    secondary: "text-gray-400",
    accent: "text-blue-400",
    success: "text-green-400"
  },
  button: {
    primary: "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white border border-blue-500/50",
    secondary: "border-gray-600/50 text-gray-300 hover:bg-gray-800/20",
    success: "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white",
    premium: "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white border border-purple-500/50"
  },
  border: {
    blue: "border-blue-500/30",
    green: "border-green-500/30",
    purple: "border-purple-500/30",
    gold: "border-yellow-500/30"
  }
}

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly')

  const plans = [
    {
      name: "Starter",
      icon: Rocket,
      price: { monthly: 49, annual: 490 },
      description: "Perfect for small projects and individual developers",
      features: [
        "1,000 API calls/month",
        "Basic municipal data access",
        "Email support",
        "Standard response times",
        "Basic documentation",
        "Community forum access"
      ],
      limitations: [
        "No priority support",
        "Limited to 5 jurisdictions",
        "No custom integrations"
      ],
      buttonText: "Start Free Trial",
      buttonStyle: theme.button.primary,
      popular: false,
      border: theme.border.blue
    },
    {
      name: "Professional",
      icon: Briefcase,
      price: { monthly: 99, annual: 990 },
      description: "Ideal for growing businesses and development teams",
      features: [
        "10,000 API calls/month",
        "Full municipal data access",
        "Priority email support",
        "Faster response times",
        "Advanced documentation",
        "API key management",
        "Usage analytics",
        "Custom rate limits",
        "All jurisdictions included"
      ],
      limitations: [
        "No phone support",
        "Standard SLA"
      ],
      buttonText: "Start Free Trial",
      buttonStyle: theme.button.success,
      popular: true,
      border: theme.border.green
    },
    {
      name: "Enterprise",
      icon: Building,
      price: { monthly: 200, annual: 2000 },
      description: "For large organizations with high-volume needs",
      features: [
        "100,000+ API calls/month",
        "Premium municipal data access",
        "24/7 phone & email support",
        "Guaranteed response times",
        "White-label documentation",
        "Advanced API management",
        "Real-time analytics",
        "Custom integrations",
        "Dedicated account manager",
        "99.9% SLA guarantee",
        "Custom data exports",
        "Priority feature requests"
      ],
      limitations: [],
      buttonText: "Contact Sales",
      buttonStyle: theme.button.premium,
      popular: false,
      border: theme.border.purple
    }
  ]

  return (
    <PageLayout>
      {/* Hero Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl text-center">
            <div className="inline-flex items-center gap-4 mb-8">
              <OrdrlyLogoSVG size="xl" className="w-20 h-20" />
              <div className="text-left">
                <h1 className="text-5xl md:text-6xl font-orbitron font-bold bg-gradient-to-r from-white via-green-200 to-green-400 bg-clip-text text-transparent leading-tight pb-2">
                  Simple Pricing
                </h1>
                <div className="text-xl md:text-2xl font-exo font-light text-blue-400 mt-2">
                  powered by Ordrly
                </div>
              </div>
            </div>
            
            <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto leading-relaxed mb-12`}>
              Transparent pricing with no hidden fees. Start with our free trial and scale as you grow.
              <span className="text-green-400"> All plans include our core municipal research capabilities.</span>
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center gap-4 mb-16">
              <span className={`font-exo ${billingCycle === 'monthly' ? theme.text.primary : theme.text.secondary}`}>
                Monthly
              </span>
              <button
                onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'annual' : 'monthly')}
                className={`relative w-16 h-8 rounded-full transition-colors ${
                  billingCycle === 'annual' ? 'bg-green-500' : 'bg-gray-600'
                }`}
              >
                <div className={`absolute top-1 w-6 h-6 bg-white rounded-full transition-transform ${
                  billingCycle === 'annual' ? 'translate-x-9' : 'translate-x-1'
                }`} />
              </button>
              <span className={`font-exo ${billingCycle === 'annual' ? theme.text.primary : theme.text.secondary}`}>
                Annual
              </span>
              {billingCycle === 'annual' && (
                <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm font-exo">
                  Save 17%
                </span>
              )}
            </div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section className={`${theme.container} pb-24`}>
          <div className="mx-auto max-w-7xl">
            <div className="grid md:grid-cols-3 gap-8">
              {plans.map((plan, index) => (
                <Card key={plan.name} className={`${theme.card} ${plan.border} relative ${plan.popular ? 'scale-105' : ''}`}>
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-1 rounded-full text-sm font-exo">
                        Most Popular
                      </div>
                    </div>
                  )}

                  <CardHeader className="text-center pb-6">
                    <div className={`w-16 h-16 ${plan.popular ? 'bg-green-500/20' : 'bg-gray-500/20'} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                      <plan.icon className={`w-8 h-8 ${plan.popular ? 'text-green-400' : 'text-gray-400'}`} />
                    </div>
                    <CardTitle className={`font-orbitron text-2xl ${theme.text.primary} mb-2`}>
                      {plan.name}
                    </CardTitle>
                    <CardDescription className={`${theme.text.secondary} font-space mb-4`}>
                      {plan.description}
                    </CardDescription>
                    <div className="text-center">
                      <div className={`text-4xl font-orbitron font-bold ${theme.text.primary} mb-2`}>
                        ${plan.price[billingCycle]}
                        <span className={`text-lg ${theme.text.secondary} font-space`}>
                          /{billingCycle === 'monthly' ? 'mo' : 'yr'}
                        </span>
                      </div>
                      {billingCycle === 'annual' && (
                        <div className={`text-sm ${theme.text.secondary} font-space`}>
                          ${Math.round(plan.price.annual / 12)}/month billed annually
                        </div>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <Button className={`w-full ${plan.buttonStyle} font-exo`}>
                      {plan.buttonText}
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>

                    <div className="space-y-3">
                      <h4 className={`font-exo font-semibold ${theme.text.primary} text-sm`}>
                        What's included:
                      </h4>
                      <ul className="space-y-2">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-3">
                            <Check className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                            <span className={`text-sm ${theme.text.secondary} font-space`}>
                              {feature}
                            </span>
                          </li>
                        ))}
                      </ul>

                      {plan.limitations.length > 0 && (
                        <div className="pt-3 border-t border-gray-700/50">
                          <h4 className={`font-exo font-semibold ${theme.text.secondary} text-sm mb-2`}>
                            Limitations:
                          </h4>
                          <ul className="space-y-2">
                            {plan.limitations.map((limitation, limitIndex) => (
                              <li key={limitIndex} className="flex items-start gap-3">
                                <X className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                                <span className={`text-sm ${theme.text.secondary} font-space`}>
                                  {limitation}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-orbitron font-bold bg-gradient-to-r from-white to-blue-400 bg-clip-text text-transparent mb-6">
                Frequently Asked Questions
              </h2>
              <p className={`text-xl ${theme.text.secondary} font-space`}>
                Everything you need to know about our municipal research API pricing
              </p>
            </div>

            <div className="grid gap-6">
              <Card className={`${theme.card} ${theme.border.blue}`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    What happens during the free trial?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    You get full access to our Starter plan for 7 days with 1,000 API calls included.
                    No credit card required. You can upgrade, downgrade, or cancel anytime.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.green}`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    How accurate is the municipal data?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    Our AI-powered system achieves 95%+ accuracy by cross-referencing multiple official sources.
                    Every response includes confidence scores and source citations for verification.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.purple}`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    Can I change plans anytime?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately,
                    and we'll prorate any billing adjustments.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    What if I exceed my API call limit?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    We'll notify you when you reach 80% of your limit. If you exceed it, we'll temporarily
                    throttle requests but won't charge overage fees. Simply upgrade to continue.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    Do you offer custom enterprise solutions?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    Absolutely! Our Enterprise plan includes custom integrations, dedicated support,
                    and volume discounts. Contact our sales team for a personalized quote.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl text-center">
            <Card className={`${theme.card} ${theme.border.green}`}>
              <CardHeader>
                <CardTitle className="text-3xl font-orbitron font-bold bg-gradient-to-r from-white to-green-400 bg-clip-text text-transparent mb-4">
                  Ready to Get Started?
                </CardTitle>
                <CardDescription className={`text-lg ${theme.text.secondary} font-space`}>
                  Join thousands of developers who trust Ordrly for municipal research
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                  <Button size="lg" className={`${theme.button.success} px-8 py-4 text-lg font-exo`}>
                    <CheckCircle className="w-5 h-5 mr-3" />
                    Start Free Trial
                    <ArrowRight className="w-5 h-5 ml-3" />
                  </Button>
                  <Button variant="outline" size="lg" className={`${theme.button.secondary} px-8 py-4 text-lg font-exo`}>
                    <Eye className="w-5 h-5 mr-3" />
                    Schedule Demo
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center pt-6">
                  <div className={`${theme.text.secondary} font-space text-sm`}>
                    <CheckCircle className="w-4 h-4 text-green-400 mx-auto mb-1" />
                    7-day free trial
                  </div>
                  <div className={`${theme.text.secondary} font-space text-sm`}>
                    <CheckCircle className="w-4 h-4 text-green-400 mx-auto mb-1" />
                    No setup fees
                  </div>
                  <div className={`${theme.text.secondary} font-space text-sm`}>
                    <CheckCircle className="w-4 h-4 text-green-400 mx-auto mb-1" />
                    Cancel anytime
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
    </PageLayout>
  )
}
