"use client"

import React from 'react'
import Link from 'next/link'
import { ArrowLeft, Shield, Lock, Eye, Database, Globe, Terminal } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { PageLayout } from '@/components/ui/page-layout'
import { OrdrlyLogoSVG } from '@/components/ui/ordrly-logo'

// Sophisticated dark theme inspired by TJ logo (black/white/blue/green)
const theme = {
  container: "relative z-10 container mx-auto px-6",
  card: "glass-morphism hover-lift",
  text: {
    primary: "text-white",
    secondary: "text-gray-400",
    accent: "text-blue-400",
    success: "text-green-400"
  },
  border: {
    blue: "border-blue-500/30",
    green: "border-green-500/30"
  }
}

export default function PrivacyPage() {
  return (
    <PageLayout showBackButton={true} backButtonText="Back to Home" backButtonHref="/">
      {/* Main Content */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl">
            {/* Hero */}
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-4 mb-8">
                <OrdrlyLogoSVG size="xl" className="w-20 h-20" />
                <div className="text-left">
                  <h1 className="text-5xl md:text-6xl font-orbitron font-bold bg-gradient-to-r from-white via-green-200 to-green-400 bg-clip-text text-transparent">
                    Privacy Policy
                  </h1>
                  <div className="text-xl md:text-2xl font-exo font-light text-blue-400 mt-2">
                    Your Data, Protected
                  </div>
                </div>
              </div>
              
              <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto leading-relaxed mb-8`}>
                We take your privacy seriously. This policy explains how we collect, use, and protect your data
                when you use our municipal research API.
              </p>
              
              <div className={`text-sm ${theme.text.secondary} font-space`}>
                Last updated: December 22, 2024
              </div>
            </div>

            {/* Privacy Sections */}
            <div className="space-y-8">
              <Card className={`${theme.card} ${theme.border.blue}`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Database className="w-6 h-6 text-blue-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      Information We Collect
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Account Information</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      When you create an account, we collect your email address, name, and billing information. 
                      This information is necessary to provide our services and process payments.
                    </p>
                  </div>
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>API Usage Data</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      We collect information about your API usage, including request timestamps, endpoints accessed, 
                      response times, and error rates. This helps us improve our service and provide usage analytics.
                    </p>
                  </div>
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Municipal Research Queries</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      We store your research queries and results to improve our AI models and provide caching benefits. 
                      Queries are anonymized and aggregated for analysis purposes.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.green}`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Lock className="w-6 h-6 text-green-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      How We Use Your Information
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className={`${theme.text.secondary} font-space`}>
                        <strong className={theme.text.primary}>Service Provision:</strong> To provide municipal research services, 
                        process API requests, and maintain your account.
                      </p>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className={`${theme.text.secondary} font-space`}>
                        <strong className={theme.text.primary}>Billing & Support:</strong> To process payments, 
                        send invoices, and provide customer support.
                      </p>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className={`${theme.text.secondary} font-space`}>
                        <strong className={theme.text.primary}>Service Improvement:</strong> To analyze usage patterns, 
                        improve our AI models, and enhance service performance.
                      </p>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className={`${theme.text.secondary} font-space`}>
                        <strong className={theme.text.primary}>Communication:</strong> To send important service updates, 
                        security alerts, and respond to your inquiries.
                      </p>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-purple-500/30`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Eye className="w-6 h-6 text-purple-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      Data Sharing & Disclosure
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    We do not sell, trade, or rent your personal information to third parties. We may share your 
                    information only in the following circumstances:
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className={`${theme.text.secondary} font-space`}>
                        <strong className={theme.text.primary}>Service Providers:</strong> With trusted third-party services 
                        that help us operate our platform (payment processing, hosting, analytics).
                      </p>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className={`${theme.text.secondary} font-space`}>
                        <strong className={theme.text.primary}>Legal Requirements:</strong> When required by law, 
                        court order, or to protect our rights and safety.
                      </p>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className={`${theme.text.secondary} font-space`}>
                        <strong className={theme.text.primary}>Business Transfers:</strong> In connection with a merger, 
                        acquisition, or sale of assets (with prior notice).
                      </p>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-yellow-500/30`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Globe className="w-6 h-6 text-yellow-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      Data Security & Retention
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Security Measures</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      We implement industry-standard security measures including encryption in transit and at rest, 
                      regular security audits, and SOC 2 compliance to protect your data.
                    </p>
                  </div>
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Data Retention</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      We retain your data for as long as your account is active or as needed to provide services. 
                      You can request data deletion at any time by contacting our support team.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                    Your Rights & Choices
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    You have the right to access, update, or delete your personal information. You can also:
                  </p>
                  <ul className="space-y-2">
                    <li className={`${theme.text.secondary} font-space`}>• Request a copy of your data</li>
                    <li className={`${theme.text.secondary} font-space`}>• Correct inaccurate information</li>
                    <li className={`${theme.text.secondary} font-space`}>• Delete your account and associated data</li>
                    <li className={`${theme.text.secondary} font-space`}>• Opt out of non-essential communications</li>
                  </ul>
                  <p className={`${theme.text.secondary} font-space leading-relaxed mt-4`}>
                    To exercise these rights, please contact <NAME_EMAIL>
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.blue}`}>
                <CardHeader>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    If you have questions about this Privacy Policy or our data practices, please contact us:
                  </p>
                  <div className="mt-4 space-y-2">
                    <p className={`${theme.text.primary} font-space`}>Email: <EMAIL></p>
                    <p className={`${theme.text.primary} font-space`}>Address: [Your Business Address]</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
    </PageLayout>
  )
}
