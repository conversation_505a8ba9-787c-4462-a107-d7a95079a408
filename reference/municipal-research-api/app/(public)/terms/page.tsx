"use client"

import React from 'react'
import Link from 'next/link'
import { ArrowLeft, FileText, Scale, AlertTriangle, Database, Globe, Terminal } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { PageLayout } from '@/components/ui/page-layout'
import { OrdrlyLogoSVG } from '@/components/ui/ordrly-logo'

// Sophisticated dark theme inspired by TJ logo (black/white/blue/green)
const theme = {
  container: "relative z-10 container mx-auto px-6",
  card: "glass-morphism hover-lift",
  text: {
    primary: "text-white",
    secondary: "text-gray-400",
    accent: "text-blue-400",
    success: "text-green-400",
    warning: "text-yellow-400"
  },
  border: {
    blue: "border-blue-500/30",
    green: "border-green-500/30",
    yellow: "border-yellow-500/30"
  }
}

export default function TermsPage() {
  return (
    <PageLayout showBackButton={true} backButtonText="Back to Home" backButtonHref="/" minimalFooter={true}>
      {/* Main Content */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl">
            {/* Hero */}
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-4 mb-8">
                <OrdrlyLogoSVG size="xl" className="w-20 h-20" />
                <div className="text-left">
                  <h1 className="text-5xl md:text-6xl font-orbitron font-bold bg-gradient-to-r from-white via-blue-200 to-blue-400 bg-clip-text text-transparent">
                    Terms of Service
                  </h1>
                  <div className="text-xl md:text-2xl font-exo font-light text-green-400 mt-2">
                    Legal Framework
                  </div>
                </div>
              </div>
              
              <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto leading-relaxed mb-8`}>
                These terms govern your use of the Ordrly municipal research API and related services.
                Please read them carefully before using our platform.
              </p>
              
              <div className={`text-sm ${theme.text.secondary} font-space`}>
                Last updated: December 22, 2024
              </div>
            </div>

            {/* Terms Sections */}
            <div className="space-y-8">
              <Card className={`${theme.card} ${theme.border.blue}`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <FileText className="w-6 h-6 text-blue-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      Acceptance of Terms
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    By accessing or using the Ordrly service, you agree to be bound by these Terms of Service
                    and all applicable laws and regulations. If you do not agree with any of these terms,
                    you are prohibited from using or accessing this service.
                  </p>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    We reserve the right to modify these terms at any time. Changes will be effective immediately 
                    upon posting. Your continued use of the service constitutes acceptance of the modified terms.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.green}`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Database className="w-6 h-6 text-green-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      API Usage & Limitations
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Permitted Use</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      You may use our API for legitimate municipal research purposes, including compliance checking, 
                      zoning analysis, and building code research. Commercial use is permitted under paid plans.
                    </p>
                  </div>
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Usage Limits</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      API usage is subject to rate limits and monthly quotas based on your subscription plan. 
                      Exceeding limits may result in temporary throttling or service suspension.
                    </p>
                  </div>
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Prohibited Activities</h3>
                    <ul className="space-y-2">
                      <li className={`${theme.text.secondary} font-space`}>• Attempting to reverse engineer or copy our AI models</li>
                      <li className={`${theme.text.secondary} font-space`}>• Using the service for illegal or fraudulent purposes</li>
                      <li className={`${theme.text.secondary} font-space`}>• Sharing API keys or account credentials</li>
                      <li className={`${theme.text.secondary} font-space`}>• Overwhelming our systems with excessive requests</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-purple-500/30`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Globe className="w-6 h-6 text-purple-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      Data Accuracy & Disclaimers
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className={`bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4`}>
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className={`font-exo font-semibold ${theme.text.warning} mb-2`}>Important Notice</h4>
                        <p className={`${theme.text.secondary} font-space text-sm leading-relaxed`}>
                          While we strive for accuracy, municipal regulations change frequently. Always verify 
                          critical information with official sources before making important decisions.
                        </p>
                      </div>
                    </div>
                  </div>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Our service provides research assistance and should not be considered legal advice. 
                    We make no warranties about the completeness, accuracy, or timeliness of the information provided. 
                    Users are responsible for verifying all information with appropriate authorities.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.yellow}`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Scale className="w-6 h-6 text-yellow-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      Payment & Billing
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Subscription Plans</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      Paid plans are billed monthly or annually in advance. All fees are non-refundable except 
                      as required by law or as specifically stated in our refund policy.
                    </p>
                  </div>
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Price Changes</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      We reserve the right to modify pricing with 30 days' notice. Existing subscribers will 
                      be grandfathered at their current rate for the remainder of their billing cycle.
                    </p>
                  </div>
                  <div>
                    <h3 className={`font-exo font-semibold ${theme.text.primary} mb-2`}>Cancellation</h3>
                    <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                      You may cancel your subscription at any time. Service will continue until the end of 
                      your current billing period. No partial refunds are provided for unused time.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-red-500/30`}>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="w-6 h-6 text-red-400" />
                    <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                      Limitation of Liability
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    To the maximum extent permitted by law, Ordrly shall not be liable for any indirect,
                    incidental, special, consequential, or punitive damages, including but not limited to
                    loss of profits, data, or business opportunities.
                  </p>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Our total liability for any claims arising from or related to this service shall not 
                    exceed the amount you paid us in the twelve months preceding the claim.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                    Termination
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    We may terminate or suspend your account immediately, without prior notice, for conduct 
                    that we believe violates these Terms of Service or is harmful to other users, us, or third parties.
                  </p>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Upon termination, your right to use the service will cease immediately. We may delete 
                    your account data after a reasonable grace period, subject to our data retention policies.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.blue}`}>
                <CardHeader>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    If you have questions about these Terms of Service, please contact us:
                  </p>
                  <div className="mt-4 space-y-2">
                    <p className={`${theme.text.primary} font-space`}>Email: <EMAIL></p>
                    <p className={`${theme.text.primary} font-space`}>Address: [Your Business Address]</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
    </PageLayout>
  )
}
