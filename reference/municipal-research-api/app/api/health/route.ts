import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Proxy to the Express API server
    const response = await fetch('http://localhost:3001/health')
    const data = await response.json()
    
    return NextResponse.json(data)
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Unable to connect to API server',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
