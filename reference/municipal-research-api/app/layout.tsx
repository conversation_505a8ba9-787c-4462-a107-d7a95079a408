import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { cn } from '@/lib/utils'
import { ThemeProvider } from '@/components/theme-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Ordrly - Municipal Research API',
  description: 'Fast, accurate municipal research and compliance data API',
  keywords: ['municipal research', 'compliance', 'API', 'regulations', 'zoning'],
  authors: [{ name: 'Ordrly Team' }],
  creator: 'Ordr<PERSON>',
  publisher: 'Ordrly',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ordrly.ai',
    title: 'Ordrly - Municipal Research API',
    description: 'Fast, accurate municipal research and compliance data API',
    siteName: 'Ordrly',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ordrly - Municipal Research API',
    description: 'Fast, accurate municipal research and compliance data API',
    creator: '@ordrly',
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(
        inter.className,
        "min-h-screen bg-background font-sans antialiased"
      )}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          forcedTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">
              {children}
            </div>
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
