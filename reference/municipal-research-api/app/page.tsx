"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  ArrowRight, Zap, Shield, Clock, Database, Globe, Terminal,
  Code, Layers, Search, Cpu, Eye, BarChart3, CheckCircle,
  Star, Users, TrendingUp, Lock, Sparkles, Rocket
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { PageLayout } from '@/components/ui/page-layout'
import { OrdrlyLogoSVG } from '@/components/ui/ordrly-logo'

// Sophisticated dark theme inspired by TJ logo (black/white/blue/green)
const theme = {
  container: "relative z-10 container mx-auto px-6",
  card: "glass-morphism hover-lift",
  text: {
    primary: "text-white",
    secondary: "text-gray-400",
    accent: "text-blue-400",
    success: "text-green-400"
  },
  button: {
    primary: "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white border border-blue-500/50",
    secondary: "border-gray-600/50 text-gray-300 hover:bg-gray-800/20",
    success: "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white"
  },
  border: {
    blue: "border-blue-500/30",
    green: "border-green-500/30",
    purple: "border-purple-500/30"
  }
}

// Animated counter component
const AnimatedCounter = ({ end, duration = 2000 }: { end: number, duration?: number }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      setCount(Math.floor(progress * end))
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    requestAnimationFrame(animate)
  }, [end, duration])

  return <span>{count.toLocaleString()}</span>
}

export default function HomePage() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <PageLayout>
      {/* Hero Section */}
        <section className={`${theme.container} py-24 md:py-32`}>
          <div className={`mx-auto max-w-6xl text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center gap-4 mb-8">
              <OrdrlyLogoSVG size="xl" className="w-20 h-20" />
              <div className="text-left">
                <h1 className="text-5xl md:text-7xl font-orbitron font-bold bg-gradient-to-r from-white via-blue-200 to-blue-400 bg-clip-text text-transparent">
                  Municipal Research
                </h1>
                <div className="text-2xl md:text-3xl font-exo font-light text-green-400 mt-2">
                  Powered by Ordrly
                </div>
              </div>
            </div>

            <p className={`text-xl md:text-2xl ${theme.text.secondary} font-space max-w-4xl mx-auto leading-relaxed mb-12`}>
              Transform municipal research with our cutting-edge API. Get instant access to zoning laws,
              building codes, and compliance data across thousands of jurisdictions.
              <span className="text-blue-400"> Built for developers, trusted by professionals.</span>
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
              <Button size="lg" className={`${theme.button.primary} px-8 py-4 text-lg font-exo`}>
                <Rocket className="w-5 h-5 mr-3" />
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-3" />
              </Button>
              <Button variant="outline" size="lg" className={`${theme.button.secondary} px-8 py-4 text-lg font-exo`}>
                <Code className="w-5 h-5 mr-3" />
                View API Docs
              </Button>
            </div>

            {/* Live Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-orbitron font-bold text-blue-400 mb-2">
                  <AnimatedCounter end={50000} />+
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>API Calls Processed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-orbitron font-bold text-green-400 mb-2">
                  <AnimatedCounter end={2500} />+
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>Jurisdictions Covered</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-orbitron font-bold text-purple-400 mb-2">
                  <AnimatedCounter end={99} />.9%
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>Uptime Guarantee</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-orbitron font-bold text-yellow-400 mb-2">
                  &lt;<AnimatedCounter end={10} />s
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>Average Response</div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-orbitron font-bold bg-gradient-to-r from-white to-blue-400 bg-clip-text text-transparent mb-6 leading-tight pb-2">
                Why Choose Ordrly?
              </h2>
              <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto`}>
                Municipal research technology powered by Ordrly that delivers accurate, real-time data
                with enterprise-grade reliability and developer-friendly integration.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <Card className={`${theme.card} ${theme.border.blue} text-center`}>
                <CardHeader>
                  <div className="w-16 h-16 bg-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 quantum-glow">
                    <Zap className="w-8 h-8 text-blue-400" />
                  </div>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Lightning Fast</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Get municipal data in seconds, not hours. Our optimized API delivers
                    comprehensive research results with sub-10 second response times.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.green} text-center`}>
                <CardHeader>
                  <div className="w-16 h-16 bg-green-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-green-400" />
                  </div>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Highly Accurate</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Research powered by Ordrly with verified sources and official documentation.
                    Every result includes citations and confidence scores for reliability.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.purple} text-center`}>
                <CardHeader>
                  <div className="w-16 h-16 bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-purple-400" />
                  </div>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Always Updated</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Real-time data updates ensure you always have the latest municipal
                    information. Our system monitors changes across all jurisdictions.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Advanced Features Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Globe className="w-6 h-6 text-cyan-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>Global Coverage</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    2,500+ jurisdictions across North America with expanding international coverage.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Code className="w-6 h-6 text-emerald-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>Developer First</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    RESTful API with comprehensive documentation, SDKs, and code examples.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Lock className="w-6 h-6 text-red-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>Enterprise Security</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    SOC 2 compliant with end-to-end encryption and enterprise-grade security.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <BarChart3 className="w-6 h-6 text-orange-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>Real-time Analytics</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    Detailed usage analytics, performance metrics, and cost optimization insights.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Layers className="w-6 h-6 text-indigo-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>Smart Caching</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    Intelligent caching system reduces costs and improves response times over time.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-gray-600/30`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Sparkles className="w-6 h-6 text-pink-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>AI-Powered</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    Advanced AI models trained specifically for municipal research and compliance.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Social Proof Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-6xl">
            <Card className={`${theme.card} ${theme.border.blue} text-center`}>
              <CardHeader>
                <CardTitle className={`font-orbitron text-3xl ${theme.text.primary} mb-4`}>
                  Trusted by Industry Leaders
                </CardTitle>
                <CardDescription className={`text-lg ${theme.text.secondary} font-space`}>
                  Join thousands of developers and professionals who rely on our municipal research API
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    </div>
                    <p className={`${theme.text.secondary} font-space text-sm`}>
                      "Ordrly transformed our municipal research workflow. What used to take hours now takes minutes."
                    </p>
                    <div className={`${theme.text.accent} font-exo text-sm mt-2`}>
                      - Senior Developer, PropTech Startup
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <Users className="w-6 h-6 text-blue-400" />
                      <span className="text-2xl font-orbitron font-bold text-blue-400">500+</span>
                    </div>
                    <p className={`${theme.text.secondary} font-space text-sm`}>
                      Active developers using our API for municipal research and compliance automation.
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <TrendingUp className="w-6 h-6 text-green-400" />
                      <span className="text-2xl font-orbitron font-bold text-green-400">95%</span>
                    </div>
                    <p className={`${theme.text.secondary} font-space text-sm`}>
                      Customer satisfaction rate with our accuracy and response times.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl text-center">
            <div className="mb-8">
              <h2 className="text-4xl md:text-5xl font-orbitron font-bold bg-gradient-to-r from-white via-blue-200 to-green-400 bg-clip-text text-transparent mb-6">
                Ready to Transform Your Municipal Research?
              </h2>
              <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto leading-relaxed`}>
                Join the future of municipal research. Start with our free trial and experience
                the power of AI-driven compliance data at your fingertips.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12">
              <Button size="lg" className={`${theme.button.success} px-10 py-4 text-lg font-exo`}>
                <CheckCircle className="w-5 h-5 mr-3" />
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-3" />
              </Button>
              <Button variant="outline" size="lg" className={`${theme.button.secondary} px-10 py-4 text-lg font-exo`}>
                <Eye className="w-5 h-5 mr-3" />
                View Live Demo
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className={`${theme.text.secondary} font-space`}>
                <CheckCircle className="w-5 h-5 text-green-400 mx-auto mb-2" />
                <span className="text-sm">No credit card required</span>
              </div>
              <div className={`${theme.text.secondary} font-space`}>
                <CheckCircle className="w-5 h-5 text-green-400 mx-auto mb-2" />
                <span className="text-sm">7-day free trial</span>
              </div>
              <div className={`${theme.text.secondary} font-space`}>
                <CheckCircle className="w-5 h-5 text-green-400 mx-auto mb-2" />
                <span className="text-sm">Cancel anytime</span>
              </div>
            </div>
          </div>
        </section>
    </PageLayout>
  )
}
