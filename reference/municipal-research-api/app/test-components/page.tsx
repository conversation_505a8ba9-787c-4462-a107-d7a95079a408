"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import {
  Search, Zap, Database, Shield, Cpu, Globe, Terminal, Code, Layers,
  ArrowRight, Play, Pause, RotateCcw, Settings, Download, Upload,
  Eye, EyeOff, Lock, Unlock, Heart, Star, Bookmark, Share, Copy,
  Check, X, Plus, Minus, Edit, Trash2, Save, RefreshCw
} from 'lucide-react'

// Sophisticated dark theme inspired by TJ logo (black/white/blue/green)
const theme = {
  page: "min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black relative overflow-hidden",
  container: "relative z-10 container mx-auto px-6 py-12",
  card: "glass-morphism hover-lift",
  text: {
    primary: "text-white",
    secondary: "text-gray-400",
    accent: "text-blue-400"
  },
  button: {
    red: "border-red-500/50 text-red-400 hover:bg-red-500/20",
    green: "border-green-500/50 text-green-400 hover:bg-green-500/20",
    yellow: "border-amber-500/50 text-amber-400 hover:bg-amber-500/20",
    blue: "border-blue-500/50 text-blue-400 hover:bg-blue-500/20",
    purple: "border-purple-500/50 text-purple-400 hover:bg-purple-500/20",
    teal: "border-teal-500/50 text-teal-400 hover:bg-teal-500/20"
  },
  border: {
    blue: "border-blue-500/30",
    purple: "border-purple-500/30",
    pink: "border-pink-500/30",
    green: "border-green-500/30",
    teal: "border-teal-500/30"
  },
  input: "bg-gray-800/50 border-blue-600/50 text-white placeholder:text-gray-500"
}

// Floating orb component
const FloatingOrb = ({ size, color, position, delay }: {
  size: string, color: string, position: string, delay?: string
}) => (
  <div
    className={`absolute ${position} ${size} ${color} rounded-full blur-xl neural-pulse`}
    style={delay ? { animationDelay: delay } : undefined}
  />
)

export default function ComponentTestPage() {
  const [inputValue, setInputValue] = useState('')
  const [isPlaying, setIsPlaying] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [isLocked, setIsLocked] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [counter, setCounter] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  const handleAsyncAction = async () => {
    setIsLoading(true)
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
  }

  return (
    <div className={theme.page}>
      {/* Background Grid */}
      <div className="absolute inset-0 grid-pattern opacity-20" />

      {/* Floating Orbs */}
      <FloatingOrb size="w-32 h-32" color="bg-gray-500/10" position="top-20 left-20" />
      <FloatingOrb size="w-24 h-24" color="bg-blue-500/15" position="top-40 right-32" delay="1s" />
      <FloatingOrb size="w-40 h-40" color="bg-slate-500/10" position="bottom-32 left-1/3" delay="2s" />

      <div className={theme.container}>
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-black border-2 border-blue-400 rounded-xl flex items-center justify-center quantum-glow">
              <Terminal className="w-6 h-6 text-blue-400" />
            </div>
            <h1 className="text-4xl font-orbitron font-bold bg-gradient-to-r from-white via-gray-200 to-blue-400 bg-clip-text text-transparent">
              Component Testing Matrix
            </h1>
          </div>
          <p className={`text-xl ${theme.text.secondary} font-space max-w-2xl mx-auto`}>
            Advanced UI component testing environment with real-time interaction monitoring
          </p>
        </div>

        {/* Control Panel */}
        <Card className={`mb-12 ${theme.card} ${theme.border.blue}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Settings className="w-5 h-5 text-teal-400" />
                <CardTitle className={`font-exo ${theme.text.primary}`}>System Control Panel</CardTitle>
              </div>
              <ThemeToggle />
            </div>
            <CardDescription className={theme.text.secondary}>
              Master control interface for component state management
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <label className={`text-sm font-medium ${theme.text.accent}`}>Input Testing</label>
                <Input
                  placeholder="Enter test data..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  className={theme.input}
                />
              </div>
              <div className="space-y-2">
                <label className={`text-sm font-medium ${theme.text.accent}`}>Counter: {counter}</label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => setCounter(c => c - 1)} className={theme.button.red}>
                    <Minus className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setCounter(c => c + 1)} className={theme.button.green}>
                    <Plus className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setCounter(0)} className={theme.button.yellow}>
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <label className={`text-sm font-medium ${theme.text.accent}`}>Async Operations</label>
                <Button
                  onClick={handleAsyncAction}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Execute
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Button Variants Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <Card className={`${theme.card} ${theme.border.purple}`}>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Cpu className="w-5 h-5 text-purple-400" />
                <CardTitle className={`font-exo ${theme.text.primary}`}>Primary Actions</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 border border-blue-500/50 text-white">
                  <Play className="w-4 h-4 mr-2" />
                  Execute
                </Button>
                <Button variant="destructive" className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600">
                  <X className="w-4 h-4 mr-2" />
                  Terminate
                </Button>
                <Button variant="outline" className={theme.button.green}>
                  <Check className="w-4 h-4 mr-2" />
                  Confirm
                </Button>
                <Button variant="outline" className={theme.button.yellow}>
                  <Edit className="w-4 h-4 mr-2" />
                  Modify
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className={`${theme.card} ${theme.border.pink}`}>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Database className="w-5 h-5 text-pink-400" />
                <CardTitle className={`font-exo ${theme.text.primary}`}>Data Operations</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button variant="secondary" className="bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50">
                  <Download className="w-4 h-4 mr-2" />
                  Import
                </Button>
                <Button variant="secondary" className="bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50">
                  <Upload className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" className={theme.button.blue}>
                  <Save className="w-4 h-4 mr-2" />
                  Persist
                </Button>
                <Button variant="outline" className={theme.button.red}>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Purge
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* State Management Interface */}
        <Card className={`mb-12 ${theme.card} ${theme.border.green}`}>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Shield className="w-5 h-5 text-green-400" />
              <CardTitle className={`font-exo ${theme.text.primary}`}>State Management Interface</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center space-y-3">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setIsPlaying(!isPlaying)}
                  className={`w-full ${isPlaying ? theme.button.green : 'border-slate-500 text-slate-400'}`}
                >
                  {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                </Button>
                <p className={`text-sm ${theme.text.secondary}`}>Playback: {isPlaying ? 'Active' : 'Paused'}</p>
              </div>

              <div className="text-center space-y-3">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setIsVisible(!isVisible)}
                  className={`w-full ${isVisible ? theme.button.blue : 'border-slate-500 text-slate-400'}`}
                >
                  {isVisible ? <Eye className="w-5 h-5" /> : <EyeOff className="w-5 h-5" />}
                </Button>
                <p className={`text-sm ${theme.text.secondary}`}>Visibility: {isVisible ? 'Shown' : 'Hidden'}</p>
              </div>

              <div className="text-center space-y-3">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setIsLocked(!isLocked)}
                  className={`w-full ${isLocked ? theme.button.red : theme.button.green}`}
                >
                  {isLocked ? <Lock className="w-5 h-5" /> : <Unlock className="w-5 h-5" />}
                </Button>
                <p className={`text-sm ${theme.text.secondary}`}>Security: {isLocked ? 'Locked' : 'Unlocked'}</p>
              </div>

              <div className="text-center space-y-3">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setIsFavorited(!isFavorited)}
                  className={`w-full ${isFavorited ? theme.button.yellow : 'border-slate-500 text-slate-400'}`}
                >
                  {isFavorited ? <Star className="w-5 h-5 fill-current" /> : <Star className="w-5 h-5" />}
                </Button>
                <p className={`text-sm ${theme.text.secondary}`}>Status: {isFavorited ? 'Starred' : 'Normal'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Advanced Interaction Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mb-12">
          <Card className={`${theme.card} ${theme.border.blue} hologram-flicker`}>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Globe className="w-5 h-5 text-blue-400" />
                <CardTitle className={`font-exo ${theme.text.primary}`}>Network Operations</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <Button variant="outline" className={`w-full ${theme.button.blue}`}>
                  <Search className="w-4 h-4 mr-2" />
                  Scan Network
                </Button>
                <Button variant="outline" className={`w-full ${theme.button.blue}`}>
                  <Layers className="w-4 h-4 mr-2" />
                  Layer Analysis
                </Button>
                <Button variant="outline" className={`w-full ${theme.button.purple}`}>
                  <Code className="w-4 h-4 mr-2" />
                  Code Injection
                </Button>
              </div>
              <div className="mt-4 p-3 bg-slate-800/50 rounded-lg border border-blue-500/30">
                <div className="flex items-center justify-between text-sm">
                  <span className={theme.text.secondary}>Network Status:</span>
                  <span className="text-green-400 flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full neural-pulse"></div>
                    Online
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className={`${theme.card} ${theme.border.purple}`}>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Heart className="w-5 h-5 text-purple-400" />
                <CardTitle className={`font-exo ${theme.text.primary}`}>Social Actions</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  onClick={() => setIsFavorited(!isFavorited)}
                  className={isFavorited ? theme.button.red : 'border-slate-500 text-slate-400'}
                >
                  <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsBookmarked(!isBookmarked)}
                  className={isBookmarked ? theme.button.yellow : 'border-slate-500 text-slate-400'}
                >
                  <Bookmark className={`w-4 h-4 ${isBookmarked ? 'fill-current' : ''}`} />
                </Button>
                <Button variant="outline" className={theme.button.blue}>
                  <Share className="w-4 h-4" />
                </Button>
                <Button variant="outline" className={theme.button.green}>
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className={theme.text.secondary}>Likes:</span>
                  <span className="text-red-400">{isFavorited ? '1,337' : '1,336'}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className={theme.text.secondary}>Bookmarks:</span>
                  <span className="text-yellow-400">{isBookmarked ? '42' : '41'}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className={`${theme.card} ${theme.border.green}`}>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Terminal className="w-5 h-5 text-green-400" />
                <CardTitle className={`font-exo ${theme.text.primary}`}>Terminal Interface</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-black/50 rounded-lg p-4 font-mono text-sm border border-green-500/30">
                <div className="text-green-400">
                  <span className="text-cyan-400">user@ordrly</span>
                  <span className="text-white">:</span>
                  <span className="text-blue-400">~/test</span>
                  <span className="text-white">$ </span>
                  <span className="data-flow">_</span>
                </div>
                <div className="text-green-300 mt-2">
                  > Component test suite initialized
                </div>
                <div className="text-yellow-300">
                  > {inputValue || 'Awaiting input...'}
                </div>
              </div>
              <Button variant="outline" className={`w-full ${theme.button.green}`}>
                <ArrowRight className="w-4 h-4 mr-2" />
                Execute Command
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Real-time Metrics Dashboard */}
        <Card className={`mb-12 ${theme.card} ${theme.border.blue}`}>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Database className="w-5 h-5 text-blue-400" />
              <CardTitle className={`font-exo ${theme.text.primary}`}>Real-time Metrics Dashboard</CardTitle>
            </div>
            <CardDescription className={theme.text.secondary}>
              Live component interaction analytics and performance monitoring
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center space-y-2">
                <div className="text-3xl font-orbitron font-bold text-blue-400">
                  {counter}
                </div>
                <div className={`text-sm ${theme.text.secondary}`}>Counter Value</div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(Math.abs(counter) * 10, 100)}%` }}
                  />
                </div>
              </div>

              <div className="text-center space-y-2">
                <div className="text-3xl font-orbitron font-bold text-purple-400">
                  {inputValue.length}
                </div>
                <div className={`text-sm ${theme.text.secondary}`}>Input Length</div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-purple-400 to-pink-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(inputValue.length * 5, 100)}%` }}
                  />
                </div>
              </div>

              <div className="text-center space-y-2">
                <div className="text-3xl font-orbitron font-bold text-green-400">
                  {[isPlaying, isVisible, !isLocked, isFavorited, isBookmarked].filter(Boolean).length}
                </div>
                <div className={`text-sm ${theme.text.secondary}`}>Active States</div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${[isPlaying, isVisible, !isLocked, isFavorited, isBookmarked].filter(Boolean).length * 20}%` }}
                  />
                </div>
              </div>

              <div className="text-center space-y-2">
                <div className="text-3xl font-orbitron font-bold text-yellow-400">
                  {isLoading ? '∞' : '100'}
                </div>
                <div className={`text-sm ${theme.text.secondary}`}>System Health</div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      isLoading ? 'bg-gradient-to-r from-yellow-400 to-orange-500 animate-pulse' : 'bg-gradient-to-r from-green-400 to-emerald-500'
                    }`}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer Actions */}
        <div className="text-center space-y-6">
          <div className="flex flex-wrap justify-center gap-4">
            <Button size="lg" className="bg-gradient-to-r from-gray-800 to-black border border-blue-500/50 hover:border-blue-400 text-white hover:bg-gradient-to-r hover:from-gray-700 hover:to-gray-900 font-exo">
              <Zap className="w-5 h-5 mr-2 text-blue-400" />
              Initialize Full Test Suite
            </Button>
            <Button variant="outline" size="lg" className="border-gray-600/50 text-gray-300 hover:bg-gray-800/20 font-exo">
              <RefreshCw className="w-5 h-5 mr-2" />
              Reset All States
            </Button>
          </div>

          <div className={`${theme.text.secondary} text-sm font-space`}>
            Component Testing Matrix v2.1.0 • Real-time State Monitoring • Advanced UI Validation
          </div>
        </div>
      </div>
    </div>
  )
}
