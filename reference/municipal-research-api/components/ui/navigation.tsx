"use client"

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ArrowRight, Terminal, Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { OrdrlyLogoSVG } from '@/components/ui/ordrly-logo'
import { useState } from 'react'

// Sophisticated dark theme inspired by TJ logo (black/white/blue/green)
const theme = {
  text: {
    primary: "text-white",
    secondary: "text-gray-300",
    accent: "text-blue-400"
  },
  button: {
    primary: "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white border border-blue-500/50",
    secondary: "border-gray-600/50 text-gray-300 hover:bg-gray-800/20"
  }
}

interface NavigationProps {
  showBackButton?: boolean
  backButtonText?: string
  backButtonHref?: string
}

export function Navigation({ showBackButton = false, backButtonText = "Back to Home", backButtonHref = "/" }: NavigationProps) {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const navItems = [
    { href: "/", label: "Home", active: pathname === "/" },
    { href: "/pricing", label: "Pricing", active: pathname === "/pricing" },
    { href: "/docs", label: "API Docs", active: pathname === "/docs" },
    { href: "/chat", label: "Research Tool", active: pathname === "/chat" },
  ]

  return (
    <header className="relative z-20 border-b border-white/10 bg-black/20 backdrop-blur-md">
      <div className="container mx-auto px-6">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <OrdrlyLogoSVG size="lg" className="w-12 h-12" />
            <span className="font-orbitron font-bold text-xl text-white">Ordrly</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {!showBackButton ? (
              <>
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`font-exo transition-colors ${
                      item.active 
                        ? theme.text.accent 
                        : `${theme.text.secondary} hover:${theme.text.accent.replace('text-', 'text-')}`
                    }`}
                  >
                    {item.label}
                  </Link>
                ))}
                <Link href="/login" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-exo`}>
                  Sign In
                </Link>
                <Button className={theme.button.primary}>
                  Get Started
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </>
            ) : (
              <Link href={backButtonHref}>
                <Button variant="outline" className={theme.button.secondary}>
                  <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
                  {backButtonText}
                </Button>
              </Link>
            )}
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 text-gray-300 hover:text-blue-400 transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-white/10 bg-black/40 backdrop-blur-md">
            <nav className="py-4 space-y-2">
              {!showBackButton ? (
                <>
                  {navItems.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={`block px-4 py-2 font-exo transition-colors ${
                        item.active 
                          ? theme.text.accent 
                          : `${theme.text.secondary} hover:${theme.text.accent.replace('text-', 'text-')}`
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  ))}
                  <Link 
                    href="/login" 
                    className={`block px-4 py-2 ${theme.text.secondary} hover:text-blue-400 transition-colors font-exo`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <div className="px-4 py-2">
                    <Button className={`w-full ${theme.button.primary}`}>
                      Get Started
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </>
              ) : (
                <div className="px-4 py-2">
                  <Link href={backButtonHref}>
                    <Button variant="outline" className={`w-full ${theme.button.secondary}`}>
                      <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
                      {backButtonText}
                    </Button>
                  </Link>
                </div>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

interface FooterProps {
  minimal?: boolean
}

export function Footer({ minimal = false }: FooterProps) {
  if (minimal) {
    return (
      <footer className="relative z-10 border-t border-white/10 bg-black/20 backdrop-blur-md">
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <Link href="/" className="flex items-center space-x-3 mb-4 md:mb-0">
              <OrdrlyLogoSVG size="md" className="w-10 h-10" />
              <span className="font-orbitron font-bold text-lg text-white">Ordrly</span>
            </Link>
            <div className="flex space-x-6 text-sm">
              <Link href="/privacy" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space`}>
                Privacy Policy
              </Link>
              <Link href="/terms" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space`}>
                Terms of Service
              </Link>
              <Link href="/contact" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space`}>
                Contact
              </Link>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-white/10 text-center">
            <div className={`${theme.text.secondary} text-sm font-space`}>
              © 2024 Ordrly AI. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    )
  }

  return (
    <footer className="relative z-10 border-t border-white/10 bg-black/20 backdrop-blur-md">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <OrdrlyLogoSVG size="lg" className="w-12 h-12" />
              <span className="font-orbitron font-bold text-xl text-white">Ordrly</span>
            </div>
            <p className={`${theme.text.secondary} font-space text-sm leading-relaxed`}>
              Municipal research API powered by Ordrly. Transform your compliance workflow
              with accurate, real-time municipal data.
            </p>
          </div>

          <div>
            <h3 className={`font-exo font-semibold ${theme.text.primary} mb-4`}>Product</h3>
            <ul className="space-y-2">
              <li><Link href="/pricing" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Pricing</Link></li>
              <li><Link href="/docs" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>API Documentation</Link></li>
              <li><Link href="/chat" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Research Tool</Link></li>
              <li><Link href="/status" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>System Status</Link></li>
            </ul>
          </div>

          <div>
            <h3 className={`font-exo font-semibold ${theme.text.primary} mb-4`}>Company</h3>
            <ul className="space-y-2">
              <li><Link href="/about" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>About Us</Link></li>
              <li><Link href="/blog" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Blog</Link></li>
              <li><Link href="/careers" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Careers</Link></li>
              <li><Link href="/contact" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Contact</Link></li>
            </ul>
          </div>

          <div>
            <h3 className={`font-exo font-semibold ${theme.text.primary} mb-4`}>Legal</h3>
            <ul className="space-y-2">
              <li><Link href="/privacy" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Privacy Policy</Link></li>
              <li><Link href="/terms" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Terms of Service</Link></li>
              <li><Link href="/security" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Security</Link></li>
              <li><Link href="/compliance" className={`${theme.text.secondary} hover:text-blue-400 transition-colors font-space text-sm`}>Compliance</Link></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-white/10 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className={`${theme.text.secondary} text-sm font-space mb-4 md:mb-0`}>
              © 2025 Ordrly. All rights reserved. Built with precision for municipal research.
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full neural-pulse"></div>
                <span className={`text-green-400 text-sm font-space`}>All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
