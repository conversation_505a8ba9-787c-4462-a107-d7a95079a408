import React from 'react'
import Image from 'next/image'

interface OrdrlyLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  variant?: 'default' | 'bordered'
}

export function OrdrlyLogo({ size = 'md', className = '', variant = 'default' }: OrdrlyLogoProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-16 h-16'
  }

  if (variant === 'bordered') {
    return (
      <div className={`${sizeClasses[size]} ${className} relative flex items-center justify-center border-2 border-blue-400 rounded-xl quantum-glow bg-white/10 backdrop-blur-sm`}>
        <Image
          src="/logo.svg"
          alt="Ordrly AI Logo"
          width={64}
          height={64}
          className="w-full h-full object-contain p-1"
          priority
        />
      </div>
    )
  }

  return (
    <div className={`${sizeClasses[size]} ${className} relative flex items-center justify-center`}>
      <Image
        src="/logo.svg"
        alt="Ordrly AI Logo"
        width={64}
        height={64}
        className="w-full h-full object-contain"
        priority
      />
    </div>
  )
}

// Inline SVG version for better control and animations
export function OrdrlyLogoSVG({ size = 'md', className = '', variant = 'default' }: OrdrlyLogoProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-16 h-16'
  }

  const containerClass = variant === 'bordered'
    ? `${sizeClasses[size]} ${className} relative flex items-center justify-center border-2 border-blue-400 rounded-xl quantum-glow bg-white/10 backdrop-blur-sm`
    : `${sizeClasses[size]} ${className} relative flex items-center justify-center`

  return (
    <div className={containerClass}>
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 64 64"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={variant === 'bordered' ? 'p-1' : ''}
      >
        {/* Black magnifier ring */}
        <circle cx="32" cy="32" r="28" fill="#fff" stroke="#000" strokeWidth="8"/>

        {/* House in light blue (#1DA1F2) */}
        <rect x="22" y="30" width="20" height="16" fill="#1DA1F2" stroke="#000" strokeWidth="5"/>
        <path d="M32 18 L18 30 H46 Z" fill="#1DA1F2" stroke="#000" strokeWidth="5"/>
        <rect x="30" y="36" width="4" height="8" fill="#fff" stroke="#000" strokeWidth="4"/>

        {/* Green check with white border */}
        <path stroke="#fff" strokeWidth="9" strokeLinecap="round" strokeLinejoin="round" d="m34 48 7 7 14-18"/>
        <path stroke="#16C784" strokeWidth="7" strokeLinecap="round" strokeLinejoin="round" d="m34 48 7 7 14-18"/>

        {/* Crisp 1px white outline just outside the black ring */}
        <circle cx="32" cy="32" r="31.5" fill="none" stroke="#fff" strokeWidth="1" shapeRendering="crispEdges"/>
      </svg>
    </div>
  )
}
