"use client"

import React from 'react'
import { Navigation, Footer } from '@/components/ui/navigation'

// Sophisticated dark theme inspired by TJ logo (black/white/blue/green)
const theme = {
  page: "min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black relative overflow-hidden"
}

// Floating orb component
const FloatingOrb = ({ size, color, position, delay }: {
  size: string, color: string, position: string, delay?: string
}) => (
  <div
    className={`absolute ${position} ${size} ${color} rounded-full blur-xl neural-pulse`}
    style={delay ? { animationDelay: delay } : undefined}
  />
)

interface PageLayoutProps {
  children: React.ReactNode
  showBackButton?: boolean
  backButtonText?: string
  backButtonHref?: string
  minimalFooter?: boolean
  showFloatingOrbs?: boolean
}

export function PageLayout({ 
  children, 
  showBackButton = false, 
  backButtonText = "Back to Home", 
  backButtonHref = "/",
  minimalFooter = false,
  showFloatingOrbs = true
}: PageLayoutProps) {
  return (
    <div className={theme.page}>
      {/* Background Grid */}
      <div className="absolute inset-0 grid-pattern opacity-20" />

      {/* Floating Orbs */}
      {showFloatingOrbs && (
        <>
          <FloatingOrb size="w-32 h-32" color="bg-blue-500/10" position="top-20 left-20" />
          <FloatingOrb size="w-24 h-24" color="bg-green-500/15" position="top-40 right-32" delay="1s" />
          <FloatingOrb size="w-40 h-40" color="bg-slate-500/10" position="bottom-32 left-1/3" delay="2s" />
          <FloatingOrb size="w-28 h-28" color="bg-purple-500/10" position="bottom-20 right-20" delay="3s" />
        </>
      )}

      {/* Navigation */}
      <Navigation 
        showBackButton={showBackButton}
        backButtonText={backButtonText}
        backButtonHref={backButtonHref}
      />

      {/* Main Content */}
      <main className="relative z-10">
        {children}
      </main>

      {/* Footer */}
      <Footer minimal={minimalFooter} />
    </div>
  )
}
