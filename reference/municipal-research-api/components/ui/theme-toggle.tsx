"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"

import { <PERSON><PERSON> } from "@/components/ui/button"

export function ThemeToggle() {
  // Theme toggle disabled - dark mode only
  return (
    <Button
      variant="ghost"
      size="icon"
      disabled
      className="opacity-50 cursor-not-allowed"
    >
      <Moon className="h-[1.2rem] w-[1.2rem] text-teal-400" />
      <span className="sr-only">Dark mode (light mode disabled)</span>
    </Button>
  )
}
