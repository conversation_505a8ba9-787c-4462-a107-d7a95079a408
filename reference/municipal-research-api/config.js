// config.js
import dotenv from 'dotenv';

// Load environment variables immediately when this module is imported
dotenv.config();

function getConfig() {
  return {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: process.env.OPENAI_MODEL || 'gpt-4.1-nano',
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 32768,
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.1
    },
    google: {
      searchApiKey: process.env.GOOGLE_SEARCH_API_KEY,
      searchEngineId: process.env.GOOGLE_SEARCH_ENGINE_ID,
      geocodingApiKey: process.env.GOOGLE_GEOCODING_API_KEY
    },
    geoapify: {
      apiKey: process.env.GEOAPIFY_API_KEY
    },
    server: {
      port: process.env.PORT || 3001,
      timeout: parseInt(process.env.REQUEST_TIMEOUT) || 10000
    },
    cache: {
      filePath: process.env.CACHE_FILE_PATH || './data/research_cache.csv',
      maxAge: parseInt(process.env.CACHE_MAX_AGE) || 86400000 // 24 hours in ms
    }
  };
}

export const config = getConfig();

// Validate required environment variables
export function validateConfig() {
  const required = [
    'OPENAI_API_KEY',
    'GOOGLE_SEARCH_API_KEY',
    'GOOGLE_SEARCH_ENGINE_ID',
    'GOOGLE_GEOCODING_API_KEY'
  ];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    console.warn(`⚠️ Missing required environment variables: ${missing.join(', ')}`);
    console.warn('📝 Please check your .env file and ensure all required API keys are set');
    console.warn('💡 See .env.example for the required format');
    return false;
  }

  console.log('✅ Configuration validated successfully');
  console.log('🔑 API Keys loaded:');
  console.log(`   OpenAI: ${config.openai.apiKey?.substring(0, 10)}...`);
  console.log(`   Google Search: ${config.google.searchApiKey?.substring(0, 10)}...`);
  console.log(`   Google Geocoding: ${config.google.geocodingApiKey?.substring(0, 10)}...`);
  console.log(`   Search Engine ID: ${config.google.searchEngineId}`);

  // Check for optional two-stage research keys
  const perplexityKey = process.env.PERPLEXITY_API_KEY;
  const geminiKey = process.env.GEMINI_API_KEY;

  if (perplexityKey && geminiKey) {
    console.log('🚀 Two-Stage Research APIs available:');
    console.log(`   Perplexity: ${perplexityKey.substring(0, 10)}...`);
    console.log(`   Gemini 2.5 Flash-Lite: ${geminiKey.substring(0, 10)}...`);
    console.log('   Cost-optimized research mode enabled!');
  } else {
    console.log('📝 Two-Stage Research APIs not configured (optional)');
    console.log('   Add PERPLEXITY_API_KEY and GEMINI_API_KEY for cost-optimized research');
  }

  return true;
}
