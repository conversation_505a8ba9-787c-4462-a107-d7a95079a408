# Municipal Research API Documentation

## Overview

The Municipal Research API provides comprehensive municipal ordinance and regulation research using a two-stage AI pipeline. The API delivers professional-grade municipal compliance information with official sources and contact details.

## Base URL

```
Development: http://localhost:3001
Production: https://api.ordrly.ai
```

## Authentication

All API requests require authentication using an API key in the request header:

```http
X-API-Key: your_api_key_here
```

### Getting an API Key

1. **Production API Keys**: Contact support or use the dashboard
2. **Trial Codes**: Use the trial code system for testing

## Rate Limits

- **Default**: 1,000 requests per hour per API key
- **Trial Keys**: Limited based on trial configuration
- Rate limit headers are included in all responses:
  - `X-RateLimit-Limit`: Total requests allowed per hour
  - `X-RateLimit-Remaining`: Requests remaining in current window
  - `X-RateLimit-Reset`: When the rate limit resets

## Endpoints

### Research

#### POST /api/v1/research

Perform municipal research for a specific address and query.

**Request Body:**
```json
{
  "address": "123 Main St, Georgetown, MI",
  "query": "fence height regulations",
  "metadata": {
    "userAgent": "MyApp/1.0",
    "referrer": "https://myapp.com"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jurisdiction": "Georgetown Township, MI",
    "topic": "fence-height-regulations",
    "answer": "Comprehensive municipal research answer...",
    "sources": [
      "https://georgetown-mi.gov/ordinances",
      "https://georgetown-mi.gov/building-dept"
    ],
    "confidence": 0.95,
    "cached": false,
    "processingTimeMs": 8500,
    "method": "perplexity-gemini"
  },
  "meta": {
    "requestId": "req_1234567890_abc123",
    "timestamp": "2025-06-22T00:32:50.000Z",
    "apiVersion": "1.0"
  }
}
```

#### POST /api/v1/research/bulk

Perform multiple research queries in a single request (max 10 queries).

**Request Body:**
```json
{
  "requests": [
    {
      "address": "123 Main St, Georgetown, MI",
      "query": "fence height regulations"
    },
    {
      "address": "456 Oak Ave, Grand Rapids, MI",
      "query": "setback requirements"
    }
  ]
}
```

#### GET /api/v1/research/topics

Get available research topics and categories.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "topic_key": "fence-height-regulations",
      "display_name": "Fence Height Regulations",
      "description": "Municipal regulations governing fence heights and setbacks",
      "category": "zoning",
      "usage_count": 150
    }
  ]
}
```

### Authentication

#### POST /api/v1/auth/api-keys

Create a new API key (requires admin access).

#### POST /api/v1/auth/trial-codes

Generate a trial code for testing.

#### POST /api/v1/auth/trial-codes/activate

Activate a trial code and receive a temporary API key.

### Analytics

#### GET /api/v1/analytics/usage

Get usage statistics for your API key.

**Query Parameters:**
- `days` (optional): Number of days to include (default: 30, max: 365)

#### GET /api/v1/analytics/billing

Get billing information and cost breakdown.

**Query Parameters:**
- `month` (optional): Specific month in YYYY-MM format

#### GET /api/v1/analytics/performance

Get performance metrics including response times and success rates.

#### GET /api/v1/analytics/summary

Get a comprehensive summary of usage, performance, and recommendations.

### Admin (Admin API Keys Only)

#### GET /api/v1/admin/stats

Get comprehensive system statistics.

#### GET /api/v1/admin/api-keys

List all API keys with usage information.

#### PUT /api/v1/admin/api-keys/:id

Update API key settings.

#### DELETE /api/v1/admin/api-keys/:id

Deactivate an API key.

## Error Handling

The API uses standard HTTP status codes and returns detailed error information:

```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Address must be at least 5 characters",
  "details": {
    "field": "address",
    "code": "too_small"
  }
}
```

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid or missing API key)
- `403` - Forbidden (insufficient permissions)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

## Response Format

All API responses follow a consistent format:

```json
{
  "success": boolean,
  "data": object | array,
  "meta": {
    "timestamp": "ISO 8601 timestamp",
    "requestId": "unique request identifier",
    "apiVersion": "1.0"
  },
  "error": "error_code (only on failure)",
  "message": "human readable message (only on failure)"
}
```

## Caching

The API implements intelligent caching based on topic and jurisdiction:

- **Cache Duration**: 24 hours for most queries
- **Cache Key**: Based on extracted topic and jurisdiction
- **Cache Indicators**: `cached: true/false` in response
- **Cost Savings**: Cached responses have minimal cost

## Pricing

- **Fresh Research**: ~$0.005-0.015 per query (varies by complexity)
- **Cached Results**: ~$0.0001 per query
- **Bulk Discounts**: Available for high-volume usage

## SDKs and Examples

### cURL Example

```bash
curl -X POST https://api.ordrly.ai/api/v1/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key_here" \
  -d '{
    "address": "123 Main St, Georgetown, MI",
    "query": "fence height regulations"
  }'
```

### JavaScript Example

```javascript
const response = await fetch('https://api.ordrly.ai/api/v1/research', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your_api_key_here'
  },
  body: JSON.stringify({
    address: '123 Main St, Georgetown, MI',
    query: 'fence height regulations'
  })
});

const result = await response.json();
console.log(result.data.answer);
```

### Python Example

```python
import requests

response = requests.post(
    'https://api.ordrly.ai/api/v1/research',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'your_api_key_here'
    },
    json={
        'address': '123 Main St, Georgetown, MI',
        'query': 'fence height regulations'
    }
)

result = response.json()
print(result['data']['answer'])
```

## Support

- **Documentation**: https://docs.ordrly.ai
- **Support Email**: <EMAIL>
- **Status Page**: https://status.ordrly.ai

## Changelog

### v1.0.0 (2025-06-22)
- Initial release
- Two-stage research pipeline
- Topic-based caching
- Comprehensive analytics
- Trial code system
