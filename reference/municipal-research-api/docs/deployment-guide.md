# Municipal Research API - Deployment Guide

## Overview

This guide covers deploying the Municipal Research API to production environments.

## Prerequisites

- Node.js 18+ 
- Supabase project configured
- Environment variables configured
- Domain name and SSL certificate

## Environment Variables

Ensure all required environment variables are set in production:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRICE_PRO=price_...
STRIPE_PRICE_APPRAISER=price_...

# AI APIs
OPENAI_API_KEY=sk-proj-...
PERPLEXITY_API_KEY=pplx-...

# Google APIs
GOOGLE_GEOCODING_API_KEY=AIza...
GOOGLE_SEARCH_API_KEY=AIza...
GOOGLE_SEARCH_ENGINE_ID=...

# Server Configuration
NODE_ENV=production
PORT=3001
```

## Deployment Options

### Option 1: Vercel (Recommended)

1. **Install Vercel CLI:**
```bash
npm install -g vercel
```

2. **Configure vercel.json:**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "src/index.ts",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "src/index.ts"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  }
}
```

3. **Deploy:**
```bash
vercel --prod
```

### Option 2: Railway

1. **Connect GitHub repository**
2. **Set environment variables in Railway dashboard**
3. **Deploy automatically on push**

### Option 3: DigitalOcean App Platform

1. **Create new app from GitHub**
2. **Configure build settings:**
   - Build Command: `npm run build`
   - Run Command: `npm start`
3. **Set environment variables**

### Option 4: Docker

1. **Create Dockerfile:**
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "start"]
```

2. **Build and run:**
```bash
docker build -t municipal-research-api .
docker run -p 3001:3001 --env-file .env municipal-research-api
```

## Database Setup

Ensure Supabase tables are created:

```sql
-- Run the table creation scripts from the setup
-- Tables: municipal_research_cache, municipal_api_usage, research_topics, trial_codes, municipal_research_analytics
```

## Health Checks

Configure health checks for your deployment platform:

- **Health Endpoint:** `GET /health`
- **Expected Response:** `200 OK` with JSON status

## Monitoring

### Logging

The API uses Winston for structured logging:

- **Error logs:** `logs/error.log`
- **All logs:** `logs/all.log`
- **Console output:** Structured JSON in production

### Metrics

Monitor these key metrics:

- **Response times:** Average < 15 seconds
- **Error rate:** < 5%
- **Cache hit rate:** > 50%
- **API key usage:** Track per-key limits

### Alerts

Set up alerts for:

- High error rates
- Slow response times
- API quota exhaustion
- Database connection issues

## Security

### API Key Security

- Store API keys securely
- Rotate keys regularly
- Monitor for suspicious usage
- Implement rate limiting

### Network Security

- Use HTTPS only
- Configure CORS properly
- Implement request validation
- Monitor for abuse

## Scaling

### Horizontal Scaling

- Deploy multiple instances
- Use load balancer
- Share cache via Redis (optional)

### Performance Optimization

- Monitor cache hit rates
- Optimize database queries
- Implement request queuing for high load
- Use CDN for static assets

## Backup and Recovery

### Database Backups

- Supabase handles automatic backups
- Export critical data regularly
- Test restore procedures

### Configuration Backups

- Version control all configuration
- Document environment variables
- Maintain deployment scripts

## Integration Testing

### Pre-deployment Checklist

- [ ] All environment variables set
- [ ] Database tables created
- [ ] API keys configured and tested
- [ ] Health check responding
- [ ] Rate limiting working
- [ ] Caching functional
- [ ] Analytics tracking
- [ ] Error handling tested

### Post-deployment Verification

```bash
# Test health endpoint
curl https://your-api.com/health

# Test authentication
curl -H "X-API-Key: test_key" https://your-api.com/api/v1/research/topics

# Test research endpoint
curl -X POST https://your-api.com/api/v1/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test_key" \
  -d '{"address": "123 Main St, Georgetown, MI", "query": "fence height"}'
```

## Troubleshooting

### Common Issues

1. **Environment Variables Not Loading**
   - Check .env file location
   - Verify variable names
   - Restart application

2. **Database Connection Errors**
   - Verify Supabase credentials
   - Check network connectivity
   - Confirm table existence

3. **API Rate Limits**
   - Monitor external API usage
   - Implement exponential backoff
   - Cache aggressively

4. **High Response Times**
   - Check external API latency
   - Monitor database performance
   - Optimize caching strategy

### Debug Mode

Enable debug logging:

```bash
NODE_ENV=development npm start
```

## Support

- **Documentation:** https://docs.ordrly.ai
- **Issues:** GitHub repository
- **Support:** <EMAIL>
