# 🤖 Smart Property Agent Documentation

## Overview

The Smart Property Agent is an intelligent layer within the Municipal Research API that can automatically detect property-related queries and provide direct answers by querying ArcGIS REST endpoints. This eliminates the need for expensive LLM calls for basic property data lookups.

## Architecture

```
User Query → Municipal Research API → Smart Property Agent → ArcGIS → Direct Response
                                   ↓ (if can't handle)
                                   Traditional Perplexity Research
```

## Features

- **Automatic Query Detection**: Identifies zoning, future land use (FLU), and other property queries
- **Direct ArcGIS Integration**: Queries municipal GIS services directly for real-time data
- **Intelligent Fallback**: Falls back to Perplexity research for unsupported queries
- **Response Caching**: Caches property data for 24 hours to improve performance
- **Extensible Design**: Easy to add new property data types and jurisdictions

## Supported Query Types

### Zoning Queries
- "What is the zoning for this property?"
- "What zone is this address in?"
- "Is this property zoned for commercial use?"
- "What are the zoning restrictions?"

### Future Land Use (FLU) Queries
- "What is the future land use designation?"
- "What does the comprehensive plan say about this area?"
- "What is the planned development for this location?"

### Combined Queries
- "What are the zoning and future land use for this address?"
- "Tell me about the development regulations for this property"

## Supported Jurisdictions

### Initial Pilot Cities (Top 10)
1. **New York, NY** - NYC Zoning Map Primary
2. **Los Angeles, CA** - LA County GIS Portal
3. **Chicago, IL** - Chicago Data Portal
4. **Houston, TX** - City of Houston GIS
5. **Phoenix, AZ** - City of Phoenix Open Data
6. **Philadelphia, PA** - City of Philadelphia GIS
7. **San Diego, CA** - City of San Diego GIS
8. **Dallas, TX** - City of Dallas GIS
9. **San Antonio, TX** - City of San Antonio GIS
10. **Austin, TX** - City of Austin Open Data

## Database Schema

### jurisdiction_gis_endpoints
Stores configuration for each jurisdiction's ArcGIS endpoints:

```sql
CREATE TABLE jurisdiction_gis_endpoints (
  id UUID PRIMARY KEY,
  jurisdiction_name TEXT NOT NULL,
  state_code TEXT NOT NULL,
  population INTEGER,
  arcgis_base_url TEXT NOT NULL,
  zoning_service_url TEXT,
  zoning_layer_id INTEGER,
  zoning_field_name TEXT,
  flu_service_url TEXT,
  flu_layer_id INTEGER,
  flu_field_name TEXT,
  coordinate_system TEXT DEFAULT 'EPSG:4326',
  is_active BOOLEAN DEFAULT true,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### property_data_cache
Caches property data responses:

```sql
CREATE TABLE property_data_cache (
  id UUID PRIMARY KEY,
  address_hash TEXT NOT NULL,
  jurisdiction_name TEXT NOT NULL,
  query_type TEXT NOT NULL, -- 'zoning', 'flu', 'both'
  response_data JSONB NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(address_hash, jurisdiction_name, query_type)
);
```

## Setup Instructions

### 1. Database Setup
```bash
# Run the database migration
npm run smart-agent:setup

# Alternative method if the above fails
npm run smart-agent:setup-alt
```

### 2. Test the Installation
```bash
# Run all tests
npm run smart-agent:test

# Test individual components
npm run smart-agent:test-query    # Query analyzer
npm run smart-agent:test-arcgis   # ArcGIS client
npm run smart-agent:test-db       # Database connection
```

### 3. Verify Integration
The smart agent is automatically integrated into the existing research flow. No additional configuration is needed.

## API Response Format

When the smart agent handles a query, it returns the same format as the traditional research flow:

```json
{
  "success": true,
  "data": {
    "jurisdiction": "Chicago, IL",
    "topic": "zoning",
    "answer": "According to the official zoning data for Chicago, IL, this property is zoned as **R1-1 Single Family Residential**...",
    "sources": [
      "https://gis.chicago.gov/arcgis/rest/services/ExternalApps/operational_layers_base/MapServer"
    ],
    "confidence": 0.9,
    "cached": false,
    "processingTimeMs": 1250,
    "method": "smart-agent"
  }
}
```

## Adding New Jurisdictions

### 1. Research ArcGIS Endpoints
Find the jurisdiction's ArcGIS REST services:
- Look for city/county GIS portals
- Common patterns: `https://gis.cityname.gov/arcgis/rest/services/`
- Identify zoning and land use layers

### 2. Add to Database
```sql
INSERT INTO jurisdiction_gis_endpoints (
  jurisdiction_name,
  state_code,
  population,
  arcgis_base_url,
  zoning_service_url,
  zoning_layer_id,
  zoning_field_name,
  notes
) VALUES (
  'City Name',
  'ST',
  123456,
  'https://gis.cityname.gov/arcgis/rest/services',
  'https://gis.cityname.gov/arcgis/rest/services/Zoning/MapServer',
  0,
  'ZONE_CLASS',
  'Description of the service'
);
```

### 3. Test the Configuration
```bash
npm run smart-agent:test-arcgis
```

## Adding New Property Data Types

The smart agent is designed to be extensible. To add new property data types (permits, flood zones, etc.):

### 1. Update Database Schema
Add new columns to `jurisdiction_gis_endpoints`:
```sql
ALTER TABLE jurisdiction_gis_endpoints 
ADD COLUMN permits_service_url TEXT,
ADD COLUMN permits_layer_id INTEGER,
ADD COLUMN permits_field_name TEXT;
```

### 2. Update Query Analyzer
Add new keywords to `PropertyQueryAnalyzer`:
```typescript
private permitKeywords = [
  'permit', 'permits', 'building permit',
  'construction permit', 'development permit'
];
```

### 3. Update Response Formatter
Add new formatting logic in `PropertyResponseFormatter`:
```typescript
private formatPermitResponse(permitData: any): string {
  // Format permit information
}
```

## Monitoring and Maintenance

### Performance Metrics
- Query detection accuracy
- ArcGIS response times
- Cache hit rates
- Fallback frequency

### Maintenance Tasks
- Test ArcGIS endpoints monthly
- Update jurisdiction configurations as needed
- Monitor error logs for failed queries
- Clean up expired cache entries

### Troubleshooting

**Smart Agent Not Activating:**
- Check query keywords match patterns
- Verify jurisdiction is in database
- Test ArcGIS endpoint availability

**ArcGIS Queries Failing:**
- Verify service URLs are correct
- Check field names match service schema
- Test coordinate system compatibility

**Database Issues:**
- Verify Supabase connection
- Check table permissions
- Validate migration completion

## Future Enhancements

### Planned Features
- Building permits integration
- Flood zone data
- Historic district information
- Environmental hazard data
- Property assessment values

### Expansion Strategy
- Add remaining top 50 cities
- Support county-level data
- International jurisdiction support
- Real-time data validation

## Support

For issues or questions about the Smart Property Agent:
1. Check the troubleshooting section above
2. Run the test suite to identify specific problems
3. Review ArcGIS service documentation for endpoint changes
4. Contact the development team for assistance
