import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define protected routes
const protectedRoutes = [
  '/dashboard',
  '/api-keys',
  '/usage',
  '/billing',
  '/chat'
]

// Define auth routes
const authRoutes = [
  '/login',
  '/signup'
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  )
  
  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some(route => 
    pathname.startsWith(route)
  )

  // For now, we'll just continue without logging
  // TODO: Implement actual authentication logic with Supabase

  // Continue with the request
  return NextResponse.next()
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Only run on specific routes that need authentication
     * Exclude static files, API routes, and assets
     */
    '/dashboard/:path*',
    '/api-keys/:path*',
    '/usage/:path*',
    '/billing/:path*',
    '/chat/:path*',
    '/login',
    '/signup'
  ],
}
