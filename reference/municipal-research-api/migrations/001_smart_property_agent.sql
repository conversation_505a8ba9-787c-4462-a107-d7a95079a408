-- Smart Property Agent Database Schema
-- Migration: 001_smart_property_agent.sql

-- Table for jurisdiction GIS endpoint configuration
CREATE TABLE IF NOT EXISTS jurisdiction_gis_endpoints (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  jurisdiction_name TEXT NOT NULL,
  state_code TEXT NOT NULL,
  population INTEGER,
  arcgis_base_url TEXT NOT NULL,
  zoning_service_url TEXT,
  zoning_layer_id INTEGER,
  zoning_field_name TEXT,
  flu_service_url TEXT,
  flu_layer_id INTEGER,
  flu_field_name TEXT,
  coordinate_system TEXT DEFAULT 'EPSG:4326',
  is_active BOOLEAN DEFAULT true,
  last_tested TIMESTAMP,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_jurisdiction_gis_endpoints_jurisdiction_state 
ON jurisdiction_gis_endpoints (jurisdiction_name, state_code);

CREATE INDEX IF NOT EXISTS idx_jurisdiction_gis_endpoints_active 
ON jurisdiction_gis_endpoints (is_active);

-- Table for property data cache
CREATE TABLE IF NOT EXISTS property_data_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  address_hash TEXT NOT NULL,
  jurisdiction_name TEXT NOT NULL,
  query_type TEXT NOT NULL, -- 'zoning', 'flu', 'both'
  response_data JSONB NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(address_hash, jurisdiction_name, query_type)
);

-- Create indexes for property cache
CREATE INDEX IF NOT EXISTS idx_property_data_cache_lookup 
ON property_data_cache (address_hash, jurisdiction_name, query_type);

CREATE INDEX IF NOT EXISTS idx_property_data_cache_expires 
ON property_data_cache (expires_at);

-- Insert initial data for top 10 pilot cities
INSERT INTO jurisdiction_gis_endpoints (
  jurisdiction_name, 
  state_code, 
  population, 
  arcgis_base_url, 
  zoning_service_url, 
  zoning_layer_id, 
  zoning_field_name,
  notes
) VALUES 
-- New York, NY
('New York', 'NY', 8336817, 
 'https://maps.nyc.gov/gis/rest/services', 
 'https://maps.nyc.gov/gis/rest/services/DoITT/ZoningMapPrimary/MapServer', 
 0, 
 'ZONEDIST',
 'NYC Zoning Map - Primary zoning districts'),

-- Los Angeles, CA  
('Los Angeles', 'CA', 3898747,
 'https://egis3.lacounty.gov/dataportal/rest/services',
 'https://egis3.lacounty.gov/dataportal/rest/services/LACounty_Dynamic/MapServer',
 1,
 'ZONE_CLASS',
 'LA County GIS Portal - Zoning layer'),

-- Chicago, IL
('Chicago', 'IL', 2746388,
 'https://gis.chicago.gov/arcgis/rest/services',
 'https://gis.chicago.gov/arcgis/rest/services/ExternalApps/operational_layers_base/MapServer',
 17,
 'ZONE_CLASS',
 'Chicago Data Portal - Zoning districts'),

-- Houston, TX
('Houston', 'TX', 2304580,
 'https://cohgis.houstontx.gov/arcgis/rest/services',
 'https://cohgis.houstontx.gov/arcgis/rest/services/PlanningAndDevelopment/MapServer',
 0,
 'ZONING',
 'City of Houston GIS - Planning and Development'),

-- Phoenix, AZ
('Phoenix', 'AZ', 1608139,
 'https://services.arcgis.com/0pT2XTDgGwSIOZSU/arcgis/rest/services',
 'https://services.arcgis.com/0pT2XTDgGwSIOZSU/arcgis/rest/services/Zoning/FeatureServer',
 0,
 'ZONE_',
 'City of Phoenix Open Data - Zoning'),

-- Philadelphia, PA
('Philadelphia', 'PA', 1603797,
 'https://services.arcgis.com/fLeGjb7u4uXqeF9q/arcgis/rest/services',
 'https://services.arcgis.com/fLeGjb7u4uXqeF9q/arcgis/rest/services/Zoning_BaseDistricts/FeatureServer',
 0,
 'LONG_CODE',
 'City of Philadelphia - Zoning Base Districts'),

-- San Diego, CA
('San Diego', 'CA', 1386932,
 'https://services1.arcgis.com/1vIhDJwtG5eNmiqX/arcgis/rest/services',
 'https://services1.arcgis.com/1vIhDJwtG5eNmiqX/arcgis/rest/services/Zoning/FeatureServer',
 0,
 'ZONE',
 'City of San Diego GIS - Zoning'),

-- Dallas, TX
('Dallas', 'TX', 1304379,
 'https://gis.dallascityhall.com/arcgis/rest/services',
 'https://gis.dallascityhall.com/arcgis/rest/services/PlanningDevelopment/MapServer',
 1,
 'ZONING',
 'City of Dallas GIS - Planning Development'),

-- San Antonio, TX
('San Antonio', 'TX', 1434625,
 'https://services.arcgis.com/g1fRTDLeMgspWrYp/arcgis/rest/services',
 'https://services.arcgis.com/g1fRTDLeMgspWrYp/arcgis/rest/services/Zoning/FeatureServer',
 0,
 'ZONE_TYPE',
 'City of San Antonio - Zoning'),

-- Austin, TX
('Austin', 'TX', 978908,
 'https://services.arcgis.com/0L95CJ0VTaxqcmED/arcgis/rest/services',
 'https://services.arcgis.com/0L95CJ0VTaxqcmED/arcgis/rest/services/Zoning/FeatureServer',
 0,
 'ZONING_ZONING_DISTRICT',
 'City of Austin Open Data - Zoning');

-- Create function to clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_property_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM property_data_cache 
  WHERE expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get jurisdiction GIS config
CREATE OR REPLACE FUNCTION get_jurisdiction_gis_config(
  jurisdiction_name_param TEXT,
  state_code_param TEXT
)
RETURNS TABLE (
  id UUID,
  jurisdiction_name TEXT,
  state_code TEXT,
  arcgis_base_url TEXT,
  zoning_service_url TEXT,
  zoning_layer_id INTEGER,
  zoning_field_name TEXT,
  flu_service_url TEXT,
  flu_layer_id INTEGER,
  flu_field_name TEXT,
  coordinate_system TEXT,
  is_active BOOLEAN
)
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT 
    jge.id,
    jge.jurisdiction_name,
    jge.state_code,
    jge.arcgis_base_url,
    jge.zoning_service_url,
    jge.zoning_layer_id,
    jge.zoning_field_name,
    jge.flu_service_url,
    jge.flu_layer_id,
    jge.flu_field_name,
    jge.coordinate_system,
    jge.is_active
  FROM jurisdiction_gis_endpoints jge
  WHERE jge.jurisdiction_name ILIKE jurisdiction_name_param
    AND jge.state_code = state_code_param
    AND jge.is_active = true
  LIMIT 1;
$$;
