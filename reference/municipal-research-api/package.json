{"name": "ordrly-research-api", "version": "1.0.0", "description": "Municipal Research API - Real-time compliance research with caching", "main": "index.js", "scripts": {"dev": "tsx src/index.ts", "build": "tsc", "start": "tsx src/index.ts", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "api:dev": "tsx watch src/index.ts", "api:build": "tsc", "api:start": "node dist/index.js", "api:start:dev": "tsx src/index.ts", "legacy:dev": "tsx watch index.ts", "legacy:start": "tsx index.ts", "legacy:test": "tsx test.ts", "smart-agent:setup": "tsx scripts/setup-smart-agent-db.ts", "smart-agent:setup-alt": "tsx scripts/setup-smart-agent-db.ts --alternative", "smart-agent:test": "tsx scripts/test-smart-agent.ts", "smart-agent:test-query": "tsx scripts/test-smart-agent.ts --query-analyzer", "smart-agent:test-arcgis": "tsx scripts/test-smart-agent.ts --arcgis", "smart-agent:test-db": "tsx scripts/test-smart-agent.ts --database"}, "keywords": ["research", "api", "municipal", "compliance", "regulations"], "author": "Ordrly Team", "license": "MIT", "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.38.4", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.7.4", "compromise": "^14.10.0", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "googleapis": "^150.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.522.0", "natural": "^6.7.0", "next": "^14.2.5", "next-themes": "^0.4.6", "openai": "^5.6.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "stripe": "^14.9.0", "supertest": "^7.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.4", "typescript": "5.1.6", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^18.17.0", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2"}, "engines": {"node": ">=18.0.0"}}