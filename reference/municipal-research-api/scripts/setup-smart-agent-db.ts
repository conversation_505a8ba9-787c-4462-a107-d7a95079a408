/**
 * 🗄️ SMART PROPERTY AGENT DATABASE SETUP
 * 
 * Sets up the database tables and initial data for the Smart Property Agent
 */

import { createClient } from '@supabase/supabase-js';
import { config } from '../src/config/config.js';
import fs from 'fs';
import path from 'path';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

async function setupSmartAgentDatabase() {
  console.log('🗄️ Setting up Smart Property Agent database...');

  try {
    // Read the migration SQL file
    const migrationPath = path.join(process.cwd(), 'migrations', '001_smart_property_agent.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 Executing database migration...');

    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

    if (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }

    console.log('✅ Database migration completed successfully');

    // Verify the tables were created
    console.log('🔍 Verifying table creation...');

    const { data: jurisdictionEndpoints, error: endpointsError } = await supabase
      .from('jurisdiction_gis_endpoints')
      .select('jurisdiction_name, state_code')
      .limit(5);

    if (endpointsError) {
      console.error('❌ Error verifying jurisdiction_gis_endpoints table:', endpointsError);
    } else {
      console.log(`✅ jurisdiction_gis_endpoints table created with ${jurisdictionEndpoints?.length || 0} initial records`);
      if (jurisdictionEndpoints && jurisdictionEndpoints.length > 0) {
        console.log('📍 Sample jurisdictions:');
        jurisdictionEndpoints.forEach(j => console.log(`   - ${j.jurisdiction_name}, ${j.state_code}`));
      }
    }

    const { data: cacheTable, error: cacheError } = await supabase
      .from('property_data_cache')
      .select('id')
      .limit(1);

    if (cacheError) {
      console.error('❌ Error verifying property_data_cache table:', cacheError);
    } else {
      console.log('✅ property_data_cache table created successfully');
    }

    console.log('🎉 Smart Property Agent database setup completed!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Test the Smart Property Agent with a property query');
    console.log('2. Monitor logs for successful ArcGIS queries');
    console.log('3. Add more jurisdictions as needed');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Alternative method if rpc doesn't work - execute statements individually
async function setupSmartAgentDatabaseAlternative() {
  console.log('🗄️ Setting up Smart Property Agent database (alternative method)...');

  try {
    // Create jurisdiction_gis_endpoints table
    console.log('📄 Creating jurisdiction_gis_endpoints table...');
    
    const createJurisdictionTable = `
      CREATE TABLE IF NOT EXISTS jurisdiction_gis_endpoints (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        jurisdiction_name TEXT NOT NULL,
        state_code TEXT NOT NULL,
        population INTEGER,
        arcgis_base_url TEXT NOT NULL,
        zoning_service_url TEXT,
        zoning_layer_id INTEGER,
        zoning_field_name TEXT,
        flu_service_url TEXT,
        flu_layer_id INTEGER,
        flu_field_name TEXT,
        coordinate_system TEXT DEFAULT 'EPSG:4326',
        is_active BOOLEAN DEFAULT true,
        last_tested TIMESTAMP,
        notes TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `;

    const { error: tableError } = await supabase.rpc('exec_sql', { sql: createJurisdictionTable });
    if (tableError) {
      console.error('❌ Failed to create jurisdiction_gis_endpoints table:', tableError);
      throw tableError;
    }

    // Create indexes
    console.log('📄 Creating indexes...');
    const createIndexes = `
      CREATE INDEX IF NOT EXISTS idx_jurisdiction_gis_endpoints_jurisdiction_state 
      ON jurisdiction_gis_endpoints (jurisdiction_name, state_code);
      
      CREATE INDEX IF NOT EXISTS idx_jurisdiction_gis_endpoints_active 
      ON jurisdiction_gis_endpoints (is_active);
    `;

    const { error: indexError } = await supabase.rpc('exec_sql', { sql: createIndexes });
    if (indexError) {
      console.error('❌ Failed to create indexes:', indexError);
      // Don't throw - indexes are not critical
    }

    // Create property_data_cache table
    console.log('📄 Creating property_data_cache table...');
    
    const createCacheTable = `
      CREATE TABLE IF NOT EXISTS property_data_cache (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        address_hash TEXT NOT NULL,
        jurisdiction_name TEXT NOT NULL,
        query_type TEXT NOT NULL,
        response_data JSONB NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT NOW(),
        UNIQUE(address_hash, jurisdiction_name, query_type)
      );
    `;

    const { error: cacheTableError } = await supabase.rpc('exec_sql', { sql: createCacheTable });
    if (cacheTableError) {
      console.error('❌ Failed to create property_data_cache table:', cacheTableError);
      throw cacheTableError;
    }

    // Insert initial data
    console.log('📄 Inserting initial jurisdiction data...');
    
    const initialJurisdictions = [
      {
        jurisdiction_name: 'New York',
        state_code: 'NY',
        population: 8336817,
        arcgis_base_url: 'https://maps.nyc.gov/gis/rest/services',
        zoning_service_url: 'https://maps.nyc.gov/gis/rest/services/DoITT/ZoningMapPrimary/MapServer',
        zoning_layer_id: 0,
        zoning_field_name: 'ZONEDIST',
        notes: 'NYC Zoning Map - Primary zoning districts'
      },
      {
        jurisdiction_name: 'Los Angeles',
        state_code: 'CA',
        population: 3898747,
        arcgis_base_url: 'https://egis3.lacounty.gov/dataportal/rest/services',
        zoning_service_url: 'https://egis3.lacounty.gov/dataportal/rest/services/LACounty_Dynamic/MapServer',
        zoning_layer_id: 1,
        zoning_field_name: 'ZONE_CLASS',
        notes: 'LA County GIS Portal - Zoning layer'
      },
      {
        jurisdiction_name: 'Chicago',
        state_code: 'IL',
        population: 2746388,
        arcgis_base_url: 'https://gis.chicago.gov/arcgis/rest/services',
        zoning_service_url: 'https://gis.chicago.gov/arcgis/rest/services/ExternalApps/operational_layers_base/MapServer',
        zoning_layer_id: 17,
        zoning_field_name: 'ZONE_CLASS',
        notes: 'Chicago Data Portal - Zoning districts'
      }
    ];

    const { error: insertError } = await supabase
      .from('jurisdiction_gis_endpoints')
      .upsert(initialJurisdictions);

    if (insertError) {
      console.error('❌ Failed to insert initial data:', insertError);
      throw insertError;
    }

    console.log('✅ Database setup completed successfully');
    console.log(`📍 Inserted ${initialJurisdictions.length} initial jurisdictions`);

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
if (process.argv.includes('--alternative')) {
  setupSmartAgentDatabaseAlternative();
} else {
  setupSmartAgentDatabase();
}
