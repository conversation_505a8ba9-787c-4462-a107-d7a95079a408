/**
 * 🧪 SMART PROPERTY AGENT TEST SCRIPT
 * 
 * Tests the Smart Property Agent functionality
 */

import { smartPropertyAgent } from '../src/services/smartPropertyAgent.js';
import { PropertyQueryAnalyzer } from '../src/services/propertyQueryAnalyzer.js';
import { ArcGISClient } from '../src/services/arcgisClient.js';
import { config } from '../src/config/config.js';

// Test queries
const testQueries = [
  {
    address: '123 Main Street, New York, NY',
    query: 'What is the zoning for this property?',
    expectedType: 'zoning'
  },
  {
    address: '456 Oak Avenue, Chicago, IL',
    query: 'What is the future land use designation?',
    expectedType: 'flu'
  },
  {
    address: '789 Pine Street, Los Angeles, CA',
    query: 'What are the zoning and future land use for this address?',
    expectedType: 'both'
  },
  {
    address: '321 Elm Drive, Austin, TX',
    query: 'Can I build a duplex on this property?',
    expectedType: 'zoning'
  },
  {
    address: '654 Maple Lane, Phoenix, AZ',
    query: 'What are the weather patterns in this area?',
    expectedType: 'unknown'
  }
];

async function testQueryAnalyzer() {
  console.log('🔍 Testing Property Query Analyzer...\n');
  
  const analyzer = new PropertyQueryAnalyzer();
  
  for (const test of testQueries) {
    console.log(`Query: "${test.query}"`);
    
    const analysis = await analyzer.analyzeQuery(test.query);
    
    console.log(`  Expected: ${test.expectedType}`);
    console.log(`  Detected: ${analysis.queryType}`);
    console.log(`  Can Handle: ${analysis.canHandle}`);
    console.log(`  Confidence: ${Math.round(analysis.confidence * 100)}%`);
    console.log(`  Keywords: ${analysis.detectedKeywords.join(', ')}`);
    
    if (analysis.reason) {
      console.log(`  Reason: ${analysis.reason}`);
    }
    
    const match = analysis.queryType === test.expectedType || 
                  (test.expectedType === 'unknown' && !analysis.canHandle);
    console.log(`  ✅ ${match ? 'PASS' : 'FAIL'}\n`);
  }
}

async function testArcGISClient() {
  console.log('🗺️ Testing ArcGIS Client...\n');
  
  const client = new ArcGISClient();
  
  // Test NYC zoning service
  const nycZoningUrl = 'https://maps.nyc.gov/gis/rest/services/DoITT/ZoningMapPrimary/MapServer';
  const nycCoordinates = { lat: 40.7589, lng: -73.9851 }; // Times Square area
  
  console.log('Testing NYC Zoning Service...');
  console.log(`URL: ${nycZoningUrl}`);
  console.log(`Coordinates: ${nycCoordinates.lat}, ${nycCoordinates.lng}`);
  
  try {
    // Test service availability
    const serviceAvailable = await client.testService(nycZoningUrl, 0);
    console.log(`Service Available: ${serviceAvailable ? '✅ YES' : '❌ NO'}`);
    
    if (serviceAvailable) {
      // Test actual query
      const result = await client.queryPoint(
        nycZoningUrl,
        0,
        nycCoordinates,
        ['ZONEDIST']
      );
      
      console.log('Query Result:', result);
      
      if (result && result.ZONEDIST) {
        console.log(`✅ Successfully retrieved zoning: ${result.ZONEDIST}`);
      } else {
        console.log('❌ No zoning data returned');
      }
    }
    
  } catch (error) {
    console.error('❌ ArcGIS test failed:', error);
  }
  
  console.log('');
}

async function testSmartAgent() {
  console.log('🤖 Testing Smart Property Agent End-to-End...\n');
  
  // Test with a property query that should work
  const testRequest = {
    address: '123 Broadway, New York, NY',
    query: 'What is the zoning for this property?'
  };
  
  console.log(`Testing: "${testRequest.query}" for ${testRequest.address}`);
  
  try {
    const result = await smartPropertyAgent.tryHandleQuery(testRequest);
    
    console.log(`Can Handle: ${result.canHandle ? '✅ YES' : '❌ NO'}`);
    
    if (result.canHandle && result.response) {
      console.log(`Success: ${result.response.success}`);
      console.log(`Jurisdiction: ${result.response.jurisdiction}`);
      console.log(`Topic: ${result.response.topic}`);
      console.log(`Confidence: ${Math.round(result.response.confidence * 100)}%`);
      console.log(`Processing Time: ${result.response.processingTimeMs}ms`);
      console.log(`Method: ${result.response.method}`);
      console.log(`Sources: ${result.response.sources.length}`);
      console.log(`Answer Preview: ${result.response.answer.substring(0, 200)}...`);
    } else {
      console.log(`Fallback Reason: ${result.fallbackReason}`);
    }
    
  } catch (error) {
    console.error('❌ Smart Agent test failed:', error);
  }
  
  console.log('');
}

async function testDatabaseConnection() {
  console.log('🗄️ Testing Database Connection...\n');
  
  try {
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);
    
    // Test jurisdiction endpoints table
    const { data: jurisdictions, error: jurisdictionError } = await supabase
      .from('jurisdiction_gis_endpoints')
      .select('jurisdiction_name, state_code, is_active')
      .eq('is_active', true)
      .limit(5);
    
    if (jurisdictionError) {
      console.error('❌ Database connection failed:', jurisdictionError);
      return;
    }
    
    console.log(`✅ Database connected successfully`);
    console.log(`📍 Found ${jurisdictions?.length || 0} active jurisdictions:`);
    
    if (jurisdictions) {
      jurisdictions.forEach(j => {
        console.log(`   - ${j.jurisdiction_name}, ${j.state_code}`);
      });
    }
    
    // Test cache table
    const { data: cacheTest, error: cacheError } = await supabase
      .from('property_data_cache')
      .select('id')
      .limit(1);
    
    if (cacheError) {
      console.error('❌ Cache table test failed:', cacheError);
    } else {
      console.log('✅ Property cache table accessible');
    }
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
  
  console.log('');
}

async function runAllTests() {
  console.log('🧪 SMART PROPERTY AGENT TEST SUITE\n');
  console.log('='.repeat(50));
  console.log('');
  
  try {
    await testDatabaseConnection();
    await testQueryAnalyzer();
    await testArcGISClient();
    await testSmartAgent();
    
    console.log('🎉 All tests completed!');
    console.log('');
    console.log('If any tests failed, check:');
    console.log('1. Database migration was run successfully');
    console.log('2. Environment variables are configured');
    console.log('3. Network connectivity to ArcGIS services');
    console.log('4. Supabase permissions and service role key');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.includes('--query-analyzer')) {
  testQueryAnalyzer();
} else if (args.includes('--arcgis')) {
  testArcGISClient();
} else if (args.includes('--smart-agent')) {
  testSmartAgent();
} else if (args.includes('--database')) {
  testDatabaseConnection();
} else {
  runAllTests();
}
