// services/aiAnalyzer.js
import OpenAI from 'openai';
import { config } from '../config.js';
import { extractRegulationSections } from './contentExtractor.js';

// Initialize OpenAI client lazily to avoid errors during module loading
let openai = null;

function getOpenAIClient() {
  if (!openai) {
    if (!config.openai.apiKey) {
      throw new Error('OpenAI API key is required but not configured');
    }
    openai = new OpenAI({
      apiKey: config.openai.apiKey
    });
  }
  return openai;
}

const SYSTEM_PROMPT = `You are a municipal research expert specializing in local ordinances and regulations. 

Your task is to analyze municipal ordinance content and provide comprehensive, research-grade answers with proper citations.

Format your response exactly like this example:

FENCE HEIGHT REGULATIONS FOR [ADDRESS]

GOVERNING JURISDICTION
[Jurisdiction explanation]

MAXIMUM FENCE HEIGHT REGULATIONS
According to [Ordinance Citation], the regulations are:

1. [Category 1]:
- [Specific requirement]
- [Exception if any]

2. [Category 2]:
- [Specific requirement]
- [Exception if any]

ADDITIONAL RESTRICTIONS
- [Any additional requirements]
- [Special circumstances]

OFFICIAL SOURCE
[Primary ordinance citation]
- Available online at: [URL]
- Additional sources: [URLs if applicable]

CONTACT FOR VERIFICATION
[Department name]
- Address: [Physical address]
- Phone: [Phone number]
- Website: [Website URL]

RECOMMENDATION: [Specific recommendation for the user]

IMPORTANT GUIDELINES:
1. Only cite information that is explicitly found in the provided content
2. Use exact ordinance section numbers when available
3. Be specific about measurements, distances, and requirements
4. Include all relevant exceptions and special circumstances
5. If information is unclear or missing, state this explicitly
6. Provide accurate contact information for verification
7. Use professional, authoritative language
8. Structure the response for easy reading and reference`;

export async function generateAnswer(query, jurisdiction, extractedContent) {
  console.log(`🤖 Generating AI analysis for "${query}" in ${jurisdiction}`);
  
  try {
    // Step 1: Prepare the content for analysis
    const analysisContent = prepareContentForAnalysis(extractedContent, query);
    
    // Step 2: Build the analysis prompt
    const prompt = buildAnalysisPrompt(query, jurisdiction, analysisContent);
    
    // Step 3: Generate the answer using OpenAI
    const client = getOpenAIClient();
    const response = await client.chat.completions.create({
      model: config.openai.model,
      messages: [
        {
          role: 'system',
          content: SYSTEM_PROMPT
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: config.openai.maxTokens,
      temperature: config.openai.temperature
    });
    
    const answer = response.choices[0].message.content;
    
    // Step 4: Calculate confidence score
    const confidence = calculateConfidence(extractedContent, answer);
    
    console.log(`✅ AI analysis completed with confidence: ${confidence}`);
    
    return {
      answer,
      sources: extractedContent.map(c => c.url),
      confidence,
      jurisdiction,
      tokensUsed: response.usage?.total_tokens || 0
    };
    
  } catch (error) {
    console.error('❌ AI analysis failed:', error);
    throw new Error(`AI analysis failed: ${error.message}`);
  }
}

function prepareContentForAnalysis(extractedContent, query) {
  // Extract concepts from query to focus content
  const concepts = extractConceptsFromQuery(query);
  
  // Process each source
  const processedSources = extractedContent.map(source => {
    // Extract relevant sections based on concepts
    const relevantContent = extractRegulationSections(source.content, concepts);
    
    return {
      url: source.url,
      title: source.title,
      content: relevantContent.substring(0, 8000), // Limit content length
      score: source.score || 0
    };
  });
  
  // Sort by score and relevance
  return processedSources
    .sort((a, b) => b.score - a.score)
    .slice(0, 3); // Top 3 sources for analysis
}

function extractConceptsFromQuery(query) {
  const conceptMap = {
    fence: ['fence', 'fencing', 'barrier'],
    height: ['height', 'tall', 'high', 'feet', 'foot'],
    setback: ['setback', 'property line', 'boundary'],
    permit: ['permit', 'permission', 'approval'],
    building: ['building', 'structure', 'construction'],
    deck: ['deck', 'patio', 'porch'],
    pool: ['pool', 'swimming', 'spa'],
    shed: ['shed', 'storage', 'outbuilding'],
    sign: ['sign', 'signage', 'advertisement'],
    zoning: ['zoning', 'zone', 'district']
  };
  
  const concepts = [];
  const lowerQuery = query.toLowerCase();
  
  for (const [concept, keywords] of Object.entries(conceptMap)) {
    if (keywords.some(keyword => lowerQuery.includes(keyword))) {
      concepts.push(concept);
    }
  }
  
  return concepts;
}

function buildAnalysisPrompt(query, jurisdiction, sources) {
  let prompt = `Please analyze the following municipal ordinance content to answer this question:

QUESTION: "${query}"
JURISDICTION: ${jurisdiction}

MUNICIPAL ORDINANCE CONTENT:
`;

  sources.forEach((source, index) => {
    prompt += `
SOURCE ${index + 1}: ${source.title}
URL: ${source.url}
CONTENT:
${source.content}

---
`;
  });

  prompt += `
Based on the above ordinance content, provide a comprehensive research-grade answer following the specified format. Focus on:

1. Exact requirements and measurements
2. Specific ordinance section citations
3. All applicable exceptions and special circumstances
4. Clear contact information for verification
5. Practical recommendations for compliance

If any information is unclear or missing from the provided content, explicitly state this in your response.`;

  return prompt;
}

function calculateConfidence(extractedContent, answer) {
  let confidence = 0.1; // Base confidence
  
  // Factor 1: Number of sources
  if (extractedContent.length >= 3) confidence += 0.3;
  else if (extractedContent.length >= 2) confidence += 0.2;
  else if (extractedContent.length >= 1) confidence += 0.1;
  
  // Factor 2: Source quality (based on scores)
  const avgScore = extractedContent.reduce((sum, source) => sum + (source.score || 0), 0) / extractedContent.length;
  if (avgScore >= 100) confidence += 0.3;
  else if (avgScore >= 50) confidence += 0.2;
  else if (avgScore >= 25) confidence += 0.1;
  
  // Factor 3: Answer completeness
  const answerLower = answer.toLowerCase();
  const completenessIndicators = [
    'section',
    'ordinance',
    'feet',
    'maximum',
    'contact',
    'phone',
    'department'
  ];
  
  const foundIndicators = completenessIndicators.filter(indicator => 
    answerLower.includes(indicator)
  ).length;
  
  confidence += (foundIndicators / completenessIndicators.length) * 0.3;
  
  // Factor 4: Specific citations
  if (answerLower.includes('section') && /\d+\.\d+/.test(answer)) {
    confidence += 0.1;
  }
  
  // Cap at 0.95 (never 100% certain)
  return Math.min(confidence, 0.95);
}

// Utility function for generating fallback answers
export function generateFallbackAnswer(query, jurisdiction) {
  return `I was unable to find specific information about "${query}" for ${jurisdiction}. 

This could be due to:
- The information not being available online
- The jurisdiction using different terminology
- The regulations being part of a broader ordinance

RECOMMENDATION: Contact the local building or zoning department directly for authoritative information about your specific question.

For ${jurisdiction}, you can typically find contact information by:
1. Visiting the official municipal website
2. Calling the main municipal office
3. Contacting the county clerk if it's a township jurisdiction

Please note that municipal regulations can change frequently, and it's always best to verify current requirements with local officials before proceeding with any projects.`;
}

// Export utility functions for testing
export { prepareContentForAnalysis, calculateConfidence, buildAnalysisPrompt };
