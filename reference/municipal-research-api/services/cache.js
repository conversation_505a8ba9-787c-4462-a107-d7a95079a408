// services/cache.js
import fs from 'fs/promises';
import { createReadStream, createWriteStream } from 'fs';
import csv from 'csv-parser';
import { createObjectCsvWriter } from 'csv-writer';
import { config } from '../config.js';
import { extractConcepts } from './search.js';

export class KnowledgeCache {
  constructor() {
    this.cacheFile = config.cache.filePath;
    this.cache = new Map();
    this.maxAge = config.cache.maxAge;
    this.isLoaded = false;
  }
  
  async initialize() {
    if (!this.isLoaded) {
      await this.loadCache();
      this.isLoaded = true;
    }
  }
  
  async findCached(query, jurisdiction) {
    await this.initialize();
    
    const key = this.generateKey(query, jurisdiction);
    const entry = this.cache.get(key);
    
    if (!entry) {
      console.log(`💾 Cache miss for: ${key}`);
      return null;
    }
    
    // Check if entry is expired
    const age = Date.now() - new Date(entry.createdAt).getTime();
    if (age > this.maxAge) {
      console.log(`⏰ Cache entry expired for: ${key}`);
      this.cache.delete(key);
      return null;
    }
    
    // Update use count and last accessed
    entry.useCount = (entry.useCount || 0) + 1;
    entry.lastAccessed = new Date().toISOString();
    
    console.log(`✅ Cache hit for: ${key} (used ${entry.useCount} times)`);
    return {
      answer: entry.answer,
      sources: JSON.parse(entry.sources || '[]'),
      confidence: parseFloat(entry.confidence || 0),
      jurisdiction: entry.jurisdiction,
      cached: true,
      cacheAge: Math.round(age / 1000 / 60) // Age in minutes
    };
  }
  
  async store(query, jurisdiction, result) {
    await this.initialize();
    
    const key = this.generateKey(query, jurisdiction);
    const entry = {
      key,
      query,
      jurisdiction,
      answer: result.answer,
      sources: JSON.stringify(result.sources || []),
      confidence: result.confidence || 0,
      createdAt: new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      useCount: 1,
      tokensUsed: result.tokensUsed || 0
    };
    
    this.cache.set(key, entry);
    await this.saveCache();
    
    console.log(`💾 Cached result for: ${key}`);
  }
  
  generateKey(query, jurisdiction) {
    // Extract and normalize concepts
    const concepts = extractConcepts(query).sort().join('-');
    
    // Normalize jurisdiction
    const normalizedJurisdiction = jurisdiction
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
    
    // Create key
    const key = `${normalizedJurisdiction}-${concepts}`;
    return key.substring(0, 100); // Limit key length
  }
  
  async loadCache() {
    try {
      // Ensure data directory exists
      await fs.mkdir('./data', { recursive: true });
      
      // Check if cache file exists
      try {
        await fs.access(this.cacheFile);
      } catch {
        // File doesn't exist, create empty cache
        console.log('📁 Creating new cache file');
        await this.createEmptyCache();
        return;
      }
      
      console.log('📖 Loading cache from file...');
      
      return new Promise((resolve, reject) => {
        const results = [];
        
        createReadStream(this.cacheFile)
          .pipe(csv())
          .on('data', (data) => {
            results.push(data);
          })
          .on('end', () => {
            // Load into memory cache
            results.forEach(row => {
              if (row.key) {
                this.cache.set(row.key, row);
              }
            });
            
            console.log(`✅ Loaded ${results.length} cache entries`);
            resolve();
          })
          .on('error', (error) => {
            console.error('❌ Error loading cache:', error);
            reject(error);
          });
      });
      
    } catch (error) {
      console.error('❌ Failed to load cache:', error);
      // Continue without cache
    }
  }
  
  async saveCache() {
    try {
      const records = Array.from(this.cache.values());
      
      if (records.length === 0) {
        return;
      }
      
      const csvWriter = createObjectCsvWriter({
        path: this.cacheFile,
        header: [
          { id: 'key', title: 'key' },
          { id: 'query', title: 'query' },
          { id: 'jurisdiction', title: 'jurisdiction' },
          { id: 'answer', title: 'answer' },
          { id: 'sources', title: 'sources' },
          { id: 'confidence', title: 'confidence' },
          { id: 'createdAt', title: 'createdAt' },
          { id: 'lastAccessed', title: 'lastAccessed' },
          { id: 'useCount', title: 'useCount' },
          { id: 'tokensUsed', title: 'tokensUsed' }
        ]
      });
      
      await csvWriter.writeRecords(records);
      console.log(`💾 Saved ${records.length} cache entries`);
      
    } catch (error) {
      console.error('❌ Failed to save cache:', error);
    }
  }
  
  async createEmptyCache() {
    const csvWriter = createObjectCsvWriter({
      path: this.cacheFile,
      header: [
        { id: 'key', title: 'key' },
        { id: 'query', title: 'query' },
        { id: 'jurisdiction', title: 'jurisdiction' },
        { id: 'answer', title: 'answer' },
        { id: 'sources', title: 'sources' },
        { id: 'confidence', title: 'confidence' },
        { id: 'createdAt', title: 'createdAt' },
        { id: 'lastAccessed', title: 'lastAccessed' },
        { id: 'useCount', title: 'useCount' },
        { id: 'tokensUsed', title: 'tokensUsed' }
      ]
    });
    
    await csvWriter.writeRecords([]);
  }
  
  // Cache management methods
  async getCacheStats() {
    await this.initialize();
    
    const entries = Array.from(this.cache.values());
    const now = Date.now();
    
    const stats = {
      totalEntries: entries.length,
      totalUses: entries.reduce((sum, entry) => sum + (parseInt(entry.useCount) || 0), 0),
      totalTokens: entries.reduce((sum, entry) => sum + (parseInt(entry.tokensUsed) || 0), 0),
      averageAge: 0,
      expiredEntries: 0,
      topJurisdictions: {},
      topConcepts: {}
    };
    
    // Calculate average age and count expired entries
    let totalAge = 0;
    entries.forEach(entry => {
      const age = now - new Date(entry.createdAt).getTime();
      totalAge += age;
      
      if (age > this.maxAge) {
        stats.expiredEntries++;
      }
      
      // Count jurisdictions
      const jurisdiction = entry.jurisdiction;
      stats.topJurisdictions[jurisdiction] = (stats.topJurisdictions[jurisdiction] || 0) + 1;
    });
    
    if (entries.length > 0) {
      stats.averageAge = Math.round(totalAge / entries.length / 1000 / 60 / 60); // Hours
    }
    
    return stats;
  }
  
  async cleanExpiredEntries() {
    await this.initialize();
    
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      const age = now - new Date(entry.createdAt).getTime();
      if (age > this.maxAge) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      await this.saveCache();
      console.log(`🧹 Cleaned ${removedCount} expired cache entries`);
    }
    
    return removedCount;
  }
}

// Create singleton instance
export const knowledgeCache = new KnowledgeCache();
