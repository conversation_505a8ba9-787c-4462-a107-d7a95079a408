// services/contentExtractor.js
import * as cheerio from 'cheerio';

export async function extractContent(sources) {
  console.log(`📄 Extracting content from ${sources.length} sources`);
  
  const extractedContent = [];
  
  for (const source of sources) {
    try {
      console.log(`📡 Fetching: ${source.link}`);
      const content = await fetchAndParse(source.link);
      
      if (content && content.length > 100) { // Minimum content threshold
        extractedContent.push({
          url: source.link,
          title: source.title,
          content: content,
          snippet: source.snippet,
          score: source.score || 0
        });
        console.log(`✅ Extracted ${content.length} characters from ${source.title}`);
      } else {
        console.warn(`⚠️ Insufficient content from ${source.link}`);
      }
    } catch (error) {
      console.warn(`❌ Failed to fetch ${source.link}:`, error.message);
      // Continue with other sources
    }
  }
  
  console.log(`📄 Successfully extracted content from ${extractedContent.length} sources`);
  return extractedContent;
}

async function fetchAndParse(url) {
  const response = await fetch(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (compatible; OrdrlyResearchBot/1.0; +https://ordrly.ai/bot)',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    },
    timeout: 10000 // 10 second timeout
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  const html = await response.text();
  return parseContent(html, url);
}

function parseContent(html, url) {
  const $ = cheerio.load(html);
  
  // Remove unwanted elements
  $('script, style, nav, header, footer, .sidebar, .navigation, .menu, .ads, .advertisement').remove();
  
  // Try different content extraction strategies based on site type
  let content = '';
  
  // Strategy 1: Municipal code sites
  if (isMunicipalCodeSite(url)) {
    content = extractMunicipalCodeContent($);
  }
  
  // Strategy 2: Government sites
  if (!content && isGovernmentSite(url)) {
    content = extractGovernmentContent($);
  }
  
  // Strategy 3: General content extraction
  if (!content) {
    content = extractGeneralContent($);
  }
  
  // Clean and format the content
  return cleanContent(content);
}

function isMunicipalCodeSite(url) {
  const municipalCodeDomains = [
    'municode.com',
    'codepublishing.com',
    'generalcode.com',
    'ecode360.com',
    'amlegal.com',
    'qcode.us'
  ];
  
  return municipalCodeDomains.some(domain => url.includes(domain));
}

function isGovernmentSite(url) {
  return url.includes('.gov');
}

function extractMunicipalCodeContent($) {
  const contentSelectors = [
    '.ordinance-text',
    '.regulation-content',
    '.code-section',
    '.section-content',
    '.ordinance-content',
    '#content .text',
    '.main-content .text',
    '.document-content',
    '.legal-content'
  ];
  
  for (const selector of contentSelectors) {
    const content = $(selector).text().trim();
    if (content.length > 200) {
      return content;
    }
  }
  
  // Fallback: look for numbered sections
  const sections = $('div, p, span').filter((i, el) => {
    const text = $(el).text();
    return /^\d+\.\d+/.test(text.trim()) || /^Section \d+/.test(text.trim());
  });
  
  if (sections.length > 0) {
    return sections.map((i, el) => $(el).text().trim()).get().join('\n\n');
  }
  
  return '';
}

function extractGovernmentContent($) {
  const contentSelectors = [
    'main',
    '.content',
    '.main-content',
    '#content',
    '.page-content',
    '.article-content',
    '.post-content',
    '.entry-content',
    '.document-content'
  ];
  
  for (const selector of contentSelectors) {
    const content = $(selector).text().trim();
    if (content.length > 200) {
      return content;
    }
  }
  
  return '';
}

function extractGeneralContent($) {
  // Remove common non-content elements
  $('.header, .footer, .nav, .navigation, .sidebar, .menu, .ads, .social, .comments').remove();
  
  // Try semantic HTML5 elements first
  const semanticSelectors = ['main', 'article', 'section'];
  for (const selector of semanticSelectors) {
    const content = $(selector).text().trim();
    if (content.length > 200) {
      return content;
    }
  }
  
  // Try common content containers
  const containerSelectors = [
    '.content',
    '.main-content',
    '#content',
    '.post',
    '.article',
    '.page'
  ];
  
  for (const selector of containerSelectors) {
    const content = $(selector).text().trim();
    if (content.length > 200) {
      return content;
    }
  }
  
  // Fallback to body, but filter out navigation and other noise
  const bodyText = $('body').text().trim();
  return bodyText.length > 200 ? bodyText : '';
}

function cleanContent(content) {
  if (!content) return '';
  
  // Normalize whitespace
  content = content.replace(/\s+/g, ' ');
  
  // Remove excessive line breaks
  content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  // Remove common navigation text
  const navigationPatterns = [
    /Skip to main content/gi,
    /Home\s*>\s*[^>]*>/gi,
    /Breadcrumb/gi,
    /Print this page/gi,
    /Share this page/gi,
    /Last updated:/gi
  ];
  
  navigationPatterns.forEach(pattern => {
    content = content.replace(pattern, '');
  });
  
  // Trim and return
  return content.trim();
}

// Utility function to extract specific regulation sections
export function extractRegulationSections(content, concepts) {
  if (!content || !concepts.length) return content;
  
  const lines = content.split('\n');
  const relevantSections = [];
  let currentSection = [];
  let isRelevantSection = false;
  
  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    
    // Check if this line starts a new section
    if (/^\d+\.\d+|^Section \d+|^Chapter \d+/i.test(line.trim())) {
      // Save previous section if it was relevant
      if (isRelevantSection && currentSection.length > 0) {
        relevantSections.push(currentSection.join('\n'));
      }
      
      // Start new section
      currentSection = [line];
      isRelevantSection = concepts.some(concept => 
        lowerLine.includes(concept.toLowerCase())
      );
    } else {
      // Add line to current section
      currentSection.push(line);
      
      // Check if this line makes the section relevant
      if (!isRelevantSection) {
        isRelevantSection = concepts.some(concept => 
          lowerLine.includes(concept.toLowerCase())
        );
      }
    }
  }
  
  // Don't forget the last section
  if (isRelevantSection && currentSection.length > 0) {
    relevantSections.push(currentSection.join('\n'));
  }
  
  return relevantSections.length > 0 ? relevantSections.join('\n\n') : content;
}

// Export utility functions for testing
export { parseContent, cleanContent, isMunicipalCodeSite };
