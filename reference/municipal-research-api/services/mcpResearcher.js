// services/mcpResearcher.js - Two-stage cost-optimized municipal research
import { config } from '../config.js';

export class MCPResearcher {
  constructor() {
    this.perplexityApiKey = process.env.PERPLEXITY_API_KEY;
    this.geminiApiKey = process.env.GEMINI_API_KEY;

    // Cost tracking counters
    this.costCounters = {
      perplexity: { requests: 0, tokens: 0, cost: 0 },
      gemini: { requests: 0, inputTokens: 0, outputTokens: 0, cost: 0 },
      total: { requests: 0, cost: 0 }
    };

    // Pricing constants (per 1M tokens)
    this.pricing = {
      perplexity: 1.00,
      gemini: {
        input: 0.10,
        output: 0.40
      }
    };
  }

  async performMunicipalResearch(jurisdiction, query) {
    console.log(`🔬 Two-Stage Research: "${query}" in ${jurisdiction}`);

    try {
      // Stage 1: Get raw municipal data (expensive, cached)
      const rawData = await this.getRawMunicipalData(jurisdiction, query);

      // Stage 2: Format for specific query (cheap, per-request)
      const formattedAnswer = await this.formatWithGemini(rawData, jurisdiction, query);

      // Update cost counters
      this.costCounters.total.requests++;
      this.costCounters.total.cost = this.costCounters.perplexity.cost + this.costCounters.gemini.cost;

      // Log cost summary
      this.logCostSummary();

      return {
        answer: formattedAnswer,
        sources: rawData.sources,
        confidence: this.calculateConfidence(rawData),
        method: 'two_stage_optimized',
        costs: {
          perplexity: this.costCounters.perplexity.cost,
          gemini: this.costCounters.gemini.cost,
          total: this.costCounters.total.cost
        }
      };

    } catch (error) {
      console.error('❌ Two-stage research failed:', error);
      throw new Error(`Two-stage research failed: ${error.message}`);
    }
  }

  // Stage 1: Get raw municipal data using Perplexity (expensive, cached)
  async getRawMunicipalData(jurisdiction, query) {
    console.log(`📊 Stage 1: Collecting raw data for ${jurisdiction}`);

    // Check if we already have raw data for this jurisdiction/topic
    const cacheKey = `raw_${jurisdiction.toLowerCase().replace(/[^a-z0-9]/g, '-')}_${this.extractTopicFromQuery(query)}`;

    // For now, always fetch fresh data (later we'll add proper caching)
    return await this.callPerplexityForRawData(jurisdiction, query);
  }

  async callPerplexityForRawData(jurisdiction, query) {
    console.log('🧠 Perplexity: Collecting topic-focused municipal data...');

    const topic = this.extractTopicFromQuery(query);
    console.log(`🎯 Extracted topic: ${topic}`);

    // Topic-focused prompt for efficient data collection
    const dataCollectionPrompt = `Research municipal regulations for ${jurisdiction} specifically related to: ${query}

Focus ONLY on collecting detailed information about this specific topic area:
- Exact measurements, distances, and dimensions
- Specific ordinance section numbers and citations
- Permit requirements and approval processes
- Allowable exceptions and special circumstances
- Contact information for relevant departments
- Official sources and documentation links

Topic category: ${topic}

Provide comprehensive but focused information. Include exact numbers, section references, and official sources. Do not format or summarize - collect all relevant regulatory details for this specific topic only.`;

    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.perplexityApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'sonar-pro',
        messages: [
          {
            role: 'system',
            content: 'You are a municipal data collector. Gather comprehensive, factual information about local regulations. Focus on collecting data, not formatting or analysis.'
          },
          {
            role: 'user',
            content: dataCollectionPrompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.1,
        return_citations: true,
        return_images: false
      })
    });

    if (!response.ok) {
      throw new Error(`Perplexity API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Update cost tracking
    const cost = this.calculatePerplexityCost(data.usage);
    this.costCounters.perplexity.requests++;
    this.costCounters.perplexity.tokens += data.usage.total_tokens || 0;
    this.costCounters.perplexity.cost += cost;

    console.log(`💰 Perplexity data collection: $${cost.toFixed(4)}`);

    return {
      content: data.choices[0].message.content,
      sources: this.extractSources({ citations: data.citations || [], content: data.choices[0].message.content }),
      usage: data.usage,
      cost: cost
    };
  }



  // Stage 2: Format raw data for specific query using Gemini (cheap, per-request)
  async formatWithGemini(rawData, jurisdiction, query) {
    console.log('🎨 Stage 2: Formatting with Gemini 2.5 Flash-Lite...');

    const formatPrompt = `You are a municipal research expert. Format the following raw municipal data into a comprehensive, professional answer for this specific question: "${query}" in ${jurisdiction}

Use this exact structure:

RESEARCH QUESTION: "${query}" for ${jurisdiction}

GOVERNING JURISDICTION
[Explanation of which jurisdiction governs this property/area]

SPECIFIC REQUIREMENTS
According to [Ordinance Citation], the requirements are:

1. [Category 1]:
- [Specific requirement with measurements]
- [Exception if any]

2. [Category 2]:
- [Specific requirement with measurements]
- [Exception if any]

ADDITIONAL RESTRICTIONS
- [Any additional requirements]
- [Special circumstances]
- [Development triggers]

OFFICIAL SOURCES
[Primary ordinance citation]
- Available online at: [URL]
- Additional sources: [URLs if applicable]

CONTACT FOR VERIFICATION
[Department name]
- Address: [Physical address]
- Phone: [Phone number]
- Website: [Website URL]

RECOMMENDATION: [Specific recommendation for the user]

RAW MUNICIPAL DATA TO FORMAT:
${rawData.content}

Important: Use ONLY information found in the raw data. Include exact measurements, section numbers, and specific requirements. Focus specifically on answering "${query}".`;

    const response = await this.callGeminiAPI(formatPrompt);
    return response;
  }

  async callGeminiAPI(prompt) {
    console.log('🤖 Calling Gemini 2.5 Flash-Lite for formatting...');

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${this.geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: 2000,
          topP: 0.8,
          topK: 10
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Update cost tracking
    const inputTokens = this.estimateTokens(prompt);
    const outputTokens = this.estimateTokens(data.candidates[0].content.parts[0].text);
    const cost = this.calculateGeminiCost(inputTokens, outputTokens);

    this.costCounters.gemini.requests++;
    this.costCounters.gemini.inputTokens += inputTokens;
    this.costCounters.gemini.outputTokens += outputTokens;
    this.costCounters.gemini.cost += cost;

    console.log(`💰 Gemini formatting: $${cost.toFixed(4)} (${inputTokens} in, ${outputTokens} out)`);

    return data.candidates[0].content.parts[0].text;
  }

  // Cost calculation methods
  calculatePerplexityCost(usage) {
    const tokens = usage.total_tokens || 0;
    return (tokens / 1000000) * this.pricing.perplexity;
  }

  calculateGeminiCost(inputTokens, outputTokens) {
    const inputCost = (inputTokens / 1000000) * this.pricing.gemini.input;
    const outputCost = (outputTokens / 1000000) * this.pricing.gemini.output;
    return inputCost + outputCost;
  }

  estimateTokens(text) {
    // Rough estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  extractTopicFromQuery(query) {
    // NLP-based topic extraction for intelligent caching
    const queryLower = query.toLowerCase();

    // Fence/boundary related
    if (/fence|fencing|boundary|barrier|wall|gate/i.test(query)) {
      return 'fence-regulations';
    }

    // Setback/distance related
    if (/setback|distance|spacing|buffer|yard|front|rear|side|property.*line|lot.*line|boundary.*line/i.test(query)) {
      return 'setback-requirements';
    }

    // Height/size related
    if (/height|tall|high|size|dimension|maximum|minimum/i.test(query)) {
      return 'height-size-limits';
    }

    // Parking related
    if (/parking|garage|driveway|vehicle|car|space/i.test(query)) {
      return 'parking-requirements';
    }

    // Permit/approval related
    if (/permit|approval|license|application|permission|need.*build/i.test(query)) {
      return 'permit-requirements';
    }

    // ADU/dwelling related
    if (/adu|accessory.*dwelling|guest.*house|in-law|granny|secondary.*unit/i.test(query)) {
      return 'adu-regulations';
    }

    // Deck/structure related
    if (/deck|patio|structure|addition|shed|gazebo/i.test(query)) {
      return 'structure-regulations';
    }

    // Zoning general
    if (/zoning|zone|district|residential|commercial/i.test(query)) {
      return 'zoning-regulations';
    }

    // Building general
    if (/building|construction|build|renovate|remodel/i.test(query)) {
      return 'building-regulations';
    }

    // Default for unmatched queries
    return 'general-municipal';
  }

  logCostSummary() {
    console.log('\n💰 COST SUMMARY:');
    console.log(`   Perplexity: ${this.costCounters.perplexity.requests} requests, $${this.costCounters.perplexity.cost.toFixed(4)}`);
    console.log(`   Gemini: ${this.costCounters.gemini.requests} requests, $${this.costCounters.gemini.cost.toFixed(4)}`);
    console.log(`   Total: ${this.costCounters.total.requests} requests, $${this.costCounters.total.cost.toFixed(4)}`);
    console.log(`   Avg per request: $${(this.costCounters.total.cost / Math.max(this.costCounters.total.requests, 1)).toFixed(4)}\n`);
  }

  extractSources(data) {
    const sources = [];

    // Extract from citations
    if (data.citations) {
      sources.push(...data.citations.map(citation => citation.url || citation));
    }

    // Extract URLs from content
    const urlRegex = /https?:\/\/[^\s\)]+/g;
    const urls = data.content.match(urlRegex) || [];
    sources.push(...urls);

    // Remove duplicates and return
    return [...new Set(sources)].slice(0, 5);
  }

  calculateConfidence(rawData) {
    let confidence = 0.3; // Base confidence

    const content = rawData.content.toLowerCase();

    // Boost confidence for specific details
    if (/\d+\s*(feet|ft|inches|in)\b/.test(content)) confidence += 0.2;
    if (/section\s*\d+|sec\.\s*\d+|§\s*\d+/.test(content)) confidence += 0.2;
    if (rawData.sources && rawData.sources.length > 0) confidence += 0.2;
    if (content.length > 1000) confidence += 0.1;

    return Math.min(confidence, 0.95); // Cap at 95%
  }
}

// Create singleton instance
export const mcpResearcher = new MCPResearcher();
