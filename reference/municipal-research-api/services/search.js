// services/search.js
import { config } from '../config.js';

export async function findMunicipalSources(jurisdiction, query) {
  console.log(`🔍 Finding municipal sources for "${query}" in ${jurisdiction}`);
  
  try {
    // Step 1: Extract concepts from the query
    const concepts = extractConcepts(query);
    console.log(`📝 Extracted concepts: ${concepts.join(', ')}`);
    
    // Step 2: Generate targeted search queries
    const searchQueries = generateSearchQueries(jurisdiction, concepts);
    console.log(`🎯 Generated ${searchQueries.length} search queries`);
    
    // Step 3: Execute searches
    const allResults = [];
    for (const searchQuery of searchQueries) {
      try {
        console.log(`🔍 Searching: "${searchQuery}"`);
        const results = await googleCustomSearch(searchQuery);
        if (results.items) {
          allResults.push(...results.items);
        }
      } catch (error) {
        console.warn(`⚠️ Search failed for "${searchQuery}":`, error.message);
        // Continue with other searches
      }
    }
    
    // Step 4: Filter and rank official sources
    const officialSources = filterOfficialSources(allResults);
    console.log(`✅ Found ${officialSources.length} official sources`);
    
    return officialSources;
    
  } catch (error) {
    console.error('❌ Municipal source discovery failed:', error);
    throw new Error(`Failed to find municipal sources: ${error.message}`);
  }
}

export function extractConcepts(query) {
  const conceptMap = {
    fence: ['fence', 'fencing', 'barrier', 'enclosure'],
    height: ['height', 'tall', 'high', 'feet', 'foot', 'ft'],
    setback: ['setback', 'property line', 'boundary', 'yard'],
    permit: ['permit', 'permission', 'approval', 'license'],
    building: ['building', 'structure', 'construction', 'erect'],
    deck: ['deck', 'patio', 'porch', 'platform'],
    pool: ['pool', 'swimming', 'spa', 'hot tub'],
    shed: ['shed', 'storage', 'outbuilding', 'accessory'],
    driveway: ['driveway', 'parking', 'pavement', 'concrete'],
    tree: ['tree', 'vegetation', 'landscaping', 'removal'],
    sign: ['sign', 'signage', 'advertisement', 'display'],
    business: ['business', 'commercial', 'home occupation'],
    zoning: ['zoning', 'zone', 'district', 'classification'],
    variance: ['variance', 'exception', 'waiver', 'appeal']
  };
  
  const concepts = [];
  const lowerQuery = query.toLowerCase();
  
  for (const [concept, keywords] of Object.entries(conceptMap)) {
    if (keywords.some(keyword => lowerQuery.includes(keyword))) {
      concepts.push(concept);
    }
  }
  
  // If no specific concepts found, use general terms
  if (concepts.length === 0) {
    concepts.push('ordinance', 'regulation', 'code');
  }
  
  return concepts;
}

function generateSearchQueries(jurisdiction, concepts) {
  const queries = [];
  
  // Primary concept-based searches
  const conceptCombos = [
    concepts.join(' '),
    concepts.slice(0, 2).join(' '), // Top 2 concepts
    concepts[0] // Primary concept only
  ].filter(combo => combo.length > 0);
  
  for (const combo of conceptCombos) {
    queries.push(`"${jurisdiction}" ${combo} ordinance`);
    queries.push(`"${jurisdiction}" zoning code ${combo}`);
    queries.push(`"${jurisdiction}" municipal code ${combo}`);
  }
  
  // Specific document type searches
  queries.push(`"${jurisdiction}" zoning ordinance`);
  queries.push(`"${jurisdiction}" building code`);
  queries.push(`"${jurisdiction}" municipal ordinances`);
  
  // Remove duplicates and limit to top 8 queries
  return [...new Set(queries)].slice(0, 8);
}

async function googleCustomSearch(query) {
  const url = `https://www.googleapis.com/customsearch/v1?key=${config.google.searchApiKey}&cx=${config.google.searchEngineId}&q=${encodeURIComponent(query)}&num=10`;
  
  const response = await fetch(url);
  const data = await response.json();
  
  if (data.error) {
    throw new Error(`Search API error: ${data.error.message}`);
  }
  
  return data;
}

function filterOfficialSources(results) {
  // Define official domain patterns
  const officialPatterns = [
    /\.gov$/,
    /\.org$/,
    /municode\.com/,
    /codepublishing\.com/,
    /generalcode\.com/,
    /ecode360\.com/,
    /amlegal\.com/,
    /codelibrary\.amlegal\.com/,
    /library\.qcode\.us/
  ];
  
  // Score and filter results
  const scoredResults = results
    .filter(result => result.link && result.title)
    .map(result => ({
      ...result,
      score: calculateSourceScore(result, officialPatterns)
    }))
    .filter(result => result.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, 5); // Top 5 sources
  
  return scoredResults;
}

function calculateSourceScore(result, officialPatterns) {
  let score = 0;
  const url = result.link.toLowerCase();
  const title = result.title.toLowerCase();
  const snippet = (result.snippet || '').toLowerCase();
  
  // Official domain bonus
  if (officialPatterns.some(pattern => pattern.test(url))) {
    score += 100;
  }
  
  // .gov domains get highest priority
  if (url.includes('.gov')) {
    score += 50;
  }
  
  // Municipal code sites
  if (url.includes('municode') || url.includes('codepublishing')) {
    score += 40;
  }
  
  // Title relevance
  const titleKeywords = ['ordinance', 'code', 'zoning', 'municipal', 'regulation'];
  titleKeywords.forEach(keyword => {
    if (title.includes(keyword)) score += 10;
  });
  
  // Content relevance
  const contentKeywords = ['section', 'chapter', 'ordinance', 'shall', 'permitted'];
  contentKeywords.forEach(keyword => {
    if (snippet.includes(keyword)) score += 5;
  });
  
  // Penalty for non-official sources
  const nonOfficialPatterns = [
    'wikipedia', 'reddit', 'facebook', 'twitter', 'blog', 'forum'
  ];
  if (nonOfficialPatterns.some(pattern => url.includes(pattern))) {
    score -= 50;
  }
  
  return score;
}

// Export utility functions for testing
export { generateSearchQueries, calculateSourceScore };
