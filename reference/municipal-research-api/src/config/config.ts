import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from the root directory
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

export const config = {
  environment: process.env.NODE_ENV || 'development',
  
  server: {
    port: parseInt(process.env.PORT || '3001'),
    timeout: parseInt(process.env.REQUEST_TIMEOUT || '30000'),
  },

  cors: {
    allowedOrigins: process.env.NODE_ENV === 'production' 
      ? ['https://ordrly.ai', 'https://api.ordrly.ai']
      : ['http://localhost:3000', 'http://localhost:3001'],
  },

  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  },

  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY!,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
    priceIds: {
      pro: process.env.STRIPE_PRICE_PRO!,
      appraiser: process.env.STRIPE_PRICE_APPRAISER!,
    },
  },

  ai: {
    gemini: {
      apiKey: 'AIzaSyBoT9p4RFNk4-c-SCQyLc07-lGJ8-RxN5E',
      model: 'gemini-1.5-flash',
      maxTokens: parseInt(process.env.GEMINI_MAX_TOKENS || '8192'),
      temperature: parseFloat(process.env.GEMINI_TEMPERATURE || '0.1'),
    },
    perplexity: {
      apiKey: process.env.PERPLEXITY_API_KEY!,
      model: 'sonar',
    },
  },

  google: {
    searchApiKey: process.env.GOOGLE_SEARCH_API_KEY!,
    searchEngineId: process.env.GOOGLE_SEARCH_ENGINE_ID!,
    geocodingApiKey: process.env.GOOGLE_GEOCODING_API_KEY!,
  },

  cache: {
    defaultTtl: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    maxEntries: 10000,
  },

  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    skipSuccessfulRequests: false,
  },

  pricing: {
    perplexity: {
      requestCost: 0.01, // $0.01 per request
      inputTokenCost: 3.0, // $3 per 1M tokens
      outputTokenCost: 15.0, // $15 per 1M tokens
    },
    gemini: {
      inputTokenCost: 0.30, // $0.30 per 1M tokens
      outputTokenCost: 2.50, // $2.50 per 1M tokens
    },
  },

  trial: {
    defaultRequests: 20,
    defaultDays: 7,
    codeLength: 5,
  },
};

// Validation function
export function validateConfig(): boolean {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'PERPLEXITY_API_KEY',
    'GOOGLE_GEOCODING_API_KEY',
    'STRIPE_SECRET_KEY',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:', missing);
    return false;
  }

  console.log('✅ All required environment variables are present');
  return true;
}
