import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

// Extend Request interface to include user and api<PERSON><PERSON>
declare global {
  namespace Express {
    interface Request {
      user?: any;
      apiKey?: any;
      usage?: {
        requestsThisMonth: number;
        limit: number;
      };
    }
  }
}

export interface ApiKeyData {
  id: string;
  user_id: string;
  name: string;
  is_active: boolean;
  rate_limit_per_hour: number;
  usage_count: number;
  last_used_at: string;
  expires_at?: string;
  allowed_endpoints: string[];
  metadata: any;
}

/**
 * Middleware to authenticate API requests using API keys
 */
export async function authenticateApiKey(req: Request, res: Response, next: NextFunction) {
  try {
    const apiKey = req.headers['x-api-key'] as string;

    if (!apiKey) {
      return res.status(401).json({
        success: false,
        error: 'API key required',
        message: 'Please provide a valid API key in the X-API-Key header'
      });
    }

    // Hash the API key for lookup (assuming keys are stored hashed)
    const crypto = await import('crypto');
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    // Look up API key in database
    const { data: apiKeyData, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('key_hash', keyHash)
      .eq('is_active', true)
      .single();

    if (error || !apiKeyData) {
      logger.warn(`Invalid API key attempt: ${apiKey.substring(0, 8)}...`);
      return res.status(401).json({
        success: false,
        error: 'Invalid API key',
        message: 'The provided API key is invalid or inactive'
      });
    }

    // Check if API key has expired
    if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
      return res.status(401).json({
        success: false,
        error: 'API key expired',
        message: 'The provided API key has expired'
      });
    }

    // Check if endpoint is allowed
    const endpoint = req.path;
    const allowedEndpoints = apiKeyData.allowed_endpoints || ['*'];
    const isEndpointAllowed = allowedEndpoints.includes('*') || 
      allowedEndpoints.some((pattern: string) => endpoint.startsWith(pattern));

    if (!isEndpointAllowed) {
      return res.status(403).json({
        success: false,
        error: 'Endpoint not allowed',
        message: 'This API key does not have access to this endpoint'
      });
    }

    // Get user profile if user_id exists
    let userData = null;
    if (apiKeyData.user_id) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', apiKeyData.user_id)
        .single();
      userData = profile;
    }

    // Update last_used_at and increment usage_count
    await supabase
      .from('api_keys')
      .update({
        last_used_at: new Date().toISOString(),
        usage_count: (apiKeyData.usage_count || 0) + 1
      })
      .eq('id', apiKeyData.id);

    // Attach data to request
    req.apiKey = apiKeyData;
    req.user = userData;

    logger.info(`API key authenticated: ${apiKeyData.name} (${apiKeyData.id})`);
    next();

  } catch (error) {
    logger.error('API key authentication error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication error',
      message: 'An error occurred during authentication'
    });
  }
}

/**
 * Middleware to check rate limits for API keys
 */
export async function checkRateLimit(req: Request, res: Response, next: NextFunction) {
  try {
    const apiKeyData = req.apiKey as ApiKeyData;
    
    if (!apiKeyData) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Check hourly rate limit
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const { count, error } = await supabase
      .from('municipal_api_usage')
      .select('*', { count: 'exact', head: true })
      .eq('api_key_id', apiKeyData.id)
      .gte('created_at', oneHourAgo.toISOString());

    if (error) {
      logger.error('Rate limit check error:', error);
      return res.status(500).json({
        success: false,
        error: 'Rate limit check failed'
      });
    }

    const requestsThisHour = count || 0;
    const hourlyLimit = apiKeyData.rate_limit_per_hour || 1000;

    if (requestsThisHour >= hourlyLimit) {
      return res.status(429).json({
        success: false,
        error: 'Rate limit exceeded',
        message: `You have exceeded the hourly rate limit of ${hourlyLimit} requests`,
        retryAfter: 3600 // 1 hour in seconds
      });
    }

    // Add rate limit info to response headers
    res.set({
      'X-RateLimit-Limit': hourlyLimit.toString(),
      'X-RateLimit-Remaining': (hourlyLimit - requestsThisHour).toString(),
      'X-RateLimit-Reset': new Date(Date.now() + 60 * 60 * 1000).toISOString()
    });

    next();

  } catch (error) {
    logger.error('Rate limit middleware error:', error);
    return res.status(500).json({
      success: false,
      error: 'Rate limit check failed'
    });
  }
}
