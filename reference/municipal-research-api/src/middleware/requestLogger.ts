import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger.js';

/**
 * Request logging middleware
 */
export function requestLogger(req: Request, res: Response, next: NextFunction) {
  const startTime = Date.now();
  
  // Log request start
  logger.http(`${req.method} ${req.url} - ${req.ip}`);
  
  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any, cb?: any) {
    const duration = Date.now() - startTime;

    // Log response
    logger.http(
      `${req.method} ${req.url} - ${res.statusCode} - ${duration}ms - ${req.ip}`
    );

    // Call original end method
    return originalEnd.call(res, chunk, encoding, cb);
  } as any;
  
  next();
}
