import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { authenticateApiKey } from '../middleware/auth.js';
import { asyncHandler, ValidationError } from '../middleware/errorHandler.js';
import { usageTracker } from '../services/usageTracker.js';

import { logger } from '../utils/logger.js';

const router = Router();

// Validation schemas
const analyticsQuerySchema = z.object({
  days: z.number().min(1).max(365).optional().default(30),
  month: z.string().regex(/^\d{4}-\d{2}$/).optional() // YYYY-MM format
});

/**
 * GET /api/v1/analytics/usage
 * Get usage statistics for the authenticated API key
 */
router.get('/usage',
  authenticateApiKey,
  asyncHandler(async (req: Request, res: Response) => {
    const validation = analyticsQuerySchema.safeParse(req.query);
    if (!validation.success) {
      throw new ValidationError('Invalid query parameters', validation.error.errors);
    }

    const { days } = validation.data;
    const apiKeyData = req.apiKey!;

    const stats = await usageTracker.getUsageStats(apiKeyData.id, days);

    res.json({
      success: true,
      data: stats,
      meta: {
        apiKeyId: apiKeyData.id,
        apiKeyName: apiKeyData.name,
        period: `${days} days`,
        timestamp: new Date().toISOString()
      }
    });
  })
);

/**
 * GET /api/v1/analytics/billing
 * Get billing information for the authenticated API key
 */
router.get('/billing',
  authenticateApiKey,
  asyncHandler(async (req: Request, res: Response) => {
    const validation = analyticsQuerySchema.safeParse(req.query);
    if (!validation.success) {
      throw new ValidationError('Invalid query parameters', validation.error.errors);
    }

    const { month } = validation.data;
    const apiKeyData = req.apiKey!;

    const billingSummary = await usageTracker.getBillingSummary(apiKeyData.id, month);

    res.json({
      success: true,
      data: billingSummary,
      meta: {
        apiKeyId: apiKeyData.id,
        apiKeyName: apiKeyData.name,
        period: month || 'current month',
        timestamp: new Date().toISOString()
      }
    });
  })
);



/**
 * GET /api/v1/analytics/performance
 * Get performance metrics for the authenticated API key
 */
router.get('/performance',
  authenticateApiKey,
  asyncHandler(async (req: Request, res: Response) => {
    const validation = analyticsQuerySchema.safeParse(req.query);
    if (!validation.success) {
      throw new ValidationError('Invalid query parameters', validation.error.errors);
    }

    const { days } = validation.data;
    const apiKeyData = req.apiKey!;

    const stats = await usageTracker.getUsageStats(apiKeyData.id, days);

    const performanceMetrics = {
      responseTime: {
        average: stats.avgResponseTime,
        trend: 'stable' // Could be calculated from historical data
      },
      reliability: {
        successRate: stats.totalRequests > 0 
          ? (stats.successfulRequests / stats.totalRequests) * 100 
          : 0,
        totalRequests: stats.totalRequests,
        failedRequests: stats.failedRequests
      },
      efficiency: {
        cacheHitRate: stats.cacheHitRate,
        avgConfidence: stats.avgConfidence,
        costEfficiency: stats.totalRequests > 0 
          ? stats.totalCost / stats.totalRequests 
          : 0
      },
      usage: {
        requestsByDay: stats.requestsByDay,
        topEndpoints: stats.topEndpoints,
        topTopics: stats.topTopics
      }
    };

    res.json({
      success: true,
      data: performanceMetrics,
      meta: {
        apiKeyId: apiKeyData.id,
        apiKeyName: apiKeyData.name,
        period: `${days} days`,
        timestamp: new Date().toISOString()
      }
    });
  })
);

/**
 * GET /api/v1/analytics/summary
 * Get a comprehensive summary of API usage and performance
 */
router.get('/summary',
  authenticateApiKey,
  asyncHandler(async (req: Request, res: Response) => {
    const apiKeyData = req.apiKey!;

    // Get stats for different periods
    const [last7Days, last30Days, currentMonth] = await Promise.all([
      usageTracker.getUsageStats(apiKeyData.id, 7),
      usageTracker.getUsageStats(apiKeyData.id, 30),
      usageTracker.getBillingSummary(apiKeyData.id)
    ]);

    const summary = {
      overview: {
        apiKeyName: apiKeyData.name,
        createdAt: apiKeyData.created_at,
        lastUsed: apiKeyData.last_used_at,
        totalUsage: apiKeyData.usage_count || 0,
        rateLimit: apiKeyData.rate_limit_per_hour
      },
      currentMonth: {
        requests: currentMonth.totalRequests,
        cost: currentMonth.totalCost,
        cacheHitRate: currentMonth.cacheHits > 0 
          ? (currentMonth.cacheHits / currentMonth.totalRequests) * 100 
          : 0,
        avgCostPerRequest: currentMonth.avgCostPerRequest
      },
      last30Days: {
        requests: last30Days.totalRequests,
        avgResponseTime: last30Days.avgResponseTime,
        successRate: last30Days.totalRequests > 0 
          ? (last30Days.successfulRequests / last30Days.totalRequests) * 100 
          : 0,
        cacheHitRate: last30Days.cacheHitRate
      },
      last7Days: {
        requests: last7Days.totalRequests,
        trend: last7Days.requestsByDay.slice(-7),
        topTopics: last7Days.topTopics.slice(0, 5)
      },
      recommendations: generateRecommendations(last30Days, currentMonth)
    };

    res.json({
      success: true,
      data: summary,
      meta: {
        apiKeyId: apiKeyData.id,
        timestamp: new Date().toISOString()
      }
    });
  })
);

/**
 * Generate usage recommendations based on analytics
 */
function generateRecommendations(monthlyStats: any, billing: any): string[] {
  const recommendations: string[] = [];

  // Cache hit rate recommendations
  if (monthlyStats.cacheHitRate < 50) {
    recommendations.push('Consider optimizing your queries to improve cache hit rate and reduce costs');
  }

  // Cost optimization
  if (billing.avgCostPerRequest > 0.01) {
    recommendations.push('High cost per request detected. Review query patterns and consider bulk operations');
  }

  // Response time optimization
  if (monthlyStats.avgResponseTime > 15000) {
    recommendations.push('Response times are high. Consider using cached results or optimizing query complexity');
  }

  // Usage patterns
  if (monthlyStats.totalRequests < 10) {
    recommendations.push('Low usage detected. Explore more API features to maximize value');
  }

  // Success rate
  const successRate = monthlyStats.totalRequests > 0 
    ? (monthlyStats.successfulRequests / monthlyStats.totalRequests) * 100 
    : 100;
  
  if (successRate < 95) {
    recommendations.push('Error rate is elevated. Check request formatting and address validation');
  }

  if (recommendations.length === 0) {
    recommendations.push('Great job! Your API usage is optimized and performing well');
  }

  return recommendations;
}

export { router as analyticsRoutes };
