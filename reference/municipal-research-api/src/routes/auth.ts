import { Router, Request, Response } from 'express';
import { z } from 'zod';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import { createClient } from '@supabase/supabase-js';
import { asyncHandler, ValidationError, UnauthorizedError } from '../middleware/errorHandler.js';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const router = Router();
const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

// Validation schemas
const createApiKeySchema = z.object({
  name: z.string().min(1, 'API key name is required'),
  description: z.string().optional(),
  rateLimit: z.number().min(1).max(10000).optional(),
  allowedEndpoints: z.array(z.string()).optional(),
  expiresInDays: z.number().min(1).max(365).optional()
});

const trialCodeSchema = z.object({
  email: z.string().email('Valid email is required'),
  notes: z.string().optional()
});

/**
 * POST /api/v1/auth/api-keys
 * Create a new API key (requires user authentication)
 */
router.post('/api-keys',
  asyncHandler(async (req: Request, res: Response) => {
    // This would typically require user authentication
    // For now, we'll create a simple endpoint for testing
    
    const validation = createApiKeySchema.safeParse(req.body);
    if (!validation.success) {
      throw new ValidationError('Invalid API key data', validation.error.errors);
    }

    const { name, description, rateLimit, allowedEndpoints, expiresInDays } = validation.data;

    // Generate API key
    const apiKey = `ordrly_${crypto.randomBytes(32).toString('hex')}`;
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    // Set expiration if specified
    let expiresAt = null;
    if (expiresInDays) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresInDays);
    }

    const apiKeyData = {
      name,
      key_hash: keyHash,
      is_active: true,
      rate_limit_per_hour: rateLimit || 1000,
      allowed_endpoints: allowedEndpoints || ['*'],
      expires_at: expiresAt?.toISOString(),
      metadata: {
        description,
        createdBy: 'api',
        createdAt: new Date().toISOString()
      }
    };

    const { data, error } = await supabase
      .from('api_keys')
      .insert(apiKeyData)
      .select()
      .single();

    if (error) {
      logger.error('API key creation error:', error);
      throw new Error('Failed to create API key');
    }

    logger.info(`🔑 API key created: ${name} (${data.id})`);

    res.status(201).json({
      success: true,
      data: {
        id: data.id,
        name: data.name,
        apiKey, // Only returned once during creation
        rateLimit: data.rate_limit_per_hour,
        allowedEndpoints: data.allowed_endpoints,
        expiresAt: data.expires_at,
        createdAt: data.created_at
      },
      message: 'API key created successfully. Store this key securely - it will not be shown again.'
    });
  })
);

/**
 * POST /api/v1/auth/trial-codes
 * Generate a trial code
 */
router.post('/trial-codes',
  asyncHandler(async (req: Request, res: Response) => {
    const validation = trialCodeSchema.safeParse(req.body);
    if (!validation.success) {
      throw new ValidationError('Invalid trial code data', validation.error.errors);
    }

    const { email, notes } = validation.data;

    // Generate trial code
    const code = `TRIAL-${crypto.randomBytes(3).toString('hex').toUpperCase()}`;

    const trialData = {
      code,
      email,
      max_requests: config.trial.defaultRequests,
      valid_days: config.trial.defaultDays,
      notes: notes || `Trial code for ${email}`
    };

    const { data, error } = await supabase
      .from('trial_codes')
      .insert(trialData)
      .select()
      .single();

    if (error) {
      logger.error('Trial code creation error:', error);
      throw new Error('Failed to create trial code');
    }

    logger.info(`🎟️ Trial code created: ${code} for ${email}`);

    res.status(201).json({
      success: true,
      data: {
        code: data.code,
        email: data.email,
        maxRequests: data.max_requests,
        validDays: data.valid_days,
        expiresAt: data.expires_at,
        createdAt: data.created_at
      },
      message: 'Trial code created successfully'
    });
  })
);

/**
 * POST /api/v1/auth/trial-codes/activate
 * Activate a trial code and create temporary API key
 */
router.post('/trial-codes/activate',
  asyncHandler(async (req: Request, res: Response) => {
    const { code, email } = req.body;

    if (!code || !email) {
      throw new ValidationError('Trial code and email are required');
    }

    // Find and validate trial code
    const { data: trialCode, error: trialError } = await supabase
      .from('trial_codes')
      .select('*')
      .eq('code', code)
      .eq('is_active', true)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (trialError || !trialCode) {
      throw new UnauthorizedError('Invalid or expired trial code');
    }

    if (trialCode.email && trialCode.email !== email) {
      throw new UnauthorizedError('Trial code is not valid for this email');
    }

    // Check if already activated
    if (trialCode.activated_at) {
      throw new ValidationError('Trial code has already been activated');
    }

    // Generate temporary API key for trial
    const apiKey = `trial_${crypto.randomBytes(24).toString('hex')}`;
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    // Create API key with trial limitations
    const apiKeyData = {
      name: `Trial: ${email}`,
      key_hash: keyHash,
      is_active: true,
      rate_limit_per_hour: Math.min(trialCode.max_requests, 100), // Limit trial rate
      allowed_endpoints: ['/api/v1/research'], // Only research endpoint
      expires_at: trialCode.expires_at,
      metadata: {
        type: 'trial',
        trialCodeId: trialCode.id,
        email,
        maxRequests: trialCode.max_requests
      }
    };

    const { data: newApiKey, error: apiKeyError } = await supabase
      .from('api_keys')
      .insert(apiKeyData)
      .select()
      .single();

    if (apiKeyError) {
      logger.error('Trial API key creation error:', apiKeyError);
      throw new Error('Failed to create trial API key');
    }

    // Mark trial code as activated
    await supabase
      .from('trial_codes')
      .update({
        activated_at: new Date().toISOString(),
        api_key_id: newApiKey.id
      })
      .eq('id', trialCode.id);

    logger.info(`🎯 Trial activated: ${code} for ${email} -> API key ${newApiKey.id}`);

    res.json({
      success: true,
      data: {
        apiKey, // Only returned once
        maxRequests: trialCode.max_requests,
        expiresAt: trialCode.expires_at,
        rateLimit: newApiKey.rate_limit_per_hour,
        allowedEndpoints: newApiKey.allowed_endpoints
      },
      message: 'Trial activated successfully. Use this API key for testing.'
    });
  })
);

/**
 * GET /api/v1/auth/trial-codes/:code/status
 * Check trial code status
 */
router.get('/trial-codes/:code/status',
  asyncHandler(async (req: Request, res: Response) => {
    const { code } = req.params;

    const { data: trialCode, error } = await supabase
      .from('trial_codes')
      .select('code, email, max_requests, requests_used, valid_days, is_active, created_at, expires_at, activated_at')
      .eq('code', code)
      .single();

    if (error || !trialCode) {
      throw new ValidationError('Trial code not found');
    }

    const isExpired = new Date(trialCode.expires_at) < new Date();
    const isActive = trialCode.is_active && !isExpired;

    res.json({
      success: true,
      data: {
        code: trialCode.code,
        email: trialCode.email,
        maxRequests: trialCode.max_requests,
        requestsUsed: trialCode.requests_used,
        remainingRequests: Math.max(0, trialCode.max_requests - trialCode.requests_used),
        isActive,
        isExpired,
        activated: !!trialCode.activated_at,
        expiresAt: trialCode.expires_at,
        createdAt: trialCode.created_at
      }
    });
  })
);

export { router as authRoutes };
