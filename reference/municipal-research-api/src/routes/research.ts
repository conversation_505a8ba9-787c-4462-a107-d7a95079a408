import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiKey, checkRateLimit } from '../middleware/auth.js';
import { asyncHandler, ValidationError } from '../middleware/errorHandler.js';
import { municipalResearchService } from '../services/municipalResearch.js';
import { usageTracker } from '../services/usageTracker.js';

import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

const router = Router();

// Request validation schemas
const researchRequestSchema = z.object({
  address: z.string().min(5, 'Address must be at least 5 characters'),
  query: z.string().min(10, 'Query must be at least 10 characters'),
  metadata: z.object({
    userAgent: z.string().optional(),
    referrer: z.string().optional()
  }).optional()
});

const bulkResearchSchema = z.object({
  requests: z.array(researchRequestSchema).min(1).max(10, 'Maximum 10 requests per batch')
});

/**
 * POST /api/v1/research
 * Main municipal research endpoint
 */
router.post('/', 
  authenticateApiKey,
  checkRateLimit,
  asyncHandler(async (req: Request, res: Response) => {
    const startTime = Date.now();
    
    // Validate request
    const validation = researchRequestSchema.safeParse(req.body);
    if (!validation.success) {
      throw new ValidationError('Invalid request data', validation.error.errors);
    }

    const { address, query, metadata } = validation.data;
    const apiKeyData = req.apiKey!;
    const userData = req.user;

    logger.info(`🔍 Research request from API key: ${apiKeyData.name} - "${query}" for ${address}`);

    try {
      // Perform research
      const result = await municipalResearchService.performResearch({
        address,
        query,
        userId: userData?.id,
        apiKeyId: apiKeyData.id
      });

      const processingTime = Date.now() - startTime;

      // Track usage
      await usageTracker.logApiUsage({
        apiKeyId: apiKeyData.id,
        userId: userData?.id,
        endpoint: '/api/v1/research',
        method: 'POST',
        address,
        query,
        jurisdiction: result.jurisdiction,
        topicKey: result.topic,
        cacheHit: result.cached,
        responseTimeMs: processingTime,
        confidenceScore: result.confidence,
        sourcesCount: result.sources.length,
        costUsd: result.costUsd,
        statusCode: 200,
        requestIp: req.ip,
        userAgent: req.get('User-Agent') || metadata?.userAgent
      });

      // Prepare response
      const response = {
        success: true,
        data: {
          jurisdiction: result.jurisdiction,
          topic: result.topic,
          answer: result.answer,
          sources: result.sources,
          confidence: result.confidence,
          cached: result.cached,
          processingTimeMs: result.processingTimeMs,
          method: result.method
        },
        meta: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          apiVersion: '1.0',
          ...(result.cached && { cacheAge: 'N/A' })
        }
      };

      logger.info(`✅ Research completed for ${apiKeyData.name}: ${result.confidence} confidence, ${result.sources.length} sources`);

      res.json(response);

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      // Log failed usage
      await usageTracker.logApiUsage({
        apiKeyId: apiKeyData.id,
        userId: userData?.id,
        endpoint: '/api/v1/research',
        method: 'POST',
        address,
        query,
        responseTimeMs: processingTime,
        statusCode: 500,
        errorMessage: error instanceof Error ? error.message : String(error),
        requestIp: req.ip,
        userAgent: req.get('User-Agent')
      });

      throw error;
    }
  })
);

/**
 * POST /api/v1/research/bulk
 * Bulk research endpoint for multiple queries
 */
router.post('/bulk',
  authenticateApiKey,
  checkRateLimit,
  asyncHandler(async (req: Request, res: Response) => {
    const validation = bulkResearchSchema.safeParse(req.body);
    if (!validation.success) {
      throw new ValidationError('Invalid bulk request data', validation.error.errors);
    }

    const { requests } = validation.data;
    const apiKeyData = req.apiKey!;
    const userData = req.user;

    logger.info(`📦 Bulk research request from ${apiKeyData.name}: ${requests.length} queries`);

    const results = [];
    let totalCost = 0;

    for (const [index, request] of requests.entries()) {
      try {
        const result = await municipalResearchService.performResearch({
          address: request.address,
          query: request.query,
          userId: userData?.id,
          apiKeyId: apiKeyData.id
        });

        results.push({
          index,
          success: true,
          data: {
            jurisdiction: result.jurisdiction,
            topic: result.topic,
            answer: result.answer,
            sources: result.sources,
            confidence: result.confidence,
            cached: result.cached,
            processingTimeMs: result.processingTimeMs
          }
        });

        totalCost += result.costUsd;

        // Track individual usage
        await usageTracker.logApiUsage({
          apiKeyId: apiKeyData.id,
          userId: userData?.id,
          endpoint: '/api/v1/research/bulk',
          method: 'POST',
          address: request.address,
          query: request.query,
          jurisdiction: result.jurisdiction,
          topicKey: result.topic,
          cacheHit: result.cached,
          confidenceScore: result.confidence,
          sourcesCount: result.sources.length,
          costUsd: result.costUsd,
          statusCode: 200,
          requestIp: req.ip,
          userAgent: req.get('User-Agent')
        });

      } catch (error) {
        results.push({
          index,
          success: false,
          error: {
            message: error instanceof Error ? error.message : String(error),
            code: 'RESEARCH_FAILED'
          }
        });

        // Track failed usage
        await usageTracker.logApiUsage({
          apiKeyId: apiKeyData.id,
          userId: userData?.id,
          endpoint: '/api/v1/research/bulk',
          method: 'POST',
          address: request.address,
          query: request.query,
          statusCode: 500,
          errorMessage: error instanceof Error ? error.message : String(error),
          requestIp: req.ip,
          userAgent: req.get('User-Agent')
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    
    res.json({
      success: true,
      data: {
        results,
        summary: {
          total: requests.length,
          successful: successCount,
          failed: requests.length - successCount,
          totalCost: Math.round(totalCost * 10000) / 10000
        }
      },
      meta: {
        requestId: `bulk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        apiVersion: '1.0'
      }
    });
  })
);

/**
 * GET /api/v1/research/topics
 * Get available research topics
 */
router.get('/topics',
  authenticateApiKey,
  asyncHandler(async (req: Request, res: Response) => {
    const { data: topics, error } = await supabase
      .from('research_topics')
      .select('topic_key, display_name, description, category, usage_count')
      .order('usage_count', { ascending: false });

    if (error) {
      throw new Error('Failed to fetch topics');
    }

    res.json({
      success: true,
      data: topics || [],
      meta: {
        timestamp: new Date().toISOString(),
        count: topics?.length || 0
      }
    });
  })
);



export { router as researchRoutes };
