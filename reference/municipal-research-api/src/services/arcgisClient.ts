/**
 * 🗺️ ARCGIS CLIENT
 * 
 * Client for querying ArcGIS REST API endpoints
 * - Handles point-in-polygon queries for property data
 * - Supports different coordinate systems
 * - Provides error handling and timeout management
 */

import axios from 'axios';
import { logger } from '../utils/logger.js';

export interface ArcGISQueryResult {
  [fieldName: string]: any;
}

export interface Coordinates {
  lat: number;
  lng: number;
}

export class ArcGISClient {
  private timeout = 10000; // 10 second timeout for ArcGIS queries

  /**
   * Query a point against an ArcGIS feature service
   */
  async queryPoint(
    serviceUrl: string,
    layerId: number,
    coordinates: Coordinates,
    outFields: string[] = ['*'],
    coordinateSystem: string = 'EPSG:4326'
  ): Promise<ArcGISQueryResult | null> {
    
    try {
      logger.info(`🗺️ Querying ArcGIS: ${serviceUrl}/${layerId} at ${coordinates.lat}, ${coordinates.lng}`);

      // Build the query URL
      const queryUrl = `${serviceUrl}/${layerId}/query`;
      
      // Create point geometry
      const pointGeometry = {
        x: coordinates.lng,
        y: coordinates.lat,
        spatialReference: { wkid: this.getWKID(coordinateSystem) }
      };

      // Build query parameters
      const params = {
        geometry: JSON.stringify(pointGeometry),
        geometryType: 'esriGeometryPoint',
        inSR: this.getWKID(coordinateSystem),
        spatialRel: 'esriSpatialRelIntersects',
        outFields: outFields.join(','),
        returnGeometry: false,
        f: 'json'
      };

      logger.info(`🗺️ ArcGIS query params:`, params);

      // Make the request
      const response = await axios.get(queryUrl, {
        params,
        timeout: this.timeout,
        headers: {
          'User-Agent': 'Ordrly-Municipal-Research-API/1.0'
        }
      });

      const data = response.data;

      // Check for ArcGIS errors
      if (data.error) {
        logger.error('ArcGIS API error:', data.error);
        throw new Error(`ArcGIS error: ${data.error.message || 'Unknown error'}`);
      }

      // Check if we got features back
      if (!data.features || data.features.length === 0) {
        logger.info(`🗺️ No features found at coordinates ${coordinates.lat}, ${coordinates.lng}`);
        return null;
      }

      // Return the attributes of the first feature
      const feature = data.features[0];
      const attributes = feature.attributes || {};

      logger.info(`🗺️ ArcGIS query successful: found ${data.features.length} features`);
      logger.info(`🗺️ Feature attributes:`, attributes);

      return attributes;

    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          logger.error(`ArcGIS query timeout for ${serviceUrl}`);
          throw new Error('ArcGIS service timeout');
        } else if (error.response) {
          logger.error(`ArcGIS HTTP error: ${error.response.status} - ${error.response.statusText}`);
          throw new Error(`ArcGIS service error: ${error.response.status}`);
        } else if (error.request) {
          logger.error(`ArcGIS network error: ${error.message}`);
          throw new Error('ArcGIS service unavailable');
        }
      }
      
      logger.error('ArcGIS query error:', error);
      throw new Error(`ArcGIS query failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Query multiple points in batch (for future use)
   */
  async queryMultiplePoints(
    serviceUrl: string,
    layerId: number,
    coordinates: Coordinates[],
    outFields: string[] = ['*'],
    coordinateSystem: string = 'EPSG:4326'
  ): Promise<(ArcGISQueryResult | null)[]> {
    
    const results: (ArcGISQueryResult | null)[] = [];
    
    // Process points sequentially to avoid overwhelming the service
    for (const coord of coordinates) {
      try {
        const result = await this.queryPoint(serviceUrl, layerId, coord, outFields, coordinateSystem);
        results.push(result);
        
        // Small delay between requests to be respectful
        await this.delay(100);
        
      } catch (error) {
        logger.error(`Failed to query point ${coord.lat}, ${coord.lng}:`, error);
        results.push(null);
      }
    }
    
    return results;
  }

  /**
   * Test if an ArcGIS service is available and responsive
   */
  async testService(serviceUrl: string, layerId: number = 0): Promise<boolean> {
    try {
      logger.info(`🧪 Testing ArcGIS service: ${serviceUrl}/${layerId}`);

      const infoUrl = `${serviceUrl}/${layerId}`;
      const response = await axios.get(infoUrl, {
        params: { f: 'json' },
        timeout: 5000 // Shorter timeout for testing
      });

      const data = response.data;
      
      if (data.error) {
        logger.error(`Service test failed: ${data.error.message}`);
        return false;
      }

      // Check if it's a valid feature layer
      if (data.type !== 'Feature Layer') {
        logger.error(`Service test failed: not a feature layer (type: ${data.type})`);
        return false;
      }

      logger.info(`✅ Service test passed: ${data.name || 'Unnamed layer'}`);
      return true;

    } catch (error) {
      logger.error('Service test error:', error);
      return false;
    }
  }

  /**
   * Get service information (fields, capabilities, etc.)
   */
  async getServiceInfo(serviceUrl: string, layerId: number = 0): Promise<any> {
    try {
      const infoUrl = `${serviceUrl}/${layerId}`;
      const response = await axios.get(infoUrl, {
        params: { f: 'json' },
        timeout: 5000
      });

      return response.data;

    } catch (error) {
      logger.error('Error getting service info:', error);
      throw error;
    }
  }

  /**
   * Convert coordinate system string to WKID
   */
  private getWKID(coordinateSystem: string): number {
    const wkidMap: { [key: string]: number } = {
      'EPSG:4326': 4326,  // WGS84
      'EPSG:3857': 3857,  // Web Mercator
      'EPSG:4269': 4269,  // NAD83
      'EPSG:2154': 2154,  // RGF93 / Lambert-93 (France)
      'EPSG:32633': 32633 // WGS 84 / UTM zone 33N
    };

    return wkidMap[coordinateSystem] || 4326; // Default to WGS84
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Build a query URL for manual testing/debugging
   */
  buildQueryUrl(
    serviceUrl: string,
    layerId: number,
    coordinates: Coordinates,
    outFields: string[] = ['*'],
    coordinateSystem: string = 'EPSG:4326'
  ): string {
    const baseUrl = `${serviceUrl}/${layerId}/query`;
    
    const pointGeometry = {
      x: coordinates.lng,
      y: coordinates.lat,
      spatialReference: { wkid: this.getWKID(coordinateSystem) }
    };

    const params = new URLSearchParams({
      geometry: JSON.stringify(pointGeometry),
      geometryType: 'esriGeometryPoint',
      inSR: this.getWKID(coordinateSystem).toString(),
      spatialRel: 'esriSpatialRelIntersects',
      outFields: outFields.join(','),
      returnGeometry: 'false',
      f: 'json'
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Extract field names from service metadata
   */
  async getAvailableFields(serviceUrl: string, layerId: number = 0): Promise<string[]> {
    try {
      const serviceInfo = await this.getServiceInfo(serviceUrl, layerId);
      
      if (serviceInfo.fields && Array.isArray(serviceInfo.fields)) {
        return serviceInfo.fields.map((field: any) => field.name);
      }
      
      return [];

    } catch (error) {
      logger.error('Error getting available fields:', error);
      return [];
    }
  }

  /**
   * Validate that required fields exist in the service
   */
  async validateFields(serviceUrl: string, layerId: number, requiredFields: string[]): Promise<boolean> {
    try {
      const availableFields = await this.getAvailableFields(serviceUrl, layerId);
      const missingFields = requiredFields.filter(field => !availableFields.includes(field));
      
      if (missingFields.length > 0) {
        logger.error(`Missing required fields: ${missingFields.join(', ')}`);
        logger.info(`Available fields: ${availableFields.join(', ')}`);
        return false;
      }
      
      return true;

    } catch (error) {
      logger.error('Error validating fields:', error);
      return false;
    }
  }
}
