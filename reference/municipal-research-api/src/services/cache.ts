import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export interface CachedResearch {
  id: string;
  topicKey: string;
  jurisdiction: string;
  queryHash: string;
  answer: string;
  sources: string[];
  confidence: number;
  processingTimeMs: number;
  costUsd: number;
  hitCount: number;
  createdAt: string;
  expiresAt: string;
}

export interface CacheStats {
  totalEntries: number;
  hitRate: number;
  totalHits: number;
  totalCost: number;
  avgConfidence: number;
  topTopics: Array<{
    topic: string;
    count: number;
    hitRate: number;
  }>;
}

class CacheService {
  private readonly DEFAULT_TTL = config.cache.defaultTtl; // 24 hours

  /**
   * Get cached research result
   */
  async getCachedResearch(topicKey: string, jurisdiction: string): Promise<CachedResearch | null> {
    try {
      const { data, error } = await supabase
        .from('municipal_research_cache')
        .select('*')
        .eq('topic_key', topicKey)
        .eq('jurisdiction', jurisdiction)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !data) {
        logger.debug(`Cache miss for ${topicKey} in ${jurisdiction}`);
        return null;
      }

      // Increment hit count
      await this.incrementHitCount(data.id);

      logger.info(`✅ Cache hit for ${topicKey} in ${jurisdiction} (hit count: ${data.hit_count + 1})`);

      return {
        id: data.id,
        topicKey: data.topic_key,
        jurisdiction: data.jurisdiction,
        queryHash: data.query_hash,
        answer: data.research_data.answer,
        sources: data.research_data.sources || [],
        confidence: parseFloat(data.confidence_score) || 0,
        processingTimeMs: data.processing_time_ms || 0,
        costUsd: parseFloat(data.cost_usd) || 0,
        hitCount: data.hit_count || 0,
        createdAt: data.created_at,
        expiresAt: data.expires_at
      };

    } catch (error) {
      logger.error('Cache retrieval error:', error);
      return null;
    }
  }

  /**
   * Store research result in cache
   */
  async storeResearch(
    topicKey: string,
    jurisdiction: string,
    researchData: {
      answer: string;
      sources: string[];
      confidence: number;
      processingTimeMs?: number;
      costUsd?: number;
    },
    originalQuery?: string
  ): Promise<void> {
    try {
      const queryHash = originalQuery ? this.generateQueryHash(originalQuery) : '';
      const expiresAt = new Date(Date.now() + this.DEFAULT_TTL).toISOString();

      const cacheEntry = {
        topic_key: topicKey,
        jurisdiction: jurisdiction,
        query_hash: queryHash,
        research_data: {
          answer: researchData.answer,
          sources: researchData.sources,
          metadata: {
            originalQuery,
            cachedAt: new Date().toISOString()
          }
        },
        confidence_score: researchData.confidence,
        sources: researchData.sources,
        processing_time_ms: researchData.processingTimeMs || 0,
        cost_usd: researchData.costUsd || 0,
        expires_at: expiresAt
      };

      const { error } = await supabase
        .from('municipal_research_cache')
        .upsert(cacheEntry, {
          onConflict: 'topic_key,jurisdiction'
        });

      if (error) {
        logger.error('Cache storage error:', error);
        return;
      }

      logger.info(`💾 Cached research for ${topicKey} in ${jurisdiction} (expires: ${expiresAt})`);

    } catch (error) {
      logger.error('Cache storage error:', error);
    }
  }

  /**
   * Check if exact query has been cached
   */
  async getCachedByQuery(query: string, jurisdiction: string): Promise<CachedResearch | null> {
    try {
      const queryHash = this.generateQueryHash(query);

      const { data, error } = await supabase
        .from('municipal_research_cache')
        .select('*')
        .eq('query_hash', queryHash)
        .eq('jurisdiction', jurisdiction)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !data) {
        return null;
      }

      await this.incrementHitCount(data.id);

      return {
        id: data.id,
        topicKey: data.topic_key,
        jurisdiction: data.jurisdiction,
        queryHash: data.query_hash,
        answer: data.research_data.answer,
        sources: data.research_data.sources || [],
        confidence: parseFloat(data.confidence_score) || 0,
        processingTimeMs: data.processing_time_ms || 0,
        costUsd: parseFloat(data.cost_usd) || 0,
        hitCount: data.hit_count || 0,
        createdAt: data.created_at,
        expiresAt: data.expires_at
      };

    } catch (error) {
      logger.error('Query cache retrieval error:', error);
      return null;
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<CacheStats> {
    try {
      // Get total entries
      const { count: totalEntries } = await supabase
        .from('municipal_research_cache')
        .select('*', { count: 'exact', head: true });

      // Get cache performance metrics
      const { data: metrics } = await supabase
        .from('municipal_research_cache')
        .select('hit_count, cost_usd, confidence_score, topic_key');

      if (!metrics) {
        return {
          totalEntries: 0,
          hitRate: 0,
          totalHits: 0,
          totalCost: 0,
          avgConfidence: 0,
          topTopics: []
        };
      }

      const totalHits = metrics.reduce((sum, entry) => sum + (entry.hit_count || 0), 0);
      const totalCost = metrics.reduce((sum, entry) => sum + parseFloat(entry.cost_usd || '0'), 0);
      const avgConfidence = metrics.reduce((sum, entry) => sum + parseFloat(entry.confidence_score || '0'), 0) / metrics.length;

      // Calculate hit rate (hits vs total requests)
      const hitRate = (totalEntries || 0) > 0 ? (totalHits / ((totalEntries || 0) + totalHits)) * 100 : 0;

      // Get top topics
      const topicCounts = new Map<string, { count: number; hits: number }>();
      metrics.forEach(entry => {
        const topic = entry.topic_key;
        const current = topicCounts.get(topic) || { count: 0, hits: 0 };
        topicCounts.set(topic, {
          count: current.count + 1,
          hits: current.hits + (entry.hit_count || 0)
        });
      });

      const topTopics = Array.from(topicCounts.entries())
        .map(([topic, data]) => ({
          topic,
          count: data.count,
          hitRate: data.count > 0 ? (data.hits / data.count) * 100 : 0
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalEntries: totalEntries || 0,
        hitRate: Math.round(hitRate * 100) / 100,
        totalHits,
        totalCost: Math.round(totalCost * 10000) / 10000,
        avgConfidence: Math.round(avgConfidence * 100) / 100,
        topTopics
      };

    } catch (error) {
      logger.error('Cache stats error:', error);
      return {
        totalEntries: 0,
        hitRate: 0,
        totalHits: 0,
        totalCost: 0,
        avgConfidence: 0,
        topTopics: []
      };
    }
  }

  /**
   * Clear expired cache entries
   */
  async clearExpiredEntries(): Promise<number> {
    try {
      const { data, error } = await supabase
        .from('municipal_research_cache')
        .delete()
        .lt('expires_at', new Date().toISOString())
        .select('id');

      if (error) {
        logger.error('Cache cleanup error:', error);
        return 0;
      }

      const deletedCount = data?.length || 0;
      if (deletedCount > 0) {
        logger.info(`🧹 Cleaned up ${deletedCount} expired cache entries`);
      }

      return deletedCount;

    } catch (error) {
      logger.error('Cache cleanup error:', error);
      return 0;
    }
  }

  /**
   * Get similar cached results for a topic
   */
  async getSimilarCachedResults(topicKey: string, limit: number = 5): Promise<CachedResearch[]> {
    try {
      const { data, error } = await supabase
        .from('municipal_research_cache')
        .select('*')
        .eq('topic_key', topicKey)
        .gt('expires_at', new Date().toISOString())
        .order('hit_count', { ascending: false })
        .limit(limit);

      if (error || !data) {
        return [];
      }

      return data.map(entry => ({
        id: entry.id,
        topicKey: entry.topic_key,
        jurisdiction: entry.jurisdiction,
        queryHash: entry.query_hash,
        answer: entry.research_data.answer,
        sources: entry.research_data.sources || [],
        confidence: parseFloat(entry.confidence_score) || 0,
        processingTimeMs: entry.processing_time_ms || 0,
        costUsd: parseFloat(entry.cost_usd) || 0,
        hitCount: entry.hit_count || 0,
        createdAt: entry.created_at,
        expiresAt: entry.expires_at
      }));

    } catch (error) {
      logger.error('Similar results error:', error);
      return [];
    }
  }

  /**
   * Invalidate cache for a specific topic and jurisdiction
   */
  async invalidateCache(topicKey: string, jurisdiction?: string): Promise<void> {
    try {
      let query = supabase
        .from('municipal_research_cache')
        .delete()
        .eq('topic_key', topicKey);

      if (jurisdiction) {
        query = query.eq('jurisdiction', jurisdiction);
      }

      const { error } = await query;

      if (error) {
        logger.error('Cache invalidation error:', error);
        return;
      }

      logger.info(`🗑️ Invalidated cache for ${topicKey}${jurisdiction ? ` in ${jurisdiction}` : ''}`);

    } catch (error) {
      logger.error('Cache invalidation error:', error);
    }
  }

  /**
   * Generate hash for query
   */
  private generateQueryHash(query: string): string {
    return crypto
      .createHash('sha256')
      .update(query.toLowerCase().trim())
      .digest('hex');
  }

  /**
   * Increment hit count for cache entry
   */
  private async incrementHitCount(cacheId: string): Promise<void> {
    try {
      // Get current hit count first
      const { data: currentEntry } = await supabase
        .from('municipal_research_cache')
        .select('hit_count')
        .eq('id', cacheId)
        .single();

      await supabase
        .from('municipal_research_cache')
        .update({
          hit_count: (currentEntry?.hit_count || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', cacheId);

    } catch (error) {
      logger.error('Hit count increment error:', error);
    }
  }
}

export const cacheService = new CacheService();
