/**
 * 🏖️ FLORIDA PROPERTY SERVICE
 * 
 * Service for querying Florida property data from Supabase
 * - Handles statewide Florida property lookups
 * - Provides land use and zoning information
 * - Supports natural language queries about property characteristics
 */

import { createClient } from '@supabase/supabase-js';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export interface FloridaPropertyData {
  property_id: string;
  city: string;
  county: string;
  primary_land_use: string;
  secondary_land_use?: string;
  land_use_code: string;
  acres: number;
  shape_area: number;
  data_source: string;
  last_updated: string;
}

export interface FloridaPropertyResult {
  found: boolean;
  properties: FloridaPropertyData[];
  searchMethod: 'coordinates' | 'city' | 'address';
  totalMatches: number;
}

export class FloridaPropertyService {
  
  /**
   * Check if coordinates are within Florida bounds
   */
  private isInFlorida(lat: number, lng: number): boolean {
    // Florida approximate bounds
    const floridaBounds = {
      north: 31.0,
      south: 24.5,
      east: -80.0,
      west: -87.6
    };
    
    return lat >= floridaBounds.south && 
           lat <= floridaBounds.north && 
           lng >= floridaBounds.west && 
           lng <= floridaBounds.east;
  }

  /**
   * Query Florida properties by coordinates (primary method)
   */
  async queryByCoordinates(lat: number, lng: number, radius: number = 0.01): Promise<FloridaPropertyResult> {
    try {
      logger.info(`🏖️ Querying Florida properties near ${lat}, ${lng} (radius: ${radius})`);

      // Check if coordinates are in Florida
      if (!this.isInFlorida(lat, lng)) {
        logger.info(`📍 Coordinates ${lat}, ${lng} are outside Florida bounds`);
        return {
          found: false,
          properties: [],
          searchMethod: 'coordinates',
          totalMatches: 0
        };
      }

      // For now, we'll use a city-based lookup since we don't have coordinate data
      // In the future, this could be enhanced with spatial queries
      logger.info(`🏖️ Falling back to city-based lookup for Florida coordinates`);
      
      return {
        found: false,
        properties: [],
        searchMethod: 'coordinates',
        totalMatches: 0
      };

    } catch (error) {
      logger.error('Error querying Florida properties by coordinates:', error);
      return {
        found: false,
        properties: [],
        searchMethod: 'coordinates',
        totalMatches: 0
      };
    }
  }

  /**
   * Query Florida properties by city name
   */
  async queryByCity(cityName: string, limit: number = 10): Promise<FloridaPropertyResult> {
    try {
      logger.info(`🏖️ Querying Florida properties in ${cityName}`);

      const { data, error, count } = await supabase
        .from('florida_properties')
        .select('*', { count: 'exact' })
        .ilike('city', `%${cityName}%`)
        .limit(limit);

      if (error) {
        logger.error('Supabase query error:', error);
        return {
          found: false,
          properties: [],
          searchMethod: 'city',
          totalMatches: 0
        };
      }

      const properties = data || [];
      logger.info(`🏖️ Found ${properties.length} properties in ${cityName}`);

      return {
        found: properties.length > 0,
        properties,
        searchMethod: 'city',
        totalMatches: count || 0
      };

    } catch (error) {
      logger.error('Error querying Florida properties by city:', error);
      return {
        found: false,
        properties: [],
        searchMethod: 'city',
        totalMatches: 0
      };
    }
  }

  /**
   * Query Florida properties by address components
   */
  async queryByAddress(address: string): Promise<FloridaPropertyResult> {
    try {
      logger.info(`🏖️ Querying Florida properties for address: ${address}`);

      // Extract city from address
      const cityMatch = this.extractCityFromAddress(address);
      if (!cityMatch) {
        logger.info(`🏖️ Could not extract city from address: ${address}`);
        return {
          found: false,
          properties: [],
          searchMethod: 'address',
          totalMatches: 0
        };
      }

      // Query by city
      return await this.queryByCity(cityMatch, 5);

    } catch (error) {
      logger.error('Error querying Florida properties by address:', error);
      return {
        found: false,
        properties: [],
        searchMethod: 'address',
        totalMatches: 0
      };
    }
  }

  /**
   * Get property statistics for a city
   */
  async getCityStats(cityName: string): Promise<{
    totalProperties: number;
    landUseBreakdown: { [key: string]: number };
    averageAcres: number;
    totalAcres: number;
  } | null> {
    try {
      logger.info(`🏖️ Getting stats for ${cityName}`);

      const { data, error } = await supabase
        .from('florida_properties')
        .select('primary_land_use, acres')
        .ilike('city', `%${cityName}%`);

      if (error || !data) {
        logger.error('Error getting city stats:', error);
        return null;
      }

      const landUseBreakdown: { [key: string]: number } = {};
      let totalAcres = 0;

      data.forEach(property => {
        const landUse = property.primary_land_use || 'UNKNOWN';
        landUseBreakdown[landUse] = (landUseBreakdown[landUse] || 0) + 1;
        totalAcres += parseFloat(property.acres) || 0;
      });

      return {
        totalProperties: data.length,
        landUseBreakdown,
        averageAcres: data.length > 0 ? totalAcres / data.length : 0,
        totalAcres
      };

    } catch (error) {
      logger.error('Error getting city stats:', error);
      return null;
    }
  }

  /**
   * Extract city name from address string
   */
  private extractCityFromAddress(address: string): string | null {
    // Common Florida cities to look for
    const floridaCities = [
      'Jacksonville', 'Miami', 'Tampa', 'Orlando', 'St. Petersburg', 'Hialeah',
      'Tallahassee', 'Fort Lauderdale', 'Port St. Lucie', 'Cape Coral',
      'Pembroke Pines', 'Hollywood', 'Gainesville', 'Miramar', 'Coral Springs',
      'Clearwater', 'Brandon', 'West Palm Beach', 'Lakeland', 'Pompano Beach',
      'Sunrise', 'Davie', 'Boca Raton', 'Deltona', 'Palm Bay', 'Largo',
      'Melbourne', 'Boynton Beach', 'Fort Myers', 'Kissimmee', 'Homestead',
      'Delray Beach', 'Tamarac', 'Daytona Beach', 'North Miami', 'Wellington',
      'North Port', 'Jupiter', 'Ocala', 'Port Orange', 'Margate',
      'Coconut Creek', 'Sanford', 'Sarasota', 'Pensacola', 'Bradenton',
      'Palm Coast', 'Pinellas Park', 'Coral Gables', 'Deerfield Beach',
      'Bonita Springs', 'Apopka', 'Titusville', 'North Lauderdale',
      'Oakland Park', 'Altamonte Springs', 'St. Cloud', 'Aventura',
      'Dunedin', 'Cutler Bay', 'Acworth', 'The Villages', 'Greenacres',
      'Ormond Beach', 'Ocoee', 'Hallandale Beach', 'Winter Garden',
      'Clermont', 'Winter Park', 'Lauderhill', 'Estero', 'Riviera Beach'
    ];

    const addressUpper = address.toUpperCase();
    
    for (const city of floridaCities) {
      if (addressUpper.includes(city.toUpperCase())) {
        return city;
      }
    }

    // Try to extract city from common address patterns
    const patterns = [
      /,\s*([A-Za-z\s]+),?\s*FL/i,  // ", City, FL"
      /,\s*([A-Za-z\s]+)\s+FL/i,    // ", City FL"
      /,\s*([A-Za-z\s]+)\s+Florida/i // ", City Florida"
    ];

    for (const pattern of patterns) {
      const match = address.match(pattern);
      if (match && match[1]) {
        const extractedCity = match[1].trim();
        // Verify it's a reasonable city name (2-50 chars, letters and spaces only)
        if (extractedCity.length >= 2 && extractedCity.length <= 50 && /^[A-Za-z\s.]+$/.test(extractedCity)) {
          return extractedCity;
        }
      }
    }

    return null;
  }

  /**
   * Check if this service can handle the given location
   */
  canHandleLocation(address: string, coordinates?: { lat: number; lng: number }): boolean {
    // Check coordinates if provided
    if (coordinates) {
      return this.isInFlorida(coordinates.lat, coordinates.lng);
    }

    // Check address for Florida indicators
    const addressUpper = address.toUpperCase();
    return addressUpper.includes('FL') || 
           addressUpper.includes('FLORIDA') ||
           this.extractCityFromAddress(address) !== null;
  }
}
