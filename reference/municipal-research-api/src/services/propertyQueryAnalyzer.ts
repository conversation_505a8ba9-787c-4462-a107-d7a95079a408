/**
 * 🔍 PROPERTY QUERY ANALYZER
 * 
 * Analyzes user queries to determine if they are property-related
 * and what type of property data is being requested.
 */

import { logger } from '../utils/logger.js';

export interface QueryAnalysis {
  canHandle: boolean;
  queryType: 'zoning' | 'flu' | 'both' | 'unknown';
  confidence: number;
  detectedKeywords: string[];
  reason?: string;
}

export class PropertyQueryAnalyzer {
  
  // Keywords that indicate zoning queries
  private zoningKeywords = [
    'zoning', 'zoned', 'zone', 'zones',
    'zoning district', 'zoning classification',
    'residential zone', 'commercial zone', 'industrial zone',
    'r1', 'r2', 'r3', 'c1', 'c2', 'm1', 'm2',
    'single family', 'multi family', 'multifamily',
    'setback', 'setbacks', 'lot coverage',
    'building height', 'height restriction',
    'density', 'units per acre',
    'permitted use', 'allowed use', 'prohibited use',
    'variance', 'special permit', 'conditional use'
  ];

  // Keywords that indicate Future Land Use (FLU) queries
  private fluKeywords = [
    'future land use', 'flu', 'land use',
    'comprehensive plan', 'general plan', 'master plan',
    'land use designation', 'land use category',
    'planned development', 'development plan',
    'growth management', 'urban planning'
  ];

  // Keywords that indicate both zoning and FLU
  private combinedKeywords = [
    'zoning and land use', 'zoning and flu',
    'development regulations', 'planning regulations',
    'land development', 'property regulations'
  ];

  // General property-related keywords that might indicate smart agent capability
  private propertyKeywords = [
    'property', 'lot', 'parcel', 'site',
    'development', 'build', 'construction',
    'permit', 'permits', 'application',
    'planning', 'planning department'
  ];

  /**
   * Analyze a query to determine if it's property-related and what type
   */
  async analyzeQuery(query: string): Promise<QueryAnalysis> {
    const normalizedQuery = query.toLowerCase().trim();
    const detectedKeywords: string[] = [];
    let confidence = 0;

    logger.info(`🔍 Analyzing query: "${query}"`);

    // Check for combined keywords first (highest priority)
    const combinedMatches = this.findKeywordMatches(normalizedQuery, this.combinedKeywords);
    if (combinedMatches.length > 0) {
      detectedKeywords.push(...combinedMatches);
      confidence = 0.95;
      
      logger.info(`🔍 Detected combined query with keywords: ${combinedMatches.join(', ')}`);
      return {
        canHandle: true,
        queryType: 'both',
        confidence,
        detectedKeywords
      };
    }

    // Check for zoning keywords
    const zoningMatches = this.findKeywordMatches(normalizedQuery, this.zoningKeywords);
    const fluMatches = this.findKeywordMatches(normalizedQuery, this.fluKeywords);

    // If both zoning and FLU keywords are found
    if (zoningMatches.length > 0 && fluMatches.length > 0) {
      detectedKeywords.push(...zoningMatches, ...fluMatches);
      confidence = 0.9;
      
      logger.info(`🔍 Detected both zoning and FLU keywords`);
      return {
        canHandle: true,
        queryType: 'both',
        confidence,
        detectedKeywords
      };
    }

    // Check for zoning-only queries
    if (zoningMatches.length > 0) {
      detectedKeywords.push(...zoningMatches);
      confidence = this.calculateConfidence(zoningMatches, normalizedQuery);
      
      if (confidence >= 0.7) {
        logger.info(`🔍 Detected zoning query with keywords: ${zoningMatches.join(', ')}`);
        return {
          canHandle: true,
          queryType: 'zoning',
          confidence,
          detectedKeywords
        };
      }
    }

    // Check for FLU-only queries
    if (fluMatches.length > 0) {
      detectedKeywords.push(...fluMatches);
      confidence = this.calculateConfidence(fluMatches, normalizedQuery);
      
      if (confidence >= 0.7) {
        logger.info(`🔍 Detected FLU query with keywords: ${fluMatches.join(', ')}`);
        return {
          canHandle: true,
          queryType: 'flu',
          confidence,
          detectedKeywords
        };
      }
    }

    // Check for general property keywords (lower confidence)
    const propertyMatches = this.findKeywordMatches(normalizedQuery, this.propertyKeywords);
    if (propertyMatches.length > 0) {
      detectedKeywords.push(...propertyMatches);
      
      // Check if query seems to be asking about property regulations
      if (this.isPropertyRegulationQuery(normalizedQuery)) {
        confidence = 0.6;
        logger.info(`🔍 Detected potential property regulation query: ${propertyMatches.join(', ')}`);
        return {
          canHandle: true,
          queryType: 'zoning', // Default to zoning for general property queries
          confidence,
          detectedKeywords
        };
      }
    }

    // Query doesn't appear to be property-related
    logger.info(`🔍 Query not property-related or confidence too low`);
    return {
      canHandle: false,
      queryType: 'unknown',
      confidence: 0,
      detectedKeywords,
      reason: detectedKeywords.length > 0 
        ? 'Property-related keywords found but confidence too low'
        : 'No property-related keywords detected'
    };
  }

  /**
   * Find keyword matches in the query
   */
  private findKeywordMatches(query: string, keywords: string[]): string[] {
    const matches: string[] = [];
    
    for (const keyword of keywords) {
      if (query.includes(keyword)) {
        matches.push(keyword);
      }
    }
    
    return matches;
  }

  /**
   * Calculate confidence based on keyword matches and query context
   */
  private calculateConfidence(matches: string[], query: string): number {
    let confidence = 0.5; // Base confidence
    
    // More matches = higher confidence
    confidence += Math.min(matches.length * 0.1, 0.3);
    
    // Longer, more specific keywords = higher confidence
    const avgKeywordLength = matches.reduce((sum, keyword) => sum + keyword.length, 0) / matches.length;
    if (avgKeywordLength > 8) {
      confidence += 0.1;
    }
    
    // Question words indicate user is asking for information
    if (this.hasQuestionWords(query)) {
      confidence += 0.1;
    }
    
    // Specific property-related phrases
    if (query.includes('what is') || query.includes('what are')) {
      confidence += 0.1;
    }
    
    // Address-like patterns suggest property-specific query
    if (this.hasAddressPattern(query)) {
      confidence += 0.1;
    }
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Check if query contains question words
   */
  private hasQuestionWords(query: string): boolean {
    const questionWords = ['what', 'how', 'where', 'when', 'why', 'which', 'can', 'is', 'are', 'does', 'do'];
    return questionWords.some(word => query.includes(word));
  }

  /**
   * Check if query contains address-like patterns
   */
  private hasAddressPattern(query: string): boolean {
    // Look for patterns like "123 Main St" or "at 456 Oak Ave"
    const addressPatterns = [
      /\d+\s+\w+\s+(st|street|ave|avenue|rd|road|dr|drive|blvd|boulevard|ln|lane|ct|court|pl|place)/i,
      /\bat\s+\d+/i,
      /\baddress\b/i,
      /\bproperty\s+at\b/i
    ];
    
    return addressPatterns.some(pattern => pattern.test(query));
  }

  /**
   * Check if query seems to be asking about property regulations
   */
  private isPropertyRegulationQuery(query: string): boolean {
    const regulationPhrases = [
      'can i build', 'can i construct', 'am i allowed',
      'is it legal', 'is it permitted', 'do i need',
      'what are the rules', 'what are the requirements',
      'restrictions', 'regulations', 'ordinance',
      'building code', 'planning code'
    ];
    
    return regulationPhrases.some(phrase => query.includes(phrase));
  }

  /**
   * Get human-readable explanation of query analysis
   */
  getAnalysisExplanation(analysis: QueryAnalysis): string {
    if (!analysis.canHandle) {
      return `Cannot handle query: ${analysis.reason}`;
    }

    const typeDescription = {
      'zoning': 'zoning information',
      'flu': 'future land use information', 
      'both': 'zoning and future land use information',
      'unknown': 'unknown property information'
    };

    return `Can provide ${typeDescription[analysis.queryType]} (confidence: ${Math.round(analysis.confidence * 100)}%, keywords: ${analysis.detectedKeywords.join(', ')})`;
  }
}
