/**
 * 📝 PROPERTY RESPONSE FORMATTER
 * 
 * Formats property data responses into conversational, user-friendly answers
 * - Converts raw ArcGIS data into natural language
 * - Provides context and explanations
 * - Maintains consistent response format
 */

import { logger } from '../utils/logger.js';
import { PropertyData } from './smartPropertyAgent.js';

export interface FormattedPropertyResponse {
  answer: string;
  sources: string[];
  confidence: number;
}

export class PropertyResponseFormatter {

  /**
   * Format property data into a conversational response
   */
  async formatPropertyResponse(
    propertyData: PropertyData,
    jurisdiction: string,
    originalQuery: string,
    queryType: string
  ): Promise<FormattedPropertyResponse> {
    
    try {
      logger.info(`📝 Formatting property response for ${jurisdiction}: ${queryType}`);

      const sources: string[] = [];
      let answer = '';
      let confidence = 0.8; // Base confidence for successful ArcGIS queries

      // Format based on what data we have
      if (queryType === 'both' && propertyData.zoning && propertyData.flu) {
        answer = this.formatBothResponse(propertyData, jurisdiction, originalQuery);
        sources.push(propertyData.zoning.source, propertyData.flu.source);
        confidence = 0.95; // High confidence when we have both data types
        
      } else if (queryType === 'zoning' && propertyData.zoning) {
        answer = this.formatZoningResponse(propertyData.zoning, jurisdiction, originalQuery);
        sources.push(propertyData.zoning.source);
        confidence = 0.9;
        
      } else if (queryType === 'flu' && propertyData.flu) {
        answer = this.formatFLUResponse(propertyData.flu, jurisdiction, originalQuery);
        sources.push(propertyData.flu.source);
        confidence = 0.9;
        
      } else if (propertyData.zoning) {
        // Fallback to zoning if we have it but not what was requested
        answer = this.formatZoningResponse(propertyData.zoning, jurisdiction, originalQuery);
        sources.push(propertyData.zoning.source);
        confidence = 0.75; // Lower confidence since it's not exactly what was requested
        
      } else if (propertyData.flu) {
        // Fallback to FLU if we have it but not what was requested
        answer = this.formatFLUResponse(propertyData.flu, jurisdiction, originalQuery);
        sources.push(propertyData.flu.source);
        confidence = 0.75;
        
      } else {
        throw new Error('No property data available to format');
      }

      // Add helpful footer information
      answer += this.addFooterInfo(jurisdiction);

      // Remove duplicate sources
      const uniqueSources = [...new Set(sources)];

      logger.info(`📝 Formatted response with confidence ${confidence}`);

      return {
        answer,
        sources: uniqueSources,
        confidence
      };

    } catch (error) {
      logger.error('Error formatting property response:', error);
      throw error;
    }
  }

  /**
   * Format response when we have both zoning and FLU data
   */
  private formatBothResponse(propertyData: PropertyData, jurisdiction: string, query: string): string {
    const zoning = propertyData.zoning!;
    const flu = propertyData.flu!;

    return `Based on the official GIS data for ${jurisdiction}, here's what I found:

**Zoning:** The property is zoned as **${zoning.value}** (${zoning.description}). This zoning designation determines what types of buildings and uses are allowed on the property.

**Future Land Use:** The comprehensive plan designates this area as **${flu.value}** (${flu.description}). This represents the long-term planning vision for how this area should develop.

Both the current zoning and future land use planning are important factors to consider for any development or use of this property. The zoning provides the immediate legal framework, while the future land use designation shows the community's long-term vision for the area.`;
  }

  /**
   * Format zoning-only response
   */
  private formatZoningResponse(zoning: PropertyData['zoning'], jurisdiction: string, query: string): string {
    // Check if this is state database data
    const isStateData = zoning!.source && (
      zoning!.source.includes('Future Land Use Database') ||
      zoning!.source.includes('(FL)') ||
      zoning!.source.includes('(CA)') ||
      zoning!.source.includes('(TX)')
    );

    if (isStateData) {
      return this.formatStateLandUseResponse(zoning!, jurisdiction, query, 'zoning');
    }

    return `According to the official zoning data for ${jurisdiction}, this property is zoned as **${zoning!.value}**.

${zoning!.description}. This zoning designation determines what types of buildings, structures, and uses are permitted on the property.

Zoning regulations typically cover aspects like:
- Permitted and prohibited uses
- Building height and size restrictions
- Setback requirements from property lines
- Parking and landscaping requirements
- Density limitations

For specific details about what's allowed under this zoning classification, you may want to review the complete zoning ordinance or contact the local planning department.`;
  }

  /**
   * Format FLU-only response
   */
  private formatFLUResponse(flu: PropertyData['flu'], jurisdiction: string, query: string): string {
    return `According to the comprehensive plan for ${jurisdiction}, this property is designated for **${flu!.value}** future land use.

${flu!.description}. This designation represents the community's long-term vision for how this area should develop over time.

Future Land Use designations guide:
- Long-term development patterns
- Infrastructure planning and investment
- Zoning decisions and amendments
- Community growth management
- Transportation and utility planning

While the Future Land Use designation provides important guidance, the current zoning regulations determine what's immediately permitted on the property. For development questions, you'll want to check both the current zoning and this future land use designation.`;
  }

  /**
   * Add helpful footer information
   */
  private addFooterInfo(jurisdiction: string): string {
    return `

---

**Need More Information?**
For detailed regulations, permit requirements, or specific development questions, I recommend contacting the ${jurisdiction} Planning Department directly. They can provide the most current information about local requirements and help with any applications or permits you might need.

*This information is based on official GIS data and is current as of the time of this query.*`;
  }

  /**
   * Format error response when property data is not available
   */
  formatUnavailableResponse(jurisdiction: string, queryType: string): FormattedPropertyResponse {
    const typeDescription = {
      'zoning': 'zoning information',
      'flu': 'future land use information',
      'both': 'zoning and future land use information'
    };

    const answer = `I don't currently have automated access to ${typeDescription[queryType as keyof typeof typeDescription]} for ${jurisdiction} through our GIS system.

However, this information is typically available through the local planning department. Here are some ways you can get this information:

**Contact the Planning Department:**
- Visit the ${jurisdiction} city/county website
- Call the planning or zoning department directly
- Visit their office in person for maps and documents

**Online Resources:**
- Many jurisdictions have online zoning maps
- Comprehensive plans are often available as PDFs
- Some areas have interactive GIS portals for public use

**Professional Help:**
- Local surveyors often have access to detailed zoning information
- Real estate professionals familiar with the area
- Land use attorneys for complex questions

I apologize that I couldn't provide the specific information you're looking for, but the local planning department will be your best resource for accurate, up-to-date ${typeDescription[queryType as keyof typeof typeDescription]}.`;

    return {
      answer,
      sources: [],
      confidence: 0.0
    };
  }

  /**
   * Format state land use response (for state database data)
   */
  private formatStateLandUseResponse(
    data: { value: string; description: string; source: string },
    jurisdiction: string,
    query: string,
    type: 'zoning' | 'flu'
  ): string {
    const isFloridaData = data.source.includes('(FL)');

    // Determine if user is asking about specific development types
    const queryLower = query.toLowerCase();
    const isDuplexQuery = queryLower.includes('duplex');
    const isCommercialQuery = queryLower.includes('commercial') || queryLower.includes('business');
    const isAgricultureQuery = queryLower.includes('agriculture') || queryLower.includes('farm');

    // Start with a natural, conversational response
    let response = `Hey there! I've got some good information for you about this property in ${jurisdiction}.`;

    // Add specific guidance based on land use type and what they're asking
    const landUse = data.description.toUpperCase();

    if (landUse.includes('WATER')) {
      if (isDuplexQuery) {
        response += ` Unfortunately, you can't build a duplex here because this property is designated as a **water area** (${data.value}). This means it's either a water body, wetland, or flood-prone area where construction isn't allowed for safety and environmental protection.`;
      } else {
        response += ` This property is designated as **${data.description}** (${data.value}), which means it's a water body or water area where development is typically prohibited.`;
      }
    } else if (landUse.includes('AGRICULTURE')) {
      if (isDuplexQuery) {
        response += ` This property is currently designated for **agricultural use** (${data.value}), so building a duplex here would require rezoning to residential. That's definitely possible, but you'll need to go through the local planning process and there may be restrictions depending on the area's growth management policies.`;
      } else if (isAgricultureQuery) {
        response += ` Great news! This property is designated for **agricultural use** (${data.value}), so it's perfect for farming and related agricultural activities.`;
      } else {
        response += ` This property is designated for **agricultural use** (${data.value}), which means it's intended for farming and related agricultural activities.`;
      }
    } else if (landUse.includes('RESIDENTIAL')) {
      if (isDuplexQuery) {
        response += ` This is promising! The property has a **residential designation** (${data.value}), which typically allows for housing development. For a duplex specifically, you'll want to check the local zoning ordinances to see if multi-family structures are permitted and what the requirements are for things like lot size and setbacks.`;
      } else {
        response += ` This property has a **residential designation** (${data.value}), which means it's intended for housing development.`;
      }
    } else if (landUse.includes('COMMERCIAL')) {
      if (isDuplexQuery) {
        response += ` This property is designated for **commercial use** (${data.value}), which typically doesn't allow residential duplexes. You'd likely need to apply for rezoning to residential or mixed-use if you want to build a duplex here.`;
      } else if (isCommercialQuery) {
        response += ` Perfect! This property is designated for **commercial use** (${data.value}), so it should be well-suited for your business plans.`;
      } else {
        response += ` This property is designated for **commercial use** (${data.value}), which means it's intended for business and retail activities.`;
      }
    } else if (landUse.includes('MIXED USE')) {
      if (isDuplexQuery) {
        response += ` This looks good for your duplex plans! The property has a **mixed-use designation** (${data.value}), which typically allows for a combination of residential, commercial, and office uses. Duplexes are often permitted in mixed-use areas, but you'll want to check the specific local zoning requirements.`;
      } else {
        response += ` This property has a **mixed-use designation** (${data.value}), which allows for a flexible combination of residential, commercial, and sometimes office uses.`;
      }
    } else if (landUse.includes('INDUSTRIAL')) {
      if (isDuplexQuery) {
        response += ` This property is designated for **industrial use** (${data.value}), which typically doesn't allow residential duplexes. You'd need to apply for rezoning to residential or mixed-use.`;
      } else {
        response += ` This property is designated for **industrial use** (${data.value}), which means it's intended for manufacturing, warehousing, and related industrial activities.`;
      }
    } else if (landUse.includes('CONSERVATION')) {
      if (isDuplexQuery || isCommercialQuery) {
        response += ` This property is in a **conservation area** (${data.value}), which means development is typically prohibited or severely restricted to protect environmental resources. Building a duplex here would likely not be allowed.`;
      } else {
        response += ` This property is designated for **conservation** (${data.value}), which means development is restricted to protect natural resources and environmental features.`;
      }
    } else {
      // Generic response for other land use types
      response += ` This property is designated as **${data.description}** (${data.value}).`;
    }

    // Add helpful next steps
    response += `\n\nKeep in mind that this is the comprehensive plan designation from ${isFloridaData ? 'Florida' : 'the state'}'s database. The local zoning ordinances will have the specific rules about what you can actually build, so I'd recommend checking with the ${jurisdiction} planning department for the most current zoning information and any permit requirements.`;

    if (isDuplexQuery || isCommercialQuery) {
      response += ` They can walk you through the process and let you know exactly what's needed for your project.`;
    }

    return response;
  }

  /**
   * Calculate confidence based on data quality
   */
  private calculateConfidence(propertyData: PropertyData, queryType: string): number {
    let confidence = 0.5; // Base confidence

    // Check if we have the requested data type
    if (queryType === 'zoning' && propertyData.zoning) confidence += 0.3;
    if (queryType === 'flu' && propertyData.flu) confidence += 0.3;
    if (queryType === 'both' && propertyData.zoning && propertyData.flu) confidence += 0.4;

    // Bonus for having both types regardless of what was requested
    if (propertyData.zoning && propertyData.flu) confidence += 0.1;

    // Check data quality
    if (propertyData.zoning?.value && propertyData.zoning.value.length > 2) confidence += 0.05;
    if (propertyData.flu?.value && propertyData.flu.value.length > 2) confidence += 0.05;

    // Check if we have descriptions
    if (propertyData.zoning?.description) confidence += 0.05;
    if (propertyData.flu?.description) confidence += 0.05;

    return Math.min(confidence, 1.0);
  }

  /**
   * Clean and normalize property values
   */
  private cleanPropertyValue(value: string): string {
    if (!value) return 'Unknown';
    
    // Remove common prefixes/suffixes that might be in the data
    let cleaned = value.toString().trim();
    
    // Handle null/undefined values that might come as strings
    if (cleaned.toLowerCase() === 'null' || cleaned.toLowerCase() === 'undefined') {
      return 'Unknown';
    }
    
    // Capitalize first letter if it's all lowercase
    if (cleaned === cleaned.toLowerCase()) {
      cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
    }
    
    return cleaned;
  }

  /**
   * Generate a fallback description for unknown codes
   */
  private generateFallbackDescription(value: string, type: 'zoning' | 'flu'): string {
    const cleanValue = this.cleanPropertyValue(value);
    
    if (type === 'zoning') {
      return `This property is zoned as ${cleanValue}. Contact the local planning department for specific details about permitted uses and restrictions under this zoning classification.`;
    } else {
      return `This area is designated for ${cleanValue} in the comprehensive plan. Contact the planning department for more details about this future land use designation.`;
    }
  }
}
