/**
 * 🗺️ STATE PROPERTY SERVICE
 * 
 * Modular state-based property data service
 * - Automatically detects state from address/coordinates
 * - Routes queries to appropriate state-specific handlers
 * - Easily extensible for new states
 * - Replaces unreliable ArcGIS approach
 */

import { createClient } from '@supabase/supabase-js';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export interface StatePropertyData {
  property_id: string;
  city: string;
  state: string;
  county: string;
  primary_land_use: string;
  secondary_land_use?: string;
  land_use_code: string;
  acres: number;
  shape_area?: number;
  data_source: string;
  last_updated: string;
  // Additional state-specific fields can be added here
  [key: string]: any;
}

export interface StatePropertyResult {
  found: boolean;
  properties: StatePropertyData[];
  state: string;
  searchMethod: 'coordinates' | 'city' | 'address';
  totalMatches: number;
  dataSource: string;
}

export interface StateHandler {
  canHandle(state: string): boolean;
  queryByCoordinates(lat: number, lng: number, radius?: number): Promise<StatePropertyResult>;
  queryByCity(cityName: string, state: string, limit?: number): Promise<StatePropertyResult>;
  queryByAddress(address: string): Promise<StatePropertyResult>;
  getStateBounds(): { north: number; south: number; east: number; west: number };
}

/**
 * Florida Property Handler
 */
class FloridaPropertyHandler implements StateHandler {
  private tableName = 'florida_properties';

  canHandle(state: string): boolean {
    return state.toUpperCase() === 'FL' || state.toUpperCase() === 'FLORIDA';
  }

  getStateBounds() {
    return {
      north: 31.0,
      south: 24.5,
      east: -80.0,
      west: -87.6
    };
  }

  async queryByCoordinates(lat: number, lng: number, radius: number = 0.01): Promise<StatePropertyResult> {
    try {
      logger.info(`🏖️ Querying Florida properties near ${lat}, ${lng} (radius: ${radius})`);

      // First, get the city/county from reverse geocoding for context
      let cityName = null;
      let countyName = null;

      try {
        const reverseGeocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${process.env.GOOGLE_MAPS_API_KEY}`;
        const response = await fetch(reverseGeocodeUrl);
        const data = await response.json();

        if (data.status === 'OK' && data.results.length > 0) {
          const result = data.results[0];

          for (const component of result.address_components) {
            if (component.types.includes('locality') || component.types.includes('administrative_area_level_3')) {
              cityName = component.long_name;
            }
            if (component.types.includes('administrative_area_level_2')) {
              countyName = component.long_name.replace(' County', '');
            }
          }
        }
      } catch (geocodeError) {
        logger.warn('Reverse geocoding failed, proceeding with coordinate-only search:', geocodeError);
      }

      logger.info(`🏖️ Reverse geocoded to: ${cityName || 'unknown city'}, ${countyName || 'unknown county'}`);

      // Since our current database doesn't have lat/lng coordinates for individual properties,
      // we'll use a smarter approach: query by city/county and return the most representative
      // land use for that area (largest by acreage, which is more likely to be the dominant use)

      const { data, error, count } = await supabase
        .from(this.tableName)
        .select('*', { count: 'exact' })
        .ilike('city', `%${cityName || ''}%`)
        .ilike('county', `%${countyName || ''}%`)
        .order('acres', { ascending: false }) // Order by largest areas first
        .limit(10);

      if (error) {
        logger.error('Supabase coordinate query error:', error);
        return this.getEmptyResult('coordinates');
      }

      if (!data || data.length === 0) {
        logger.info(`🏖️ No properties found near ${lat}, ${lng} in ${cityName}, ${countyName}`);
        return {
          found: false,
          properties: [],
          state: 'FL',
          searchMethod: 'coordinates',
          totalMatches: 0,
          dataSource: 'Florida Future Land Use Database'
        };
      }

      logger.info(`🏖️ Found ${data.length} properties near ${lat}, ${lng} in ${cityName}, ${countyName}`);

      return {
        found: true,
        properties: data,
        state: 'FL',
        searchMethod: 'coordinates',
        totalMatches: count || 0,
        dataSource: 'Florida Future Land Use Database (reverse geocoded)'
      };

    } catch (error) {
      logger.error('Error querying Florida properties by coordinates:', error);
      return this.getEmptyResult('coordinates');
    }
  }

  async queryByCity(cityName: string, state: string, limit: number = 10): Promise<StatePropertyResult> {
    try {
      logger.info(`🏖️ Querying Florida properties in ${cityName}`);

      const { data, error, count } = await supabase
        .from(this.tableName)
        .select('*', { count: 'exact' })
        .ilike('city', `%${cityName}%`)
        .limit(limit);

      if (error) {
        logger.error('Supabase query error:', error);
        return this.getEmptyResult('city');
      }

      const properties = data || [];
      logger.info(`🏖️ Found ${properties.length} properties in ${cityName}, FL`);

      return {
        found: properties.length > 0,
        properties,
        state: 'FL',
        searchMethod: 'city',
        totalMatches: count || 0,
        dataSource: 'Florida Future Land Use Database'
      };

    } catch (error) {
      logger.error('Error querying Florida properties by city:', error);
      return this.getEmptyResult('city');
    }
  }

  async queryByAddress(address: string): Promise<StatePropertyResult> {
    try {
      logger.info(`🏖️ Querying Florida properties for address: ${address}`);

      const cityMatch = this.extractCityFromAddress(address);
      if (!cityMatch) {
        logger.info(`🏖️ Could not extract city from address: ${address}`);
        return this.getEmptyResult('address');
      }

      return await this.queryByCity(cityMatch, 'FL', 5);

    } catch (error) {
      logger.error('Error querying Florida properties by address:', error);
      return this.getEmptyResult('address');
    }
  }

  private extractCityFromAddress(address: string): string | null {
    // Common Florida cities
    const floridaCities = [
      'Jacksonville', 'Miami', 'Tampa', 'Orlando', 'St. Petersburg', 'Hialeah',
      'Tallahassee', 'Fort Lauderdale', 'Port St. Lucie', 'Cape Coral',
      'Pembroke Pines', 'Hollywood', 'Gainesville', 'Miramar', 'Coral Springs',
      'Clearwater', 'Brandon', 'West Palm Beach', 'Lakeland', 'Pompano Beach',
      'Sunrise', 'Davie', 'Boca Raton', 'Deltona', 'Palm Bay', 'Largo',
      'Melbourne', 'Boynton Beach', 'Fort Myers', 'Kissimmee', 'Homestead'
    ];

    const addressUpper = address.toUpperCase();
    
    for (const city of floridaCities) {
      if (addressUpper.includes(city.toUpperCase())) {
        return city;
      }
    }

    // Try to extract city from address patterns
    const patterns = [
      /,\s*([A-Za-z\s]+),?\s*FL/i,
      /,\s*([A-Za-z\s]+)\s+FL/i,
      /,\s*([A-Za-z\s]+)\s+Florida/i
    ];

    for (const pattern of patterns) {
      const match = address.match(pattern);
      if (match && match[1]) {
        const extractedCity = match[1].trim();
        if (extractedCity.length >= 2 && extractedCity.length <= 50 && /^[A-Za-z\s.]+$/.test(extractedCity)) {
          return extractedCity;
        }
      }
    }

    return null;
  }

  private getEmptyResult(method: 'coordinates' | 'city' | 'address'): StatePropertyResult {
    return {
      found: false,
      properties: [],
      state: 'FL',
      searchMethod: method,
      totalMatches: 0,
      dataSource: 'Florida Future Land Use Database'
    };
  }
}

/**
 * Main State Property Service
 */
export class StatePropertyService {
  private handlers: StateHandler[] = [];

  constructor() {
    // Register state handlers
    this.handlers.push(new FloridaPropertyHandler());
    
    logger.info(`🗺️ Initialized StatePropertyService with ${this.handlers.length} state handlers`);
  }

  /**
   * Detect state from address
   */
  detectStateFromAddress(address: string): string | null {
    const addressUpper = address.toUpperCase();
    
    // Common state patterns
    const statePatterns = [
      { pattern: /\b(FL|FLORIDA)\b/i, state: 'FL' },
      { pattern: /\b(CA|CALIFORNIA)\b/i, state: 'CA' },
      { pattern: /\b(TX|TEXAS)\b/i, state: 'TX' },
      { pattern: /\b(NY|NEW YORK)\b/i, state: 'NY' },
      // Add more states as needed
    ];

    for (const { pattern, state } of statePatterns) {
      if (pattern.test(addressUpper)) {
        return state;
      }
    }

    return null;
  }

  /**
   * Detect state from coordinates
   */
  detectStateFromCoordinates(lat: number, lng: number): string | null {
    for (const handler of this.handlers) {
      const bounds = handler.getStateBounds();
      if (lat >= bounds.south && lat <= bounds.north && 
          lng >= bounds.west && lng <= bounds.east) {
        
        // Get the state code from the handler
        if (handler instanceof FloridaPropertyHandler) {
          return 'FL';
        }
      }
    }
    return null;
  }

  /**
   * Get handler for a specific state
   */
  private getHandlerForState(state: string): StateHandler | null {
    return this.handlers.find(handler => handler.canHandle(state)) || null;
  }

  /**
   * Query property data by coordinates
   */
  async queryByCoordinates(lat: number, lng: number, radius?: number): Promise<StatePropertyResult | null> {
    const state = this.detectStateFromCoordinates(lat, lng);
    if (!state) {
      logger.info(`🗺️ No state handler found for coordinates ${lat}, ${lng}`);
      return null;
    }

    const handler = this.getHandlerForState(state);
    if (!handler) {
      logger.info(`🗺️ No handler available for state: ${state}`);
      return null;
    }

    logger.info(`🗺️ Using ${state} handler for coordinate query`);
    return await handler.queryByCoordinates(lat, lng, radius);
  }

  /**
   * Query property data by address
   */
  async queryByAddress(address: string): Promise<StatePropertyResult | null> {
    const state = this.detectStateFromAddress(address);
    if (!state) {
      logger.info(`🗺️ Could not detect state from address: ${address}`);
      return null;
    }

    const handler = this.getHandlerForState(state);
    if (!handler) {
      logger.info(`🗺️ No handler available for state: ${state}`);
      return null;
    }

    logger.info(`🗺️ Using ${state} handler for address query`);
    return await handler.queryByAddress(address);
  }

  /**
   * Query property data by city and state
   */
  async queryByCity(cityName: string, state: string, limit?: number): Promise<StatePropertyResult | null> {
    const handler = this.getHandlerForState(state);
    if (!handler) {
      logger.info(`🗺️ No handler available for state: ${state}`);
      return null;
    }

    logger.info(`🗺️ Using ${state} handler for city query`);
    return await handler.queryByCity(cityName, state, limit);
  }

  /**
   * Check if we can handle a location
   */
  canHandleLocation(address: string, coordinates?: { lat: number; lng: number }): boolean {
    if (coordinates) {
      return this.detectStateFromCoordinates(coordinates.lat, coordinates.lng) !== null;
    }
    return this.detectStateFromAddress(address) !== null;
  }

  /**
   * Check if we can handle a specific query type for a location
   */
  canHandleQueryType(address: string, coordinates: { lat: number; lng: number }, queryType: string): boolean {
    const state = this.detectStateFromCoordinates(coordinates.lat, coordinates.lng) ||
                  this.detectStateFromAddress(address);

    if (!state) {
      return false;
    }

    // Florida handler only supports FLU (Future Land Use) queries, not detailed zoning
    if (state === 'FL') {
      return queryType === 'flu' || queryType === 'both';
    }

    // Add logic for other states as we expand
    return false;
  }

  /**
   * Get list of supported states
   */
  getSupportedStates(): string[] {
    return ['FL']; // Will expand as we add more states
  }
}
