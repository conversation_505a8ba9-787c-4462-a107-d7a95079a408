import { createClient } from '@supabase/supabase-js';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export interface UsageLogEntry {
  apiKeyId: string;
  userId?: string;
  endpoint: string;
  method: string;
  address?: string;
  query?: string;
  jurisdiction?: string;
  topicKey?: string;
  cacheHit?: boolean;
  responseTimeMs?: number;
  confidenceScore?: number;
  sourcesCount?: number;
  costUsd?: number;
  statusCode: number;
  errorMessage?: string;
  requestIp?: string;
  userAgent?: string;
}

export interface UsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  cacheHitRate: number;
  avgResponseTime: number;
  avgConfidence: number;
  totalCost: number;
  requestsByDay: Array<{
    date: string;
    count: number;
    cost: number;
  }>;
  topEndpoints: Array<{
    endpoint: string;
    count: number;
    avgResponseTime: number;
  }>;
  topTopics: Array<{
    topic: string;
    count: number;
    cacheHitRate: number;
  }>;
}

class UsageTracker {
  /**
   * Log API usage for billing and analytics
   */
  async logApiUsage(entry: UsageLogEntry): Promise<void> {
    try {
      const usageEntry = {
        api_key_id: entry.apiKeyId,
        user_id: entry.userId || null,
        endpoint: entry.endpoint,
        request_method: entry.method,
        address: entry.address || null,
        query: entry.query || null,
        jurisdiction: entry.jurisdiction || null,
        topic_key: entry.topicKey || null,
        cache_hit: entry.cacheHit || false,
        response_time_ms: entry.responseTimeMs || null,
        confidence_score: entry.confidenceScore || null,
        sources_count: entry.sourcesCount || 0,
        cost_usd: entry.costUsd || 0,
        status_code: entry.statusCode,
        error_message: entry.errorMessage || null,
        request_ip: entry.requestIp || null,
        user_agent: entry.userAgent || null
      };

      const { error } = await supabase
        .from('municipal_api_usage')
        .insert(usageEntry);

      if (error) {
        logger.error('Usage logging error:', error);
        return;
      }

      logger.debug(`📊 Usage logged: ${entry.endpoint} - ${entry.statusCode} - ${entry.responseTimeMs}ms`);

    } catch (error) {
      logger.error('Usage tracking error:', error);
    }
  }

  /**
   * Get usage statistics for an API key
   */
  async getUsageStats(apiKeyId: string, days: number = 30): Promise<UsageStats> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: usage, error } = await supabase
        .from('municipal_api_usage')
        .select('*')
        .eq('api_key_id', apiKeyId)
        .gte('created_at', startDate.toISOString());

      if (error || !usage) {
        logger.error('Usage stats error:', error);
        return this.getEmptyStats();
      }

      return this.calculateStats(usage);

    } catch (error) {
      logger.error('Usage stats calculation error:', error);
      return this.getEmptyStats();
    }
  }

  /**
   * Get usage statistics for all API keys (admin)
   */
  async getAllUsageStats(days: number = 30): Promise<UsageStats> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: usage, error } = await supabase
        .from('municipal_api_usage')
        .select('*')
        .gte('created_at', startDate.toISOString());

      if (error || !usage) {
        logger.error('All usage stats error:', error);
        return this.getEmptyStats();
      }

      return this.calculateStats(usage);

    } catch (error) {
      logger.error('All usage stats calculation error:', error);
      return this.getEmptyStats();
    }
  }

  /**
   * Get billing summary for an API key
   */
  async getBillingSummary(apiKeyId: string, month?: string): Promise<{
    totalRequests: number;
    totalCost: number;
    cacheHits: number;
    cacheMisses: number;
    avgCostPerRequest: number;
    breakdown: Array<{
      date: string;
      requests: number;
      cost: number;
      cacheHitRate: number;
    }>;
  }> {
    try {
      let startDate: Date;
      let endDate: Date;

      if (month) {
        // Parse month in YYYY-MM format
        const [year, monthNum] = month.split('-').map(Number);
        startDate = new Date(year, monthNum - 1, 1);
        endDate = new Date(year, monthNum, 0);
      } else {
        // Current month
        const now = new Date();
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      }

      const { data: usage, error } = await supabase
        .from('municipal_api_usage')
        .select('*')
        .eq('api_key_id', apiKeyId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (error || !usage) {
        return {
          totalRequests: 0,
          totalCost: 0,
          cacheHits: 0,
          cacheMisses: 0,
          avgCostPerRequest: 0,
          breakdown: []
        };
      }

      const totalRequests = usage.length;
      const totalCost = usage.reduce((sum, entry) => sum + parseFloat(entry.cost_usd || '0'), 0);
      const cacheHits = usage.filter(entry => entry.cache_hit).length;
      const cacheMisses = totalRequests - cacheHits;
      const avgCostPerRequest = totalRequests > 0 ? totalCost / totalRequests : 0;

      // Group by day for breakdown
      const dailyBreakdown = new Map<string, { requests: number; cost: number; cacheHits: number }>();
      
      usage.forEach(entry => {
        const date = new Date(entry.created_at).toISOString().split('T')[0];
        const current = dailyBreakdown.get(date) || { requests: 0, cost: 0, cacheHits: 0 };
        
        dailyBreakdown.set(date, {
          requests: current.requests + 1,
          cost: current.cost + parseFloat(entry.cost_usd || '0'),
          cacheHits: current.cacheHits + (entry.cache_hit ? 1 : 0)
        });
      });

      const breakdown = Array.from(dailyBreakdown.entries()).map(([date, data]) => ({
        date,
        requests: data.requests,
        cost: Math.round(data.cost * 10000) / 10000,
        cacheHitRate: data.requests > 0 ? (data.cacheHits / data.requests) * 100 : 0
      })).sort((a, b) => a.date.localeCompare(b.date));

      return {
        totalRequests,
        totalCost: Math.round(totalCost * 10000) / 10000,
        cacheHits,
        cacheMisses,
        avgCostPerRequest: Math.round(avgCostPerRequest * 10000) / 10000,
        breakdown
      };

    } catch (error) {
      logger.error('Billing summary error:', error);
      return {
        totalRequests: 0,
        totalCost: 0,
        cacheHits: 0,
        cacheMisses: 0,
        avgCostPerRequest: 0,
        breakdown: []
      };
    }
  }

  /**
   * Calculate comprehensive usage statistics
   */
  private calculateStats(usage: any[]): UsageStats {
    const totalRequests = usage.length;
    const successfulRequests = usage.filter(entry => entry.status_code < 400).length;
    const failedRequests = totalRequests - successfulRequests;
    const cacheHits = usage.filter(entry => entry.cache_hit).length;
    const cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;

    const responseTimes = usage
      .filter(entry => entry.response_time_ms)
      .map(entry => entry.response_time_ms);
    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    const confidenceScores = usage
      .filter(entry => entry.confidence_score)
      .map(entry => parseFloat(entry.confidence_score));
    const avgConfidence = confidenceScores.length > 0
      ? confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length
      : 0;

    const totalCost = usage.reduce((sum, entry) => sum + parseFloat(entry.cost_usd || '0'), 0);

    // Group by day
    const dailyRequests = new Map<string, { count: number; cost: number }>();
    usage.forEach(entry => {
      const date = new Date(entry.created_at).toISOString().split('T')[0];
      const current = dailyRequests.get(date) || { count: 0, cost: 0 };
      dailyRequests.set(date, {
        count: current.count + 1,
        cost: current.cost + parseFloat(entry.cost_usd || '0')
      });
    });

    const requestsByDay = Array.from(dailyRequests.entries())
      .map(([date, data]) => ({
        date,
        count: data.count,
        cost: Math.round(data.cost * 10000) / 10000
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    // Top endpoints
    const endpointCounts = new Map<string, { count: number; totalTime: number }>();
    usage.forEach(entry => {
      const current = endpointCounts.get(entry.endpoint) || { count: 0, totalTime: 0 };
      endpointCounts.set(entry.endpoint, {
        count: current.count + 1,
        totalTime: current.totalTime + (entry.response_time_ms || 0)
      });
    });

    const topEndpoints = Array.from(endpointCounts.entries())
      .map(([endpoint, data]) => ({
        endpoint,
        count: data.count,
        avgResponseTime: data.count > 0 ? Math.round(data.totalTime / data.count) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Top topics
    const topicCounts = new Map<string, { count: number; cacheHits: number }>();
    usage.forEach(entry => {
      if (entry.topic_key) {
        const current = topicCounts.get(entry.topic_key) || { count: 0, cacheHits: 0 };
        topicCounts.set(entry.topic_key, {
          count: current.count + 1,
          cacheHits: current.cacheHits + (entry.cache_hit ? 1 : 0)
        });
      }
    });

    const topTopics = Array.from(topicCounts.entries())
      .map(([topic, data]) => ({
        topic,
        count: data.count,
        cacheHitRate: data.count > 0 ? (data.cacheHits / data.count) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      avgResponseTime: Math.round(avgResponseTime),
      avgConfidence: Math.round(avgConfidence * 100) / 100,
      totalCost: Math.round(totalCost * 10000) / 10000,
      requestsByDay,
      topEndpoints,
      topTopics
    };
  }

  /**
   * Get empty stats structure
   */
  private getEmptyStats(): UsageStats {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cacheHitRate: 0,
      avgResponseTime: 0,
      avgConfidence: 0,
      totalCost: 0,
      requestsByDay: [],
      topEndpoints: [],
      topTopics: []
    };
  }
}

export const usageTracker = new UsageTracker();
