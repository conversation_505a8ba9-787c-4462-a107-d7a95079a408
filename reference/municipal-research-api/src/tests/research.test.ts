import request from 'supertest';
import app from '../index.js';
import { createClient } from '@supabase/supabase-js';
import { config } from '../config/config.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

describe('Municipal Research API', () => {
  let testApiKey: string;
  let testApiKeyId: string;

  beforeAll(async () => {
    // Create a test API key
    const response = await request(app)
      .post('/api/v1/auth/api-keys')
      .send({
        name: 'Test API Key',
        description: 'Integration test key'
      });

    testApiKey = response.body.data.apiKey;
    testApiKeyId = response.body.data.id;
  });

  afterAll(async () => {
    // Clean up test API key
    if (testApiKeyId) {
      await supabase
        .from('api_keys')
        .delete()
        .eq('id', testApiKeyId);
    }
  });

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'healthy',
        version: '1.0.0',
        environment: 'development'
      });
    });
  });

  describe('Authentication', () => {
    it('should reject requests without API key', async () => {
      const response = await request(app)
        .post('/api/v1/research')
        .send({
          address: '123 Main St, Georgetown, MI',
          query: 'fence height regulations'
        })
        .expect(401);

      expect(response.body.error).toBe('API key required');
    });

    it('should reject requests with invalid API key', async () => {
      const response = await request(app)
        .post('/api/v1/research')
        .set('X-API-Key', 'invalid_key')
        .send({
          address: '123 Main St, Georgetown, MI',
          query: 'fence height regulations'
        })
        .expect(401);

      expect(response.body.error).toBe('Invalid API key');
    });

    it('should accept requests with valid API key', async () => {
      const response = await request(app)
        .post('/api/v1/research')
        .set('X-API-Key', testApiKey)
        .send({
          address: '123 Main St, Georgetown, MI',
          query: 'fence height regulations'
        });

      // Should not be 401 (may be 200 or 500 depending on external APIs)
      expect(response.status).not.toBe(401);
    });
  });

  describe('Research Endpoint', () => {
    it('should validate request body', async () => {
      const response = await request(app)
        .post('/api/v1/research')
        .set('X-API-Key', testApiKey)
        .send({
          address: '123', // Too short
          query: 'test' // Too short
        })
        .expect(400);

      expect(response.body.error).toBe('VALIDATION_ERROR');
    });

    it('should perform municipal research', async () => {
      const response = await request(app)
        .post('/api/v1/research')
        .set('X-API-Key', testApiKey)
        .send({
          address: '123 Main St, Georgetown, MI',
          query: 'fence height regulations'
        });

      if (response.status === 200) {
        expect(response.body).toMatchObject({
          success: true,
          data: {
            jurisdiction: expect.any(String),
            topic: expect.any(String),
            answer: expect.any(String),
            sources: expect.any(Array),
            confidence: expect.any(Number),
            cached: expect.any(Boolean),
            processingTimeMs: expect.any(Number)
          },
          meta: {
            requestId: expect.any(String),
            timestamp: expect.any(String),
            apiVersion: '1.0'
          }
        });

        expect(response.body.data.confidence).toBeGreaterThanOrEqual(0);
        expect(response.body.data.confidence).toBeLessThanOrEqual(1);
        expect(response.body.data.sources.length).toBeGreaterThan(0);
      }
    }, 30000); // 30 second timeout for research

    it('should return cached results on second request', async () => {
      // First request
      const firstResponse = await request(app)
        .post('/api/v1/research')
        .set('X-API-Key', testApiKey)
        .send({
          address: '123 Main St, Georgetown, MI',
          query: 'fence height regulations'
        });

      if (firstResponse.status === 200) {
        // Second request should be cached
        const secondResponse = await request(app)
          .post('/api/v1/research')
          .set('X-API-Key', testApiKey)
          .send({
            address: '123 Main St, Georgetown, MI',
            query: 'fence height regulations'
          })
          .expect(200);

        expect(secondResponse.body.data.cached).toBe(true);
        expect(secondResponse.body.data.processingTimeMs).toBeLessThan(1000);
      }
    }, 30000);
  });

  describe('Bulk Research', () => {
    it('should handle bulk research requests', async () => {
      const response = await request(app)
        .post('/api/v1/research/bulk')
        .set('X-API-Key', testApiKey)
        .send({
          requests: [
            {
              address: '123 Main St, Georgetown, MI',
              query: 'fence height regulations'
            },
            {
              address: '456 Oak Ave, Grand Rapids, MI',
              query: 'setback requirements'
            }
          ]
        });

      if (response.status === 200) {
        expect(response.body).toMatchObject({
          success: true,
          data: {
            results: expect.any(Array),
            summary: {
              total: 2,
              successful: expect.any(Number),
              failed: expect.any(Number),
              totalCost: expect.any(Number)
            }
          }
        });

        expect(response.body.data.results).toHaveLength(2);
      }
    }, 60000); // 60 second timeout for bulk
  });

  describe('Topics Endpoint', () => {
    it('should return available topics', async () => {
      const response = await request(app)
        .get('/api/v1/research/topics')
        .set('X-API-Key', testApiKey)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.any(Array),
        meta: {
          timestamp: expect.any(String),
          count: expect.any(Number)
        }
      });

      if (response.body.data.length > 0) {
        expect(response.body.data[0]).toMatchObject({
          topic_key: expect.any(String),
          display_name: expect.any(String),
          category: expect.any(String),
          usage_count: expect.any(Number)
        });
      }
    });
  });

  describe('Analytics', () => {
    it('should return usage statistics', async () => {
      const response = await request(app)
        .get('/api/v1/analytics/usage')
        .set('X-API-Key', testApiKey)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          totalRequests: expect.any(Number),
          successfulRequests: expect.any(Number),
          failedRequests: expect.any(Number),
          cacheHitRate: expect.any(Number),
          avgResponseTime: expect.any(Number),
          avgConfidence: expect.any(Number),
          totalCost: expect.any(Number),
          requestsByDay: expect.any(Array),
          topEndpoints: expect.any(Array),
          topTopics: expect.any(Array)
        }
      });
    });

    it('should return billing information', async () => {
      const response = await request(app)
        .get('/api/v1/analytics/billing')
        .set('X-API-Key', testApiKey)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          totalRequests: expect.any(Number),
          totalCost: expect.any(Number),
          cacheHits: expect.any(Number),
          cacheMisses: expect.any(Number),
          avgCostPerRequest: expect.any(Number),
          breakdown: expect.any(Array)
        }
      });
    });
  });

  describe('Trial Codes', () => {
    it('should create trial codes', async () => {
      const response = await request(app)
        .post('/api/v1/auth/trial-codes')
        .send({
          email: '<EMAIL>',
          notes: 'Test trial code'
        })
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          code: expect.stringMatching(/^TRIAL-[A-Z0-9]{6}$/),
          email: '<EMAIL>',
          maxRequests: expect.any(Number),
          validDays: expect.any(Number)
        }
      });
    });

    it('should check trial code status', async () => {
      // Create a trial code first
      const createResponse = await request(app)
        .post('/api/v1/auth/trial-codes')
        .send({
          email: '<EMAIL>'
        });

      const trialCode = createResponse.body.data.code;

      const response = await request(app)
        .get(`/api/v1/auth/trial-codes/${trialCode}/status`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          code: trialCode,
          email: '<EMAIL>',
          maxRequests: expect.any(Number),
          requestsUsed: 0,
          remainingRequests: expect.any(Number),
          isActive: true,
          isExpired: false,
          activated: false
        }
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should include rate limit headers', async () => {
      const response = await request(app)
        .get('/api/v1/research/topics')
        .set('X-API-Key', testApiKey);

      expect(response.headers).toHaveProperty('x-ratelimit-limit');
      expect(response.headers).toHaveProperty('x-ratelimit-remaining');
      expect(response.headers).toHaveProperty('x-ratelimit-reset');
    });
  });
});
