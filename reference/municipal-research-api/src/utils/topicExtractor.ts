import natural from 'natural';
import compromise from 'compromise';
import { createClient } from '@supabase/supabase-js';
import { config } from '../config/config.js';
import { logger } from './logger.js';

const supabase = createClient(config.supabase.url, config.supabase.serviceRoleKey);

export interface TopicMatch {
  topicKey: string;
  displayName: string;
  confidence: number;
  matchedKeywords: string[];
}

class TopicExtractor {
  private stemmer: any;
  private topics: Map<string, any> = new Map();
  private lastTopicsUpdate: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.stemmer = natural.PorterStemmer;
    this.loadTopics();
  }

  /**
   * Extract the most relevant topic from a query
   */
  async extractTopic(query: string): Promise<string> {
    try {
      // Ensure topics are loaded and fresh
      await this.ensureTopicsLoaded();

      // Clean and normalize the query
      const normalizedQuery = this.normalizeQuery(query);
      
      // Extract keywords from the query
      const queryKeywords = this.extractKeywords(normalizedQuery);
      
      // Find the best matching topic
      const topicMatch = this.findBestTopicMatch(queryKeywords);
      
      if (topicMatch && topicMatch.confidence > 0.3) {
        logger.info(`🏷️ Topic extracted: ${topicMatch.topicKey} (confidence: ${topicMatch.confidence.toFixed(2)})`);
        
        // Update topic usage statistics
        await this.updateTopicUsage(topicMatch.topicKey);
        
        return topicMatch.topicKey;
      }

      // If no good match found, create a generic topic key
      const genericTopic = this.createGenericTopicKey(queryKeywords);
      logger.info(`🏷️ Generic topic created: ${genericTopic}`);
      
      return genericTopic;

    } catch (error) {
      logger.error('Topic extraction error:', error);
      // Fallback to a simple topic key
      return this.createSimpleTopicKey(query);
    }
  }

  /**
   * Get all available topics with their match confidence for a query
   */
  async getTopicSuggestions(query: string): Promise<TopicMatch[]> {
    await this.ensureTopicsLoaded();
    
    const normalizedQuery = this.normalizeQuery(query);
    const queryKeywords = this.extractKeywords(normalizedQuery);
    
    const matches: TopicMatch[] = [];
    
    for (const [topicKey, topicData] of this.topics) {
      const confidence = this.calculateTopicConfidence(queryKeywords, topicData.keywords);
      
      if (confidence > 0.1) {
        matches.push({
          topicKey,
          displayName: topicData.display_name,
          confidence,
          matchedKeywords: this.getMatchedKeywords(queryKeywords, topicData.keywords)
        });
      }
    }
    
    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Load topics from database
   */
  private async loadTopics() {
    try {
      const { data: topics, error } = await supabase
        .from('research_topics')
        .select('*')
        .order('usage_count', { ascending: false });

      if (error) {
        logger.error('Failed to load topics:', error);
        return;
      }

      this.topics.clear();
      for (const topic of topics || []) {
        this.topics.set(topic.topic_key, topic);
      }

      this.lastTopicsUpdate = Date.now();
      logger.info(`📚 Loaded ${this.topics.size} research topics`);

    } catch (error) {
      logger.error('Topic loading error:', error);
    }
  }

  /**
   * Ensure topics are loaded and not stale
   */
  private async ensureTopicsLoaded() {
    const now = Date.now();
    if (this.topics.size === 0 || (now - this.lastTopicsUpdate) > this.CACHE_TTL) {
      await this.loadTopics();
    }
  }

  /**
   * Normalize query text for processing
   */
  private normalizeQuery(query: string): string {
    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Extract meaningful keywords from query
   */
  private extractKeywords(query: string): string[] {
    // Use compromise for NLP processing
    const doc = compromise(query);
    
    // Extract nouns, adjectives, and important terms
    const nouns = doc.nouns().out('array');
    const adjectives = doc.adjectives().out('array');
    const terms = doc.terms().out('array');
    
    // Combine and filter keywords
    const allKeywords = [...nouns, ...adjectives, ...terms];
    
    // Filter out common stop words and short words
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'what', 'where', 'when', 'how', 'why', 'who']);
    
    const keywords = allKeywords
      .filter(word => word.length > 2 && !stopWords.has(word.toLowerCase()))
      .map(word => this.stemmer.stem(word.toLowerCase()));
    
    return [...new Set(keywords)]; // Remove duplicates
  }

  /**
   * Find the best matching topic for given keywords
   */
  private findBestTopicMatch(queryKeywords: string[]): TopicMatch | null {
    let bestMatch: TopicMatch | null = null;
    let bestConfidence = 0;

    for (const [topicKey, topicData] of this.topics) {
      const confidence = this.calculateTopicConfidence(queryKeywords, topicData.keywords);
      
      if (confidence > bestConfidence) {
        bestConfidence = confidence;
        bestMatch = {
          topicKey,
          displayName: topicData.display_name,
          confidence,
          matchedKeywords: this.getMatchedKeywords(queryKeywords, topicData.keywords)
        };
      }
    }

    return bestMatch;
  }

  /**
   * Calculate confidence score for topic match
   */
  private calculateTopicConfidence(queryKeywords: string[], topicKeywords: any[]): number {
    if (!Array.isArray(topicKeywords) || topicKeywords.length === 0) {
      return 0;
    }

    // Stem topic keywords for comparison
    const stemmedTopicKeywords = topicKeywords.map(keyword => 
      this.stemmer.stem(keyword.toLowerCase())
    );

    let matches = 0;
    let totalWeight = 0;

    for (const queryKeyword of queryKeywords) {
      for (const topicKeyword of stemmedTopicKeywords) {
        const similarity = natural.JaroWinklerDistance(queryKeyword, topicKeyword, {});
        if (similarity > 0.8) {
          matches += similarity;
        }
      }
      totalWeight += 1;
    }

    return totalWeight > 0 ? matches / totalWeight : 0;
  }

  /**
   * Get keywords that matched between query and topic
   */
  private getMatchedKeywords(queryKeywords: string[], topicKeywords: any[]): string[] {
    if (!Array.isArray(topicKeywords)) return [];

    const stemmedTopicKeywords = topicKeywords.map(keyword => 
      this.stemmer.stem(keyword.toLowerCase())
    );

    const matched: string[] = [];

    for (const queryKeyword of queryKeywords) {
      for (const topicKeyword of stemmedTopicKeywords) {
        if (natural.JaroWinklerDistance(queryKeyword, topicKeyword, {}) > 0.8) {
          matched.push(topicKeyword);
        }
      }
    }

    return [...new Set(matched)];
  }

  /**
   * Create a generic topic key from keywords
   */
  private createGenericTopicKey(keywords: string[]): string {
    const relevantKeywords = keywords.slice(0, 3); // Take top 3 keywords
    return relevantKeywords.join('-') + '-regulations';
  }

  /**
   * Create a simple topic key as fallback
   */
  private createSimpleTopicKey(query: string): string {
    const words = query.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .slice(0, 2);
    
    return words.join('-') + '-inquiry';
  }

  /**
   * Update topic usage statistics
   */
  private async updateTopicUsage(topicKey: string) {
    try {
      const { data: currentTopic } = await supabase
        .from('research_topics')
        .select('usage_count')
        .eq('topic_key', topicKey)
        .single();

      await supabase
        .from('research_topics')
        .update({
          usage_count: (currentTopic?.usage_count || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('topic_key', topicKey);

    } catch (error) {
      logger.error('Failed to update topic usage:', error);
    }
  }
}

export const topicExtractor = new TopicExtractor();
