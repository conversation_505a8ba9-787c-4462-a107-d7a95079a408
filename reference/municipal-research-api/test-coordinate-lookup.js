/**
 * Test coordinate-based property lookup for Jacksonville address
 */

import { StatePropertyService } from './src/services/statePropertyService.js';
import { config } from './src/config/config.js';

// Set up environment
process.env.GOOGLE_MAPS_API_KEY = config.google.mapsApiKey;

async function testCoordinateLookup() {
  console.log('🧪 Testing coordinate-based property lookup...\n');
  
  const statePropertyService = new StatePropertyService();
  
  // Test coordinates for Jacksonville, FL (approximately downtown area)
  const testCoordinates = {
    lat: 30.3322,
    lng: -81.6557
  };
  
  console.log(`📍 Testing coordinates: ${testCoordinates.lat}, ${testCoordinates.lng}`);
  console.log('   (This should be in Jacksonville, FL)\n');
  
  try {
    // Test coordinate-based lookup
    const result = await statePropertyService.queryByCoordinates(
      testCoordinates.lat, 
      testCoordinates.lng
    );
    
    console.log('📊 Results:');
    console.log(`   Found: ${result?.found}`);
    console.log(`   State: ${result?.state}`);
    console.log(`   Search Method: ${result?.searchMethod}`);
    console.log(`   Total Matches: ${result?.totalMatches}`);
    console.log(`   Data Source: ${result?.dataSource}`);
    
    if (result?.found && result.properties.length > 0) {
      console.log('\n🏠 Sample Properties:');
      result.properties.slice(0, 3).forEach((prop, index) => {
        console.log(`   ${index + 1}. ${prop.primary_land_use} (${prop.land_use_code})`);
        console.log(`      City: ${prop.city}, County: ${prop.county}`);
        console.log(`      Acres: ${prop.acres}`);
      });
    } else {
      console.log('\n❌ No properties found');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testCoordinateLookup().then(() => {
  console.log('\n✅ Test completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test error:', error);
  process.exit(1);
});
