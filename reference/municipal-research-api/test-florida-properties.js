#!/usr/bin/env node

/**
 * Test script for Florida property queries
 * Tests the new state-based property system
 */

import { StatePropertyService } from './dist/services/statePropertyService.js';

async function testFloridaProperties() {
  console.log('🧪 Testing Florida Property Service...\n');
  
  const service = new StatePropertyService();
  
  // Test addresses
  const testAddresses = [
    '123 Main St, Jacksonville, FL',
    '456 Ocean Ave, Miami, FL',
    '789 Park Blvd, Tampa, FL',
    '321 Beach Dr, St. Petersburg, FL',
    '555 University Ave, Gainesville, FL'
  ];
  
  console.log('📍 Testing address detection and queries:\n');
  
  for (const address of testAddresses) {
    console.log(`\n🔍 Testing: ${address}`);
    
    // Test state detection
    const detectedState = service.detectStateFromAddress(address);
    console.log(`   State detected: ${detectedState || 'None'}`);
    
    // Test if we can handle this location
    const canHandle = service.canHandleLocation(address);
    console.log(`   Can handle: ${canHandle}`);
    
    if (canHandle) {
      try {
        // Test property query
        const result = await service.queryByAddress(address);
        
        if (result && result.found) {
          console.log(`   ✅ Found ${result.properties.length} properties`);
          console.log(`   📊 Total matches: ${result.totalMatches}`);
          console.log(`   🏛️ Data source: ${result.dataSource}`);
          
          // Show first property details
          if (result.properties.length > 0) {
            const prop = result.properties[0];
            console.log(`   🏠 Sample property: ${prop.primary_land_use} (${prop.land_use_code})`);
            console.log(`   📍 Location: ${prop.city}, ${prop.county} County`);
            console.log(`   📏 Size: ${prop.acres} acres`);
          }
        } else {
          console.log(`   ❌ No properties found`);
        }
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
      }
    }
    
    console.log('   ' + '-'.repeat(50));
  }
  
  // Test coordinate detection
  console.log('\n\n🌍 Testing coordinate detection:\n');
  
  const testCoordinates = [
    { lat: 30.3322, lng: -81.6557, name: 'Jacksonville, FL' },
    { lat: 25.7617, lng: -80.1918, name: 'Miami, FL' },
    { lat: 27.9506, lng: -82.4572, name: 'Tampa, FL' },
    { lat: 34.0522, lng: -118.2437, name: 'Los Angeles, CA (should not work)' }
  ];
  
  for (const coord of testCoordinates) {
    console.log(`\n📍 Testing coordinates: ${coord.name} (${coord.lat}, ${coord.lng})`);
    
    const detectedState = service.detectStateFromCoordinates(coord.lat, coord.lng);
    console.log(`   State detected: ${detectedState || 'None'}`);
    
    const canHandle = service.canHandleLocation('', coord);
    console.log(`   Can handle: ${canHandle}`);
  }
  
  // Test supported states
  console.log('\n\n🗺️ Supported states:');
  const supportedStates = service.getSupportedStates();
  console.log(`   ${supportedStates.join(', ')}`);
  
  console.log('\n✅ Florida Property Service test complete!');
}

// Run the test
testFloridaProperties().catch(console.error);
