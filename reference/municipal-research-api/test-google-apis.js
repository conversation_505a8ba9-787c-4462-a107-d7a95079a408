// test-google-apis.js - Test Google API keys directly
import dotenv from 'dotenv';

dotenv.config();

async function testGeocodingAPI() {
  console.log('🧪 Testing Google Geocoding API...');
  
  const address = '1600 Amphitheatre Parkway, Mountain View, CA';
  const key = process.env.GOOGLE_GEOCODING_API_KEY;
  
  if (!key) {
    console.log('❌ No GOOGLE_GEOCODING_API_KEY found');
    return;
  }
  
  console.log(`🔑 Using key: ${key.substring(0, 10)}...`);
  
  const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${key}`;
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    console.log('📡 Response status:', data.status);
    if (data.error_message) {
      console.log('❌ Error message:', data.error_message);
    }
    if (data.results && data.results.length > 0) {
      console.log('✅ Success! Found results:', data.results.length);
    }
  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
}

async function testCustomSearchAPI() {
  console.log('\n🧪 Testing Google Custom Search API...');
  
  const query = 'test';
  const key = process.env.GOOGLE_SEARCH_API_KEY;
  const cx = process.env.GOOGLE_SEARCH_ENGINE_ID;
  
  if (!key) {
    console.log('❌ No GOOGLE_SEARCH_API_KEY found');
    return;
  }
  
  if (!cx) {
    console.log('❌ No GOOGLE_SEARCH_ENGINE_ID found');
    return;
  }
  
  console.log(`🔑 Using key: ${key.substring(0, 10)}...`);
  console.log(`🔍 Using CX: ${cx}`);
  
  const url = `https://www.googleapis.com/customsearch/v1?key=${key}&cx=${cx}&q=${encodeURIComponent(query)}`;
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.error) {
      console.log('❌ Error:', data.error.message);
      console.log('📋 Error details:', data.error);
    } else if (data.items) {
      console.log('✅ Success! Found results:', data.items.length);
    } else {
      console.log('⚠️ No results found');
    }
  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
}

async function main() {
  console.log('🔧 Google APIs Test Suite');
  console.log('========================\n');
  
  await testGeocodingAPI();
  await testCustomSearchAPI();
  
  console.log('\n✅ Test complete');
}

main().catch(console.error);
