// test-large-scale.js - Large-scale validation of the two-stage system
import dotenv from 'dotenv';
import { mcpResearcher } from './services/mcpResearcher.js';

dotenv.config();

async function runLargeScaleTests() {
  console.log('🧪 LARGE-SCALE MUNICIPAL RESEARCH VALIDATION');
  console.log('============================================\n');
  
  // Check API keys
  const perplexityKey = process.env.PERPLEXITY_API_KEY;
  const geminiKey = process.env.GEMINI_API_KEY;
  
  if (!perplexityKey || !geminiKey) {
    console.log('❌ API keys not configured');
    return;
  }
  
  console.log('✅ API keys configured - starting large-scale tests\n');
  
  // Comprehensive test matrix: 20 jurisdictions × 8 query types = 160 tests
  const jurisdictions = [
    "Mountain View, California",
    "Austin, Texas", 
    "Portland, Oregon",
    "Denver, Colorado",
    "Georgetown Township, MI",
    "Seattle, Washington",
    "Phoenix, Arizona",
    "Miami, Florida",
    "Chicago, Illinois",
    "Boston, Massachusetts",
    "Atlanta, Georgia",
    "Las Vegas, Nevada",
    "San Diego, California",
    "Dallas, Texas",
    "Nashville, Tennessee",
    "Charlotte, North Carolina",
    "Minneapolis, Minnesota",
    "Tampa, Florida",
    "Sacramento, California",
    "Kansas City, Missouri"
  ];
  
  const queryTemplates = [
    "What is the maximum height of a fence?",
    "What are the setback requirements for new construction?", 
    "Do I need a permit to build a deck?",
    "What are the parking requirements for residential properties?",
    "What are the regulations for accessory dwelling units?",
    "Can I build a shed without a permit?",
    "What are the zoning restrictions for home businesses?",
    "What building codes apply to home renovations?"
  ];
  
  // Test configuration
  const SAMPLE_SIZE = 40; // Test 40 random combinations (adjust as needed)
  const DELAY_BETWEEN_TESTS = 2000; // 2 seconds between tests
  
  console.log(`📊 Test Configuration:`);
  console.log(`   Jurisdictions: ${jurisdictions.length}`);
  console.log(`   Query types: ${queryTemplates.length}`);
  console.log(`   Sample size: ${SAMPLE_SIZE} tests`);
  console.log(`   Delay between tests: ${DELAY_BETWEEN_TESTS}ms\n`);
  
  // Generate random test combinations
  const testCases = [];
  for (let i = 0; i < SAMPLE_SIZE; i++) {
    const jurisdiction = jurisdictions[Math.floor(Math.random() * jurisdictions.length)];
    const query = queryTemplates[Math.floor(Math.random() * queryTemplates.length)];
    testCases.push({ jurisdiction, query, testNumber: i + 1 });
  }
  
  // Track results
  let results = {
    successful: 0,
    failed: 0,
    totalCost: 0,
    totalTime: 0,
    topicDistribution: {},
    confidenceScores: [],
    sourceCounts: [],
    errors: []
  };
  
  console.log('🚀 Starting large-scale tests...\n');
  
  for (const testCase of testCases) {
    console.log(`📋 TEST ${testCase.testNumber}/${SAMPLE_SIZE}`);
    console.log(`📍 ${testCase.jurisdiction}`);
    console.log(`❓ ${testCase.query}`);
    
    const startTime = Date.now();
    
    try {
      const result = await mcpResearcher.performMunicipalResearch(
        testCase.jurisdiction,
        testCase.query
      );
      
      const processingTime = Date.now() - startTime;
      
      // Track success metrics
      results.successful++;
      results.totalCost += result.costs.total;
      results.totalTime += processingTime;
      results.confidenceScores.push(result.confidence);
      results.sourceCounts.push(result.sources.length);
      
      // Track topic distribution
      const topic = mcpResearcher.extractTopicFromQuery(testCase.query);
      results.topicDistribution[topic] = (results.topicDistribution[topic] || 0) + 1;
      
      console.log(`✅ Success: ${processingTime}ms, $${result.costs.total.toFixed(4)}, confidence: ${result.confidence.toFixed(2)}`);
      
    } catch (error) {
      results.failed++;
      results.errors.push({
        test: testCase.testNumber,
        jurisdiction: testCase.jurisdiction,
        query: testCase.query,
        error: error.message
      });
      
      console.log(`❌ Failed: ${error.message}`);
    }
    
    // Progress indicator
    const progress = ((testCase.testNumber / SAMPLE_SIZE) * 100).toFixed(1);
    console.log(`📊 Progress: ${progress}% (${results.successful} success, ${results.failed} failed)\n`);
    
    // Delay between tests
    if (testCase.testNumber < SAMPLE_SIZE) {
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_TESTS));
    }
  }
  
  // Calculate final statistics
  const successRate = (results.successful / SAMPLE_SIZE) * 100;
  const avgCost = results.totalCost / results.successful;
  const avgTime = results.totalTime / results.successful;
  const avgConfidence = results.confidenceScores.reduce((a, b) => a + b, 0) / results.confidenceScores.length;
  const avgSources = results.sourceCounts.reduce((a, b) => a + b, 0) / results.sourceCounts.length;
  
  // Final report
  console.log('🏁 LARGE-SCALE TEST RESULTS');
  console.log('===========================\n');
  
  console.log('📊 SUCCESS METRICS:');
  console.log(`   Success rate: ${successRate.toFixed(1)}% (${results.successful}/${SAMPLE_SIZE})`);
  console.log(`   Average processing time: ${Math.round(avgTime)}ms`);
  console.log(`   Average confidence: ${avgConfidence.toFixed(2)}`);
  console.log(`   Average sources per query: ${avgSources.toFixed(1)}`);
  
  console.log('\n💰 COST ANALYSIS:');
  console.log(`   Total cost: $${results.totalCost.toFixed(4)}`);
  console.log(`   Average cost per request: $${avgCost.toFixed(4)}`);
  console.log(`   Cost per successful request: $${(results.totalCost / results.successful).toFixed(4)}`);
  
  console.log('\n🎯 TOPIC DISTRIBUTION:');
  Object.entries(results.topicDistribution)
    .sort(([,a], [,b]) => b - a)
    .forEach(([topic, count]) => {
      const percentage = ((count / results.successful) * 100).toFixed(1);
      console.log(`   ${topic}: ${count} queries (${percentage}%)`);
    });
  
  console.log('\n📈 ECONOMIC PROJECTIONS:');
  const dailyCosts = {
    100: avgCost * 100,
    1000: avgCost * 1000, 
    10000: avgCost * 10000
  };
  
  Object.entries(dailyCosts).forEach(([requests, cost]) => {
    const monthly = cost * 30;
    console.log(`   ${requests} requests/day: $${cost.toFixed(2)}/day = $${monthly.toFixed(2)}/month`);
  });
  
  if (results.errors.length > 0) {
    console.log('\n❌ ERRORS ENCOUNTERED:');
    results.errors.forEach(error => {
      console.log(`   Test ${error.test}: ${error.error}`);
    });
  }
  
  console.log('\n🎯 VALIDATION SUMMARY:');
  console.log(`   System reliability: ${successRate >= 95 ? '✅ EXCELLENT' : successRate >= 90 ? '✅ GOOD' : '⚠️ NEEDS IMPROVEMENT'}`);
  console.log(`   Cost efficiency: ${avgCost <= 0.005 ? '✅ EXCELLENT' : avgCost <= 0.01 ? '✅ GOOD' : '⚠️ HIGH'}`);
  console.log(`   Response quality: ${avgConfidence >= 0.7 ? '✅ HIGH' : avgConfidence >= 0.5 ? '✅ MODERATE' : '⚠️ LOW'}`);
  console.log(`   Processing speed: ${avgTime <= 15000 ? '✅ FAST' : avgTime <= 30000 ? '✅ ACCEPTABLE' : '⚠️ SLOW'}`);
}

// Run the large-scale tests
runLargeScaleTests().catch(error => {
  console.error('Large-scale test execution failed:', error);
  process.exit(1);
});
