// test-two-stage-research.js - Test the new two-stage cost-optimized research
import dotenv from 'dotenv';
import { mcpResearcher } from './services/mcpResearcher.js';

dotenv.config();

async function testTwoStageResearch() {
  console.log('🧪 Testing Two-Stage Cost-Optimized Municipal Research');
  console.log('=====================================================\n');

  // Check if API keys are configured
  const perplexityKey = process.env.PERPLEXITY_API_KEY;
  const geminiKey = process.env.GEMINI_API_KEY;

  if (!perplexityKey || perplexityKey === 'your_perplexity_api_key_here') {
    console.log('❌ PERPLEXITY_API_KEY not configured');
    console.log('📝 Please add your Perplexity API key to .env file');
    return;
  }

  if (!geminiKey || geminiKey === 'your_gemini_api_key_here') {
    console.log('❌ GEMINI_API_KEY not configured');
    console.log('📝 Please add your Gemini API key to .env file');
    return;
  }
  
  console.log('✅ Two-Stage Research API keys configured');
  console.log(`🔑 Perplexity: ${perplexityKey.substring(0, 10)}...`);
  console.log(`🔑 Gemini: ${geminiKey.substring(0, 10)}...`);

  // Comprehensive test suite with 5 different jurisdictions and queries
  const testCases = [
    {
      name: "Georgetown Township Fence Height (MCP Comparison)",
      jurisdiction: "Georgetown Township, MI",
      query: "What is the maximum height of a fence?"
    },
    {
      name: "Mountain View Setback Requirements",
      jurisdiction: "Mountain View, California",
      query: "What are the setback requirements for new construction?"
    },
    {
      name: "Austin Deck Permit Requirements",
      jurisdiction: "Austin, Texas",
      query: "Do I need a permit to build a deck?"
    },
    {
      name: "Portland Parking Requirements",
      jurisdiction: "Portland, Oregon",
      query: "What are the parking requirements for residential properties?"
    },
    {
      name: "Denver ADU Regulations",
      jurisdiction: "Denver, Colorado",
      query: "What are the regulations for accessory dwelling units?"
    }
  ];

  console.log('\n🎯 COMPREHENSIVE TEST SUITE: 5 Jurisdictions');
  console.log('==============================================');
  console.log('Testing accuracy, consistency, and cost across multiple jurisdictions\n');
  
  let totalCosts = { perplexity: 0, gemini: 0, total: 0 };
  let totalTime = 0;
  let successCount = 0;

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n📋 TEST ${i + 1}/5: ${testCase.name}`);
    console.log(`📍 Jurisdiction: ${testCase.jurisdiction}`);
    console.log(`❓ Query: ${testCase.query}`);
    console.log('⏱️ Starting research...\n');

    const startTime = Date.now();

    try {
      const result = await mcpResearcher.performMunicipalResearch(
        testCase.jurisdiction,
        testCase.query
      );

      const processingTime = Date.now() - startTime;
      totalTime += processingTime;
      successCount++;

      // Accumulate costs
      totalCosts.perplexity += result.costs.perplexity;
      totalCosts.gemini += result.costs.gemini;
      totalCosts.total += result.costs.total;

      console.log('✅ Research Results:');
      console.log(`⏱️ Processing time: ${processingTime}ms`);
      console.log(`🎯 Confidence: ${result.confidence}`);
      console.log(`📚 Sources found: ${result.sources.length}`);
      console.log(`💰 Cost: $${result.costs.total.toFixed(4)}`);

      console.log('\n📄 Answer Preview:');
      console.log('-'.repeat(60));
      console.log(result.answer.substring(0, 400) + '...');
      console.log('-'.repeat(60));

      if (result.sources.length > 0) {
        console.log('\n🔗 Top Sources:');
        result.sources.slice(0, 3).forEach((source, index) => {
          console.log(`   ${index + 1}. ${source}`);
        });
      }

      console.log('\n' + '='.repeat(80));

    } catch (error) {
      console.log(`❌ Test ${i + 1} failed: ${error.message}`);
      console.log('📋 Error details:', error);
      console.log('\n' + '='.repeat(80));
    }

    // Add delay between tests to be respectful to APIs
    if (i < testCases.length - 1) {
      console.log('⏳ Waiting 3 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Final summary
  console.log('\n🏁 COMPREHENSIVE TEST RESULTS');
  console.log('==============================');
  console.log(`✅ Successful tests: ${successCount}/${testCases.length}`);
  console.log(`⏱️ Average processing time: ${Math.round(totalTime / successCount)}ms`);
  console.log(`💰 Total costs: $${totalCosts.total.toFixed(4)}`);
  console.log(`💰 Average cost per request: $${(totalCosts.total / successCount).toFixed(4)}`);
  console.log(`💰 Cost breakdown:`);
  console.log(`   Perplexity: $${totalCosts.perplexity.toFixed(4)} (${((totalCosts.perplexity / totalCosts.total) * 100).toFixed(1)}%)`);
  console.log(`   Gemini: $${totalCosts.gemini.toFixed(4)} (${((totalCosts.gemini / totalCosts.total) * 100).toFixed(1)}%)`);

  // Economic projections
  const avgCostPerRequest = totalCosts.total / successCount;
  console.log('\n📊 ECONOMIC PROJECTIONS:');
  console.log(`   100 requests/day: $${(avgCostPerRequest * 100).toFixed(2)}/day = $${(avgCostPerRequest * 100 * 30).toFixed(2)}/month`);
  console.log(`   1,000 requests/day: $${(avgCostPerRequest * 1000).toFixed(2)}/day = $${(avgCostPerRequest * 1000 * 30).toFixed(2)}/month`);
  console.log(`   10,000 requests/day: $${(avgCostPerRequest * 10000).toFixed(2)}/day = $${(avgCostPerRequest * 10000 * 30).toFixed(2)}/month`);
}

// Run the test
testTwoStageResearch().catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
