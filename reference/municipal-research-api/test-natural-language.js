#!/usr/bin/env node

/**
 * Test script for natural language property queries
 * Tests the Smart Property Agent with real user questions
 */

import { smartPropertyAgent } from './dist/services/smartPropertyAgent.js';

async function testNaturalLanguageQueries() {
  console.log('🤖 Testing Smart Property Agent with Natural Language Queries...\n');
  
  // Test queries - the exact types you wanted to support
  const testQueries = [
    {
      address: '123 Main St, Jacksonville, FL',
      query: 'What is the future land use for this address?',
      description: 'Basic FLU query'
    },
    {
      address: '456 Ocean Ave, Miami, FL', 
      query: 'Can I build a duplex here?',
      description: 'Duplex development question'
    },
    {
      address: '789 Park Blvd, Tampa, FL',
      query: 'Is this parcel zoned for agriculture?',
      description: 'Agriculture zoning question'
    },
    {
      address: '321 University Ave, Gainesville, FL',
      query: 'What can I build on this property?',
      description: 'General development question'
    },
    {
      address: '555 Commercial Dr, Orlando, FL',
      query: 'Is this property zoned for commercial use?',
      description: 'Commercial zoning question'
    },
    {
      address: '777 Residential St, Fort Lauderdale, FL',
      query: 'What are the zoning restrictions for this address?',
      description: 'Zoning restrictions inquiry'
    }
  ];
  
  console.log('🔍 Testing natural language property queries:\n');
  
  for (let i = 0; i < testQueries.length; i++) {
    const test = testQueries[i];
    console.log(`\n${i + 1}. ${test.description}`);
    console.log(`   📍 Address: ${test.address}`);
    console.log(`   ❓ Question: "${test.query}"`);
    console.log('   ' + '-'.repeat(60));
    
    try {
      // Test the Smart Property Agent
      const result = await smartPropertyAgent.tryHandleQuery({
        address: test.address,
        query: test.query,
        userId: 'test-user',
        apiKeyId: 'test-key'
      });
      
      if (result.canHandle && result.response) {
        console.log(`   ✅ Smart Agent handled the query!`);
        console.log(`   🎯 Method: ${result.response.method}`);
        console.log(`   📊 Confidence: ${result.response.confidence}`);
        console.log(`   ⏱️ Processing time: ${result.response.processingTimeMs}ms`);
        console.log(`   🏛️ Jurisdiction: ${result.response.jurisdiction}`);
        console.log(`   📝 Topic: ${result.response.topic}`);
        console.log(`   💰 Cost: $${result.response.costUsd}`);
        console.log(`   📚 Sources: ${result.response.sources.length} source(s)`);
        
        // Show the answer (truncated for readability)
        const answer = result.response.answer;
        const truncatedAnswer = answer.length > 200 ? answer.substring(0, 200) + '...' : answer;
        console.log(`   💬 Answer: ${truncatedAnswer}`);
        
        if (result.response.sources.length > 0) {
          console.log(`   🔗 First source: ${result.response.sources[0]}`);
        }
        
      } else {
        console.log(`   ❌ Smart Agent could not handle this query`);
        if (result.fallbackReason) {
          console.log(`   📝 Reason: ${result.fallbackReason}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('   ' + '='.repeat(60));
  }
  
  // Test query analysis
  console.log('\n\n🔍 Testing Query Analysis:\n');
  
  const queryAnalysisTests = [
    'What is the zoning for this property?',
    'Can I build a duplex here?',
    'What is the future land use?',
    'Is this agricultural land?',
    'What are the building restrictions?',
    'Can I open a business here?',
    'What is the fence height limit?' // This should NOT be handled by Smart Agent
  ];
  
  // Import the query analyzer
  const { PropertyQueryAnalyzer } = await import('./dist/services/propertyQueryAnalyzer.js');
  const analyzer = new PropertyQueryAnalyzer();
  
  for (const query of queryAnalysisTests) {
    const analysis = analyzer.analyzeQuery(query);
    console.log(`Query: "${query}"`);
    console.log(`  Can handle: ${analysis.canHandle}`);
    console.log(`  Type: ${analysis.queryType}`);
    console.log(`  Confidence: ${analysis.confidence}`);
    console.log(`  Keywords: ${analysis.detectedKeywords.join(', ')}`);
    if (analysis.reason) {
      console.log(`  Reason: ${analysis.reason}`);
    }
    console.log('');
  }
  
  console.log('✅ Natural Language Query test complete!');
  console.log('\n🎉 The Smart Property Agent is ready to handle Florida property queries!');
  console.log('📋 Supported query types:');
  console.log('   • Future land use questions');
  console.log('   • Zoning inquiries'); 
  console.log('   • Development feasibility (duplex, commercial, etc.)');
  console.log('   • Agricultural land identification');
  console.log('   • General property use questions');
}

// Run the test
testNaturalLanguageQueries().catch(console.error);
