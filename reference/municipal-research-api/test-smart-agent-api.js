/**
 * Test the Smart Property Agent integration with the Municipal Research API
 */

async function testSmartAgentAPI() {
  console.log('🧪 Testing Smart Property Agent API Integration...\n');

  const baseUrl = 'http://localhost:3001';
  
  // Test data
  const testCases = [
    {
      name: 'Property Zoning Query',
      address: '123 Main Street, Chicago, IL',
      query: 'What is the zoning for this property?',
      expectedAgent: true
    },
    {
      name: 'Future Land Use Query', 
      address: '456 Oak Avenue, Austin, TX',
      query: 'What is the future land use designation?',
      expectedAgent: true
    },
    {
      name: 'Non-Property Query',
      address: '789 Pine Street, Houston, TX', 
      query: 'What are the weather patterns in this area?',
      expectedAgent: false
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📋 Test: ${testCase.name}`);
    console.log(`Address: ${testCase.address}`);
    console.log(`Query: "${testCase.query}"`);
    console.log(`Expected Smart Agent: ${testCase.expectedAgent ? 'YES' : 'NO'}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch(`${baseUrl}/api/v1/research`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-api-key' // You'll need a valid API key
        },
        body: JSON.stringify({
          address: testCase.address,
          query: testCase.query
        })
      });

      const responseTime = Date.now() - startTime;
      
      if (!response.ok) {
        console.log(`❌ HTTP Error: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.log(`Error details: ${errorText}`);
        continue;
      }

      const data = await response.json();
      
      console.log(`✅ Response received (${responseTime}ms)`);
      console.log(`Method: ${data.data?.method || 'unknown'}`);
      console.log(`Confidence: ${data.data?.confidence || 'unknown'}`);
      console.log(`Sources: ${data.data?.sources?.length || 0}`);
      console.log(`Cached: ${data.data?.cached || false}`);
      
      // Check if smart agent was used
      const usedSmartAgent = data.data?.method === 'smart-agent';
      const matchesExpectation = usedSmartAgent === testCase.expectedAgent;
      
      console.log(`Smart Agent Used: ${usedSmartAgent ? 'YES' : 'NO'}`);
      console.log(`Result: ${matchesExpectation ? '✅ PASS' : '❌ FAIL'}`);
      
      if (data.data?.answer) {
        console.log(`Answer Preview: ${data.data.answer.substring(0, 150)}...`);
      }

    } catch (error) {
      console.log(`❌ Request failed: ${error.message}`);
    }
  }

  console.log('\n🎉 Smart Agent API Integration Test Complete!');
}

// Run the test
testSmartAgentAPI().catch(console.error);
