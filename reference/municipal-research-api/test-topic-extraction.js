// test-topic-extraction.js - Test the enhanced NLP topic extraction
import { mcpResearcher } from './services/mcpResearcher.js';

function testTopicExtraction() {
  console.log('🧪 Testing Enhanced NLP Topic Extraction');
  console.log('========================================\n');
  
  const testQueries = [
    "What is the maximum height of a fence?",
    "Can I build a 6-foot privacy fence in my backyard?", 
    "What are the setback requirements for new construction?",
    "How far must my house be from the property line?",
    "Do I need a permit to build a deck?",
    "What permits are required for home renovation?",
    "What are the parking requirements for residential properties?",
    "How many parking spaces do I need for my driveway?",
    "What are the regulations for accessory dwelling units?",
    "Can I build a guest house on my property?",
    "What are the zoning restrictions in this area?",
    "What building codes apply to my renovation?",
    "How tall can my shed be?",
    "What are the rules for building a gazebo?"
  ];
  
  console.log('Query → Extracted Topic');
  console.log('-'.repeat(80));
  
  testQueries.forEach(query => {
    const topic = mcpResearcher.extractTopicFromQuery(query);
    console.log(`"${query}" → ${topic}`);
  });
  
  console.log('\n🎯 Topic-Based Caching Benefits:');
  console.log('================================');
  console.log('✅ "fence height" + "privacy fence" → SAME cache (fence-regulations)');
  console.log('✅ "setback requirements" + "distance from property line" → SAME cache (setback-requirements)');
  console.log('✅ "deck permit" + "renovation permits" → SAME cache (permit-requirements)');
  console.log('✅ Smart grouping reduces redundant Perplexity calls!');
  
  console.log('\n💰 Cost Optimization:');
  console.log('=====================');
  console.log('Without caching: Every query = $0.0037');
  console.log('With topic caching: First query per topic = $0.0037, subsequent = $0.0011');
  console.log('Cache hit rate improves over time as topic coverage grows!');
}

testTopicExtraction();
