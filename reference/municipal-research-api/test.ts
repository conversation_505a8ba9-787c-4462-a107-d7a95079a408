// test.ts - Main test runner for the Municipal Research API
import dotenv from 'dotenv';
import { runAllTests } from './test/integration.test.js';

// Load environment variables
dotenv.config();

async function main() {
  console.log('🧪 Municipal Research API Test Runner');
  console.log('====================================\n');

  // Check if required environment variables are set
  const requiredEnvVars = [
    'OPENAI_API_KEY',
    'GOOGLE_SEARCH_API_KEY',
    'GOOGLE_SEARCH_ENGINE_ID',
    'GOOGLE_GEOCODING_API_KEY'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.log('⚠️ Missing required environment variables:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('\n📝 Please set these in your .env file before running tests');
    console.log('💡 See .env.example for the required format');
    process.exit(1);
  }

  try {
    console.log('✅ Environment variables configured');
    console.log('🚀 Starting test suite...\n');

    const results = await runAllTests();

    console.log('\n📊 Final Results:');
    console.log(`   Passed: ${results.passed}`);
    console.log(`   Total:  ${results.total}`);
    console.log(`   Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);

    if (results.passed === results.total) {
      console.log('\n🎉 All tests passed! The API is ready for production.');
      process.exit(0);
    } else {
      console.log('\n⚠️ Some tests failed. Please review the output above.');
      process.exit(1);
    }

  } catch (error) {
    console.error('\n❌ Test execution failed:', error.message);
    process.exit(1);
  }
}

main();
