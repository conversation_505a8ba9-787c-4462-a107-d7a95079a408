// test/integration.test.js
import { getJurisdiction } from '../services/geocoding.js';
import { findMunicipalSources, extractConcepts } from '../services/search.js';
import { extractContent } from '../services/contentExtractor.js';
import { generateAnswer, generateFallbackAnswer } from '../services/aiAnalyzer.js';
import { knowledgeCache } from '../services/cache.js';

// Test cases based on the implementation plan
const testCases = [
  {
    name: "Georgetown Township Fence Height",
    address: "6570 Alward Dr, Hudsonville, MI 49426",
    query: "What is the maximum height of a fence?",
    expectedJurisdiction: "Georgetown Township, MI",
    expectedConcepts: ["fence", "height"],
    minimumConfidence: 0.8
  },
  {
    name: "Austin Deck Regulations", 
    address: "123 Main St, Austin, TX 78701",
    query: "Can I build a deck without a permit?",
    expectedJurisdiction: "Austin, TX",
    expectedConcepts: ["deck", "permit"],
    minimumConfidence: 0.8
  },
  {
    name: "General Zoning Query",
    address: "100 Main Street, Anytown, CA 90210",
    query: "What are the setback requirements for buildings?",
    expectedConcepts: ["building", "setback"],
    minimumConfidence: 0.3 // Lower expectation for general queries
  }
];

async function runIntegrationTests() {
  console.log('🧪 Starting Municipal Research API Integration Tests');
  console.log('=' .repeat(60));
  
  let passedTests = 0;
  let totalTests = 0;
  
  for (const testCase of testCases) {
    console.log(`\n📋 Test Case: ${testCase.name}`);
    console.log(`📍 Address: ${testCase.address}`);
    console.log(`❓ Query: ${testCase.query}`);
    
    try {
      totalTests++;
      await runSingleTest(testCase);
      passedTests++;
      console.log(`✅ ${testCase.name} - PASSED`);
    } catch (error) {
      console.log(`❌ ${testCase.name} - FAILED: ${error.message}`);
    }
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All integration tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above for details.');
  }
  
  return { passed: passedTests, total: totalTests };
}

async function runSingleTest(testCase) {
  const startTime = Date.now();
  
  // Test 1: Jurisdiction Identification
  console.log('  🏛️ Testing jurisdiction identification...');
  const jurisdiction = await getJurisdiction(testCase.address);
  
  if (testCase.expectedJurisdiction && jurisdiction !== testCase.expectedJurisdiction) {
    throw new Error(`Expected jurisdiction "${testCase.expectedJurisdiction}", got "${jurisdiction}"`);
  }
  
  console.log(`    ✓ Jurisdiction: ${jurisdiction}`);
  
  // Test 2: Concept Extraction
  console.log('  📝 Testing concept extraction...');
  const concepts = extractConcepts(testCase.query);
  
  if (testCase.expectedConcepts) {
    const missingConcepts = testCase.expectedConcepts.filter(expected => 
      !concepts.includes(expected)
    );
    
    if (missingConcepts.length > 0) {
      throw new Error(`Missing expected concepts: ${missingConcepts.join(', ')}`);
    }
  }
  
  console.log(`    ✓ Concepts: ${concepts.join(', ')}`);
  
  // Test 3: Municipal Source Discovery
  console.log('  🔍 Testing municipal source discovery...');
  const sources = await findMunicipalSources(jurisdiction, testCase.query);
  
  if (sources.length === 0) {
    console.log('    ⚠️ No sources found - will test fallback behavior');
  } else {
    console.log(`    ✓ Found ${sources.length} sources`);
    
    // Test 4: Content Extraction (only if sources found)
    console.log('  📄 Testing content extraction...');
    const extractedContent = await extractContent(sources.slice(0, 2)); // Limit for testing
    
    console.log(`    ✓ Extracted content from ${extractedContent.length} sources`);
    
    // Test 5: AI Analysis (only if content extracted)
    if (extractedContent.length > 0) {
      console.log('  🤖 Testing AI analysis...');
      const result = await generateAnswer(testCase.query, jurisdiction, extractedContent);
      
      if (result.confidence < testCase.minimumConfidence) {
        throw new Error(`Confidence ${result.confidence} below minimum ${testCase.minimumConfidence}`);
      }
      
      console.log(`    ✓ AI analysis completed with confidence: ${result.confidence}`);
      console.log(`    ✓ Answer length: ${result.answer.length} characters`);
      
      // Test 6: Caching
      console.log('  💾 Testing caching system...');
      await knowledgeCache.store(testCase.query, jurisdiction, result);
      
      const cachedResult = await knowledgeCache.findCached(testCase.query, jurisdiction);
      if (!cachedResult) {
        throw new Error('Failed to retrieve cached result');
      }
      
      console.log(`    ✓ Caching system working correctly`);
    }
  }
  
  // Test 7: Fallback Answer Generation
  console.log('  🔄 Testing fallback answer generation...');
  const fallbackAnswer = generateFallbackAnswer(testCase.query, jurisdiction);
  
  if (!fallbackAnswer || fallbackAnswer.length < 100) {
    throw new Error('Fallback answer too short or missing');
  }
  
  console.log(`    ✓ Fallback answer generated (${fallbackAnswer.length} characters)`);
  
  const processingTime = Date.now() - startTime;
  console.log(`  ⏱️ Total processing time: ${processingTime}ms`);
  
  // Performance check
  if (processingTime > 30000) { // 30 seconds
    console.log(`    ⚠️ Processing time exceeded 30 seconds`);
  }
}

// Unit tests for individual components
async function runUnitTests() {
  console.log('\n🔬 Running Unit Tests');
  console.log('-' .repeat(40));
  
  // Test concept extraction
  console.log('Testing concept extraction...');
  const testQueries = [
    { query: "fence height", expected: ["fence", "height"] },
    { query: "building permit required", expected: ["building", "permit"] },
    { query: "deck construction rules", expected: ["deck", "building"] }
  ];
  
  for (const test of testQueries) {
    const concepts = extractConcepts(test.query);
    const hasExpected = test.expected.every(expected => concepts.includes(expected));
    
    if (!hasExpected) {
      throw new Error(`Concept extraction failed for "${test.query}"`);
    }
    
    console.log(`  ✓ "${test.query}" -> [${concepts.join(', ')}]`);
  }
  
  console.log('✅ All unit tests passed');
}

// Performance testing
async function runPerformanceTests() {
  console.log('\n⚡ Running Performance Tests');
  console.log('-' .repeat(40));
  
  const performanceTest = testCases[0]; // Use Georgetown Township test
  const iterations = 3;
  const times = [];
  
  for (let i = 0; i < iterations; i++) {
    console.log(`Performance test iteration ${i + 1}/${iterations}...`);
    
    const startTime = Date.now();
    
    try {
      const jurisdiction = await getJurisdiction(performanceTest.address);
      const sources = await findMunicipalSources(jurisdiction, performanceTest.query);
      
      if (sources.length > 0) {
        const extractedContent = await extractContent(sources.slice(0, 1));
        if (extractedContent.length > 0) {
          await generateAnswer(performanceTest.query, jurisdiction, extractedContent);
        }
      }
      
      const processingTime = Date.now() - startTime;
      times.push(processingTime);
      
      console.log(`  Iteration ${i + 1}: ${processingTime}ms`);
      
    } catch (error) {
      console.log(`  Iteration ${i + 1}: Failed - ${error.message}`);
    }
  }
  
  if (times.length > 0) {
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const maxTime = Math.max(...times);
    const minTime = Math.min(...times);
    
    console.log(`📊 Performance Results:`);
    console.log(`  Average: ${Math.round(avgTime)}ms`);
    console.log(`  Min: ${minTime}ms`);
    console.log(`  Max: ${maxTime}ms`);
    
    // Performance targets from the plan
    if (avgTime < 10000) {
      console.log(`✅ Performance target met (< 10 seconds)`);
    } else {
      console.log(`⚠️ Performance target missed (${Math.round(avgTime/1000)}s > 10s)`);
    }
  }
}

// Main test runner
async function runAllTests() {
  try {
    console.log('🚀 Municipal Research API Test Suite');
    console.log('Starting comprehensive testing...\n');
    
    // Initialize cache
    await knowledgeCache.initialize();
    
    // Run all test suites
    await runUnitTests();
    const integrationResults = await runIntegrationTests();
    await runPerformanceTests();
    
    console.log('\n🏁 Test Suite Complete');
    
    return integrationResults;
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    throw error;
  }
}

// Export for use in other test files
export { runAllTests, runIntegrationTests, runUnitTests, runPerformanceTests };

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
    .then(results => {
      process.exit(results.passed === results.total ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}
