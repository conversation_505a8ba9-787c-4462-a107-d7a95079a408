{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": false, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "forceConsistentCasingInFileNames": true, "outDir": "./dist"}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "app", "components", "lib", "*.test.ts", "test/**/*", "src/tests/**/*", "src/**/*.test.ts"]}