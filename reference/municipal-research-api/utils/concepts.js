// utils/concepts.js
export function extractConcepts(query) {
  const conceptMap = {
    fence: ['fence', 'fencing', 'barrier', 'enclosure'],
    height: ['height', 'tall', 'high', 'feet', 'foot', 'ft'],
    setback: ['setback', 'property line', 'boundary', 'yard'],
    permit: ['permit', 'permission', 'approval', 'license'],
    building: ['building', 'structure', 'construction', 'erect'],
    deck: ['deck', 'patio', 'porch', 'platform'],
    pool: ['pool', 'swimming', 'spa', 'hot tub'],
    shed: ['shed', 'storage', 'outbuilding', 'accessory'],
    driveway: ['driveway', 'parking', 'pavement', 'concrete'],
    tree: ['tree', 'vegetation', 'landscaping', 'removal'],
    sign: ['sign', 'signage', 'advertisement', 'display'],
    business: ['business', 'commercial', 'home occupation'],
    zoning: ['zoning', 'zone', 'district', 'classification'],
    variance: ['variance', 'exception', 'waiver', 'appeal']
  };
  
  const concepts = [];
  const lowerQuery = query.toLowerCase();
  
  for (const [concept, keywords] of Object.entries(conceptMap)) {
    if (keywords.some(keyword => lowerQuery.includes(keyword))) {
      concepts.push(concept);
    }
  }
  
  // If no specific concepts found, use general terms
  if (concepts.length === 0) {
    concepts.push('ordinance', 'regulation', 'code');
  }
  
  return concepts;
}
