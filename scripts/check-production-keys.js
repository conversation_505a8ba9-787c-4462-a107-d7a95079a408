#!/usr/bin/env node

/**
 * Production Stripe Keys Verification Script
 * 
 * This script helps verify that you have the correct LIVE Stripe keys
 * configured for production deployment.
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Checking Stripe Keys Configuration...\n')

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local')
if (!fs.existsSync(envPath)) {
  console.log('❌ .env.local file not found')
  process.exit(1)
}

// Read environment variables
const envContent = fs.readFileSync(envPath, 'utf8')
const envVars = {}

envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=')
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim()
  }
})

// Check Stripe keys
const stripeKeys = {
  'STRIPE_SECRET_KEY': envVars.STRIPE_SECRET_KEY,
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY': envVars.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  'STRIPE_WEBHOOK_SECRET': envVars.STRIPE_WEBHOOK_SECRET,
  'STRIPE_PRICE_PRO': envVars.STRIPE_PRICE_PRO,
  'STRIPE_PRICE_APPRAISER': envVars.STRIPE_PRICE_APPRAISER,
  'NEXT_PUBLIC_STRIPE_PRICE_PRO': envVars.NEXT_PUBLIC_STRIPE_PRICE_PRO,
  'NEXT_PUBLIC_STRIPE_PRICE_APPRAISER': envVars.NEXT_PUBLIC_STRIPE_PRICE_APPRAISER,
}

console.log('📊 Current Stripe Configuration:')
console.log('================================\n')

let hasTestKeys = false
let hasLiveKeys = false
let missingKeys = []

Object.entries(stripeKeys).forEach(([key, value]) => {
  if (!value) {
    console.log(`❌ ${key}: NOT SET`)
    missingKeys.push(key)
    return
  }

  // Check if it's a test or live key
  if (key === 'STRIPE_SECRET_KEY') {
    if (value.startsWith('sk_test_')) {
      console.log(`⚠️  ${key}: TEST KEY (${value.substring(0, 20)}...)`)
      hasTestKeys = true
    } else if (value.startsWith('sk_live_')) {
      console.log(`✅ ${key}: LIVE KEY (${value.substring(0, 20)}...)`)
      hasLiveKeys = true
    } else {
      console.log(`❌ ${key}: INVALID FORMAT (${value.substring(0, 20)}...)`)
    }
  } else if (key === 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY') {
    if (value.startsWith('pk_test_')) {
      console.log(`⚠️  ${key}: TEST KEY (${value.substring(0, 20)}...)`)
      hasTestKeys = true
    } else if (value.startsWith('pk_live_')) {
      console.log(`✅ ${key}: LIVE KEY (${value.substring(0, 20)}...)`)
      hasLiveKeys = true
    } else {
      console.log(`❌ ${key}: INVALID FORMAT (${value.substring(0, 20)}...)`)
    }
  } else if (key === 'STRIPE_WEBHOOK_SECRET') {
    if (value.startsWith('whsec_')) {
      console.log(`✅ ${key}: SET (${value.substring(0, 15)}...)`)
    } else {
      console.log(`❌ ${key}: INVALID FORMAT (${value.substring(0, 15)}...)`)
    }
  } else {
    // Price IDs
    if (value.startsWith('price_')) {
      console.log(`✅ ${key}: SET (${value})`)
    } else {
      console.log(`❌ ${key}: INVALID FORMAT (${value})`)
    }
  }
})

console.log('\n🎯 Summary:')
console.log('===========')

if (missingKeys.length > 0) {
  console.log(`❌ Missing keys: ${missingKeys.join(', ')}`)
}

if (hasTestKeys && !hasLiveKeys) {
  console.log('⚠️  STATUS: DEVELOPMENT MODE (Test keys detected)')
  console.log('   → You are using TEST keys. This is fine for development.')
  console.log('   → For PRODUCTION, you need to replace with LIVE keys.')
} else if (hasLiveKeys && !hasTestKeys) {
  console.log('✅ STATUS: PRODUCTION MODE (Live keys detected)')
  console.log('   → You are using LIVE keys. Ready for production!')
} else if (hasTestKeys && hasLiveKeys) {
  console.log('❌ STATUS: MIXED KEYS (Both test and live keys detected)')
  console.log('   → This is a configuration error. Use either ALL test or ALL live keys.')
} else {
  console.log('❌ STATUS: NO VALID KEYS DETECTED')
  console.log('   → Please configure your Stripe keys.')
}

console.log('\n📚 Next Steps:')
console.log('==============')

if (hasTestKeys && !hasLiveKeys) {
  console.log('1. Go to Stripe Dashboard: https://dashboard.stripe.com')
  console.log('2. Switch to LIVE MODE (toggle off "Test mode")')
  console.log('3. Get your live API keys from Developers > API keys')
  console.log('4. Create live products and get price IDs')
  console.log('5. Set up live webhook endpoint')
  console.log('6. Update Vercel environment variables with live keys')
  console.log('7. Deploy and test with real payments')
} else if (hasLiveKeys) {
  console.log('1. Verify webhook endpoint is working')
  console.log('2. Test subscription flow with real payment')
  console.log('3. Monitor Stripe Dashboard for events')
  console.log('4. Set up billing portal for subscription management')
}

console.log('\n📖 For detailed instructions, see: PRODUCTION_STRIPE_KEYS.md')
