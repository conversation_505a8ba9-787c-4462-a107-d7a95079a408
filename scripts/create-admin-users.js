#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create admin and marketer test users for Epic 4 tests
 * This uses the Supabase Admin API to create users properly
 */

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://qxiryfbdruydrofclmvz.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE'

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createAdminUsers() {
  console.log('🔧 Creating admin and marketer test users...')

  try {
    // Create <EMAIL>
    console.log('Creating <EMAIL>...')
    const { data: adminUser, error: adminError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      email_confirm: true,
      user_metadata: {
        role: 'admin'
      }
    })

    if (adminError) {
      console.error('Error creating admin user:', adminError)
    } else {
      console.log('✅ Admin user created:', adminUser.user.email)
      
      // Create admin profile
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: adminUser.user.id,
          email: adminUser.user.email,
          role: 'admin',
          subscription_tier: 'pro',
          pulls_this_month: 0,
          extra_credits: 1000,
          referral_code: 'ADMIN001',
          first_time_user: false
        })

      if (profileError) {
        console.error('Error creating admin profile:', profileError)
      } else {
        console.log('✅ Admin profile created')
      }
    }

    // Check if marketer already exists, if not create it
    const { data: existingMarketer } = await supabase.auth.admin.getUserByEmail('<EMAIL>')

    if (!existingMarketer.user) {
      console.log('Creating <EMAIL>...')
      const { data: marketerUser, error: marketerError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'marketer123',
        email_confirm: true,
        user_metadata: {
          role: 'marketer'
        }
      })

      if (marketerError) {
        console.error('Error creating marketer user:', marketerError)
      } else {
        console.log('✅ Marketer user created:', marketerUser.user.email)
        
        // Create marketer profile
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({
            id: marketerUser.user.id,
            email: marketerUser.user.email,
            role: 'marketer',
            subscription_tier: 'pro',
            pulls_this_month: 0,
            extra_credits: 500,
            referral_code: 'MARKET01',
            first_time_user: false
          })

        if (profileError) {
          console.error('Error creating marketer profile:', profileError)
        } else {
          console.log('✅ Marketer profile created')
        }
      }
    } else {
      console.log('✅ Marketer user already exists')
    }

    // Verify users were created
    console.log('\n📋 Verifying test users...')
    const { data: profiles, error: queryError } = await supabase
      .from('profiles')
      .select('id, email, role, subscription_tier')
      .in('email', ['<EMAIL>', '<EMAIL>'])

    if (queryError) {
      console.error('Error querying profiles:', queryError)
    } else {
      console.log('Test users:')
      profiles.forEach(profile => {
        console.log(`  - ${profile.email}: ${profile.role} (${profile.subscription_tier})`)
      })
    }

    console.log('\n🎉 Admin users setup complete!')

  } catch (error) {
    console.error('❌ Error setting up admin users:', error)
    process.exit(1)
  }
}

// Run the script
createAdminUsers()
