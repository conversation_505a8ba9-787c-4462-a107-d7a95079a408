#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create test users for Epic 3 billing tests
 * This creates users through the proper signup flow
 */

const fetch = require('node-fetch')

const BASE_URL = 'http://localhost:3000'

const testUsers = [
  {
    email: '<EMAIL>',
    password: 'password123',
    tier: 'free'
  },
  {
    email: '<EMAIL>', 
    password: 'password123',
    tier: 'pro'
  },
  {
    email: '<EMAIL>',
    password: 'password123', 
    tier: 'appraiser'
  }
]

async function createTestUser(user) {
  try {
    console.log(`Creating user: ${user.email}`)
    
    // Sign up the user
    const signupResponse = await fetch(`${BASE_URL}/api/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: user.email,
        password: user.password,
        name: `Test ${user.tier.charAt(0).toUpperCase() + user.tier.slice(1)} User`
      })
    })

    if (!signupResponse.ok) {
      const error = await signupResponse.text()
      console.log(`❌ Failed to create ${user.email}: ${error}`)
      return false
    }

    console.log(`✅ Created user: ${user.email}`)
    
    // If not free tier, we would need to update their subscription
    // For now, we'll just create them as free users and update manually if needed
    
    return true
  } catch (error) {
    console.error(`❌ Error creating ${user.email}:`, error.message)
    return false
  }
}

async function main() {
  console.log('🚀 Creating test users for Epic 3 billing tests...\n')
  
  for (const user of testUsers) {
    await createTestUser(user)
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  console.log('\n✅ Test user creation complete!')
  console.log('\nNote: All users are created as free tier. To test pro/appraiser features,')
  console.log('you may need to manually update their subscription_tier in the database.')
}

if (require.main === module) {
  main().catch(console.error)
}

module.exports = { createTestUser, testUsers }
