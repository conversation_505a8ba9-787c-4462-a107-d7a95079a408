#!/usr/bin/env python3
"""
Create Florida-wide property data CSV for Supabase import
Processes all 322k+ records from the Florida Future Land Use dataset
Optimized for Smart Property Agent queries across the entire state
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_florida_csv():
    """Create a clean Florida-wide property data CSV"""
    
    # Read the full Florida dataset
    input_file = "prop-data/csv_output/flu_l2_2020_apr22.csv"
    logger.info(f"Reading data from {input_file}")
    
    df = pd.read_csv(input_file, low_memory=False)
    logger.info(f"Loaded {len(df)} total Florida records")
    
    # Clean and standardize the data for all of Florida
    florida_data = df.copy()
    
    # Create standardized columns for our property lookup system
    florida_data['city'] = florida_data['JURISDICT'].str.title()
    florida_data['state'] = 'FL'
    florida_data['county'] = florida_data['COUNTY'].str.title()
    
    # Standardize land use categories
    florida_data['primary_land_use'] = florida_data['FLU_L1_DESC'].fillna('UNKNOWN')
    florida_data['secondary_land_use'] = florida_data['FLU_L2_DESC'].fillna('')
    florida_data['land_use_code'] = florida_data['FLU_L1'].fillna('UNK')
    
    # Clean up numeric fields
    florida_data['acres'] = pd.to_numeric(florida_data['ACRES'], errors='coerce').fillna(0)
    florida_data['shape_area'] = pd.to_numeric(florida_data['SHAPE_Area'], errors='coerce').fillna(0)
    
    # Create a unique property ID
    florida_data['property_id'] = 'FL_' + florida_data['FLU_ID'].astype(str) + '_' + florida_data['AUTOID'].astype(str)
    
    # Add data source info
    florida_data['data_source'] = 'Florida Future Land Use 2020'
    florida_data['last_updated'] = '2020-04-22'
    
    # Clean up city names (remove extra spaces, standardize)
    florida_data['city'] = florida_data['city'].str.strip().str.replace(r'\s+', ' ', regex=True)
    florida_data['county'] = florida_data['county'].str.strip().str.replace(r'\s+', ' ', regex=True)
    
    # Select and order columns for final CSV
    final_columns = [
        'property_id',
        'city', 
        'state',
        'county',
        'primary_land_use',
        'secondary_land_use', 
        'land_use_code',
        'acres',
        'shape_area',
        'data_source',
        'last_updated',
        # Keep original IDs for reference
        'FLU_ID',
        'GCID', 
        'AUTOID'
    ]
    
    final_data = florida_data[final_columns].copy()
    
    # Remove any rows with missing critical data
    initial_count = len(final_data)
    final_data = final_data.dropna(subset=['property_id', 'city', 'county'])
    logger.info(f"Removed {initial_count - len(final_data)} rows with missing critical data")
    
    # Sort by county, city, and land use for easier browsing
    final_data = final_data.sort_values(['county', 'city', 'primary_land_use', 'acres'], 
                                       ascending=[True, True, True, False])
    
    # Save the cleaned data
    output_file = "prop-data/florida_property_data.csv"
    final_data.to_csv(output_file, index=False)
    
    logger.info(f"Saved {len(final_data)} Florida records to {output_file}")
    
    # Generate comprehensive summary statistics
    logger.info("\n" + "="*60)
    logger.info("FLORIDA PROPERTY DATA SUMMARY")
    logger.info("="*60)
    logger.info(f"Total Properties: {len(final_data):,}")
    logger.info(f"Counties: {final_data['county'].nunique()}")
    logger.info(f"Cities/Jurisdictions: {final_data['city'].nunique()}")
    logger.info(f"Total Acres: {final_data['acres'].sum():,.1f}")
    
    logger.info("\n=== TOP 15 COUNTIES BY PROPERTY COUNT ===")
    county_summary = final_data['county'].value_counts().head(15)
    for county, count in county_summary.items():
        pct = (count / len(final_data)) * 100
        logger.info(f"  {county}: {count:,} ({pct:.1f}%)")
    
    logger.info("\n=== TOP 15 CITIES BY PROPERTY COUNT ===")
    city_summary = final_data['city'].value_counts().head(15)
    for city, count in city_summary.items():
        pct = (count / len(final_data)) * 100
        logger.info(f"  {city}: {count:,} ({pct:.1f}%)")
    
    logger.info("\n=== LAND USE BREAKDOWN ===")
    land_use_summary = final_data['primary_land_use'].value_counts()
    for land_use, count in land_use_summary.items():
        pct = (count / len(final_data)) * 100
        logger.info(f"  {land_use}: {count:,} ({pct:.1f}%)")
    
    logger.info("\n=== SIZE DISTRIBUTION ===")
    logger.info(f"  Average Property Size: {final_data['acres'].mean():.2f} acres")
    logger.info(f"  Median Property Size: {final_data['acres'].median():.2f} acres")
    logger.info(f"  Largest Property: {final_data['acres'].max():.1f} acres")
    logger.info(f"  Properties > 10 acres: {len(final_data[final_data['acres'] > 10]):,}")
    logger.info(f"  Properties > 100 acres: {len(final_data[final_data['acres'] > 100]):,}")
    logger.info(f"  Properties > 1,000 acres: {len(final_data[final_data['acres'] > 1000]):,}")
    
    # Show sample records from different counties
    logger.info("\n=== SAMPLE RECORDS FROM MAJOR COUNTIES ===")
    major_counties = final_data['county'].value_counts().head(5).index
    for county in major_counties:
        county_data = final_data[final_data['county'] == county]
        sample = county_data[['property_id', 'city', 'primary_land_use', 'acres']].head(3)
        logger.info(f"\n{county} County (Sample):")
        print(sample.to_string(index=False))
    
    # File size info
    file_size_mb = Path(output_file).stat().st_size / (1024 * 1024)
    logger.info(f"\n=== FILE INFO ===")
    logger.info(f"Output file size: {file_size_mb:.1f} MB")
    logger.info(f"Records per MB: {len(final_data) / file_size_mb:.0f}")
    
    return output_file

if __name__ == "__main__":
    output_file = create_florida_csv()
    logger.info(f"\n✅ Florida property data ready: {output_file}")
    logger.info("This dataset covers the entire state of Florida with 322k+ properties")
    logger.info("Next step: Import this CSV into Supabase for statewide Smart Property Agent coverage")
