#!/usr/bin/env python3
"""
Create Jacksonville-specific property data CSV for Supabase import
Optimized for Smart Property Agent queries
"""

import pandas as pd
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_jacksonville_csv():
    """Create a clean Jacksonville property data CSV"""
    
    # Read the full Florida dataset
    input_file = "prop-data/csv_output/flu_l2_2020_apr22.csv"
    logger.info(f"Reading data from {input_file}")
    
    df = pd.read_csv(input_file, low_memory=False)
    logger.info(f"Loaded {len(df)} total records")
    
    # Filter for Jacksonville area (Jacksonville + Jacksonville Beach)
    jax_filter = df['JURISDICT'].str.contains('JACKSONVILLE', case=False, na=False)
    jax_data = df[jax_filter].copy()
    
    logger.info(f"Found {len(jax_data)} Jacksonville area records")
    
    # Clean and standardize the data
    jax_data = jax_data.copy()
    
    # Create standardized columns for our property lookup system
    jax_data['city'] = jax_data['JURISDICT'].str.title()
    jax_data['state'] = 'FL'
    jax_data['county'] = 'Duval'
    
    # Standardize land use categories
    jax_data['primary_land_use'] = jax_data['FLU_L1_DESC'].fillna('UNKNOWN')
    jax_data['secondary_land_use'] = jax_data['FLU_L2_DESC'].fillna('')
    jax_data['land_use_code'] = jax_data['FLU_L1'].fillna('UNK')
    
    # Clean up numeric fields
    jax_data['acres'] = pd.to_numeric(jax_data['ACRES'], errors='coerce').fillna(0)
    jax_data['shape_area'] = pd.to_numeric(jax_data['SHAPE_Area'], errors='coerce').fillna(0)
    
    # Create a unique property ID
    jax_data['property_id'] = 'JAX_' + jax_data['FLU_ID'].astype(str) + '_' + jax_data['AUTOID'].astype(str)
    
    # Add data source info
    jax_data['data_source'] = 'Jacksonville Future Land Use 2020'
    jax_data['last_updated'] = '2020-04-22'
    
    # Select and order columns for final CSV
    final_columns = [
        'property_id',
        'city', 
        'state',
        'county',
        'primary_land_use',
        'secondary_land_use', 
        'land_use_code',
        'acres',
        'shape_area',
        'data_source',
        'last_updated',
        # Keep original IDs for reference
        'FLU_ID',
        'GCID', 
        'AUTOID'
    ]
    
    final_data = jax_data[final_columns].copy()
    
    # Sort by city and land use for easier browsing
    final_data = final_data.sort_values(['city', 'primary_land_use', 'acres'], ascending=[True, True, False])
    
    # Save the cleaned data
    output_file = "prop-data/jacksonville_property_data.csv"
    final_data.to_csv(output_file, index=False)
    
    logger.info(f"Saved {len(final_data)} Jacksonville records to {output_file}")
    
    # Generate summary statistics
    logger.info("\n=== JACKSONVILLE PROPERTY DATA SUMMARY ===")
    logger.info(f"Total Properties: {len(final_data):,}")
    logger.info(f"Cities: {final_data['city'].nunique()}")
    logger.info(f"Total Acres: {final_data['acres'].sum():,.1f}")
    
    logger.info("\n=== LAND USE BREAKDOWN ===")
    land_use_summary = final_data['primary_land_use'].value_counts()
    for land_use, count in land_use_summary.items():
        pct = (count / len(final_data)) * 100
        logger.info(f"  {land_use}: {count:,} ({pct:.1f}%)")
    
    logger.info("\n=== CITY BREAKDOWN ===")
    city_summary = final_data['city'].value_counts()
    for city, count in city_summary.items():
        pct = (count / len(final_data)) * 100
        logger.info(f"  {city}: {count:,} ({pct:.1f}%)")
    
    logger.info("\n=== SIZE DISTRIBUTION ===")
    logger.info(f"  Average Property Size: {final_data['acres'].mean():.2f} acres")
    logger.info(f"  Median Property Size: {final_data['acres'].median():.2f} acres")
    logger.info(f"  Largest Property: {final_data['acres'].max():.1f} acres")
    logger.info(f"  Properties > 10 acres: {len(final_data[final_data['acres'] > 10]):,}")
    logger.info(f"  Properties > 100 acres: {len(final_data[final_data['acres'] > 100]):,}")
    
    # Show sample records
    logger.info("\n=== SAMPLE RECORDS ===")
    sample_data = final_data[['property_id', 'city', 'primary_land_use', 'acres']].head(10)
    print(sample_data.to_string(index=False))
    
    return output_file

if __name__ == "__main__":
    output_file = create_jacksonville_csv()
    logger.info(f"\n✅ Jacksonville property data ready: {output_file}")
    logger.info("Next step: Import this CSV into Supabase for the Smart Property Agent")
