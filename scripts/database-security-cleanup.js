#!/usr/bin/env node

/**
 * 🧹 DATABASE SECURITY CLEANUP SCRIPT
 * 
 * Surgically removes dead tables and implements proper RLS policies
 * Based on comprehensive codebase analysis - SAFE TO RUN
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// CONFIRMED DEAD TABLES (zero usage in codebase)
const DEAD_TABLES = [
  'conversation_sessions',
  'municipal_sources', 
  'quality_alerts',
  'municipal_research_analytics',
  'content_change_log',
  'source_quality_metrics',
  'source_verification_log',
  'test_execution_results',
  'research_test_cases',
  'research_sessions',
  'quality_metrics',
  'jurisdiction_hierarchy',
  'jurisdiction_filters',
  'confidence_thresholds',
  'research_topics',
  // Additional dead tables from analysis
  'admin_access_log',
  'admin_actions',
  'article_ratings',
  'chat_feedback',
  'chat_generated_notes',
  'content_versions',
  'contribution_files',
  'data_exports',
  'feature_usage',
  'feedback',
  'knowledge_performance_alerts',
  'knowledge_performance_metrics',
  'knowledge_refresh_jobs',
  'ordinance_clauses',
  'ordinance_versions',
  'pending_updates',
  'privacy_exports',
  'projects',
  'red_flags',
  'regions',
  'saved_search_folders',
  'saved_searches',
  'search_suggestions',
  'support_tickets',
  'syndication_metrics',
  'unanswered_queries',
  'user_contributions',
  'user_preferences',
  'user_shortcuts'
]

// ACTIVE CORE TABLES (used in application)
const ACTIVE_TABLES = [
  'profiles',
  'chat_conversations', 
  'chat_messages',
  'municipal_research_cache',
  'municipal_api_usage',
  'compliance_knowledge',
  'automation_logs',
  'referrals',
  'usage_alerts',
  'search_history',
  'content_items'
]

// TESTING TABLES (limited usage, keep but restrict)
const TESTING_TABLES = [
  'research_quality_metrics',
  'trial_codes'
]

async function executeSQL(description, sql) {
  console.log(`🔧 ${description}...`)
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error(`❌ Error: ${error.message}`)
      return false
    }
    
    console.log(`✅ ${description} completed`)
    return true
  } catch (error) {
    console.error(`❌ Failed: ${description}`)
    console.error(error)
    return false
  }
}

async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '${tableName}');`
    })
    
    if (error) return false
    return data && data[0] && data[0].exists
  } catch {
    return false
  }
}

async function dropDeadTables() {
  console.log('\n🗑️  PHASE 1: Removing Dead Tables')
  console.log('=' .repeat(50))
  
  let successCount = 0
  let totalTables = 0
  
  for (const tableName of DEAD_TABLES) {
    const exists = await checkTableExists(tableName)
    if (!exists) {
      console.log(`⏭️  Skipping ${tableName} (doesn't exist)`)
      continue
    }
    
    totalTables++
    const success = await executeSQL(
      `Drop dead table: ${tableName}`,
      `DROP TABLE IF EXISTS public.${tableName} CASCADE;`
    )
    
    if (success) successCount++
  }
  
  console.log(`\n📊 Dead Tables Summary: ${successCount}/${totalTables} removed`)
  return successCount === totalTables
}

async function secureActiveTables() {
  console.log('\n🔒 PHASE 2: Securing Active Tables')
  console.log('=' .repeat(50))
  
  // Create RLS policies for user-specific tables
  const userSpecificPolicies = `
    -- User-specific data policies
    CREATE POLICY IF NOT EXISTS "Users access own conversations" ON public.chat_conversations
      FOR ALL TO authenticated USING (auth.uid() = user_id);
    
    CREATE POLICY IF NOT EXISTS "Users access own API usage" ON public.municipal_api_usage
      FOR ALL TO authenticated USING (auth.uid() = user_id);
    
    CREATE POLICY IF NOT EXISTS "Users access own search history" ON public.search_history
      FOR ALL TO authenticated USING (auth.uid() = user_id);
  `
  
  // Create policies for public read-only data
  const publicReadPolicies = `
    -- Public read-only data policies
    CREATE POLICY IF NOT EXISTS "Public read access to research cache" ON public.municipal_research_cache
      FOR SELECT TO authenticated USING (true);
    
    CREATE POLICY IF NOT EXISTS "Public read access to compliance knowledge" ON public.compliance_knowledge
      FOR SELECT TO authenticated USING (true);
  `
  
  // Create policies for system/admin data
  const systemPolicies = `
    -- System/admin data policies (restrict to admin users)
    CREATE POLICY IF NOT EXISTS "Admin access to automation logs" ON public.automation_logs
      FOR ALL TO authenticated USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
      );
    
    CREATE POLICY IF NOT EXISTS "Admin access to content items" ON public.content_items
      FOR ALL TO authenticated USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
      );
  `
  
  const step1 = await executeSQL('Create user-specific policies', userSpecificPolicies)
  const step2 = await executeSQL('Create public read policies', publicReadPolicies)
  const step3 = await executeSQL('Create system policies', systemPolicies)
  
  return step1 && step2 && step3
}

async function secureTestingTables() {
  console.log('\n🧪 PHASE 3: Securing Testing Tables')
  console.log('=' .repeat(50))
  
  const testingPolicies = `
    -- Restrict testing tables to admin users only
    CREATE POLICY IF NOT EXISTS "Admin access to research quality metrics" ON public.research_quality_metrics
      FOR ALL TO authenticated USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
      );
    
    CREATE POLICY IF NOT EXISTS "Admin access to trial codes" ON public.trial_codes
      FOR ALL TO authenticated USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
      );
  `
  
  return await executeSQL('Create testing table policies', testingPolicies)
}

async function verifyCleanup() {
  console.log('\n✅ PHASE 4: Verification')
  console.log('=' .repeat(50))
  
  // Check remaining tables
  const { data: remainingTables } = await supabase.rpc('exec_sql', {
    sql: `SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE' ORDER BY table_name;`
  })
  
  console.log('\n📋 Remaining Tables:')
  if (remainingTables) {
    remainingTables.forEach(row => {
      const tableName = row.table_name
      if (ACTIVE_TABLES.includes(tableName)) {
        console.log(`  ✅ ${tableName} (active)`)
      } else if (TESTING_TABLES.includes(tableName)) {
        console.log(`  🧪 ${tableName} (testing)`)
      } else if (tableName === 'spatial_ref_sys') {
        console.log(`  🗺️  ${tableName} (PostGIS system table)`)
      } else {
        console.log(`  ⚠️  ${tableName} (review needed)`)
      }
    })
  }
  
  // Check RLS status
  const { data: rlsStatus } = await supabase.rpc('exec_sql', {
    sql: `SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public' AND tablename = ANY($1) ORDER BY tablename;`,
  })
  
  console.log('\n🔒 RLS Status for Active Tables:')
  if (rlsStatus) {
    rlsStatus.forEach(row => {
      const status = row.rowsecurity ? '✅ Enabled' : '❌ Disabled'
      console.log(`  ${row.tablename}: ${status}`)
    })
  }
}

async function main() {
  console.log('🚀 Starting Database Security Cleanup')
  console.log('=' .repeat(60))
  console.log(`📊 Analysis Summary:`)
  console.log(`  • Dead tables to remove: ${DEAD_TABLES.length}`)
  console.log(`  • Active tables to secure: ${ACTIVE_TABLES.length}`)
  console.log(`  • Testing tables to restrict: ${TESTING_TABLES.length}`)
  console.log('=' .repeat(60))
  
  try {
    const phase1 = await dropDeadTables()
    const phase2 = await secureActiveTables()
    const phase3 = await secureTestingTables()
    
    await verifyCleanup()
    
    if (phase1 && phase2 && phase3) {
      console.log('\n🎉 Database Security Cleanup Completed Successfully!')
      console.log('✅ Dead tables removed')
      console.log('✅ Active tables secured with RLS')
      console.log('✅ Testing tables restricted')
      console.log('\n💡 Next: Run Supabase security linter to verify fixes')
    } else {
      console.log('\n⚠️  Cleanup completed with some issues. Check logs above.')
    }
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error)
    process.exit(1)
  }
}

// Run the cleanup
main().catch(error => {
  console.error('❌ Script failed:', error)
  process.exit(1)
})
