#!/usr/bin/env node

/**
 * Debug authentication issues for chat
 */

const axios = require('axios');

async function debugAuth() {
  console.log('🔍 Debugging Authentication Issues...\n');

  try {
    // Step 1: Test auth endpoint
    console.log('1️⃣ Testing auth status...');
    const authResponse = await axios.get('http://localhost:3000/api/test-auth');
    console.log('Auth Status:', authResponse.data);

    if (!authResponse.data.authenticated) {
      console.log('\n❌ User not authenticated. Trying to authenticate...');
      
      // Try to authenticate with test user
      const loginResponse = await axios.post('http://localhost:3000/api/test-auth', {
        email: '<EMAIL>',
        password: 'business123'
      });
      
      console.log('Login Result:', loginResponse.data);
    }

    // Step 2: Test conversations endpoint directly
    console.log('\n2️⃣ Testing conversations endpoint...');
    try {
      const conversationsResponse = await axios.get('http://localhost:3000/api/chat/conversations');
      console.log('✅ Conversations API working:', conversationsResponse.data);
    } catch (error) {
      console.log('❌ Conversations API failed:', error.response?.data || error.message);
      
      if (error.response?.status === 401) {
        console.log('🔍 Authentication issue detected');
      } else if (error.response?.status === 403) {
        console.log('🔍 Permission/tier issue detected');
      }
    }

    // Step 3: Check feature flags
    console.log('\n3️⃣ Checking feature flags...');
    console.log('Environment variables:');
    console.log('- NEXT_PUBLIC_CHAT_ENABLED:', process.env.NEXT_PUBLIC_CHAT_ENABLED);
    console.log('- NEXT_PUBLIC_EPIC6_ENABLED:', process.env.NEXT_PUBLIC_EPIC6_ENABLED);

  } catch (error) {
    console.error('\n❌ Debug failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the debug
debugAuth();
