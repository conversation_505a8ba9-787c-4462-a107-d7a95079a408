#!/usr/bin/env node

/**
 * 🔍 MUNICIPAL API DEBUG TOOL
 * 
 * This script will help us understand exactly what's happening at each stage:
 * 1. What Perplexity returns (raw response structure)
 * 2. What <PERSON> receives as input
 * 3. What <PERSON> returns
 * 4. How sources are being processed
 * 
 * We'll add debug logging to the municipal API temporarily to see the data flow.
 */

const fetch = require('node-fetch')

async function debugMunicipalAPI() {
  console.log('🔍 MUNICIPAL API DEBUG SESSION')
  console.log('==============================')
  console.log('')

  const testCases = [
    {
      name: 'Simple Fence Query',
      address: '123 Test St, Georgetown, MI',
      query: 'What is the maximum fence height?'
    },
    {
      name: 'Parking Requirements',
      address: '456 Oak Ave, Grand Rapids, MI', 
      query: 'What are the parking requirements for a restaurant?'
    }
  ]

  for (const testCase of testCases) {
    console.log(`🧪 Testing: ${testCase.name}`)
    console.log(`📍 Address: ${testCase.address}`)
    console.log(`❓ Query: ${testCase.query}`)
    console.log('')

    try {
      const response = await fetch('http://localhost:3001/api/v1/research', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'ordrly_a14dacb722b572748c33627b1ac0426a7cd03b07460214523250941e43b90f2e'
        },
        body: JSON.stringify({
          address: testCase.address,
          query: testCase.query
        })
      })

      const data = await response.json()

      console.log('📊 RESPONSE ANALYSIS:')
      console.log('====================')
      console.log(`Status: ${response.status}`)
      console.log(`Success: ${data.success}`)
      console.log(`Jurisdiction: ${data.data?.jurisdiction}`)
      console.log(`Method: ${data.data?.method}`)
      console.log(`Cached: ${data.data?.cached}`)
      console.log(`Confidence: ${data.data?.confidence}`)
      console.log(`Processing Time: ${data.data?.processingTimeMs}ms`)
      console.log(`Sources Count: ${data.data?.sources?.length || 0}`)
      console.log('')

      if (data.data?.sources?.length > 0) {
        console.log('📚 SOURCES FOUND:')
        data.data.sources.forEach((source, index) => {
          console.log(`  ${index + 1}. ${source}`)
        })
      } else {
        console.log('❌ NO SOURCES RETURNED')
      }
      console.log('')

      // Check if answer has citation numbers
      const answer = data.data?.answer || ''
      const citationMatches = answer.match(/\[\d+\]/g) || []
      console.log(`📝 CITATION NUMBERS IN ANSWER: ${citationMatches.length}`)
      if (citationMatches.length > 0) {
        console.log(`   Found: ${citationMatches.join(', ')}`)
        console.log('   ⚠️  This suggests sources exist but aren\'t being extracted!')
      }
      console.log('')

      console.log('📄 ANSWER PREVIEW:')
      console.log(answer.substring(0, 300) + '...')
      console.log('')

    } catch (error) {
      console.error(`❌ Error testing ${testCase.name}:`, error.message)
    }

    console.log('=' .repeat(60))
    console.log('')
  }

  console.log('🎯 ANALYSIS SUMMARY')
  console.log('==================')
  console.log('Based on the results above, we can see:')
  console.log('1. Whether Perplexity is finding sources (citation numbers in answer)')
  console.log('2. Whether sources are being lost in processing')
  console.log('3. Whether Gemini stage is working or failing')
  console.log('')
  console.log('💡 NEXT STEPS:')
  console.log('- If citation numbers exist but sources=[], then source extraction is broken')
  console.log('- If Gemini is failing (400/401 errors), we need to fix the API call format')
  console.log('- If both stages work but sources are lost, we need to preserve them through the pipeline')
}

// Helper function to add debug logging to municipal API
function generateDebugPatch() {
  console.log('')
  console.log('🛠️  DEBUG PATCH FOR MUNICIPAL API')
  console.log('=================================')
  console.log('Add this temporary logging to municipalResearch.ts:')
  console.log('')
  console.log('// After Perplexity call (around line 200):')
  console.log('console.log("🔍 PERPLEXITY RAW RESPONSE:", JSON.stringify({')
  console.log('  citations: result.citations,')
  console.log('  citationsType: typeof result.citations,')
  console.log('  citationsKeys: result.citations ? Object.keys(result.citations) : null,')
  console.log('  hasSearchResults: !!result.search_results,')
  console.log('  searchResultsLength: result.search_results?.length')
  console.log('}, null, 2))')
  console.log('')
  console.log('// Before Gemini call (around line 240):')
  console.log('console.log("🧠 GEMINI INPUT:", {')
  console.log('  promptLength: prompt.length,')
  console.log('  sourcesFound: sources.length,')
  console.log('  sources: sources')
  console.log('})')
  console.log('')
  console.log('// After Gemini call (around line 280):')
  console.log('console.log("🧠 GEMINI RESPONSE:", {')
  console.log('  success: !!response.data,')
  console.log('  hasContent: !!response.data.candidates?.[0]?.content,')
  console.log('  error: response.data.error')
  console.log('})')
}

// Run the debug session
debugMunicipalAPI().then(() => {
  generateDebugPatch()
}).catch(console.error)
