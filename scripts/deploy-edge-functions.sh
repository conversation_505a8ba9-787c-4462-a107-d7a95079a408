#!/bin/bash

# Deploy Supabase Edge Functions for Chat AI Processing
# This script deploys the chat-ai-response Edge Function to Supabase

set -e

echo "🚀 Deploying Supabase Edge Functions..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if we're logged in to Supabase
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please run:"
    echo "   supabase login"
    exit 1
fi

# Deploy the chat-ai-response function
echo "📦 Deploying chat-ai-response Edge Function..."
supabase functions deploy chat-ai-response --project-ref qxiryfbdruydrofclmvz

# Set environment variables for the Edge Function
echo "🔧 Setting environment variables..."
supabase secrets set OPENAI_API_KEY="$OPENAI_API_KEY" --project-ref qxiryfbdruydrofclmvz

echo "✅ Edge Functions deployed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Test the Edge Function: supabase functions invoke chat-ai-response --project-ref qxiryfbdruydrofclmvz"
echo "2. Monitor logs: supabase functions logs chat-ai-response --project-ref qxiryfbdruydrofclmvz"
echo "3. Update your application to use the Edge Function endpoint"
echo ""
echo "🔗 Edge Function URL: https://qxiryfbdruydrofclmvz.supabase.co/functions/v1/chat-ai-response"
