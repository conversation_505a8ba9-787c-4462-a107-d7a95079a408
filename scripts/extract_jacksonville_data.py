#!/usr/bin/env python3
"""
Jacksonville Property Data Extractor
Extracts data from .gdb files and converts to CSV for Supabase import
"""

import os
import sys
import pandas as pd
import geopandas as gpd
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_gdb_layers(gdb_path):
    """List all layers in a geodatabase"""
    try:
        layers_df = gpd.list_layers(gdb_path)
        if isinstance(layers_df, pd.DataFrame):
            # Extract layer names from the DataFrame
            layer_names = layers_df['name'].tolist() if 'name' in layers_df.columns else []
        else:
            layer_names = layers_df

        logger.info(f"Found {len(layer_names)} layers in {gdb_path}")
        for i, layer in enumerate(layer_names):
            logger.info(f"  {i+1}. {layer}")
        return layer_names
    except Exception as e:
        logger.error(f"Error listing layers: {e}")
        return []

def extract_layer_to_csv(gdb_path, layer_name, output_dir):
    """Extract a specific layer from GDB to CSV"""
    try:
        logger.info(f"Reading layer: {layer_name}")
        gdf = gpd.read_file(gdb_path, layer=layer_name)
        
        # Log basic info about the layer
        logger.info(f"Layer {layer_name}:")
        logger.info(f"  - Rows: {len(gdf)}")
        logger.info(f"  - Columns: {len(gdf.columns)}")
        logger.info(f"  - Columns: {list(gdf.columns)}")
        
        # Convert to regular DataFrame (removes geometry for CSV)
        df = pd.DataFrame(gdf.drop(columns='geometry' if 'geometry' in gdf.columns else []))
        
        # Clean column names (remove special characters, spaces)
        df.columns = [col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '') 
                     for col in df.columns]
        
        # Save to CSV
        output_file = os.path.join(output_dir, f"{layer_name}.csv")
        df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(df)} records to {output_file}")
        
        # Show sample data
        logger.info("Sample data (first 3 rows):")
        print(df.head(3).to_string())
        print("\n" + "="*80 + "\n")
        
        return output_file
        
    except Exception as e:
        logger.error(f"Error extracting layer {layer_name}: {e}")
        return None

def analyze_data_for_property_lookup(csv_file):
    """Analyze the CSV to understand what data is available for property lookup"""
    try:
        df = pd.read_csv(csv_file)
        logger.info(f"\nAnalyzing {csv_file} for property lookup capabilities:")
        
        # Look for address-related columns
        address_cols = [col for col in df.columns if any(term in col.lower() 
                       for term in ['address', 'addr', 'street', 'road', 'ave', 'blvd'])]
        
        # Look for parcel/property ID columns
        id_cols = [col for col in df.columns if any(term in col.lower() 
                  for term in ['parcel', 'pin', 'id', 'folio', 'account'])]
        
        # Look for zoning/land use columns
        zoning_cols = [col for col in df.columns if any(term in col.lower() 
                      for term in ['zone', 'zoning', 'land_use', 'flu', 'future', 'use'])]
        
        # Look for coordinate columns
        coord_cols = [col for col in df.columns if any(term in col.lower() 
                     for term in ['lat', 'lon', 'x', 'y', 'coord', 'point'])]
        
        logger.info(f"  Address columns: {address_cols}")
        logger.info(f"  ID columns: {id_cols}")
        logger.info(f"  Zoning/Land Use columns: {zoning_cols}")
        logger.info(f"  Coordinate columns: {coord_cols}")
        
        # Show unique values for key columns (first 10)
        for col in zoning_cols[:2]:  # Show first 2 zoning columns
            unique_vals = df[col].unique()[:10]
            logger.info(f"  Sample {col} values: {unique_vals}")
            
        return {
            'address_cols': address_cols,
            'id_cols': id_cols,
            'zoning_cols': zoning_cols,
            'coord_cols': coord_cols,
            'total_records': len(df)
        }
        
    except Exception as e:
        logger.error(f"Error analyzing {csv_file}: {e}")
        return None

def main():
    # Set up paths
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    gdb_path = project_root / "prop-data" / "flu_l2_2020_apr22.gdb"
    output_dir = project_root / "prop-data" / "csv_output"
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    logger.info(f"Processing geodatabase: {gdb_path}")
    logger.info(f"Output directory: {output_dir}")
    
    # Check if GDB exists
    if not gdb_path.exists():
        logger.error(f"Geodatabase not found: {gdb_path}")
        sys.exit(1)
    
    # List all layers in the geodatabase
    layers = list_gdb_layers(str(gdb_path))

    if len(layers) == 0:
        logger.error("No layers found in geodatabase")
        sys.exit(1)
    
    # Extract each layer to CSV
    extracted_files = []
    for layer in layers:
        csv_file = extract_layer_to_csv(str(gdb_path), layer, str(output_dir))
        if csv_file:
            extracted_files.append(csv_file)
    
    # Analyze each CSV for property lookup capabilities
    logger.info("\n" + "="*80)
    logger.info("ANALYSIS FOR PROPERTY LOOKUP")
    logger.info("="*80)
    
    for csv_file in extracted_files:
        analysis = analyze_data_for_property_lookup(csv_file)
        if analysis:
            logger.info(f"\nFile: {os.path.basename(csv_file)} - {analysis['total_records']} records")
    
    logger.info(f"\nExtraction complete! {len(extracted_files)} CSV files created in {output_dir}")
    logger.info("Next steps:")
    logger.info("1. Review the CSV files and analysis above")
    logger.info("2. Identify which file(s) contain the best property lookup data")
    logger.info("3. We can then import the selected data into Supabase")

if __name__ == "__main__":
    main()
