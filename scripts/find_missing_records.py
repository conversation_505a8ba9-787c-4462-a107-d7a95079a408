#!/usr/bin/env python3
"""
Find missing records from Florida property data import
Compares source CSV with Supabase database to identify failed imports
"""

import pandas as pd
import requests
import json
import logging
from pathlib import Path
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://qxiryfbdruydrofclmvz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE"

def get_supabase_headers():
    """Get headers for Supabase API requests"""
    return {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

def get_existing_property_ids():
    """Get all property_ids currently in Supabase"""
    logger.info("Fetching existing property IDs from Supabase...")
    
    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = get_supabase_headers()
    
    # Get all property_ids in batches
    all_ids = set()
    offset = 0
    limit = 1000
    
    while True:
        params = {
            'select': 'property_id',
            'limit': limit,
            'offset': offset
        }
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code != 200:
            logger.error(f"Failed to fetch data: {response.status_code} - {response.text}")
            return None
            
        data = response.json()
        
        if not data:
            break
            
        batch_ids = {record['property_id'] for record in data}
        all_ids.update(batch_ids)
        
        logger.info(f"Fetched {len(batch_ids)} IDs (total: {len(all_ids)})")
        
        if len(data) < limit:
            break
            
        offset += limit
        time.sleep(0.1)  # Small delay to be nice to the API
    
    logger.info(f"Found {len(all_ids)} existing property IDs in Supabase")
    return all_ids

def find_missing_records():
    """Find records that exist in CSV but not in Supabase"""
    
    # Load source CSV
    csv_file = "prop-data/florida_property_data.csv"
    logger.info(f"Loading source data from {csv_file}")
    
    if not Path(csv_file).exists():
        logger.error(f"CSV file not found: {csv_file}")
        return None
    
    # Read CSV with only the columns we need for comparison
    df = pd.read_csv(csv_file, usecols=['property_id', 'city', 'county', 'primary_land_use'])
    logger.info(f"Loaded {len(df)} records from source CSV")
    
    # Get existing IDs from Supabase
    existing_ids = get_existing_property_ids()
    if existing_ids is None:
        return None
    
    # Find missing records
    source_ids = set(df['property_id'])
    missing_ids = source_ids - existing_ids
    
    logger.info(f"Source CSV has {len(source_ids)} unique property IDs")
    logger.info(f"Supabase has {len(existing_ids)} property IDs")
    logger.info(f"Missing {len(missing_ids)} records ({len(missing_ids)/len(source_ids)*100:.1f}%)")
    
    if missing_ids:
        # Get the missing records
        missing_df = df[df['property_id'].isin(missing_ids)].copy()
        
        # Save missing records for analysis
        missing_file = "prop-data/missing_records.csv"
        missing_df.to_csv(missing_file, index=False)
        logger.info(f"Saved {len(missing_df)} missing records to {missing_file}")
        
        # Analyze missing records by county/city
        logger.info("\n=== MISSING RECORDS ANALYSIS ===")
        logger.info("By County:")
        county_counts = missing_df['county'].value_counts().head(10)
        for county, count in county_counts.items():
            logger.info(f"  {county}: {count} missing")
        
        logger.info("\nBy City:")
        city_counts = missing_df['city'].value_counts().head(10)
        for city, count in city_counts.items():
            logger.info(f"  {city}: {count} missing")
        
        logger.info("\nBy Land Use:")
        landuse_counts = missing_df['primary_land_use'].value_counts().head(10)
        for landuse, count in landuse_counts.items():
            logger.info(f"  {landuse}: {count} missing")
        
        return missing_df
    else:
        logger.info("✅ No missing records found!")
        return pd.DataFrame()

if __name__ == "__main__":
    missing_records = find_missing_records()
    if missing_records is not None and len(missing_records) > 0:
        logger.info(f"\n📋 Next step: Run retry_failed_imports.py to import the {len(missing_records)} missing records")
    else:
        logger.info("✅ All records are successfully imported!")
