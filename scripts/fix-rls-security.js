#!/usr/bin/env node

/**
 * Fix RLS Security Issues Script
 * Addresses Supabase security linter issues by enabling RLS on tables
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function executeSQL(description, sql) {
  console.log(`🔧 ${description}...`)
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error(`❌ Error: ${error.message}`)
      throw error
    }
    
    console.log(`✅ ${description} completed`)
    return true
  } catch (error) {
    console.error(`❌ Failed: ${description}`)
    console.error(error)
    return false
  }
}

async function fixRLSIssues() {
  console.log('🚀 Starting RLS Security Fixes...\n')

  // Step 1: Fix compliance_knowledge table (critical issue)
  const step1Success = await executeSQL(
    'Fix compliance_knowledge table RLS',
    `ALTER TABLE public.compliance_knowledge ENABLE ROW LEVEL SECURITY;`
  )

  // Step 2: Enable RLS on user-specific tables
  const step2Success = await executeSQL(
    'Enable RLS on user-specific tables',
    `
    ALTER TABLE public.conversation_sessions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.trial_codes ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.municipal_api_usage ENABLE ROW LEVEL SECURITY;
    `
  )

  // Step 3: Enable RLS on public read-only tables
  const step3Success = await executeSQL(
    'Enable RLS on public read-only tables',
    `
    ALTER TABLE public.municipal_research_cache ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.municipal_sources ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.jurisdiction_hierarchy ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.jurisdiction_filters ENABLE ROW LEVEL SECURITY;
    `
  )

  // Step 4: Enable RLS on system/admin tables
  const step4Success = await executeSQL(
    'Enable RLS on system/admin tables',
    `
    ALTER TABLE public.source_verification_log ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.quality_alerts ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.source_quality_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.content_change_log ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.research_quality_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.municipal_research_analytics ENABLE ROW LEVEL SECURITY;
    `
  )

  // Step 5: Enable RLS on development/testing tables
  const step5Success = await executeSQL(
    'Enable RLS on development/testing tables',
    `
    ALTER TABLE public.confidence_thresholds ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.test_execution_results ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.research_sessions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.research_test_cases ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.quality_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.research_topics ENABLE ROW LEVEL SECURITY;
    `
  )

  // Step 6: Handle spatial_ref_sys (PostGIS system table)
  const step6Success = await executeSQL(
    'Handle spatial_ref_sys table',
    `
    ALTER TABLE public.spatial_ref_sys ENABLE ROW LEVEL SECURITY;
    
    -- Create policy for public read access to spatial reference systems
    DROP POLICY IF EXISTS "Public read access to spatial reference systems" ON public.spatial_ref_sys;
    CREATE POLICY "Public read access to spatial reference systems" 
    ON public.spatial_ref_sys FOR SELECT USING (true);
    `
  )

  console.log('\n📊 Summary:')
  console.log(`Step 1 (compliance_knowledge): ${step1Success ? '✅' : '❌'}`)
  console.log(`Step 2 (user tables): ${step2Success ? '✅' : '❌'}`)
  console.log(`Step 3 (public tables): ${step3Success ? '✅' : '❌'}`)
  console.log(`Step 4 (system tables): ${step4Success ? '✅' : '❌'}`)
  console.log(`Step 5 (dev tables): ${step5Success ? '✅' : '❌'}`)
  console.log(`Step 6 (spatial_ref_sys): ${step6Success ? '✅' : '❌'}`)

  const allSuccess = step1Success && step2Success && step3Success && step4Success && step5Success && step6Success

  if (allSuccess) {
    console.log('\n🎉 All RLS security fixes applied successfully!')
    console.log('The Supabase security linter should now show fewer errors.')
  } else {
    console.log('\n⚠️ Some fixes failed. Check the errors above.')
    process.exit(1)
  }
}

// Run the fixes
fixRLSIssues().catch(error => {
  console.error('❌ Script failed:', error)
  process.exit(1)
})
