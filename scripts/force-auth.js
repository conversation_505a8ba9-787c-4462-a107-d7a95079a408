#!/usr/bin/env node

/**
 * Force authentication for development
 */

const axios = require('axios');

async function forceAuth() {
  console.log('🔐 Forcing authentication...\n');

  try {
    // Use the dev auth endpoint
    const response = await axios.post('http://localhost:3000/api/dev-auth');
    console.log('✅ Auth result:', response.data);

    // Test conversations endpoint
    console.log('\n🧪 Testing conversations endpoint...');
    const conversationsResponse = await axios.get('http://localhost:3000/api/chat/conversations');
    console.log('✅ Conversations working:', conversationsResponse.data);

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

forceAuth();
