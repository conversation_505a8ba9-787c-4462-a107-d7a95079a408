#!/usr/bin/env node

const fetch = require('node-fetch');

async function forceLogout() {
  console.log('🔄 Forcing logout...');
  
  try {
    const response = await fetch('http://localhost:3000/api/force-logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Success:', result.message);
    } else {
      console.log('❌ Error:', result.error);
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

forceLogout();
