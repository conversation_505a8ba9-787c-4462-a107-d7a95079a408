#!/usr/bin/env python3
"""
Import Florida property data to Supabase in batches
Handles the 322k+ records efficiently with progress tracking
"""

import pandas as pd
import requests
import json
import time
import logging
from pathlib import Path
import os
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://qxiryfbdruydrofclmvz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE"

def get_supabase_headers():
    """Get headers for Supabase API requests"""
    return {
        "apikey": SUPABASE_SERVICE_KEY,
        "Authorization": f"Bearer {SUPABASE_SERVICE_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }

def clean_data_for_import(df):
    """Clean and prepare data for Supabase import"""
    # Create a copy to avoid modifying original
    clean_df = df.copy()

    # Handle NaN values
    clean_df = clean_df.fillna({
        'secondary_land_use': '',
        'land_use_code': '',
        'acres': 0,
        'shape_area': 0,
        'data_source': '',
        'GCID': 0,
        'AUTOID': 0
    })

    # Convert date string to proper format
    clean_df['last_updated'] = '2020-04-22'

    # Ensure numeric fields are properly formatted
    clean_df['acres'] = pd.to_numeric(clean_df['acres'], errors='coerce').fillna(0)
    clean_df['shape_area'] = pd.to_numeric(clean_df['shape_area'], errors='coerce').fillna(0)
    clean_df['GCID'] = pd.to_numeric(clean_df['GCID'], errors='coerce').fillna(0)
    clean_df['AUTOID'] = pd.to_numeric(clean_df['AUTOID'], errors='coerce').fillna(0).astype(int)

    # Remove any rows with missing critical data
    clean_df = clean_df.dropna(subset=['property_id', 'city', 'county', 'primary_land_use'])

    return clean_df

def batch_insert_to_supabase(data_batch, batch_num, total_batches):
    """Insert a batch of data to Supabase"""
    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = get_supabase_headers()
    
    try:
        # Convert DataFrame to list of dictionaries
        records = data_batch.to_dict('records')
        
        # Make the request
        response = requests.post(url, headers=headers, json=records, timeout=60)
        
        if response.status_code in [200, 201]:
            logger.info(f"✅ Batch {batch_num}/{total_batches} imported successfully ({len(records)} records)")
            return True
        else:
            logger.error(f"❌ Batch {batch_num} failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error importing batch {batch_num}: {e}")
        return False

def import_florida_data():
    """Main function to import Florida property data"""
    
    # Load the CSV data
    csv_file = "prop-data/florida_property_data.csv"
    logger.info(f"Loading data from {csv_file}")
    
    if not Path(csv_file).exists():
        logger.error(f"CSV file not found: {csv_file}")
        return False
    
    # Read the CSV
    df = pd.read_csv(csv_file)
    logger.info(f"Loaded {len(df)} records from CSV")
    
    # Clean the data
    clean_df = clean_data_for_import(df)
    logger.info(f"Cleaned data: {len(clean_df)} records ready for import")
    
    # Remove columns that don't exist in Supabase table
    supabase_columns = [
        'property_id', 'city', 'state', 'county', 'primary_land_use',
        'secondary_land_use', 'land_use_code', 'acres', 'shape_area',
        'data_source', 'last_updated', 'flu_id', 'gcid', 'autoid'
    ]
    
    # Map CSV columns to Supabase columns
    column_mapping = {
        'FLU_ID': 'flu_id',
        'GCID': 'gcid',
        'AUTOID': 'autoid'
    }
    
    clean_df = clean_df.rename(columns=column_mapping)
    clean_df = clean_df[supabase_columns]
    
    # Import in batches
    batch_size = 1000  # Supabase can handle ~1000 records per request
    total_records = len(clean_df)
    total_batches = (total_records + batch_size - 1) // batch_size
    
    logger.info(f"Starting import: {total_records} records in {total_batches} batches")
    
    successful_batches = 0
    failed_batches = 0
    start_time = time.time()
    
    for i in range(0, total_records, batch_size):
        batch_num = (i // batch_size) + 1
        batch_data = clean_df.iloc[i:i + batch_size]
        
        logger.info(f"Processing batch {batch_num}/{total_batches} (records {i+1}-{min(i+batch_size, total_records)})")
        
        if batch_insert_to_supabase(batch_data, batch_num, total_batches):
            successful_batches += 1
        else:
            failed_batches += 1
            
        # Add a small delay to avoid overwhelming the API
        time.sleep(0.5)
        
        # Progress update every 10 batches
        if batch_num % 10 == 0:
            elapsed = time.time() - start_time
            rate = batch_num / elapsed * 60  # batches per minute
            eta = (total_batches - batch_num) / rate if rate > 0 else 0
            logger.info(f"Progress: {batch_num}/{total_batches} batches ({batch_num/total_batches*100:.1f}%) - ETA: {eta:.1f} minutes")
    
    # Final summary
    elapsed_total = time.time() - start_time
    logger.info(f"\n{'='*60}")
    logger.info(f"IMPORT COMPLETE")
    logger.info(f"{'='*60}")
    logger.info(f"Total time: {elapsed_total/60:.1f} minutes")
    logger.info(f"Successful batches: {successful_batches}")
    logger.info(f"Failed batches: {failed_batches}")
    logger.info(f"Records imported: {successful_batches * batch_size}")
    logger.info(f"Success rate: {successful_batches/total_batches*100:.1f}%")
    
    return failed_batches == 0

if __name__ == "__main__":
    success = import_florida_data()
    if success:
        logger.info("🎉 Florida property data import completed successfully!")
    else:
        logger.error("❌ Import completed with errors. Check logs above.")
