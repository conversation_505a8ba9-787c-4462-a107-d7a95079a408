#!/usr/bin/env python3
"""
Import only the missing records from Florida property data
Identifies what's missing and imports just those records
"""

import pandas as pd
import requests
import json
import logging
from pathlib import Path
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://qxiryfbdruydrofclmvz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE"

def get_supabase_headers():
    """Get headers for Supabase API requests"""
    return {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

def get_existing_property_ids_sample():
    """Get a sample of existing property_ids to identify the pattern of missing records"""
    logger.info("Getting sample of existing property IDs...")
    
    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = get_supabase_headers()
    
    # Get a large sample to identify patterns
    params = {
        'select': 'property_id,flu_id,autoid',
        'limit': 10000,
        'order': 'flu_id.asc'
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        data = response.json()
        existing_ids = {record['property_id'] for record in data}
        logger.info(f"Sample: {len(existing_ids)} existing property IDs")
        return existing_ids
    else:
        logger.error(f"Failed to fetch sample: {response.status_code}")
        return set()

def find_missing_records_smart():
    """Smart approach to find missing records without loading all existing IDs"""
    
    # Load source CSV
    csv_file = "prop-data/florida_property_data.csv"
    logger.info(f"Loading source data from {csv_file}")
    
    df = pd.read_csv(csv_file)
    logger.info(f"Loaded {len(df)} records from source CSV")
    
    # Clean and prepare data (reuse existing logic)
    from import_florida_data_to_supabase import clean_data_for_import
    clean_df = clean_data_for_import(df)
    
    # Map columns
    column_mapping = {
        'FLU_ID': 'flu_id',
        'GCID': 'gcid',
        'AUTOID': 'autoid'
    }
    clean_df = clean_df.rename(columns=column_mapping)
    
    # Create property_id if not exists
    if 'property_id' not in clean_df.columns:
        clean_df['property_id'] = 'FL_' + clean_df['flu_id'].astype(str) + '_' + clean_df['autoid'].astype(str)
    
    # Get sample of existing IDs to check patterns
    existing_sample = get_existing_property_ids_sample()
    
    # Check which records from our sample are missing
    source_sample = set(clean_df['property_id'].head(10000))
    missing_in_sample = source_sample - existing_sample
    
    logger.info(f"In sample of 10k: {len(missing_in_sample)} missing records")
    
    # If we have missing records in sample, likely we have missing throughout
    if missing_in_sample:
        logger.info("Found missing records in sample. Checking specific ranges...")
        
        # Strategy: Check records in chunks to identify missing ones
        missing_records = []
        chunk_size = 1000
        
        for i in range(0, len(clean_df), chunk_size):
            chunk = clean_df.iloc[i:i + chunk_size]
            chunk_ids = set(chunk['property_id'])
            
            # Check if these IDs exist in Supabase
            id_list = list(chunk_ids)
            
            # Query Supabase for these specific IDs
            url = f"{SUPABASE_URL}/rest/v1/florida_properties"
            headers = get_supabase_headers()
            
            # Use filter to check existence
            filter_query = 'property_id.in.(' + ','.join(f'"{id}"' for id in id_list) + ')'
            params = {
                'select': 'property_id',
                'property_id': filter_query
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                existing_in_chunk = {record['property_id'] for record in response.json()}
                missing_in_chunk = chunk_ids - existing_in_chunk
                
                if missing_in_chunk:
                    missing_chunk_records = chunk[chunk['property_id'].isin(missing_in_chunk)]
                    missing_records.append(missing_chunk_records)
                    logger.info(f"Chunk {i//chunk_size + 1}: {len(missing_in_chunk)} missing records")
                else:
                    logger.info(f"Chunk {i//chunk_size + 1}: All records exist")
            else:
                logger.warning(f"Failed to check chunk {i//chunk_size + 1}: {response.status_code}")
            
            time.sleep(0.1)  # Rate limiting
        
        if missing_records:
            missing_df = pd.concat(missing_records, ignore_index=True)
            logger.info(f"Found {len(missing_df)} total missing records")
            return missing_df
        else:
            logger.info("No missing records found!")
            return pd.DataFrame()
    
    else:
        logger.info("No missing records detected in sample")
        return pd.DataFrame()

def import_missing_records():
    """Import only the missing records"""
    
    # Find missing records
    missing_df = find_missing_records_smart()
    
    if len(missing_df) == 0:
        logger.info("✅ No missing records to import!")
        return True
    
    # Prepare missing records for import
    supabase_columns = [
        'property_id', 'city', 'state', 'county', 'primary_land_use',
        'secondary_land_use', 'land_use_code', 'acres', 'shape_area',
        'data_source', 'last_updated', 'flu_id', 'gcid', 'autoid'
    ]
    
    missing_df = missing_df[supabase_columns]
    
    # Import in small batches with retry
    batch_size = 100  # Small batches for reliability
    total_records = len(missing_df)
    total_batches = (total_records + batch_size - 1) // batch_size
    
    logger.info(f"Importing {total_records} missing records in {total_batches} batches")
    
    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = get_supabase_headers()
    headers['Prefer'] = 'resolution=merge-duplicates'  # Handle any duplicates
    
    successful = 0
    failed = 0
    
    for i in range(0, total_records, batch_size):
        batch_num = (i // batch_size) + 1
        batch_data = missing_df.iloc[i:i + batch_size]
        
        records = batch_data.to_dict('records')
        
        for attempt in range(3):  # 3 retry attempts
            try:
                response = requests.post(url, headers=headers, json=records, timeout=60)
                
                if response.status_code in [200, 201]:
                    logger.info(f"✅ Batch {batch_num}/{total_batches} imported ({len(records)} records)")
                    successful += len(records)
                    break
                else:
                    logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} failed: {response.status_code}")
                    if attempt == 2:
                        logger.error(f"❌ Batch {batch_num} failed permanently: {response.text}")
                        failed += len(records)
                    else:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        
            except Exception as e:
                logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} error: {e}")
                if attempt == 2:
                    logger.error(f"❌ Batch {batch_num} failed permanently: {e}")
                    failed += len(records)
                else:
                    time.sleep(2 ** attempt)
        
        time.sleep(0.2)  # Small delay between batches
    
    logger.info(f"\n{'='*50}")
    logger.info(f"MISSING RECORDS IMPORT COMPLETE")
    logger.info(f"{'='*50}")
    logger.info(f"Successfully imported: {successful}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Success rate: {successful/(successful+failed)*100:.1f}%")
    
    return failed == 0

if __name__ == "__main__":
    success = import_missing_records()
    if success:
        logger.info("🎉 All missing records imported successfully!")
    else:
        logger.error("❌ Some records still failed to import.")
