#!/usr/bin/env python3
"""
Import only the missing records from Florida property data
Uses efficient chunked comparison to identify missing records
"""

import pandas as pd
import requests
import logging
from pathlib import Path
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://qxiryfbdruydrofclmvz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE"

def get_supabase_headers():
    """Get headers for Supabase API requests"""
    return {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json'
    }

def find_missing_records_efficiently():
    """Find missing records by comparing source CSV with database in chunks"""
    
    # Load source CSV
    csv_file = "prop-data/florida_property_data.csv"
    logger.info(f"Loading source data from {csv_file}")
    
    df = pd.read_csv(csv_file, low_memory=False)
    logger.info(f"Loaded {len(df)} records from source CSV")
    
    # Clean and prepare data (reuse existing logic)
    from import_florida_data_to_supabase import clean_data_for_import
    clean_df = clean_data_for_import(df)
    
    # Map columns
    column_mapping = {
        'FLU_ID': 'flu_id',
        'GCID': 'gcid',
        'AUTOID': 'autoid'
    }
    clean_df = clean_df.rename(columns=column_mapping)
    
    # Create property_id if not exists
    if 'property_id' not in clean_df.columns:
        clean_df['property_id'] = 'FL_' + clean_df['flu_id'].astype(str) + '_' + clean_df['autoid'].astype(str)
    
    # Prepare final columns
    supabase_columns = [
        'property_id', 'city', 'state', 'county', 'primary_land_use',
        'secondary_land_use', 'land_use_code', 'acres', 'shape_area',
        'data_source', 'last_updated', 'flu_id', 'gcid', 'autoid'
    ]
    clean_df = clean_df[supabase_columns]
    
    logger.info(f"Prepared {len(clean_df)} records for comparison")
    
    # Find missing records by checking chunks
    missing_records = []
    chunk_size = 2000
    total_chunks = (len(clean_df) + chunk_size - 1) // chunk_size
    
    logger.info(f"Checking for missing records in {total_chunks} chunks...")
    
    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = get_supabase_headers()
    
    for i in range(0, len(clean_df), chunk_size):
        chunk_num = (i // chunk_size) + 1
        chunk = clean_df.iloc[i:i + chunk_size]
        chunk_ids = list(chunk['property_id'])
        
        logger.info(f"Checking chunk {chunk_num}/{total_chunks} ({len(chunk_ids)} records)")
        
        # Query Supabase for these specific IDs using IN filter
        # Split into smaller sub-chunks for the IN query (Supabase has limits)
        sub_chunk_size = 100
        existing_in_chunk = set()
        
        for j in range(0, len(chunk_ids), sub_chunk_size):
            sub_chunk_ids = chunk_ids[j:j + sub_chunk_size]
            
            # Create IN filter
            id_filter = ','.join(f'"{id}"' for id in sub_chunk_ids)
            params = {
                'select': 'property_id',
                'property_id': f'in.({id_filter})'
            }
            
            try:
                response = requests.get(url, headers=headers, params=params, timeout=30)
                
                if response.status_code == 200:
                    existing_data = response.json()
                    existing_ids = {record['property_id'] for record in existing_data}
                    existing_in_chunk.update(existing_ids)
                else:
                    logger.warning(f"Failed to check sub-chunk: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"Error checking sub-chunk: {e}")
            
            time.sleep(0.05)  # Small delay
        
        # Find missing records in this chunk
        chunk_ids_set = set(chunk_ids)
        missing_in_chunk = chunk_ids_set - existing_in_chunk
        
        if missing_in_chunk:
            missing_chunk_records = chunk[chunk['property_id'].isin(missing_in_chunk)]
            missing_records.append(missing_chunk_records)
            logger.info(f"  Found {len(missing_in_chunk)} missing records in chunk {chunk_num}")
        else:
            logger.info(f"  All records exist in chunk {chunk_num}")
        
        # Progress update
        if chunk_num % 10 == 0:
            logger.info(f"Progress: {chunk_num}/{total_chunks} chunks checked")
    
    if missing_records:
        missing_df = pd.concat(missing_records, ignore_index=True)
        logger.info(f"\n✅ Found {len(missing_df)} total missing records")
        return missing_df
    else:
        logger.info("\n✅ No missing records found!")
        return pd.DataFrame()

def import_missing_records(missing_df):
    """Import the missing records with proper error handling"""
    
    if len(missing_df) == 0:
        logger.info("No records to import")
        return True
    
    logger.info(f"Importing {len(missing_df)} missing records...")
    
    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = get_supabase_headers()
    headers['Prefer'] = 'return=minimal'
    
    # Import in small batches for reliability
    batch_size = 200
    total_batches = (len(missing_df) + batch_size - 1) // batch_size
    
    successful_records = 0
    failed_records = 0
    
    for i in range(0, len(missing_df), batch_size):
        batch_num = (i // batch_size) + 1
        batch_data = missing_df.iloc[i:i + batch_size]
        
        records = batch_data.to_dict('records')
        
        logger.info(f"Importing batch {batch_num}/{total_batches} ({len(records)} records)")
        
        # Try with retry
        for attempt in range(3):
            try:
                response = requests.post(url, headers=headers, json=records, timeout=60)
                
                if response.status_code in [200, 201]:
                    successful_records += len(records)
                    logger.info(f"✅ Batch {batch_num} imported successfully")
                    break
                else:
                    if attempt == 2:
                        logger.error(f"❌ Batch {batch_num} failed: {response.status_code} - {response.text[:200]}")
                        failed_records += len(records)
                    else:
                        logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} failed, retrying...")
                        time.sleep(2 ** attempt)
                        
            except Exception as e:
                if attempt == 2:
                    logger.error(f"❌ Batch {batch_num} exception: {e}")
                    failed_records += len(records)
                else:
                    logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} exception, retrying...")
                    time.sleep(2 ** attempt)
        
        time.sleep(0.1)  # Small delay between batches
    
    logger.info(f"\n{'='*50}")
    logger.info(f"MISSING RECORDS IMPORT COMPLETE")
    logger.info(f"{'='*50}")
    logger.info(f"Successfully imported: {successful_records}")
    logger.info(f"Failed: {failed_records}")
    logger.info(f"Success rate: {successful_records/(successful_records+failed_records)*100:.1f}%")
    
    return failed_records == 0

def main():
    """Main function to find and import missing records"""
    
    logger.info("🔍 Starting targeted import of missing Florida property records")
    
    # Find missing records
    missing_df = find_missing_records_efficiently()
    
    if len(missing_df) == 0:
        logger.info("🎉 All records are already imported!")
        return True
    
    # Save missing records for reference
    missing_file = "prop-data/missing_records_found.csv"
    missing_df.to_csv(missing_file, index=False)
    logger.info(f"📄 Saved missing records to {missing_file}")
    
    # Import missing records
    success = import_missing_records(missing_df)
    
    if success:
        logger.info("🎉 All missing records imported successfully!")
        # Clean up the missing records file
        Path(missing_file).unlink()
    else:
        logger.error("❌ Some records failed to import. Check logs above.")
    
    return success

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("✅ Import process completed successfully!")
    else:
        logger.error("❌ Import process completed with errors.")
