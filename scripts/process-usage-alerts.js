#!/usr/bin/env node

/**
 * Usage Alert Processing Script
 * 
 * This script processes pending usage alerts by:
 * 1. Calling the Supabase function to get unprocessed alerts
 * 2. For each alert, calling our API to send the email
 * 
 * Run this script every 5 minutes via cron:
 * 0,5,10,15,20,25,30,35,40,45,50,55 * * * * /usr/bin/node /path/to/process-usage-alerts.js
 */

const https = require('https');

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const API_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://ordrly.ai';

async function callSupabaseFunction(query) {
  const postData = JSON.stringify({ query });
  
  const options = {
    hostname: new URL(SUPABASE_URL).hostname,
    port: 443,
    path: '/rest/v1/rpc/process_usage_alerts',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'apikey': SUPABASE_SERVICE_KEY,
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve({ status: res.statusCode, data: JSON.parse(data) });
        } catch (e) {
          resolve({ status: res.statusCode, data });
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function getPendingAlerts() {
  const query = `
    SELECT ua.id, ua.user_id, ua.alert_type, ua.searches_count, p.email, p.name
    FROM usage_alerts ua
    JOIN profiles p ON ua.user_id = p.id
    WHERE ua.email_sent = false 
      AND ua.alert_type = 'usage_warning'
      AND ua.sent_at IS NULL
    ORDER BY ua.created_at ASC
    LIMIT 10
  `;

  const postData = JSON.stringify({ query });
  
  const options = {
    hostname: new URL(SUPABASE_URL).hostname,
    port: 443,
    path: '/rest/v1/rpc/execute_sql',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'apikey': SUPABASE_SERVICE_KEY,
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve({ status: res.statusCode, data: JSON.parse(data) });
        } catch (e) {
          resolve({ status: res.statusCode, data });
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function callUsageAlertAPI(userId) {
  const postData = JSON.stringify({
    user_id: userId,
    force_check: false
  });

  const url = new URL(`${API_URL}/api/usage-alerts`);
  
  const options = {
    hostname: url.hostname,
    port: url.port || 443,
    path: url.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve({ status: res.statusCode, data: JSON.parse(data) });
        } catch (e) {
          resolve({ status: res.statusCode, data });
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function markAlertProcessed(alertId) {
  const query = `UPDATE usage_alerts SET sent_at = NOW(), email_sent = true WHERE id = '${alertId}'`;
  const postData = JSON.stringify({ query });
  
  const options = {
    hostname: new URL(SUPABASE_URL).hostname,
    port: 443,
    path: '/rest/v1/rpc/execute_sql',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'apikey': SUPABASE_SERVICE_KEY,
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => resolve({ status: res.statusCode, data }));
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function processAlertsViaSupabase() {
  const postData = JSON.stringify({});

  const options = {
    hostname: new URL(SUPABASE_URL).hostname,
    port: 443,
    path: '/rest/v1/rpc/process_usage_alerts',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'apikey': SUPABASE_SERVICE_KEY,
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve({ status: res.statusCode, data: JSON.parse(data) });
        } catch (e) {
          resolve({ status: res.statusCode, data });
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

async function main() {
  console.log(`[${new Date().toISOString()}] Starting usage alert processing...`);

  try {
    // Call Supabase function to process alerts
    const result = await processAlertsViaSupabase();
    console.log(`[${new Date().toISOString()}] Supabase response:`, result);

    if (result.status === 200 && result.data && result.data.length > 0) {
      const { processed_count, user_ids } = result.data[0];
      console.log(`[${new Date().toISOString()}] Processed ${processed_count} alerts for users:`, user_ids);

      // Call our API for each user
      for (const userId of user_ids || []) {
        try {
          const apiResult = await callUsageAlertAPI(userId);
          console.log(`[${new Date().toISOString()}] API call for user ${userId}:`, apiResult.status);
        } catch (error) {
          console.error(`[${new Date().toISOString()}] API call failed for user ${userId}:`, error.message);
        }
      }
    } else {
      console.log(`[${new Date().toISOString()}] No alerts to process`);
    }

    console.log(`[${new Date().toISOString()}] Usage alert processing completed successfully`);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error processing usage alerts:`, error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
