#!/usr/bin/env tsx
/**
 * Production Deployment Validation Script
 * Validates the enhanced research system in production environment
 * Part of Phase 5: Production Deployment & Validation
 */

import { createClient } from '@supabase/supabase-js'
import { ResearchTestFramework } from '../src/lib/testing/research-test-framework'
import { ResearchQualityMonitor } from '../src/lib/monitoring/research-quality-monitor'
import { BulletproofResearchService } from '../src/lib/services/bulletproof-research'
import fs from 'fs'
import path from 'path'

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY!
const VALIDATION_OUTPUT_DIR = './production-validation'

interface ValidationConfig {
  runSmokeTests: boolean
  runAccuracyValidation: boolean
  runPerformanceTests: boolean
  runMonitoringValidation: boolean
  targetAccuracy: number
  maxResponseTime: number
  minCacheHitRate: number
}

interface ValidationResult {
  timestamp: string
  environment: 'production' | 'staging'
  overall_status: 'pass' | 'fail'
  smoke_tests: {
    status: 'pass' | 'fail' | 'skipped'
    details: any
  }
  accuracy_validation: {
    status: 'pass' | 'fail' | 'skipped'
    pass_rate: number
    target_rate: number
    details: any
  }
  performance_tests: {
    status: 'pass' | 'fail' | 'skipped'
    avg_response_time: number
    cache_hit_rate: number
    details: any
  }
  monitoring_validation: {
    status: 'pass' | 'fail' | 'skipped'
    alerts_count: number
    system_health: string
    details: any
  }
  recommendations: string[]
}

async function main() {
  console.log('🚀 Production Deployment Validation')
  console.log('=' .repeat(60))

  // Parse configuration
  const config = parseValidationConfig()
  
  // Initialize services
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)
  const testFramework = new ResearchTestFramework(supabase)
  const qualityMonitor = new ResearchQualityMonitor(supabase)
  const researchService = new BulletproofResearchService(supabase)

  // Ensure output directory exists
  if (!fs.existsSync(VALIDATION_OUTPUT_DIR)) {
    fs.mkdirSync(VALIDATION_OUTPUT_DIR, { recursive: true })
  }

  const validationResult: ValidationResult = {
    timestamp: new Date().toISOString(),
    environment: 'production',
    overall_status: 'pass',
    smoke_tests: { status: 'skipped', details: {} },
    accuracy_validation: { status: 'skipped', pass_rate: 0, target_rate: config.targetAccuracy, details: {} },
    performance_tests: { status: 'skipped', avg_response_time: 0, cache_hit_rate: 0, details: {} },
    monitoring_validation: { status: 'skipped', alerts_count: 0, system_health: 'unknown', details: {} },
    recommendations: []
  }

  try {
    // 1. Smoke Tests
    if (config.runSmokeTests) {
      console.log('\n🔥 Running Smoke Tests')
      validationResult.smoke_tests = await runSmokeTests(researchService)
      
      if (validationResult.smoke_tests.status === 'fail') {
        validationResult.overall_status = 'fail'
        console.error('❌ Smoke tests failed - aborting validation')
        await generateValidationReport(validationResult)
        process.exit(1)
      }
    }

    // 2. Accuracy Validation
    if (config.runAccuracyValidation) {
      console.log('\n🎯 Running Accuracy Validation')
      validationResult.accuracy_validation = await runAccuracyValidation(testFramework, config)
      
      if (validationResult.accuracy_validation.status === 'fail') {
        validationResult.overall_status = 'fail'
        validationResult.recommendations.push('Accuracy below target - review failed test cases and enhance research algorithms')
      }
    }

    // 3. Performance Tests
    if (config.runPerformanceTests) {
      console.log('\n⚡ Running Performance Tests')
      validationResult.performance_tests = await runPerformanceTests(qualityMonitor, config)
      
      if (validationResult.performance_tests.status === 'fail') {
        validationResult.overall_status = 'fail'
        validationResult.recommendations.push('Performance issues detected - optimize caching and response times')
      }
    }

    // 4. Monitoring Validation
    if (config.runMonitoringValidation) {
      console.log('\n📊 Running Monitoring Validation')
      validationResult.monitoring_validation = await runMonitoringValidation(qualityMonitor)
      
      if (validationResult.monitoring_validation.status === 'fail') {
        validationResult.overall_status = 'fail'
        validationResult.recommendations.push('Active alerts detected - resolve critical issues before deployment')
      }
    }

    // Generate final report
    await generateValidationReport(validationResult)

    // Display summary
    console.log('\n' + '='.repeat(60))
    console.log('📋 Production Validation Summary')
    console.log(`   Overall Status: ${validationResult.overall_status === 'pass' ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`   Smoke Tests: ${getStatusIcon(validationResult.smoke_tests.status)}`)
    console.log(`   Accuracy: ${getStatusIcon(validationResult.accuracy_validation.status)} (${(validationResult.accuracy_validation.pass_rate * 100).toFixed(1)}%)`)
    console.log(`   Performance: ${getStatusIcon(validationResult.performance_tests.status)}`)
    console.log(`   Monitoring: ${getStatusIcon(validationResult.monitoring_validation.status)}`)

    if (validationResult.recommendations.length > 0) {
      console.log('\n📝 Recommendations:')
      validationResult.recommendations.forEach(rec => console.log(`   • ${rec}`))
    }

    if (validationResult.overall_status === 'pass') {
      console.log('\n🎉 PRODUCTION DEPLOYMENT VALIDATED - SYSTEM READY!')
    } else {
      console.log('\n⚠️  VALIDATION FAILED - RESOLVE ISSUES BEFORE DEPLOYMENT')
      process.exit(1)
    }

  } catch (error) {
    console.error('❌ Validation error:', error)
    validationResult.overall_status = 'fail'
    await generateValidationReport(validationResult)
    process.exit(1)
  }
}

async function runSmokeTests(researchService: BulletproofResearchService): Promise<any> {
  try {
    console.log('   Testing basic research functionality...')
    
    // Test basic research request
    const testResult = await researchService.performResearch({
      query: 'What are the setback requirements for a storage shed?',
      jurisdiction: 'Austin, TX'
    })

    if (!testResult.success) {
      return {
        status: 'fail',
        details: { error: 'Research failed', test: 'basic_research' }
      }
    }

    console.log('   ✅ Basic research functionality working')

    // Test confidence threshold
    if (testResult.confidence < 0.5) {
      return {
        status: 'fail',
        details: { error: 'Confidence too low', confidence: testResult.confidence, test: 'confidence_check' }
      }
    }

    console.log('   ✅ Confidence scoring working')

    // Test source verification
    const hasVerifiableSources = testResult.sources.some(s => s.url && s.url.length > 10)
    if (!hasVerifiableSources) {
      return {
        status: 'fail',
        details: { error: 'No verifiable sources found', sources: testResult.sources.length, test: 'source_verification' }
      }
    }

    console.log('   ✅ Source verification working')

    return {
      status: 'pass',
      details: {
        confidence: testResult.confidence,
        sources_count: testResult.sources.length,
        response_time: testResult.processingTimeMs,
        used_cache: testResult.usedCache
      }
    }

  } catch (error) {
    return {
      status: 'fail',
      details: { error: error instanceof Error ? error.message : 'Unknown error', test: 'smoke_test_exception' }
    }
  }
}

async function runAccuracyValidation(testFramework: ResearchTestFramework, config: ValidationConfig): Promise<any> {
  try {
    console.log('   Running subset of validation tests...')
    
    // Run a representative sample of tests (20% of full suite)
    const categoryResult = await testFramework.runCategoryTests('common_projects')
    
    const passRate = categoryResult.pass_rate
    const targetMet = passRate >= config.targetAccuracy

    console.log(`   Pass Rate: ${(passRate * 100).toFixed(1)}% (Target: ${(config.targetAccuracy * 100).toFixed(1)}%)`)

    return {
      status: targetMet ? 'pass' : 'fail',
      pass_rate: passRate,
      target_rate: config.targetAccuracy,
      details: {
        total_tests: categoryResult.total_tests,
        passed_tests: categoryResult.passed_tests,
        failed_tests: categoryResult.failed_tests,
        average_confidence: categoryResult.average_confidence,
        failed_test_details: categoryResult.failed_test_details
      }
    }

  } catch (error) {
    return {
      status: 'fail',
      pass_rate: 0,
      target_rate: config.targetAccuracy,
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function runPerformanceTests(qualityMonitor: ResearchQualityMonitor, config: ValidationConfig): Promise<any> {
  try {
    console.log('   Collecting performance metrics...')
    
    const metrics = await qualityMonitor.collectQualityMetrics('hour')
    
    const avgResponseTime = metrics.performance_metrics.average_response_time
    const cacheHitRate = metrics.performance_metrics.cache_hit_rate
    
    const responseTimeOk = avgResponseTime <= config.maxResponseTime
    const cacheHitRateOk = cacheHitRate >= config.minCacheHitRate

    console.log(`   Avg Response Time: ${avgResponseTime}ms (Max: ${config.maxResponseTime}ms)`)
    console.log(`   Cache Hit Rate: ${(cacheHitRate * 100).toFixed(1)}% (Min: ${(config.minCacheHitRate * 100).toFixed(1)}%)`)

    return {
      status: responseTimeOk && cacheHitRateOk ? 'pass' : 'fail',
      avg_response_time: avgResponseTime,
      cache_hit_rate: cacheHitRate,
      details: {
        response_time_ok: responseTimeOk,
        cache_hit_rate_ok: cacheHitRateOk,
        error_rate: metrics.performance_metrics.error_rate,
        research_trigger_rate: metrics.performance_metrics.research_trigger_rate
      }
    }

  } catch (error) {
    return {
      status: 'fail',
      avg_response_time: 0,
      cache_hit_rate: 0,
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function runMonitoringValidation(qualityMonitor: ResearchQualityMonitor): Promise<any> {
  try {
    console.log('   Checking system health and alerts...')
    
    const [metrics, alerts] = await Promise.all([
      qualityMonitor.collectQualityMetrics('day'),
      qualityMonitor.getActiveAlerts()
    ])

    const criticalAlerts = alerts.filter(a => a.severity === 'critical').length
    const highAlerts = alerts.filter(a => a.severity === 'high').length
    
    const systemHealthy = criticalAlerts === 0 && highAlerts === 0
    
    console.log(`   Active Alerts: ${alerts.length} (Critical: ${criticalAlerts}, High: ${highAlerts})`)
    console.log(`   System Health: ${systemHealthy ? 'Healthy' : 'Issues Detected'}`)

    return {
      status: systemHealthy ? 'pass' : 'fail',
      alerts_count: alerts.length,
      system_health: systemHealthy ? 'healthy' : 'issues_detected',
      details: {
        critical_alerts: criticalAlerts,
        high_alerts: alerts.filter(a => a.severity === 'high').length,
        medium_alerts: alerts.filter(a => a.severity === 'medium').length,
        low_alerts: alerts.filter(a => a.severity === 'low').length,
        alert_details: alerts.map(a => ({ type: a.alert_type, severity: a.severity, message: a.message }))
      }
    }

  } catch (error) {
    return {
      status: 'fail',
      alerts_count: 0,
      system_health: 'unknown',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

function parseValidationConfig(): ValidationConfig {
  const args = process.argv.slice(2)
  
  const config: ValidationConfig = {
    runSmokeTests: true,
    runAccuracyValidation: true,
    runPerformanceTests: true,
    runMonitoringValidation: true,
    targetAccuracy: 0.99,
    maxResponseTime: 30000,
    minCacheHitRate: 0.80
  }

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--skip-smoke':
        config.runSmokeTests = false
        break
      case '--skip-accuracy':
        config.runAccuracyValidation = false
        break
      case '--skip-performance':
        config.runPerformanceTests = false
        break
      case '--skip-monitoring':
        config.runMonitoringValidation = false
        break
      case '--target-accuracy':
        const accuracy = parseFloat(args[++i])
        if (!isNaN(accuracy) && accuracy > 0 && accuracy <= 1) {
          config.targetAccuracy = accuracy
        }
        break
    }
  }

  return config
}

async function generateValidationReport(result: ValidationResult): Promise<void> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  
  // Generate JSON report
  const jsonReportPath = path.join(VALIDATION_OUTPUT_DIR, `production-validation-${timestamp}.json`)
  fs.writeFileSync(jsonReportPath, JSON.stringify(result, null, 2))
  
  // Generate markdown report
  const markdownReport = generateMarkdownReport(result)
  const markdownReportPath = path.join(VALIDATION_OUTPUT_DIR, `production-validation-${timestamp}.md`)
  fs.writeFileSync(markdownReportPath, markdownReport)
  
  console.log(`\n📄 Validation reports generated:`)
  console.log(`   JSON: ${jsonReportPath}`)
  console.log(`   Markdown: ${markdownReportPath}`)
}

function generateMarkdownReport(result: ValidationResult): string {
  return `# Production Deployment Validation Report

**Timestamp**: ${new Date(result.timestamp).toLocaleString()}  
**Environment**: ${result.environment}  
**Overall Status**: ${result.overall_status === 'pass' ? '✅ PASS' : '❌ FAIL'}

## Test Results

### Smoke Tests
**Status**: ${getStatusIcon(result.smoke_tests.status)}  
**Details**: ${JSON.stringify(result.smoke_tests.details, null, 2)}

### Accuracy Validation
**Status**: ${getStatusIcon(result.accuracy_validation.status)}  
**Pass Rate**: ${(result.accuracy_validation.pass_rate * 100).toFixed(1)}%  
**Target**: ${(result.accuracy_validation.target_rate * 100).toFixed(1)}%  

### Performance Tests
**Status**: ${getStatusIcon(result.performance_tests.status)}  
**Avg Response Time**: ${result.performance_tests.avg_response_time}ms  
**Cache Hit Rate**: ${(result.performance_tests.cache_hit_rate * 100).toFixed(1)}%  

### Monitoring Validation
**Status**: ${getStatusIcon(result.monitoring_validation.status)}  
**Active Alerts**: ${result.monitoring_validation.alerts_count}  
**System Health**: ${result.monitoring_validation.system_health}  

## Recommendations

${result.recommendations.length > 0 
  ? result.recommendations.map(rec => `- ${rec}`).join('\n')
  : 'No recommendations - system performing optimally'
}

## Next Steps

${result.overall_status === 'pass'
  ? '✅ **APPROVED FOR PRODUCTION DEPLOYMENT**\n\n- Deploy enhanced research system\n- Monitor system performance\n- Continue regular validation runs'
  : '❌ **NOT APPROVED FOR DEPLOYMENT**\n\n- Address failed validation items\n- Re-run validation after fixes\n- Ensure all tests pass before deployment'
}
`
}

function getStatusIcon(status: string): string {
  switch (status) {
    case 'pass': return '✅ PASS'
    case 'fail': return '❌ FAIL'
    case 'skipped': return '⏭️ SKIPPED'
    default: return '❓ UNKNOWN'
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error)
}

export { main as runProductionValidation }
