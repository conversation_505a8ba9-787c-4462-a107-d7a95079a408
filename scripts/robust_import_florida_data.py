#!/usr/bin/env python3
"""
Robust Florida property data import with retry logic and error handling
Handles failures gracefully and ensures 100% import success
"""

import pandas as pd
import requests
import json
import time
import logging
from pathlib import Path
import os
from datetime import datetime
import pickle

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://qxiryfbdruydrofclmvz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE"

# Progress tracking
PROGRESS_FILE = "scripts/import_progress.pkl"
FAILED_BATCHES_FILE = "scripts/failed_batches.pkl"

def get_supabase_headers():
    """Get headers for Supabase API requests"""
    return {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

def save_progress(completed_batches, failed_batches):
    """Save import progress to disk"""
    progress = {
        'completed_batches': completed_batches,
        'failed_batches': failed_batches,
        'timestamp': datetime.now()
    }
    with open(PROGRESS_FILE, 'wb') as f:
        pickle.dump(progress, f)

def load_progress():
    """Load import progress from disk"""
    if Path(PROGRESS_FILE).exists():
        with open(PROGRESS_FILE, 'rb') as f:
            return pickle.load(f)
    return {'completed_batches': set(), 'failed_batches': {}}

def batch_insert_with_retry(data_batch, batch_num, total_batches, max_retries=3):
    """Insert a batch with retry logic and detailed error handling"""
    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = get_supabase_headers()
    
    for attempt in range(max_retries):
        try:
            # Convert DataFrame to list of dictionaries
            records = data_batch.to_dict('records')
            
            # Use upsert to handle potential duplicates
            headers_with_upsert = headers.copy()
            headers_with_upsert['Prefer'] = 'resolution=merge-duplicates'
            
            # Increase timeout for larger batches
            timeout = min(120, 30 + len(records) * 0.1)
            
            logger.info(f"🔄 Batch {batch_num}/{total_batches} attempt {attempt + 1} ({len(records)} records)")
            
            response = requests.post(url, headers=headers_with_upsert, json=records, timeout=timeout)
            
            if response.status_code in [200, 201]:
                logger.info(f"✅ Batch {batch_num} imported successfully")
                return True, None
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:500]}"
                logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} failed: {error_msg}")
                
                # If it's a 409 conflict, try smaller batch size
                if response.status_code == 409 and len(records) > 100:
                    logger.info(f"🔄 Retrying batch {batch_num} with smaller chunks due to conflicts")
                    return retry_with_smaller_chunks(data_batch, batch_num, total_batches)
                
                if attempt == max_retries - 1:
                    return False, error_msg
                    
                # Exponential backoff
                time.sleep(2 ** attempt)
                
        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} error: {error_msg}")
            
            if attempt == max_retries - 1:
                return False, error_msg
                
            time.sleep(2 ** attempt)
    
    return False, "Max retries exceeded"

def retry_with_smaller_chunks(data_batch, batch_num, total_batches, chunk_size=100):
    """Retry failed batch with smaller chunks"""
    logger.info(f"🔄 Breaking batch {batch_num} into smaller chunks of {chunk_size}")
    
    total_records = len(data_batch)
    chunks = [data_batch.iloc[i:i + chunk_size] for i in range(0, total_records, chunk_size)]
    
    all_success = True
    for i, chunk in enumerate(chunks):
        chunk_num = f"{batch_num}.{i+1}"
        success, error = batch_insert_with_retry(chunk, chunk_num, total_batches, max_retries=2)
        if not success:
            logger.error(f"❌ Chunk {chunk_num} failed even with smaller size: {error}")
            all_success = False
    
    return all_success, None if all_success else "Some chunks failed"

def import_florida_data_robust():
    """Main function with robust error handling and progress tracking"""
    
    # Load progress
    progress = load_progress()
    completed_batches = progress['completed_batches']
    failed_batches = progress.get('failed_batches', {})
    
    # Load the CSV data
    csv_file = "prop-data/florida_property_data.csv"
    logger.info(f"Loading data from {csv_file}")
    
    if not Path(csv_file).exists():
        logger.error(f"CSV file not found: {csv_file}")
        return False
    
    # Read the CSV
    df = pd.read_csv(csv_file)
    logger.info(f"Loaded {len(df)} records from CSV")
    
    # Clean and prepare data (reuse existing logic)
    from import_florida_data_to_supabase import clean_data_for_import
    clean_df = clean_data_for_import(df)
    
    # Map columns
    supabase_columns = [
        'property_id', 'city', 'state', 'county', 'primary_land_use',
        'secondary_land_use', 'land_use_code', 'acres', 'shape_area',
        'data_source', 'last_updated', 'flu_id', 'gcid', 'autoid'
    ]
    
    column_mapping = {
        'FLU_ID': 'flu_id',
        'GCID': 'gcid', 
        'AUTOID': 'autoid'
    }
    
    clean_df = clean_df.rename(columns=column_mapping)
    clean_df = clean_df[supabase_columns]
    
    # Import in batches
    batch_size = 500  # Smaller batches for better reliability
    total_records = len(clean_df)
    total_batches = (total_records + batch_size - 1) // batch_size
    
    logger.info(f"Starting robust import: {total_records} records in {total_batches} batches")
    logger.info(f"Previously completed: {len(completed_batches)} batches")
    
    successful_batches = len(completed_batches)
    new_failed_batches = {}
    start_time = time.time()
    
    for i in range(0, total_records, batch_size):
        batch_num = (i // batch_size) + 1
        
        # Skip if already completed
        if batch_num in completed_batches:
            continue
            
        batch_data = clean_df.iloc[i:i + batch_size]
        
        logger.info(f"Processing batch {batch_num}/{total_batches} (records {i+1}-{min(i+batch_size, total_records)})")
        
        success, error = batch_insert_with_retry(batch_data, batch_num, total_batches)
        
        if success:
            successful_batches += 1
            completed_batches.add(batch_num)
        else:
            new_failed_batches[batch_num] = {
                'error': error,
                'records': len(batch_data),
                'start_index': i
            }
            logger.error(f"❌ Batch {batch_num} failed permanently: {error}")
        
        # Save progress every 10 batches
        if batch_num % 10 == 0:
            save_progress(completed_batches, {**failed_batches, **new_failed_batches})
            elapsed = time.time() - start_time
            rate = (batch_num - len(progress['completed_batches'])) / elapsed * 60 if elapsed > 0 else 0
            remaining_batches = total_batches - len(completed_batches)
            eta = remaining_batches / rate if rate > 0 else 0
            logger.info(f"Progress: {len(completed_batches)}/{total_batches} batches ({len(completed_batches)/total_batches*100:.1f}%) - ETA: {eta:.1f} minutes")
        
        # Small delay between batches
        time.sleep(0.2)
    
    # Final save
    save_progress(completed_batches, {**failed_batches, **new_failed_batches})
    
    # Final summary
    elapsed_total = time.time() - start_time
    total_failed = len(new_failed_batches)
    
    logger.info(f"\n{'='*60}")
    logger.info(f"ROBUST IMPORT COMPLETE")
    logger.info(f"{'='*60}")
    logger.info(f"Total time: {elapsed_total/60:.1f} minutes")
    logger.info(f"Successful batches: {successful_batches}")
    logger.info(f"Failed batches: {total_failed}")
    logger.info(f"Success rate: {successful_batches/total_batches*100:.1f}%")
    
    if total_failed > 0:
        logger.info(f"\n❌ {total_failed} batches still failed. Check failed_batches.pkl for details.")
        logger.info("You can run this script again to retry failed batches.")
    else:
        logger.info("🎉 All batches imported successfully!")
        # Clean up progress files
        for f in [PROGRESS_FILE, FAILED_BATCHES_FILE]:
            if Path(f).exists():
                Path(f).unlink()
    
    return total_failed == 0

if __name__ == "__main__":
    success = import_florida_data_robust()
    if success:
        logger.info("🎉 Florida property data import completed successfully!")
    else:
        logger.error("❌ Import completed with some failures. Run again to retry.")
