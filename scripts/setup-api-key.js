#!/usr/bin/env node

/**
 * Setup API <NAME_EMAIL> with Pro Status
 * This script creates a new API key for testing the compliance research system
 */

const { createClient } = require('@supabase/supabase-js')
const crypto = require('crypto')

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

/**
 * Generate a new API key
 */
function generateApiKey() {
  const prefix = 'ordrly_'
  const randomPart = Math.random().toString(36).substring(2, 15) + 
                    Math.random().toString(36).substring(2, 15)
  return prefix + randomPart
}

/**
 * Hash API key for secure storage
 */
function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex')
}

async function setupApiKey() {
  try {
    console.log('🔧 Setting up API <NAME_EMAIL>...')

    // 1. Verify user exists and has pro status
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('id, email, subscription_tier, role')
      .eq('email', '<EMAIL>')
      .single()

    if (userError || !user) {
      console.error('❌ User <EMAIL> not found:', userError)
      process.exit(1)
    }

    console.log('✅ User found:', {
      id: user.id,
      email: user.email,
      tier: user.subscription_tier,
      role: user.role
    })

    // 2. Generate new API key
    const apiKey = generateApiKey()
    const keyHash = hashApiKey(apiKey)

    console.log('🔑 Generated new API key:', apiKey)

    // 3. Store API key in database
    const { data: apiKeyRecord, error: keyError } = await supabase
      .from('api_keys')
      .insert({
        user_id: user.id,
        name: 'Testing API Key - Compliance Research',
        key_hash: keyHash,
        is_active: true,
        created_at: new Date().toISOString()
      })
      .select('id')
      .single()

    if (keyError) {
      console.error('❌ Failed to create API key:', keyError)
      process.exit(1)
    }

    console.log('✅ API key created successfully!')
    console.log('📋 API Key Details:')
    console.log('   Key ID:', apiKeyRecord.id)
    console.log('   API Key:', apiKey)
    console.log('   User ID:', user.id)
    console.log('   User Tier:', user.subscription_tier)

    // 4. Test the API key
    console.log('\n🧪 Testing API key...')
    
    const testResponse = await fetch('http://localhost:3000/api/research', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jurisdiction: 'Austin, TX',
        ruleType: 'fence',
        userQuery: 'What are the fence height requirements?'
      })
    })

    if (testResponse.ok) {
      const testResult = await testResponse.json()
      console.log('✅ API key test successful!')
      console.log('   Confidence:', Math.round((testResult.confidence || 0) * 100) + '%')
      console.log('   Sources:', testResult.sources?.length || 0)
    } else {
      console.log('⚠️  API key created but test failed:', testResponse.status)
      const errorText = await testResponse.text()
      console.log('   Error:', errorText)
    }

    // 5. Update environment or provide instructions
    console.log('\n📝 Next Steps:')
    console.log('1. Use this API key for testing:', apiKey)
    console.log('2. The user has pro status with unlimited API calls')
    console.log('3. Update your test scripts to use this key')
    console.log('4. The key is stored securely in the database')

    return {
      apiKey,
      keyId: apiKeyRecord.id,
      userId: user.id,
      userTier: user.subscription_tier
    }

  } catch (error) {
    console.error('❌ Setup failed:', error)
    process.exit(1)
  }
}

// Run the setup
if (require.main === module) {
  setupApiKey()
    .then(() => {
      console.log('\n🎉 API key setup complete!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Setup failed:', error)
      process.exit(1)
    })
}

module.exports = { setupApiKey, generateApiKey, hashApiKey }
