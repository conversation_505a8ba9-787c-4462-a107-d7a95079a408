const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required Supabase environment variables')
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupDatabase() {
  console.log('Setting up Ordrly database...')

  try {
    // Enable PostGIS extension
    console.log('1. Enabling PostGIS extension...')
    await supabase.rpc('exec_sql', {
      sql: 'CREATE EXTENSION IF NOT EXISTS postgis WITH SCHEMA extensions;'
    })
    
    await supabase.rpc('exec_sql', {
      sql: 'GRANT USAGE ON SCHEMA extensions TO anon, authenticated;'
    })
    console.log('✓ PostGIS enabled')

    // Create regions table
    console.log('2. Creating regions table...')
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE public.regions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL,
          level TEXT NOT NULL CHECK (level IN ('federal', 'state', 'county', 'city', 'township')),
          state_code TEXT,
          county_name TEXT,
          fips_code TEXT,
          geometry GEOMETRY(MULTIPOLYGON, 4326) NOT NULL,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    })
    console.log('✓ Regions table created')

    // Create spatial indexes
    console.log('3. Creating spatial indexes...')
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE INDEX idx_regions_geometry ON public.regions USING GIST (geometry);
        CREATE INDEX idx_regions_level ON public.regions (level);
        CREATE INDEX idx_regions_state ON public.regions (state_code);
      `
    })
    console.log('✓ Spatial indexes created')

    // Create other tables
    console.log('4. Creating other tables...')
    await supabase.rpc('exec_sql', {
      sql: `
        -- Ordinances metadata table
        CREATE TABLE public.ordinances_metadata (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          region_id UUID REFERENCES public.regions(id) ON DELETE CASCADE,
          source_name TEXT NOT NULL,
          source_url TEXT,
          document_type TEXT,
          last_scraped TIMESTAMP WITH TIME ZONE,
          is_active BOOLEAN DEFAULT true,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Clauses table for individual ordinance rules
        CREATE TABLE public.clauses (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          ordinance_id UUID REFERENCES public.ordinances_metadata(id) ON DELETE CASCADE,
          section_number TEXT,
          title TEXT,
          content TEXT NOT NULL,
          raw_text TEXT,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Tags for rule classification
        CREATE TABLE public.tags (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT UNIQUE NOT NULL,
          category TEXT,
          description TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Junction table for clause tags
        CREATE TABLE public.clause_tags (
          clause_id UUID REFERENCES public.clauses(id) ON DELETE CASCADE,
          tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
          confidence DECIMAL(3,2) DEFAULT 1.0,
          PRIMARY KEY (clause_id, tag_id)
        );

        -- AI-generated summaries cache
        CREATE TABLE public.compliance_summaries (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          region_id UUID REFERENCES public.regions(id) ON DELETE CASCADE,
          tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
          summary TEXT NOT NULL,
          citations JSONB DEFAULT '[]',
          confidence_score DECIMAL(3,2),
          model_version TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(region_id, tag_id)
        );

        -- Address lookup cache
        CREATE TABLE public.address_cache (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          query_text TEXT NOT NULL,
          formatted_address TEXT NOT NULL,
          latitude DECIMAL(10, 8) NOT NULL,
          longitude DECIMAL(11, 8) NOT NULL,
          county TEXT,
          state TEXT,
          zip_code TEXT,
          source TEXT DEFAULT 'nominatim',
          expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX idx_address_cache_query ON public.address_cache (query_text);
        CREATE INDEX idx_address_cache_expires ON public.address_cache (expires_at);
      `
    })
    console.log('✓ All tables created')

    console.log('Database setup completed successfully!')
  } catch (error) {
    console.error('Error setting up database:', error)
  }
}

setupDatabase()
