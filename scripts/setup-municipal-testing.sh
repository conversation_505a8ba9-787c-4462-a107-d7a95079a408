#!/bin/bash

# 🧪 MUNICIPAL API TESTING SETUP SCRIPT
# 
# This script helps set up the testing environment for municipal API integration

echo "🧪 MUNICIPAL API INTEGRATION TESTING SETUP"
echo "==========================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Run this script from the Ordrly project root directory"
    exit 1
fi

# Check if reference directory exists
if [ ! -d "reference/municipal-research-api" ]; then
    echo "❌ Error: Municipal research API not found in reference/ directory"
    echo "   Make sure you've cloned the municipal-research-api into reference/"
    exit 1
fi

echo "✅ Found municipal research API in reference/"

# Check environment variables
echo ""
echo "🔧 CHECKING ENVIRONMENT VARIABLES"
echo "================================="

if [ -z "$MUNICIPAL_API_KEY" ]; then
    echo "⚠️  MUNICIPAL_API_KEY not set"
    echo "   Add to your .env file: MUNICIPAL_API_KEY=your_api_key_here"
else
    echo "✅ MUNICIPAL_API_KEY is set"
fi

if [ -z "$MUNICIPAL_API_URL" ]; then
    echo "⚠️  MUNICIPAL_API_URL not set, using default: http://localhost:3001"
    export MUNICIPAL_API_URL="http://localhost:3001"
else
    echo "✅ MUNICIPAL_API_URL is set: $MUNICIPAL_API_URL"
fi

# Check if municipal API is running
echo ""
echo "🌐 CHECKING MUNICIPAL API STATUS"
echo "================================"

if curl -s "$MUNICIPAL_API_URL/health" > /dev/null 2>&1; then
    echo "✅ Municipal API is running at $MUNICIPAL_API_URL"
    
    # Get health status
    HEALTH_STATUS=$(curl -s "$MUNICIPAL_API_URL/health" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    if [ "$HEALTH_STATUS" = "healthy" ]; then
        echo "✅ Municipal API is healthy"
    else
        echo "⚠️  Municipal API status: $HEALTH_STATUS"
    fi
else
    echo "❌ Municipal API is not running at $MUNICIPAL_API_URL"
    echo ""
    echo "🚀 TO START MUNICIPAL API:"
    echo "========================="
    echo "1. cd reference/municipal-research-api"
    echo "2. npm install"
    echo "3. cp .env.example .env"
    echo "4. Edit .env with your API keys"
    echo "5. npm run dev"
    echo ""
    echo "Then run this script again to continue testing."
    exit 1
fi

# Check Ordrly dependencies
echo ""
echo "📦 CHECKING DEPENDENCIES"
echo "========================"

if [ ! -d "node_modules" ]; then
    echo "⚠️  Node modules not found, installing..."
    npm install
else
    echo "✅ Node modules found"
fi

# Run the integration tests
echo ""
echo "🧪 RUNNING INTEGRATION TESTS"
echo "============================"

npm run test:municipal

echo ""
echo "🎯 NEXT STEPS"
echo "============="
echo "1. Review test results above"
echo "2. If all tests pass, you're ready to proceed with Chat UI integration"
echo "3. If tests fail, check the troubleshooting section in TESTING_GUIDE.md"
echo ""
echo "📚 For detailed testing instructions, see: TESTING_GUIDE.md"
