#!/usr/bin/env python3
"""
Import Florida property data using SQL COPY command for maximum speed and reliability
This is the fastest way to import large datasets into Supabase
"""

import pandas as pd
import requests
import logging
from pathlib import Path
import tempfile
import os
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = "https://qxiryfbdruydrofclmvz.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF4aXJ5ZmJkcnV5ZHJvZmNsbXZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODA0ODM3MywiZXhwIjoyMDYzNjI0MzczfQ._FiVQWh2COEX3bSbOF_M1-533m32plmx2tmi2a5QBNE"

def prepare_csv_for_copy():
    """Prepare the CSV file for SQL COPY command"""
    
    # Load and clean the data
    csv_file = "prop-data/florida_property_data.csv"
    logger.info(f"Loading data from {csv_file}")
    
    df = pd.read_csv(csv_file)
    logger.info(f"Loaded {len(df)} records from CSV")
    
    # Clean and prepare data (reuse existing logic)
    from import_florida_data_to_supabase import clean_data_for_import
    clean_df = clean_data_for_import(df)
    
    # Map columns to match Supabase table
    supabase_columns = [
        'property_id', 'city', 'state', 'county', 'primary_land_use',
        'secondary_land_use', 'land_use_code', 'acres', 'shape_area',
        'data_source', 'last_updated', 'flu_id', 'gcid', 'autoid'
    ]
    
    column_mapping = {
        'FLU_ID': 'flu_id',
        'GCID': 'gcid',
        'AUTOID': 'autoid'
    }
    
    clean_df = clean_df.rename(columns=column_mapping)
    clean_df = clean_df[supabase_columns]
    
    # Create a temporary CSV file optimized for COPY
    temp_csv = "prop-data/florida_property_data_for_copy.csv"
    
    # Ensure all text fields are properly escaped and quoted
    clean_df.to_csv(temp_csv, index=False, quoting=1, escapechar='\\')
    
    logger.info(f"Prepared {len(clean_df)} records for SQL COPY in {temp_csv}")
    return temp_csv, len(clean_df)

def clear_existing_data():
    """Clear existing florida_properties data using Supabase MCP"""
    logger.info("Clearing existing florida_properties data...")

    # Use Supabase MCP to execute DELETE query
    try:
        from supabase import create_client
        supabase = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)

        # Delete all records
        result = supabase.table('florida_properties').delete().neq('property_id', '').execute()

        logger.info("✅ Cleared existing data")
        return True
    except Exception as e:
        logger.error(f"Failed to clear table: {e}")
        # Try alternative approach with manual batching
        return clear_data_in_batches()

def clear_data_in_batches():
    """Clear data in batches as fallback"""
    logger.info("Attempting to clear data in batches...")

    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json'
    }

    # Get all property_ids in batches and delete them
    offset = 0
    limit = 1000
    total_deleted = 0

    while True:
        # Get a batch of IDs
        params = {
            'select': 'property_id',
            'limit': limit,
            'offset': offset
        }

        response = requests.get(url, headers=headers, params=params)

        if response.status_code != 200:
            logger.error(f"Failed to fetch records for deletion: {response.status_code}")
            return False

        data = response.json()

        if not data:
            break

        # Delete this batch
        property_ids = [record['property_id'] for record in data]

        # Use filter to delete specific records
        delete_url = f"{url}?property_id=in.({','.join(f'"{id}"' for id in property_ids)})"
        delete_response = requests.delete(delete_url, headers=headers)

        if delete_response.status_code in [200, 204]:
            total_deleted += len(property_ids)
            logger.info(f"Deleted batch: {len(property_ids)} records (total: {total_deleted})")
        else:
            logger.error(f"Failed to delete batch: {delete_response.status_code}")
            return False

        if len(data) < limit:
            break

        offset += limit
        time.sleep(0.1)

    logger.info(f"✅ Cleared {total_deleted} existing records")
    return True

def bulk_insert_optimized(data_df, batch_size=2000):
    """Optimized bulk insert with large batches and proper error handling"""

    url = f"{SUPABASE_URL}/rest/v1/florida_properties"
    headers = {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'resolution=merge-duplicates,return=minimal'
    }

    total_records = len(data_df)
    total_batches = (total_records + batch_size - 1) // batch_size

    logger.info(f"Starting optimized bulk import: {total_records} records in {total_batches} batches of {batch_size}")

    successful_records = 0
    failed_batches = []

    import time
    start_time = time.time()

    for i in range(0, total_records, batch_size):
        batch_num = (i // batch_size) + 1
        batch_data = data_df.iloc[i:i + batch_size]

        logger.info(f"Processing batch {batch_num}/{total_batches} (records {i+1}-{min(i+batch_size, total_records)})")

        # Convert to records
        records = batch_data.to_dict('records')

        # Try with exponential backoff
        for attempt in range(3):
            try:
                response = requests.post(
                    url,
                    headers=headers,
                    json=records,
                    timeout=120  # 2 minute timeout for large batches
                )

                if response.status_code in [200, 201]:
                    successful_records += len(records)
                    logger.info(f"✅ Batch {batch_num} imported successfully ({len(records)} records)")
                    break
                else:
                    if attempt == 2:  # Last attempt
                        logger.error(f"❌ Batch {batch_num} failed: {response.status_code} - {response.text[:200]}")
                        failed_batches.append({
                            'batch_num': batch_num,
                            'start_index': i,
                            'size': len(records),
                            'error': f"{response.status_code}: {response.text[:200]}"
                        })
                    else:
                        logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} failed, retrying...")
                        time.sleep(2 ** attempt)  # Exponential backoff

            except Exception as e:
                if attempt == 2:  # Last attempt
                    logger.error(f"❌ Batch {batch_num} exception: {e}")
                    failed_batches.append({
                        'batch_num': batch_num,
                        'start_index': i,
                        'size': len(records),
                        'error': str(e)
                    })
                else:
                    logger.warning(f"⚠️ Batch {batch_num} attempt {attempt + 1} exception, retrying...")
                    time.sleep(2 ** attempt)

        # Progress update every 10 batches
        if batch_num % 10 == 0:
            elapsed = time.time() - start_time
            rate = batch_num / elapsed * 60 if elapsed > 0 else 0
            eta = (total_batches - batch_num) / rate if rate > 0 else 0
            logger.info(f"Progress: {batch_num}/{total_batches} batches ({batch_num/total_batches*100:.1f}%) - ETA: {eta:.1f} minutes")

        # Small delay to be nice to the API
        time.sleep(0.1)

    return successful_records, failed_batches

def import_via_optimized_bulk():
    """Import using optimized bulk inserts with upsert - most reliable method for Supabase"""

    logger.info("Starting optimized bulk import with upsert (no need to clear existing data)")

    # Prepare data
    csv_file, record_count = prepare_csv_for_copy()

    # Load the prepared data
    logger.info(f"Loading prepared data from {csv_file}")
    df = pd.read_csv(csv_file)

    logger.info(f"Starting optimized bulk import of {len(df)} records...")

    # Import with optimized batching using upsert
    successful_records, failed_batches = bulk_insert_optimized(df, batch_size=1500)

    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"OPTIMIZED BULK IMPORT COMPLETE")
    logger.info(f"{'='*60}")
    logger.info(f"Total records: {record_count}")
    logger.info(f"Successfully imported: {successful_records}")
    logger.info(f"Failed batches: {len(failed_batches)}")
    logger.info(f"Success rate: {successful_records/record_count*100:.1f}%")

    if failed_batches:
        logger.info(f"\n❌ Failed batches:")
        for batch in failed_batches:
            logger.info(f"  Batch {batch['batch_num']}: {batch['size']} records - {batch['error']}")

    return len(failed_batches) == 0


if __name__ == "__main__":
    success = import_via_optimized_bulk()
    if success:
        logger.info("🎉 Florida property data import completed successfully!")
    else:
        logger.error("❌ Import failed. Check logs for details.")
