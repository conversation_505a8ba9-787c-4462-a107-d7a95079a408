#!/usr/bin/env node

/**
 * Test script to verify account page functionality
 * This script tests the account page endpoints and data consistency
 */

require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testAccountPageData() {
  console.log('🧪 Testing Account Page Data Consistency...\n')

  try {
    // Test 1: Check profiles table structure
    console.log('1. Checking profiles table structure...')
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, subscription_tier, pulls_this_month, searches_used, extra_credits')
      .limit(5)

    if (profilesError) {
      console.error('❌ Profiles query failed:', profilesError)
      return false
    }

    console.log('✅ Profiles table accessible')
    
    // Test 2: Check for field consistency
    console.log('\n2. Checking field consistency...')
    let inconsistencies = 0
    
    for (const profile of profiles) {
      if (profile.pulls_this_month !== null && profile.searches_used !== null) {
        if (profile.pulls_this_month !== profile.searches_used) {
          console.warn(`⚠️  Field mismatch for user ${profile.email}: pulls_this_month=${profile.pulls_this_month}, searches_used=${profile.searches_used}`)
          inconsistencies++
        }
      }
    }

    if (inconsistencies === 0) {
      console.log('✅ No field inconsistencies found')
    } else {
      console.log(`⚠️  Found ${inconsistencies} field inconsistencies`)
    }

    // Test 3: Test billing API endpoint
    console.log('\n3. Testing billing API endpoint...')
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/billing/overview`, {
        headers: {
          'Authorization': `Bearer ${supabaseServiceKey}`
        }
      })
      
      if (response.status === 401) {
        console.log('✅ Billing API properly requires authentication')
      } else {
        console.log(`ℹ️  Billing API response status: ${response.status}`)
      }
    } catch (error) {
      console.log('ℹ️  Billing API test skipped (server not running)')
    }

    // Test 4: Check for missing profiles
    console.log('\n4. Checking for users without profiles...')
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()
    
    if (authError) {
      console.error('❌ Failed to list auth users:', authError)
      return false
    }

    const profileIds = new Set(profiles.map(p => p.id))
    const missingProfiles = authUsers.users.filter(user => !profileIds.has(user.id))

    if (missingProfiles.length > 0) {
      console.warn(`⚠️  Found ${missingProfiles.length} users without profiles`)
      for (const user of missingProfiles.slice(0, 3)) {
        console.log(`   - ${user.email} (${user.id})`)
      }
    } else {
      console.log('✅ All auth users have profiles')
    }

    console.log('\n🎉 Account page data consistency test completed!')
    return true

  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}

async function fixFieldInconsistencies() {
  console.log('\n🔧 Fixing field inconsistencies...')
  
  try {
    // Update profiles to ensure pulls_this_month and searches_used are consistent
    const { error } = await supabase
      .from('profiles')
      .update({
        searches_used: supabase.raw('COALESCE(pulls_this_month, searches_used, 0)'),
        pulls_this_month: supabase.raw('COALESCE(pulls_this_month, searches_used, 0)')
      })
      .neq('id', '********-0000-0000-0000-************') // Update all real profiles

    if (error) {
      console.error('❌ Failed to fix inconsistencies:', error)
      return false
    }

    console.log('✅ Field inconsistencies fixed')
    return true
  } catch (error) {
    console.error('❌ Fix failed:', error)
    return false
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)
  
  if (args.includes('--fix')) {
    await fixFieldInconsistencies()
  }
  
  await testAccountPageData()
}

main().catch(console.error)
