#!/usr/bin/env node

/**
 * Test script to verify chat message counting works correctly
 */

require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testMessageCounting() {
  console.log('🧪 Testing Chat Message Counting...\n')

  try {
    // Test 1: Get a sample user with chat conversations
    console.log('1. Finding users with chat conversations...')
    const { data: conversations, error: convError } = await supabase
      .from('chat_conversations')
      .select('user_id, id')
      .limit(5)

    if (convError) {
      console.error('❌ Failed to get conversations:', convError)
      return false
    }

    if (!conversations || conversations.length === 0) {
      console.log('ℹ️  No chat conversations found in database')
      return true
    }

    console.log(`✅ Found ${conversations.length} conversations`)

    // Test 2: Count messages for each user
    console.log('\n2. Counting messages for users...')
    const userMessageCounts = new Map()

    for (const conv of conversations) {
      const userId = conv.user_id
      
      if (!userMessageCounts.has(userId)) {
        // Get current month's messages for this user
        const currentMonth = new Date()
        const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
        
        const { count: messageCount, error: messageError } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('role', 'user')
          .in('conversation_id', 
            supabase
              .from('chat_conversations')
              .select('id')
              .eq('user_id', userId)
          )
          .gte('created_at', firstDayOfMonth.toISOString())

        if (messageError) {
          console.warn(`⚠️  Failed to count messages for user ${userId}:`, messageError)
          userMessageCounts.set(userId, 0)
        } else {
          userMessageCounts.set(userId, messageCount || 0)
        }
      }
    }

    // Test 3: Display results
    console.log('\n3. Message count results:')
    for (const [userId, count] of userMessageCounts) {
      // Get user email for display
      const { data: profile } = await supabase
        .from('profiles')
        .select('email, subscription_tier')
        .eq('id', userId)
        .single()

      const email = profile?.email || 'unknown'
      const tier = profile?.subscription_tier || 'unknown'
      
      console.log(`   📧 ${email} (${tier}): ${count} messages this month`)
    }

    // Test 4: Verify the query structure matches account page
    console.log('\n4. Testing account page query structure...')
    const testUserId = Array.from(userMessageCounts.keys())[0]
    
    if (testUserId) {
      const currentMonth = new Date()
      const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
      
      const { count: directCount, error: directError } = await supabase
        .from('chat_messages')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'user')
        .in('conversation_id', 
          supabase
            .from('chat_conversations')
            .select('id')
            .eq('user_id', testUserId)
        )
        .gte('created_at', firstDayOfMonth.toISOString())

      if (directError) {
        console.error('❌ Direct query failed:', directError)
        return false
      }

      const expectedCount = userMessageCounts.get(testUserId)
      if (directCount === expectedCount) {
        console.log('✅ Query structure matches expected results')
      } else {
        console.warn(`⚠️  Query mismatch: expected ${expectedCount}, got ${directCount}`)
      }
    }

    console.log('\n🎉 Message counting test completed!')
    return true

  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}

// Main execution
testMessageCounting().catch(console.error)
