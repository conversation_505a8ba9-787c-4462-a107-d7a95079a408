#!/usr/bin/env tsx

/**
 * 🧪 MUNICIPAL API INTEGRATION TESTING
 * 
 * Comprehensive test suite for the municipal research API integration:
 * 1. Test municipal API directly (port 3001)
 * 2. Test our HTTP client
 * 3. Test our adapter layer
 * 4. Test the full /api/research endpoint
 * 5. Test auth/middleware flow
 */

import { municipalResearchClient } from '../src/lib/services/municipal-research-client'
import { ordrlyMunicipalAdapter } from '../src/lib/services/ordrly-municipal-adapter'

interface TestResult {
  name: string
  success: boolean
  duration: number
  error?: string
  data?: any
}

class MunicipalIntegrationTester {
  private results: TestResult[] = []
  private municipalApiUrl = process.env.MUNICIPAL_API_URL || 'http://localhost:3001'
  private municipalApiKey = process.env.MUNICIPAL_API_KEY || 'test-api-key'

  async runAllTests() {
    console.log('🧪 MUNICIPAL API INTEGRATION TESTING')
    console.log('=====================================')
    console.log(`Municipal API URL: ${this.municipalApiUrl}`)
    console.log(`Municipal API Key: ${this.municipalApiKey.substring(0, 10)}...`)
    console.log('')

    // Phase 1: Basic connectivity
    await this.testMunicipalApiHealth()
    
    // Phase 2: Direct API testing
    await this.testMunicipalApiDirect()
    
    // Phase 3: Client layer testing
    await this.testMunicipalClient()
    
    // Phase 4: Adapter layer testing
    await this.testOrdrlyAdapter()
    
    // Phase 5: Full integration testing
    await this.testFullIntegration()

    // Print results
    this.printResults()
  }

  private async runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
    const startTime = Date.now()
    console.log(`🔍 Testing: ${name}`)
    
    try {
      const data = await testFn()
      const duration = Date.now() - startTime
      const result: TestResult = { name, success: true, duration, data }
      this.results.push(result)
      console.log(`✅ ${name} - ${duration}ms`)
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const result: TestResult = { 
        name, 
        success: false, 
        duration, 
        error: error instanceof Error ? error.message : String(error) 
      }
      this.results.push(result)
      console.log(`❌ ${name} - ${duration}ms - ${result.error}`)
      return result
    }
  }

  private async testMunicipalApiHealth() {
    await this.runTest('Municipal API Health Check', async () => {
      const response = await fetch(`${this.municipalApiUrl}/health`)
      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`)
      }
      const data = await response.json()
      if (data.status !== 'healthy') {
        throw new Error(`API not healthy: ${data.status}`)
      }
      return data
    })
  }

  private async testMunicipalApiDirect() {
    await this.runTest('Municipal API Direct Research Call', async () => {
      const response = await fetch(`${this.municipalApiUrl}/api/v1/research`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.municipalApiKey
        },
        body: JSON.stringify({
          address: '123 Main St, Georgetown, MI',
          query: 'What is the maximum height for a fence?'
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Direct API call failed: ${response.status} - ${errorText}`)
      }

      const data = await response.json()
      if (!data.success) {
        throw new Error(`API returned error: ${data.error}`)
      }

      // Validate response structure
      if (!data.data || !data.data.answer || !data.data.jurisdiction) {
        throw new Error('Invalid response structure')
      }

      return {
        jurisdiction: data.data.jurisdiction,
        confidence: data.data.confidence,
        sourcesCount: data.data.sources?.length || 0,
        cached: data.data.cached,
        processingTime: data.data.processingTimeMs
      }
    })
  }

  private async testMunicipalClient() {
    await this.runTest('Municipal Research Client', async () => {
      const client = municipalResearchClient
      
      const result = await client.research({
        address: '456 Oak Ave, Grand Rapids, MI',
        query: 'What are the setback requirements for a deck?'
      })

      if (!result.success) {
        throw new Error('Client call failed')
      }

      return {
        jurisdiction: result.data.jurisdiction,
        confidence: result.data.confidence,
        sourcesCount: result.data.sources?.length || 0,
        cached: result.data.cached
      }
    })
  }

  private async testOrdrlyAdapter() {
    await this.runTest('Ordrly Municipal Adapter', async () => {
      const result = await ordrlyMunicipalAdapter.performResearch({
        query: 'What permits are needed for a shed?',
        address: '789 Pine St, Hudsonville, MI'
      })

      if (!result.success) {
        throw new Error('Adapter call failed')
      }

      // Validate Ordrly format
      if (!result.answer || !result.jurisdiction || !result.sources) {
        throw new Error('Invalid Ordrly response format')
      }

      return {
        jurisdiction: result.jurisdiction,
        confidence: result.confidence,
        sourcesCount: result.sources.length,
        cached: result.cached,
        hasVerifiedSources: result.sources.some(s => s.verified)
      }
    })
  }

  private async testFullIntegration() {
    await this.runTest('Full /api/research Integration', async () => {
      // This would test the actual /api/research endpoint
      // For now, we'll simulate it since we need the server running
      console.log('⚠️  Full integration test requires Ordrly server running')
      console.log('   Run: npm run dev')
      console.log('   Then test: POST http://localhost:3000/api/research')
      
      return { 
        note: 'Manual testing required - server must be running',
        testEndpoint: 'POST http://localhost:3000/api/research',
        testPayload: {
          query: 'What is the maximum fence height?',
          address: '123 Test St, Georgetown, MI'
        }
      }
    })
  }

  private printResults() {
    console.log('\n📊 TEST RESULTS SUMMARY')
    console.log('========================')
    
    const passed = this.results.filter(r => r.success).length
    const failed = this.results.filter(r => !r.success).length
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0)

    console.log(`✅ Passed: ${passed}`)
    console.log(`❌ Failed: ${failed}`)
    console.log(`⏱️  Total Time: ${totalTime}ms`)
    console.log('')

    if (failed > 0) {
      console.log('❌ FAILED TESTS:')
      this.results
        .filter(r => !r.success)
        .forEach(r => console.log(`   - ${r.name}: ${r.error}`))
      console.log('')
    }

    if (passed === this.results.length) {
      console.log('🎉 ALL TESTS PASSED!')
      console.log('')
      console.log('✅ Municipal API integration is working correctly')
      console.log('✅ Ready to proceed with Chat UI integration')
    } else {
      console.log('🚨 SOME TESTS FAILED!')
      console.log('')
      console.log('❌ Fix the failing tests before proceeding')
      console.log('❌ Check municipal API is running on port 3001')
      console.log('❌ Verify API key configuration')
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new MunicipalIntegrationTester()
  tester.runAllTests().catch(console.error)
}

export { MunicipalIntegrationTester }
