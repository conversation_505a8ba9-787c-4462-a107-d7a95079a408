#!/usr/bin/env node

/**
 * Test script to verify sources panel functionality
 * Tests the complete flow: Chat → Research API → Sources extraction
 */

const axios = require('axios');

async function testSourcesFlow() {
  console.log('🧪 Testing Sources Panel Flow...\n');

  try {
    // Step 1: Create a test conversation
    console.log('1️⃣ Creating test conversation...');
    const conversationResponse = await axios.post('http://localhost:3000/api/chat/conversations', {
      address: '123 Main St, Mobile, AL 36602'
    });

    if (!conversationResponse.data.success) {
      throw new Error('Failed to create conversation');
    }

    const conversationId = conversationResponse.data.conversation.id;
    console.log(`✅ Created conversation: ${conversationId}`);

    // Step 2: Send a test message
    console.log('\n2️⃣ Sending test message...');
    const messageResponse = await axios.post('http://localhost:3000/api/chat/messages', {
      conversation_id: conversationId,
      content: 'What are the setback requirements for residential buildings?'
    });

    if (!messageResponse.data.success) {
      throw new Error('Failed to send message');
    }

    console.log('✅ Message sent successfully');

    // Step 3: Check the AI response for sources
    const aiMessage = messageResponse.data.aiMessage;
    console.log('\n3️⃣ Analyzing AI response...');
    console.log('📄 AI Response:', aiMessage.content.substring(0, 200) + '...');
    
    if (aiMessage.metadata) {
      console.log('\n📊 Metadata Analysis:');
      console.log('- Has sources:', !!aiMessage.metadata.sources);
      console.log('- Sources count:', aiMessage.metadata.sources?.length || 0);
      console.log('- Has citations:', !!aiMessage.metadata.citations);
      console.log('- Citations count:', aiMessage.metadata.citations?.length || 0);
      
      if (aiMessage.metadata.sources && aiMessage.metadata.sources.length > 0) {
        console.log('\n📋 Sources found:');
        aiMessage.metadata.sources.forEach((source, index) => {
          console.log(`  ${index + 1}. ${source.title}`);
          console.log(`     Section: ${source.section || 'N/A'}`);
          console.log(`     URL: ${source.url || 'N/A'}`);
          console.log(`     Verified: ${source.verified || false}`);
          console.log('');
        });
      } else {
        console.log('❌ No sources found in metadata');
      }
    } else {
      console.log('❌ No metadata found in AI response');
    }

    // Step 4: Fetch messages to see how they're stored
    console.log('\n4️⃣ Fetching stored messages...');
    const messagesResponse = await axios.get(`http://localhost:3000/api/chat/messages?conversation_id=${conversationId}`);
    
    if (messagesResponse.data.success) {
      const assistantMessages = messagesResponse.data.messages.filter(msg => msg.role === 'assistant');
      console.log(`✅ Found ${assistantMessages.length} assistant messages`);
      
      assistantMessages.forEach((msg, index) => {
        console.log(`\n📨 Assistant Message ${index + 1}:`);
        console.log('- Has metadata:', !!msg.metadata);
        console.log('- Has sources in metadata:', !!msg.metadata?.sources);
        console.log('- Sources count:', msg.metadata?.sources?.length || 0);
        
        if (msg.metadata?.sources) {
          console.log('- Source titles:', msg.metadata.sources.map(s => s.title));
        }
      });
    }

    console.log('\n✅ Sources panel test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Run the test
testSourcesFlow();
