#!/usr/bin/env node

/**
 * Environment Variable Verification Script for Epic 6
 * Run this before deployment to ensure all required variables are set
 */

const fs = require('fs')
const path = require('path')

// Load environment variables from .env.local
function loadEnvFile(filename) {
  const envPath = path.join(process.cwd(), filename)
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    const lines = envContent.split('\n')

    lines.forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=')
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '') // Remove quotes
          process.env[key] = value
        }
      }
    })

    console.log(`📁 Loaded environment variables from ${filename}`)
    return true
  }
  return false
}

// Try to load environment files in order of preference
const envFiles = ['.env.local', '.env']
let envFileLoaded = false

for (const envFile of envFiles) {
  if (loadEnvFile(envFile)) {
    envFileLoaded = true
    break
  }
}

if (!envFileLoaded) {
  console.log('⚠️  No .env.local or .env file found. Checking system environment variables only.\n')
} else {
  console.log('')
}

const requiredVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'OPENAI_API_KEY',
  'GEOAPIFY_API_KEY',
  'STRIPE_SECRET_KEY',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
]

const optionalVars = [
  'OPENAI_MODEL',
  'NEXT_PUBLIC_EPIC6_ENABLED',
  'NEXT_PUBLIC_CHAT_ENABLED',
  'NEXT_PUBLIC_RED_FLAGS_ENABLED',
  'NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED',
  'NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED',
  'STRIPE_WEBHOOK_SECRET',
  'NEXT_PUBLIC_GOOGLE_MAPS_API_KEY',
  'GOOGLE_SEARCH_API_KEY',
  'GOOGLE_GEOCODING_API_KEY'
]

console.log('🔍 Epic 6 Environment Variable Check\n')

let hasErrors = false

// Check required variables
console.log('📋 Required Variables:')
requiredVars.forEach(varName => {
  const value = process.env[varName]
  if (value) {
    console.log(`✅ ${varName}: Set (${value.substring(0, 10)}...)`)
  } else {
    console.log(`❌ ${varName}: MISSING`)
    hasErrors = true
  }
})

console.log('\n📋 Optional Variables:')
optionalVars.forEach(varName => {
  const value = process.env[varName]
  if (value) {
    console.log(`✅ ${varName}: ${value}`)
  } else {
    console.log(`⚠️  ${varName}: Not set (using default)`)
  }
})

console.log('\n📊 Summary:')
if (hasErrors) {
  console.log('❌ Missing required environment variables. Please set them before deployment.')
  process.exit(1)
} else {
  console.log('✅ All required environment variables are set!')
  console.log('🚀 Ready for deployment!')
}
