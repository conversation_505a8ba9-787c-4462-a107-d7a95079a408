#!/usr/bin/env node

/**
 * Production Environment Verification Script
 *
 * This script verifies that all required environment variables are set correctly
 * for production deployment and identifies potential authentication issues.
 */

// Load environment variables from .env.local if not in production
if (process.env.NODE_ENV !== 'production') {
  require('dotenv').config({ path: '.env.local' })
}

const requiredEnvVars = {
  // Critical Authentication URLs
  'NEXTAUTH_URL': {
    required: true,
    production: 'https://www.ordrly.ai',
    description: 'NextAuth base URL - MUST match production domain'
  },
  'NEXT_PUBLIC_BASE_URL': {
    required: true,
    production: 'https://www.ordrly.ai',
    description: 'Application base URL'
  },
  'NEXT_PUBLIC_SITE_URL': {
    required: true,
    production: 'https://www.ordrly.ai',
    description: 'Site URL for redirects and callbacks'
  },

  // Supabase Configuration
  'NEXT_PUBLIC_SUPABASE_URL': {
    required: true,
    production: 'https://qxiryfbdruydrofclmvz.supabase.co',
    description: 'Supabase project URL'
  },
  'NEXT_PUBLIC_SUPABASE_ANON_KEY': {
    required: true,
    startsWith: 'eyJ',
    description: 'Supabase anonymous key'
  },
  'SUPABASE_SERVICE_ROLE_KEY': {
    required: true,
    startsWith: 'eyJ',
    description: 'Supabase service role key'
  },

  // Municipal API
  'MUNICIPAL_API_KEY': {
    required: true,
    startsWith: 'ordrly_',
    description: 'Municipal research API key'
  },
  'MUNICIPAL_API_URL': {
    required: true,
    production: 'https://api.ordrly.ai',
    description: 'Municipal research API URL'
  },

  // Stripe (Production)
  'STRIPE_SECRET_KEY': {
    required: true,
    productionStartsWith: 'sk_live_',
    testStartsWith: 'sk_test_',
    description: 'Stripe secret key (must be live in production)'
  },
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY': {
    required: true,
    productionStartsWith: 'pk_live_',
    testStartsWith: 'pk_test_',
    description: 'Stripe publishable key (must be live in production)'
  },

  // Environment
  'NODE_ENV': {
    required: true,
    production: 'production',
    description: 'Node environment'
  }
}

function verifyEnvironment() {
  console.log('🔍 Verifying Production Environment Variables...\n')
  
  const issues = []
  const warnings = []
  const isProduction = process.env.NODE_ENV === 'production'
  
  // Check each required variable
  Object.entries(requiredEnvVars).forEach(([key, config]) => {
    const value = process.env[key]
    
    if (!value) {
      issues.push(`❌ MISSING: ${key} - ${config.description}`)
      return
    }
    
    // Check production-specific values
    if (config.production && value !== config.production) {
      if (isProduction) {
        issues.push(`❌ INCORRECT: ${key} should be "${config.production}" in production, got "${value}"`)
      } else {
        warnings.push(`⚠️  DEV: ${key} is "${value}" (production should be "${config.production}")`)
      }
    }
    
    // Check prefixes
    if (config.startsWith && !value.startsWith(config.startsWith)) {
      issues.push(`❌ INVALID: ${key} should start with "${config.startsWith}"`)
    }
    
    // Check production vs test keys
    if (isProduction && config.productionStartsWith && !value.startsWith(config.productionStartsWith)) {
      issues.push(`❌ TEST KEY IN PRODUCTION: ${key} should start with "${config.productionStartsWith}" in production`)
    }
    
    if (!isProduction && config.testStartsWith && !value.startsWith(config.testStartsWith)) {
      warnings.push(`⚠️  LIVE KEY IN DEV: ${key} starts with live key prefix (should be test in development)`)
    }
    
    console.log(`✅ ${key}: ${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`)
  })
  
  console.log('\n📊 Verification Results:')
  console.log(`Environment: ${process.env.NODE_ENV || 'undefined'}`)
  console.log(`Issues found: ${issues.length}`)
  console.log(`Warnings: ${warnings.length}`)
  
  if (issues.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES:')
    issues.forEach(issue => console.log(issue))
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS:')
    warnings.forEach(warning => console.log(warning))
  }
  
  if (issues.length === 0) {
    console.log('\n🎉 All required environment variables are properly configured!')
  } else {
    console.log('\n💡 Fix the issues above to resolve authentication problems.')
    process.exit(1)
  }
}

// Run verification
verifyEnvironment()
