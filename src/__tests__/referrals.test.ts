/**
 * @jest-environment node
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { createClient } from '@supabase/supabase-js'

// Mock environment variables for testing
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-key'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

describe('Referral System', () => {
  let testUserId1: string
  let testUserId2: string
  let referralCode: string

  beforeEach(async () => {
    // Create test users
    const { data: user1 } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',
      email_confirm: true
    })

    const { data: user2 } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',
      email_confirm: true
    })

    testUserId1 = user1.user?.id || ''
    testUserId2 = user2.user?.id || ''

    // Create profiles
    await supabase.from('profiles').insert([
      {
        id: testUserId1,
        email: '<EMAIL>',
        subscription_tier: 'free',
        pulls_this_month: 0,
        extra_credits: 0
      },
      {
        id: testUserId2,
        email: '<EMAIL>',
        subscription_tier: 'free',
        pulls_this_month: 0,
        extra_credits: 0
      }
    ])
  })

  afterEach(async () => {
    // Clean up test data
    if (testUserId1) {
      await supabase.from('referrals').delete().eq('referrer_id', testUserId1)
      await supabase.from('referrals').delete().eq('referee_id', testUserId1)
      await supabase.from('profiles').delete().eq('id', testUserId1)
      await supabase.auth.admin.deleteUser(testUserId1)
    }

    if (testUserId2) {
      await supabase.from('referrals').delete().eq('referrer_id', testUserId2)
      await supabase.from('referrals').delete().eq('referee_id', testUserId2)
      await supabase.from('profiles').delete().eq('id', testUserId2)
      await supabase.auth.admin.deleteUser(testUserId2)
    }
  })

  describe('Referral Code Generation', () => {
    it('should generate a unique referral code', async () => {
      const { data, error } = await supabase.rpc('get_or_create_referral_code', {
        user_id_param: testUserId1
      })

      expect(error).toBeNull()
      expect(data).toBeTruthy()
      expect(typeof data).toBe('string')
      expect(data.length).toBeGreaterThan(0)

      referralCode = data
    })

    it('should return the same code on subsequent calls', async () => {
      // First call
      const { data: code1 } = await supabase.rpc('get_or_create_referral_code', {
        user_id_param: testUserId1
      })

      // Second call
      const { data: code2 } = await supabase.rpc('get_or_create_referral_code', {
        user_id_param: testUserId1
      })

      expect(code1).toBe(code2)
    })
  })

  describe('Referral Processing', () => {
    beforeEach(async () => {
      // Generate referral code for user1
      const { data } = await supabase.rpc('get_or_create_referral_code', {
        user_id_param: testUserId1
      })
      referralCode = data
    })

    it('should process a valid referral signup', async () => {
      const { data, error } = await supabase.rpc('process_referral_signup', {
        referee_id_param: testUserId2,
        referral_code_param: referralCode
      })

      expect(error).toBeNull()
      expect(data).toBe(true)

      // Check that referral record was created
      const { data: referrals } = await supabase
        .from('referrals')
        .select('*')
        .eq('referrer_id', testUserId1)
        .eq('referee_id', testUserId2)

      expect(referrals).toHaveLength(1)
      expect(referrals?.[0].status).toBe('pending')
    })

    it('should reject invalid referral code', async () => {
      const { data, error } = await supabase.rpc('process_referral_signup', {
        referee_id_param: testUserId2,
        referral_code_param: 'INVALID123'
      })

      expect(error).toBeNull()
      expect(data).toBe(false)
    })

    it('should prevent duplicate referrals', async () => {
      // First referral
      await supabase.rpc('process_referral_signup', {
        referee_id_param: testUserId2,
        referral_code_param: referralCode
      })

      // Attempt duplicate referral
      const { data, error } = await supabase.rpc('process_referral_signup', {
        referee_id_param: testUserId2,
        referral_code_param: referralCode
      })

      expect(error).toBeNull()
      expect(data).toBe(false)
    })
  })

  describe('Credit Awarding', () => {
    beforeEach(async () => {
      // Set up a pending referral
      const { data } = await supabase.rpc('get_or_create_referral_code', {
        user_id_param: testUserId1
      })
      referralCode = data

      await supabase.rpc('process_referral_signup', {
        referee_id_param: testUserId2,
        referral_code_param: referralCode
      })
    })

    it('should award credits for verified referral', async () => {
      // Get initial credits
      const { data: initialProfile } = await supabase
        .from('profiles')
        .select('extra_credits')
        .eq('id', testUserId1)
        .single()

      const initialCredits = initialProfile?.extra_credits || 0

      // Award credits
      const { data, error } = await supabase.rpc('award_referral_credits', {
        referee_id_param: testUserId2
      })

      expect(error).toBeNull()
      expect(data).toBe(true)

      // Check that credits were awarded
      const { data: updatedProfile } = await supabase
        .from('profiles')
        .select('extra_credits')
        .eq('id', testUserId1)
        .single()

      expect(updatedProfile?.extra_credits).toBe(initialCredits + 2)

      // Check that referral status was updated
      const { data: referral } = await supabase
        .from('referrals')
        .select('status, credits_awarded')
        .eq('referrer_id', testUserId1)
        .eq('referee_id', testUserId2)
        .single()

      expect(referral?.status).toBe('credited')
      expect(referral?.credits_awarded).toBe(2)
    })

    it('should not award credits twice', async () => {
      // First award
      await supabase.rpc('award_referral_credits', {
        referee_id_param: testUserId2
      })

      // Get credits after first award
      const { data: profileAfterFirst } = await supabase
        .from('profiles')
        .select('extra_credits')
        .eq('id', testUserId1)
        .single()

      const creditsAfterFirst = profileAfterFirst?.extra_credits || 0

      // Attempt second award
      const { data, error } = await supabase.rpc('award_referral_credits', {
        referee_id_param: testUserId2
      })

      expect(error).toBeNull()
      expect(data).toBe(false)

      // Check that credits weren't awarded again
      const { data: profileAfterSecond } = await supabase
        .from('profiles')
        .select('extra_credits')
        .eq('id', testUserId1)
        .single()

      expect(profileAfterSecond?.extra_credits).toBe(creditsAfterFirst)
    })
  })

  describe('RLS Policies', () => {
    it('should only allow users to see their own referrals', async () => {
      // This would require setting up proper auth context
      // For now, we'll test the basic functionality
      expect(true).toBe(true)
    })
  })
})
