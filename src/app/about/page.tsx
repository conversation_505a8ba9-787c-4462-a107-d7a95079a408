import { Metadata } from 'next'
import Image from 'next/image'

export const metadata: Metadata = {
  title: 'About Us - Our Story',
  description: 'Learn about <PERSON> and the story behind Ordrly - how a layoff, a baby, and a "boring" problem created the fastest way to research municipal ordinances.',
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
            Our Story
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            How a Layoff, a Baby, and a "Boring" Problem Created Ordrly
          </p>
        </div>
      </div>

      {/* Founder Section */}
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row items-center gap-8 mb-8">
          <div className="lg:w-1/3">
            <div className="relative w-48 h-48 lg:w-64 lg:h-64 mx-auto">
              <Image
                src="/headshot.jpeg"
                alt="Brandon Allen, Founder of Ordrly"
                fill
                className="rounded-full object-cover shadow-lg"
                priority
              />
            </div>
          </div>
          <div className="lg:w-2/3 text-center lg:text-left">
            <h2 className="text-3xl font-bold text-foreground mb-2">
              Brandon Allen
            </h2>
            <p className="text-xl text-primary font-medium mb-4">
              Founder & CEO of Ordrly
            </p>
            <p className="text-lg text-muted-foreground leading-relaxed">
              A technology enthusiast who turned personal frustration into a solution that helps
              professionals save time and reduce stress when researching municipal ordinances.
            </p>
          </div>
        </div>
      </div>

      {/* Story Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="prose prose-lg max-w-none text-foreground">
          <div className="space-y-8 text-lg leading-relaxed">
            <p>
              My name is <span className="font-semibold text-primary">Brandon Allen</span>, and the story of Ordrly starts at one of my lowest points.
            </p>

            <p>
              Just after the new year, I was laid off. With a baby girl on the way and a stack of rejection letters growing, the pressure was immense. I'd always dreamed of starting a company, but I lacked the confidence and, frankly, the right idea.
            </p>

            <p>
              A few months ago, needing a break from the resume grind, I went to a friend's house to play video games and forget about life for a while. We started talking about AI—its speed, its power, and what it could mean for the future.
            </p>

            <p>
              That's when his wife came home.
            </p>

            <p>
              She joined our conversation and started describing the nightmare of her work: digging through complex municipal codes. She talked about the long hours, the "bad apple" deals caused by one missed rule, and the constant stress of staying ahead.
            </p>

            <p>
              My first thought was, <em>"That sounds incredibly boring."</em> But my next thought, the one that hit me like a spark, was different.
            </p>

            <div className="bg-primary/5 border-l-4 border-primary p-6 my-8 rounded-r-lg">
              <div className="space-y-2 text-primary font-medium">
                <p>"What if AI can help with that?"</p>
                <p>"What if I could save you time?"</p>
                <p>"What if you could get those 10-20 hours back each week to spend with your family?"</p>
              </div>
            </div>

            <p>
              The way her eyes lit up told me everything. In that moment, this wasn't a boring problem anymore. <span className="font-semibold text-primary">It was a human problem.</span>
            </p>

            <p>
              For the next several weeks and months, I poured my soul into building a solution. Fueled by a passion for AI and a desire to help my friend, Ordrly was born.
            </p>

            <div className="bg-secondary/10 p-8 rounded-lg my-12 text-center">
              <h2 className="text-2xl font-bold text-foreground mb-4">My Goal is Simple</h2>
              <p className="text-xl text-muted-foreground">
                To give you back your most valuable asset—<span className="font-semibold text-primary">your time</span>.
              </p>
              <p className="mt-4 text-lg">
                If Ordrly doesn't save you time and frustration, I need to know, because that's the entire reason we're here.
              </p>
            </div>

            <p>
              I'm not an expert in municipal law. I'm a guy who wanted to solve a problem for a friend. When you use Ordrly, you become that friend. I'm here to help you conquer the chaos of research so you can focus on what truly matters.
            </p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-primary/10 to-secondary/10 p-8 rounded-lg">
            <h3 className="text-2xl font-bold text-foreground mb-4">Ready to End the Frustration?</h3>
            <p className="text-lg text-muted-foreground mb-6">
              I built Ordrly to solve a real problem for a friend. Now, let me solve it for you. See for yourself how much time you can save.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/signup"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary/90 transition-colors duration-200"
              >
                Start Your Free Trial
              </a>
              <a
                href="/pricing"
                className="inline-flex items-center justify-center px-6 py-3 border border-border text-base font-medium rounded-md text-foreground bg-background hover:bg-accent transition-colors duration-200"
              >
                View Pricing
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
