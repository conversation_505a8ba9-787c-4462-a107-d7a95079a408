'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Archive, AlertTriangle, Clock, Database } from 'lucide-react'

// Import UI components individually to avoid potential bundling issues
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface ArchivalAnalysis {
  cutoff_date: string
  cutoff_days: number
  archivable_items: {
    search_history: number
    chat_messages: number
    saved_searches: number
  }
  total_archivable: number
  storage_savings_estimate: number
}

export default function DataManagementPage() {
  const [loading, setLoading] = useState(true)
  const [archiving, setArchiving] = useState(false)
  const [analysis, setAnalysis] = useState<ArchivalAnalysis | null>(null)
  const [cutoffDays, setCutoffDays] = useState(90)
  const router = useRouter()
  const supabase = createClient()

  const loadArchivalAnalysis = useCallback(async () => {
    try {
      const response = await fetch(`/api/data/archive?cutoff_days=${cutoffDays}`)
      if (response.ok) {
        const data = await response.json()
        setAnalysis(data)
      }
    } catch (error) {
      console.error('Failed to load archival analysis:', error)
    } finally {
      setLoading(false)
    }
  }, [cutoffDays])

  // Trust middleware for auth - just load data
  useEffect(() => {
    loadArchivalAnalysis()
  }, [loadArchivalAnalysis])

  useEffect(() => {
    if (cutoffDays) {
      loadArchivalAnalysis()
    }
  }, [cutoffDays, loadArchivalAnalysis])

  const handleArchive = async (dryRun = false) => {
    setArchiving(true)
    try {
      const response = await fetch('/api/data/archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          archive_type: 'manual',
          cutoff_days: cutoffDays,
          dry_run: dryRun
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        
        if (!dryRun) {
          // Refresh analysis after archiving
          await loadArchivalAnalysis()
        }
      }
    } catch (error) {
      console.error('Archival failed:', error)
      alert('Failed to archive data')
    } finally {
      setArchiving(false)
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading data management...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background py-16 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-card border border-border rounded-2xl shadow-premium p-8 animate-fade-in">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-4xl font-bold text-foreground mb-2">Data Management</h1>
              <p className="text-muted-foreground">Manage your data storage and archival settings</p>
            </div>
            <div className="flex items-center space-x-2">
              <Database className="h-6 w-6 text-primary" />
              <span className="text-sm text-muted-foreground">Auto-archival available</span>
            </div>
          </div>

          {/* Archival Settings */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Archive className="h-5 w-5 mr-2" />
                Data Archival Settings
              </CardTitle>
              <CardDescription>
                Automatically archive old data to free up storage space
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Archive data older than:
                  </label>
                  <select
                    value={cutoffDays}
                    onChange={(e) => setCutoffDays(parseInt(e.target.value))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={30}>30 days</option>
                    <option value={60}>60 days</option>
                    <option value={90}>90 days</option>
                    <option value={180}>6 months</option>
                    <option value={365}>1 year</option>
                  </select>
                </div>

                {analysis && (
                  <div className="bg-muted/30 rounded-lg p-4 border border-border">
                    <h4 className="font-medium text-card-foreground mb-3">Archival Preview</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary">
                          {analysis.archivable_items.search_history}
                        </div>
                        <div className="text-sm text-muted-foreground">Search History</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-success">
                          {analysis.archivable_items.chat_messages}
                        </div>
                        <div className="text-sm text-muted-foreground">Chat Messages</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-secondary">
                          {analysis.total_archivable}
                        </div>
                        <div className="text-sm text-muted-foreground">Total Items</div>
                      </div>
                    </div>
                    <div className="mt-4 text-center">
                      <Badge variant="secondary">
                        Estimated savings: {formatBytes(analysis.storage_savings_estimate)}
                      </Badge>
                    </div>
                  </div>
                )}

                <div className="flex space-x-3">
                  <Button
                    onClick={() => handleArchive(true)}
                    disabled={archiving || !analysis?.total_archivable}
                    variant="outline"
                  >
                    <Clock className="h-4 w-4 mr-2" />
                    Preview Archive
                  </Button>
                  <Button
                    onClick={() => handleArchive(false)}
                    disabled={archiving || !analysis?.total_archivable}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    <Archive className="h-4 w-4 mr-2" />
                    {archiving ? 'Archiving...' : 'Archive Now'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Storage Information */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Storage Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Current Storage Usage</span>
                  <Badge variant="outline">
                    {analysis ? formatBytes(analysis.storage_savings_estimate) : 'Calculating...'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Archival Cutoff Date</span>
                  <span className="text-sm font-medium">
                    {analysis ? new Date(analysis.cutoff_date).toLocaleDateString() : 'Loading...'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Items Available for Archive</span>
                  <span className="text-sm font-medium">
                    {analysis?.total_archivable || 0} items
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Warning */}
          {analysis && analysis.total_archivable > 0 && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-orange-900">Archive Notice</h4>
                    <p className="text-sm text-orange-800 mt-1">
                      Archived data will be moved to long-term storage and may take longer to access. 
                      This action cannot be easily undone. Consider downloading important data before archiving.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
