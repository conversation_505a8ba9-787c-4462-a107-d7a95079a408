'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import {
  Download,
  Upload,
  FileText,
  Database,
  Globe,
  CheckCircle,
  Clock,
  AlertCircle,
  Archive,
  Share
} from 'lucide-react'

// Import UI components individually to avoid potential bundling issues
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'

interface ExportRequest {
  id: string
  export_type: string
  file_format: string
  status: string
  file_url?: string
  created_at: string
  completed_at?: string
  expires_at?: string
}

export default function DataPortabilityPage() {
  const [loading, setLoading] = useState(true)
  const [exporting, setExporting] = useState(false)
  const [exports, setExports] = useState<ExportRequest[]>([])
  const [selectedDataTypes, setSelectedDataTypes] = useState<string[]>(['searches', 'saved_searches'])
  const [exportFormat, setExportFormat] = useState('csv')
  const [includeMetadata, setIncludeMetadata] = useState(true)
  const router = useRouter()
  const supabase = createClient()

  const dataTypes = [
    { id: 'searches', label: 'Search History', description: 'All your property searches and results' },
    { id: 'saved_searches', label: 'Saved Searches', description: 'Your bookmarked and organized searches' },
    { id: 'chats', label: 'Chat History', description: 'AI assistant conversations and responses' },
    { id: 'preferences', label: 'User Preferences', description: 'Your settings and customizations' },
    { id: 'shortcuts', label: 'Custom Shortcuts', description: 'Your personalized quick access links' }
  ]

  const formatOptions = [
    { value: 'csv', label: 'CSV', description: 'Comma-separated values, compatible with Excel' },
    { value: 'json', label: 'JSON', description: 'JavaScript Object Notation, developer-friendly' },
    { value: 'xml', label: 'XML', description: 'Extensible Markup Language, structured format' },
    { value: 'zip', label: 'ZIP Archive', description: 'Compressed archive with multiple formats' }
  ]

  const checkAuthAndLoadExports = useCallback(async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        router.push('/login')
        return
      }

      await loadExports()
    } catch (err) {
      console.error('Auth check error:', err)
      router.push('/login')
    }
  }, [router, supabase.auth])

  useEffect(() => {
    checkAuthAndLoadExports()
  }, [checkAuthAndLoadExports])

  const loadExports = async () => {
    try {
      const response = await fetch('/api/privacy/exports')
      if (response.ok) {
        const data = await response.json()
        setExports(data.exports || [])
      }
    } catch (error) {
      console.error('Failed to load exports:', error)
    } finally {
      setLoading(false)
    }
  }

  const requestExport = async () => {
    if (selectedDataTypes.length === 0) {
      alert('Please select at least one data type to export')
      return
    }

    setExporting(true)
    try {
      const response = await fetch('/api/privacy/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          export_type: selectedDataTypes.length === 1 ? selectedDataTypes[0] : 'multiple',
          data_types: selectedDataTypes,
          file_format: exportFormat,
          include_metadata: includeMetadata
        })
      })

      if (response.ok) {
        await loadExports()
      }
    } catch (error) {
      console.error('Failed to request export:', error)
    } finally {
      setExporting(false)
    }
  }

  const downloadExport = async (exportItem: ExportRequest) => {
    if (!exportItem.file_url) return

    try {
      const response = await fetch(exportItem.file_url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ordrly-export-${exportItem.export_type}-${new Date(exportItem.created_at).toISOString().split('T')[0]}.${exportItem.file_format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Failed to download export:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'pending':
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
      case 'processing':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const isExpired = (expiresAt?: string) => {
    if (!expiresAt) return false
    return new Date(expiresAt) < new Date()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading data portability...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Data Portability</h1>
            <p className="text-gray-600">
              Export your data in portable formats for backup or migration to other services
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Export Configuration */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Download className="h-5 w-5 mr-2" />
                    Create New Export
                  </CardTitle>
                  <CardDescription>
                    Select the data you want to export and choose your preferred format
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label className="text-base font-medium mb-3 block">Data Types</Label>
                    <div className="space-y-3">
                      {dataTypes.map((dataType) => (
                        <div key={dataType.id} className="flex items-start space-x-3">
                          <Checkbox
                            id={dataType.id}
                            checked={selectedDataTypes.includes(dataType.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedDataTypes(prev => [...prev, dataType.id])
                              } else {
                                setSelectedDataTypes(prev => prev.filter(id => id !== dataType.id))
                              }
                            }}
                          />
                          <div className="flex-1">
                            <Label htmlFor={dataType.id} className="font-medium">
                              {dataType.label}
                            </Label>
                            <p className="text-sm text-gray-500">{dataType.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="format" className="text-base font-medium mb-3 block">
                      Export Format
                    </Label>
                    <Select value={exportFormat} onValueChange={setExportFormat}>
                      {formatOptions.map((format) => (
                        <option key={format.value} value={format.value}>
                          {format.label} - {format.description}
                        </option>
                      ))}
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="metadata"
                      checked={includeMetadata}
                      onCheckedChange={setIncludeMetadata}
                    />
                    <Label htmlFor="metadata">
                      Include metadata (timestamps, IDs, etc.)
                    </Label>
                  </div>

                  <Button
                    onClick={requestExport}
                    disabled={exporting || selectedDataTypes.length === 0}
                    className="w-full"
                  >
                    {exporting ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Creating Export...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Create Export
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Export History and Data Rights */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Archive className="h-5 w-5 mr-2" />
                    Export History
                  </CardTitle>
                  <CardDescription>
                    Your recent data export requests and downloads
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {exports.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">No exports yet</p>
                      <p className="text-sm text-gray-400">
                        Create your first export to see it here
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {exports.slice(0, 3).map((exportItem) => (
                        <div key={exportItem.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(exportItem.status)}
                              <span className="font-medium">
                                {exportItem.export_type} Export
                              </span>
                              <Badge className={getStatusColor(exportItem.status)}>
                                {exportItem.status}
                              </Badge>
                            </div>
                            <span className="text-sm text-gray-500">
                              {exportItem.file_format.toUpperCase()}
                            </span>
                          </div>

                          <div className="text-sm text-gray-600 mb-3">
                            <div>Created: {new Date(exportItem.created_at).toLocaleString()}</div>
                            {exportItem.completed_at && (
                              <div>Completed: {new Date(exportItem.completed_at).toLocaleString()}</div>
                            )}
                          </div>

                          <div className="flex space-x-2">
                            {exportItem.status === 'completed' && exportItem.file_url && !isExpired(exportItem.expires_at) && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => downloadExport(exportItem)}
                                className="flex items-center"
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Download
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
