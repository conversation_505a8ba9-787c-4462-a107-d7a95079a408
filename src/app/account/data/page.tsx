'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON>outer } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Shield,
  Download,
  Trash2,
  Database,
  Settings,
  AlertTriangle,
  FileText,
  Archive,
  MessageSquare,
  Bookmark,
  Search,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface DataSettings {
  save_search_history: boolean
  save_chat_history: boolean
  retention_days: number
  auto_delete_enabled: boolean
  export_enabled: boolean
}

interface StorageUsage {
  searches_count: number
  chats_count: number
  saved_searches_count: number
  total_size_bytes: number
  last_calculated_at: string
}

interface ExportRequest {
  id: string
  export_type: string
  file_format: string
  status: string
  file_url?: string
  created_at: string
  completed_at?: string
  expires_at?: string
}

export default function DataManagementPage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleteType, setDeleteType] = useState<string>('')
  const [dataSettings, setDataSettings] = useState<DataSettings>({
    save_search_history: true,
    save_chat_history: true,
    retention_days: 365,
    auto_delete_enabled: false,
    export_enabled: true
  })
  const [storageUsage, setStorageUsage] = useState<StorageUsage>({
    searches_count: 0,
    chats_count: 0,
    saved_searches_count: 0,
    total_size_bytes: 0,
    last_calculated_at: new Date().toISOString()
  })
  const [exports, setExports] = useState<ExportRequest[]>([])
  const router = useRouter()
  const supabase = createClient()

  const loadDataSettings = async () => {
    try {
      const response = await fetch('/api/privacy/settings')
      if (response.ok) {
        const data = await response.json()
        if (data.settings) {
          setDataSettings(data.settings)
        }
      }
    } catch (error) {
      console.error('Failed to load data settings:', error)
    }
  }

  const loadStorageUsage = async () => {
    try {
      const response = await fetch('/api/privacy/storage-usage')
      if (response.ok) {
        const data = await response.json()
        if (data.usage) {
          setStorageUsage(data.usage)
        }
      }
    } catch (error) {
      console.error('Failed to load storage usage:', error)
    }
  }

  const loadExports = async () => {
    try {
      const response = await fetch('/api/privacy/exports')
      if (response.ok) {
        const data = await response.json()
        setExports(data.exports || [])
      }
    } catch (error) {
      console.error('Failed to load exports:', error)
    } finally {
      setLoading(false)
    }
  }

  // Trust middleware for auth - just load data
  useEffect(() => {
    Promise.all([
      loadDataSettings(),
      loadStorageUsage(),
      loadExports()
    ])
  }, [])

  const saveSettings = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/privacy/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataSettings)
      })

      if (response.ok) {
        setHasUnsavedChanges(false)
        // Show success toast
      }
    } catch (error) {
      console.error('Failed to save settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const requestExport = async (exportType: string, format: string) => {
    try {
      const response = await fetch('/api/privacy/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          export_type: exportType,
          file_format: format
        })
      })

      if (response.ok) {
        await loadExports()
        // Show success toast
        const exportTypeNames: Record<string, string> = {
          'searches': 'Search History',
          'chats': 'Chat History',
          'saved_searches': 'Saved Searches',
          'all': 'All Data'
        }
        const typeName = exportTypeNames[exportType] || exportType

        // Create a temporary toast notification
        const toast = document.createElement('div')
        toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300'
        toast.textContent = `${typeName} export started - check your downloads. You'll get an email if it's large.`
        document.body.appendChild(toast)

        setTimeout(() => {
          toast.style.opacity = '0'
          setTimeout(() => document.body.removeChild(toast), 300)
        }, 3000)
      } else {
        throw new Error('Export failed')
      }
    } catch (error) {
      console.error('Failed to request export:', error)

      // Show error toast
      const toast = document.createElement('div')
      toast.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300'
      toast.textContent = 'Unable to generate export. Please try again later.'
      document.body.appendChild(toast)

      setTimeout(() => {
        toast.style.opacity = '0'
        setTimeout(() => document.body.removeChild(toast), 300)
      }, 3000)
    }
  }

  const handleDeleteClick = (dataType: string) => {
    setDeleteType(dataType)
    setShowDeleteModal(true)
  }

  const confirmDelete = async () => {
    setShowDeleteModal(false)

    try {
      const response = await fetch(`/api/privacy/delete-data`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ data_type: deleteType })
      })

      if (response.ok) {
        await loadStorageUsage()

        // Show success toast
        const typeName = getDeleteTypeDisplayName(deleteType)
        const toast = document.createElement('div')
        toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300'
        toast.textContent = `Successfully deleted ${typeName} ✓`
        document.body.appendChild(toast)

        setTimeout(() => {
          toast.style.opacity = '0'
          setTimeout(() => document.body.removeChild(toast), 300)
        }, 3000)
      } else {
        throw new Error('Delete failed')
      }
    } catch (error) {
      console.error('Failed to delete data:', error)

      // Show error toast
      const typeName = getDeleteTypeDisplayName(deleteType)
      const toast = document.createElement('div')
      toast.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300'
      toast.textContent = `Unable to delete ${typeName}. Please try again.`
      document.body.appendChild(toast)

      setTimeout(() => {
        toast.style.opacity = '0'
        setTimeout(() => document.body.removeChild(toast), 300)
      }, 3000)
    }
  }

  const getDeleteTypeDisplayName = (type: string) => {
    const names: Record<string, string> = {
      'searches': 'search history',
      'chats': 'chat history',
      'saved_searches': 'saved searches',
      'all': 'all data'
    }
    return names[type] || type
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const updateSetting = (key: keyof DataSettings, value: boolean | number) => {
    setDataSettings(prev => ({
      ...prev,
      [key]: value
    }))
    setHasUnsavedChanges(true)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading data management...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <div className="flex-1 py-16 px-4">
        <div className="max-w-4xl mx-auto">
        <div className="bg-card border border-border rounded-2xl shadow-premium p-8 animate-fade-in">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-4xl font-bold text-foreground mb-2">Data Management</h1>
              <p className="text-muted-foreground">Control your data retention and privacy settings</p>
            </div>
            <Button
              onClick={saveSettings}
              disabled={saving}
              className={`flex items-center transition-all duration-200 ${
                hasUnsavedChanges ? 'bg-blue-600 hover:bg-blue-700' : ''
              }`}
            >
              <Settings className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>

          <Tabs defaultValue="settings" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="storage">Storage</TabsTrigger>
              <TabsTrigger value="exports">Exports</TabsTrigger>
              <TabsTrigger value="deletion">Data Deletion</TabsTrigger>
            </TabsList>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="space-y-1">
                    <p className="text-xs text-gray-400">Settings</p>
                    <CardTitle className="text-2xl font-semibold text-white flex items-center">
                      <Shield className="h-6 w-6 mr-2" />
                      Privacy Settings
                    </CardTitle>
                  </div>
                  <CardDescription>
                    Configure what data we collect and how long we keep it
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="font-semibold text-white flex items-center">
                          <Search className="h-4 w-4 mr-2" />
                          Save search history
                        </Label>
                        <p className="text-sm text-gray-400">
                          Store your searches to provide personalized recommendations
                        </p>
                      </div>
                      <Switch
                        checked={dataSettings.save_search_history}
                        onCheckedChange={(checked) => updateSetting('save_search_history', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="font-semibold text-white flex items-center">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Save chat history
                        </Label>
                        <p className="text-sm text-gray-400">
                          Store your AI chat conversations for reference
                        </p>
                      </div>
                      <Switch
                        checked={dataSettings.save_chat_history}
                        onCheckedChange={(checked) => updateSetting('save_chat_history', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="font-semibold text-white flex items-center">
                          <Download className="h-4 w-4 mr-2" />
                          Enable data exports
                        </Label>
                        <p className="text-sm text-gray-400">
                          Allow downloading your data in portable formats
                        </p>
                      </div>
                      <Switch
                        checked={dataSettings.export_enabled}
                        onCheckedChange={(checked) => updateSetting('export_enabled', checked)}
                      />
                    </div>
                  </div>

                  <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-6">
                    <div className="flex-1 space-y-2">
                      <Label>Data retention period</Label>
                      <Select
                        value={dataSettings.retention_days.toString()}
                        onValueChange={(value) => updateSetting('retention_days', parseInt(value))}
                        disabled={!dataSettings.auto_delete_enabled}
                      >
                        <SelectTrigger className={!dataSettings.auto_delete_enabled ? 'opacity-50 cursor-not-allowed' : ''}>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="30">30 days</SelectItem>
                          <SelectItem value="90">90 days</SelectItem>
                          <SelectItem value="180">6 months</SelectItem>
                          <SelectItem value="365">1 year</SelectItem>
                          <SelectItem value="730">2 years</SelectItem>
                          <SelectItem value="-1">Forever</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-gray-400">
                        How long to keep your data before automatic deletion
                      </p>
                      {!dataSettings.auto_delete_enabled && (
                        <p className="text-xs text-red-400">
                          Please enable auto-delete to select a retention period.
                        </p>
                      )}
                    </div>

                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="font-semibold text-white">Auto-delete old data</Label>
                        <Switch
                          checked={dataSettings.auto_delete_enabled}
                          onCheckedChange={(checked) => updateSetting('auto_delete_enabled', checked)}
                        />
                      </div>
                      <p className="text-sm text-gray-400">
                        Automatically delete data older than retention period
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="storage" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-semibold text-white flex items-center">
                    <Database className="h-6 w-6 mr-2" />
                    Storage Usage
                  </CardTitle>
                  <CardDescription>
                    Overview of your data storage usage
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className={`text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20 ${
                      storageUsage.searches_count === 0 ? 'opacity-50' : ''
                    }`}>
                      <div className="flex items-center justify-center mb-2">
                        <FileText className="h-6 w-6 mr-2 text-blue-500" />
                      </div>
                      <div className="text-2xl font-bold text-blue-500">
                        {storageUsage.searches_count}
                      </div>
                      <div className="text-sm text-blue-500/80">Search Records</div>
                      {storageUsage.searches_count === 0 && (
                        <div className="text-xs text-gray-400 mt-1">You have no search records yet.</div>
                      )}
                    </div>
                    <div className={`text-center p-4 bg-green-500/10 rounded-lg border border-green-500/20 ${
                      storageUsage.chats_count === 0 ? 'opacity-50' : ''
                    }`}>
                      <div className="flex items-center justify-center mb-2">
                        <MessageSquare className="h-6 w-6 mr-2 text-green-500" />
                      </div>
                      <div className="text-2xl font-bold text-green-500">
                        {storageUsage.chats_count}
                      </div>
                      <div className="text-sm text-green-500/80">Chat Messages</div>
                      {storageUsage.chats_count === 0 && (
                        <div className="text-xs text-gray-400 mt-1">You have no chat messages yet.</div>
                      )}
                    </div>
                    <div className={`text-center p-4 bg-gray-500/10 rounded-lg border border-gray-500/20 ${
                      storageUsage.saved_searches_count === 0 ? 'opacity-50' : ''
                    }`}>
                      <div className="flex items-center justify-center mb-2">
                        <Bookmark className="h-6 w-6 mr-2 text-gray-500" />
                      </div>
                      <div className="text-2xl font-bold text-gray-500">
                        {storageUsage.saved_searches_count}
                      </div>
                      <div className="text-sm text-gray-500/80">Saved Searches</div>
                      {storageUsage.saved_searches_count === 0 && (
                        <div className="text-xs text-gray-400 mt-1">You have no saved searches yet.</div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-white">Total Storage Used</span>
                    </div>
                    <div className="relative">
                      <Progress
                        value={Math.min((storageUsage.total_size_bytes / (100 * 1024 * 1024)) * 100, 100)}
                        className="bg-gradient-to-r from-blue-500 to-green-400"
                      />
                      <span className="absolute right-0 top-0 text-sm text-gray-200 mt-1">
                        {formatBytes(storageUsage.total_size_bytes)}
                      </span>
                    </div>
                    <p className="text-xs text-gray-400">
                      Last calculated: {new Date(storageUsage.last_calculated_at).toLocaleString()}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="exports" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-semibold text-white flex items-center">
                    <Download className="h-6 w-6 mr-2" />
                    Data Exports
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400">
                    Download your searches, chats, and saved data in portable formats.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <Button
                      onClick={() => requestExport('searches', 'csv')}
                      variant="outline"
                      className="flex items-center justify-center bg-gray-700 border-gray-600 text-gray-200 hover:bg-blue-600 hover:border-blue-600 hover:text-white transition-all duration-200"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Export Search History (CSV)
                    </Button>
                    <Button
                      onClick={() => requestExport('chats', 'json')}
                      variant="outline"
                      className="flex items-center justify-center bg-gray-700 border-gray-600 text-gray-200 hover:bg-blue-600 hover:border-blue-600 hover:text-white transition-all duration-200"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Export Chat History (JSON)
                    </Button>
                    <Button
                      onClick={() => requestExport('saved_searches', 'csv')}
                      variant="outline"
                      className="flex items-center justify-center bg-gray-700 border-gray-600 text-gray-200 hover:bg-blue-600 hover:border-blue-600 hover:text-white transition-all duration-200"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Export Saved Searches (CSV)
                    </Button>
                    <Button
                      onClick={() => requestExport('all', 'zip')}
                      variant="outline"
                      className="flex items-center justify-center bg-red-600 border-red-600 text-white hover:bg-red-700 hover:border-red-700 transition-all duration-200"
                    >
                      <Archive className="h-4 w-4 mr-2" />
                      Export All Data (ZIP)
                    </Button>
                  </div>
                  <p className="text-xs text-gray-400 text-center">
                    May take several minutes; you'll receive an email when ready.
                  </p>

                  {exports.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="font-medium">Recent Exports</h4>
                      {exports.map((exportItem, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <div>
                              <div className="font-medium">
                                {exportItem.export_type} ({exportItem.file_format.toUpperCase()})
                              </div>
                              <div className="text-sm text-gray-500">
                                {new Date(exportItem.created_at).toLocaleString()}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              variant={exportItem.status === 'completed' ? 'default' : 'secondary'}
                            >
                              {exportItem.status}
                            </Badge>
                            {exportItem.status === 'completed' && exportItem.file_url && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(exportItem.file_url, '_blank')}
                              >
                                Download
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="deletion" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl font-semibold text-white flex items-center">
                    <Trash2 className="h-6 w-6 mr-2 text-red-400" />
                    Data Deletion
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-400">
                    Make sure you export your data first—this cannot be undone.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="p-4 bg-red-800 bg-opacity-40 border border-red-700 rounded-lg">
                      <div className="flex items-center mb-2">
                        <AlertTriangle className="h-6 w-6 text-red-400 mr-2" />
                        <span className="font-medium text-red-100">Warning</span>
                      </div>
                      <p className="text-sm text-red-100">
                        Data deletion is permanent and cannot be undone. Make sure to export any data you want to keep before proceeding.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <Button
                        variant="destructive"
                        onClick={() => handleDeleteClick('searches')}
                        className="flex items-center justify-center bg-red-600 hover:bg-red-700 transition-all duration-200"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete All Search History
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => handleDeleteClick('chats')}
                        className="flex items-center justify-center bg-red-600 hover:bg-red-700 transition-all duration-200"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete All Chat History
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => handleDeleteClick('saved_searches')}
                        className="flex items-center justify-center bg-red-600 hover:bg-red-700 transition-all duration-200"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete All Saved Searches
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => handleDeleteClick('all')}
                        className="flex items-center justify-center bg-red-700 hover:bg-red-800 ring-red-500 hover:ring-2 transition-all duration-200"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete All Data
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-card border border-border rounded-2xl p-6 max-w-md mx-4 shadow-premium">
              <div className="flex items-center mb-4">
                <AlertTriangle className="h-6 w-6 text-red-400 mr-3" />
                <h3 className="text-lg font-semibold text-card-foreground">Confirm Deletion</h3>
              </div>
              <p className="text-muted-foreground mb-6">
                Are you sure you want to delete all {getDeleteTypeDisplayName(deleteType)}? This action cannot be undone.
              </p>
              <div className="flex space-x-3 justify-end">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteModal(false)}
                  className="px-4 py-2"
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={confirmDelete}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700"
                >
                  Yes, delete
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
