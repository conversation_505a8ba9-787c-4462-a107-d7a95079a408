import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'My Account - Manage Your Ordrly Profile | Ordrly',
  description: 'Manage your Ordrly account settings, subscription, search history, and privacy preferences. Access your property compliance search data and billing information.',
  keywords: ['account settings', 'user profile', 'subscription management', 'search history', 'privacy settings'],
  robots: {
    index: false, // Account pages should not be indexed for privacy
    follow: true,
  },
}

export default function AccountLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
