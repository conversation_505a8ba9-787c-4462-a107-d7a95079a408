'use client'

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { User as SupabaseUser } from '@supabase/supabase-js'
import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { RefreshHandler } from '@/components/auth/RefreshHandler'
// PullPackButton removed per user request - no buying more searches
// import { PullPackButton } from '@/components/billing/UpgradeButton'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { User, CreditCard, Settings, BarChart3, Crown, Zap, RefreshCw, MessageCircle, Shield } from 'lucide-react'

// Client component for billing portal button
function BillingPortalButton() {
  return (
    <Button
      onClick={() => {
        // Open in same tab to handle redirects properly
        window.location.href = '/api/billing/portal'
      }}
      className="w-full"
    >
      Manage Billing
    </Button>
  )
}

// Sign out function for client component
async function handleSignOut() {
  const supabase = createClient()
  await supabase.auth.signOut()
  window.location.href = '/login'
}

function AccountPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [user, setUser] = useState<SupabaseUser | null>(null)
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  const errorParam = searchParams.get('error')
  const successParam = searchParams.get('success')

  useEffect(() => {
    let isMounted = true

    async function loadUserData() {
      try {
        const supabase = createClient()

        // Add timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Auth timeout')), 10000)
        )

        const authPromise = supabase.auth.getUser()

        const {
          data: { user },
          error: authError
        } = await Promise.race([authPromise, timeoutPromise]) as any

        if (!isMounted) return

        if (authError || !user) {
          console.error('Auth error in account page:', authError)
          router.push('/login')
          return
        }

        setUser(user)

        // Get user profile data with error handling
        const profileTimeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Profile query timeout')), 10000)
        )

        const profilePromise = supabase
          .from('profiles')
          .select('subscription_tier, subscription_status, subscription_period_end, cancel_at_period_end, stripe_customer_id, extra_credits, created_at, name, email')
          .eq('id', user.id)
          .single()

        const { data: profile, error: profileError } = await Promise.race([profilePromise, profileTimeoutPromise]) as any

        if (!isMounted) return

        console.log('Account page profile data:', {
          subscription_tier: profile?.subscription_tier,
          subscription_status: profile?.subscription_status,
          cancel_at_period_end: profile?.cancel_at_period_end,
          subscription_period_end: profile?.subscription_period_end,
          stripe_customer_id: profile?.stripe_customer_id
        })

        if (profileError || !profile) {
          console.error('Profile error in account page:', profileError)
          // More graceful error handling - try to create a basic profile first
          if (profileError && profileError.code === 'PGRST116') {
            console.log('Profile not found, attempting to create basic profile...')
            try {
              const { error: createError } = await supabase
                .from('profiles')
                .insert({
                  id: user.id,
                  email: user.email,
                  name: user.user_metadata?.name || user.email?.split('@')[0],
                  subscription_tier: 'trial',
                  pulls_this_month: 0,
                  searches_used: 0,
                  extra_credits: 0,
                  stripe_customer_id: null
                })

              if (!createError) {
                // Retry loading the profile
                const { data: newProfile, error: retryError } = await supabase
                  .from('profiles')
                  .select('subscription_tier, subscription_status, subscription_period_end, cancel_at_period_end, stripe_customer_id, pulls_this_month, searches_used, extra_credits, created_at, name, email')
                  .eq('id', user.id)
                  .single()

                if (!retryError && newProfile) {
                  setProfile(newProfile)
                  console.log('Successfully created and loaded new profile')
                  return // Continue with the rest of the function
                }
              }
            } catch (createErr) {
              console.error('Failed to create profile:', createErr)
            }
          }
          router.push('/login?error=profile-error')
          return
        }

        // Get user's chat message count for this month (with error handling)
        let messageCount = 0
        try {
          const currentMonth = new Date()
          const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)

          // First get user's conversations
          const { data: userConversations, error: convError } = await supabase
            .from('chat_conversations')
            .select('id')
            .eq('user_id', user.id)

          if (!convError && userConversations && userConversations.length > 0) {
            const conversationIds = userConversations.map(conv => conv.id)

            const { count, error: messageCountError } = await supabase
              .from('chat_messages')
              .select('*', { count: 'exact', head: true })
              .eq('role', 'user')
              .in('conversation_id', conversationIds)
              .gte('created_at', firstDayOfMonth.toISOString())

            if (!messageCountError) {
              messageCount = count || 0
            } else {
              console.warn('Failed to get message count:', messageCountError)
            }
          }
        } catch (error) {
          console.warn('Error counting messages:', error)
          messageCount = 0
        }

        // Add message count to profile
        const profileWithMessages = {
          ...profile,
          messages_this_month: messageCount
        }

        setProfile(profileWithMessages)

        // Also fetch the latest billing data to ensure subscription status is current
        if (profile.stripe_customer_id) {
          try {
            // Add timeout to prevent infinite loading
            const controller = new AbortController()
            const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

            const billingResponse = await fetch('/api/billing/overview', {
              signal: controller.signal
            })
            clearTimeout(timeoutId)

            if (!isMounted) return

            if (billingResponse.ok) {
              const billingData = await billingResponse.json()
              console.log('Latest billing data:', billingData)

              // Update profile with latest subscription info from Stripe
              if (billingData.subscription && isMounted) {
                const updatedProfile = {
                  ...profile,
                  subscription_tier: billingData.subscription.tier,
                  subscription_status: billingData.subscription.status,
                  cancel_at_period_end: billingData.subscription.cancel_at_period_end,
                  subscription_period_end: billingData.subscription.current_period_end
                }
                setProfile(updatedProfile)
                console.log('Updated profile with latest billing data:', updatedProfile)
              }
            } else {
              console.warn('Billing API returned non-OK status:', billingResponse.status)
            }
          } catch (error) {
            console.error('Failed to fetch latest billing data:', error)
            // Continue with profile data from database even if billing API fails
          }
        }
      } catch (err: any) {
        console.error('Account page error:', {
          error: err,
          message: err.message,
          stack: err.stack,
          timestamp: new Date().toISOString(),
          userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown'
        })
        if (isMounted) {
          setError(err.message)
          // Add more specific error handling
          if (err.message.includes('timeout') || err.message.includes('Auth timeout')) {
            router.push('/login?error=timeout')
          } else if (err.message.includes('Profile')) {
            router.push('/login?error=profile-error')
          } else {
            router.push('/login?error=account-error')
          }
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    loadUserData()

    return () => {
      isMounted = false
    }
  }, []) // Remove router dependency to prevent infinite loop

  const refreshSubscriptionStatus = async () => {
    if (!profile?.stripe_customer_id) return

    setRefreshing(true)
    try {
      // Add timeout to prevent hanging
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      const billingResponse = await fetch('/api/billing/overview', {
        signal: controller.signal
      })
      clearTimeout(timeoutId)
      if (billingResponse.ok) {
        const billingData = await billingResponse.json()
        console.log('Refreshed billing data:', billingData)

        if (billingData.subscription) {
          const updatedProfile = {
            ...profile,
            subscription_tier: billingData.subscription.tier,
            subscription_status: billingData.subscription.status,
            cancel_at_period_end: billingData.subscription.cancel_at_period_end,
            subscription_period_end: billingData.subscription.current_period_end
          }
          setProfile(updatedProfile)
          console.log('Manually refreshed profile:', updatedProfile)
        } else {
          // No active subscription found
          const updatedProfile = {
            ...profile,
            subscription_tier: 'trial',
            subscription_status: 'canceled',
            cancel_at_period_end: false,
            subscription_period_end: null
          }
          setProfile(updatedProfile)
          console.log('No active subscription found, updated to trial tier')
        }
      }
    } catch (error) {
      console.error('Failed to refresh subscription status:', error)
    } finally {
      setRefreshing(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 py-16 px-4 flex items-center justify-center">
        <div className="bg-card/50 backdrop-blur-sm border border-border rounded-3xl p-12 shadow-2xl">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-6"></div>
            <p className="text-muted-foreground text-lg">Loading your account...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !user || !profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 py-16 px-4 flex items-center justify-center">
        <div className="bg-card/50 backdrop-blur-sm border border-border rounded-3xl p-12 shadow-2xl text-center">
          <div className="bg-destructive/10 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
            <User className="h-10 w-10 text-destructive" />
          </div>
          <h2 className="text-2xl font-bold text-foreground mb-4">Account Error</h2>
          <p className="text-destructive mb-6">Failed to load account data</p>
          <Button onClick={() => router.push('/login')} size="lg" className="px-8">
            Return to Login
          </Button>
        </div>
      </div>
    )
  }

  // Calculate usage percentage based on subscription tier
  const getUsageStats = () => {
    try {
      if (!profile) {
        return { limit: 500, totalAvailable: 500, percentage: 0, isUnlimited: false }
      }

      const tier = profile.subscription_tier
      let limit = 0

      switch (tier) {
        case 'trial':
          limit = 500
          break
        case 'starter':
          limit = 500
          break
        case 'professional':
          limit = 2000
          break
        case 'business':
          return { limit: -1, percentage: 0, isUnlimited: true }
        default:
          console.warn(`Unknown subscription tier: ${tier}, defaulting to trial`)
          limit = 500 // Default to trial limit
      }

      const totalAvailable = limit + (profile.extra_credits || 0)
      const usedCount = profile.messages_this_month || 0
      const percentage = Math.min((usedCount / totalAvailable) * 100, 100)

      return { limit, totalAvailable, percentage, isUnlimited: false }
    } catch (error) {
      console.error('Error calculating usage stats:', error)
      return { limit: 500, totalAvailable: 500, percentage: 0, isUnlimited: false }
    }
  }

  const usageStats = getUsageStats()
  const usagePercentage = usageStats?.percentage || 0

  // Handle error messages
  const getErrorMessage = (error: string) => {
    switch (error) {
      case 'no-customer':
        return 'Unable to access billing portal. Please contact support if you have an active subscription.'
      case 'portal-not-configured':
        return 'Billing portal is not yet configured. Please contact support to manage your subscription.'
      case 'customer-not-found':
        return 'Your billing information could not be found. Please contact support for assistance.'
      case 'portal-failed':
        return 'Billing portal is temporarily unavailable. Please try again later or contact support.'
      default:
        return 'An error occurred. Please try again.'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 py-16 px-4">
      <RefreshHandler />
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="bg-primary/10 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-8">
            <User className="h-12 w-12 text-primary" />
          </div>
          <h1 className="text-5xl font-bold text-foreground mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
            Account Dashboard
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Manage your Ordrly account, subscription, and preferences all in one place
          </p>
        </div>

        {/* Error Messages */}
        {errorParam && (
          <div className="mb-8 p-6 bg-gradient-to-r from-destructive/10 to-destructive/5 border border-destructive/20 rounded-3xl shadow-xl animate-slide-up">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="bg-destructive/20 rounded-full p-2">
                  <svg className="h-6 w-6 text-destructive" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-destructive mb-2">Billing Error</h3>
                <p className="text-base text-destructive/90">{getErrorMessage(errorParam)}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Messages */}
        {successParam && (
          <div className="mb-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border border-green-200 dark:border-green-800 rounded-3xl shadow-xl animate-slide-up">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="bg-green-100 dark:bg-green-900/50 rounded-full p-2">
                  <svg className="h-6 w-6 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">Success!</h3>
                <p className="text-base text-green-700 dark:text-green-300">Your subscription has been updated successfully!</p>
              </div>
            </div>
          </div>
        )}

        {/* Main Content Grid */}
        <div className="space-y-12 animate-slide-up">
          {/* Top Section - Profile and Plan */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {/* Profile Card */}
            <Card className="h-full bg-gradient-to-br from-card to-card/80 border-border/50 shadow-2xl">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="bg-primary/10 rounded-full p-2">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Email Address</label>
                  <p className="text-base text-foreground font-medium bg-muted/30 rounded-lg px-3 py-2">{user.email}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Member Since</label>
                  <p className="text-base text-foreground font-medium bg-muted/30 rounded-lg px-3 py-2">
                    {new Date(profile.created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Subscription Card */}
            <Card className="h-full bg-gradient-to-br from-card to-card/80 border-border/50 shadow-2xl">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="bg-primary/10 rounded-full p-2">
                    <Zap className="h-5 w-5 text-primary" />
                  </div>
                  Current Plan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Badge
                      variant={
                        profile.subscription_tier === 'professional' ||
                        profile.subscription_tier === 'business'
                          ? 'default'
                          : 'secondary'
                      }
                      className="text-sm px-4 py-2"
                    >
                      {profile.subscription_tier === 'trial' && '🎯 Trial Plan'}
                      {profile.subscription_tier === 'starter' && '🚀 Starter Plan'}
                      {profile.subscription_tier === 'professional' && '⚡ Professional Plan'}
                      {profile.subscription_tier === 'business' && '🏢 Business Plan'}
                    </Badge>
                    <span className="text-sm font-semibold text-primary">
                      {profile.subscription_tier === 'starter' && '$49/month'}
                      {profile.subscription_tier === 'professional' && '$99/month'}
                      {profile.subscription_tier === 'business' && 'Contact us'}
                      {profile.subscription_tier === 'trial' && '7-day trial'}
                    </span>
                  </div>
                  <div className="bg-muted/30 rounded-lg p-4">
                    <p className="text-sm text-muted-foreground">
                      {profile.subscription_tier === 'trial' && '🎯 7-day trial with 500 messages'}
                      {profile.subscription_tier === 'starter' && '🚀 500 messages per month'}
                      {profile.subscription_tier === 'professional' && '⚡ 2,000 messages per month'}
                      {profile.subscription_tier === 'business' && '🏢 Unlimited messages & enterprise features'}
                    </p>
                  </div>
                </div>
                {profile.subscription_tier === 'professional' && (
                  <div className="p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <p className="text-sm text-blue-800 dark:text-blue-200 text-center">
                      Need enterprise features? <a href="mailto:<EMAIL>" className="underline font-medium">Contact us</a> about Business plans.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Usage & Limits Card */}
            <Card className="h-full">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="bg-primary/10 rounded-full p-2">
                    <BarChart3 className="h-5 w-5 text-primary" />
                  </div>
                  Usage & Limits
                </CardTitle>
                <CardDescription className="text-sm">Track your message usage and account limits</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Messages Used</span>
                    <span className="text-lg font-bold text-foreground">{profile.messages_this_month || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                      {usageStats.isUnlimited ? 'Plan Limit' : 'Total Available'}
                    </span>
                    <span className="text-lg font-bold text-foreground">
                      {usageStats.isUnlimited ? 'Unlimited' : usageStats.totalAvailable}
                    </span>
                  </div>
                  {!usageStats.isUnlimited && (
                    <>
                      <Progress value={usagePercentage} className="w-full h-3" />
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">
                          {usageStats.totalAvailable - profile.pulls_this_month} remaining
                        </span>
                        <span className="text-muted-foreground">
                          {Math.round(usagePercentage)}% used
                        </span>
                      </div>
                    </>
                  )}
                  {usageStats.isUnlimited && (
                    <div className="p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
                      <p className="text-sm text-green-800 dark:text-green-200 font-medium text-center">
                        ✓ Unlimited messages available
                      </p>
                    </div>
                  )}
                </div>

                {/* Contextual Messages for Limited Users */}
                {!usageStats.isUnlimited && (
                  <div className="space-y-4">
                    {profile.extra_credits > 0 && (
                      <div className="p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
                        <p className="text-sm text-green-800 dark:text-green-200 font-medium">
                          🎉 You have {profile.extra_credits} bonus messages remaining!
                        </p>
                      </div>
                    )}
                    {profile.pulls_this_month >= usageStats.totalAvailable && (
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-3">
                          ⚠️ Limit reached! You&apos;ve used all your messages for this month.
                        </p>
                        <Link href="/pricing">
                          <Button className="w-full bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 shadow-lg hover:shadow-xl transition-all duration-200">
                            {profile.subscription_tier === 'starter' ? 'Upgrade to Professional' : 'Upgrade Plan'}
                          </Button>
                        </Link>
                      </div>
                    )}
                    {profile.pulls_this_month < usageStats.totalAvailable && (
                      <div className="p-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
                          💡 Need more messages?
                          {profile.subscription_tier === 'trial' && ' Upgrade to continue using Ordrly.'}
                          {profile.subscription_tier === 'starter' && ' Upgrade to Professional for 2,000 messages per month.'}
                        </p>
                        <Link href="/pricing">
                          <Button className="w-full bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 shadow-lg hover:shadow-xl transition-all duration-200">
                            {profile.subscription_tier === 'starter' ? 'Upgrade to Professional' : 'Upgrade Plan'}
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Trial Upgrade Section */}
          {profile.subscription_tier === 'trial' && (
            <div className="flex justify-center">
              <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-blue-500/5 shadow-2xl max-w-4xl w-full">
                <CardHeader className="text-center pb-4">
                  <div className="bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <Crown className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                    Upgrade Your Trial
                  </CardTitle>
                  <CardDescription className="text-base">
                    Continue using Ordrly with full access to municipal compliance research
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl mx-auto">
                  {/* Starter Plan */}
                  <div className="border border-border rounded-xl p-6 bg-card/50 hover:bg-card/80 transition-all duration-200">
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold text-foreground mb-2">Starter</h3>
                      <div className="text-3xl font-bold text-primary mb-2">$49<span className="text-lg text-muted-foreground">/month</span></div>
                      <p className="text-sm text-muted-foreground">Perfect for homeowners</p>
                    </div>
                    <ul className="space-y-2 mb-6 text-sm">
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        500 messages per month
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        AI chat assistance
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        Municipal compliance research
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        Standard email support
                      </li>
                    </ul>
                    <Link href="/checkout/starter" className="block">
                      <Button className="w-full bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90">
                        Choose Starter
                      </Button>
                    </Link>
                  </div>

                  {/* Professional Plan */}
                  <div className="border-2 border-primary rounded-xl p-6 bg-gradient-to-br from-primary/5 to-blue-500/5 relative">
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-primary text-primary-foreground px-3 py-1">Most Popular</Badge>
                    </div>
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold text-foreground mb-2">Professional</h3>
                      <div className="text-3xl font-bold text-primary mb-2">$99<span className="text-lg text-muted-foreground">/month</span></div>
                      <p className="text-sm text-muted-foreground">For contractors & frequent users</p>
                    </div>
                    <ul className="space-y-2 mb-6 text-sm">
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        2,000 messages per month
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        AI chat assistance
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        Priority email support
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        Enhanced source analysis
                      </li>
                    </ul>
                    <Link href="/checkout/professional" className="block">
                      <Button className="w-full bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90">
                        Choose Professional
                      </Button>
                    </Link>
                  </div>
                </div>

                  <div className="text-center p-4 bg-muted/30 rounded-lg">
                    <p className="text-sm text-muted-foreground mb-2">
                      💡 <strong>Need more time to decide?</strong> Your trial includes full Starter plan access.
                    </p>
                    <p className="text-xs text-muted-foreground">
                      No credit card required during trial • Cancel anytime
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 gap-6 mb-12">
            <Link href="/chat">
              <Button size="lg" className="w-full h-16 text-lg bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 shadow-xl hover:shadow-2xl transition-all duration-200 hover:scale-[1.02]">
                <MessageCircle className="h-6 w-6 mr-3" />
                Start AI Chat
              </Button>
            </Link>
          </div>



          {/* Billing Section */}
          {profile.subscription_tier !== 'trial' && (
            <Card className="border border-gray-700">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-base">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Billing Management
                  </div>
                  <Button
                    onClick={refreshSubscriptionStatus}
                    disabled={refreshing}
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                  >
                    <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
                  </Button>
                </CardTitle>
                <CardDescription className="text-sm">Manage your subscription and billing details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border">
                  <div>
                    <p className="font-medium text-sm">
                      {profile.subscription_tier === 'starter' && 'Starter Plan'}
                      {profile.subscription_tier === 'professional' && 'Professional Plan'}
                      {profile.subscription_tier === 'business' && 'Business Plan'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {profile.subscription_tier === 'starter' && '$49/month'}
                      {profile.subscription_tier === 'professional' && '$99/month'}
                      {profile.subscription_tier === 'business' && 'Custom pricing'}
                      {' • '}
                      {profile.cancel_at_period_end ? 'Cancels at period end' : 'Active'}
                    </p>
                    {profile.cancel_at_period_end && profile.subscription_period_end && (
                      <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                        Access until {(() => {
                          const date = new Date(profile.subscription_period_end)
                          return isNaN(date.getTime()) ? 'period end' : date.toLocaleDateString()
                        })()}
                      </p>
                    )}
                  </div>
                  <Badge
                    variant={profile.cancel_at_period_end ? "secondary" : "default"}
                    className={`text-xs ${profile.cancel_at_period_end ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''}`}
                  >
                    {profile.cancel_at_period_end ? 'Ending' : 'Active'}
                  </Badge>
                </div>

                {profile.cancel_at_period_end && (
                  <div className="p-3 bg-orange-50 dark:bg-orange-950 border border-orange-200 dark:border-orange-800 rounded-lg">
                    <p className="text-sm text-orange-800 dark:text-orange-200 font-medium mb-2">
                      ⚠️ Subscription Ending
                    </p>
                    <p className="text-xs text-orange-700 dark:text-orange-300">
                      Your subscription will end on {profile.subscription_period_end ? (() => {
                        const date = new Date(profile.subscription_period_end)
                        return isNaN(date.getTime()) ? 'the current period end' : date.toLocaleDateString()
                      })() : 'the current period end'}.
                      You'll still have access to Pro features until then. Use "Manage Billing" below to reactivate.
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  <BillingPortalButton />
                  <p className="text-xs text-gray-400">
                    Redirects to Stripe billing portal
                  </p>
                </div>
              </CardContent>
            </Card>
          )}



          {/* Additional Settings */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Settings className="h-4 w-4" />
                  Referral Program
                </CardTitle>
                <CardDescription className="text-sm">Invite friends and earn bonus messages</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Earn 25 extra messages for each verified signup!
                    <span className="text-xs text-gray-400 ml-1" title="Friend must confirm their email">ⓘ</span>
                  </p>
                  {profile.extra_credits > 0 && (
                    <div className="p-2 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-md">
                      <p className="text-sm text-green-800 dark:text-green-200 font-medium">
                        You have {profile.extra_credits} bonus messages
                      </p>
                    </div>
                  )}
                  <Link
                    href="/account/referrals"
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-border bg-card hover:bg-muted/50 text-card-foreground shadow-sm hover:shadow-md h-10 px-4 w-full"
                  >
                    Manage Referrals
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Shield className="h-4 w-4" />
                  Privacy & Data Control
                </CardTitle>
                <CardDescription className="text-sm">Manage your privacy settings and data</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link
                    href="/account/privacy"
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-border bg-card hover:bg-muted/50 text-card-foreground shadow-sm hover:shadow-md h-10 px-4 w-full"
                  >
                    Privacy Settings
                  </Link>
                  <Link
                    href="/account/data"
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-border bg-card hover:bg-muted/50 text-card-foreground shadow-sm hover:shadow-md h-10 px-4 w-full"
                  >
                    Export & Delete Data
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>


      </div>
    </div>
  )
}

export default function AccountPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background py-8 px-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading account...</p>
        </div>
      </div>
    }>
      <AccountPageContent />
    </Suspense>
  )
}
