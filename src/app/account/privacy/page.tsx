'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { PrivacySettings } from '@/components/privacy/PrivacySettings'
import { BulkActions } from '@/components/privacy/BulkActions'
import { Ref<PERSON><PERSON><PERSON>ler } from '@/components/auth/RefreshHandler'
import { Shield, Download, Trash2 } from 'lucide-react'

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

function PrivacyPageContent() {
  const [exporting, setExporting] = useState(false)
  const [bulkDeleting, setBulkDeleting] = useState<'searches' | 'chats' | 'all' | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClient()

  // Trust middleware for auth
  useEffect(() => {
    setLoading(false)
  }, [])

  const handleExport = async (exportType: 'full' | 'searches' | 'chats') => {
    try {
      setExporting(true)
      setError(null)

      const response = await fetch('/api/privacy/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          export_type: exportType,
          file_format: 'json'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to export data')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ordrly-${exportType}-export-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      setSuccess(`${exportType} data exported successfully`)
    } catch (err) {
      console.error('Export error:', err)
      setError('Failed to export data')
    } finally {
      setExporting(false)
    }
  }

  const handleBulkDelete = async (deleteType: 'searches' | 'chats' | 'all') => {
    const confirmMessage = {
      searches: 'Are you sure you want to delete ALL your search history? This action cannot be undone.',
      chats: 'Are you sure you want to delete ALL your chat conversations? This action cannot be undone.',
      all: 'Are you sure you want to delete ALL your history data (searches and chats)? This action cannot be undone.'
    }

    if (!confirm(confirmMessage[deleteType])) {
      return
    }

    try {
      setBulkDeleting(deleteType)
      setError(null)

      const response = await fetch('/api/privacy/bulk-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ delete_type: deleteType })
      })

      if (!response.ok) {
        throw new Error('Failed to delete data')
      }

      setSuccess(`${deleteType} data deleted successfully`)
    } catch (err) {
      console.error('Bulk delete error:', err)
      setError('Failed to delete data')
    } finally {
      setBulkDeleting(null)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-3xl shadow-2xl p-12">
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-6"></div>
                <p className="text-muted-foreground text-lg">Loading privacy settings...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 py-16 px-4">
      <RefreshHandler />
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="bg-primary/10 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-8">
            <Shield className="h-12 w-12 text-primary" />
          </div>
          <h1 className="text-5xl font-bold text-foreground mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
            Privacy & Data Control
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-4">
            Manage your data retention preferences, export your information, or delete your history
          </p>
          <p className="text-base text-muted-foreground/80 max-w-2xl mx-auto">
            You have complete control over your data and privacy settings with Ordrly
          </p>
        </div>

        <div className="bg-gradient-to-r from-card/80 to-card/60 backdrop-blur-sm border border-border/50 rounded-3xl shadow-2xl p-8 animate-slide-up">
          {/* Status Messages */}
          {error && (
            <div className="bg-gradient-to-r from-destructive/10 to-destructive/5 border border-destructive/20 rounded-2xl p-6 mb-8 shadow-lg">
              <div className="flex items-center">
                <div className="bg-destructive/20 rounded-full p-2 mr-3">
                  <svg className="h-5 w-5 text-destructive" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-destructive font-medium">{error}</p>
              </div>
            </div>
          )}

          {success && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border border-green-200 dark:border-green-800 rounded-2xl p-6 mb-8 shadow-lg">
              <div className="flex items-center">
                <div className="bg-green-100 dark:bg-green-900/50 rounded-full p-2 mr-3">
                  <svg className="h-5 w-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-green-800 dark:text-green-200 font-medium">{success}</p>
              </div>
            </div>
          )}

          {/* Privacy Settings and Bulk Actions */}
          <div className="space-y-12">
            <PrivacySettings />
            <BulkActions
              onExport={handleExport}
              onBulkDelete={handleBulkDelete}
              exporting={exporting}
              bulkDeleting={bulkDeleting}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default function PrivacyPage() {
  return <PrivacyPageContent />
}
