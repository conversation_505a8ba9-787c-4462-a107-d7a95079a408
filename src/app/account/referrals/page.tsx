import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import ReferralDashboard from '@/components/referrals/ReferralDashboard'

export default async function ReferralsPage() {
  const supabase = await createServerClient()
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  return (
    <div className="min-h-screen bg-background py-16 px-4">
      <div className="max-w-7xl mx-auto">
        <ReferralDashboard />
      </div>
    </div>
  )
}
