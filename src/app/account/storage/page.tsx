'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

import { Database, HardDrive, Archive, Trash2, AlertTriangle } from 'lucide-react'

interface StorageUsage {
  storage_limit: number
  total_searches: number
  total_usage: number
  percentage: number
  tier: string
  breakdown: {
    searches: number
    chats: number
    saved_searches: number
    total_items: number
  }
  needs_archival: boolean
  archival_recommended: boolean
  last_updated: string
}

export default function StoragePage() {
  const [loading, setLoading] = useState(true)
  const [storageUsage, setStorageUsage] = useState<StorageUsage | null>(null)
  const router = useRouter()
  const supabase = createClient()

  const loadStorageUsage = async () => {
    try {
      const response = await fetch('/api/storage/usage')
      if (response.ok) {
        const data = await response.json()
        setStorageUsage(data)
      }
    } catch (error) {
      console.error('Failed to load storage usage:', error)
    } finally {
      setLoading(false)
    }
  }

  // Trust middleware for auth - just load data
  useEffect(() => {
    loadStorageUsage()
  }, [loadStorageUsage])

  const refreshUsage = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/storage/usage', { method: 'POST' })
      if (response.ok) {
        await loadStorageUsage()
      }
    } catch (error) {
      console.error('Failed to refresh storage usage:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStorageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-orange-500'
    if (percentage >= 50) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getStorageStatus = (percentage: number) => {
    if (percentage >= 90) return { label: 'Critical', color: 'text-red-600' }
    if (percentage >= 75) return { label: 'High', color: 'text-orange-600' }
    if (percentage >= 50) return { label: 'Moderate', color: 'text-yellow-600' }
    return { label: 'Good', color: 'text-green-600' }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading storage information...</p>
        </div>
      </div>
    )
  }

  if (!storageUsage) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Failed to load storage information</p>
          <Button onClick={loadStorageUsage} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  const status = getStorageStatus(storageUsage.percentage)

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Storage Usage</h1>
              <p className="text-gray-600">Monitor your data storage and usage limits. Storage limits are communicated clearly.</p>
            </div>
            <div className="flex items-center space-x-2">
              <HardDrive className="h-6 w-6 text-blue-600" />
              <Badge variant="outline" className={status.color}>
                {status.label}
              </Badge>
            </div>
          </div>

          {/* Storage Overview */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Storage Overview
              </CardTitle>
              <CardDescription>
                Current usage for your {storageUsage.tier} subscription
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Usage Bar */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      {storageUsage.total_usage} / {storageUsage.storage_limit} items
                    </span>
                    <span className="text-sm text-gray-500">
                      {storageUsage.percentage}% used
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-300 ${getStorageColor(storageUsage.percentage)}`}
                      style={{ width: `${Math.min(storageUsage.percentage, 100)}%` }}
                    ></div>
                  </div>
                </div>

                {/* Storage Breakdown */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {storageUsage.breakdown.searches}
                    </div>
                    <div className="text-sm text-gray-600">Search History</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {storageUsage.breakdown.chats}
                    </div>
                    <div className="text-sm text-gray-600">Chat Messages</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {storageUsage.breakdown.saved_searches}
                    </div>
                    <div className="text-sm text-gray-600">Saved Searches</div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-3">
                  <Button onClick={refreshUsage} variant="outline">
                    <Database className="h-4 w-4 mr-2" />
                    Refresh Usage
                  </Button>
                  <Button
                    onClick={() => router.push('/history')}
                    variant="outline"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Manage History
                  </Button>
                  {storageUsage.archival_recommended && (
                    <Button
                      onClick={() => router.push('/account/data-management')}
                      className="bg-orange-600 hover:bg-orange-700"
                    >
                      <Archive className="h-4 w-4 mr-2" />
                      Archive Data
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Storage Limits by Tier */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Storage Limits by Subscription Tier - Storage Limits Communicated</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <span className="font-medium">Free Tier</span>
                    <span className="text-sm text-gray-500 ml-2">Basic usage</span>
                  </div>
                  <Badge variant="outline">100 items</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div>
                    <span className="font-medium">Pro Tier</span>
                    <span className="text-sm text-gray-500 ml-2">Enhanced storage</span>
                  </div>
                  <Badge variant="outline">1,000 items</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                  <div>
                    <span className="font-medium">Appraiser Tier</span>
                    <span className="text-sm text-gray-500 ml-2">Professional storage</span>
                  </div>
                  <Badge variant="outline">5,000 items</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Warnings and Recommendations */}
          {(storageUsage.needs_archival || storageUsage.archival_recommended) && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-orange-900">
                      {storageUsage.needs_archival ? 'Storage Critical' : 'Archival Recommended'}
                    </h4>
                    <p className="text-sm text-orange-800 mt-1">
                      {storageUsage.needs_archival
                        ? 'Your storage is nearly full. Consider archiving old data or upgrading your plan.'
                        : 'You have a significant amount of old data. Archiving can help optimize your storage.'
                      }
                    </p>
                    <div className="mt-3 space-x-2">
                      <Button
                        size="sm"
                        onClick={() => router.push('/account/data-management')}
                        className="bg-orange-600 hover:bg-orange-700"
                      >
                        Manage Data
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => router.push('/pricing')}
                      >
                        Upgrade Plan
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Last Updated */}
          <div className="text-center text-sm text-gray-500 mt-6">
            Last updated: {new Date(storageUsage.last_updated).toLocaleString()}
          </div>
        </div>
      </div>
    </div>
  )
}
