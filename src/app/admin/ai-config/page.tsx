'use client'

import { useState, useEffect } from 'react'

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

interface AIConfig {
  model: string
  maxTokens: number
  temperature: number
  contextWindow?: number
}

interface AIConfigResponse {
  current: AIConfig
  isValidModel: boolean
  recommendations: {
    maxTokensRecommended: number
    temperatureRecommended: number
    costEfficiency: string
    useCase: string
  }
  availableModels: string[]
  environmentVariables: {
    OPENAI_MODEL: string
    OPENAI_MAX_TOKENS: string
    OPENAI_TEMPERATURE: string
  }
}

export default function AIConfigPage() {
  const [config, setConfig] = useState<AIConfigResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/ai-config')
      if (!response.ok) throw new Error('Failed to fetch config')
      const data = await response.json()
      setConfig(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">Loading AI configuration...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Error: {error}
          </div>
        </div>
      </div>
    )
  }

  if (!config) return null

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">🧠 AI Configuration</h1>

          {/* Current Configuration */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Current Configuration</h2>

            {/* Massive Context Window Alert */}
            {config.current.contextWindow && config.current.contextWindow > 500000 && (
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 p-4 rounded-lg mb-4">
                <div className="flex items-center">
                  <span className="text-2xl mr-2">🚀</span>
                  <div>
                    <div className="font-semibold text-yellow-800">Massive Context Window Detected!</div>
                    <div className="text-sm text-yellow-700">
                      Your model can process {(config.current.contextWindow / 1000000).toFixed(1)}M+ tokens
                      (~{(config.current.contextWindow * 4 / 1000000).toFixed(1)}M characters) in a single request
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm text-blue-600 font-medium">Model</div>
                <div className="text-lg font-bold text-blue-900">{config.current.model}</div>
                <div className={`text-xs mt-1 ${config.isValidModel ? 'text-green-600' : 'text-red-600'}`}>
                  {config.isValidModel ? '✅ Valid' : '❌ Invalid'}
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-sm text-green-600 font-medium">Max Tokens</div>
                <div className="text-lg font-bold text-green-900">{config.current.maxTokens.toLocaleString()}</div>
                <div className="text-xs text-green-600 mt-1">Output Limit</div>
              </div>

              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="text-sm text-orange-600 font-medium">Context Window</div>
                <div className="text-lg font-bold text-orange-900">
                  {config.current.contextWindow ? config.current.contextWindow.toLocaleString() : 'Unknown'}
                </div>
                <div className="text-xs text-orange-600 mt-1">Input Capacity</div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-sm text-purple-600 font-medium">Temperature</div>
                <div className="text-lg font-bold text-purple-900">{config.current.temperature}</div>
                <div className="text-xs text-purple-600 mt-1">Creativity Level</div>
              </div>
            </div>
          </div>

          {/* Recommendations */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Model Recommendations</h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Cost Efficiency</div>
                  <div className={`font-semibold ${
                    config.recommendations.costEfficiency === 'high' ? 'text-green-600' :
                    config.recommendations.costEfficiency === 'medium' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {config.recommendations.costEfficiency.toUpperCase()}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Recommended Max Tokens</div>
                  <div className="font-semibold text-gray-900">
                    {config.recommendations.maxTokensRecommended.toLocaleString()}
                  </div>
                </div>
              </div>
              <div className="mt-3">
                <div className="text-sm text-gray-600">Use Case</div>
                <div className="text-gray-900">{config.recommendations.useCase}</div>
              </div>
            </div>
          </div>

          {/* Available Models */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Available Models</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {config.availableModels.map((model) => (
                <div
                  key={model}
                  className={`p-3 rounded-lg border-2 ${
                    model === config.current.model
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{model}</div>
                  {model === config.current.model && (
                    <div className="text-xs text-blue-600 mt-1">Currently Active</div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Environment Variables */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Environment Variables</h2>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
              <div>OPENAI_MODEL={config.environmentVariables.OPENAI_MODEL}</div>
              <div>OPENAI_MAX_TOKENS={config.environmentVariables.OPENAI_MAX_TOKENS}</div>
              <div>OPENAI_TEMPERATURE={config.environmentVariables.OPENAI_TEMPERATURE}</div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">💡 How to Update Configuration</h3>
            <ol className="list-decimal list-inside text-yellow-700 space-y-1 text-sm">
              <li>Edit the <code className="bg-yellow-100 px-1 rounded">.env.local</code> file in your project root</li>
              <li>Update the desired environment variables (OPENAI_MODEL, OPENAI_MAX_TOKENS, OPENAI_TEMPERATURE)</li>
              <li>Restart the development server (<code className="bg-yellow-100 px-1 rounded">npm run dev</code>)</li>
              <li>Refresh this page to verify the changes</li>
            </ol>
          </div>

          {/* Refresh Button */}
          <div className="mt-6 text-center">
            <button
              onClick={fetchConfig}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              🔄 Refresh Configuration
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
