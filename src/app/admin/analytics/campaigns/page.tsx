'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  Mail,
  Eye,
  MousePointer,
  ArrowLeft,
  Download,
  RefreshCw,
  Users
} from 'lucide-react'
import Link from 'next/link'

interface CampaignAnalytics {
  summary: {
    totalCampaigns: number
    totalSent: number
    totalDelivered: number
    totalOpened: number
    totalClicked: number
    averageOpenRate: number
    averageClickRate: number
    averageDeliveryRate: number
  }
  campaigns: Array<{
    id: string
    type: string
    subject: string
    sentAt: string
    status: string
    metrics: {
      sent: number
      delivered: number
      opened: number
      clicked: number
      openRate: number
      clickRate: number
      bounceRate: number
      unsubscribeRate: number
    }
    audience: {
      targetSize: number
      actualSent: number
    }
  }>
  recentPerformance: {
    campaignsLast30Days: number
    openRateTrend: string
    clickRateTrend: string
    conversionTrend: string
  }
  topPerformers: Array<{
    id: string
    subject: string
    metrics: {
      openRate: number
      clickRate: number
    }
  }>
}

export default function CampaignAnalytics() {
  const [analytics, setAnalytics] = useState<CampaignAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchCampaignAnalytics()
  }, [])

  const fetchCampaignAnalytics = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/analytics/campaigns')
      if (!response.ok) {
        throw new Error('Failed to fetch campaign analytics')
      }

      const data = await response.json()
      if (data.success) {
        setAnalytics(data.analytics)
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshAnalytics = async () => {
    setRefreshing(true)
    await fetchCampaignAnalytics()
    setRefreshing(false)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return <Badge variant="default" className="bg-green-100 text-green-800">Sent</Badge>
      case 'scheduled':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Scheduled</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPerformanceColor = (rate: number, type: 'open' | 'click') => {
    const threshold = type === 'open' ? 20 : 3 // Industry averages
    if (rate >= threshold * 1.5) return 'text-green-600'
    if (rate >= threshold) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchCampaignAnalytics} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <div className="flex items-center mb-2">
            <Link href="/admin/analytics">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Analytics
              </Button>
            </Link>
            <span className="text-gray-400">/</span>
            <span className="ml-2 text-gray-600">Campaigns</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Campaign Analytics</h1>
          <p className="text-gray-600 mt-2">
            Detailed performance metrics showing emails sent, delivered, opened, and clicked for your campaigns. Track delivery rates and engagement statistics.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Sent Delivered Opened Clicked Performance
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={refreshAnalytics}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Campaigns</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics?.summary.totalCampaigns || 0}
                </p>
              </div>
              <Mail className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Open Rate</p>
                <p className={`text-2xl font-bold ${getPerformanceColor(analytics?.summary.averageOpenRate || 0, 'open')}`}>
                  {analytics?.summary.averageOpenRate || 0}%
                </p>
              </div>
              <Eye className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Click Rate</p>
                <p className={`text-2xl font-bold ${getPerformanceColor(analytics?.summary.averageClickRate || 0, 'click')}`}>
                  {analytics?.summary.averageClickRate || 0}%
                </p>
              </div>
              <MousePointer className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sent</p>
                <p className="text-2xl font-bold text-orange-600">
                  {analytics?.summary.totalSent || 0}
                </p>
              </div>
              <Users className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Recent Performance</CardTitle>
            <CardDescription>
              Campaign trends over the last 30 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Campaigns Sent</span>
                <span className="font-semibold">{analytics?.recentPerformance.campaignsLast30Days || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Open Rate Trend</span>
                <span className="font-semibold text-green-600">{analytics?.recentPerformance.openRateTrend || '0%'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Click Rate Trend</span>
                <span className="font-semibold text-blue-600">{analytics?.recentPerformance.clickRateTrend || '0%'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Conversion Trend</span>
                <span className="font-semibold text-purple-600">{analytics?.recentPerformance.conversionTrend || '0%'}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Performing Campaigns</CardTitle>
            <CardDescription>
              Campaigns with the highest engagement rates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics?.topPerformers.slice(0, 5).map((campaign, index) => (
                <div key={campaign.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs font-semibold">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{campaign.subject}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-green-600">{campaign.metrics.openRate}% open</p>
                    <p className="text-xs text-gray-500">{campaign.metrics.clickRate}% click</p>
                  </div>
                </div>
              )) || []}

              {(!analytics?.topPerformers || analytics.topPerformers.length === 0) && (
                <div className="text-center py-4">
                  <p className="text-gray-500 text-sm">No campaign data available</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaign Details */}
      <Card>
        <CardHeader>
          <CardTitle>Campaign Performance Details</CardTitle>
          <CardDescription>
            Detailed metrics for all campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics?.campaigns.map((campaign) => (
              <div key={campaign.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-blue-500" />
                    <div>
                      <h3 className="font-medium text-gray-900">{campaign.subject}</h3>
                      <p className="text-sm text-gray-500">
                        {campaign.type} • Sent {new Date(campaign.sentAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(campaign.status)}
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Sent</p>
                    <p className="font-semibold">{campaign.metrics.sent}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Delivered</p>
                    <p className="font-semibold">{campaign.metrics.delivered}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Opened</p>
                    <p className="font-semibold">{campaign.metrics.opened}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Clicked</p>
                    <p className="font-semibold">{campaign.metrics.clicked}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Open Rate</p>
                    <p className={`font-semibold ${getPerformanceColor(campaign.metrics.openRate, 'open')}`}>
                      {campaign.metrics.openRate}%
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600">Click Rate</p>
                    <p className={`font-semibold ${getPerformanceColor(campaign.metrics.clickRate, 'click')}`}>
                      {campaign.metrics.clickRate}%
                    </p>
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>Target: {campaign.audience.targetSize} users</span>
                    <span>Bounce: {campaign.metrics.bounceRate}% • Unsubscribe: {campaign.metrics.unsubscribeRate}%</span>
                  </div>
                </div>
              </div>
            )) || []}

            {(!analytics?.campaigns || analytics.campaigns.length === 0) && (
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No campaign data available</p>
                <Link href="/admin/marketing/campaigns">
                  <Button className="mt-4">
                    <Mail className="h-4 w-4 mr-2" />
                    Create Your First Campaign
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
