'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  TrendingUp,
  Clock,
  Activity,
  ArrowLeft,
  Download,
  RefreshCw,
  Search,
  MessageCircle,
  Eye
} from 'lucide-react'
import Link from 'next/link'

interface EngagementAnalytics {
  overview: {
    totalUsers: number
    activeUsers: number
    newUsers: number
    retentionRate: number
    churnRate: number
    avgLifetimeValue: number
  }
  userActivity: {
    dailyActiveUsers: number
    weeklyActiveUsers: number
    monthlyActiveUsers: number
    averageSessionDuration: string
    bounceRate: number
  }
  featureUsage: {
    totalSearches: number
    totalChats: number
    avgSearchesPerUser: number
    avgChatsPerUser: number
    mostUsedFeatures: Array<{
      feature: string
      usage: number
      percentage: number
    }>
  }
  tierEngagement: Array<{
    tier: string
    totalUsers: number
    activeUsers: number
    engagementRate: number
  }>
  cohortAnalysis: {
    week1Retention: number
    week2Retention: number
    week4Retention: number
    month3Retention: number
    month6Retention: number
  }
  insights: string[]
}

export default function EngagementAnalytics() {
  const [analytics, setAnalytics] = useState<EngagementAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchEngagementAnalytics()
  }, [])

  const fetchEngagementAnalytics = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/analytics/engagement')
      if (!response.ok) {
        throw new Error('Failed to fetch engagement analytics')
      }

      const data = await response.json()
      if (data.success) {
        setAnalytics(data.analytics)
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshAnalytics = async () => {
    setRefreshing(true)
    await fetchEngagementAnalytics()
    setRefreshing(false)
  }

  const getTierBadge = (tier: string) => {
    switch (tier) {
      case 'free':
        return <Badge variant="outline">Free</Badge>
      case 'pro':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Pro</Badge>
      case 'appraiser':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Appraiser</Badge>
      default:
        return <Badge variant="outline">{tier}</Badge>
    }
  }

  const getEngagementColor = (rate: number) => {
    if (rate >= 70) return 'text-green-600'
    if (rate >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchEngagementAnalytics} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <div className="flex items-center mb-2">
            <Link href="/admin/analytics">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Analytics
              </Button>
            </Link>
            <span className="text-gray-400">/</span>
            <span className="ml-2 text-gray-600">Engagement</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">User Engagement Analytics</h1>
          <p className="text-gray-600 mt-2">
            Track active users, retention rates, churn analysis, lifetime value, and feature usage patterns. Monitor user behavior and calculate customer lifetime value metrics.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Active Users Retention Churn Lifetime Value
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={refreshAnalytics}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics?.overview.totalUsers || 0}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-green-600">
                  {analytics?.overview.activeUsers || 0}
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Retention Rate</p>
                <p className={`text-2xl font-bold ${getEngagementColor(analytics?.overview.retentionRate || 0)}`}>
                  {analytics?.overview.retentionRate || 0}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Session</p>
                <p className="text-2xl font-bold text-orange-600">
                  {analytics?.userActivity.averageSessionDuration || '0 min'}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Activity & Feature Usage */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>User Activity</CardTitle>
            <CardDescription>
              Daily, weekly, and monthly active user metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Daily Active Users</span>
                <span className="font-semibold">{analytics?.userActivity.dailyActiveUsers || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Weekly Active Users</span>
                <span className="font-semibold">{analytics?.userActivity.weeklyActiveUsers || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Monthly Active Users</span>
                <span className="font-semibold">{analytics?.userActivity.monthlyActiveUsers || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Bounce Rate</span>
                <span className="font-semibold text-red-600">{analytics?.userActivity.bounceRate || 0}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Feature Usage</CardTitle>
            <CardDescription>
              Most popular features and usage patterns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics?.featureUsage.mostUsedFeatures.map((feature) => (
                <div key={feature.feature} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {feature.feature === 'Address Search' && <Search className="h-4 w-4 text-blue-500" />}
                    {feature.feature === 'Chat Assistant' && <MessageCircle className="h-4 w-4 text-green-500" />}
                    {feature.feature === 'Compliance Check' && <Eye className="h-4 w-4 text-purple-500" />}
                    <span className="text-sm text-gray-600">{feature.feature}</span>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold">{feature.usage}</span>
                    <span className="text-xs text-gray-500 ml-2">({feature.percentage}%)</span>
                  </div>
                </div>
              )) || []}

              <div className="pt-3 border-t border-gray-200">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Avg Searches/User</span>
                  <span className="font-semibold">{analytics?.featureUsage.avgSearchesPerUser || 0}</span>
                </div>
                <div className="flex justify-between items-center text-sm mt-2">
                  <span className="text-gray-600">Avg Chats/User</span>
                  <span className="font-semibold">{analytics?.featureUsage.avgChatsPerUser || 0}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tier Engagement & Retention */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Engagement by Subscription Tier</CardTitle>
            <CardDescription>
              User engagement rates across different subscription levels
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics?.tierEngagement.map((tier) => (
                <div key={tier.tier} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getTierBadge(tier.tier)}
                    <div>
                      <p className="font-medium text-sm capitalize">{tier.tier} Users</p>
                      <p className="text-xs text-gray-500">{tier.totalUsers} total users</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${getEngagementColor(tier.engagementRate)}`}>
                      {tier.engagementRate}%
                    </p>
                    <p className="text-xs text-gray-500">{tier.activeUsers} active</p>
                  </div>
                </div>
              )) || []}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cohort Retention Analysis</CardTitle>
            <CardDescription>
              User retention rates over time periods
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Week 1 Retention</span>
                <span className="font-semibold text-green-600">{analytics?.cohortAnalysis.week1Retention || 0}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Week 2 Retention</span>
                <span className="font-semibold text-green-600">{analytics?.cohortAnalysis.week2Retention || 0}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Month 1 Retention</span>
                <span className="font-semibold text-yellow-600">{analytics?.cohortAnalysis.week4Retention || 0}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Month 3 Retention</span>
                <span className="font-semibold text-orange-600">{analytics?.cohortAnalysis.month3Retention || 0}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Month 6 Retention</span>
                <span className="font-semibold text-red-600">{analytics?.cohortAnalysis.month6Retention || 0}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Key Insights</CardTitle>
          <CardDescription>
            AI-generated insights from your engagement data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics?.insights.map((insight, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs font-semibold mt-0.5">
                  {index + 1}
                </div>
                <p className="text-sm text-gray-700">{insight}</p>
              </div>
            )) || []}

            {(!analytics?.insights || analytics.insights.length === 0) && (
              <div className="text-center py-4">
                <p className="text-gray-500 text-sm">No insights available yet</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
