'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  Zap,
  Plus,
  Play,
  Pause,
  Edit,
  Trash2,
  Users,
  Mail,
  Clock,
  TrendingUp,
  RefreshCw,
  Settings,
  Eye
} from 'lucide-react'

interface Workflow {
  id: string
  name: string
  description: string
  trigger: {
    type: string
    conditions: Record<string, unknown>
  }
  actions: Array<{
    type: string
    template?: string
    delay: number
  }>
  status: string
  stats: {
    triggered: number
    completed: number
    conversion_rate: number
  }
  created_at: string
  updated_at: string
}

interface WorkflowData {
  workflows: Workflow[]
  summary: {
    total: number
    active: number
    draft: number
    paused: number
  }
  performance: {
    totalTriggered: number
    totalCompleted: number
    averageConversionRate: number
  }
}

export default function MarketingAutomation() {
  const [workflowData, setWorkflowData] = useState<WorkflowData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchWorkflows()
  }, [])

  const fetchWorkflows = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/automation/workflows')
      if (!response.ok) {
        throw new Error('Failed to fetch workflows')
      }

      const data = await response.json()
      if (data.success) {
        setWorkflowData(data.workflows)
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshWorkflows = async () => {
    setRefreshing(true)
    await fetchWorkflows()
    setRefreshing(false)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
      case 'paused':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Paused</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTriggerIcon = (triggerType: string) => {
    switch (triggerType) {
      case 'user_signup':
        return <Users className="h-4 w-4" />
      case 'usage_limit_reached':
        return <TrendingUp className="h-4 w-4" />
      case 'user_inactive':
        return <Clock className="h-4 w-4" />
      case 'first_search_completed':
        return <Zap className="h-4 w-4" />
      default:
        return <Mail className="h-4 w-4" />
    }
  }

  const formatDelay = (delay: number) => {
    if (delay === 0) return 'Immediate'
    const hours = delay / (1000 * 60 * 60)
    const days = hours / 24

    if (days >= 1) {
      return `${Math.round(days)} day${Math.round(days) !== 1 ? 's' : ''}`
    } else {
      return `${Math.round(hours)} hour${Math.round(hours) !== 1 ? 's' : ''}`
    }
  }

  const filteredWorkflows = workflowData?.workflows.filter(workflow => {
    if (activeTab === 'all') return true
    return workflow.status === activeTab
  }) || []

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchWorkflows} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Marketing Automation</h1>
          <p className="text-gray-600 mt-2">
            Create and manage automated email workflows, sequences, and triggers for your campaigns. Set up automation rules and workflow sequences with custom triggers.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Automation Sequence Workflow Trigger
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={refreshWorkflows}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Workflow
          </Button>
        </div>
      </div>

      {/* Automation Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Workflows</p>
                <p className="text-2xl font-bold text-gray-900">
                  {workflowData?.summary.total || 0}
                </p>
              </div>
              <Zap className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-green-600">
                  {workflowData?.summary.active || 0}
                </p>
              </div>
              <Play className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Triggered</p>
                <p className="text-2xl font-bold text-purple-600">
                  {workflowData?.performance.totalTriggered || 0}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Conversion</p>
                <p className="text-2xl font-bold text-orange-600">
                  {workflowData?.performance.averageConversionRate || 0}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflow Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="draft">Draft</TabsTrigger>
          <TabsTrigger value="paused">Paused</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === 'all' ? 'All Workflows' :
                 activeTab === 'active' ? 'Active Workflows' :
                 activeTab === 'draft' ? 'Draft Workflows' : 'Paused Workflows'}
              </CardTitle>
              <CardDescription>
                {filteredWorkflows.length} workflow{filteredWorkflows.length !== 1 ? 's' : ''}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {filteredWorkflows.map((workflow) => (
                  <div key={workflow.id} className="border rounded-lg p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        {getTriggerIcon(workflow.trigger.type)}
                        <div>
                          <h3 className="font-medium text-gray-900">{workflow.name}</h3>
                          <p className="text-sm text-gray-500">{workflow.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(workflow.status)}
                      </div>
                    </div>

                    {/* Trigger Info */}
                    <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-sm text-blue-900 mb-2">Trigger</h4>
                      <p className="text-sm text-blue-700">
                        {workflow.trigger.type === 'user_signup' && 'When a user signs up'}
                        {workflow.trigger.type === 'usage_limit_reached' && 'When user reaches usage limit'}
                        {workflow.trigger.type === 'user_inactive' && 'When user becomes inactive'}
                        {workflow.trigger.type === 'first_search_completed' && 'When user completes first search'}
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="mb-4">
                      <h4 className="font-medium text-sm text-gray-900 mb-2">Actions</h4>
                      <div className="space-y-2">
                        {workflow.actions.map((action, index) => (
                          <div key={index} className="flex items-center space-x-3 text-sm">
                            <div className="flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-600 rounded-full text-xs font-semibold">
                              {index + 1}
                            </div>
                            <Mail className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">
                              Send &quot;{action.template}&quot; email
                            </span>
                            <span className="text-gray-400">•</span>
                            <span className="text-gray-500">
                              {formatDelay(action.delay)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Performance Stats */}
                    <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
                      <div className="text-center">
                        <p className="text-lg font-semibold text-gray-900">{workflow.stats.triggered}</p>
                        <p className="text-xs text-gray-600">Triggered</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-semibold text-gray-900">{workflow.stats.completed}</p>
                        <p className="text-xs text-gray-600">Completed</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-semibold text-green-600">{workflow.stats.conversion_rate}%</p>
                        <p className="text-xs text-gray-600">Conversion</p>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2 pt-2 border-t border-gray-200">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      {workflow.status === 'active' ? (
                        <Button variant="outline" size="sm">
                          <Pause className="h-4 w-4 mr-1" />
                          Pause
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm">
                          <Play className="h-4 w-4 mr-1" />
                          Activate
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </div>

                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="flex justify-between items-center text-xs text-gray-500">
                        <span>Created {new Date(workflow.created_at).toLocaleDateString()}</span>
                        <span>Updated {new Date(workflow.updated_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                ))}

                {filteredWorkflows.length === 0 && (
                  <div className="text-center py-8">
                    <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">
                      {activeTab === 'all' ? 'No workflows yet' : `No ${activeTab} workflows`}
                    </p>
                    <p className="text-gray-400 text-sm mt-2">
                      Create automated email sequences to engage users at the right time
                    </p>
                    <Button className="mt-4">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Workflow
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
