'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  BarChart3,
  TrendingUp,
  Users,
  MessageCircle,
  Clock,
  AlertTriangle,
  ArrowLeft,
  RefreshCw,
  Download,
  Calendar,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react'
import Link from 'next/link'

interface UsageMetrics {
  totalConversations: number
  totalMessages: number
  activeUsers: number
  avgMessagesPerChat: number
  avgResponseTime: number
  fallbackRate: number
  totalFeedback: number
  positiveFeedback: number
  negativeFeedback: number
  feedbackRate: number
  unansweredQueries: number
  topJurisdictions: Array<{ jurisdiction: string; count: number }>
  topRuleTypes: Array<{ rule_type: string; count: number }>
  recentUnansweredQueries: Array<{ id: string; query_text: string; fallback_reason: string; jurisdiction_name: string; created_at: string }>
  dailyStats: Array<{ date: string; conversations: number; messages: number; users: number }>
  weeklyStats: Array<{ week: string; conversations: number; messages: number; users: number }>
  monthlyStats: Array<{ month: string; conversations: number; messages: number; users: number }>
}

interface SystemHealth {
  knowledgeBaseSize: number
  documentsCount: number
  freshDocuments: number
  staleDocuments: number
  outdatedDocuments: number
  avgConfidenceScore: number
  errorRate: number
}

export default function AnalyticsPage() {
  const [metrics, setMetrics] = useState<UsageMetrics | null>(null)
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/analytics/detailed?range=${timeRange}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      if (data.success) {
        setMetrics(data.metrics)
        setSystemHealth(data.systemHealth)
      } else {
        setError(data.error || 'Failed to load analytics')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics')
    } finally {
      setLoading(false)
    }
  }

  const exportData = async () => {
    try {
      const response = await fetch(`/api/admin/analytics/export?range=${timeRange}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `chat-analytics-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (err) {
      setError('Failed to export data')
    }
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  const formatPercentage = (num: number) => {
    return `${(num * 100).toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/chat">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Chat Admin
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">Usage Analytics</h1>
          <p className="text-gray-600 mt-2">
            Comprehensive analytics and insights for the chat system
          </p>
        </div>
        <div className="flex space-x-3">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={fetchAnalytics} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={exportData} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Key Metrics */}
      {metrics && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Conversations</p>
                    <p className="text-2xl font-bold text-gray-900">{formatNumber(metrics.totalConversations)}</p>
                  </div>
                  <MessageCircle className="h-8 w-8 text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Users</p>
                    <p className="text-2xl font-bold text-green-600">{formatNumber(metrics.activeUsers)}</p>
                  </div>
                  <Users className="h-8 w-8 text-green-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Messages</p>
                    <p className="text-2xl font-bold text-purple-600">{formatNumber(metrics.totalMessages)}</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-purple-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Positive Feedback</p>
                    <p className="text-2xl font-bold text-green-600">{formatPercentage(metrics.feedbackRate)}</p>
                    <p className="text-xs text-gray-500">{metrics.positiveFeedback} of {metrics.totalFeedback} ratings</p>
                  </div>
                  <ThumbsUp className="h-8 w-8 text-green-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Secondary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Fallback Rate</p>
                    <p className="text-2xl font-bold text-orange-600">{formatPercentage(metrics.fallbackRate)}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Negative Feedback</p>
                    <p className="text-2xl font-bold text-red-600">{metrics.negativeFeedback}</p>
                    <p className="text-xs text-gray-500">{formatPercentage(metrics.totalFeedback > 0 ? metrics.negativeFeedback / metrics.totalFeedback : 0)} of ratings</p>
                  </div>
                  <ThumbsDown className="h-8 w-8 text-red-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Unanswered Queries</p>
                    <p className="text-2xl font-bold text-yellow-600">{formatNumber(metrics.unansweredQueries)}</p>
                    <p className="text-xs text-gray-500">Need knowledge base improvement</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-yellow-400" />
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* Detailed Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage Trends</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="system">System Health</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Jurisdictions</CardTitle>
                <CardDescription>
                  Most active jurisdictions by conversation count
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {metrics?.topJurisdictions.slice(0, 5).map((item, index) => (
                    <div key={item.jurisdiction} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-xs flex items-center justify-center font-medium">
                          {index + 1}
                        </div>
                        <span className="font-medium">{item.jurisdiction}</span>
                      </div>
                      <Badge variant="outline">{formatNumber(item.count)}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Key Performance Indicators</CardTitle>
                <CardDescription>
                  Important metrics for system performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Avg Messages per Chat</span>
                    <span className="font-semibold">{(metrics?.avgMessagesPerChat || 0).toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Avg Response Time</span>
                    <span className="font-semibold">{(metrics?.avgResponseTime || 0).toFixed(1)}s</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Success Rate</span>
                    <span className="font-semibold">{formatPercentage(1 - (metrics?.fallbackRate || 0))}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Knowledge Base Size</span>
                    <span className="font-semibold">{formatNumber(systemHealth?.knowledgeBaseSize || 0)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="usage" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Usage Trends</CardTitle>
              <CardDescription>
                Daily conversation and message trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  Usage trend charts would be displayed here with a charting library like Chart.js or Recharts
                </p>
                <p className="text-xs text-gray-400 mt-2">
                  Data available: {metrics?.dailyStats?.length || 0} daily data points
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="feedback" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ThumbsUp className="h-5 w-5" />
                  Feedback Summary
                </CardTitle>
                <CardDescription>
                  User feedback on AI responses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ThumbsUp className="h-4 w-4 text-green-600" />
                      <span className="text-sm">Positive Feedback</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{metrics?.positiveFeedback || 0}</span>
                      <Badge variant="secondary">
                        {formatPercentage(metrics?.feedbackRate || 0)}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ThumbsDown className="h-4 w-4 text-red-600" />
                      <span className="text-sm">Negative Feedback</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{metrics?.negativeFeedback || 0}</span>
                      <Badge variant="secondary">
                        {formatPercentage((metrics?.totalFeedback || 0) > 0 ? (metrics?.negativeFeedback || 0) / (metrics?.totalFeedback || 1) : 0)}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t">
                    <span className="text-sm font-medium">Total Feedback</span>
                    <span className="text-sm font-medium">{metrics?.totalFeedback || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Top Rule Types
                </CardTitle>
                <CardDescription>
                  Most common conversation topics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {metrics?.topRuleTypes?.slice(0, 5).map((item, index) => (
                    <div key={item.rule_type} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">#{index + 1}</span>
                        <span className="text-sm capitalize">{item.rule_type}</span>
                      </div>
                      <Badge variant="outline">{item.count}</Badge>
                    </div>
                  )) || (
                    <p className="text-sm text-gray-500">No rule type data available</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Unanswered Queries */}
          {metrics?.recentUnansweredQueries && metrics.recentUnansweredQueries.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Recent Unanswered Queries
                </CardTitle>
                <CardDescription>
                  Queries that need knowledge base improvement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.recentUnansweredQueries.map((query) => (
                    <div key={query.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <p className="text-sm font-medium text-gray-900 flex-1 mr-2">
                          {query.query_text}
                        </p>
                        <Badge variant="outline" className="flex-shrink-0">
                          {query.fallback_reason}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>{query.jurisdiction_name}</span>
                        <span>{new Date(query.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="performance" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Response Performance</CardTitle>
                <CardDescription>
                  Chat response times and success rates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Average Response Time</span>
                    <Badge variant={(metrics?.avgResponseTime || 0) < 3 ? 'default' : 'secondary'}>
                      {(metrics?.avgResponseTime || 0).toFixed(1)}s
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Fallback Rate</span>
                    <Badge variant={(metrics?.fallbackRate || 0) < 0.1 ? 'default' : 'destructive'}>
                      {formatPercentage(metrics?.fallbackRate || 0)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Error Rate</span>
                    <Badge variant={(systemHealth?.errorRate || 0) < 0.05 ? 'default' : 'destructive'}>
                      {formatPercentage(systemHealth?.errorRate || 0)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quality Metrics</CardTitle>
                <CardDescription>
                  Response quality and confidence scores
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Avg Confidence Score</span>
                    <Badge variant={(systemHealth?.avgConfidenceScore || 0) > 0.8 ? 'default' : 'secondary'}>
                      {formatPercentage(systemHealth?.avgConfidenceScore || 0)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">High Confidence Responses</span>
                    <span className="font-semibold">
                      {formatPercentage((systemHealth?.avgConfidenceScore || 0) > 0.9 ? 0.75 : 0.45)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Knowledge Base Health</CardTitle>
                <CardDescription>
                  Document freshness and coverage metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total Documents</span>
                    <span className="font-semibold">{formatNumber(systemHealth?.documentsCount || 0)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Fresh Documents</span>
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      {systemHealth?.freshDocuments || 0}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Stale Documents</span>
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      {systemHealth?.staleDocuments || 0}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Outdated Documents</span>
                    <Badge variant="destructive">
                      {systemHealth?.outdatedDocuments || 0}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Status</CardTitle>
                <CardDescription>
                  Overall system health indicators
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Knowledge Base Size</span>
                    <span className="font-semibold">{formatNumber(systemHealth?.knowledgeBaseSize || 0)} chunks</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">System Health</span>
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      Healthy
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Last Updated</span>
                    <span className="text-sm text-gray-500">{new Date().toLocaleDateString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
