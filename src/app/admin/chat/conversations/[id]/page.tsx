'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ArrowLeft, Download, User, Bot, Calendar, MapPin, MessageCircle } from 'lucide-react'
import { toast } from 'sonner'
import Link from 'next/link'
import { MessageContent } from '@/components/chat/MessageContent'

interface ChatConversation {
  id: string
  address: string
  rule_type: string
  jurisdiction_name: string
  created_at: string
  updated_at: string
  user_id: string
  user_email?: string
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  created_at: string
  metadata?: {
    citations?: Array<{
      title: string
      section?: string
      document_type?: string
      url?: string
      jurisdiction?: string
    }>
    confidence_score?: number
    data_source?: string
  }
}

export default function AdminConversationViewPage() {
  const [conversation, setConversation] = useState<ChatConversation | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isAdmin, setIsAdmin] = useState(false)
  const router = useRouter()
  const params = useParams()
  const conversationId = params.id as string

  useEffect(() => {
    checkAdminAccess()
  }, [])

  useEffect(() => {
    if (isAdmin && conversationId) {
      loadConversationData()
      logAdminAccess()
    }
  }, [isAdmin, conversationId])

  const checkAdminAccess = async () => {
    try {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        router.push('/login')
        return
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (profile?.role !== 'admin') {
        toast.error('Access denied: Admin privileges required')
        router.push('/')
        return
      }

      setIsAdmin(true)
    } catch (error) {
      console.error('Admin access check error:', error)
      toast.error('Failed to verify admin access')
      router.push('/')
    }
  }

  const loadConversationData = async () => {
    try {
      const supabase = createClient()
      
      // Get conversation details
      const { data: convData, error: convError } = await supabase
        .from('chat_conversations')
        .select(`
          *,
          profiles!chat_conversations_user_id_fkey(email)
        `)
        .eq('id', conversationId)
        .single()

      if (convError) throw convError

      setConversation({
        ...convData,
        user_email: convData.profiles?.email
      })

      // Get messages
      const { data: messagesData, error: messagesError } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })

      if (messagesError) throw messagesError

      setMessages(messagesData || [])
    } catch (error) {
      console.error('Failed to load conversation:', error)
      toast.error('Failed to load conversation')
      router.push('/admin/chat/conversations')
    } finally {
      setIsLoading(false)
    }
  }

  const logAdminAccess = async () => {
    try {
      const supabase = createClient()
      await supabase
        .from('admin_access_log')
        .insert({
          admin_user_id: (await supabase.auth.getUser()).data.user?.id,
          accessed_conversation_id: conversationId,
          access_type: 'view',
          ip_address: null,
          user_agent: navigator.userAgent
        })
    } catch (error) {
      console.error('Failed to log admin access:', error)
    }
  }

  const handleExport = async () => {
    try {
      const supabase = createClient()
      await supabase
        .from('admin_access_log')
        .insert({
          admin_user_id: (await supabase.auth.getUser()).data.user?.id,
          accessed_conversation_id: conversationId,
          access_type: 'export',
          ip_address: null,
          user_agent: navigator.userAgent
        })

      const response = await fetch(`/api/chat/export/${conversationId}`)
      
      if (!response.ok) {
        throw new Error('Export failed')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `chat-export-${conversationId.slice(0, 8)}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast.success('Chat exported successfully')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export chat')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner />
          <span className="ml-2 text-muted-foreground">Loading conversation...</span>
        </div>
      </div>
    )
  }

  if (!conversation) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="py-12 text-center">
            <h3 className="text-lg font-medium text-foreground mb-2">
              Conversation not found
            </h3>
            <p className="text-muted-foreground mb-4">
              The requested conversation could not be found.
            </p>
            <Link href="/admin/chat/conversations">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Conversations
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <Link href="/admin/chat/conversations">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Conversation Details
              </h1>
              <p className="text-muted-foreground">
                Admin view - ID: {conversation.id.slice(0, 8)}
              </p>
            </div>
          </div>
          <Button onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>

        {/* Conversation Info */}
        <Card>
          <CardHeader>
            <CardTitle>{conversation.address}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Jurisdiction:</span>
                <span className="font-medium">{conversation.jurisdiction_name}</span>
              </div>
              
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">User:</span>
                <span className="font-medium">{conversation.user_email || 'Unknown'}</span>
              </div>
              
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Created:</span>
                <span className="font-medium">{formatDate(conversation.created_at)}</span>
              </div>
              
              <div className="flex items-center gap-2 text-sm">
                <MessageCircle className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Messages:</span>
                <span className="font-medium">{messages.length}</span>
              </div>
            </div>
            
            <div className="mt-4">
              <Badge variant="secondary">{conversation.rule_type}</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Messages */}
      <Card>
        <CardHeader>
          <CardTitle>Conversation History</CardTitle>
        </CardHeader>
        <CardContent>
          {messages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No messages in this conversation
            </div>
          ) : (
            <div className="space-y-6">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex items-start gap-3 ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  {/* Avatar */}
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      message.role === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      {message.role === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4" />
                      )}
                    </div>
                  </div>

                  {/* Message Content */}
                  <div className={`flex-1 max-w-[80%] ${
                    message.role === 'user' ? 'text-right' : 'text-left'
                  }`}>
                    <div className={`inline-block p-4 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-foreground'
                    }`}>
                      <MessageContent
                        content={message.content}
                        role={message.role}
                        citations={[]}
                        onCitationClick={() => {}}
                        highlightedCitation={null}
                        metadata={message.metadata}
                      />
                    </div>
                    
                    <div className="mt-2 text-xs text-muted-foreground">
                      <div>{formatTime(message.created_at)}</div>
                      {message.metadata?.confidence_score && (
                        <div>Confidence: {Math.round(message.metadata.confidence_score * 100)}%</div>
                      )}
                      {message.metadata?.data_source && (
                        <div>Source: {message.metadata.data_source}</div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
