'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Search, MessageCircle, Calendar, User, MapPin, Eye, Download } from 'lucide-react'
import { toast } from 'sonner'
import Link from 'next/link'

interface ChatConversation {
  id: string
  address: string
  rule_type: string
  jurisdiction_name: string
  created_at: string
  updated_at: string
  user_id: string
  message_count: number
  user_email?: string
}

export default function AdminChatConversationsPage() {
  const [conversations, setConversations] = useState<ChatConversation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredConversations, setFilteredConversations] = useState<ChatConversation[]>([])
  const [isAdmin, setIsAdmin] = useState(false)
  const router = useRouter()

  useEffect(() => {
    checkAdminAccess()
  }, [])

  useEffect(() => {
    if (isAdmin) {
      loadConversations()
    }
  }, [isAdmin])

  useEffect(() => {
    // Filter conversations based on search term
    if (!searchTerm.trim()) {
      setFilteredConversations(conversations)
    } else {
      const filtered = conversations.filter(conv =>
        conv.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.jurisdiction_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.rule_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.user_email?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredConversations(filtered)
    }
  }, [searchTerm, conversations])

  const checkAdminAccess = async () => {
    try {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        router.push('/login')
        return
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (profile?.role !== 'admin') {
        toast.error('Access denied: Admin privileges required')
        router.push('/')
        return
      }

      setIsAdmin(true)
    } catch (error) {
      console.error('Admin access check error:', error)
      toast.error('Failed to verify admin access')
      router.push('/')
    }
  }

  const loadConversations = async () => {
    try {
      const supabase = createClient()
      
      // Get conversations with message counts and user emails
      const { data, error } = await supabase
        .from('chat_conversations')
        .select(`
          *,
          profiles!chat_conversations_user_id_fkey(email),
          chat_messages(count)
        `)
        .order('updated_at', { ascending: false })

      if (error) throw error

      const conversationsWithCounts = data?.map(conv => ({
        ...conv,
        message_count: conv.chat_messages?.[0]?.count || 0,
        user_email: conv.profiles?.email
      })) || []

      setConversations(conversationsWithCounts)
    } catch (error) {
      console.error('Failed to load conversations:', error)
      toast.error('Failed to load conversations')
    } finally {
      setIsLoading(false)
    }
  }

  const logAdminAccess = async (conversationId: string, accessType: string) => {
    try {
      const supabase = createClient()
      await supabase
        .from('admin_access_log')
        .insert({
          admin_user_id: (await supabase.auth.getUser()).data.user?.id,
          accessed_conversation_id: conversationId,
          access_type: accessType,
          ip_address: null, // Will be filled by server if available
          user_agent: navigator.userAgent
        })
    } catch (error) {
      console.error('Failed to log admin access:', error)
    }
  }

  const handleViewConversation = async (conversationId: string) => {
    await logAdminAccess(conversationId, 'view')
    router.push(`/admin/chat/conversations/${conversationId}`)
  }

  const handleExportConversation = async (conversationId: string) => {
    await logAdminAccess(conversationId, 'export')
    
    try {
      const response = await fetch(`/api/chat/export/${conversationId}`)
      
      if (!response.ok) {
        throw new Error('Export failed')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `chat-export-${conversationId.slice(0, 8)}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast.success('Chat exported successfully')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export chat')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Chat Conversations
        </h1>
        <p className="text-muted-foreground">
          Admin view of all chat conversations for audit and support purposes
        </p>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Conversations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by address, jurisdiction, rule type, or user email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setSearchTerm('')}
              disabled={!searchTerm}
            >
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Conversations List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner />
          <span className="ml-2 text-muted-foreground">Loading conversations...</span>
        </div>
      ) : filteredConversations.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              {searchTerm ? 'No matching conversations' : 'No conversations found'}
            </h3>
            <p className="text-muted-foreground">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : 'No chat conversations have been created yet'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredConversations.map((conversation) => (
            <Card key={conversation.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-start gap-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-foreground mb-2">
                          {conversation.address}
                        </h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <MapPin className="h-4 w-4" />
                            <span>{conversation.jurisdiction_name}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <User className="h-4 w-4" />
                            <span>{conversation.user_email || 'Unknown user'}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span>Created {formatDate(conversation.created_at)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <MessageCircle className="h-4 w-4" />
                            <span>{conversation.message_count} messages</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">
                            {conversation.rule_type}
                          </Badge>
                          <Badge variant="outline">
                            ID: {conversation.id.slice(0, 8)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewConversation(conversation.id)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExportConversation(conversation.id)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Summary Stats */}
      <Card className="mt-6">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-foreground">
                {conversations.length}
              </div>
              <div className="text-sm text-muted-foreground">
                Total Conversations
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-foreground">
                {conversations.reduce((sum, conv) => sum + conv.message_count, 0)}
              </div>
              <div className="text-sm text-muted-foreground">
                Total Messages
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-foreground">
                {new Set(conversations.map(conv => conv.user_id)).size}
              </div>
              <div className="text-sm text-muted-foreground">
                Unique Users
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
