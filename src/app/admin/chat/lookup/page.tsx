'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Search,
  MessageCircle,
  User,
  MapPin,
  Calendar,
  Eye,
  ArrowLeft,
  Bot
} from 'lucide-react'
import Link from 'next/link'

interface ChatSession {
  id: string
  userEmail: string
  address: string
  jurisdiction: string
  ruleType: string
  messageCount: number
  createdAt: string
  updatedAt: string
  lastMessageAt: string
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  createdAt: string
  metadata?: any
}

function ChatLookupPageContent() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState('')
  const [searchType, setSearchType] = useState<'email' | 'address'>('email')
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [loading, setLoading] = useState(false)
  const [messagesLoading, setMessagesLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Check for conversation ID in URL params
  useEffect(() => {
    const conversationId = searchParams.get('conversation')
    if (conversationId) {
      loadConversationById(conversationId)
    }
  }, [searchParams])

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setLoading(true)
    setError(null)
    setSessions([])
    setSelectedSession(null)
    setMessages([])

    try {
      const params = new URLSearchParams({
        type: searchType,
        query: searchQuery.trim()
      })

      const response = await fetch(`/api/admin/chats?${params}`)
      if (!response.ok) {
        throw new Error('Failed to search chat sessions')
      }

      const data = await response.json()
      if (data.success) {
        setSessions(data.sessions)
      } else {
        setError(data.error || 'Search failed')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }

  const loadConversationById = async (conversationId: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/admin/chats?conversation=${conversationId}`)
      if (!response.ok) {
        throw new Error('Failed to load conversation')
      }

      const data = await response.json()
      if (data.success && data.session) {
        setSelectedSession(data.session)
        await loadMessages(conversationId)
      } else {
        setError('Conversation not found')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load conversation')
    } finally {
      setLoading(false)
    }
  }

  const loadMessages = async (conversationId: string) => {
    setMessagesLoading(true)

    try {
      const response = await fetch(`/api/admin/chats/${conversationId}/messages`)
      if (!response.ok) {
        throw new Error('Failed to load messages')
      }

      const data = await response.json()
      if (data.success) {
        setMessages(data.messages)
      }

    } catch (err) {
      console.error('Failed to load messages:', err)
    } finally {
      setMessagesLoading(false)
    }
  }

  const handleSessionSelect = async (session: ChatSession) => {
    setSelectedSession(session)
    await loadMessages(session.id)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/chat">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Chat Admin
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Chat Session Lookup</h1>
          <p className="text-gray-600 mt-2">
            Search and view user chat sessions for support and troubleshooting
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Search Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Search Chat Sessions</CardTitle>
              <CardDescription>
                Find user conversations by email or address
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Button
                  variant={searchType === 'email' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSearchType('email')}
                >
                  Email
                </Button>
                <Button
                  variant={searchType === 'address' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSearchType('address')}
                >
                  Address
                </Button>
              </div>

              <div className="flex space-x-2">
                <Input
                  placeholder={searchType === 'email' ? '<EMAIL>' : '123 Main St'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button onClick={handleSearch} disabled={loading}>
                  <Search className="h-4 w-4" />
                </Button>
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded">
                  {error}
                </div>
              )}

              {/* Search Results */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {sessions.map((session) => (
                  <div
                    key={session.id}
                    className={`p-3 border rounded cursor-pointer hover:bg-gray-50 ${
                      selectedSession?.id === session.id ? 'border-blue-500 bg-blue-50' : ''
                    }`}
                    onClick={() => handleSessionSelect(session)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {session.address}
                        </p>
                        <p className="text-xs text-gray-500">
                          {session.userEmail}
                        </p>
                        <p className="text-xs text-gray-400">
                          {session.messageCount} messages • {formatDate(session.updatedAt)}
                        </p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {session.ruleType}
                      </Badge>
                    </div>
                  </div>
                ))}

                {sessions.length === 0 && !loading && searchQuery && (
                  <div className="text-center py-8 text-gray-500">
                    <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No chat sessions found</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Conversation View */}
        <div className="lg:col-span-2">
          {selectedSession ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <MessageCircle className="h-5 w-5 mr-2" />
                      {selectedSession.address}
                    </CardTitle>
                    <CardDescription>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {selectedSession.userEmail}
                        </span>
                        <span className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {selectedSession.jurisdiction}
                        </span>
                        <span className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(selectedSession.createdAt)}
                        </span>
                      </div>
                    </CardDescription>
                  </div>
                  <Badge variant="secondary">
                    {selectedSession.messageCount} messages
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                {messagesLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex items-start gap-3 ${
                          message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                        }`}
                      >
                        <div className="flex-shrink-0">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            message.role === 'user' 
                              ? 'bg-blue-100 text-blue-600' 
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            {message.role === 'user' ? (
                              <User className="h-4 w-4" />
                            ) : (
                              <Bot className="h-4 w-4" />
                            )}
                          </div>
                        </div>
                        <div className={`flex-1 ${message.role === 'user' ? 'text-right' : 'text-left'}`}>
                          <div className={`inline-block p-3 rounded-lg max-w-[80%] ${
                            message.role === 'user'
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-100 text-gray-900'
                          }`}>
                            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatDate(message.createdAt)}
                          </p>
                        </div>
                      </div>
                    ))}

                    {messages.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p>No messages in this conversation</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center py-16">
                <div className="text-center">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Select a Chat Session
                  </h3>
                  <p className="text-gray-500">
                    Search for a user or address to view their chat conversations
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default function ChatLookupPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading chat lookup...</p>
        </div>
      </div>
    }>
      <ChatLookupPageContent />
    </Suspense>
  )
}
