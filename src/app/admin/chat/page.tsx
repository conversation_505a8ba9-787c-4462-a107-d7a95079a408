'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  MessageCircle,
  Search,
  Database,
  FileText,
  BarChart3,
  RefreshCw,
  Users,
  Settings,
  Eye,
  Upload
} from 'lucide-react'
import Link from 'next/link'

interface ChatStats {
  totalConversations: number
  activeUsers: number
  messagesThisWeek: number
  avgMessagesPerChat: number
  fallbackRate: number
  documentsInKB: number
}

interface RecentChat {
  id: string
  userEmail: string
  address: string
  jurisdiction: string
  messageCount: number
  lastActivity: string
  status: string
}

export default function ChatAdminDashboard() {
  const [stats, setStats] = useState<ChatStats | null>(null)
  const [recentChats, setRecentChats] = useState<RecentChat[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchChatData()
  }, [])

  const fetchChatData = async () => {
    try {
      setLoading(true)

      // Fetch chat analytics
      const analyticsResponse = await fetch('/api/admin/analytics')
      if (!analyticsResponse.ok) {
        throw new Error('Failed to fetch chat analytics')
      }
      const analyticsData = await analyticsResponse.json()

      if (analyticsData.success) {
        setStats(analyticsData.stats)
        setRecentChats(analyticsData.recentChats || [])
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Completed</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchChatData} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Chat Management</h1>
          <p className="text-gray-600 mt-2">
            Manage Pro-tier Chat features, knowledge base, and user sessions. Monitor system health and performance.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Admin Tools for Chat & Data Management
          </div>
        </div>
        <div className="flex space-x-3">
          <Link href="/admin/chat/lookup">
            <Button variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Chat Lookup
            </Button>
          </Link>
          <Link href="/admin/chat/documents">
            <Button variant="outline">
              <Database className="h-4 w-4 mr-2" />
              Knowledge Base
            </Button>
          </Link>
          <Link href="/admin/chat/reindex">
            <Button>
              <RefreshCw className="h-4 w-4 mr-2" />
              Re-index
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Conversations</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.totalConversations || 0}</p>
              </div>
              <MessageCircle className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-green-600">{stats?.activeUsers || 0}</p>
              </div>
              <Users className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Messages This Week</p>
                <p className="text-2xl font-bold text-purple-600">{stats?.messagesThisWeek || 0}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Knowledge Base</p>
                <p className="text-2xl font-bold text-orange-600">{stats?.documentsInKB || 0}</p>
              </div>
              <Database className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chat Management Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sessions">Recent Sessions</TabsTrigger>
          <TabsTrigger value="knowledge">Knowledge Base</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
                <CardDescription>
                  Key chat system metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Avg Messages/Chat</span>
                    <span className="font-semibold">{stats?.avgMessagesPerChat || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Fallback Rate</span>
                    <span className="font-semibold">{stats?.fallbackRate || 0}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Documents in KB</span>
                    <span className="font-semibold">{stats?.documentsInKB || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common chat management tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/admin/chat/lookup">
                    <Button variant="outline" className="w-full justify-start">
                      <Search className="h-4 w-4 mr-2" />
                      Search User Chats
                    </Button>
                  </Link>
                  <Link href="/admin/chat/documents">
                    <Button variant="outline" className="w-full justify-start">
                      <Database className="h-4 w-4 mr-2" />
                      Manage Documents
                    </Button>
                  </Link>
                  <Link href="/admin/chat/templates">
                    <Button variant="outline" className="w-full justify-start">
                      <FileText className="h-4 w-4 mr-2" />
                      Prompt Templates
                    </Button>
                  </Link>
                  <Link href="/admin/chat/reindex">
                    <Button variant="outline" className="w-full justify-start">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Re-index Knowledge Base
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Chat Sessions</CardTitle>
              <CardDescription>
                Latest user chat sessions and activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentChats.slice(0, 5).map((chat) => (
                  <div key={chat.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <MessageCircle className="h-5 w-5 text-blue-500" />
                      <div>
                        <h3 className="font-medium text-gray-900">{chat.address}</h3>
                        <p className="text-sm text-gray-500">
                          {chat.userEmail} • {chat.jurisdiction}
                        </p>
                        <p className="text-xs text-gray-400">
                          {chat.messageCount} messages • Last active {new Date(chat.lastActivity).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(chat.status)}
                      <Link href={`/admin/chat/lookup?conversation=${chat.id}`}>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}

                {recentChats.length === 0 && (
                  <div className="text-center py-8">
                    <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No recent chat sessions</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="knowledge" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Knowledge Base Management</CardTitle>
              <CardDescription>
                Manage documents and data sources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">Manage your compliance knowledge base</p>
                <div className="flex justify-center space-x-3">
                  <Link href="/admin/chat/documents">
                    <Button variant="outline">
                      <Database className="h-4 w-4 mr-2" />
                      View Documents
                    </Button>
                  </Link>
                  <Link href="/admin/chat/upload">
                    <Button>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Document
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Chat Settings</CardTitle>
              <CardDescription>
                Configure chat system preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Prompt Templates</h4>
                    <p className="text-sm text-gray-500">Manage chat prompt templates</p>
                  </div>
                  <Link href="/admin/chat/templates">
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Manage
                    </Button>
                  </Link>
                </div>
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">System Configuration</h4>
                    <p className="text-sm text-gray-500">Configure chat system settings</p>
                  </div>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
