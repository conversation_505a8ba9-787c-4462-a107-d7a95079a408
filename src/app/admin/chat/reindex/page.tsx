'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  RefreshCw,
  Database,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Clock,
  Play
} from 'lucide-react'
import Link from 'next/link'

interface ReindexJob {
  id: string
  type: 'full' | 'jurisdiction'
  target?: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  startedAt?: string
  completedAt?: string
  error?: string
  documentsProcessed?: number
  totalDocuments?: number
}

interface JurisdictionStats {
  jurisdiction: string
  documentCount: number
  lastUpdated: string
  status: 'fresh' | 'stale' | 'outdated'
}

export default function ReindexPage() {
  const [jurisdictions, setJurisdictions] = useState<JurisdictionStats[]>([])
  const [activeJob, setActiveJob] = useState<ReindexJob | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchJurisdictionStats()
    checkActiveJob()
  }, [])

  const fetchJurisdictionStats = async () => {
    try {
      const response = await fetch('/api/admin/documents/status')
      if (!response.ok) {
        throw new Error('Failed to fetch jurisdiction stats')
      }

      const data = await response.json()
      if (data.success) {
        setJurisdictions(data.jurisdictions)
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const checkActiveJob = async () => {
    try {
      const response = await fetch('/api/admin/reindex/status')
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.job) {
          setActiveJob(data.job)
          
          // Poll for updates if job is running
          if (data.job.status === 'running' || data.job.status === 'pending') {
            setTimeout(checkActiveJob, 2000)
          }
        }
      }
    } catch (err) {
      console.error('Failed to check job status:', err)
    }
  }

  const startReindex = async (type: 'full' | 'jurisdiction', target?: string) => {
    try {
      setError(null)
      
      const response = await fetch('/api/admin/reindex', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type, target }),
      })

      if (!response.ok) {
        throw new Error('Failed to start re-index')
      }

      const data = await response.json()
      if (data.success) {
        setActiveJob(data.job)
        // Start polling for updates
        setTimeout(checkActiveJob, 2000)
      } else {
        setError(data.error || 'Failed to start re-index')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start re-index')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'fresh':
        return <Badge variant="default" className="bg-green-100 text-green-800">Fresh</Badge>
      case 'stale':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Stale</Badge>
      case 'outdated':
        return <Badge variant="destructive">Outdated</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getJobStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'running':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/chat">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Chat Admin
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Knowledge Base Re-indexing</h1>
          <p className="text-gray-600 mt-2">
            Refresh embeddings and update the knowledge base for improved chat responses
          </p>
        </div>
      </div>

      {error && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Re-index Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <RefreshCw className="h-5 w-5 mr-2" />
              Re-index Controls
            </CardTitle>
            <CardDescription>
              Trigger knowledge base re-indexing operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Full Re-index */}
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium">Full Re-index</h3>
                <Badge variant="outline">All Jurisdictions</Badge>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Re-process all documents and regenerate embeddings for the entire knowledge base.
                This may take several minutes.
              </p>
              <Button
                onClick={() => startReindex('full')}
                disabled={activeJob?.status === 'running' || activeJob?.status === 'pending'}
                className="w-full"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Start Full Re-index
              </Button>
            </div>

            {/* Jurisdiction-specific Re-index */}
            <div className="space-y-3">
              <h3 className="font-medium">Jurisdiction-specific Re-index</h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {jurisdictions.map((jurisdiction) => (
                  <div key={jurisdiction.jurisdiction} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{jurisdiction.jurisdiction}</span>
                        {getStatusBadge(jurisdiction.status)}
                      </div>
                      <p className="text-xs text-gray-500">
                        {jurisdiction.documentCount} documents • Updated {formatDate(jurisdiction.lastUpdated)}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => startReindex('jurisdiction', jurisdiction.jurisdiction)}
                      disabled={activeJob?.status === 'running' || activeJob?.status === 'pending'}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Job Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Re-index Status
            </CardTitle>
            <CardDescription>
              Current and recent re-indexing operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activeJob ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getJobStatusIcon(activeJob.status)}
                    <span className="font-medium capitalize">{activeJob.status}</span>
                  </div>
                  <Badge variant="outline">
                    {activeJob.type === 'full' ? 'Full Re-index' : `${activeJob.target}`}
                  </Badge>
                </div>

                {activeJob.status === 'running' && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{activeJob.progress}%</span>
                    </div>
                    <Progress value={activeJob.progress} className="w-full" />
                    {activeJob.documentsProcessed && activeJob.totalDocuments && (
                      <p className="text-xs text-gray-500">
                        {activeJob.documentsProcessed} of {activeJob.totalDocuments} documents processed
                      </p>
                    )}
                  </div>
                )}

                {activeJob.status === 'completed' && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded">
                    <p className="text-sm text-green-800">
                      Re-index completed successfully at {formatDate(activeJob.completedAt!)}
                    </p>
                  </div>
                )}

                {activeJob.status === 'failed' && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded">
                    <p className="text-sm text-red-800">
                      Re-index failed: {activeJob.error}
                    </p>
                  </div>
                )}

                {activeJob.startedAt && (
                  <p className="text-xs text-gray-500">
                    Started: {formatDate(activeJob.startedAt)}
                  </p>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No active re-indexing operations</p>
                <p className="text-xs text-gray-400 mt-2">
                  Start a re-index operation to refresh the knowledge base
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
