'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  FileText,
  Plus,
  Edit,
  Trash2,
  ArrowLeft,
  Save,
  X,
  Eye,
  Copy,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import Link from 'next/link'

interface PromptTemplate {
  id: string
  name: string
  description: string
  templateText: string
  category: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<PromptTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [previewTemplate, setPreviewTemplate] = useState<PromptTemplate | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  const categories = [
    'general',
    'appraisal',
    'safety',
    'building',
    'zoning',
    'fire',
    'electrical',
    'plumbing'
  ]

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/admin/prompt-templates')
      if (!response.ok) {
        throw new Error('Failed to fetch templates')
      }

      const data = await response.json()
      if (data.success) {
        setTemplates(data.templates)
      } else {
        setError(data.error || 'Failed to load templates')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load templates')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTemplate = () => {
    setEditingTemplate({
      id: '',
      name: '',
      description: '',
      templateText: '',
      category: 'general',
      isActive: true,
      createdAt: '',
      updatedAt: ''
    })
    setIsDialogOpen(true)
  }

  const handleEditTemplate = (template: PromptTemplate) => {
    setEditingTemplate({ ...template })
    setIsDialogOpen(true)
  }

  const handleSaveTemplate = async () => {
    if (!editingTemplate) return

    try {
      const isNew = !editingTemplate.id
      const url = isNew ? '/api/admin/prompt-templates' : `/api/admin/prompt-templates/${editingTemplate.id}`
      const method = isNew ? 'POST' : 'PATCH'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editingTemplate.name,
          description: editingTemplate.description,
          templateText: editingTemplate.templateText,
          category: editingTemplate.category,
          isActive: editingTemplate.isActive
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save template')
      }

      await fetchTemplates()
      setIsDialogOpen(false)
      setEditingTemplate(null)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save template')
    }
  }

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/prompt-templates/${templateId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete template')
      }

      await fetchTemplates()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete template')
    }
  }

  const handleToggleActive = async (template: PromptTemplate) => {
    try {
      const response = await fetch(`/api/admin/prompt-templates/${template.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !template.isActive
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update template')
      }

      await fetchTemplates()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update template')
    }
  }

  const handleCopyTemplate = (template: PromptTemplate) => {
    navigator.clipboard.writeText(template.templateText)
    // You could add a toast notification here
  }

  const getCategoryBadge = (category: string) => {
    const colors: Record<string, string> = {
      general: 'bg-gray-100 text-gray-800',
      appraisal: 'bg-blue-100 text-blue-800',
      safety: 'bg-red-100 text-red-800',
      building: 'bg-green-100 text-green-800',
      zoning: 'bg-purple-100 text-purple-800',
      fire: 'bg-orange-100 text-orange-800',
      electrical: 'bg-yellow-100 text-yellow-800',
      plumbing: 'bg-cyan-100 text-cyan-800'
    }

    return (
      <Badge variant="secondary" className={colors[category] || colors.general}>
        {category.charAt(0).toUpperCase() + category.slice(1)}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/chat">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Chat Admin
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">Prompt Templates</h1>
          <p className="text-gray-600 mt-2">
            Manage chat prompt templates for different compliance scenarios
          </p>
        </div>
        <Button onClick={handleCreateTemplate}>
          <Plus className="h-4 w-4 mr-2" />
          New Template
        </Button>
      </div>

      {error && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <Card key={template.id} className={`${!template.isActive ? 'opacity-60' : ''}`}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {template.description}
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-1">
                  {template.isActive ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
              <div className="flex items-center justify-between mt-3">
                {getCategoryBadge(template.category)}
                <span className="text-xs text-gray-500">
                  {formatDate(template.updatedAt)}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 mb-4 line-clamp-3">
                {template.templateText.substring(0, 150)}
                {template.templateText.length > 150 && '...'}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setPreviewTemplate(template)
                    setIsPreviewOpen(true)
                  }}
                >
                  <Eye className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleCopyTemplate(template)}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEditTemplate(template)}
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleToggleActive(template)}
                  className={template.isActive ? 'text-yellow-600' : 'text-green-600'}
                >
                  {template.isActive ? <X className="h-3 w-3" /> : <CheckCircle className="h-3 w-3" />}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteTemplate(template.id)}
                  className="text-red-600"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {templates.length === 0 && (
          <div className="col-span-full text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No templates found
            </h3>
            <p className="text-gray-500 mb-4">
              Create your first prompt template to get started
            </p>
            <Button onClick={handleCreateTemplate}>
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </div>
        )}
      </div>

      {/* Edit/Create Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate?.id ? 'Edit Template' : 'Create Template'}
            </DialogTitle>
            <DialogDescription>
              {editingTemplate?.id 
                ? 'Update the prompt template details'
                : 'Create a new prompt template for chat interactions'
              }
            </DialogDescription>
          </DialogHeader>
          
          {editingTemplate && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={editingTemplate.name}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    name: e.target.value
                  })}
                  placeholder="e.g., Building Code Compliance Check"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={editingTemplate.description}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    description: e.target.value
                  })}
                  placeholder="Brief description of when to use this template"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={editingTemplate.category} 
                  onValueChange={(value) => setEditingTemplate({
                    ...editingTemplate,
                    category: value
                  })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="templateText">Template Text</Label>
                <Textarea
                  id="templateText"
                  value={editingTemplate.templateText}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    templateText: e.target.value
                  })}
                  placeholder="Enter the prompt template text..."
                  className="mt-1 min-h-[200px]"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={editingTemplate.isActive}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    isActive: e.target.checked
                  })}
                />
                <Label htmlFor="isActive">Active (available for use)</Label>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveTemplate}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Template
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{previewTemplate?.name}</DialogTitle>
            <DialogDescription>
              {previewTemplate?.description}
            </DialogDescription>
          </DialogHeader>
          
          {previewTemplate && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                {getCategoryBadge(previewTemplate.category)}
                <Badge variant={previewTemplate.isActive ? 'default' : 'secondary'}>
                  {previewTemplate.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              
              <div>
                <Label>Template Text</Label>
                <div className="mt-2 p-4 bg-gray-50 rounded-lg border">
                  <pre className="whitespace-pre-wrap text-sm">
                    {previewTemplate.templateText}
                  </pre>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={() => handleCopyTemplate(previewTemplate)}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Text
                </Button>
                <Button onClick={() => {
                  setIsPreviewOpen(false)
                  handleEditTemplate(previewTemplate)
                }}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Template
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
