'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Upload,
  ArrowLeft,
  FileText,
  AlertCircle,
  CheckCircle,
  Database,
  Loader2
} from 'lucide-react'
import Link from 'next/link'

interface UploadProgress {
  stage: 'uploading' | 'processing' | 'embedding' | 'complete' | 'error'
  progress: number
  message: string
  error?: string
}

export default function DocumentUploadPage() {
  const router = useRouter()
  const [file, setFile] = useState<File | null>(null)
  const [title, setTitle] = useState('')
  const [jurisdiction, setJurisdiction] = useState('')
  const [documentType, setDocumentType] = useState('')
  const [description, setDescription] = useState('')
  const [sourceUrl, setSourceUrl] = useState('')
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null)
  const [error, setError] = useState<string | null>(null)

  const documentTypes = [
    'ordinance',
    'building_code',
    'fire_code',
    'electrical_code',
    'plumbing_code',
    'zoning_code',
    'safety_code',
    'environmental_code',
    'other'
  ]

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setError(null)
      
      // Auto-populate title if not set
      if (!title) {
        const fileName = selectedFile.name.replace(/\.[^/.]+$/, '')
        setTitle(fileName)
      }
    }
  }

  const validateForm = () => {
    if (!file) {
      setError('Please select a file to upload')
      return false
    }

    if (!title.trim()) {
      setError('Please enter a document title')
      return false
    }

    if (!jurisdiction.trim()) {
      setError('Please enter a jurisdiction')
      return false
    }

    if (!documentType) {
      setError('Please select a document type')
      return false
    }

    // Validate file type
    const allowedTypes = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    if (!allowedTypes.includes(file.type)) {
      setError('Please upload a PDF, Word document, or text file')
      return false
    }

    // Validate file size (50MB limit)
    if (file.size > 50 * 1024 * 1024) {
      setError('File size must be less than 50MB')
      return false
    }

    return true
  }

  const handleUpload = async () => {
    if (!validateForm()) return

    setUploading(true)
    setError(null)
    setUploadProgress({
      stage: 'uploading',
      progress: 0,
      message: 'Uploading file...'
    })

    try {
      const formData = new FormData()
      formData.append('file', file!)
      formData.append('title', title.trim())
      formData.append('jurisdiction', jurisdiction.trim())
      formData.append('documentType', documentType)
      formData.append('description', description.trim())
      formData.append('sourceUrl', sourceUrl.trim())

      const response = await fetch('/api/admin/documents/upload', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      // Start polling for progress
      const data = await response.json()
      if (data.success && data.jobId) {
        pollUploadProgress(data.jobId)
      } else {
        throw new Error('Upload failed to start')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed')
      setUploadProgress({
        stage: 'error',
        progress: 0,
        message: 'Upload failed',
        error: err instanceof Error ? err.message : 'Unknown error'
      })
      setUploading(false)
    }
  }

  const pollUploadProgress = async (jobId: string) => {
    try {
      const response = await fetch(`/api/admin/documents/upload/${jobId}/status`)
      if (!response.ok) {
        throw new Error('Failed to get upload status')
      }

      const data = await response.json()
      if (data.success && data.progress) {
        setUploadProgress(data.progress)

        if (data.progress.stage === 'complete') {
          setUploading(false)
          // Redirect to documents page after a delay
          setTimeout(() => {
            router.push('/admin/chat/documents')
          }, 2000)
        } else if (data.progress.stage === 'error') {
          setUploading(false)
          setError(data.progress.error || 'Processing failed')
        } else {
          // Continue polling
          setTimeout(() => pollUploadProgress(jobId), 1000)
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get upload status')
      setUploading(false)
    }
  }

  const getProgressIcon = (stage: string) => {
    switch (stage) {
      case 'uploading':
        return <Upload className="h-5 w-5 text-blue-500" />
      case 'processing':
        return <FileText className="h-5 w-5 text-yellow-500" />
      case 'embedding':
        return <Database className="h-5 w-5 text-purple-500" />
      case 'complete':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <Loader2 className="h-5 w-5 text-gray-500 animate-spin" />
    }
  }

  return (
    <div className="max-w-4xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/chat/documents">
          <Button variant="outline" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Documents
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Upload Document</h1>
          <p className="text-gray-600 mt-2">
            Add a new compliance document to the knowledge base
          </p>
        </div>
      </div>

      {error && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Form */}
        <Card>
          <CardHeader>
            <CardTitle>Document Information</CardTitle>
            <CardDescription>
              Provide details about the document you're uploading
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="file">Document File *</Label>
              <Input
                id="file"
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={handleFileChange}
                disabled={uploading}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">
                Supported formats: PDF, Word, Text (max 50MB)
              </p>
            </div>

            <div>
              <Label htmlFor="title">Document Title *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="e.g., Springfield Building Code 2023"
                disabled={uploading}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="jurisdiction">Jurisdiction *</Label>
              <Input
                id="jurisdiction"
                value={jurisdiction}
                onChange={(e) => setJurisdiction(e.target.value)}
                placeholder="e.g., Springfield, IL"
                disabled={uploading}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="documentType">Document Type *</Label>
              <Select value={documentType} onValueChange={setDocumentType} disabled={uploading}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="sourceUrl">Source URL (Optional)</Label>
              <Input
                id="sourceUrl"
                value={sourceUrl}
                onChange={(e) => setSourceUrl(e.target.value)}
                placeholder="https://example.com/document.pdf"
                disabled={uploading}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Additional notes about this document..."
                disabled={uploading}
                className="mt-1"
                rows={3}
              />
            </div>

            <Button 
              onClick={handleUpload} 
              disabled={uploading || !file}
              className="w-full"
            >
              {uploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Document
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Upload Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Upload Progress</CardTitle>
            <CardDescription>
              Track the processing status of your document
            </CardDescription>
          </CardHeader>
          <CardContent>
            {uploadProgress ? (
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  {getProgressIcon(uploadProgress.stage)}
                  <div className="flex-1">
                    <p className="font-medium capitalize">
                      {uploadProgress.stage.replace('_', ' ')}
                    </p>
                    <p className="text-sm text-gray-600">
                      {uploadProgress.message}
                    </p>
                  </div>
                </div>

                <Progress value={uploadProgress.progress} className="w-full" />

                <div className="text-sm text-gray-500">
                  {uploadProgress.progress}% complete
                </div>

                {uploadProgress.stage === 'complete' && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Document uploaded successfully! Redirecting to documents list...
                    </AlertDescription>
                  </Alert>
                )}

                {uploadProgress.stage === 'error' && uploadProgress.error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {uploadProgress.error}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  Upload progress will appear here once you start uploading
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Upload Instructions */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Upload Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Supported File Types</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• PDF documents (.pdf)</li>
                <li>• Microsoft Word (.doc, .docx)</li>
                <li>• Plain text files (.txt)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Processing Steps</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• File upload and validation</li>
                <li>• Text extraction and chunking</li>
                <li>• Embedding generation</li>
                <li>• Knowledge base integration</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
