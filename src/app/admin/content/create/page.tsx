'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
// import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import {
  Video,
  Megaphone,
  Image,
  Quote,
  Camera,
  ArrowLeft,
  Save,
  Calendar,
  Send
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface ContentFormData {
  title: string
  description: string
  contentType: string
  platform: string[]
  contentData: Record<string, unknown>
  scheduledFor?: string
}

const CONTENT_TYPES = [
  { id: 'video', label: 'Video', icon: Video, description: 'Upload and manage video content' },
  { id: 'announcement', label: 'Announcement', icon: Megaphone, description: 'Create launch announcements and threads' },
  { id: 'meme', label: 'Meme', icon: Image, description: 'Create meme content for social media' },
  { id: 'quote', label: 'Quote', icon: Quote, description: 'Create quote cards and testimonials' },
  { id: 'screenshot', label: 'Screenshot', icon: Camera, description: 'Product screenshots and demos' }
]

const PLATFORMS = [
  { id: 'twitter', label: 'Twitter', color: 'bg-blue-100 text-blue-800' },
  { id: 'linkedin', label: 'LinkedIn', color: 'bg-blue-100 text-blue-800' },
  { id: 'tiktok', label: 'TikTok', color: 'bg-black text-white' },
  { id: 'instagram', label: 'Instagram', color: 'bg-pink-100 text-pink-800' },
  { id: 'youtube', label: 'YouTube', color: 'bg-red-100 text-red-800' },
  { id: 'facebook', label: 'Facebook', color: 'bg-blue-100 text-blue-800' }
]

export default function CreateContentPage() {
  const router = useRouter()
  const [formData, setFormData] = useState<ContentFormData>({
    title: '',
    description: '',
    contentType: 'video',
    platform: [],
    contentData: {}
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (field: keyof ContentFormData, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePlatformToggle = (platformId: string) => {
    setFormData(prev => ({
      ...prev,
      platform: prev.platform.includes(platformId)
        ? prev.platform.filter(p => p !== platformId)
        : [...prev.platform, platformId]
    }))
  }

  const handleContentDataChange = (key: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      contentData: {
        ...prev.contentData,
        [key]: value
      }
    }))
  }

  const handleSubmit = async (action: 'save' | 'schedule') => {
    try {
      setLoading(true)
      setError(null)

      if (!formData.title.trim()) {
        throw new Error('Title is required')
      }

      if (formData.platform.length === 0) {
        throw new Error('At least one platform must be selected')
      }

      const response = await fetch('/api/content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          title: formData.title,
          description: formData.description,
          contentType: formData.contentType,
          platform: formData.platform,
          contentData: formData.contentData
        })
      })

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to create content')
      }

      // If scheduling, make another API call
      if (action === 'schedule' && formData.scheduledFor) {
        const scheduleResponse = await fetch('/api/content', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'schedule',
            itemId: result.data.id,
            scheduledFor: formData.scheduledFor
          })
        })

        const scheduleResult = await scheduleResponse.json()
        if (!scheduleResult.success) {
          throw new Error(scheduleResult.error || 'Failed to schedule content')
        }
      }

      router.push('/admin/content')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const selectedContentType = CONTENT_TYPES.find(type => type.id === formData.contentType)

  return (
    <div className="max-w-4xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/content">
          <Button variant="ghost" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Content
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create Content</h1>
          <p className="text-gray-600 mt-2">
            Create new content for your marketing campaigns
          </p>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Content Type Selection */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Content Type</CardTitle>
              <CardDescription>
                Choose the type of content you want to create
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {CONTENT_TYPES.map((type) => {
                const Icon = type.icon
                return (
                  <div
                    key={type.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      formData.contentType === type.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInputChange('contentType', type.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="h-5 w-5" />
                      <div>
                        <p className="font-medium text-sm">{type.label}</p>
                        <p className="text-xs text-gray-500">{type.description}</p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Platform Selection */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Platforms</CardTitle>
              <CardDescription>
                Select where this content will be published
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {PLATFORMS.map((platform) => (
                  <div
                    key={platform.id}
                    className={`p-2 border rounded cursor-pointer transition-colors ${
                      formData.platform.includes(platform.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handlePlatformToggle(platform.id)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{platform.label}</span>
                      {formData.platform.includes(platform.id) && (
                        <Badge className={platform.color}>{platform.label}</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {selectedContentType && <selectedContentType.icon className="h-5 w-5 mr-2" />}
                {selectedContentType?.label} Content
              </CardTitle>
              <CardDescription>
                Fill in the details for your {selectedContentType?.label.toLowerCase()} content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter content title..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter content description..."
                  />
                </div>
              </div>

              {/* Content Type Specific Fields */}
              {formData.contentType === 'video' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Video URL
                    </label>
                    <input
                      type="url"
                      value={(formData.contentData.videoUrl as string) || ''}
                      onChange={(e) => handleContentDataChange('videoUrl', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="https://..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      CTA Text
                    </label>
                    <input
                      type="text"
                      value={(formData.contentData.ctaText as string) || 'Check your property on Ordrly.com'}
                      onChange={(e) => handleContentDataChange('ctaText', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hashtags (comma-separated)
                    </label>
                    <input
                      type="text"
                      value={(formData.contentData.hashtags as string[])?.join(', ') || ''}
                      onChange={(e) => handleContentDataChange('hashtags', e.target.value.split(',').map(tag => tag.trim()))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="#HOA, #PropertyCompliance, #Ordrly"
                    />
                  </div>
                </div>
              )}

              {formData.contentType === 'announcement' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Announcement Content
                    </label>
                    <textarea
                      value={(formData.contentData.content as string) || ''}
                      onChange={(e) => handleContentDataChange('content', e.target.value)}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Write your announcement content..."
                    />
                  </div>
                </div>
              )}

              {/* Scheduling */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Scheduling</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Schedule for later (optional)
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.scheduledFor || ''}
                    onChange={(e) => handleInputChange('scheduledFor', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => handleSubmit('save')}
                  disabled={loading}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Draft
                </Button>

                {formData.scheduledFor && (
                  <Button
                    onClick={() => handleSubmit('schedule')}
                    disabled={loading}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule
                  </Button>
                )}

                {!formData.scheduledFor && (
                  <Button
                    onClick={() => handleSubmit('save')}
                    disabled={loading}
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Create Content
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
