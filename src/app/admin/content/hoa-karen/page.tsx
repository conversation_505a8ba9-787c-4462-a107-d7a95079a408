'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Video,
  ArrowLeft,
  Save,
  // Upload,
  // Play,
  CheckCircle,
  Clock,
  Sparkles
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface HOAKarenVideo {
  title: string
  description: string
  videoUrl: string
  thumbnailUrl: string
  duration?: number
  scenario: string
  cliffhanger: string
  ctaText: string
}

const HOA_KAREN_SCENARIOS = [
  {
    id: 'fence_height',
    title: 'Fence Height Drama',
    description: '<PERSON> complains about a 6-foot fence being "too tall" and "blocking her view"',
    cliffhanger: 'But little did she know, the city ordinance actually allows 8-foot fences...'
  },
  {
    id: 'shed_placement',
    title: 'Shed Setback Saga',
    description: 'Karen demands neighbor move their shed because it&apos;s &quot;too close to the property line&quot;',
    cliffhanger: 'Turns out the shed meets all setback requirements and is perfectly legal...'
  },
  {
    id: 'deck_permits',
    title: 'Deck Permit Panic',
    description: '<PERSON> insists a new deck needs permits and threatens to call the city',
    cliffhanger: 'But decks under 30 inches high don&apos;t require permits in this jurisdiction...'
  },
  {
    id: 'tree_removal',
    title: 'Tree Removal Tantrum',
    description: 'Karen claims removing a tree on private property is illegal',
    cliffhanger: 'Actually, homeowners can remove non-protected trees on their own property...'
  },
  {
    id: 'parking_rules',
    title: 'Parking Enforcement',
    description: 'Karen complains about RV parking in neighbor\'s driveway',
    cliffhanger: 'But the city allows RV parking for up to 72 hours...'
  }
]

const VIDEO_TEMPLATES = {
  intro: "🎬 HOA Karen strikes again! This time she's convinced that [SCENARIO] is against the rules...",
  conflict: "Karen: 'That's definitely not allowed! I'm calling the HOA!' 😤",
  resolution: "Plot twist: [CLIFFHANGER] ✨",
  cta: "I used Ordrly to check the actual ordinances. 📱 Check your property at Ordrly.com"
}

export default function HOAKarenCreatorPage() {
  const router = useRouter()
  const [formData, setFormData] = useState<HOAKarenVideo>({
    title: '',
    description: '',
    videoUrl: '',
    thumbnailUrl: '',
    scenario: '',
    cliffhanger: '',
    ctaText: 'Check your property on Ordrly.com'
  })
  const [selectedScenario, setSelectedScenario] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleScenarioSelect = (scenarioId: string) => {
    const scenario = HOA_KAREN_SCENARIOS.find(s => s.id === scenarioId)
    if (scenario) {
      setSelectedScenario(scenarioId)
      setFormData(prev => ({
        ...prev,
        title: `HOA Karen: ${scenario.title}`,
        description: scenario.description,
        scenario: scenario.description,
        cliffhanger: scenario.cliffhanger
      }))
    }
  }

  const handleInputChange = (field: keyof HOAKarenVideo, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const generateVideoScript = () => {
    if (!selectedScenario) return ''

    const scenario = HOA_KAREN_SCENARIOS.find(s => s.id === selectedScenario)
    if (!scenario) return ''

    return `${VIDEO_TEMPLATES.intro.replace('[SCENARIO]', scenario.description)}

${VIDEO_TEMPLATES.conflict}

${VIDEO_TEMPLATES.resolution.replace('[CLIFFHANGER]', scenario.cliffhanger)}

${VIDEO_TEMPLATES.cta}`
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!formData.title.trim()) {
        throw new Error('Title is required')
      }

      if (!formData.videoUrl.trim()) {
        throw new Error('Video URL is required')
      }

      if (!formData.description.trim()) {
        throw new Error('Description is required')
      }

      const response = await fetch('/api/content/videos', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create_hoa_karen',
          title: formData.title,
          description: formData.description,
          videoUrl: formData.videoUrl,
          thumbnailUrl: formData.thumbnailUrl,
          duration: formData.duration
        })
      })

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to create HOA Karen video')
      }

      setSuccess(true)
      setTimeout(() => {
        router.push('/admin/content')
      }, 2000)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="max-w-4xl mx-auto py-16 px-4">
        <div className="text-center">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">HOA Karen Video Created!</h1>
          <p className="text-gray-600 mb-6">
            Your video has been added to the content library and is ready for scheduling.
          </p>
          <Link href="/admin/content">
            <Button>
              View Content Library
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/content">
          <Button variant="ghost" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Content
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Sparkles className="h-8 w-8 mr-3 text-purple-500" />
            HOA Karen Video Creator
          </h1>
          <p className="text-gray-600 mt-2">
            Create viral short-form videos featuring HOA Karen scenarios
          </p>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Scenario Selection */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Choose Your Scenario</CardTitle>
              <CardDescription>
                Select a HOA Karen scenario for your video
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {HOA_KAREN_SCENARIOS.map((scenario) => (
                <div
                  key={scenario.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedScenario === scenario.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleScenarioSelect(scenario.id)}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">{scenario.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{scenario.description}</p>
                      <p className="text-xs text-purple-600 mt-2 italic">
                        Cliffhanger: {scenario.cliffhanger}
                      </p>
                    </div>
                    {selectedScenario === scenario.id && (
                      <Badge className="bg-purple-100 text-purple-800">Selected</Badge>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Video Script Preview */}
          {selectedScenario && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Video Script Preview</CardTitle>
                <CardDescription>
                  Suggested script for your HOA Karen video
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                    {generateVideoScript()}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Video Details Form */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Video className="h-5 w-5 mr-2" />
                Video Details
              </CardTitle>
              <CardDescription>
                Upload your video and add details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="HOA Karen: [Scenario Name]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Brief description of the scenario..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video URL *
                </label>
                <input
                  type="url"
                  value={formData.videoUrl}
                  onChange={(e) => handleInputChange('videoUrl', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="https://..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Upload your video to a hosting service and paste the URL here
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Thumbnail URL
                </label>
                <input
                  type="url"
                  value={formData.thumbnailUrl}
                  onChange={(e) => handleInputChange('thumbnailUrl', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="https://..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Duration (seconds)
                </label>
                <input
                  type="number"
                  value={formData.duration || ''}
                  onChange={(e) => handleInputChange('duration', parseInt(e.target.value) || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="30"
                  min="1"
                  max="180"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Recommended: 15-60 seconds for TikTok/Instagram, up to 180 for YouTube Shorts
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  CTA Text
                </label>
                <input
                  type="text"
                  value={formData.ctaText}
                  onChange={(e) => handleInputChange('ctaText', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              {/* Video Requirements */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Video Requirements</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Include visible Ordrly watermark or brand mention</li>
                  <li>• End with cliffhanger and &quot;I used Ordrly&quot; reveal</li>
                  <li>• Add CTA overlay: &quot;Check your property on Ordrly.com&quot;</li>
                  <li>• Vertical format (9:16) for TikTok/Instagram</li>
                  <li>• Keep it under 60 seconds for maximum engagement</li>
                </ul>
              </div>

              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => router.push('/admin/content')}
                  disabled={loading}
                >
                  Cancel
                </Button>

                <Button
                  onClick={handleSubmit}
                  disabled={loading || !selectedScenario}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {loading ? (
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Create HOA Karen Video
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
