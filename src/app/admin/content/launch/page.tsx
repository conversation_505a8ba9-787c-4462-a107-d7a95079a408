'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
// import { Badge } from '@/components/ui/badge'
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  Megaphone,
  ArrowLeft,
  Save,
  Copy,
  CheckCircle,
  Clock,
  Twitter,
  Linkedin,
  Sparkles,
  Eye,
  Calendar
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface LaunchAnnouncementData {
  platform: 'twitter' | 'linkedin'
  includeScreenshots: boolean
  includeTestimonials: boolean
  customMessage: string
  scheduledFor?: string
}

export default function LaunchAnnouncementPage() {
  const router = useRouter()
  const [formData, setFormData] = useState<LaunchAnnouncementData>({
    platform: 'twitter',
    includeScreenshots: true,
    includeTestimonials: true,
    customMessage: ''
  })
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [copied, setCopied] = useState(false)

  const handleInputChange = (field: keyof LaunchAnnouncementData, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const generateAnnouncement = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/content/announcements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate_launch_thread',
          platform: formData.platform,
          includeScreenshots: formData.includeScreenshots,
          includeTestimonials: formData.includeTestimonials,
          customMessage: formData.customMessage || undefined
        })
      })

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to generate announcement')
      }

      setGeneratedContent(result.data.content_template)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedContent)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const saveAnnouncement = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/content/announcements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          title: `Launch Thread - ${formData.platform.charAt(0).toUpperCase() + formData.platform.slice(1)}`,
          platform: formData.platform,
          contentTemplate: generatedContent,
          contentData: {
            includeScreenshots: formData.includeScreenshots,
            includeTestimonials: formData.includeTestimonials,
            customMessage: formData.customMessage,
            generatedAt: new Date().toISOString()
          },
          scheduledFor: formData.scheduledFor
        })
      })

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to save announcement')
      }

      setSuccess(true)
      setTimeout(() => {
        router.push('/admin/content')
      }, 2000)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="max-w-4xl mx-auto py-16 px-4">
        <div className="text-center">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Launch Announcement Saved!</h1>
          <p className="text-gray-600 mb-6">
            Your launch announcement has been saved and is ready for publishing.
          </p>
          <Link href="/admin/content">
            <Button>
              View Content Library
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto py-16 px-4">
      <div className="flex items-center mb-8">
        <Link href="/admin/content">
          <Button variant="ghost" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Content
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Megaphone className="h-8 w-8 mr-3 text-blue-500" />
            Launch Announcement Generator
          </h1>
          <p className="text-gray-600 mt-2">
            Generate professional launch threads for Twitter and LinkedIn
          </p>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Configuration */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Announcement Settings</CardTitle>
              <CardDescription>
                Configure your launch announcement
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Platform Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Platform
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <div
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      formData.platform === 'twitter'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInputChange('platform', 'twitter')}
                  >
                    <div className="flex items-center space-x-2">
                      <Twitter className="h-5 w-5 text-blue-500" />
                      <span className="font-medium">Twitter</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Thread format (8 tweets)</p>
                  </div>

                  <div
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      formData.platform === 'linkedin'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInputChange('platform', 'linkedin')}
                  >
                    <div className="flex items-center space-x-2">
                      <Linkedin className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">LinkedIn</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Professional post</p>
                  </div>
                </div>
              </div>

              {/* Content Options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Include Content
                </label>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">Screenshots</p>
                      <p className="text-xs text-gray-500">Include product screenshots</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={formData.includeScreenshots}
                      onChange={(e) => handleInputChange('includeScreenshots', e.target.checked)}
                      className="h-4 w-4 text-blue-600 rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">Testimonials</p>
                      <p className="text-xs text-gray-500">Include user testimonials</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={formData.includeTestimonials}
                      onChange={(e) => handleInputChange('includeTestimonials', e.target.checked)}
                      className="h-4 w-4 text-blue-600 rounded"
                    />
                  </div>
                </div>
              </div>

              {/* Custom Message */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Opening Message (Optional)
                </label>
                <textarea
                  value={formData.customMessage}
                  onChange={(e) => handleInputChange('customMessage', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add a personal touch to your announcement..."
                />
              </div>

              {/* Scheduling */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Schedule for Later (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={formData.scheduledFor || ''}
                  onChange={(e) => handleInputChange('scheduledFor', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Generate Button */}
              <Button
                onClick={generateAnnouncement}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Sparkles className="h-4 w-4 mr-2" />
                )}
                Generate Launch Announcement
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Preview */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Preview
              </CardTitle>
              <CardDescription>
                Generated {formData.platform} launch announcement
              </CardDescription>
            </CardHeader>
            <CardContent>
              {generatedContent ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {generatedContent}
                    </pre>
                  </div>

                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      onClick={copyToClipboard}
                      className="flex-1"
                    >
                      {copied ? (
                        <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4 mr-2" />
                      )}
                      {copied ? 'Copied!' : 'Copy Text'}
                    </Button>

                    <Button
                      onClick={saveAnnouncement}
                      disabled={loading}
                      className="flex-1"
                    >
                      {formData.scheduledFor ? (
                        <Calendar className="h-4 w-4 mr-2" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      {formData.scheduledFor ? 'Schedule' : 'Save'}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Click &quot;Generate Launch Announcement&quot; to create your content
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tips */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Launch Tips</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <p>Post during peak hours (9-10 AM or 7-9 PM)</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <p>Engage with comments quickly after posting</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <p>Cross-post to both personal and brand accounts</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <p>Include relevant hashtags for discoverability</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <p>Pin the announcement to your profile</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
