'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import {
  Calendar,
  Video,
  Megaphone,
  BarChart3,
  Plus,
  Clock,
  CheckCircle,
  // XCircle,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import Link from 'next/link'

interface ContentItem {
  id: string
  title: string
  description?: string
  content_type: string
  platform: string[]
  status: string
  scheduled_for?: string
  published_at?: string
  created_at: string
}

interface ContentStats {
  draft: number
  scheduled: number
  published: number
  archived: number
}

export default function ContentManagementPage() {
  const [contentItems, setContentItems] = useState<ContentItem[]>([])
  const [stats, setStats] = useState<ContentStats>({ draft: 0, scheduled: 0, published: 0, archived: 0 })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchContentData()
  }, [])

  const fetchContentData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/content')

      if (!response.ok) {
        throw new Error('Failed to fetch content data')
      }

      const result = await response.json()
      if (result.success) {
        setContentItems(result.data.items)

        // Calculate stats
        const newStats = result.data.items.reduce((acc: ContentStats, item: ContentItem) => {
          acc[item.status as keyof ContentStats] = (acc[item.status as keyof ContentStats] || 0) + 1
          return acc
        }, { draft: 0, scheduled: 0, published: 0, archived: 0 })

        setStats(newStats)
      } else {
        throw new Error(result.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  // const getStatusIcon = (status: string) => {
  //   switch (status) {
  //     case 'published':
  //       return <CheckCircle className="h-4 w-4 text-green-500" />
  //     case 'scheduled':
  //       return <Clock className="h-4 w-4 text-yellow-500" />
  //     case 'draft':
  //       return <Edit className="h-4 w-4 text-gray-500" />
  //     case 'archived':
  //       return <XCircle className="h-4 w-4 text-red-500" />
  //     default:
  //       return <Clock className="h-4 w-4 text-gray-500" />
  //   }
  // }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="default" className="bg-green-100 text-green-800">Published</Badge>
      case 'scheduled':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Scheduled</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      case 'archived':
        return <Badge variant="destructive">Archived</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="h-4 w-4" />
      case 'announcement':
        return <Megaphone className="h-4 w-4" />
      default:
        return <Eye className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchContentData} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600 mt-2">
            Manage your content calendar, videos, and launch announcements
          </p>
        </div>
        <div className="flex space-x-3">
          <Link href="/admin/content/calendar">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Calendar
            </Button>
          </Link>
          <Link href="/admin/content/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Content
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Draft</p>
                <p className="text-2xl font-bold text-gray-900">{stats.draft}</p>
              </div>
              <Edit className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Scheduled</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.scheduled}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-green-600">{stats.published}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-blue-600">
                  {stats.draft + stats.scheduled + stats.published + stats.archived}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="videos">Videos</TabsTrigger>
          <TabsTrigger value="announcements">Announcements</TabsTrigger>
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Content</CardTitle>
              <CardDescription>
                Your latest content items across all types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contentItems.slice(0, 10).map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      {getContentTypeIcon(item.content_type)}
                      <div>
                        <h3 className="font-medium text-gray-900">{item.title}</h3>
                        <p className="text-sm text-gray-500">
                          {item.content_type} • {item.platform.join(', ')}
                        </p>
                        <p className="text-xs text-gray-400">
                          {item.scheduled_for ?
                            `Scheduled for ${new Date(item.scheduled_for).toLocaleDateString()}` :
                            `Created ${new Date(item.created_at).toLocaleDateString()}`
                          }
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(item.status)}
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="videos" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Video className="h-5 w-5 mr-2" />
                Video Content
              </CardTitle>
              <CardDescription>
                Manage your video assets and HOA Karen series
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contentItems
                  .filter(item => item.content_type === 'video')
                  .map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Video className="h-5 w-5 text-blue-500" />
                        <div>
                          <h3 className="font-medium text-gray-900">{item.title}</h3>
                          <p className="text-sm text-gray-500">{item.description}</p>
                          <p className="text-xs text-gray-400">
                            Platforms: {item.platform.join(', ')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        {getStatusBadge(item.status)}
                      </div>
                    </div>
                  ))}

                {contentItems.filter(item => item.content_type === 'video').length === 0 && (
                  <div className="text-center py-8">
                    <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No video content yet</p>
                    <Link href="/admin/content/videos/create">
                      <Button className="mt-4">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Video Content
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="announcements" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Megaphone className="h-5 w-5 mr-2" />
                Launch Announcements
              </CardTitle>
              <CardDescription>
                Manage your launch threads and announcements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contentItems
                  .filter(item => item.content_type === 'announcement')
                  .map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Megaphone className="h-5 w-5 text-purple-500" />
                        <div>
                          <h3 className="font-medium text-gray-900">{item.title}</h3>
                          <p className="text-sm text-gray-500">
                            Platforms: {item.platform.join(', ')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        {getStatusBadge(item.status)}
                      </div>
                    </div>
                  ))}

                {contentItems.filter(item => item.content_type === 'announcement').length === 0 && (
                  <div className="text-center py-8">
                    <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No announcements yet</p>
                    <Link href="/admin/content/announcements/create">
                      <Button className="mt-4">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Announcement
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Content Calendar
              </CardTitle>
              <CardDescription>
                View and manage your content schedule
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">Calendar view coming soon</p>
                <Link href="/admin/content/calendar">
                  <Button>
                    <Calendar className="h-4 w-4 mr-2" />
                    Open Full Calendar
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
