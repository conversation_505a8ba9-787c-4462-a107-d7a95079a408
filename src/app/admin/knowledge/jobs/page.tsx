'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ArrowLeft, RefreshCw } from 'lucide-react'
import { RefreshJobMonitor } from '@/components/admin/RefreshJobMonitor'
import { KnowledgeRefreshJob } from '@/lib/types/knowledge'

export default function RefreshJobsPage() {
  const [jobs, setJobs] = useState<KnowledgeRefreshJob[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchJobs()
  }, [])

  const fetchJobs = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/admin/knowledge/refresh?limit=50')
      if (!response.ok) {
        throw new Error('Failed to fetch refresh jobs')
      }

      const data = await response.json()
      setJobs(data.jobs || [])

    } catch (err) {
      console.error('Error fetching jobs:', err)
      setError(err instanceof Error ? err.message : 'Failed to load jobs')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Jobs</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={fetchJobs} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Link href="/admin/knowledge">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Refresh Jobs</h1>
            <p className="text-gray-600 mt-2">
              Monitor and manage knowledge base refresh operations
            </p>
          </div>
        </div>
      </div>

      <RefreshJobMonitor jobs={jobs} onRefresh={fetchJobs} />
    </div>
  )
}
