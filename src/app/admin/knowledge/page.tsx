'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { 
  Database, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  FileText,
  Settings,
  TrendingUp,
  Activity
} from 'lucide-react'
import { KnowledgeManagementDashboard } from '@/components/admin/KnowledgeManagementDashboard'
import { RefreshJobMonitor } from '@/components/admin/RefreshJobMonitor'
import { PerformanceAnalyticsDashboard } from '@/components/admin/PerformanceAnalyticsDashboard'
import { StalenessReportResponse, KnowledgeRefreshJob } from '@/lib/types/knowledge'

interface KnowledgeStats {
  total_documents: number
  active_documents: number
  stale_documents: number
  updates_available: number
  average_staleness: number
  last_refresh: string | null
  running_jobs: number
}

export default function KnowledgeManagementPage() {
  const [stats, setStats] = useState<KnowledgeStats | null>(null)
  const [stalenessReport, setStalenessReport] = useState<StalenessReportResponse | null>(null)
  const [recentJobs, setRecentJobs] = useState<KnowledgeRefreshJob[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchKnowledgeData()
  }, [])

  const fetchKnowledgeData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch staleness report
      const stalenessResponse = await fetch('/api/admin/knowledge/staleness')
      if (!stalenessResponse.ok) {
        throw new Error('Failed to fetch staleness data')
      }
      const stalenessData: StalenessReportResponse = await stalenessResponse.json()
      setStalenessReport(stalenessData)

      // Fetch recent refresh jobs
      const jobsResponse = await fetch('/api/admin/knowledge/refresh?limit=10')
      if (!jobsResponse.ok) {
        throw new Error('Failed to fetch refresh jobs')
      }
      const jobsData = await jobsResponse.json()
      setRecentJobs(jobsData.jobs || [])

      // Calculate stats from the data
      const runningJobs = jobsData.jobs?.filter((job: KnowledgeRefreshJob) => 
        ['pending', 'running'].includes(job.status)
      ).length || 0

      const stats: KnowledgeStats = {
        total_documents: stalenessData.summary.total_documents,
        active_documents: stalenessData.summary.total_documents - stalenessData.summary.stale_documents,
        stale_documents: stalenessData.summary.stale_documents,
        updates_available: stalenessData.summary.updates_available,
        average_staleness: stalenessData.summary.average_staleness,
        last_refresh: jobsData.jobs?.[0]?.completed_at || null,
        running_jobs: runningJobs
      }
      setStats(stats)

    } catch (err) {
      console.error('Error fetching knowledge data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load knowledge data')
    } finally {
      setLoading(false)
    }
  }

  const refreshData = () => {
    fetchKnowledgeData()
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Data</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={refreshData} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Knowledge Base Management</h1>
          <p className="text-gray-600 mt-2">
            Monitor document freshness, manage versions, and control knowledge base updates. 
            Ensure compliance information stays current and accurate.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Data Freshness • Version Control • Automated Updates
          </div>
        </div>
        <div className="flex space-x-3">
          <Link href="/admin/knowledge/versions">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Versions
            </Button>
          </Link>
          <Link href="/admin/knowledge/jobs">
            <Button variant="outline">
              <Activity className="h-4 w-4 mr-2" />
              Jobs
            </Button>
          </Link>
          <Button onClick={refreshData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Documents</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.total_documents || 0}
                </p>
              </div>
              <Database className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Stale Documents</p>
                <p className="text-2xl font-bold text-orange-600">
                  {stats?.stale_documents || 0}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Updates Available</p>
                <p className="text-2xl font-bold text-green-600">
                  {stats?.updates_available || 0}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Running Jobs</p>
                <p className="text-2xl font-bold text-blue-600">
                  {stats?.running_jobs || 0}
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('staleness')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'staleness'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Staleness Report
          </button>
          <button
            onClick={() => setActiveTab('jobs')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'jobs'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Refresh Jobs
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Analytics
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <KnowledgeManagementDashboard 
          stats={stats}
          stalenessReport={stalenessReport}
          onRefresh={refreshData}
        />
      )}

      {activeTab === 'staleness' && stalenessReport && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Staleness Report</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stalenessReport.tracking.map((item) => (
                  <div key={`${item.jurisdiction}-${item.document_type}`} 
                       className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{item.jurisdiction}</h4>
                      <p className="text-sm text-gray-600">{item.document_type}</p>
                      <p className="text-xs text-gray-500">
                        Version: {item.current_version || 'Unknown'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge variant={
                        item.staleness_score < 0.3 ? 'default' :
                        item.staleness_score < 0.6 ? 'secondary' : 'destructive'
                      }>
                        {Math.round(item.staleness_score * 100)}% stale
                      </Badge>
                      {item.update_available && (
                        <Badge variant="outline">Update Available</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'jobs' && (
        <RefreshJobMonitor
          jobs={recentJobs}
          onRefresh={refreshData}
        />
      )}

      {activeTab === 'analytics' && (
        <PerformanceAnalyticsDashboard />
      )}
    </div>
  )
}
