'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { 
  ArrowLeft,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Edit,
  Eye,
  MoreHorizontal
} from 'lucide-react'
import { DocumentVersionsResponse, ComplianceKnowledgeWithVersion } from '@/lib/types/knowledge'
import { toast } from 'sonner'

export default function DocumentVersionsPage() {
  const [documents, setDocuments] = useState<ComplianceKnowledgeWithVersion[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedDocs, setSelectedDocs] = useState<Set<number>>(new Set())
  const [filters, setFilters] = useState({
    jurisdiction: '',
    document_type: '',
    is_active: '',
    search: ''
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    has_more: false
  })

  useEffect(() => {
    fetchDocuments()
  }, [filters, pagination.page])

  const fetchDocuments = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.jurisdiction && { jurisdiction: filters.jurisdiction }),
        ...(filters.document_type && { document_type: filters.document_type }),
        ...(filters.is_active && { is_active: filters.is_active })
      })

      const response = await fetch(`/api/admin/knowledge/versions?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch document versions')
      }

      const data: DocumentVersionsResponse = await response.json()
      setDocuments(data.versions)
      setPagination(data.pagination)

    } catch (err) {
      console.error('Error fetching documents:', err)
      setError(err instanceof Error ? err.message : 'Failed to load documents')
    } finally {
      setLoading(false)
    }
  }

  const handleBulkAction = async (action: string) => {
    if (selectedDocs.size === 0) {
      toast.error('Please select documents first')
      return
    }

    try {
      const response = await fetch('/api/admin/knowledge/versions', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document_ids: Array.from(selectedDocs),
          action
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `Failed to ${action} documents`)
      }

      toast.success(`Successfully ${action}d ${selectedDocs.size} documents`)
      setSelectedDocs(new Set())
      fetchDocuments()

    } catch (error) {
      console.error(`Error ${action}ing documents:`, error)
      toast.error(error instanceof Error ? error.message : `Failed to ${action} documents`)
    }
  }

  const toggleDocSelection = (docId: number) => {
    const newSelected = new Set(selectedDocs)
    if (newSelected.has(docId)) {
      newSelected.delete(docId)
    } else {
      newSelected.add(docId)
    }
    setSelectedDocs(newSelected)
  }

  const toggleSelectAll = () => {
    if (selectedDocs.size === documents.length) {
      setSelectedDocs(new Set())
    } else {
      setSelectedDocs(new Set(documents.map(doc => doc.id)))
    }
  }

  const getUniqueValues = (field: keyof ComplianceKnowledgeWithVersion) => {
    return [...new Set(documents.map(doc => doc[field] as string))].filter(Boolean).sort()
  }

  const filteredDocuments = documents.filter(doc => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      return (
        doc.title?.toLowerCase().includes(searchLower) ||
        doc.jurisdiction.toLowerCase().includes(searchLower) ||
        doc.document_type.toLowerCase().includes(searchLower) ||
        doc.document_version?.toLowerCase().includes(searchLower)
      )
    }
    return true
  })

  if (loading && documents.length === 0) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Link href="/admin/knowledge">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Document Versions</h1>
            <p className="text-gray-600 mt-2">
              Manage document versions, activation status, and metadata
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          {selectedDocs.size > 0 && (
            <>
              <Button
                onClick={() => handleBulkAction('activate')}
                variant="outline"
                size="sm"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Activate ({selectedDocs.size})
              </Button>
              <Button
                onClick={() => handleBulkAction('deactivate')}
                variant="outline"
                size="sm"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Deactivate ({selectedDocs.size})
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="search"
                  placeholder="Search documents..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="jurisdiction">Jurisdiction</Label>
              <Select
                value={filters.jurisdiction}
                onValueChange={(value) => setFilters(prev => ({ ...prev, jurisdiction: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All jurisdictions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All jurisdictions</SelectItem>
                  {getUniqueValues('jurisdiction').map(jurisdiction => (
                    <SelectItem key={jurisdiction} value={jurisdiction}>
                      {jurisdiction}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="document_type">Document Type</Label>
              <Select
                value={filters.document_type}
                onValueChange={(value) => setFilters(prev => ({ ...prev, document_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All types</SelectItem>
                  {getUniqueValues('document_type').map(docType => (
                    <SelectItem key={docType} value={docType}>
                      {docType}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="is_active">Status</Label>
              <Select
                value={filters.is_active}
                onValueChange={(value) => setFilters(prev => ({ ...prev, is_active: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Documents ({pagination.total})
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={selectedDocs.size === filteredDocuments.length && filteredDocuments.length > 0}
                onCheckedChange={toggleSelectAll}
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
              <Button onClick={fetchDocuments} variant="outline" className="mt-4">
                Retry
              </Button>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No documents found</p>
              <p className="text-sm">Try adjusting your filters</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredDocuments.map((doc) => (
                <div key={doc.id} className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50">
                  <Checkbox
                    checked={selectedDocs.has(doc.id)}
                    onCheckedChange={() => toggleDocSelection(doc.id)}
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium truncate">
                        {doc.title || `${doc.jurisdiction} - ${doc.document_type}`}
                      </h4>
                      <Badge variant={doc.is_active ? 'default' : 'secondary'}>
                        {doc.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      <span className="font-medium">Jurisdiction:</span> {doc.jurisdiction} • 
                      <span className="font-medium ml-2">Type:</span> {doc.document_type} • 
                      <span className="font-medium ml-2">Version:</span> {doc.document_version || 'Unknown'}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Chunk {doc.chunk_sequence} • 
                      Updated: {new Date(doc.last_updated_at).toLocaleDateString()}
                      {doc.publish_date && ` • Published: ${new Date(doc.publish_date).toLocaleDateString()}`}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination.total > pagination.limit && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} documents
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  disabled={pagination.page === 1 || loading}
                  variant="outline"
                  size="sm"
                >
                  Previous
                </Button>
                <Button
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  disabled={!pagination.has_more || loading}
                  variant="outline"
                  size="sm"
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
