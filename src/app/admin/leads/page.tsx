'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  Users,
  Mail,
  Phone,
  Building,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  MessageCircle,
  TrendingUp
} from 'lucide-react'

interface Lead {
  id: string
  email: string
  name?: string
  phone?: string
  company?: string
  source: string
  campaign?: string
  message?: string
  status: string
  last_contact?: string
  created_at: string
  updated_at: string
}

export default function LeadManagement() {
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchLeads()
  }, [])

  const fetchLeads = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/leads/capture')
      if (!response.ok) {
        throw new Error('Failed to fetch leads')
      }

      const data = await response.json()
      if (data.success) {
        setLeads(data.leads || [])
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshLeads = async () => {
    setRefreshing(true)
    await fetchLeads()
    setRefreshing(false)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'new':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">New</Badge>
      case 'contacted':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Contacted</Badge>
      case 'qualified':
        return <Badge variant="default" className="bg-green-100 text-green-800">Qualified</Badge>
      case 'converted':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Converted</Badge>
      case 'lost':
        return <Badge variant="destructive">Lost</Badge>
      case 'updated':
        return <Badge variant="outline">Updated</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'website':
        return <Users className="h-4 w-4" />
      case 'newsletter':
        return <Mail className="h-4 w-4" />
      case 'social':
        return <MessageCircle className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const filteredLeads = leads.filter(lead => {
    if (activeTab === 'all') return true
    return lead.status === activeTab
  })

  const leadStats = {
    total: leads.length,
    new: leads.filter(l => l.status === 'new').length,
    contacted: leads.filter(l => l.status === 'contacted').length,
    qualified: leads.filter(l => l.status === 'qualified').length,
    converted: leads.filter(l => l.status === 'converted').length
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchLeads} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Lead Management</h1>
          <p className="text-gray-600 mt-2">
            Track and manage your leads, prospects, and contacts from various sources. Organize your leads database and manage prospect contacts effectively.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Leads Prospects Contacts
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={refreshLeads}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Lead Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Leads</p>
                <p className="text-2xl font-bold text-gray-900">{leadStats.total}</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">New</p>
                <p className="text-2xl font-bold text-blue-600">{leadStats.new}</p>
              </div>
              <Mail className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Contacted</p>
                <p className="text-2xl font-bold text-yellow-600">{leadStats.contacted}</p>
              </div>
              <Phone className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Qualified</p>
                <p className="text-2xl font-bold text-green-600">{leadStats.qualified}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Converted</p>
                <p className="text-2xl font-bold text-purple-600">{leadStats.converted}</p>
              </div>
              <Building className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lead Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="new">New</TabsTrigger>
          <TabsTrigger value="contacted">Contacted</TabsTrigger>
          <TabsTrigger value="qualified">Qualified</TabsTrigger>
          <TabsTrigger value="converted">Converted</TabsTrigger>
          <TabsTrigger value="lost">Lost</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === 'all' ? 'All Leads' :
                 activeTab === 'new' ? 'New Leads' :
                 activeTab === 'contacted' ? 'Contacted Leads' :
                 activeTab === 'qualified' ? 'Qualified Leads' :
                 activeTab === 'converted' ? 'Converted Leads' : 'Lost Leads'}
              </CardTitle>
              <CardDescription>
                {filteredLeads.length} lead{filteredLeads.length !== 1 ? 's' : ''}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredLeads.map((lead) => (
                  <div key={lead.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {getSourceIcon(lead.source)}
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {lead.name || lead.email}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {lead.email} {lead.phone && `• ${lead.phone}`}
                          </p>
                          {lead.company && (
                            <p className="text-sm text-gray-500">
                              <Building className="h-3 w-3 inline mr-1" />
                              {lead.company}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(lead.status)}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <p className="text-gray-600">Source</p>
                        <p className="font-semibold capitalize">{lead.source}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Campaign</p>
                        <p className="font-semibold">{lead.campaign || 'Organic'}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Created</p>
                        <p className="font-semibold">{new Date(lead.created_at).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Last Contact</p>
                        <p className="font-semibold">
                          {lead.last_contact ?
                            new Date(lead.last_contact).toLocaleDateString() :
                            'Never'
                          }
                        </p>
                      </div>
                    </div>

                    {lead.message && (
                      <div className="mb-3">
                        <p className="text-gray-600 text-sm">Message:</p>
                        <p className="text-sm bg-gray-50 p-2 rounded">{lead.message}</p>
                      </div>
                    )}

                    <div className="flex space-x-2 pt-2 border-t border-gray-200">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Mail className="h-4 w-4 mr-1" />
                        Email
                      </Button>
                      <Button variant="outline" size="sm">
                        <Phone className="h-4 w-4 mr-1" />
                        Call
                      </Button>
                    </div>
                  </div>
                ))}

                {filteredLeads.length === 0 && (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">
                      {activeTab === 'all' ? 'No leads yet' : `No ${activeTab} leads`}
                    </p>
                    <p className="text-gray-400 text-sm mt-2">
                      Leads will appear here when users submit contact forms or sign up for newsletters
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
