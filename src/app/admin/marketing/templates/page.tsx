'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Mail,
  Plus,
  Eye,
  Edit,
  Copy,
  Trash2,
  ArrowLeft,
  FileText,
  Zap,
  Heart,
  Gift
} from 'lucide-react'
import Link from 'next/link'

interface EmailTemplate {
  id: string
  name: string
  description: string
  type: string
  category: string
  subject: string
  previewText: string
  content: string
  isActive: boolean
  usageCount: number
  lastUsed?: string
  createdAt: string
}

export default function EmailTemplates() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('all')

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      setLoading(true)

      // Mock templates data (would come from API in real implementation)
      const mockTemplates: EmailTemplate[] = [
        {
          id: 'template_welcome',
          name: 'Welcome Email',
          description: 'Welcome new users to Ordrly',
          type: 'transactional',
          category: 'onboarding',
          subject: 'Welcome to Ordrly - Get Started Today!',
          previewText: 'Thanks for joining Ordrly. Here\'s how to get started...',
          content: 'Welcome to Ordrly! We\'re excited to have you on board...',
          isActive: true,
          usageCount: 156,
          lastUsed: '2024-01-28T10:00:00Z',
          createdAt: '2024-01-15T09:00:00Z'
        },
        {
          id: 'template_upgrade_nudge',
          name: 'Upgrade Nudge',
          description: 'Encourage free users to upgrade to Pro',
          type: 'marketing',
          category: 'conversion',
          subject: 'Unlock unlimited searches with Ordrly Pro',
          previewText: 'You\'re almost at your search limit. Upgrade to Pro for unlimited access...',
          content: 'Hi there! We noticed you\'re getting close to your monthly search limit...',
          isActive: true,
          usageCount: 89,
          lastUsed: '2024-01-27T14:30:00Z',
          createdAt: '2024-01-10T11:00:00Z'
        },
        {
          id: 'template_newsletter',
          name: 'Monthly Newsletter',
          description: 'Monthly updates and real estate insights',
          type: 'newsletter',
          category: 'engagement',
          subject: 'Ordrly Monthly: Real Estate Trends & Updates',
          previewText: 'This month\'s real estate insights, new features, and compliance updates...',
          content: 'Welcome to this month\'s Ordrly newsletter! Here\'s what\'s new...',
          isActive: true,
          usageCount: 234,
          lastUsed: '2024-01-25T16:00:00Z',
          createdAt: '2024-01-05T08:00:00Z'
        },
        {
          id: 'template_re_engagement',
          name: 'Win Back Inactive Users',
          description: 'Re-engage users who haven\'t used Ordrly recently',
          type: 'marketing',
          category: 'retention',
          subject: 'We miss you! Come back to Ordrly',
          previewText: 'It\'s been a while since your last search. Here\'s what you\'ve missed...',
          content: 'We noticed you haven\'t used Ordrly in a while. We\'d love to have you back...',
          isActive: true,
          usageCount: 67,
          lastUsed: '2024-01-26T12:00:00Z',
          createdAt: '2024-01-08T13:00:00Z'
        },
        {
          id: 'template_feature_announcement',
          name: 'New Feature Announcement',
          description: 'Announce new features and updates',
          type: 'announcement',
          category: 'product',
          subject: 'New Feature: Enhanced Search Analytics',
          previewText: 'We\'ve added powerful new analytics to help you track your searches...',
          content: 'Exciting news! We\'ve just launched enhanced search analytics...',
          isActive: false,
          usageCount: 12,
          lastUsed: '2024-01-20T09:00:00Z',
          createdAt: '2024-01-18T10:00:00Z'
        }
      ]

      setTemplates(mockTemplates)
    } catch (err) {
      console.error('Error fetching templates:', err)
    } finally {
      setLoading(false)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'transactional':
        return <Mail className="h-4 w-4" />
      case 'marketing':
        return <Zap className="h-4 w-4" />
      case 'newsletter':
        return <FileText className="h-4 w-4" />
      case 'announcement':
        return <Gift className="h-4 w-4" />
      default:
        return <Mail className="h-4 w-4" />
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'transactional':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Transactional</Badge>
      case 'marketing':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Marketing</Badge>
      case 'newsletter':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Newsletter</Badge>
      case 'announcement':
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Announcement</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const categories = [
    { value: 'all', label: 'All Templates' },
    { value: 'onboarding', label: 'Onboarding' },
    { value: 'conversion', label: 'Conversion' },
    { value: 'engagement', label: 'Engagement' },
    { value: 'retention', label: 'Retention' },
    { value: 'product', label: 'Product Updates' }
  ]

  const filteredTemplates = templates.filter(template =>
    selectedCategory === 'all' || template.category === selectedCategory
  )

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <div className="flex items-center mb-2">
            <Link href="/admin/marketing">
              <Button variant="ghost" size="sm" className="mr-2">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Marketing
              </Button>
            </Link>
            <span className="text-gray-400">/</span>
            <span className="ml-2 text-gray-600">Templates</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Email Templates</h1>
          <p className="text-gray-600 mt-2">
            Create and manage reusable email templates and designs for your campaigns. Customize layouts and design elements for professional email marketing.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Template Design Layout Manager
          </div>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Import Template
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>
      </div>

      {/* Template Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Templates</p>
                <p className="text-2xl font-bold text-gray-900">{templates.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-green-600">
                  {templates.filter(t => t.isActive).length}
                </p>
              </div>
              <Zap className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Most Used</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.max(...templates.map(t => t.usageCount), 0)}
                </p>
              </div>
              <Heart className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Categories</p>
                <p className="text-2xl font-bold text-orange-600">
                  {new Set(templates.map(t => t.category)).size}
                </p>
              </div>
              <Gift className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Category Filter */}
      <div className="flex space-x-2 mb-6 overflow-x-auto">
        {categories.map((category) => (
          <Button
            key={category.value}
            variant={selectedCategory === category.value ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category.value)}
            className="whitespace-nowrap"
          >
            {category.label}
          </Button>
        ))}
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  {getTypeIcon(template.type)}
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                </div>
                <div className="flex items-center space-x-2">
                  {getTypeBadge(template.type)}
                  {template.isActive && (
                    <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
                  )}
                </div>
              </div>
              <CardDescription>{template.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Subject:</p>
                  <p className="text-sm text-gray-600">{template.subject}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Preview:</p>
                  <p className="text-sm text-gray-600 line-clamp-2">{template.previewText}</p>
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Used {template.usageCount} times</span>
                  {template.lastUsed && (
                    <span>Last used {new Date(template.lastUsed).toLocaleDateString()}</span>
                  )}
                </div>
                <div className="flex space-x-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {selectedCategory === 'all' ? 'No templates yet' : `No ${selectedCategory} templates`}
          </h3>
          <p className="text-gray-500 mb-6">
            Create your first email template to get started with campaigns
          </p>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>
      )}
    </div>
  )
}
