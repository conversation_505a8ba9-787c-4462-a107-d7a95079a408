'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Layout,
  Plus,
  Edit,
  Eye,
  Copy,
  Trash2,
  <PERSON><PERSON>,
  Settings,
  Globe
} from 'lucide-react'

interface LandingPage {
  id: string
  name: string
  url: string
  status: string
  template: string
  conversions: number
  visitors: number
  conversionRate: number
  lastModified: string
}

export default function LandingPageBuilder() {
  const [pages] = useState<LandingPage[]>([
    {
      id: '1',
      name: 'Pro Upgrade Landing Page',
      url: '/upgrade-pro',
      status: 'published',
      template: 'Conversion Template',
      conversions: 45,
      visitors: 1200,
      conversionRate: 3.75,
      lastModified: '2024-01-20T10:00:00Z'
    },
    {
      id: '2',
      name: 'Free Trial Landing Page',
      url: '/free-trial',
      status: 'draft',
      template: 'Lead Generation Template',
      conversions: 0,
      visitors: 0,
      conversionRate: 0,
      lastModified: '2024-01-22T14:30:00Z'
    }
  ])

  const templates = [
    { name: 'Conversion Template', description: 'Optimized for conversions' },
    { name: 'Lead Generation Template', description: 'Perfect for capturing leads' },
    { name: 'Product Launch Template', description: 'Showcase new features' },
    { name: 'Event Registration Template', description: 'Drive event signups' }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="default" className="bg-green-100 text-green-800">Published</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      case 'archived':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Archived</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Landing Page Builder</h1>
          <p className="text-gray-600 mt-2">
            Create and manage landing pages with our page builder and templates
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Palette className="h-4 w-4 mr-2" />
            Templates
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Page
          </Button>
        </div>
      </div>

      {/* Page Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Pages</p>
                <p className="text-2xl font-bold text-gray-900">{pages.length}</p>
              </div>
              <Layout className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-green-600">
                  {pages.filter(p => p.status === 'published').length}
                </p>
              </div>
              <Globe className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Visitors</p>
                <p className="text-2xl font-bold text-purple-600">
                  {pages.reduce((sum, p) => sum + p.visitors, 0)}
                </p>
              </div>
              <Eye className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Conversion</p>
                <p className="text-2xl font-bold text-orange-600">
                  {pages.length > 0 ?
                    (pages.reduce((sum, p) => sum + p.conversionRate, 0) / pages.length).toFixed(1) : 0
                  }%
                </p>
              </div>
              <Settings className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Landing Pages List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Your Landing Pages</CardTitle>
            <CardDescription>
              Manage your existing landing pages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pages.map((page) => (
                <div key={page.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-medium text-gray-900">{page.name}</h3>
                      <p className="text-sm text-gray-500">{page.url}</p>
                    </div>
                    {getStatusBadge(page.status)}
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                    <div>
                      <p className="text-gray-600">Visitors</p>
                      <p className="font-semibold">{page.visitors}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Conversions</p>
                      <p className="font-semibold">{page.conversions}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Rate</p>
                      <p className="font-semibold">{page.conversionRate}%</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center text-xs text-gray-500 mb-3">
                    <span>Template: {page.template}</span>
                    <span>Modified {new Date(page.lastModified).toLocaleDateString()}</span>
                  </div>

                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      Preview
                    </Button>
                    <Button variant="outline" size="sm">
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              {pages.length === 0 && (
                <div className="text-center py-8">
                  <Layout className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No landing pages yet</p>
                  <Button className="mt-4">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Page
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Available Templates</CardTitle>
            <CardDescription>
              Choose from our pre-built templates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {templates.map((template, index) => (
                <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">{template.name}</h3>
                    <Button variant="outline" size="sm">
                      Use Template
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500">{template.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Page Builder Tools */}
      <Card>
        <CardHeader>
          <CardTitle>Page Builder Tools</CardTitle>
          <CardDescription>
            Advanced tools for creating high-converting landing pages
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 border rounded-lg">
              <Layout className="h-12 w-12 text-blue-500 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">Drag & Drop Builder</h3>
              <p className="text-sm text-gray-600">
                Build pages visually with our intuitive drag and drop interface
              </p>
            </div>

            <div className="text-center p-6 border rounded-lg">
              <Palette className="h-12 w-12 text-purple-500 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">Custom Styling</h3>
              <p className="text-sm text-gray-600">
                Customize colors, fonts, and layouts to match your brand
              </p>
            </div>

            <div className="text-center p-6 border rounded-lg">
              <Settings className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">A/B Testing</h3>
              <p className="text-sm text-gray-600">
                Test different versions to optimize conversion rates
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
