'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Eye,
  TrendingUp,
  Filter,
  RefreshCw,
  Target,
  Zap
} from 'lucide-react'

interface Segment {
  id: string
  name: string
  description: string
  criteria: Record<string, unknown>
  type: string
  size: number
  percentage: number
  growth: string
  lastUpdated: string
  status: string
}

interface SegmentData {
  segments: Segment[]
  summary: {
    totalSegments: number
    totalUsers: number
    averageSegmentSize: number
    mostActiveSegment: Segment | null
  }
}

export default function UserSegmentation() {
  const [segmentData, setSegmentData] = useState<SegmentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  const [refreshing, setRefreshing] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)

  useEffect(() => {
    fetchSegments()
  }, [])

  const fetchSegments = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/segments')
      if (!response.ok) {
        throw new Error('Failed to fetch segments')
      }

      const data = await response.json()
      if (data.success) {
        setSegmentData(data.segments)
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshSegments = async () => {
    setRefreshing(true)
    await fetchSegments()
    setRefreshing(false)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
      case 'inactive':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Inactive</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getGrowthColor = (growth: string) => {
    if (growth.startsWith('+')) return 'text-green-600'
    if (growth.startsWith('-')) return 'text-red-600'
    return 'text-gray-600'
  }

  const filteredSegments = segmentData?.segments.filter(segment => {
    if (activeTab === 'all') return true
    return segment.status === activeTab
  }) || []

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchSegments} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  if (showCreateForm) {
    return (
      <div className="max-w-4xl mx-auto py-16 px-4">
        <div className="flex items-center mb-8">
          <Button
            variant="ghost"
            onClick={() => setShowCreateForm(false)}
            className="mr-4"
          >
            ← Back to Segments
          </Button>
          <h1 className="text-3xl font-bold text-gray-900">Create User Segment</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>New User Segment</CardTitle>
            <CardDescription>
              Define criteria to automatically group users for targeted campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Segment Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., High-Value Users"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe this segment..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Segment Type
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="dynamic">Dynamic (Auto-updating)</option>
                  <option value="static">Static (Fixed list)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Criteria
                </label>
                <div className="space-y-3">
                  <div className="flex space-x-3">
                    <select className="flex-1 px-3 py-2 border border-gray-300 rounded-md">
                      <option>Subscription Tier</option>
                      <option>Usage Activity</option>
                      <option>Registration Date</option>
                      <option>Search Count</option>
                    </select>
                    <select className="flex-1 px-3 py-2 border border-gray-300 rounded-md">
                      <option>equals</option>
                      <option>greater than</option>
                      <option>less than</option>
                      <option>contains</option>
                    </select>
                    <input
                      type="text"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Value"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Condition
                  </Button>
                </div>
              </div>

              <div className="flex space-x-4">
                <Button className="flex-1">
                  <Target className="h-4 w-4 mr-2" />
                  Create Segment
                </Button>
                <Button variant="outline" className="flex-1">
                  Preview Users
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Segmentation</h1>
          <p className="text-gray-600 mt-2">
            Create and manage user segments, groups, and filters with custom criteria for targeted marketing campaigns. Define segment criteria and group users by specific filters.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            Segment Group Filter Criteria
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={refreshSegments}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Segment
          </Button>
        </div>
      </div>

      {/* Segment Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Segments</p>
                <p className="text-2xl font-bold text-gray-900">
                  {segmentData?.summary.totalSegments || 0}
                </p>
              </div>
              <Target className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-green-600">
                  {segmentData?.summary.totalUsers || 0}
                </p>
              </div>
              <Users className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Segment Size</p>
                <p className="text-2xl font-bold text-purple-600">
                  {segmentData?.summary.averageSegmentSize || 0}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Most Active</p>
                <p className="text-lg font-bold text-orange-600">
                  {segmentData?.summary.mostActiveSegment?.name.split(' ')[0] || 'None'}
                </p>
              </div>
              <Zap className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Segment Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Segments</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSegments.map((segment) => (
              <Card key={segment.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{segment.name}</CardTitle>
                      <CardDescription className="mt-1">{segment.description}</CardDescription>
                    </div>
                    {getStatusBadge(segment.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Size</span>
                      <div className="text-right">
                        <span className="font-semibold">{segment.size}</span>
                        <span className="text-xs text-gray-500 ml-1">({segment.percentage}%)</span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Growth</span>
                      <span className={`font-semibold ${getGrowthColor(segment.growth)}`}>
                        {segment.growth}
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Type</span>
                      <Badge variant="outline" className="capitalize">
                        {segment.type}
                      </Badge>
                    </div>

                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span>Updated</span>
                      <span>{new Date(segment.lastUpdated).toLocaleDateString()}</span>
                    </div>

                    <div className="flex space-x-2 pt-2 border-t border-gray-200">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredSegments.length === 0 && (
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === 'all' ? 'No segments yet' : `No ${activeTab} segments`}
              </h3>
              <p className="text-gray-500 mb-6">
                Create user segments to target specific groups with personalized campaigns
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Segment
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
