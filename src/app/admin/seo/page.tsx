'use client'


import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  TrendingUp,
  Target,
  BarChart3,
  Settings,
  Globe,
  FileText
} from 'lucide-react'

export default function SEOManagement() {

  const seoData = {
    overview: {
      totalPages: 45,
      indexedPages: 42,
      averageRanking: 15,
      organicTraffic: 2847
    },
    keywords: [
      { keyword: 'property compliance', position: 3, volume: 1200, difficulty: 45 },
      { keyword: 'zoning rules', position: 8, volume: 800, difficulty: 38 },
      { keyword: 'building permits', position: 12, volume: 2100, difficulty: 52 }
    ],
    pages: [
      { url: '/search', title: 'Property Search', metaScore: 85, issues: 1 },
      { url: '/pricing', title: 'Pricing Plans', metaScore: 92, issues: 0 },
      { url: '/faq', title: 'FAQ', metaScore: 78, issues: 2 }
    ]
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">SEO Management</h1>
          <p className="text-gray-600 mt-2">
            Optimize your website with SEO tools, meta tags, keywords, and optimization strategies
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* SEO Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Pages</p>
                <p className="text-2xl font-bold text-gray-900">{seoData.overview.totalPages}</p>
              </div>
              <Globe className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Indexed Pages</p>
                <p className="text-2xl font-bold text-green-600">{seoData.overview.indexedPages}</p>
              </div>
              <Search className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Ranking</p>
                <p className="text-2xl font-bold text-purple-600">{seoData.overview.averageRanking}</p>
              </div>
              <Target className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Organic Traffic</p>
                <p className="text-2xl font-bold text-orange-600">{seoData.overview.organicTraffic}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SEO Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Keyword Rankings</CardTitle>
            <CardDescription>
              Track your keyword positions and search volume
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {seoData.keywords.map((keyword, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{keyword.keyword}</p>
                    <p className="text-xs text-gray-500">Volume: {keyword.volume}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-lg">#{keyword.position}</p>
                    <p className="text-xs text-gray-500">Difficulty: {keyword.difficulty}%</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Page Optimization</CardTitle>
            <CardDescription>
              SEO scores and issues for your pages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {seoData.pages.map((page, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{page.title}</p>
                    <p className="text-xs text-gray-500">{page.url}</p>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={page.metaScore >= 90 ? "default" : page.metaScore >= 70 ? "secondary" : "destructive"}
                        className="text-xs"
                      >
                        {page.metaScore}%
                      </Badge>
                      {page.issues > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {page.issues} issues
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SEO Tools */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2" />
              Keyword Research
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Discover new keywords and analyze search trends
            </p>
            <Button className="w-full">
              Research Keywords
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Meta Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Optimize meta titles and descriptions
            </p>
            <Button className="w-full">
              Edit Meta Tags
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Site Audit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Run comprehensive SEO audit
            </p>
            <Button className="w-full">
              Start Audit
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
