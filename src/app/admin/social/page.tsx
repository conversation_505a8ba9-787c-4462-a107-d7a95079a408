'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Share2,
  Plus,
  Calendar,
  Twitter,
  Facebook,
  Linkedin,
  Instagram,
  BarChart3,
  Clock,
  Users,
  Heart
} from 'lucide-react'

interface SocialPost {
  id: string
  content: string
  platforms: string[]
  status: string
  scheduledFor?: string
  publishedAt?: string
  engagement: {
    likes: number
    shares: number
    comments: number
  }
}

export default function SocialMediaManagement() {
  const [posts] = useState<SocialPost[]>([
    {
      id: '1',
      content: 'New feature alert! 🚨 Check property compliance rules quickly with our updated search tool.',
      platforms: ['twitter', 'linkedin'],
      status: 'published',
      publishedAt: '2024-01-20T10:00:00Z',
      engagement: { likes: 45, shares: 12, comments: 8 }
    },
    {
      id: '2',
      content: 'Planning a home improvement project? Make sure you know the rules first! 🏠',
      platforms: ['facebook', 'twitter'],
      status: 'scheduled',
      scheduledFor: '2024-01-25T14:00:00Z',
      engagement: { likes: 0, shares: 0, comments: 0 }
    }
  ])

  const platforms = [
    { name: 'Twitter', icon: Twitter, color: 'text-blue-400', connected: true },
    { name: 'Facebook', icon: Facebook, color: 'text-blue-600', connected: true },
    { name: 'LinkedIn', icon: Linkedin, color: 'text-blue-700', connected: false },
    { name: 'Instagram', icon: Instagram, color: 'text-pink-500', connected: false }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="default" className="bg-green-100 text-green-800">Published</Badge>
      case 'scheduled':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Scheduled</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'twitter':
        return <Twitter className="h-4 w-4 text-blue-400" />
      case 'facebook':
        return <Facebook className="h-4 w-4 text-blue-600" />
      case 'linkedin':
        return <Linkedin className="h-4 w-4 text-blue-700" />
      case 'instagram':
        return <Instagram className="h-4 w-4 text-pink-500" />
      default:
        return <Share2 className="h-4 w-4" />
    }
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Social Media Management</h1>
          <p className="text-gray-600 mt-2">
            Create, schedule, and manage your social media posts across Twitter, Facebook, and other platforms
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Post
          </Button>
        </div>
      </div>

      {/* Social Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Posts</p>
                <p className="text-2xl font-bold text-gray-900">{posts.length}</p>
              </div>
              <Share2 className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Scheduled</p>
                <p className="text-2xl font-bold text-blue-600">
                  {posts.filter(p => p.status === 'scheduled').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Likes</p>
                <p className="text-2xl font-bold text-red-600">
                  {posts.reduce((sum, p) => sum + p.engagement.likes, 0)}
                </p>
              </div>
              <Heart className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Shares</p>
                <p className="text-2xl font-bold text-green-600">
                  {posts.reduce((sum, p) => sum + p.engagement.shares, 0)}
                </p>
              </div>
              <Users className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Platform Connections & Posts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Connected Platforms</CardTitle>
            <CardDescription>
              Manage your social media platform connections
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {platforms.map((platform) => {
                const IconComponent = platform.icon
                return (
                  <div key={platform.name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <IconComponent className={`h-6 w-6 ${platform.color}`} />
                      <span className="font-medium">{platform.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {platform.connected ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">Connected</Badge>
                      ) : (
                        <Badge variant="outline">Not Connected</Badge>
                      )}
                      <Button variant="outline" size="sm">
                        {platform.connected ? 'Disconnect' : 'Connect'}
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Posts</CardTitle>
            <CardDescription>
              Your latest social media activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {posts.map((post) => (
                <div key={post.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <p className="text-sm text-gray-900 flex-1">{post.content}</p>
                    {getStatusBadge(post.status)}
                  </div>

                  <div className="flex items-center space-x-2 mb-3">
                    {post.platforms.map((platform) => (
                      <div key={platform} className="flex items-center">
                        {getPlatformIcon(platform)}
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <div className="flex space-x-4">
                      <span>❤️ {post.engagement.likes}</span>
                      <span>🔄 {post.engagement.shares}</span>
                      <span>💬 {post.engagement.comments}</span>
                    </div>
                    <span>
                      {post.publishedAt ?
                        `Published ${new Date(post.publishedAt).toLocaleDateString()}` :
                        post.scheduledFor ?
                        `Scheduled for ${new Date(post.scheduledFor).toLocaleDateString()}` :
                        'Draft'
                      }
                    </span>
                  </div>
                </div>
              ))}

              {posts.length === 0 && (
                <div className="text-center py-8">
                  <Share2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No posts yet</p>
                  <Button className="mt-4">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Post
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Post Composer */}
      <Card>
        <CardHeader>
          <CardTitle>Create New Post</CardTitle>
          <CardDescription>
            Compose and schedule posts for your social media platforms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <textarea
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="What's happening? Share your latest updates..."
            />

            <div className="flex items-center justify-between">
              <div className="flex space-x-4">
                <label className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <Twitter className="h-4 w-4 text-blue-400" />
                  <span className="text-sm">Twitter</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <Facebook className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">Facebook</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <Linkedin className="h-4 w-4 text-blue-700" />
                  <span className="text-sm">LinkedIn</span>
                </label>
              </div>

              <div className="flex space-x-2">
                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule
                </Button>
                <Button>
                  Post Now
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
