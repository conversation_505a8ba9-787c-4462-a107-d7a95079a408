'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import { toast } from 'sonner'

interface Subscription {
  id: string
  status: string
  created: string
  price_id: string
  amount: number
}

interface SubscriptionData {
  customer_id: string
  total_subscriptions: number
  subscriptions: Subscription[]
}

export default function SubscriptionCleanupPage() {
  const [loading, setLoading] = useState(false)
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null)
  const [customerId, setCustomerId] = useState('')

  const listSubscriptions = async () => {
    if (!customerId.trim()) {
      toast.error('Please enter a customer ID')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/admin/cleanup-subscriptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          customerId: customerId.trim()
        })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch subscriptions')
      }

      setSubscriptionData(data)
      toast.success(`Found ${data.total_subscriptions} subscriptions`)
    } catch (error) {
      console.error('Error listing subscriptions:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to list subscriptions')
    } finally {
      setLoading(false)
    }
  }

  const cancelDuplicates = async () => {
    if (!customerId.trim()) {
      toast.error('Please enter a customer ID')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/admin/cleanup-subscriptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'cancel_duplicates',
          customerId: customerId.trim()
        })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to cancel duplicates')
      }

      toast.success(data.message)
      // Refresh the list
      await listSubscriptions()
    } catch (error) {
      console.error('Error canceling duplicates:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to cancel duplicates')
    } finally {
      setLoading(false)
    }
  }

  const cancelSpecific = async (subscriptionId: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/cleanup-subscriptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'cancel',
          customerId: customerId.trim(),
          subscriptionId
        })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to cancel subscription')
      }

      toast.success(data.message)
      // Refresh the list
      await listSubscriptions()
    } catch (error) {
      console.error('Error canceling subscription:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to cancel subscription')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
      case 'canceled':
        return <Badge variant="secondary">Canceled</Badge>
      case 'incomplete':
        return <Badge variant="destructive">Incomplete</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Subscription Cleanup</h1>
        <p className="text-muted-foreground mt-2">
          Manage and clean up duplicate subscriptions for customers
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Customer Lookup</CardTitle>
          <CardDescription>
            Enter the Stripe customer ID to view and manage subscriptions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <input
              type="text"
              placeholder="cus_xxxxxxxxxx"
              value={customerId}
              onChange={(e) => setCustomerId(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button onClick={listSubscriptions} disabled={loading}>
              {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : 'List Subscriptions'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {subscriptionData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Subscriptions for {subscriptionData.customer_id}</span>
              <div className="flex gap-2">
                {subscriptionData.total_subscriptions > 1 && (
                  <Button 
                    onClick={cancelDuplicates} 
                    disabled={loading}
                    variant="destructive"
                    size="sm"
                  >
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Cancel Duplicates
                  </Button>
                )}
              </div>
            </CardTitle>
            <CardDescription>
              Total: {subscriptionData.total_subscriptions} subscriptions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {subscriptionData.subscriptions.map((sub) => (
                <div key={sub.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">{sub.id}</code>
                      {getStatusBadge(sub.status)}
                    </div>
                    {sub.status === 'active' && (
                      <Button
                        onClick={() => cancelSpecific(sub.id)}
                        disabled={loading}
                        variant="outline"
                        size="sm"
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Cancel
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div>
                      <strong>Created:</strong> {new Date(sub.created).toLocaleDateString()}
                    </div>
                    <div>
                      <strong>Amount:</strong> ${(sub.amount / 100).toFixed(2)}
                    </div>
                    <div>
                      <strong>Price ID:</strong> {sub.price_id || 'N/A'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
