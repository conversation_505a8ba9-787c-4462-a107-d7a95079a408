'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import {
  TestTube,
  Plus,
  Play,
  Pause,
  Edit,
  Trash2,
  Eye,
  TrendingUp,
  Target,
  BarChart3,
  RefreshCw
} from 'lucide-react'

interface ABTest {
  id: string
  name: string
  description: string
  type: string
  status: string
  variants: Array<{
    name: string
    traffic: number
    conversions: number
    conversionRate: number
  }>
  metrics: {
    participants: number
    conversions: number
    conversionRate: number
    confidence: number
    winner?: string
  }
  startDate: string
  endDate?: string
  duration: number
}

interface ABTestData {
  tests: ABTest[]
  summary: {
    totalTests: number
    activeTests: number
    completedTests: number
    averageConversionRate: number
  }
  insights: string[]
}

export default function ABTesting() {
  const [testData, setTestData] = useState<ABTestData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  const [refreshing, setRefreshing] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)

  useEffect(() => {
    fetchTests()
  }, [])

  const fetchTests = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/ab-tests')
      if (!response.ok) {
        throw new Error('Failed to fetch A/B tests')
      }

      const data = await response.json()
      if (data.success) {
        setTestData(data.tests)
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshTests = async () => {
    setRefreshing(true)
    await fetchTests()
    setRefreshing(false)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return <Badge variant="default" className="bg-green-100 text-green-800">Running</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      case 'completed':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Completed</Badge>
      case 'paused':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Paused</Badge>
      case 'stopped':
        return <Badge variant="destructive">Stopped</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 95) return 'text-green-600'
    if (confidence >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  const filteredTests = testData?.tests.filter(test => {
    if (activeTab === 'all') return true
    return test.status === activeTab
  }) || []

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchTests} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  if (showCreateForm) {
    return (
      <div className="max-w-4xl mx-auto py-16 px-4">
        <div className="flex items-center mb-8">
          <Button
            variant="ghost"
            onClick={() => setShowCreateForm(false)}
            className="mr-4"
          >
            ← Back to Tests
          </Button>
          <h1 className="text-3xl font-bold text-gray-900">Create A/B Test</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>New A/B Test</CardTitle>
            <CardDescription>
              Set up a new experiment to test different variations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Test Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Email Subject Line Test"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe what you're testing..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Test Type
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="email">Email Campaign</option>
                  <option value="landing_page">Landing Page</option>
                  <option value="pricing">Pricing Page</option>
                  <option value="signup_flow">Signup Flow</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Traffic Split
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Variant A</label>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="50"
                      min="0"
                      max="100"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Variant B</label>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="50"
                      min="0"
                      max="100"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Success Metric
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="click_rate">Click Rate</option>
                  <option value="conversion_rate">Conversion Rate</option>
                  <option value="signup_rate">Signup Rate</option>
                  <option value="engagement_rate">Engagement Rate</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (days)
                  </label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="14"
                    min="1"
                  />
                </div>
              </div>

              <div className="flex space-x-4">
                <Button className="flex-1">
                  <TestTube className="h-4 w-4 mr-2" />
                  Create Test
                </Button>
                <Button variant="outline" className="flex-1">
                  Save as Draft
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">A/B Testing</h1>
          <p className="text-gray-600 mt-2">
            Create and manage A/B tests, split tests, variants, and experiments to optimize your marketing campaigns. Run split test experiments with multiple variants.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            A/B Test Split Test Variant Experiment
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={refreshTests}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Test
          </Button>
        </div>
      </div>

      {/* Test Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Tests</p>
                <p className="text-2xl font-bold text-gray-900">
                  {testData?.summary.totalTests || 0}
                </p>
              </div>
              <TestTube className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Tests</p>
                <p className="text-2xl font-bold text-green-600">
                  {testData?.summary.activeTests || 0}
                </p>
              </div>
              <Play className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-purple-600">
                  {testData?.summary.completedTests || 0}
                </p>
              </div>
              <Target className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Conversion</p>
                <p className="text-2xl font-bold text-orange-600">
                  {testData?.summary.averageConversionRate || 0}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="running">Running</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="draft">Draft</TabsTrigger>
          <TabsTrigger value="paused">Paused</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <div className="space-y-6">
            {filteredTests.map((test) => (
              <Card key={test.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{test.name}</CardTitle>
                      <CardDescription className="mt-1">{test.description}</CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(test.status)}
                      <Badge variant="outline" className="capitalize">
                        {test.type}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Test Overview */}
                    <div>
                      <h4 className="font-medium text-sm text-gray-900 mb-3">Test Overview</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Participants</span>
                          <span className="font-semibold">{test.metrics.participants}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Conversions</span>
                          <span className="font-semibold">{test.metrics.conversions}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Conversion Rate</span>
                          <span className="font-semibold">{test.metrics.conversionRate}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Confidence</span>
                          <span className={`font-semibold ${getConfidenceColor(test.metrics.confidence)}`}>
                            {test.metrics.confidence}%
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Variants */}
                    <div>
                      <h4 className="font-medium text-sm text-gray-900 mb-3">Variants</h4>
                      <div className="space-y-3">
                        {test.variants.map((variant, index) => (
                          <div key={index} className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium text-sm">{variant.name}</span>
                              {test.metrics.winner === variant.name && (
                                <Badge variant="default" className="bg-green-100 text-green-800 text-xs">
                                  Winner
                                </Badge>
                              )}
                            </div>
                            <div className="text-xs text-gray-600">
                              <div className="flex justify-between">
                                <span>Traffic: {variant.traffic}%</span>
                                <span>Conv: {variant.conversionRate}%</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Timeline */}
                    <div>
                      <h4 className="font-medium text-sm text-gray-900 mb-3">Timeline</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Started</span>
                          <span className="font-semibold">
                            {new Date(test.startDate).toLocaleDateString()}
                          </span>
                        </div>
                        {test.endDate && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Ended</span>
                            <span className="font-semibold">
                              {new Date(test.endDate).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-gray-600">Duration</span>
                          <span className="font-semibold">{test.duration} days</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-4 mt-4 border-t border-gray-200">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                    <Button variant="outline" size="sm">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Analytics
                    </Button>
                    {test.status === 'running' ? (
                      <Button variant="outline" size="sm">
                        <Pause className="h-4 w-4 mr-1" />
                        Pause
                      </Button>
                    ) : test.status === 'paused' ? (
                      <Button variant="outline" size="sm">
                        <Play className="h-4 w-4 mr-1" />
                        Resume
                      </Button>
                    ) : (
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTests.length === 0 && (
            <div className="text-center py-12">
              <TestTube className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === 'all' ? 'No tests yet' : `No ${activeTab} tests`}
              </h3>
              <p className="text-gray-500 mb-6">
                Create A/B tests to optimize your campaigns and improve conversion rates
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Test
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
