import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default async function AdvancedSearchPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has access to advanced search
  const hasAccess = profile.subscription_tier === 'pro' || profile.subscription_tier === 'appraiser'

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Advanced Search
            </h1>
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Upgrade Required
              </h2>
              <p className="text-gray-600 mb-6">
                Advanced search features are available for Pro subscribers only.
              </p>
              <div className="space-y-4">
                <Link href="/pricing">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Upgrade to Pro
                  </Button>
                </Link>
                <div>
                  <Link href="/search">
                    <Button variant="outline">
                      Back to Basic Search
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Advanced Search & Filters
          </h1>
          <p className="text-gray-600 mb-8">
            Use advanced criteria and filters to refine your compliance searches with precision.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Advanced Filters */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Advanced Filters & Search Criteria</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Type
                  </label>
                  <select className="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option>All Property Types</option>
                    <option>Residential</option>
                    <option>Commercial</option>
                    <option>Industrial</option>
                    <option>Mixed Use</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Jurisdiction Level
                  </label>
                  <select className="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option>All Levels</option>
                    <option>Federal</option>
                    <option>State</option>
                    <option>County</option>
                    <option>City</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Compliance Status
                  </label>
                  <select className="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option>All Statuses</option>
                    <option>Allowed</option>
                    <option>Permit Required</option>
                    <option>Restricted</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Search Criteria */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Search Criteria</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Keywords
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="Enter keywords..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Range
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="date"
                      className="border border-gray-300 rounded-md px-3 py-2"
                    />
                    <input
                      type="date"
                      className="border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort By
                  </label>
                  <select className="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option>Relevance</option>
                    <option>Date (Newest)</option>
                    <option>Date (Oldest)</option>
                    <option>Jurisdiction</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-between">
            <Link href="/search">
              <Button variant="outline">
                Back to Basic Search
              </Button>
            </Link>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Search with Filters
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
