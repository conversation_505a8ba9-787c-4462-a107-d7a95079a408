import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default async function ApiAccessPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has access to API
  const hasAccess = profile.subscription_tier === 'appraiser'

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              API Access
            </h1>
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Appraiser Access Required
              </h2>
              <p className="text-gray-600 mb-6">
                API access is available for Appraiser subscribers only.
              </p>
              <div className="space-y-4">
                <Link href="/pricing">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Upgrade to Appraiser
                  </Button>
                </Link>
                <div>
                  <Link href="/search">
                    <Button variant="outline">
                      Back to Search
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            API Access & Developer Integration
          </h1>
          <p className="text-gray-600 mb-8">
            Programmatic access to property data, compliance analysis, and integration tools for developers and webhooks.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* API Keys */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">API Keys</h2>

              <div className="space-y-4">
                <div className="border border-gray-200 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-semibold text-gray-900">Production Key</h3>
                    <span className="text-sm text-green-600">Active</span>
                  </div>
                  <div className="bg-gray-50 p-2 rounded font-mono text-sm text-gray-700 mb-3">
                    ordrly_prod_••••••••••••••••••••••••••••••••
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">Regenerate</Button>
                    <Button variant="outline" size="sm">Copy</Button>
                  </div>
                </div>

                <div className="border border-gray-200 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-semibold text-gray-900">Test Key</h3>
                    <span className="text-sm text-blue-600">Sandbox</span>
                  </div>
                  <div className="bg-gray-50 p-2 rounded font-mono text-sm text-gray-700 mb-3">
                    ordrly_test_••••••••••••••••••••••••••••••••
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">Regenerate</Button>
                    <Button variant="outline" size="sm">Copy</Button>
                  </div>
                </div>

                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  Generate New API Key
                </Button>
              </div>
            </div>

            {/* Usage Statistics */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Usage Statistics</h2>

              <div className="bg-gray-50 p-6 rounded-lg">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">This Month</span>
                    <span className="font-semibold text-gray-900">1,247 requests</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Rate Limit</span>
                    <span className="font-semibold text-green-600">1000/hour</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Success Rate</span>
                    <span className="font-semibold text-green-600">99.8%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Avg Response Time</span>
                    <span className="font-semibold text-blue-600">245ms</span>
                  </div>
                </div>
              </div>

              <div className="text-sm text-gray-600">
                <p className="mb-2">API Limits:</p>
                <ul className="space-y-1 ml-4">
                  <li>• 1,000 requests per hour</li>
                  <li>• 10,000 requests per month</li>
                  <li>• 99.9% uptime SLA</li>
                  <li>• Dedicated support</li>
                </ul>
              </div>
            </div>
          </div>

          {/* API Endpoints */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Available Endpoints
            </h2>
            <div className="space-y-4">
              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">Property Search</h3>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">GET</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">Search for property compliance information</p>
                <code className="text-sm bg-gray-50 p-2 rounded block">
                  GET /api/v1/search?address=123+Main+St&city=Anytown&state=CA
                </code>
              </div>

              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">Property Details</h3>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">GET</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">Get detailed property information</p>
                <code className="text-sm bg-gray-50 p-2 rounded block">
                  GET /api/v1/property/{'{property_id}'}
                </code>
              </div>

              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">Historical Data</h3>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">GET</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">Access historical property data</p>
                <code className="text-sm bg-gray-50 p-2 rounded block">
                  GET /api/v1/historical/{'{property_id}'}?from=2020-01-01&to=2024-01-01
                </code>
              </div>

              <div className="border border-gray-200 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">Bulk Search</h3>
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">POST</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">Search multiple properties at once</p>
                <code className="text-sm bg-gray-50 p-2 rounded block">
                  POST /api/v1/bulk-search
                </code>
              </div>
            </div>
          </div>

          {/* Documentation */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Documentation & Tools
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="border border-gray-200 p-4 rounded-lg text-center">
                <h3 className="font-semibold text-gray-900 mb-2">API Documentation</h3>
                <p className="text-sm text-gray-600 mb-3">Complete API reference and examples</p>
                <Button variant="outline" className="w-full">View Docs</Button>
              </div>
              <div className="border border-gray-200 p-4 rounded-lg text-center">
                <h3 className="font-semibold text-gray-900 mb-2">Code Examples</h3>
                <p className="text-sm text-gray-600 mb-3">Sample code in multiple languages</p>
                <Button variant="outline" className="w-full">View Examples</Button>
              </div>
              <div className="border border-gray-200 p-4 rounded-lg text-center">
                <h3 className="font-semibold text-gray-900 mb-2">API Testing</h3>
                <p className="text-sm text-gray-600 mb-3">Interactive API testing tool</p>
                <Button variant="outline" className="w-full">Test API</Button>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <Link href="/historical-data">
              <Button variant="outline">
                Historical Data
              </Button>
            </Link>
            <Link href="/account/data">
              <Button className="bg-green-600 hover:bg-green-700">
                Data Export
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
