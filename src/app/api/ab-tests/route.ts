import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/ab-tests - Get A/B tests
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get A/B tests
    const abTests = await getABTests()

    return NextResponse.json({
      success: true,
      tests: abTests
    })
  } catch (error) {
    console.error('Error in GET /api/ab-tests:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/ab-tests - Create new A/B test
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, variants, trafficSplit, goal, duration } = body

    if (!name || !variants || variants.length < 2) {
      return NextResponse.json({
        error: 'Name and at least 2 variants are required'
      }, { status: 400 })
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Create A/B test (mock for now)
    const testData = {
      id: `test_${Date.now()}`,
      name,
      description: description || null,
      variants,
      trafficSplit: trafficSplit || { A: 50, B: 50 },
      goal: goal || 'conversion',
      duration: duration || 14, // days
      status: 'draft',
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      message: 'A/B test created successfully',
      test: testData
    })
  } catch (error) {
    console.error('Error in POST /api/ab-tests:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getABTests() {
  try {
    // Mock A/B tests data (would come from database in real implementation)
    const mockTests = [
      {
        id: 'test_email_subject_lines',
        name: 'Email Subject Line Test',
        description: 'Testing different subject lines for upgrade nudge emails',
        variants: [
          {
            id: 'variant_a',
            name: 'Control',
            description: 'Current subject line',
            content: 'Upgrade to Pro for unlimited searches',
            traffic: 50
          },
          {
            id: 'variant_b',
            name: 'Urgency',
            description: 'Subject line with urgency',
            content: 'Only 3 searches left - Upgrade now!',
            traffic: 50
          }
        ],
        goal: 'email_open_rate',
        status: 'running',
        startDate: '2024-01-20T00:00:00Z',
        endDate: '2024-02-03T23:59:59Z',
        results: {
          variant_a: {
            participants: 245,
            conversions: 67,
            conversionRate: 27.3,
            confidence: 95
          },
          variant_b: {
            participants: 238,
            conversions: 89,
            conversionRate: 37.4,
            confidence: 98
          }
        },
        winner: 'variant_b',
        significance: 'statistically_significant',
        created_at: '2024-01-20T10:00:00Z',
        updated_at: '2024-01-28T15:30:00Z'
      },
      {
        id: 'test_pricing_page_layout',
        name: 'Pricing Page Layout Test',
        description: 'Testing different layouts for the pricing page',
        variants: [
          {
            id: 'variant_a',
            name: 'Current Layout',
            description: 'Existing pricing page design',
            content: 'Standard 3-column layout',
            traffic: 33
          },
          {
            id: 'variant_b',
            name: 'Simplified Layout',
            description: 'Cleaner, more focused design',
            content: '2-column layout with emphasis on Pro',
            traffic: 33
          },
          {
            id: 'variant_c',
            name: 'Feature Comparison',
            description: 'Detailed feature comparison table',
            content: 'Expanded feature comparison',
            traffic: 34
          }
        ],
        goal: 'subscription_conversion',
        status: 'running',
        startDate: '2024-01-25T00:00:00Z',
        endDate: '2024-02-08T23:59:59Z',
        results: {
          variant_a: {
            participants: 156,
            conversions: 12,
            conversionRate: 7.7,
            confidence: 85
          },
          variant_b: {
            participants: 149,
            conversions: 18,
            conversionRate: 12.1,
            confidence: 92
          },
          variant_c: {
            participants: 162,
            conversions: 15,
            conversionRate: 9.3,
            confidence: 88
          }
        },
        winner: null,
        significance: 'not_significant',
        created_at: '2024-01-25T09:00:00Z',
        updated_at: '2024-01-28T16:45:00Z'
      },
      {
        id: 'test_onboarding_flow',
        name: 'User Onboarding Flow Test',
        description: 'Testing different onboarding sequences for new users',
        variants: [
          {
            id: 'variant_a',
            name: 'Standard Flow',
            description: 'Current onboarding process',
            content: 'Email verification → Welcome → Tutorial',
            traffic: 50
          },
          {
            id: 'variant_b',
            name: 'Interactive Flow',
            description: 'Hands-on tutorial first',
            content: 'Email verification → Interactive demo → Welcome',
            traffic: 50
          }
        ],
        goal: 'first_search_completion',
        status: 'completed',
        startDate: '2024-01-10T00:00:00Z',
        endDate: '2024-01-24T23:59:59Z',
        results: {
          variant_a: {
            participants: 89,
            conversions: 56,
            conversionRate: 62.9,
            confidence: 94
          },
          variant_b: {
            participants: 92,
            conversions: 71,
            conversionRate: 77.2,
            confidence: 97
          }
        },
        winner: 'variant_b',
        significance: 'statistically_significant',
        created_at: '2024-01-10T08:00:00Z',
        updated_at: '2024-01-24T12:00:00Z'
      },
      {
        id: 'test_cta_button_color',
        name: 'CTA Button Color Test',
        description: 'Testing different colors for upgrade buttons',
        variants: [
          {
            id: 'variant_a',
            name: 'Blue Button',
            description: 'Current blue CTA button',
            content: '#3B82F6',
            traffic: 50
          },
          {
            id: 'variant_b',
            name: 'Green Button',
            description: 'Green CTA button',
            content: '#10B981',
            traffic: 50
          }
        ],
        goal: 'button_click_rate',
        status: 'draft',
        startDate: null,
        endDate: null,
        results: null,
        winner: null,
        significance: null,
        created_at: '2024-01-28T14:00:00Z',
        updated_at: '2024-01-28T14:00:00Z'
      }
    ]

    return {
      tests: mockTests,
      summary: {
        total: mockTests.length,
        running: mockTests.filter(t => t.status === 'running').length,
        completed: mockTests.filter(t => t.status === 'completed').length,
        draft: mockTests.filter(t => t.status === 'draft').length,
        significantWins: mockTests.filter(t => t.significance === 'statistically_significant').length
      },
      insights: [
        'Urgency-based subject lines increase open rates by 37%',
        'Interactive onboarding improves first search completion by 23%',
        'Feature comparison tables may be overwhelming for users',
        'A/B testing has improved overall conversion rates by 15%'
      ]
    }
  } catch (error) {
    console.error('Error getting A/B tests:', error)
    return {
      tests: [],
      summary: {
        total: 0,
        running: 0,
        completed: 0,
        draft: 0,
        significantWins: 0
      },
      insights: []
    }
  }
}
