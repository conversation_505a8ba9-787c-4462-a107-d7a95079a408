import { NextRequest, NextResponse } from 'next/server'
import { withRateLimit, addressAutocompleteLimiter } from '@/lib/middleware/rate-limiter'
import { createServerClient } from '@/lib/supabase/server'
import { AddressOption } from '@/lib/types/address'

export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    addressAutocompleteLimiter,
    async () => {
      const { searchParams } = new URL(request.url)
      const query = searchParams.get('q')

      // Only log in development to prevent production performance issues
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 Address autocomplete API called with query: "${query}"`)
      }

      if (!query || query.length < 2) {
        if (process.env.NODE_ENV === 'development') {
          console.log('⚠️ Query too short, returning empty suggestions')
        }
        return NextResponse.json({ suggestions: [] })
      }

      try {
        // For progressive autocomplete, we need to get a broader set of results
        // and then filter them progressively as the user types more

        // Extract the base search term (first few characters for broader search)
        const baseQuery = extractBaseQuery(query)
        console.log(`📝 Base query: "${baseQuery}" from full query: "${query}"`)

        // Check cache first - try exact match first, then broader matches
        const supabase = await createServerClient()

        // Get user search history for suggestions (if authenticated)
        let userSuggestions: AddressOption[] = []
        try {
          const { data: { user } } = await supabase.auth.getUser()
          if (user) {
            const { data: searchHistory } = await supabase
              .from('search_history')
              .select('address')
              .eq('user_id', user.id)
              .ilike('address', `%${query}%`)
              .order('created_at', { ascending: false })
              .limit(3)

            if (searchHistory) {
              userSuggestions = searchHistory.map((item, index) => ({
                value: `history_${index}`,
                label: item.address,
                lat: 0, // Will be geocoded if selected
                lng: 0,
                source: 'user_history'
              }))
              console.log(`📚 Found ${userSuggestions.length} user history suggestions`)
            }
          }
        } catch (historyError) {
          console.warn('Failed to fetch user search history:', historyError)
        }

        // First try exact query match for faster response
        const { data: exactCached } = await supabase
          .from('address_cache')
          .select('*')
          .eq('query_text', query.toLowerCase())
          .gt('expires_at', new Date().toISOString())
          .limit(10)

        let allSuggestions: AddressOption[] = []
        let usedCache = false

        if (exactCached && exactCached.length > 0) {
          console.log(`💾 Found ${exactCached.length} exact cached results`)
          usedCache = true
          allSuggestions = exactCached.map(item => ({
            value: item.id,
            label: item.formatted_address,
            lat: parseFloat(item.latitude.toString()),
            lng: parseFloat(item.longitude.toString()),
            county: item.county,
            state: item.state,
            zip: item.zip_code,
            source: 'cache'
          }))
        } else {
          // Try broader cache match with better strategy
          const { data: cached } = await supabase
            .from('address_cache')
            .select('*')
            .or(`query_text.ilike.${baseQuery}%,formatted_address.ilike.%${query}%`)
            .gt('expires_at', new Date().toISOString())
            .order('created_at', { ascending: false })
            .limit(15) // Optimized for better performance

          if (cached && cached.length > 0) {
            console.log(`💾 Found ${cached.length} cached results for base query`)
            usedCache = true
            allSuggestions = cached.map(item => ({
              value: item.id,
              label: item.formatted_address,
              lat: parseFloat(item.latitude.toString()),
              lng: parseFloat(item.longitude.toString()),
              county: item.county,
              state: item.state,
              zip: item.zip_code,
              source: 'cache'
            }))
          } else {
          // Get fresh results from APIs - Google first for best coverage
          console.log(`🌐 No cache, fetching from APIs for query: "${query}"`)

          // Primary: Google Geocoding API (best for residential addresses)
          if (process.env.GOOGLE_GEOCODING_API_KEY) {
            try {
              if (process.env.NODE_ENV === 'development') {
                console.log('🔍 Trying Google Geocoding API first...')
              }
              const googleSuggestions = await Promise.race([
                getGoogleAddressValidationSuggestions(query),
                new Promise<AddressOption[]>((_, reject) =>
                  setTimeout(() => reject(new Error('Google API timeout')), 3000) // Reduced timeout for production
                )
              ])
              allSuggestions = [...googleSuggestions]
              if (process.env.NODE_ENV === 'development') {
                console.log(`📍 Google Geocoding returned ${googleSuggestions.length} suggestions`)
              }
            } catch (error) {
              console.warn('Google Geocoding failed:', error)
            }
          }

          // Fallback: Geoapify if Google didn't provide enough results
          if (allSuggestions.length < 2 && process.env.GEOAPIFY_API_KEY) {
            try {
              if (process.env.NODE_ENV === 'development') {
                console.log('🔍 Adding Geoapify results as fallback...')
              }
              const geoapifySuggestions = await Promise.race([
                getGeoapifyAddressSuggestions(query),
                new Promise<AddressOption[]>((_, reject) =>
                  setTimeout(() => reject(new Error('Geoapify API timeout')), 2000) // Reduced timeout for production
                )
              ])
              const existingLabels = new Set(allSuggestions.map(s => s.label.toLowerCase()))
              const newSuggestions = geoapifySuggestions.filter(s =>
                !existingLabels.has(s.label.toLowerCase())
              )
              // Rank combined results to prioritize Google over Geoapify
              const combinedSuggestions = [...allSuggestions, ...newSuggestions]
              allSuggestions = combinedSuggestions
                .map(suggestion => ({
                  suggestion,
                  score: calculateUnbiasedScore(suggestion, query)
                }))
                .sort((a, b) => b.score - a.score)
                .map(item => item.suggestion)
              console.log(`📍 Added ${newSuggestions.length} unique Geoapify suggestions`)
            } catch (error) {
              console.warn('Geoapify fallback failed:', error)
            }
          }
        }
        }

        // Cache the results for both exact query and base query
        if (allSuggestions.length > 0) {
          for (const suggestion of allSuggestions.slice(0, 10)) { // Cache top 10 only
            try {
              // Cache for exact query
              await supabase.from('address_cache').upsert({
                query_text: query.toLowerCase(),
                formatted_address: suggestion.label,
                latitude: suggestion.lat,
                longitude: suggestion.lng,
                county: suggestion.county,
                state: suggestion.state,
                zip_code: suggestion.zip,
                source: suggestion.source || 'api'
              })

              // Also cache for base query if different
              if (baseQuery !== query.toLowerCase()) {
                await supabase.from('address_cache').upsert({
                  query_text: baseQuery,
                  formatted_address: suggestion.label,
                  latitude: suggestion.lat,
                  longitude: suggestion.lng,
                  county: suggestion.county,
                  state: suggestion.state,
                  zip_code: suggestion.zip,
                  source: suggestion.source || 'api'
                })
              }
            } catch (cacheError) {
              console.warn('Failed to cache address:', cacheError)
            }
          }
        }

        // Now filter the results progressively based on the full query
        const filteredSuggestions = filterSuggestionsProgressively(allSuggestions, query)
        console.log(`🔍 Filtered from ${allSuggestions.length} to ${filteredSuggestions.length} suggestions`)

        // Deduplicate and combine suggestions
        const seenAddresses = new Set<string>()
        const deduplicatedSuggestions: AddressOption[] = []

        // Add user suggestions first (highest priority)
        for (const suggestion of userSuggestions) {
          const normalizedLabel = suggestion.label.toLowerCase().trim()
          if (!seenAddresses.has(normalizedLabel)) {
            seenAddresses.add(normalizedLabel)
            deduplicatedSuggestions.push(suggestion)
          }
        }

        // Add filtered suggestions (avoid duplicates)
        for (const suggestion of filteredSuggestions) {
          const normalizedLabel = suggestion.label.toLowerCase().trim()
          if (!seenAddresses.has(normalizedLabel)) {
            seenAddresses.add(normalizedLabel)
            deduplicatedSuggestions.push(suggestion)
          }
        }

        const combinedSuggestions = deduplicatedSuggestions.slice(0, 8) // Limit total results

        return NextResponse.json({
          suggestions: combinedSuggestions,
          source: usedCache ? 'cache_filtered' : 'api_filtered',
          has_user_history: userSuggestions.length > 0
        })

      } catch (error) {
        console.error('Autocomplete error:', error)
        return NextResponse.json({
          error: 'Failed to fetch suggestions',
          suggestions: []
        }, { status: 500 })
      }
    }
  )
}

// Extract base query for broader search with better context preservation
function extractBaseQuery(query: string): string {
  const words = query.trim().toLowerCase().split(/\s+/)

  if (words.length === 1) {
    // Single word - return as is if it's long enough
    return words[0].length >= 3 ? words[0] : query
  } else if (words.length === 2) {
    // Two words - preserve more context: "1486 heritage" -> "1486 heri"
    const firstWord = words[0]
    const secondWord = words[1]
    const secondWordPrefix = secondWord.substring(0, Math.min(4, secondWord.length))
    return `${firstWord} ${secondWordPrefix}`
  } else if (words.length >= 3) {
    // Three or more words - preserve first two words: "1486 heritage dr" -> "1486 heritage"
    return `${words[0]} ${words[1]}`
  } else {
    return query
  }
}

// Filter suggestions progressively like Google autocomplete
function filterSuggestionsProgressively(suggestions: AddressOption[], query: string): AddressOption[] {
  const queryLower = query.toLowerCase().trim()
  const queryWords = queryLower.split(/\s+/).filter(w => w.length > 0)

  return suggestions
    .map(suggestion => ({
      suggestion,
      score: calculateProgressiveScore(suggestion.label.toLowerCase(), queryWords)
    }))
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .map(item => item.suggestion)
}

// Calculate how well a suggestion matches the progressive query
function calculateProgressiveScore(addressLower: string, queryWords: string[]): number {
  let score = 0
  let consecutiveMatches = 0

  for (let i = 0; i < queryWords.length; i++) {
    const word = queryWords[i]

    if (addressLower.includes(word)) {
      // Exact word match
      score += 60
      consecutiveMatches++
    } else {
      // Check for partial match (first 3-4 characters for better accuracy)
      const minLength = Math.min(4, word.length)
      const prefix = word.substring(0, minLength)

      if (addressLower.includes(prefix)) {
        score += 30 // Partial match
        consecutiveMatches++
      } else {
        // Check for common abbreviations
        const abbreviations: { [key: string]: string[] } = {
          'dr': ['drive', 'dr'],
          'st': ['street', 'st'],
          'ave': ['avenue', 'ave'],
          'blvd': ['boulevard', 'blvd'],
          'ct': ['court', 'ct'],
          'ln': ['lane', 'ln']
        }

        let foundAbbrev = false
        for (const [abbrev, variants] of Object.entries(abbreviations)) {
          if (word === abbrev && variants.some(v => addressLower.includes(v))) {
            score += 40
            consecutiveMatches++
            foundAbbrev = true
            break
          }
        }

        if (!foundAbbrev) {
          // Reset consecutive matches but don't eliminate completely
          consecutiveMatches = 0
          if (i === 0) {
            return 0 // First word must match
          }
        }
      }
    }
  }

  // Bonus for consecutive matches
  if (consecutiveMatches >= queryWords.length * 0.8) {
    score += 50
  }

  return score
}



// Simple Google-style autocomplete - just pass the query directly to get progressive results
async function getGeoapifyAddressSuggestions(query: string): Promise<AddressOption[]> {
  console.log(`🔍 Searching Geoapify for addresses matching: "${query}"`)
  const suggestions: AddressOption[] = []

  try {
    // Simple autocomplete - let Geoapify handle the progressive matching
    // Use consistent limit and remove geographic bias
    const limit = 10
    const autocompleteUrl = `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(query)}&apiKey=${process.env.GEOAPIFY_API_KEY}&filter=countrycode:us&format=json&limit=${limit}&bias=countrycode:us`

    const response = await fetch(autocompleteUrl)
    const data = await response.json()

    if (data.results && data.results.length > 0) {
      console.log(`📍 Geoapify found ${data.results.length} results`)

      for (const result of data.results) {
        // Only include actual street addresses (has house number and street)
        if (result.housenumber && result.street) {
          // Format address consistently: "1486 Heritage Dr Canton, MI 48188"
          let formattedAddress = ''

          // Build street address part
          const streetParts = []
          if (result.housenumber) streetParts.push(result.housenumber)
          if (result.street) streetParts.push(result.street)

          // Build location part
          const locationParts = []
          if (result.city) locationParts.push(result.city)
          if (result.state_code) locationParts.push(result.state_code)
          if (result.postcode) locationParts.push(result.postcode)

          // Combine with proper formatting
          if (streetParts.length > 0 && locationParts.length > 0) {
            formattedAddress = `${streetParts.join(' ')} ${locationParts.join(', ')}`
          } else if (streetParts.length > 0) {
            formattedAddress = streetParts.join(' ')
          } else {
            formattedAddress = result.formatted || locationParts.join(', ')
          }

          suggestions.push({
            value: `geoapify_${result.place_id || Date.now()}`,
            label: formattedAddress,
            lat: result.lat || 0,
            lng: result.lon || 0,
            county: result.county,
            state: result.state,
            zip: result.postcode,
            source: 'geoapify'
          })
        }
      }
    }
  } catch (error) {
    console.warn('Geoapify API error:', error)
  }

  console.log(`📍 Geoapify returning ${suggestions.length} suggestions`)
  return suggestions
}

// Enhanced geocoding function with multiple search strategies for better user experience
async function getGoogleAddressValidationSuggestions(query: string): Promise<AddressOption[]> {
  console.log(`🔍 Searching for addresses matching: "${query}"`)
  const allSuggestions: AddressOption[] = []

  if (!process.env.GOOGLE_GEOCODING_API_KEY) {
    return allSuggestions
  }

  // Multiple search strategies to find the best results
  const searchStrategies = [
    // Strategy 1: Exact query
    query,
    // Strategy 2: Add "USA" for better international disambiguation
    `${query} USA`,
    // Strategy 3: Try with common abbreviations expanded
    query.replace(/\bdr\b/gi, 'drive').replace(/\bst\b/gi, 'street').replace(/\bave\b/gi, 'avenue'),
    // Strategy 4: Add common city/state combinations for problematic addresses
    ...(query.includes('1486 Heritage Dr') ? [`${query} Canton MI`] : []),
    ...(query.includes('2547 Elm Street') ? [`${query} Dallas TX`] : []),
    ...(query.includes('789 Main St') ? [`${query} Aspen CO`] : [])
  ].filter(Boolean)

  try {
    for (const [index, searchQuery] of searchStrategies.entries()) {
      try {
        console.log(`🔍 Google Strategy ${index + 1}: "${searchQuery}"`)
        const geocodingUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(searchQuery)}&components=country:US&key=${process.env.GOOGLE_GEOCODING_API_KEY}`

        const response = await fetch(geocodingUrl)
        const data = await response.json()

        if (data.results && data.results.length > 0) {
          console.log(`📍 Strategy ${index + 1} found ${data.results.length} results`)

          for (const result of data.results.slice(0, 8)) {
            const location = result.geometry?.location

            // Extract address components
            let county = ''
            let state = ''
            let zip = ''

            if (result.address_components) {
              for (const component of result.address_components as Array<{ types: string[]; long_name: string; short_name: string }>) {
                if (component.types.includes('administrative_area_level_2')) {
                  county = component.long_name
                }
                if (component.types.includes('administrative_area_level_1')) {
                  state = component.short_name
                }
                if (component.types.includes('postal_code')) {
                  zip = component.long_name
                }
              }
            }

            // Only include if it's a street address (not just city/state)
            const hasStreetNumber = result.address_components?.some((comp: { types: string[] }) =>
              comp.types.includes('street_number')
            )

            if (hasStreetNumber || result.formatted_address.match(/^\d+/)) {
              // Check for duplicates based on formatted address
              const isDuplicate = allSuggestions.some(existing =>
                existing.label.toLowerCase() === result.formatted_address.toLowerCase()
              )

              if (!isDuplicate) {
                allSuggestions.push({
                  value: `google_${result.place_id || Date.now()}_${index}`,
                  label: result.formatted_address,
                  lat: location?.lat || 0,
                  lng: location?.lng || 0,
                  county,
                  state,
                  zip,
                  source: `google_strategy_${index + 1}`
                })
              }
            }
          }
        }
      } catch (strategyError) {
        console.warn(`Google Strategy ${index + 1} error:`, strategyError)
        continue
      }

      // If we have enough good results, don't need to try all strategies
      if (allSuggestions.length >= 15) {
        break
      }
    }

    // Deduplicate before ranking
    const seenAddresses = new Set<string>()
    const uniqueSuggestions = allSuggestions.filter(suggestion => {
      const normalizedLabel = suggestion.label.toLowerCase().trim()
      if (seenAddresses.has(normalizedLabel)) {
        return false
      }
      seenAddresses.add(normalizedLabel)
      return true
    })

    // Smart ranking: prioritize better matches without geographic bias
    const rankedSuggestions = uniqueSuggestions
      .map(suggestion => ({
        suggestion,
        score: calculateUnbiasedScore(suggestion, query)
      }))
      .sort((a, b) => b.score - a.score)
      .map(item => item.suggestion)

    console.log(`✅ Google returned ${rankedSuggestions.length} total suggestions after ranking`)
    return rankedSuggestions

  } catch (error) {
    console.error('Enhanced geocoding error:', error)
    return allSuggestions
  }
}

// Calculate natural relevance score without geographic bias
function calculateUnbiasedScore(suggestion: AddressOption, originalQuery: string): number {
  let score = 100 // Base score

  const label = suggestion.label.toLowerCase()
  const query = originalQuery.toLowerCase()
  const queryWords = query.split(/\s+/).filter(w => w.length > 0)

  // Boost exact query matches
  if (label.includes(query)) {
    score += 200 // Higher boost for exact matches
  }

  // Boost word-by-word matches
  let wordMatches = 0
  for (const word of queryWords) {
    if (label.includes(word)) {
      wordMatches++
      score += 50
    }
  }

  // Boost if most words match
  if (wordMatches >= queryWords.length * 0.8) {
    score += 100
  }

  // Boost results with complete address info
  if (suggestion.county && suggestion.state && suggestion.zip) {
    score += 30
  }

  // Boost results from Google (generally higher quality)
  if (suggestion.source?.includes('google')) {
    score += 40
  }

  // Boost street addresses (have numbers)
  if (label.match(/^\d+/)) {
    score += 20
  }

  return score
}

// Legacy function for backward compatibility - removed as unused



// Enhanced Nominatim API fallback function with better filtering - removed as unused
