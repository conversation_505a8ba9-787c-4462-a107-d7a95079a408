import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const address = searchParams.get('address')

    if (!address) {
      return NextResponse.json(
        { error: 'Address parameter is required' },
        { status: 400 }
      )
    }

    // Get coordinates from address using the same geocoding logic as other endpoints
    const coordinates = await getCoordinatesFromAddress(address)

    if (!coordinates) {
      return NextResponse.json(
        { error: 'Could not geocode address' },
        { status: 404 }
      )
    }

    return NextResponse.json({ coordinates })

  } catch (error) {
    console.error('Geocoding API error:', error)
    return NextResponse.json(
      { error: 'Failed to geocode address' },
      { status: 500 }
    )
  }
}

// Geocoding function using Geoapify (same as used in other endpoints)
async function getCoordinatesFromAddress(address: string) {
  try {
    // Use Geoapify for geocoding (same as address autocomplete)
    if (!process.env.GEOAPIFY_API_KEY) {
      throw new Error('Geoapify API key not configured')
    }

    const response = await fetch(
      `https://api.geoapify.com/v1/geocode/search?text=${encodeURIComponent(address)}&apiKey=${process.env.GEOAPIFY_API_KEY}`
    )

    if (!response.ok) {
      throw new Error(`Geoapify error: ${response.status}`)
    }

    const data = await response.json()

    if (data.features && data.features.length > 0) {
      const feature = data.features[0]
      return {
        lat: feature.geometry.coordinates[1],
        lng: feature.geometry.coordinates[0]
      }
    }

    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}
