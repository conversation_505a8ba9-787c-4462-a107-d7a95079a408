import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/analytics/detailed - Get detailed analytics
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '30d'

    // Calculate date range
    const days = range === '7d' ? 7 : range === '30d' ? 30 : 90
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'analytics_viewed',
      p_target_type: 'detailed_analytics',
      p_metadata: { range, start_date: startDate }
    })

    // Get detailed metrics
    const metrics = await getDetailedMetrics(supabase, startDate)
    const systemHealth = await getSystemHealth(supabase)

    return NextResponse.json({
      success: true,
      metrics,
      systemHealth
    })

  } catch (error) {
    console.error('Detailed analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch detailed analytics' },
      { status: 500 }
    )
  }
}

async function getDetailedMetrics(supabase: any, startDate: string) {
  try {
    // Total conversations in range
    const { count: totalConversations } = await supabase
      .from('chat_conversations')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate)

    // Total messages in range
    const { count: totalMessages } = await supabase
      .from('chat_messages')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate)

    // Active users in range
    const { data: activeUsersData } = await supabase
      .from('chat_conversations')
      .select('user_id')
      .gte('created_at', startDate)

    const activeUsers = new Set(activeUsersData?.map((c: any) => c.user_id) || []).size

    // Average messages per chat
    const avgMessagesPerChat = totalConversations > 0 ? totalMessages / totalConversations : 0

    // Simulated metrics (in real implementation, these would be calculated from actual data)
    const avgResponseTime = 2.3 // seconds
    
    // Fallback rate from unanswered_queries
    const { count: fallbackCount } = await supabase
      .from('unanswered_queries')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate)

    const fallbackRate = totalMessages > 0 ? (fallbackCount || 0) / totalMessages : 0

    // Top jurisdictions
    const { data: jurisdictionData } = await supabase
      .from('chat_conversations')
      .select('jurisdiction_name')
      .gte('created_at', startDate)

    const jurisdictionCounts = (jurisdictionData || []).reduce((acc: any, conv: any) => {
      const jurisdiction = conv.jurisdiction_name || 'Unknown'
      acc[jurisdiction] = (acc[jurisdiction] || 0) + 1
      return acc
    }, {})

    const topJurisdictions = Object.entries(jurisdictionCounts)
      .map(([jurisdiction, count]) => ({ jurisdiction, count: count as number }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // Daily stats (simplified - in real implementation would group by date)
    const dailyStats = generateMockDailyStats(startDate, totalConversations, totalMessages, activeUsers)

    return {
      totalConversations: totalConversations || 0,
      totalMessages: totalMessages || 0,
      activeUsers,
      avgMessagesPerChat,
      avgResponseTime,
      fallbackRate,
      topJurisdictions,
      dailyStats,
      weeklyStats: [], // Would be calculated from daily stats
      monthlyStats: [] // Would be calculated from daily stats
    }

  } catch (error) {
    console.error('Error fetching detailed metrics:', error)
    return {
      totalConversations: 0,
      totalMessages: 0,
      activeUsers: 0,
      avgMessagesPerChat: 0,
      avgResponseTime: 0,
      fallbackRate: 0,
      topJurisdictions: [],
      dailyStats: [],
      weeklyStats: [],
      monthlyStats: []
    }
  }
}

async function getSystemHealth(supabase: any) {
  try {
    // Knowledge base size
    const { count: knowledgeBaseSize } = await supabase
      .from('compliance_knowledge')
      .select('*', { count: 'exact', head: true })

    // Unique documents count
    const { data: documentsData } = await supabase
      .from('compliance_knowledge')
      .select('source_document_id')

    const documentsCount = new Set(documentsData?.map((d: any) => d.source_document_id) || []).size

    // Document freshness (using the admin_document_status view)
    const { data: documentStatus } = await supabase
      .from('admin_document_status')
      .select('*')

    let freshDocuments = 0
    let staleDocuments = 0
    let outdatedDocuments = 0

    if (documentStatus) {
      documentStatus.forEach((doc: any) => {
        const daysSinceUpdate = Math.floor(
          (Date.now() - new Date(doc.last_updated).getTime()) / (1000 * 60 * 60 * 24)
        )

        if (daysSinceUpdate <= 7) {
          freshDocuments++
        } else if (daysSinceUpdate <= 30) {
          staleDocuments++
        } else {
          outdatedDocuments++
        }
      })
    }

    // Simulated metrics
    const avgConfidenceScore = 0.85
    const errorRate = 0.02

    return {
      knowledgeBaseSize: knowledgeBaseSize || 0,
      documentsCount,
      freshDocuments,
      staleDocuments,
      outdatedDocuments,
      avgConfidenceScore,
      errorRate
    }

  } catch (error) {
    console.error('Error fetching system health:', error)
    return {
      knowledgeBaseSize: 0,
      documentsCount: 0,
      freshDocuments: 0,
      staleDocuments: 0,
      outdatedDocuments: 0,
      avgConfidenceScore: 0,
      errorRate: 0
    }
  }
}

function generateMockDailyStats(startDate: string, totalConversations: number, totalMessages: number, activeUsers: number) {
  const days = Math.floor((Date.now() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24))
  const dailyStats = []

  for (let i = days; i >= 0; i--) {
    const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
    
    // Distribute totals across days with some randomness
    const dayFactor = Math.random() * 0.5 + 0.75 // 0.75 to 1.25
    const conversations = Math.floor((totalConversations / days) * dayFactor)
    const messages = Math.floor((totalMessages / days) * dayFactor)
    const users = Math.floor((activeUsers / days) * dayFactor)

    dailyStats.push({
      date: date.toISOString().split('T')[0],
      conversations,
      messages,
      users
    })
  }

  return dailyStats
}
