import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/analytics - Get chat system analytics
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'analytics_viewed',
      p_target_type: 'chat_analytics',
      p_metadata: { timestamp: new Date().toISOString() }
    })

    // Get chat statistics
    const stats = await getChatAnalytics(supabase)
    const recentChats = await getRecentChats(supabase)

    return NextResponse.json({
      success: true,
      stats,
      recentChats
    })

  } catch (error) {
    console.error('Admin analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    )
  }
}

async function getChatAnalytics(supabase: any) {
  try {
    // Get total conversations
    const { count: totalConversations } = await supabase
      .from('chat_conversations')
      .select('*', { count: 'exact', head: true })

    // Get active users (users with conversations in last 30 days)
    const { count: activeUsers } = await supabase
      .from('chat_conversations')
      .select('user_id', { count: 'exact', head: true })
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())

    // Get messages this week
    const { count: messagesThisWeek } = await supabase
      .from('chat_messages')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

    // Get average messages per conversation
    const { data: avgData } = await supabase
      .rpc('get_avg_messages_per_conversation')

    // Get fallback rate from unanswered_queries
    const { count: totalQueries } = await supabase
      .from('chat_messages')
      .select('*', { count: 'exact', head: true })
      .eq('role', 'user')
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

    const { count: fallbackQueries } = await supabase
      .from('unanswered_queries')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

    // Get documents in knowledge base
    const { count: documentsInKB } = await supabase
      .from('compliance_knowledge')
      .select('source_document_id', { count: 'exact', head: true })

    const fallbackRate = totalQueries > 0 ? Math.round((fallbackQueries / totalQueries) * 100) : 0
    const avgMessagesPerChat = avgData?.[0]?.avg_messages || 0

    return {
      totalConversations: totalConversations || 0,
      activeUsers: activeUsers || 0,
      messagesThisWeek: messagesThisWeek || 0,
      avgMessagesPerChat: Math.round(avgMessagesPerChat * 10) / 10,
      fallbackRate,
      documentsInKB: documentsInKB || 0
    }

  } catch (error) {
    console.error('Error fetching chat analytics:', error)
    return {
      totalConversations: 0,
      activeUsers: 0,
      messagesThisWeek: 0,
      avgMessagesPerChat: 0,
      fallbackRate: 0,
      documentsInKB: 0
    }
  }
}

async function getRecentChats(supabase: any) {
  try {
    const { data: chats } = await supabase
      .from('chat_conversations')
      .select(`
        id,
        address,
        jurisdiction_name,
        updated_at,
        profiles!inner(email)
      `)
      .order('updated_at', { ascending: false })
      .limit(10)

    if (!chats) return []

    // Get message counts for each conversation
    const chatsWithCounts = await Promise.all(
      chats.map(async (chat: any) => {
        const { count: messageCount } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', chat.id)

        return {
          id: chat.id,
          userEmail: chat.profiles.email,
          address: chat.address,
          jurisdiction: chat.jurisdiction_name,
          messageCount: messageCount || 0,
          lastActivity: chat.updated_at,
          status: 'active' // Could be enhanced with actual status logic
        }
      })
    )

    return chatsWithCounts

  } catch (error) {
    console.error('Error fetching recent chats:', error)
    return []
  }
}
