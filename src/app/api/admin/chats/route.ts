import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/chats - Search chat sessions or get specific conversation
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const conversationId = searchParams.get('conversation')
    const searchType = searchParams.get('type') // 'email' or 'address'
    const searchQuery = searchParams.get('query')

    // If conversation ID is provided, get specific conversation
    if (conversationId) {
      const session = await getConversationById(supabase, conversationId)
      
      // Log admin action
      await supabase.rpc('log_admin_action', {
        p_action_type: 'chat_view',
        p_target_id: conversationId,
        p_target_type: 'conversation',
        p_metadata: { conversation_id: conversationId }
      })

      return NextResponse.json({
        success: true,
        session
      })
    }

    // If search parameters are provided, search for sessions
    if (searchType && searchQuery) {
      const sessions = await searchChatSessions(supabase, searchType, searchQuery)
      
      // Log admin action
      await supabase.rpc('log_admin_action', {
        p_action_type: 'chat_lookup',
        p_target_type: 'search',
        p_metadata: { 
          search_type: searchType, 
          search_query: searchQuery,
          results_count: sessions.length 
        }
      })

      return NextResponse.json({
        success: true,
        sessions
      })
    }

    return NextResponse.json({
      success: true,
      sessions: []
    })

  } catch (error) {
    console.error('Admin chat lookup error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch chat sessions' },
      { status: 500 }
    )
  }
}

async function getConversationById(supabase: any, conversationId: string) {
  try {
    const { data: conversation } = await supabase
      .from('chat_conversations')
      .select(`
        id,
        address,
        rule_type,
        jurisdiction_name,
        created_at,
        updated_at,
        profiles!inner(email)
      `)
      .eq('id', conversationId)
      .single()

    if (!conversation) {
      return null
    }

    // Get message count and last message time
    const { count: messageCount } = await supabase
      .from('chat_messages')
      .select('*', { count: 'exact', head: true })
      .eq('conversation_id', conversationId)

    const { data: lastMessage } = await supabase
      .from('chat_messages')
      .select('created_at')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    return {
      id: conversation.id,
      userEmail: conversation.profiles.email,
      address: conversation.address,
      jurisdiction: conversation.jurisdiction_name,
      ruleType: conversation.rule_type,
      messageCount: messageCount || 0,
      createdAt: conversation.created_at,
      updatedAt: conversation.updated_at,
      lastMessageAt: lastMessage?.created_at || conversation.created_at
    }

  } catch (error) {
    console.error('Error fetching conversation by ID:', error)
    return null
  }
}

async function searchChatSessions(supabase: any, searchType: string, searchQuery: string) {
  try {
    let query = supabase
      .from('chat_conversations')
      .select(`
        id,
        address,
        rule_type,
        jurisdiction_name,
        created_at,
        updated_at,
        profiles!inner(email)
      `)

    // Apply search filter based on type
    if (searchType === 'email') {
      query = query.ilike('profiles.email', `%${searchQuery}%`)
    } else if (searchType === 'address') {
      query = query.ilike('address', `%${searchQuery}%`)
    }

    const { data: conversations } = await query
      .order('updated_at', { ascending: false })
      .limit(50)

    if (!conversations) return []

    // Get message counts for each conversation
    const sessionsWithCounts = await Promise.all(
      conversations.map(async (conversation: any) => {
        const { count: messageCount } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', conversation.id)

        const { data: lastMessage } = await supabase
          .from('chat_messages')
          .select('created_at')
          .eq('conversation_id', conversation.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .single()

        return {
          id: conversation.id,
          userEmail: conversation.profiles.email,
          address: conversation.address,
          jurisdiction: conversation.jurisdiction_name,
          ruleType: conversation.rule_type,
          messageCount: messageCount || 0,
          createdAt: conversation.created_at,
          updatedAt: conversation.updated_at,
          lastMessageAt: lastMessage?.created_at || conversation.created_at
        }
      })
    )

    return sessionsWithCounts

  } catch (error) {
    console.error('Error searching chat sessions:', error)
    return []
  }
}
