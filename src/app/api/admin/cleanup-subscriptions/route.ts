import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe/config'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const { action, customerId, subscriptionId } = await request.json()

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's profile to verify they own this customer
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id, email')
      .eq('id', user.id)
      .single()

    if (!profile?.stripe_customer_id || profile.stripe_customer_id !== customerId) {
      return NextResponse.json({ error: 'Unauthorized - customer mismatch' }, { status: 403 })
    }

    if (action === 'list') {
      // List all subscriptions for the customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        limit: 20
      })

      const subscriptionDetails = subscriptions.data.map((sub) => ({
        id: sub.id,
        status: sub.status,
        created: new Date(sub.created * 1000).toISOString(),
        price_id: sub.items.data[0]?.price.id || null,
        amount: sub.items.data[0]?.price.unit_amount || 0,
      }))

      return NextResponse.json({
        customer_id: customerId,
        total_subscriptions: subscriptions.data.length,
        subscriptions: subscriptionDetails
      })
    }

    if (action === 'cancel' && subscriptionId) {
      // Cancel a specific subscription immediately
      const canceledSubscription = await stripe.subscriptions.cancel(subscriptionId)

      return NextResponse.json({
        success: true,
        message: `Subscription ${subscriptionId} has been canceled`,
        subscription: {
          id: canceledSubscription.id,
          status: canceledSubscription.status,
          canceled_at: canceledSubscription.canceled_at ? new Date(canceledSubscription.canceled_at * 1000).toISOString() : null
        }
      })
    }

    if (action === 'cancel_duplicates') {
      // Find all active subscriptions
      const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        status: 'active',
        limit: 20
      })

      if (subscriptions.data.length <= 1) {
        return NextResponse.json({
          message: 'No duplicate subscriptions found',
          total_active: subscriptions.data.length
        })
      }

      // Sort by creation date (keep the newest one)
      const sortedSubscriptions = subscriptions.data.sort((a, b) => b.created - a.created)
      const keepSubscription = sortedSubscriptions[0]
      const duplicatesToCancel = sortedSubscriptions.slice(1)

      const cancelResults = []
      for (const duplicate of duplicatesToCancel) {
        try {
          const canceled = await stripe.subscriptions.cancel(duplicate.id)
          cancelResults.push({
            id: canceled.id,
            status: 'canceled',
            was_created: new Date(duplicate.created * 1000).toISOString()
          })
        } catch (error) {
          cancelResults.push({
            id: duplicate.id,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return NextResponse.json({
        success: true,
        message: `Canceled ${cancelResults.length} duplicate subscriptions`,
        kept_subscription: {
          id: keepSubscription.id,
          created: new Date(keepSubscription.created * 1000).toISOString()
        },
        canceled_subscriptions: cancelResults
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('Error in subscription cleanup:', error)
    return NextResponse.json(
      { error: 'Failed to process subscription cleanup' },
      { status: 500 }
    )
  }
}
