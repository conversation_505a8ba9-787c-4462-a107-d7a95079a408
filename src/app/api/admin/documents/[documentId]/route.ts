import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// DELETE /api/admin/documents/[documentId] - Delete a document from knowledge base
export async function DELETE(
  request: NextRequest,
  { params }: { params: { documentId: string } }
) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { documentId } = params

    // Get document info before deletion for logging
    const { data: documentInfo } = await supabase
      .from('compliance_knowledge')
      .select('source_document_id, title, jurisdiction, document_type')
      .eq('source_document_id', documentId)
      .limit(1)
      .single()

    if (!documentInfo) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Delete all chunks for this document
    const { error: deleteError } = await supabase
      .from('compliance_knowledge')
      .delete()
      .eq('source_document_id', documentId)

    if (deleteError) {
      throw deleteError
    }

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'document_deleted',
      p_target_id: documentId,
      p_target_type: 'document',
      p_metadata: {
        document_id: documentId,
        title: documentInfo.title,
        jurisdiction: documentInfo.jurisdiction,
        document_type: documentInfo.document_type,
        deleted_at: new Date().toISOString()
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    })

  } catch (error) {
    console.error('Document deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    )
  }
}
