import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/documents - Get all documents in knowledge base
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'document_list_viewed',
      p_target_type: 'knowledge_base',
      p_metadata: { timestamp: new Date().toISOString() }
    })

    // Get documents and stats
    const documents = await getDocuments(supabase)
    const stats = await getDocumentStats(supabase)

    return NextResponse.json({
      success: true,
      documents,
      stats
    })

  } catch (error) {
    console.error('Admin documents error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    )
  }
}

async function getDocuments(supabase: any) {
  try {
    // Get unique documents with aggregated data
    const { data: documents } = await supabase
      .from('compliance_knowledge')
      .select(`
        source_document_id,
        document_type,
        jurisdiction,
        title,
        source_url,
        metadata,
        created_at
      `)
      .order('source_document_id')

    if (!documents) return []

    // Group by source_document_id and aggregate data
    const documentMap = new Map()

    for (const doc of documents) {
      const key = doc.source_document_id
      
      if (!documentMap.has(key)) {
        documentMap.set(key, {
          id: key,
          sourceDocumentId: doc.source_document_id,
          title: doc.title || `${doc.jurisdiction} ${doc.document_type}`,
          documentType: doc.document_type,
          jurisdiction: doc.jurisdiction,
          sourceUrl: doc.source_url,
          metadata: doc.metadata,
          createdAt: doc.created_at,
          totalChunks: 0,
          lastUpdated: doc.created_at
        })
      }

      const existing = documentMap.get(key)
      existing.totalChunks += 1
      
      // Update last updated if this chunk is newer
      if (new Date(doc.created_at) > new Date(existing.lastUpdated)) {
        existing.lastUpdated = doc.created_at
      }
    }

    // Convert to array and add status
    const documentsArray = Array.from(documentMap.values()).map(doc => ({
      ...doc,
      status: getDocumentStatus(doc.lastUpdated)
    }))

    return documentsArray

  } catch (error) {
    console.error('Error fetching documents:', error)
    return []
  }
}

async function getDocumentStats(supabase: any) {
  try {
    // Get total unique documents
    const { data: uniqueDocs } = await supabase
      .from('compliance_knowledge')
      .select('source_document_id')

    const uniqueDocuments = new Set(uniqueDocs?.map((d: any) => d.source_document_id) || [])
    const totalDocuments = uniqueDocuments.size

    // Get total chunks
    const { count: totalChunks } = await supabase
      .from('compliance_knowledge')
      .select('*', { count: 'exact', head: true })

    // Get unique jurisdictions
    const { data: jurisdictionData } = await supabase
      .from('compliance_knowledge')
      .select('jurisdiction')

    const uniqueJurisdictions = new Set(jurisdictionData?.map((d: any) => d.jurisdiction) || [])
    const jurisdictions = uniqueJurisdictions.size

    // Calculate status counts
    const documents = await getDocuments(supabase)
    const freshDocuments = documents.filter(d => d.status === 'fresh').length
    const staleDocuments = documents.filter(d => d.status === 'stale').length
    const outdatedDocuments = documents.filter(d => d.status === 'outdated').length

    return {
      totalDocuments,
      totalChunks: totalChunks || 0,
      jurisdictions,
      freshDocuments,
      staleDocuments,
      outdatedDocuments
    }

  } catch (error) {
    console.error('Error fetching document stats:', error)
    return {
      totalDocuments: 0,
      totalChunks: 0,
      jurisdictions: 0,
      freshDocuments: 0,
      staleDocuments: 0,
      outdatedDocuments: 0
    }
  }
}

function getDocumentStatus(lastUpdated: string): 'fresh' | 'stale' | 'outdated' {
  const daysSinceUpdate = Math.floor(
    (Date.now() - new Date(lastUpdated).getTime()) / (1000 * 60 * 60 * 24)
  )

  if (daysSinceUpdate <= 7) {
    return 'fresh'
  } else if (daysSinceUpdate <= 30) {
    return 'stale'
  } else {
    return 'outdated'
  }
}

// POST /api/admin/documents - Upload new document
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // For now, return a simple response indicating upload is not fully implemented
    // In a full implementation, this would handle file upload, text extraction, and embedding
    return NextResponse.json({
      success: false,
      error: 'Document upload functionality is not yet fully implemented. This is a placeholder for the complete upload pipeline that would include file storage, text extraction, chunking, and embedding generation.'
    }, { status: 501 })

  } catch (error) {
    console.error('Document upload error:', error)
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    )
  }
}
