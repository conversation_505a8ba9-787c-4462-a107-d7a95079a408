import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/documents/status - Get document status by jurisdiction
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get jurisdiction statistics from the admin_document_status view
    const { data: jurisdictionStats } = await supabase
      .from('admin_document_status')
      .select('*')
      .order('jurisdiction')

    if (!jurisdictionStats) {
      return NextResponse.json({
        success: true,
        jurisdictions: []
      })
    }

    // Process the data to determine status
    const jurisdictions = jurisdictionStats.map(stat => {
      const daysSinceUpdate = Math.floor(
        (Date.now() - new Date(stat.last_updated).getTime()) / (1000 * 60 * 60 * 24)
      )

      let status: 'fresh' | 'stale' | 'outdated'
      if (daysSinceUpdate <= 7) {
        status = 'fresh'
      } else if (daysSinceUpdate <= 30) {
        status = 'stale'
      } else {
        status = 'outdated'
      }

      return {
        jurisdiction: stat.jurisdiction,
        documentCount: stat.unique_documents,
        lastUpdated: stat.last_updated,
        status,
        totalChunks: stat.total_chunks,
        freshnessScore: Math.round(stat.freshness_score * 100)
      }
    })

    return NextResponse.json({
      success: true,
      jurisdictions
    })

  } catch (error) {
    console.error('Document status error:', error)
    return NextResponse.json(
      { error: 'Failed to get document status' },
      { status: 500 }
    )
  }
}
