import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/documents/upload/[jobId]/status - Get upload job status
export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { jobId } = params

    // Get the upload job status
    const { data: job } = await supabase
      .from('admin_actions')
      .select('*')
      .eq('action_type', 'document_upload_job')
      .eq('target_id', jobId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (!job) {
      return NextResponse.json({ error: 'Upload job not found' }, { status: 404 })
    }

    const progress = {
      stage: job.metadata.stage || 'uploading',
      progress: job.metadata.progress || 0,
      message: job.metadata.message || 'Processing...',
      error: job.metadata.error || null
    }

    return NextResponse.json({
      success: true,
      progress
    })

  } catch (error) {
    console.error('Upload status error:', error)
    return NextResponse.json(
      { error: 'Failed to get upload status' },
      { status: 500 }
    )
  }
}
