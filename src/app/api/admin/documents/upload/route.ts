import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// POST /api/admin/documents/upload - Upload and process new document
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const title = formData.get('title') as string
    const jurisdiction = formData.get('jurisdiction') as string
    const documentType = formData.get('documentType') as string
    const description = formData.get('description') as string
    const sourceUrl = formData.get('sourceUrl') as string

    // Validate required fields
    if (!file || !title || !jurisdiction || !documentType) {
      return NextResponse.json({ 
        error: 'Missing required fields: file, title, jurisdiction, documentType' 
      }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Unsupported file type. Please upload PDF, Word, or text files.' 
      }, { status: 400 })
    }

    // Validate file size (50MB limit)
    if (file.size > 50 * 1024 * 1024) {
      return NextResponse.json({ 
        error: 'File size exceeds 50MB limit' 
      }, { status: 400 })
    }

    // Create a job ID for tracking
    const jobId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'document_uploaded',
      p_target_id: jobId,
      p_target_type: 'upload_job',
      p_metadata: {
        job_id: jobId,
        title,
        jurisdiction,
        document_type: documentType,
        file_name: file.name,
        file_size: file.size,
        file_type: file.type
      }
    })

    // In a full implementation, this would:
    // 1. Store the file in Supabase Storage or S3
    // 2. Extract text from the file (PDF parsing, etc.)
    // 3. Chunk the text into appropriate segments
    // 4. Generate embeddings for each chunk
    // 5. Store chunks and embeddings in compliance_knowledge table
    // 6. Update job status throughout the process

    // For demonstration, we'll create a mock processing job
    await createMockUploadJob(supabase, jobId, {
      title,
      jurisdiction,
      documentType,
      description,
      sourceUrl,
      fileName: file.name,
      fileSize: file.size,
      adminUserId: user.id
    })

    return NextResponse.json({
      success: true,
      jobId,
      message: 'Upload started successfully'
    })

  } catch (error) {
    console.error('Document upload error:', error)
    return NextResponse.json(
      { error: 'Failed to start document upload' },
      { status: 500 }
    )
  }
}

async function createMockUploadJob(supabase: any, jobId: string, metadata: any) {
  // Store initial job status
  await supabase
    .from('admin_actions')
    .insert({
      admin_user_id: metadata.adminUserId,
      action_type: 'document_upload_job',
      target_id: jobId,
      target_type: 'upload_job',
      metadata: {
        job_id: jobId,
        stage: 'uploading',
        progress: 0,
        message: 'Starting upload...',
        ...metadata,
        created_at: new Date().toISOString()
      }
    })

  // Simulate processing stages
  setTimeout(() => simulateUploadProgress(supabase, jobId, metadata), 1000)
}

async function simulateUploadProgress(supabase: any, jobId: string, metadata: any) {
  const stages = [
    { stage: 'uploading', progress: 25, message: 'File uploaded successfully' },
    { stage: 'processing', progress: 50, message: 'Extracting text from document...' },
    { stage: 'embedding', progress: 75, message: 'Generating embeddings...' },
    { stage: 'complete', progress: 100, message: 'Document processed and added to knowledge base' }
  ]

  for (let i = 0; i < stages.length; i++) {
    const stage = stages[i]
    
    await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds between stages
    
    await supabase
      .from('admin_actions')
      .update({
        metadata: supabase.raw(`metadata || '${JSON.stringify({
          stage: stage.stage,
          progress: stage.progress,
          message: stage.message,
          updated_at: new Date().toISOString()
        })}'::jsonb`)
      })
      .eq('target_id', jobId)
      .eq('action_type', 'document_upload_job')

    // On completion, create a mock document entry
    if (stage.stage === 'complete') {
      await createMockDocument(supabase, metadata)
    }
  }
}

async function createMockDocument(supabase: any, metadata: any) {
  // Create a mock document entry in compliance_knowledge
  // In a real implementation, this would be multiple chunks with actual embeddings
  
  const sourceDocumentId = `doc_${Date.now()}`
  const mockEmbedding = new Array(1536).fill(0).map(() => Math.random() - 0.5) // Mock OpenAI embedding

  try {
    await supabase
      .from('compliance_knowledge')
      .insert({
        source_document_id: sourceDocumentId,
        document_type: metadata.documentType,
        jurisdiction: metadata.jurisdiction,
        title: metadata.title,
        content_chunk: `This is a mock document chunk for ${metadata.title}. In a real implementation, this would contain the actual extracted and chunked text from the uploaded document.`,
        chunk_sequence: 1,
        embedding: `[${mockEmbedding.join(',')}]`,
        metadata: {
          description: metadata.description,
          file_name: metadata.fileName,
          file_size: metadata.fileSize,
          upload_job_id: metadata.job_id,
          processed_at: new Date().toISOString()
        },
        source_url: metadata.sourceUrl || null,
        last_updated_at: new Date().toISOString()
      })

    console.log(`Mock document created: ${sourceDocumentId}`)
  } catch (error) {
    console.error('Failed to create mock document:', error)
  }
}
