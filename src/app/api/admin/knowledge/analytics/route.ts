import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/knowledge/analytics - Get performance analytics
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '24h'
    const operationType = searchParams.get('operationType')

    // Validate time range
    const validTimeRanges = ['1h', '24h', '7d', '30d']
    if (!validTimeRanges.includes(timeRange)) {
      return NextResponse.json(
        { error: 'Invalid time range. Must be one of: 1h, 24h, 7d, 30d' },
        { status: 400 }
      )
    }

    const timeRangeMap = {
      '1h': '1 hour',
      '24h': '24 hours',
      '7d': '7 days',
      '30d': '30 days'
    } as const

    // Build metrics query
    let metricsQuery = supabase
      .from('knowledge_performance_metrics')
      .select('*')
      .gte('started_at', `now() - interval '${timeRangeMap[timeRange as keyof typeof timeRangeMap]}'`)
      .order('started_at', { ascending: false })

    if (operationType) {
      metricsQuery = metricsQuery.eq('operation_type', operationType)
    }

    const { data: metrics, error: metricsError } = await metricsQuery

    if (metricsError) {
      console.error('Error fetching performance metrics:', metricsError)
      return NextResponse.json(
        { error: 'Failed to fetch performance metrics' },
        { status: 500 }
      )
    }

    // Build alerts query
    let alertsQuery = supabase
      .from('knowledge_performance_alerts')
      .select('*')
      .gte('triggered_at', `now() - interval '${timeRangeMap[timeRange as keyof typeof timeRangeMap]}'`)
      .order('triggered_at', { ascending: false })

    if (operationType) {
      alertsQuery = alertsQuery.eq('operation_type', operationType)
    }

    const { data: alerts, error: alertsError } = await alertsQuery

    if (alertsError) {
      console.error('Error fetching performance alerts:', alertsError)
      return NextResponse.json(
        { error: 'Failed to fetch performance alerts' },
        { status: 500 }
      )
    }

    // Calculate summary statistics
    const summary = calculateSummaryStats(metrics || [])

    // Calculate trends (compare with previous period)
    const previousPeriodQuery = supabase
      .from('knowledge_performance_metrics')
      .select('duration_ms, items_processed, success_rate')
      .gte('started_at', `now() - interval '${timeRangeMap[timeRange as keyof typeof timeRangeMap]}' * 2`)
      .lt('started_at', `now() - interval '${timeRangeMap[timeRange as keyof typeof timeRangeMap]}'`)

    if (operationType) {
      previousPeriodQuery.eq('operation_type', operationType)
    }

    const { data: previousMetrics } = await previousPeriodQuery
    const previousSummary = calculateSummaryStats(previousMetrics || [])
    const trends = calculateTrends(summary, previousSummary)

    // Group metrics by operation type for charts
    const metricsByType = (metrics || []).reduce((acc, metric) => {
      if (!acc[metric.operation_type]) {
        acc[metric.operation_type] = []
      }
      acc[metric.operation_type].push(metric)
      return acc
    }, {} as Record<string, any[]>)

    // Group alerts by severity
    const alertsBySeverity = (alerts || []).reduce((acc, alert) => {
      if (!acc[alert.severity]) {
        acc[alert.severity] = 0
      }
      acc[alert.severity]++
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      timeRange,
      summary,
      trends,
      metrics: metrics || [],
      metricsByType,
      alerts: alerts || [],
      alertsBySeverity,
      unresolvedAlerts: (alerts || []).filter(alert => !alert.resolved_at).length
    })

  } catch (error) {
    console.error('Performance analytics API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/knowledge/analytics/alerts/{alertId}/acknowledge - Acknowledge alert
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { alertId, action } = await request.json()

    if (!alertId) {
      return NextResponse.json(
        { error: 'Alert ID is required' },
        { status: 400 }
      )
    }

    if (!action || !['acknowledge', 'resolve'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "acknowledge" or "resolve"' },
        { status: 400 }
      )
    }

    const updateData: any = {}
    
    if (action === 'acknowledge') {
      updateData.acknowledged_by = user.id
      updateData.acknowledged_at = new Date().toISOString()
    } else if (action === 'resolve') {
      updateData.resolved_at = new Date().toISOString()
      if (!updateData.acknowledged_by) {
        updateData.acknowledged_by = user.id
        updateData.acknowledged_at = new Date().toISOString()
      }
    }

    const { data: updatedAlert, error: updateError } = await supabase
      .from('knowledge_performance_alerts')
      .update(updateData)
      .eq('id', alertId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating alert:', updateError)
      return NextResponse.json(
        { error: 'Failed to update alert' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: `Alert ${action}d successfully`,
      alert: updatedAlert
    })

  } catch (error) {
    console.error('Alert acknowledgment API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function calculateSummaryStats(metrics: any[]) {
  if (metrics.length === 0) {
    return {
      total_operations: 0,
      avg_duration_ms: 0,
      avg_items_per_second: 0,
      avg_success_rate: 0,
      total_items_processed: 0,
      total_errors: 0,
      fastest_operation_ms: 0,
      slowest_operation_ms: 0
    }
  }

  const totalDuration = metrics.reduce((sum, m) => sum + m.duration_ms, 0)
  const totalItems = metrics.reduce((sum, m) => sum + m.items_processed, 0)
  const totalErrors = metrics.reduce((sum, m) => sum + m.error_count, 0)
  const avgSuccessRate = metrics.reduce((sum, m) => sum + m.success_rate, 0) / metrics.length
  const durations = metrics.map(m => m.duration_ms)

  return {
    total_operations: metrics.length,
    avg_duration_ms: Math.round(totalDuration / metrics.length),
    avg_items_per_second: totalItems > 0 ? Number((totalItems / (totalDuration / 1000)).toFixed(2)) : 0,
    avg_success_rate: Number((avgSuccessRate * 100).toFixed(1)),
    total_items_processed: totalItems,
    total_errors: totalErrors,
    fastest_operation_ms: Math.min(...durations),
    slowest_operation_ms: Math.max(...durations)
  }
}

function calculateTrends(current: any, previous: any) {
  if (previous.total_operations === 0) {
    return {
      duration_trend: 0,
      throughput_trend: 0,
      success_rate_trend: 0,
      operations_trend: 0
    }
  }

  const durationChange = ((current.avg_duration_ms - previous.avg_duration_ms) / previous.avg_duration_ms) * 100
  const throughputChange = ((current.avg_items_per_second - previous.avg_items_per_second) / previous.avg_items_per_second) * 100
  const successRateChange = current.avg_success_rate - previous.avg_success_rate
  const operationsChange = ((current.total_operations - previous.total_operations) / previous.total_operations) * 100

  return {
    duration_trend: Number(durationChange.toFixed(1)),
    throughput_trend: Number(throughputChange.toFixed(1)),
    success_rate_trend: Number(successRateChange.toFixed(1)),
    operations_trend: Number(operationsChange.toFixed(1))
  }
}
