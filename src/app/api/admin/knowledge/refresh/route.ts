import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { 
  RefreshJobCreateRequest, 
  RefreshJobCreateResponse,
  KnowledgeRefreshError,
  REFRESH_JOB_PRIORITIES 
} from '@/lib/types/knowledge'

// POST /api/admin/knowledge/refresh - Trigger manual knowledge refresh
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse request body
    const body: RefreshJobCreateRequest = await request.json()
    
    // Validate request
    if (!body.job_type || !['manual', 'emergency'].includes(body.job_type)) {
      return NextResponse.json(
        { error: 'Invalid job_type. Must be "manual" or "emergency"' },
        { status: 400 }
      )
    }

    if (!body.scope || Object.keys(body.scope).length === 0) {
      return NextResponse.json(
        { error: 'Scope is required. Specify jurisdiction, document_type, or specific_ids' },
        { status: 400 }
      )
    }

    // Check for existing running jobs to prevent conflicts
    const { data: runningJobs, error: jobCheckError } = await supabase
      .from('knowledge_refresh_jobs')
      .select('id, status, scope')
      .in('status', ['pending', 'running'])
      .order('created_at', { ascending: false })

    if (jobCheckError) {
      console.error('Error checking running jobs:', jobCheckError)
      return NextResponse.json(
        { error: 'Failed to check existing jobs' },
        { status: 500 }
      )
    }

    // Check for scope conflicts with running jobs
    const hasConflict = runningJobs?.some(job => {
      const jobScope = job.scope as any
      const requestScope = body.scope
      
      // Check for jurisdiction overlap
      if (jobScope.jurisdiction && requestScope.jurisdiction) {
        const overlap = jobScope.jurisdiction.some((j: string) => 
          requestScope.jurisdiction?.includes(j)
        )
        if (overlap) return true
      }
      
      // Check for document_type overlap
      if (jobScope.document_type && requestScope.document_type) {
        const overlap = jobScope.document_type.some((dt: string) => 
          requestScope.document_type?.includes(dt)
        )
        if (overlap) return true
      }
      
      return false
    })

    if (hasConflict && body.job_type !== 'emergency') {
      return NextResponse.json(
        { 
          error: 'Conflicting refresh job already running. Use emergency priority to override.',
          running_jobs: runningJobs?.map(job => ({
            id: job.id,
            status: job.status,
            scope: job.scope
          }))
        },
        { status: 409 }
      )
    }

    // Determine priority
    const priority = body.job_type === 'emergency' 
      ? REFRESH_JOB_PRIORITIES.EMERGENCY 
      : REFRESH_JOB_PRIORITIES.NORMAL

    // Estimate total items to process
    let totalItems = 0
    try {
      const { count } = await supabase
        .from('compliance_knowledge')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)
        .in('jurisdiction', body.scope.jurisdiction || [])
        .in('document_type', body.scope.document_type || [])

      totalItems = count || 0
    } catch (error) {
      console.warn('Could not estimate total items:', error)
      totalItems = 1000 // Default estimate
    }

    // Create refresh job record
    const { data: job, error: createError } = await supabase
      .from('knowledge_refresh_jobs')
      .insert({
        triggered_by: user.id,
        job_type: body.job_type,
        scope: body.scope,
        status: 'pending',
        total_items: totalItems,
        progress: {
          current_step: 'initializing',
          percentage: 0,
          steps_completed: []
        },
        result_summary: {
          items_processed: 0,
          items_updated: 0,
          items_added: 0,
          items_removed: 0,
          errors: [],
          duration_ms: 0
        }
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating refresh job:', createError)
      return NextResponse.json(
        { error: 'Failed to create refresh job' },
        { status: 500 }
      )
    }

    // Trigger the actual refresh process via Edge Function
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

      if (supabaseUrl && supabaseServiceKey) {
        // Trigger the auto-refresh Edge Function asynchronously
        fetch(`${supabaseUrl}/functions/v1/knowledge-auto-refresh`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            job_id: job.id,
            scope: body.scope
          })
        }).catch(error => {
          console.error('Failed to trigger auto-refresh Edge Function:', error)
          // Update job status to failed
          supabase
            .from('knowledge_refresh_jobs')
            .update({
              status: 'failed',
              error_message: `Failed to trigger refresh process: ${error.message}`,
              completed_at: new Date().toISOString()
            })
            .eq('id', job.id)
            .then(() => console.log('Job marked as failed due to Edge Function trigger failure'))
        })
      } else {
        console.warn('Supabase URL or Service Key not configured, refresh will remain pending')
      }
    } catch (error) {
      console.error('Error triggering refresh process:', error)
    }
    
    const response: RefreshJobCreateResponse = {
      job_id: job.id,
      status: 'pending',
      message: `Knowledge refresh job created successfully. Job ID: ${job.id}`
    }

    return NextResponse.json(response, { status: 201 })

  } catch (error) {
    console.error('Knowledge refresh API error:', error)
    
    if (error instanceof KnowledgeRefreshError) {
      return NextResponse.json(
        { error: error.message, code: error.code, details: error.details },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/admin/knowledge/refresh - List recent refresh jobs
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
    const offset = parseInt(searchParams.get('offset') || '0')
    const status = searchParams.get('status')

    // Build query
    let query = supabase
      .from('knowledge_refresh_jobs')
      .select(`
        id,
        triggered_by,
        job_type,
        scope,
        status,
        progress,
        total_items,
        processed_items,
        started_at,
        completed_at,
        error_message,
        created_at,
        profiles!knowledge_refresh_jobs_triggered_by_fkey(email)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status) {
      query = query.eq('status', status)
    }

    const { data: jobs, error: fetchError } = await query

    if (fetchError) {
      console.error('Error fetching refresh jobs:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch refresh jobs' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      jobs: jobs || [],
      pagination: {
        limit,
        offset,
        has_more: (jobs?.length || 0) === limit
      }
    })

  } catch (error) {
    console.error('Knowledge refresh list API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
