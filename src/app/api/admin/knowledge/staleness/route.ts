import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { StalenessReportResponse } from '@/lib/types/knowledge'

// GET /api/admin/knowledge/staleness - Get staleness report
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const jurisdiction = searchParams.get('jurisdiction')
    const document_type = searchParams.get('document_type')
    const min_staleness_score = parseFloat(searchParams.get('min_staleness_score') || '0')

    // Build query for staleness tracking
    let query = supabase
      .from('document_staleness_tracking')
      .select('*')
      .order('staleness_score', { ascending: false })

    // Apply filters
    if (jurisdiction) {
      query = query.eq('jurisdiction', jurisdiction)
    }
    if (document_type) {
      query = query.eq('document_type', document_type)
    }
    if (min_staleness_score > 0) {
      query = query.gte('staleness_score', min_staleness_score)
    }

    const { data: tracking, error: fetchError } = await query

    if (fetchError) {
      console.error('Error fetching staleness tracking:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch staleness data' },
        { status: 500 }
      )
    }

    // Calculate summary statistics
    const totalDocuments = tracking?.length || 0
    const staleDocuments = tracking?.filter(t => t.staleness_score > 0.3).length || 0
    const averageStaleness = totalDocuments > 0 
      ? tracking!.reduce((sum, t) => sum + t.staleness_score, 0) / totalDocuments 
      : 0
    const updatesAvailable = tracking?.filter(t => t.update_available).length || 0

    const response: StalenessReportResponse = {
      tracking: tracking || [],
      summary: {
        total_documents: totalDocuments,
        stale_documents: staleDocuments,
        average_staleness: Math.round(averageStaleness * 1000) / 1000,
        updates_available: updatesAvailable
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Staleness report API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/knowledge/staleness - Trigger staleness check
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse request body for optional filters
    const body = await request.json().catch(() => ({}))
    const { jurisdiction, document_type } = body

    // Call the staleness update function
    const { error: updateError } = await supabase.rpc('update_staleness_tracking')

    if (updateError) {
      console.error('Error updating staleness tracking:', updateError)
      return NextResponse.json(
        { error: 'Failed to update staleness tracking' },
        { status: 500 }
      )
    }

    // If specific filters provided, update only those
    if (jurisdiction || document_type) {
      let updateQuery = `
        INSERT INTO document_staleness_tracking (
          jurisdiction,
          document_type,
          current_version,
          staleness_score,
          last_checked_at
        )
        SELECT DISTINCT
          jurisdiction,
          document_type,
          document_version,
          calculate_staleness_score(publish_date, adoption_date, document_type),
          now()
        FROM compliance_knowledge
        WHERE is_active = true
      `

      const conditions = []
      if (jurisdiction) conditions.push(`jurisdiction = '${jurisdiction}'`)
      if (document_type) conditions.push(`document_type = '${document_type}'`)
      
      if (conditions.length > 0) {
        updateQuery += ` AND ${conditions.join(' AND ')}`
      }

      updateQuery += `
        ON CONFLICT (jurisdiction, document_type) 
        DO UPDATE SET
          current_version = EXCLUDED.current_version,
          staleness_score = EXCLUDED.staleness_score,
          last_checked_at = EXCLUDED.last_checked_at,
          updated_at = now()
      `

      const { error: specificUpdateError } = await supabase.rpc('exec_sql', { 
        sql: updateQuery 
      })

      if (specificUpdateError) {
        console.warn('Specific staleness update failed, but general update succeeded:', specificUpdateError)
      }
    }

    // Get updated counts
    const { data: summary } = await supabase
      .from('document_staleness_tracking')
      .select('staleness_score, update_available')

    const totalDocuments = summary?.length || 0
    const staleDocuments = summary?.filter(s => s.staleness_score > 0.3).length || 0
    const updatesAvailable = summary?.filter(s => s.update_available).length || 0

    return NextResponse.json({
      message: 'Staleness tracking updated successfully',
      summary: {
        total_documents: totalDocuments,
        stale_documents: staleDocuments,
        updates_available: updatesAvailable,
        updated_at: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Staleness update API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/knowledge/staleness - Update staleness thresholds
export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse request body
    const { jurisdiction, document_type, thresholds, update_available } = await request.json()

    if (!jurisdiction || !document_type) {
      return NextResponse.json(
        { error: 'jurisdiction and document_type are required' },
        { status: 400 }
      )
    }

    // Build update data
    const updateData: any = {}
    
    if (thresholds) {
      updateData.metadata = { thresholds }
    }
    
    if (typeof update_available === 'boolean') {
      updateData.update_available = update_available
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid update fields provided' },
        { status: 400 }
      )
    }

    // Update the staleness tracking record
    const { data: updated, error: updateError } = await supabase
      .from('document_staleness_tracking')
      .update(updateData)
      .eq('jurisdiction', jurisdiction)
      .eq('document_type', document_type)
      .select()

    if (updateError) {
      console.error('Error updating staleness record:', updateError)
      return NextResponse.json(
        { error: 'Failed to update staleness record' },
        { status: 500 }
      )
    }

    if (!updated || updated.length === 0) {
      return NextResponse.json(
        { error: 'Staleness record not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Staleness record updated successfully',
      updated_record: updated[0]
    })

  } catch (error) {
    console.error('Staleness update API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
