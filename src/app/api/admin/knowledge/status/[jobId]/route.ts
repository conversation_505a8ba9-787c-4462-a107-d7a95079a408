import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { RefreshJobStatusResponse } from '@/lib/types/knowledge'

// GET /api/admin/knowledge/status/[jobId] - Get refresh job status
export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role or owns the job
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.role === 'admin'

    // Fetch the job
    let query = supabase
      .from('knowledge_refresh_jobs')
      .select(`
        id,
        triggered_by,
        job_type,
        scope,
        status,
        progress,
        total_items,
        processed_items,
        started_at,
        completed_at,
        error_message,
        result_summary,
        created_at,
        updated_at,
        profiles!knowledge_refresh_jobs_triggered_by_fkey(email)
      `)
      .eq('id', params.jobId)

    // Non-admin users can only see their own jobs
    if (!isAdmin) {
      query = query.eq('triggered_by', user.id)
    }

    const { data: job, error: fetchError } = await query.single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 })
      }
      console.error('Error fetching refresh job:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch job status' },
        { status: 500 }
      )
    }

    // Determine if job can be cancelled
    const canCancel = ['pending', 'running'].includes(job.status) && 
                     (isAdmin || job.triggered_by === user.id)

    const response: RefreshJobStatusResponse = {
      job,
      can_cancel: canCancel
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Job status API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/knowledge/status/[jobId] - Update job status (cancel, etc.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { action } = await request.json()

    if (!action || !['cancel'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Supported actions: cancel' },
        { status: 400 }
      )
    }

    // Check if user has admin role or owns the job
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.role === 'admin'

    // Fetch the job to verify ownership and status
    let jobQuery = supabase
      .from('knowledge_refresh_jobs')
      .select('id, triggered_by, status')
      .eq('id', params.jobId)

    if (!isAdmin) {
      jobQuery = jobQuery.eq('triggered_by', user.id)
    }

    const { data: job, error: fetchError } = await jobQuery.single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 })
      }
      console.error('Error fetching job for update:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch job' },
        { status: 500 }
      )
    }

    // Check if job can be cancelled
    if (!['pending', 'running'].includes(job.status)) {
      return NextResponse.json(
        { error: `Cannot cancel job with status: ${job.status}` },
        { status: 400 }
      )
    }

    // Handle cancel action
    if (action === 'cancel') {
      const { error: updateError } = await supabase
        .from('knowledge_refresh_jobs')
        .update({
          status: 'cancelled',
          completed_at: new Date().toISOString(),
          error_message: `Cancelled by user ${user.id}`
        })
        .eq('id', params.jobId)

      if (updateError) {
        console.error('Error cancelling job:', updateError)
        return NextResponse.json(
          { error: 'Failed to cancel job' },
          { status: 500 }
        )
      }

      // TODO: Signal the actual refresh process to stop (will be implemented in Phase 3)
      
      return NextResponse.json({
        message: 'Job cancelled successfully',
        job_id: params.jobId,
        status: 'cancelled'
      })
    }

  } catch (error) {
    console.error('Job update API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/knowledge/status/[jobId] - Delete job record (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Check if job exists and is not running
    const { data: job, error: fetchError } = await supabase
      .from('knowledge_refresh_jobs')
      .select('id, status')
      .eq('id', params.jobId)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 })
      }
      console.error('Error fetching job for deletion:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch job' },
        { status: 500 }
      )
    }

    // Prevent deletion of running jobs
    if (job.status === 'running') {
      return NextResponse.json(
        { error: 'Cannot delete running job. Cancel it first.' },
        { status: 400 }
      )
    }

    // Delete the job
    const { error: deleteError } = await supabase
      .from('knowledge_refresh_jobs')
      .delete()
      .eq('id', params.jobId)

    if (deleteError) {
      console.error('Error deleting job:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete job' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Job deleted successfully',
      job_id: params.jobId
    })

  } catch (error) {
    console.error('Job deletion API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
