import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { DocumentVersionsResponse } from '@/lib/types/knowledge'

// GET /api/admin/knowledge/versions - List document versions
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const jurisdiction = searchParams.get('jurisdiction')
    const document_type = searchParams.get('document_type')
    const is_active = searchParams.get('is_active')
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 200)
    const offset = (page - 1) * limit

    // Build query
    let query = supabase
      .from('compliance_knowledge')
      .select(`
        id,
        source_document_id,
        document_type,
        jurisdiction,
        project_type_tags,
        title,
        chunk_sequence,
        metadata,
        source_url,
        last_updated_at,
        created_at,
        document_version,
        publish_date,
        adoption_date,
        is_active,
        superseded_by
      `, { count: 'exact' })
      .order('jurisdiction')
      .order('document_type')
      .order('document_version')
      .order('chunk_sequence')
      .range(offset, offset + limit - 1)

    // Apply filters
    if (jurisdiction) {
      query = query.eq('jurisdiction', jurisdiction)
    }
    if (document_type) {
      query = query.eq('document_type', document_type)
    }
    if (is_active !== null) {
      query = query.eq('is_active', is_active === 'true')
    }

    const { data: versions, error: fetchError, count } = await query

    if (fetchError) {
      console.error('Error fetching document versions:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch document versions' },
        { status: 500 }
      )
    }

    const response: DocumentVersionsResponse = {
      versions: (versions || []) as any,
      pagination: {
        page,
        limit,
        total: count || 0,
        has_more: (count || 0) > offset + limit
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Document versions API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/knowledge/versions - Update document version status
export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Parse request body
    const { 
      document_ids, 
      action, 
      new_version, 
      publish_date, 
      adoption_date 
    } = await request.json()

    if (!document_ids || !Array.isArray(document_ids) || document_ids.length === 0) {
      return NextResponse.json(
        { error: 'document_ids array is required' },
        { status: 400 }
      )
    }

    if (!action || !['activate', 'deactivate', 'update_version', 'supersede'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Supported: activate, deactivate, update_version, supersede' },
        { status: 400 }
      )
    }

    // Validate document IDs exist
    const { data: existingDocs, error: validateError } = await supabase
      .from('compliance_knowledge')
      .select('id, jurisdiction, document_type, document_version, is_active')
      .in('id', document_ids)

    if (validateError) {
      console.error('Error validating document IDs:', validateError)
      return NextResponse.json(
        { error: 'Failed to validate document IDs' },
        { status: 500 }
      )
    }

    if (!existingDocs || existingDocs.length !== document_ids.length) {
      return NextResponse.json(
        { error: 'Some document IDs not found' },
        { status: 404 }
      )
    }

    let updateData: any = {}
    let results: any[] = []

    switch (action) {
      case 'activate':
        updateData = { is_active: true }
        break

      case 'deactivate':
        updateData = { is_active: false }
        break

      case 'update_version':
        if (!new_version) {
          return NextResponse.json(
            { error: 'new_version is required for update_version action' },
            { status: 400 }
          )
        }
        updateData = {
          document_version: new_version,
          ...(publish_date && { publish_date }),
          ...(adoption_date && { adoption_date })
        }
        break

      case 'supersede':
        // For supersede action, we need to handle it differently
        // This would typically involve creating new versions and linking them
        return NextResponse.json(
          { error: 'Supersede action not yet implemented' },
          { status: 501 }
        )

      default:
        return NextResponse.json(
          { error: 'Unknown action' },
          { status: 400 }
        )
    }

    // Perform the update
    const { data: updatedDocs, error: updateError } = await supabase
      .from('compliance_knowledge')
      .update(updateData)
      .in('id', document_ids)
      .select('id, jurisdiction, document_type, document_version, is_active')

    if (updateError) {
      console.error('Error updating documents:', updateError)
      return NextResponse.json(
        { error: 'Failed to update documents' },
        { status: 500 }
      )
    }

    // Log the changes for audit trail
    const changes = updatedDocs?.map(doc => ({
      document_id: doc.id,
      jurisdiction: doc.jurisdiction,
      document_type: doc.document_type,
      action,
      old_values: existingDocs.find(d => d.id === doc.id),
      new_values: doc,
      updated_by: user.id,
      updated_at: new Date().toISOString()
    }))

    return NextResponse.json({
      message: `Successfully ${action}d ${updatedDocs?.length || 0} documents`,
      updated_documents: updatedDocs,
      changes
    })

  } catch (error) {
    console.error('Document version update API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
