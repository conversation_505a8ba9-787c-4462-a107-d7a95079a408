import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import type { PromptTemplate } from '@/lib/types/chat'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Fetch all prompt templates
    const { data: templates, error: templatesError } = await supabase
      .from('prompt_templates')
      .select('*')
      .order('category', { ascending: true })
      .order('name', { ascending: true })

    if (templatesError) {
      console.error('Error fetching prompt templates:', templatesError)
      return NextResponse.json(
        { error: 'Failed to fetch prompt templates' },
        { status: 500 }
      )
    }

    // Transform to match admin interface expectations
    const transformedTemplates = templates?.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description || '',
      templateText: template.template_text,
      category: template.category,
      isActive: template.is_active,
      createdAt: template.created_at,
      updatedAt: template.updated_at
    })) || []

    return NextResponse.json({
      success: true,
      templates: transformedTemplates
    })

  } catch (error) {
    console.error('Admin prompt templates API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, description, templateText, category, isActive } = await request.json()

    if (!name || !templateText || !category) {
      return NextResponse.json(
        { error: 'Name, template text, and category are required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Create new prompt template
    const { data: template, error: createError } = await supabase
      .from('prompt_templates')
      .insert({
        name,
        description: description || null,
        template_text: templateText,
        category,
        is_active: isActive !== false, // Default to true
        created_by: user.id
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating prompt template:', createError)
      return NextResponse.json(
        { error: 'Failed to create prompt template' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      template: {
        id: template.id,
        name: template.name,
        description: template.description || '',
        templateText: template.template_text,
        category: template.category,
        isActive: template.is_active,
        createdAt: template.created_at,
        updatedAt: template.updated_at
      }
    })

  } catch (error) {
    console.error('Admin create prompt template error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
