import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// POST /api/admin/reindex - Trigger knowledge base re-indexing
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { type, target } = await request.json()

    if (!type || !['full', 'jurisdiction'].includes(type)) {
      return NextResponse.json({ error: 'Invalid reindex type' }, { status: 400 })
    }

    if (type === 'jurisdiction' && !target) {
      return NextResponse.json({ error: 'Jurisdiction target required' }, { status: 400 })
    }

    // Check if there's already an active job
    const activeJob = await getActiveReindexJob(supabase)
    if (activeJob) {
      return NextResponse.json({ 
        error: 'A re-index operation is already in progress' 
      }, { status: 409 })
    }

    // Create new re-index job
    const job = await createReindexJob(supabase, type, target, user.id)

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'reindex_triggered',
      p_target_id: job.id,
      p_target_type: 'reindex_job',
      p_metadata: { 
        reindex_type: type,
        target: target || 'all',
        job_id: job.id
      }
    })

    // Start the re-indexing process (this would typically be a background job)
    // For now, we'll simulate it with a simple process
    processReindexJob(supabase, job.id, type, target)

    return NextResponse.json({
      success: true,
      job
    })

  } catch (error) {
    console.error('Admin reindex error:', error)
    return NextResponse.json(
      { error: 'Failed to start re-index' },
      { status: 500 }
    )
  }
}

async function getActiveReindexJob(supabase: any) {
  try {
    const { data: job } = await supabase
      .from('admin_actions')
      .select('*')
      .eq('action_type', 'reindex_triggered')
      .eq('metadata->>status', 'running')
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    return job
  } catch (error) {
    return null
  }
}

async function createReindexJob(supabase: any, type: string, target: string | undefined, adminUserId: string) {
  const jobId = `reindex_${Date.now()}`
  
  const job = {
    id: jobId,
    type,
    target,
    status: 'pending',
    progress: 0,
    startedAt: new Date().toISOString(),
    documentsProcessed: 0,
    totalDocuments: 0
  }

  // Store job in admin_actions table
  await supabase
    .from('admin_actions')
    .insert({
      admin_user_id: adminUserId,
      action_type: 'reindex_triggered',
      target_id: jobId,
      target_type: 'reindex_job',
      metadata: {
        ...job,
        status: 'pending'
      }
    })

  return job
}

async function processReindexJob(supabase: any, jobId: string, type: string, target?: string) {
  try {
    // Update job status to running
    await updateJobStatus(supabase, jobId, 'running', 0)

    // Get documents to reindex
    let query = supabase.from('compliance_knowledge').select('id, source_document_id, content_chunk')
    
    if (type === 'jurisdiction' && target) {
      query = query.eq('jurisdiction', target)
    }

    const { data: documents } = await query

    if (!documents || documents.length === 0) {
      await updateJobStatus(supabase, jobId, 'completed', 100)
      return
    }

    const totalDocuments = documents.length
    await updateJobProgress(supabase, jobId, 0, 0, totalDocuments)

    // Process documents in batches
    const batchSize = 10
    for (let i = 0; i < documents.length; i += batchSize) {
      const batch = documents.slice(i, i + batchSize)
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real implementation, you would:
      // 1. Generate new embeddings for each document
      // 2. Update the embedding column in compliance_knowledge
      // 3. Handle any errors appropriately
      
      const processed = Math.min(i + batchSize, documents.length)
      const progress = Math.round((processed / totalDocuments) * 100)
      
      await updateJobProgress(supabase, jobId, progress, processed, totalDocuments)
    }

    // Mark job as completed
    await updateJobStatus(supabase, jobId, 'completed', 100)

    // Log completion
    await supabase.rpc('log_admin_action', {
      p_action_type: 'reindex_completed',
      p_target_id: jobId,
      p_target_type: 'reindex_job',
      p_metadata: { 
        job_id: jobId,
        documents_processed: totalDocuments,
        completed_at: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Reindex job failed:', error)
    await updateJobStatus(supabase, jobId, 'failed', 0, error instanceof Error ? error.message : 'Unknown error')
  }
}

async function updateJobStatus(supabase: any, jobId: string, status: string, progress: number, error?: string) {
  const updateData: any = {
    status,
    progress,
    updated_at: new Date().toISOString()
  }

  if (status === 'completed') {
    updateData.completed_at = new Date().toISOString()
  }

  if (error) {
    updateData.error = error
  }

  await supabase
    .from('admin_actions')
    .update({
      metadata: supabase.raw(`metadata || '${JSON.stringify(updateData)}'::jsonb`)
    })
    .eq('target_id', jobId)
    .eq('action_type', 'reindex_triggered')
}

async function updateJobProgress(supabase: any, jobId: string, progress: number, processed: number, total: number) {
  await supabase
    .from('admin_actions')
    .update({
      metadata: supabase.raw(`metadata || '${JSON.stringify({
        progress,
        documents_processed: processed,
        total_documents: total,
        updated_at: new Date().toISOString()
      })}'::jsonb`)
    })
    .eq('target_id', jobId)
    .eq('action_type', 'reindex_triggered')
}
