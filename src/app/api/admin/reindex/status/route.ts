import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/reindex/status - Get current reindex job status
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get the most recent reindex job
    const { data: recentJob } = await supabase
      .from('admin_actions')
      .select('*')
      .eq('action_type', 'reindex_triggered')
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (!recentJob) {
      return NextResponse.json({
        success: true,
        job: null
      })
    }

    const job = {
      id: recentJob.target_id,
      type: recentJob.metadata.type,
      target: recentJob.metadata.target,
      status: recentJob.metadata.status,
      progress: recentJob.metadata.progress || 0,
      startedAt: recentJob.metadata.startedAt,
      completedAt: recentJob.metadata.completed_at,
      error: recentJob.metadata.error,
      documentsProcessed: recentJob.metadata.documents_processed,
      totalDocuments: recentJob.metadata.total_documents
    }

    return NextResponse.json({
      success: true,
      job
    })

  } catch (error) {
    console.error('Reindex status error:', error)
    return NextResponse.json(
      { error: 'Failed to get reindex status' },
      { status: 500 }
    )
  }
}
