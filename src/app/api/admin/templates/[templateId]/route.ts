import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// PATCH /api/admin/templates/[templateId] - Update template
export async function PATCH(
  request: NextRequest,
  { params }: { params: { templateId: string } }
) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { templateId } = params
    const updateData = await request.json()

    // Validate template exists
    const { data: existingTemplate } = await supabase
      .from('prompt_templates')
      .select('id, name')
      .eq('id', templateId)
      .single()

    if (!existingTemplate) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Prepare update object
    const updates: any = {
      updated_at: new Date().toISOString()
    }

    if (updateData.name !== undefined) {
      updates.name = updateData.name.trim()
    }
    if (updateData.description !== undefined) {
      updates.description = updateData.description?.trim() || null
    }
    if (updateData.templateText !== undefined) {
      updates.template_text = updateData.templateText.trim()
    }
    if (updateData.category !== undefined) {
      updates.category = updateData.category
    }
    if (updateData.isActive !== undefined) {
      updates.is_active = updateData.isActive
    }

    // Update template
    const { data: template, error } = await supabase
      .from('prompt_templates')
      .update(updates)
      .eq('id', templateId)
      .select()
      .single()

    if (error) {
      throw error
    }

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'template_updated',
      p_target_id: templateId,
      p_target_type: 'prompt_template',
      p_metadata: {
        template_id: templateId,
        name: template.name,
        updated_fields: Object.keys(updateData)
      }
    })

    return NextResponse.json({
      success: true,
      template
    })

  } catch (error) {
    console.error('Template update error:', error)
    return NextResponse.json(
      { error: 'Failed to update template' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/templates/[templateId] - Delete template
export async function DELETE(
  request: NextRequest,
  { params }: { params: { templateId: string } }
) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { templateId } = params

    // Get template info before deletion
    const { data: template } = await supabase
      .from('prompt_templates')
      .select('id, name, category')
      .eq('id', templateId)
      .single()

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    // Delete template
    const { error } = await supabase
      .from('prompt_templates')
      .delete()
      .eq('id', templateId)

    if (error) {
      throw error
    }

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'template_deleted',
      p_target_id: templateId,
      p_target_type: 'prompt_template',
      p_metadata: {
        template_id: templateId,
        name: template.name,
        category: template.category,
        deleted_at: new Date().toISOString()
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    })

  } catch (error) {
    console.error('Template deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 }
    )
  }
}
