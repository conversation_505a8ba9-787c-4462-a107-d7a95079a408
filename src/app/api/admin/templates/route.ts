import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/admin/templates - Get all prompt templates
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get all templates
    const { data: templates, error } = await supabase
      .from('prompt_templates')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'template_list_viewed',
      p_target_type: 'prompt_templates',
      p_metadata: { template_count: templates?.length || 0 }
    })

    return NextResponse.json({
      success: true,
      templates: templates || []
    })

  } catch (error) {
    console.error('Templates fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    )
  }
}

// POST /api/admin/templates - Create new template
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { name, description, templateText, category, isActive } = await request.json()

    // Validate required fields
    if (!name || !templateText || !category) {
      return NextResponse.json({ 
        error: 'Missing required fields: name, templateText, category' 
      }, { status: 400 })
    }

    // Create template
    const { data: template, error } = await supabase
      .from('prompt_templates')
      .insert({
        name: name.trim(),
        description: description?.trim() || null,
        template_text: templateText.trim(),
        category,
        is_active: isActive !== false, // Default to true
        created_by: user.id
      })
      .select()
      .single()

    if (error) {
      throw error
    }

    // Log admin action
    await supabase.rpc('log_admin_action', {
      p_action_type: 'template_created',
      p_target_id: template.id,
      p_target_type: 'prompt_template',
      p_metadata: {
        template_id: template.id,
        name: template.name,
        category: template.category
      }
    })

    return NextResponse.json({
      success: true,
      template
    })

  } catch (error) {
    console.error('Template creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    )
  }
}
