import { NextRequest, NextResponse } from 'next/server'
import { getAIConfig, validateAIModel, getModelRecommendations, getContextWindow } from '@/lib/ai-config'

export async function GET() {
  try {
    const config = getAIConfig()
    const isValidModel = validateAIModel(config.model)
    const recommendations = getModelRecommendations(config.model)

    return NextResponse.json({
      current: {
        ...config,
        contextWindow: config.contextWindow || getContextWindow(config.model)
      },
      isValidModel,
      recommendations,
      availableModels: [
        'gpt-4.1-nano',
        'gpt-4.1-mini',
        'gpt-4.1',
        'gpt-4o-mini',
        'gpt-4o',
        'gpt-4-turbo',
        'gpt-4',
        'gpt-3.5-turbo'
      ],
      environmentVariables: {
        OPENAI_MODEL: process.env.OPENAI_MODEL || 'not set',
        OPENAI_MAX_TOKENS: process.env.OPENAI_MAX_TOKENS || 'not set',
        OPENAI_TEMPERATURE: process.env.OPENAI_TEMPERATURE || 'not set'
      }
    })
  } catch (error) {
    console.error('AI config error:', error)
    return NextResponse.json({
      error: `Failed to get AI configuration: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { model, maxTokens, temperature } = await request.json()

    // Validate inputs
    if (model && !validateAIModel(model)) {
      return NextResponse.json({
        error: `Unsupported model: ${model}`
      }, { status: 400 })
    }

    if (maxTokens && (maxTokens < 1000 || maxTokens > 100000)) {
      return NextResponse.json({
        error: 'Max tokens must be between 1000 and 100000'
      }, { status: 400 })
    }

    if (temperature && (temperature < 0 || temperature > 2)) {
      return NextResponse.json({
        error: 'Temperature must be between 0 and 2'
      }, { status: 400 })
    }

    // Note: This endpoint shows current config but doesn't actually update environment variables
    // Environment variables need to be updated in .env.local file and server restarted
    const currentConfig = getAIConfig()

    return NextResponse.json({
      message: 'To update AI configuration, modify these environment variables in .env.local and restart the server:',
      currentConfig,
      suggestedUpdates: {
        OPENAI_MODEL: model || currentConfig.model,
        OPENAI_MAX_TOKENS: maxTokens || currentConfig.maxTokens,
        OPENAI_TEMPERATURE: temperature || currentConfig.temperature
      },
      instructions: [
        '1. Update .env.local file with new values',
        '2. Restart the development server (npm run dev)',
        '3. Verify changes with GET /api/ai-config'
      ]
    })
  } catch (error) {
    console.error('AI config update error:', error)
    return NextResponse.json({
      error: `Failed to process AI configuration update: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
}
