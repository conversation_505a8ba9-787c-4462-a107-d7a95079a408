import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

interface FrequentArea {
  area: string
  count: number
  popular_projects: string[]
}

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ areas: [] })
    }

    // Get user's search history
    const { data: searchHistory } = await supabase
      .from('search_history')
      .select('address, rule_type, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(100)

    if (!searchHistory || searchHistory.length === 0) {
      return NextResponse.json({ areas: [] })
    }

    // Analyze frequent areas
    const areaAnalysis = new Map<string, {
      area: string
      count: number
      last_searched: string
      first_searched: string
      projects: Map<string, number>
    }>()

    searchHistory.forEach(search => {
      // Extract area from address (typically city/state)
      const addressParts = search.address.split(',')
      if (addressParts.length >= 2) {
        // Get city (second to last part) and state (last part)
        const city = addressParts[addressParts.length - 2]?.trim()
        const state = addressParts[addressParts.length - 1]?.trim()
        
        if (city && state) {
          const area = `${city}, ${state}`
          
          if (!areaAnalysis.has(area)) {
            areaAnalysis.set(area, {
              area,
              count: 0,
              last_searched: search.created_at,
              projects: new Map<string, number>(),
              first_searched: search.created_at
            })
          }

          const areaData = areaAnalysis.get(area)
          if (areaData) {
            areaData.count++

            // Track project types in this area
            const projectType = search.rule_type || 'unknown'
            areaData.projects.set(projectType, (areaData.projects.get(projectType) || 0) + 1)

            // Update last searched if this is more recent
            if (new Date(search.created_at) > new Date(areaData.last_searched)) {
              areaData.last_searched = search.created_at
            }

            // Update first searched if this is older
            if (new Date(search.created_at) < new Date(areaData.first_searched)) {
              areaData.first_searched = search.created_at
            }
          }
        }
      }
    })

    // Convert to array and sort by frequency
    const frequentAreas = Array.from(areaAnalysis.values())
      .filter(area => area.count >= 2) // Only show areas with 2+ searches
      .map(area => ({
        area: area.area,
        count: area.count,
        last_searched: area.last_searched,
        first_searched: area.first_searched,
        popular_projects: Array.from(area.projects.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 3)
          .map(([project]) => project),
        search_frequency: calculateFrequency(area.first_searched, area.last_searched, area.count)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // Add insights
    const insights = generateInsights(frequentAreas, searchHistory.length)

    return NextResponse.json({
      areas: frequentAreas,
      insights,
      total_areas: frequentAreas.length,
      total_searches: searchHistory.length,
      generated_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Frequent areas analysis error:', error)
    return NextResponse.json({ areas: [] })
  }
}

function calculateFrequency(firstSearched: string, lastSearched: string, count: number) {
  const first = new Date(firstSearched)
  const last = new Date(lastSearched)
  const daysDiff = Math.max(1, Math.ceil((last.getTime() - first.getTime()) / (1000 * 60 * 60 * 24)))
  
  return {
    searches_per_day: count / daysDiff,
    total_days: daysDiff,
    frequency_label: count / daysDiff > 0.5 ? 'Very Active' : 
                    count / daysDiff > 0.2 ? 'Active' : 'Occasional'
  }
}

function generateInsights(areas: FrequentArea[], totalSearches: number) {
  const insights: Array<{
    type: string
    title: string
    description: string
    area?: string
    areas?: string[]
    project?: string
  }> = []

  if (areas.length === 0) {
    return insights
  }

  // Most active area
  const mostActive = areas[0]
  insights.push({
    type: 'most_active_area',
    title: `${mostActive.area} is your most searched area`,
    description: `${mostActive.count} searches (${Math.round((mostActive.count / totalSearches) * 100)}% of total)`,
    area: mostActive.area
  })

  // Geographic diversity
  if (areas.length >= 3) {
    insights.push({
      type: 'geographic_diversity',
      title: 'You search across multiple areas',
      description: `Active in ${areas.length} different areas`,
      areas: areas.slice(0, 3).map(a => a.area)
    })
  }

  // Project consistency
  const projectConsistency = analyzeProjectConsistency(areas)
  if (projectConsistency.consistent) {
    insights.push({
      type: 'project_consistency',
      title: `You consistently search for ${projectConsistency.project}`,
      description: `${projectConsistency.project} appears in ${projectConsistency.areas} areas`,
      project: projectConsistency.project
    })
  }

  return insights
}

function analyzeProjectConsistency(areas: FrequentArea[]) {
  const projectCounts = new Map<string, number>()
  
  areas.forEach(area => {
    area.popular_projects.forEach((project: string) => {
      projectCounts.set(project, (projectCounts.get(project) || 0) + 1)
    })
  })

  const mostCommon = Array.from(projectCounts.entries())
    .sort((a, b) => b[1] - a[1])[0]

  if (mostCommon && mostCommon[1] >= Math.ceil(areas.length * 0.6)) {
    return {
      consistent: true,
      project: mostCommon[0],
      areas: mostCommon[1]
    }
  }

  return { consistent: false }
}
