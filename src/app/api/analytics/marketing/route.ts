import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'

// GET /api/analytics/marketing - Get marketing analytics overview
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get marketing analytics data
    const analytics = await getMarketingAnalytics(supabase)

    return NextResponse.json({
      success: true,
      analytics
    })
  } catch (error) {
    console.error('Error in GET /api/analytics/marketing:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getMarketingAnalytics(supabase: SupabaseClient) {
  try {
    // Get email campaign stats
    const { data: campaigns } = await supabase
      .from('email_campaigns')
      .select('*')

    // Get user stats
    const { data: users } = await supabase
      .from('profiles')
      .select('subscription_tier, created_at')

    // Get search activity (as engagement metric)
    const { data: searches } = await supabase
      .from('search_history')
      .select('created_at')
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days

    // Calculate metrics
    const totalCampaigns = campaigns?.length || 0
    const sentCampaigns = campaigns?.filter(c => c.sent_at).length || 0
    const openedCampaigns = campaigns?.filter(c => c.opened_at).length || 0
    const clickedCampaigns = campaigns?.filter(c => c.clicked_at).length || 0

    const totalUsers = users?.length || 0
    const freeUsers = users?.filter(u => u.subscription_tier === 'free').length || 0
    const proUsers = users?.filter(u => u.subscription_tier === 'pro').length || 0
    const appraiserUsers = users?.filter(u => u.subscription_tier === 'appraiser').length || 0

    const recentSearches = searches?.length || 0

    // Calculate rates
    const openRate = sentCampaigns > 0 ? Math.round((openedCampaigns / sentCampaigns) * 100) : 0
    const clickRate = sentCampaigns > 0 ? Math.round((clickedCampaigns / sentCampaigns) * 100) : 0
    const conversionRate = totalUsers > 0 ? Math.round(((proUsers + appraiserUsers) / totalUsers) * 100) : 0

    return {
      overview: {
        totalCampaigns,
        sentCampaigns,
        openRate,
        clickRate,
        conversionRate,
        totalSubscribers: totalUsers,
        activeUsers: recentSearches
      },
      campaigns: {
        total: totalCampaigns,
        sent: sentCampaigns,
        opened: openedCampaigns,
        clicked: clickedCampaigns,
        openRate,
        clickRate
      },
      subscribers: {
        total: totalUsers,
        free: freeUsers,
        pro: proUsers,
        appraiser: appraiserUsers,
        conversionRate
      },
      engagement: {
        recentSearches,
        averageSearchesPerUser: totalUsers > 0 ? Math.round(recentSearches / totalUsers) : 0
      },
      revenue: {
        mrr: (proUsers * 29) + (appraiserUsers * 99), // Monthly recurring revenue estimate
        arr: ((proUsers * 29) + (appraiserUsers * 99)) * 12, // Annual recurring revenue estimate
        averageRevenuePerUser: totalUsers > 0 ? Math.round(((proUsers * 29) + (appraiserUsers * 99)) / totalUsers) : 0
      }
    }
  } catch (error) {
    console.error('Error calculating marketing analytics:', error)
    // Return default analytics if there's an error
    return {
      overview: {
        totalCampaigns: 0,
        sentCampaigns: 0,
        openRate: 0,
        clickRate: 0,
        conversionRate: 0,
        totalSubscribers: 0,
        activeUsers: 0
      },
      campaigns: {
        total: 0,
        sent: 0,
        opened: 0,
        clicked: 0,
        openRate: 0,
        clickRate: 0
      },
      subscribers: {
        total: 0,
        free: 0,
        pro: 0,
        appraiser: 0,
        conversionRate: 0
      },
      engagement: {
        recentSearches: 0,
        averageSearchesPerUser: 0
      },
      revenue: {
        mrr: 0,
        arr: 0,
        averageRevenuePerUser: 0
      }
    }
  }
}
