import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

interface AnnouncementUpdateData {
  title?: string
  content?: string
  type?: string
  target_users?: string[]
  priority?: string
  is_active?: boolean
  expires_at?: string
  updated_at: string
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { searchParams } = new URL(request.url)
    
    const targetUsers = searchParams.get('target_users') || 'all'
    const limit = parseInt(searchParams.get('limit') || '5')

    let query = supabase
      .from('feature_announcements')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: true })
      .order('created_at', { ascending: false })
      .limit(limit)

    // Filter by target users
    if (targetUsers !== 'all') {
      query = query.or(`target_users.eq.all,target_users.eq.${targetUsers}`)
    }

    // Filter out expired announcements
    const now = new Date().toISOString()
    query = query.or(`expires_at.is.null,expires_at.gt.${now}`)

    const { data: announcements, error } = await query

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      announcements: announcements || []
    })

  } catch (error) {
    console.error('Announcements fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch announcements'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { title, content, type, target_users, priority, expires_at } = body

    if (!title || !content) {
      return NextResponse.json({
        success: false,
        error: 'Title and content are required'
      }, { status: 400 })
    }

    const announcementData = {
      title,
      content,
      type: type || 'info',
      target_users: target_users || 'all',
      priority: priority || 0,
      expires_at: expires_at || null
    }

    const { data: announcement, error } = await supabase
      .from('feature_announcements')
      .insert(announcementData)
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      announcement
    })

  } catch (error) {
    console.error('Announcement create error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create announcement'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { id, title, content, type, target_users, priority, is_active, expires_at } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'Announcement ID is required'
      }, { status: 400 })
    }

    const updateData: AnnouncementUpdateData = {
      updated_at: new Date().toISOString()
    }

    if (title !== undefined) updateData.title = title
    if (content !== undefined) updateData.content = content
    if (type !== undefined) updateData.type = type
    if (target_users !== undefined) updateData.target_users = target_users
    if (priority !== undefined) updateData.priority = priority
    if (is_active !== undefined) updateData.is_active = is_active
    if (expires_at !== undefined) updateData.expires_at = expires_at

    const { data: announcement, error } = await supabase
      .from('feature_announcements')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      announcement
    })

  } catch (error) {
    console.error('Announcement update error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update announcement'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'Announcement ID is required'
      }, { status: 400 })
    }

    const { error } = await supabase
      .from('feature_announcements')
      .delete()
      .eq('id', id)

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      message: 'Announcement deleted successfully'
    })

  } catch (error) {
    console.error('Announcement delete error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete announcement'
    }, { status: 500 })
  }
}
