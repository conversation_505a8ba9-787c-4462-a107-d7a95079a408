import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { searchParams } = new URL(request.url)
    
    const articleId = searchParams.get('article_id')

    if (!articleId) {
      return NextResponse.json({
        success: false,
        error: 'Article ID is required'
      }, { status: 400 })
    }

    // Get ratings for the article
    const { data: ratings, error } = await supabase
      .from('article_ratings')
      .select('*')
      .eq('article_id', articleId)

    if (error) {
      throw error
    }

    // Calculate statistics
    const totalRatings = ratings?.length || 0
    const averageRating = totalRatings > 0 
      ? ratings!.reduce((sum, r) => sum + (r.rating || 0), 0) / totalRatings 
      : 0
    const helpfulCount = ratings?.filter(r => r.helpful === true).length || 0
    const notHelpfulCount = ratings?.filter(r => r.helpful === false).length || 0

    return NextResponse.json({
      success: true,
      statistics: {
        totalRatings,
        averageRating: Math.round(averageRating * 10) / 10,
        helpfulCount,
        notHelpfulCount,
        helpfulPercentage: totalRatings > 0 ? Math.round((helpfulCount / totalRatings) * 100) : 0
      },
      ratings: ratings || []
    })

  } catch (error) {
    console.error('Article ratings fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch ratings'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { article_id, rating, helpful, feedback } = body

    if (!article_id) {
      return NextResponse.json({
        success: false,
        error: 'Article ID is required'
      }, { status: 400 })
    }

    // Validate rating if provided
    if (rating !== undefined && (rating < 1 || rating > 5)) {
      return NextResponse.json({
        success: false,
        error: 'Rating must be between 1 and 5'
      }, { status: 400 })
    }

    const ratingData = {
      article_id,
      user_id: user.id,
      rating: rating || null,
      helpful: helpful !== undefined ? helpful : null,
      feedback: feedback || null
    }

    const { data: newRating, error } = await supabase
      .from('article_ratings')
      .upsert(ratingData, { onConflict: 'article_id,user_id' })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      rating: newRating
    })

  } catch (error) {
    console.error('Article rating create error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create rating'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const articleId = searchParams.get('article_id')

    if (!articleId) {
      return NextResponse.json({
        success: false,
        error: 'Article ID is required'
      }, { status: 400 })
    }

    const { error } = await supabase
      .from('article_ratings')
      .delete()
      .eq('article_id', articleId)
      .eq('user_id', user.id)

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      message: 'Rating deleted successfully'
    })

  } catch (error) {
    console.error('Article rating delete error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete rating'
    }, { status: 500 })
  }
}
