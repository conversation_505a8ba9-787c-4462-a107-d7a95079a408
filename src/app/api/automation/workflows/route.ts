import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/automation/workflows - Get marketing automation workflows
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get workflows (mock data for now, would come from database)
    const workflows = await getAutomationWorkflows()

    return NextResponse.json({
      success: true,
      workflows
    })
  } catch (error) {
    console.error('Error in GET /api/automation/workflows:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/automation/workflows - Create new automation workflow
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, trigger, actions, status } = body

    if (!name || !trigger || !actions) {
      return NextResponse.json({
        error: 'Name, trigger, and actions are required'
      }, { status: 400 })
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Create workflow
    const workflowData = {
      name,
      description: description || null,
      trigger,
      actions,
      status: status || 'draft',
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // For now, return mock success (would save to database in real implementation)
    const mockWorkflow = {
      id: `workflow_${Date.now()}`,
      ...workflowData
    }

    return NextResponse.json({
      success: true,
      message: 'Workflow created successfully',
      workflow: mockWorkflow
    })
  } catch (error) {
    console.error('Error in POST /api/automation/workflows:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getAutomationWorkflows() {
  try {
    // In a real implementation, this would fetch from a workflows table
    // For now, return mock workflows based on existing email campaigns

    const mockWorkflows = [
      {
        id: 'workflow_welcome_series',
        name: 'Welcome Email Series',
        description: 'Automated welcome sequence for new users',
        trigger: {
          type: 'user_signup',
          conditions: {
            subscription_tier: 'free'
          }
        },
        actions: [
          {
            type: 'send_email',
            template: 'welcome',
            delay: 0
          },
          {
            type: 'send_email',
            template: 'getting_started',
            delay: 24 * 60 * 60 * 1000 // 24 hours
          },
          {
            type: 'send_email',
            template: 'upgrade_nudge',
            delay: 7 * 24 * 60 * 60 * 1000 // 7 days
          }
        ],
        status: 'active',
        stats: {
          triggered: 156,
          completed: 142,
          conversion_rate: 23
        },
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-20T15:30:00Z'
      },
      {
        id: 'workflow_upgrade_nudge',
        name: 'Upgrade Nudge Campaign',
        description: 'Encourage free users to upgrade after usage limits',
        trigger: {
          type: 'usage_limit_reached',
          conditions: {
            subscription_tier: 'free',
            pulls_this_month: 5
          }
        },
        actions: [
          {
            type: 'send_email',
            template: 'upgrade_nudge',
            delay: 0
          },
          {
            type: 'send_email',
            template: 'upgrade_reminder',
            delay: 3 * 24 * 60 * 60 * 1000 // 3 days
          }
        ],
        status: 'active',
        stats: {
          triggered: 89,
          completed: 76,
          conversion_rate: 31
        },
        created_at: '2024-01-10T09:00:00Z',
        updated_at: '2024-01-25T11:15:00Z'
      },
      {
        id: 'workflow_re_engagement',
        name: 'Re-engagement Campaign',
        description: 'Win back inactive users',
        trigger: {
          type: 'user_inactive',
          conditions: {
            last_login: '30_days_ago'
          }
        },
        actions: [
          {
            type: 'send_email',
            template: 'we_miss_you',
            delay: 0
          },
          {
            type: 'send_email',
            template: 'special_offer',
            delay: 7 * 24 * 60 * 60 * 1000 // 7 days
          }
        ],
        status: 'active',
        stats: {
          triggered: 234,
          completed: 198,
          conversion_rate: 12
        },
        created_at: '2024-01-05T14:00:00Z',
        updated_at: '2024-01-22T16:45:00Z'
      },
      {
        id: 'workflow_onboarding',
        name: 'User Onboarding Flow',
        description: 'Guide new users through key features',
        trigger: {
          type: 'first_search_completed',
          conditions: {}
        },
        actions: [
          {
            type: 'send_email',
            template: 'search_tips',
            delay: 2 * 60 * 60 * 1000 // 2 hours
          },
          {
            type: 'send_email',
            template: 'advanced_features',
            delay: 2 * 24 * 60 * 60 * 1000 // 2 days
          }
        ],
        status: 'draft',
        stats: {
          triggered: 0,
          completed: 0,
          conversion_rate: 0
        },
        created_at: '2024-01-28T12:00:00Z',
        updated_at: '2024-01-28T12:00:00Z'
      }
    ]

    return {
      workflows: mockWorkflows,
      summary: {
        total: mockWorkflows.length,
        active: mockWorkflows.filter(w => w.status === 'active').length,
        draft: mockWorkflows.filter(w => w.status === 'draft').length,
        paused: mockWorkflows.filter(w => w.status === 'paused').length
      },
      performance: {
        totalTriggered: mockWorkflows.reduce((sum, w) => sum + w.stats.triggered, 0),
        totalCompleted: mockWorkflows.reduce((sum, w) => sum + w.stats.completed, 0),
        averageConversionRate: Math.round(
          mockWorkflows.reduce((sum, w) => sum + w.stats.conversion_rate, 0) / mockWorkflows.length
        )
      }
    }
  } catch (error) {
    console.error('Error getting automation workflows:', error)
    return {
      workflows: [],
      summary: {
        total: 0,
        active: 0,
        draft: 0,
        paused: 0
      },
      performance: {
        totalTriggered: 0,
        totalCompleted: 0,
        averageConversionRate: 0
      }
    }
  }
}
