import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe/config'
import { trackSubscriptionCancellation } from '@/lib/automation/events'

export async function POST() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's profile with Stripe customer ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id, subscription_tier')
      .eq('id', user.id)
      .single()

    if (!profile?.stripe_customer_id) {
      return NextResponse.json({ error: 'No billing account found' }, { status: 404 })
    }

    if (profile.subscription_tier === 'trial') {
      return NextResponse.json({ error: 'No active subscription to cancel' }, { status: 400 })
    }

    // Get active subscriptions for the customer
    const subscriptions = await stripe.subscriptions.list({
      customer: profile.stripe_customer_id,
      status: 'active',
    })

    if (subscriptions.data.length === 0) {
      return NextResponse.json({ error: 'No active subscription found' }, { status: 404 })
    }

    // Cancel the subscription at period end
    const subscription = subscriptions.data[0]
    await stripe.subscriptions.update(subscription.id, {
      cancel_at_period_end: true,
    })

    // Update profile to reflect cancellation
    await supabase
      .from('profiles')
      .update({
        cancel_at_period_end: true,
        subscription_status: 'canceling'
      })
      .eq('id', user.id)

    // Track subscription cancellation for automation
    try {
      await trackSubscriptionCancellation(user.id, {
        tier: profile.subscription_tier,
        cancelAtPeriodEnd: true
      })
    } catch (trackingError) {
      console.error('Error tracking subscription cancellation:', trackingError)
      // Don't fail the cancellation if tracking fails
    }

    return NextResponse.json({
      success: true,
      message: 'Subscription will be canceled at the end of the billing period'
    })

  } catch (error) {
    console.error('Error canceling subscription:', error)
    return NextResponse.json(
      { error: 'Failed to cancel subscription' },
      { status: 500 }
    )
  }
}
