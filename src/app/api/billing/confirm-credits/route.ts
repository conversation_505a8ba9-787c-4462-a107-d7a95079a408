import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe/config'

export async function POST(request: NextRequest) {
  try {
    // Parse request body with error handling
    let requestData
    try {
      requestData = await request.json()
    } catch {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      )
    }

    const { paymentIntentId } = requestData

    if (!paymentIntentId || typeof paymentIntentId !== 'string') {
      return NextResponse.json(
        { error: 'paymentIntentId is required and must be a string' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Retrieve the payment intent to get metadata
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)

    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json({ error: 'Payment not completed' }, { status: 400 })
    }

    const { credits } = paymentIntent.metadata

    if (!credits) {
      return NextResponse.json({ error: 'Invalid payment metadata' }, { status: 400 })
    }

    const creditsToAdd = parseInt(credits)

    if (isNaN(creditsToAdd) || creditsToAdd < 1) {
      return NextResponse.json({ error: 'Invalid credits amount' }, { status: 400 })
    }

    // Get current profile for email
    const { data: profile } = await supabase
      .from('profiles')
      .select('email, name, extra_credits')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 })
    }

    // Use the custom function to increment credits
    const { error: creditsError } = await supabase.rpc('increment_extra_credits', {
      user_id_param: user.id,
      credits_to_add: creditsToAdd
    })

    if (creditsError) {
      console.error('Error adding credits:', creditsError)
      return NextResponse.json({ error: 'Failed to add credits' }, { status: 500 })
    }

    // Get updated profile
    const { data: updatedProfile } = await supabase
      .from('profiles')
      .select('extra_credits')
      .eq('id', user.id)
      .single()

    return NextResponse.json({
      success: true,
      credits: {
        added: creditsToAdd,
        total: updatedProfile?.extra_credits || 0,
      }
    })
  } catch (error) {
    console.error('Error confirming credits purchase:', error)
    return NextResponse.json(
      { error: 'Failed to process credits purchase' },
      { status: 500 }
    )
  }
}
