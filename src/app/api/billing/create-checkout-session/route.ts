import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe/config'

export async function POST(request: NextRequest) {
  try {
    const { priceId, planType } = await request.json()

    console.log('🔍 Checkout Debug Info:')
    console.log('- Received priceId:', priceId)
    console.log('- Received planType:', planType)
    console.log('- Env STRIPE_PRICE_PROFESSIONAL_MONTHLY:', process.env.STRIPE_PRICE_PROFESSIONAL_MONTHLY)
    console.log('- Env NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_MONTHLY:', process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_MONTHLY)

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get or create Stripe customer
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id, email, subscription_tier, subscription_status')
      .eq('id', user.id)
      .single()

    let customerId = profile?.stripe_customer_id

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email!,
        metadata: {
          supabase_user_id: user.id,
        },
      })
      customerId = customer.id

      // Update profile with Stripe customer ID
      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id)
    }

    // Check for existing active subscriptions
    if (customerId) {
      const existingSubscriptions = await stripe.subscriptions.list({
        customer: customerId,
        status: 'active',
        limit: 10
      })

      if (existingSubscriptions.data.length > 0) {
        console.log(`Customer ${customerId} already has ${existingSubscriptions.data.length} active subscription(s)`)
        return NextResponse.json({
          error: 'You already have an active subscription. Please manage your existing subscription instead of creating a new one.',
          code: 'EXISTING_SUBSCRIPTION',
          existingSubscriptions: existingSubscriptions.data.length
        }, { status: 400 })
      }
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}&plan_type=${planType}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout/error?error=canceled&plan_type=${planType}`,
      metadata: {
        supabase_user_id: user.id,
        plan_type: planType,
      },
      subscription_data: {
        metadata: {
          supabase_user_id: user.id,
          plan_type: planType,
        },
      },
    })

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    })
  } catch (error) {
    console.error('Error creating checkout session:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
}
