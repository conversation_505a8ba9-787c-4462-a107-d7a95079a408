import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe/config'

export async function POST(request: NextRequest) {
  try {
    // Parse request body with error handling
    let requestData
    try {
      requestData = await request.json()
    } catch {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      )
    }

    const { credits, priceInCents } = requestData

    // Validate required parameters
    if (!credits || typeof credits !== 'number' || credits < 1) {
      return NextResponse.json(
        { error: 'credits is required and must be a positive number' },
        { status: 400 }
      )
    }

    if (!priceInCents || typeof priceInCents !== 'number' || priceInCents < 100) {
      return NextResponse.json(
        { error: 'priceInCents is required and must be at least 100 (minimum $1.00)' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify user is on trial plan
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier, stripe_customer_id, email, name')
      .eq('id', user.id)
      .single()

    if (profile?.subscription_tier !== 'trial') {
      return NextResponse.json(
        { error: 'Credit purchases are only available for trial plan users' },
        { status: 400 }
      )
    }

    // Get or create Stripe customer
    let customerId = profile?.stripe_customer_id

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email!,
        name: profile?.name || user.user_metadata?.full_name || '',
        metadata: {
          supabase_user_id: user.id,
        },
      })
      customerId = customer.id

      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id)
    }

    // Additional validation passed - credits and price already validated above

    // Create payment intent for one-time credit purchase
    const paymentIntent = await stripe.paymentIntents.create({
      amount: priceInCents,
      currency: 'usd',
      customer: customerId,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        supabase_user_id: user.id,
        type: 'credits_purchase',
        credits: credits.toString(),
      },
    })

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    })
  } catch (error) {
    console.error('Error creating credits payment intent:', error)
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    )
  }
}
