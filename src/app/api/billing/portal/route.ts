import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'
import { stripe } from '@/lib/stripe/config'

export async function GET(request: NextRequest) {
  let profile: { stripe_customer_id?: string } | null = null

  try {
    console.log('🔄 Billing portal request started')

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      console.log('❌ No authenticated user found')
      return NextResponse.redirect(new URL('/login', request.url))
    }

    console.log('✅ User authenticated:', user.id)

    // Get user's Stripe customer ID
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('❌ Error fetching profile:', profileError)
      return NextResponse.redirect(new URL('/account?error=profile-error', request.url))
    }

    profile = profileData

    if (!profile?.stripe_customer_id) {
      console.log('🔄 User has no Stripe customer ID, creating one:', user.id)

      // Create Stripe customer for the user
      const customer = await stripe.customers.create({
        email: user.email!,
        metadata: {
          supabase_user_id: user.id,
        },
      })

      console.log('✅ Created Stripe customer:', customer.id)

      // Update profile with new Stripe customer ID
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ stripe_customer_id: customer.id })
        .eq('id', user.id)

      if (updateError) {
        console.error('❌ Error updating profile with customer ID:', updateError)
        return NextResponse.redirect(new URL('/account?error=customer-update-failed', request.url))
      }

      profile = { stripe_customer_id: customer.id }
    }

    console.log('✅ Using Stripe customer ID:', profile.stripe_customer_id)

    // Get return URL from query params or default to account page
    const url = new URL(request.url)
    const returnUrl = url.searchParams.get('return_url') || '/account'
    const fullReturnUrl = `${process.env.NEXT_PUBLIC_SITE_URL}${returnUrl}?portal_return=true`

    console.log('🔄 Creating billing portal session...')

    // Create billing portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: profile.stripe_customer_id!,
      return_url: fullReturnUrl,
    })

    console.log('✅ Billing portal session created, redirecting to:', portalSession.url)

    return NextResponse.redirect(portalSession.url)
  } catch (error: unknown) {
    console.error('Error creating billing portal session:', error)

    // Handle specific Stripe errors
    if (error && typeof error === 'object' && 'code' in error) {
      if (error.code === 'billing_portal_configuration_inactive') {
        console.error('Billing portal is not configured in Stripe Dashboard')
        return NextResponse.redirect(new URL('/account?error=portal-not-configured', request.url))
      }

      if (error.code === 'resource_missing') {
        console.error('Stripe customer not found:', profile?.stripe_customer_id)
        return NextResponse.redirect(new URL('/account?error=customer-not-found', request.url))
      }
    }

    return NextResponse.redirect(new URL('/account?error=portal-failed', request.url))
  }
}
