import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'

// GET /api/blog/posts - Get blog posts
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const published = searchParams.get('published')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    const category = searchParams.get('category')

    // Check if user is authenticated for admin access
    const {
      data: { user },
    } = await supabase.auth.getUser()

    let isAdmin = false
    if (user) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      isAdmin = profile?.role === 'admin'
    }

    // Get blog posts
    const posts = await getBlogPosts(supabase, {
      published: published === 'true' ? true : published === 'false' ? false : undefined,
      limit,
      offset,
      category: category || undefined,
      isAdmin
    })

    return NextResponse.json({
      success: true,
      posts: posts.posts,
      pagination: posts.pagination
    })
  } catch (error) {
    console.error('Error in GET /api/blog/posts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/blog/posts - Create new blog post (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, content, excerpt, category, tags, published, featured_image } = body

    if (!title || !content) {
      return NextResponse.json({
        error: 'Title and content are required'
      }, { status: 400 })
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Create blog post
    const postData = {
      title,
      content,
      excerpt: excerpt || content.substring(0, 200) + '...',
      category: category || 'general',
      tags: tags || [],
      published: published || false,
      featured_image: featured_image || null,
      author_id: user.id,
      slug: generateSlug(title),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // For now, return mock success (would save to database in real implementation)
    const mockPost = {
      id: `post_${Date.now()}`,
      ...postData
    }

    return NextResponse.json({
      success: true,
      message: 'Blog post created successfully',
      post: mockPost
    })
  } catch (error) {
    console.error('Error in POST /api/blog/posts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getBlogPosts(_supabase: SupabaseClient, options: {
  published?: boolean
  limit: number
  offset: number
  category?: string
  isAdmin: boolean
}) {
  try {
    // Mock blog posts data (would come from database in real implementation)
    const mockPosts = [
      {
        id: 'post_real_estate_trends_2024',
        title: 'Real Estate Market Trends for 2024',
        slug: 'real-estate-trends-2024',
        excerpt: 'Discover the latest trends shaping the real estate market in 2024, from technology adoption to changing buyer preferences.',
        content: 'The real estate market continues to evolve rapidly in 2024...',
        category: 'market-analysis',
        tags: ['real estate', 'trends', '2024', 'market analysis'],
        published: true,
        featured_image: '/images/blog/real-estate-trends-2024.jpg',
        author: {
          id: 'admin_user',
          name: 'Ordrly Team',
          avatar: '/images/team/ordrly-team.jpg'
        },
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        views: 1247,
        reading_time: 8
      },
      {
        id: 'post_property_valuation_guide',
        title: 'Complete Guide to Property Valuation',
        slug: 'property-valuation-guide',
        excerpt: 'Learn how to accurately value properties using modern tools and techniques. Essential reading for real estate professionals.',
        content: 'Property valuation is a critical skill for real estate professionals...',
        category: 'education',
        tags: ['valuation', 'appraisal', 'guide', 'real estate'],
        published: true,
        featured_image: '/images/blog/property-valuation-guide.jpg',
        author: {
          id: 'admin_user',
          name: 'Ordrly Team',
          avatar: '/images/team/ordrly-team.jpg'
        },
        created_at: '2024-01-10T14:30:00Z',
        updated_at: '2024-01-12T09:15:00Z',
        views: 892,
        reading_time: 12
      },
      {
        id: 'post_ordrly_features_update',
        title: 'New Features: Enhanced Search and Analytics',
        slug: 'ordrly-features-update',
        excerpt: 'Exciting new features have been added to Ordrly, including improved search capabilities and detailed analytics dashboards.',
        content: 'We are excited to announce several new features that will enhance your Ordrly experience...',
        category: 'product-updates',
        tags: ['ordrly', 'features', 'updates', 'search', 'analytics'],
        published: true,
        featured_image: '/images/blog/ordrly-features-update.jpg',
        author: {
          id: 'admin_user',
          name: 'Ordrly Team',
          avatar: '/images/team/ordrly-team.jpg'
        },
        created_at: '2024-01-25T16:00:00Z',
        updated_at: '2024-01-25T16:00:00Z',
        views: 634,
        reading_time: 6
      },
      {
        id: 'post_compliance_best_practices',
        title: 'Real Estate Compliance Best Practices',
        slug: 'compliance-best-practices',
        excerpt: 'Stay compliant with the latest real estate regulations and best practices. A comprehensive guide for professionals.',
        content: 'Compliance in real estate is more important than ever...',
        category: 'compliance',
        tags: ['compliance', 'regulations', 'best practices', 'legal'],
        published: false,
        featured_image: '/images/blog/compliance-best-practices.jpg',
        author: {
          id: 'admin_user',
          name: 'Ordrly Team',
          avatar: '/images/team/ordrly-team.jpg'
        },
        created_at: '2024-01-28T11:00:00Z',
        updated_at: '2024-01-28T11:00:00Z',
        views: 0,
        reading_time: 10
      },
      {
        id: 'post_market_data_analysis',
        title: 'How to Analyze Market Data Effectively',
        slug: 'market-data-analysis',
        excerpt: 'Master the art of market data analysis with these proven techniques and tools used by top real estate professionals.',
        content: 'Effective market data analysis is the foundation of successful real estate decisions...',
        category: 'education',
        tags: ['market data', 'analysis', 'tools', 'techniques'],
        published: true,
        featured_image: '/images/blog/market-data-analysis.jpg',
        author: {
          id: 'admin_user',
          name: 'Ordrly Team',
          avatar: '/images/team/ordrly-team.jpg'
        },
        created_at: '2024-01-05T13:45:00Z',
        updated_at: '2024-01-06T08:20:00Z',
        views: 1156,
        reading_time: 9
      }
    ]

    // Filter posts based on options
    let filteredPosts = mockPosts

    // Filter by published status
    if (options.published !== undefined) {
      filteredPosts = filteredPosts.filter(post => post.published === options.published)
    } else if (!options.isAdmin) {
      // Non-admin users can only see published posts
      filteredPosts = filteredPosts.filter(post => post.published)
    }

    // Filter by category
    if (options.category) {
      filteredPosts = filteredPosts.filter(post => post.category === options.category)
    }

    // Apply pagination
    const total = filteredPosts.length
    const paginatedPosts = filteredPosts.slice(options.offset, options.offset + options.limit)

    return {
      posts: paginatedPosts,
      pagination: {
        total,
        limit: options.limit,
        offset: options.offset,
        hasMore: total > options.offset + options.limit
      }
    }
  } catch (error) {
    console.error('Error getting blog posts:', error)
    return {
      posts: [],
      pagination: {
        total: 0,
        limit: options.limit,
        offset: options.offset,
        hasMore: false
      }
    }
  }
}

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}
