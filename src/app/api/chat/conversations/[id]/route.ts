import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const conversationId = params.id

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's tier
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'free'

    // Check if user has access to chat feature
    if (!hasFeatureAccess(userTier, 'enableChat') || !isFeatureEnabled('CHAT_ENABLED')) {
      return NextResponse.json(
        {
          error: 'Chat feature requires Pro tier or higher',
          upgradeRequired: true,
          suggestedTier: 'pro'
        },
        { status: 403 }
      )
    }

    // Verify conversation belongs to user
    const { data: conversation } = await supabase
      .from('chat_conversations')
      .select('id')
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .single()

    if (!conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Delete all messages in the conversation first
    const { error: messagesError } = await supabase
      .from('chat_messages')
      .delete()
      .eq('conversation_id', conversationId)

    if (messagesError) {
      console.error('Error deleting messages:', messagesError)
      return NextResponse.json(
        { error: 'Failed to delete conversation messages' },
        { status: 500 }
      )
    }

    // Delete the conversation
    const { error: conversationError } = await supabase
      .from('chat_conversations')
      .delete()
      .eq('id', conversationId)
      .eq('user_id', user.id)

    if (conversationError) {
      console.error('Error deleting conversation:', conversationError)
      return NextResponse.json(
        { error: 'Failed to delete conversation' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Delete conversation error:', error)
    return NextResponse.json(
      {
        error: 'Failed to delete conversation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const conversationId = params.id
    const updates = await request.json()

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's tier
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'free'

    // Check if user has access to chat feature
    if (!hasFeatureAccess(userTier, 'enableChat') || !isFeatureEnabled('CHAT_ENABLED')) {
      return NextResponse.json(
        {
          error: 'Chat feature requires Pro tier or higher',
          upgradeRequired: true,
          suggestedTier: 'pro'
        },
        { status: 403 }
      )
    }

    // Verify conversation belongs to user
    const { data: conversation } = await supabase
      .from('chat_conversations')
      .select('id')
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .single()

    if (!conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Only allow updating certain fields
    const allowedUpdates: Record<string, any> = {}
    if (updates.address) allowedUpdates.address = updates.address
    if (updates.rule_type) allowedUpdates.rule_type = updates.rule_type
    if (updates.jurisdiction_name) allowedUpdates.jurisdiction_name = updates.jurisdiction_name

    if (Object.keys(allowedUpdates).length === 0) {
      return NextResponse.json(
        { error: 'No valid updates provided' },
        { status: 400 }
      )
    }

    // Add updated timestamp
    allowedUpdates.updated_at = new Date().toISOString()

    // Update the conversation
    const { data: updatedConversation, error } = await supabase
      .from('chat_conversations')
      .update(allowedUpdates)
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating conversation:', error)
      return NextResponse.json(
        { error: 'Failed to update conversation' },
        { status: 500 }
      )
    }

    return NextResponse.json(updatedConversation)

  } catch (error) {
    console.error('Update conversation error:', error)
    return NextResponse.json(
      {
        error: 'Failed to update conversation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
