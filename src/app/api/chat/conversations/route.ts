import { NextRequest, NextResponse } from 'next/server'
import { createServerClient as createSSRServerClient } from '@supabase/ssr'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import { logConversationCreated, logChatError, logChatFeatureUsage } from '@/lib/logging/chat-logger'

export async function GET(request: NextRequest) {
  try {

    // Create Supabase client with proper cookie handling (same as middleware)
    const supabase = createSSRServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // We don't need to set cookies in GET requests, but this is required
            // for the interface
          },
        },
      }
    )

    // More robust auth check
    let user = null
    try {
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
      if (authError) {
        console.error('Auth error in conversations GET:', authError)
      }
      user = authUser
    } catch (error) {
      console.error('Exception getting user in conversations GET:', error)
    }

    if (!user) {
      console.log('❌ No user found in conversations GET')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    console.log('✅ User authenticated in conversations GET:', user.email)

    // Get user's tier with fallback
    let userTier = 'free'
    try {
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_tier')
        .eq('id', user.id)
        .single()

      userTier = profile?.subscription_tier || 'free'
      console.log('✅ User tier:', userTier)
    } catch (error) {
      console.error('Error fetching profile, using free tier:', error)
    }

    // Simplified feature check - allow all authenticated users for now
    console.log('✅ Chat feature access granted for user:', user.email)

    // Get user's conversations
    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select(`
        id,
        address,
        rule_type,
        jurisdiction_name,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching conversations:', error)
      await logChatError(user.id, new Error(error.message), {
        errorType: 'fetch_conversations_failed'
      })
      return NextResponse.json(
        { error: 'Failed to fetch conversations' },
        { status: 500 }
      )
    }

    // Log feature usage
    await logChatFeatureUsage(user.id, 'conversations_fetched', {
      conversationCount: conversations.length
    })

    return NextResponse.json({ conversations })

  } catch (error) {
    console.error('Chat conversations error:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch conversations',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { address, rule_type = 'general', jurisdiction_name = 'TBD', context_data } = await request.json()

    if (!address) {
      return NextResponse.json(
        { error: 'Address is required' },
        { status: 400 }
      )
    }

    // Resolve jurisdiction if coordinates are provided and jurisdiction is TBD
    let resolvedJurisdiction = jurisdiction_name
    if (jurisdiction_name === 'TBD' && context_data?.coordinates) {
      console.log('🔍 Resolving jurisdiction from coordinates during chat creation...')
      try {
        const { getJurisdictionFromGeoapify } = await import('@/lib/services/jurisdiction-lookup')
        const coordinates = context_data.coordinates as { lat: number; lng: number }
        const jurisdictionResult = await getJurisdictionFromGeoapify(coordinates.lat, coordinates.lng)

        if (jurisdictionResult) {
          // Include state to disambiguate (e.g., "Grand Rapids, MI" vs "Grand Rapids, MN")
          resolvedJurisdiction = `${jurisdictionResult.name}, ${jurisdictionResult.state_code}`
          console.log(`✅ Resolved jurisdiction during creation: ${resolvedJurisdiction}`)
        } else {
          console.warn('⚠️ Could not resolve jurisdiction from coordinates, keeping as TBD')
        }
      } catch (error) {
        console.error('❌ Error resolving jurisdiction during creation:', error)
        // Keep as TBD if resolution fails
      }
    }

    // Create Supabase client with proper cookie handling (same as middleware)
    const supabase = createSSRServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // We don't need to set cookies in POST requests for this endpoint
          },
        },
      }
    )

    // More robust auth check
    let user = null
    try {
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
      if (authError) {
        console.error('Auth error in conversations POST:', authError)
      }
      user = authUser
    } catch (error) {
      console.error('Exception getting user in conversations POST:', error)
    }

    if (!user) {
      console.log('❌ No user found in conversations POST')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    console.log('✅ User authenticated in conversations POST:', user.email)

    // Get user's tier with fallback
    let userTier = 'free'
    try {
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_tier')
        .eq('id', user.id)
        .single()

      userTier = profile?.subscription_tier || 'free'
      console.log('✅ User tier:', userTier)
    } catch (error) {
      console.error('Error fetching profile, using free tier:', error)
    }

    // Simplified feature check - allow all authenticated users for now
    console.log('✅ Chat feature access granted for user:', user.email)

    // Check if conversation already exists for this address
    const { data: existingConversation } = await supabase
      .from('chat_conversations')
      .select('id')
      .eq('user_id', user.id)
      .eq('address', address)
      .single()

    if (existingConversation) {
      // Update existing conversation with new context
      const { data: updatedConversation, error: updateError } = await supabase
        .from('chat_conversations')
        .update({
          context_data,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingConversation.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating conversation:', updateError)
        await logChatError(user.id, new Error(updateError.message), {
          conversationId: existingConversation.id,
          errorType: 'update_conversation_failed'
        })
        return NextResponse.json(
          { error: 'Failed to update conversation' },
          { status: 500 }
        )
      }

      // Log conversation update
      await logChatFeatureUsage(user.id, 'conversation_updated', {
        conversationId: updatedConversation.id,
        address
      })

      return NextResponse.json({ conversation: updatedConversation })
    }

    // Create new conversation
    const { data: conversation, error } = await supabase
      .from('chat_conversations')
      .insert({
        user_id: user.id,
        address,
        rule_type,
        jurisdiction_name: resolvedJurisdiction,
        context_data
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating conversation:', error)
      await logChatError(user.id, new Error(error.message), {
        errorType: 'create_conversation_failed'
      })
      return NextResponse.json(
        { error: 'Failed to create conversation' },
        { status: 500 }
      )
    }

    // Log conversation creation
    await logConversationCreated(
      user.id,
      conversation.id,
      address,
      rule_type,
      resolvedJurisdiction
    )

    // Log feature usage (keep existing for compatibility)
    await supabase.from('feature_usage').insert({
      user_id: user.id,
      feature_type: 'chat_conversation_created',
      metadata: {
        conversation_id: conversation.id,
        address,
        rule_type,
        jurisdiction_name: resolvedJurisdiction
      }
    })

    return NextResponse.json({ conversation })

  } catch (error) {
    console.error('Create conversation error:', error)
    await logChatError(undefined, error instanceof Error ? error : new Error('Unknown error'), {
      errorType: 'create_conversation_exception'
    })
    return NextResponse.json(
      {
        error: 'Failed to create conversation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
