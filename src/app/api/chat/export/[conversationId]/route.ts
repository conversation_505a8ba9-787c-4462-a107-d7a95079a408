import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import jsPDF from 'jspdf'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  created_at: string
  metadata?: {
    citations?: Array<{
      title: string
      section?: string
      document_type?: string
      url?: string
      jurisdiction?: string
    }>
  }
}

interface ChatConversation {
  id: string
  address: string
  rule_type: string
  jurisdiction_name: string
  created_at: string
  user_id: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: { conversationId: string } }
) {
  try {
    const { conversationId } = params

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get conversation details
    const { data: conversation, error: conversationError } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('id', conversationId)
      .single()

    if (conversationError || !conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Check if user owns this conversation or is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.role === 'admin'
    const isOwner = conversation.user_id === user.id

    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Log admin access if admin is accessing
    if (isAdmin && !isOwner) {
      await supabase
        .from('admin_access_log')
        .insert({
          admin_user_id: user.id,
          accessed_conversation_id: conversationId,
          access_type: 'export',
          ip_address: request.ip || request.headers.get('x-forwarded-for'),
          user_agent: request.headers.get('user-agent')
        })
    }

    // Get all messages for this conversation
    const { data: messages, error: messagesError } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })

    if (messagesError) {
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      )
    }

    // Generate PDF
    const pdfBuffer = await generateChatPDF(conversation, messages || [])

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="chat-export-${conversationId.slice(0, 8)}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    })

  } catch (error) {
    console.error('Export error:', error)
    return NextResponse.json(
      { error: 'Failed to export chat' },
      { status: 500 }
    )
  }
}

async function generateChatPDF(
  conversation: ChatConversation,
  messages: ChatMessage[]
): Promise<Buffer> {
  const doc = new jsPDF()
  const pageWidth = doc.internal.pageSize.getWidth()
  const pageHeight = doc.internal.pageSize.getHeight()
  const margin = 20
  const maxWidth = pageWidth - (margin * 2)
  
  let yPosition = margin
  const lineHeight = 7
  const sectionSpacing = 10

  // Helper function to add text with word wrapping
  function addWrappedText(text: string, x: number, y: number, maxWidth: number, fontSize: number = 10): number {
    doc.setFontSize(fontSize)
    const lines = doc.splitTextToSize(text, maxWidth)
    
    for (let i = 0; i < lines.length; i++) {
      if (y + (i * lineHeight) > pageHeight - margin) {
        doc.addPage()
        y = margin
      }
      doc.text(lines[i], x, y + (i * lineHeight))
    }
    
    return y + (lines.length * lineHeight)
  }

  // Header
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('Ordrly Chat Export', margin, yPosition)
  yPosition += lineHeight + 5

  // Conversation details
  doc.setFontSize(12)
  doc.setFont('helvetica', 'normal')
  yPosition = addWrappedText(`Address: ${conversation.address}`, margin, yPosition, maxWidth, 12)
  yPosition += 3
  yPosition = addWrappedText(`Jurisdiction: ${conversation.jurisdiction_name}`, margin, yPosition, maxWidth, 12)
  yPosition += 3
  yPosition = addWrappedText(`Rule Type: ${conversation.rule_type}`, margin, yPosition, maxWidth, 12)
  yPosition += 3
  yPosition = addWrappedText(`Export Date: ${new Date().toLocaleDateString()}`, margin, yPosition, maxWidth, 12)
  yPosition += sectionSpacing

  // Disclaimer
  doc.setFontSize(10)
  doc.setFont('helvetica', 'italic')
  yPosition = addWrappedText(
    'Disclaimer: This chat transcript contains AI-generated responses based on building codes and regulations. Always verify information with local authorities before making decisions.',
    margin,
    yPosition,
    maxWidth,
    10
  )
  yPosition += sectionSpacing

  // Messages
  doc.setFont('helvetica', 'normal')
  let citationCounter = 1
  const citationMap = new Map<string, number>()

  for (const message of messages) {
    // Check if we need a new page
    if (yPosition > pageHeight - 50) {
      doc.addPage()
      yPosition = margin
    }

    // Message header
    doc.setFontSize(11)
    doc.setFont('helvetica', 'bold')
    const timestamp = new Date(message.created_at).toLocaleString()
    const header = message.role === 'user' ? `You (${timestamp}):` : `Ordrly AI (${timestamp}):`
    yPosition = addWrappedText(header, margin, yPosition, maxWidth, 11)
    yPosition += 3

    // Message content
    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    let content = message.content

    // Process citations for AI messages
    if (message.role === 'assistant' && message.metadata?.citations) {
      for (const citation of message.metadata.citations) {
        const citationKey = `${citation.title}-${citation.section || ''}`
        if (!citationMap.has(citationKey)) {
          citationMap.set(citationKey, citationCounter++)
        }
      }
    }

    yPosition = addWrappedText(content, margin + 10, yPosition, maxWidth - 10, 10)
    yPosition += sectionSpacing
  }

  // Citations section
  if (citationMap.size > 0) {
    // Check if we need a new page for citations
    if (yPosition > pageHeight - 100) {
      doc.addPage()
      yPosition = margin
    }

    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    yPosition = addWrappedText('Sources and Citations:', margin, yPosition, maxWidth, 12)
    yPosition += sectionSpacing

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')

    // Collect all unique citations from messages
    const allCitations = new Set<string>()
    for (const message of messages) {
      if (message.role === 'assistant' && message.metadata?.citations) {
        for (const citation of message.metadata.citations) {
          const citationText = `${citation.title}${citation.section ? ` - ${citation.section}` : ''}${citation.jurisdiction ? ` (${citation.jurisdiction})` : ''}`
          allCitations.add(citationText)
        }
      }
    }

    let citationNum = 1
    for (const citation of Array.from(allCitations)) {
      yPosition = addWrappedText(`[${citationNum}] ${citation}`, margin, yPosition, maxWidth, 10)
      yPosition += 5
      citationNum++
    }
  }

  // Footer
  const totalPages = (doc as any).getNumberOfPages()
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i)
    doc.setFontSize(8)
    doc.setFont('helvetica', 'normal')
    doc.text(
      `Page ${i} of ${totalPages} - Generated by Ordrly.ai`,
      pageWidth / 2,
      pageHeight - 10,
      { align: 'center' }
    )
  }

  return Buffer.from(doc.output('arraybuffer'))
}
