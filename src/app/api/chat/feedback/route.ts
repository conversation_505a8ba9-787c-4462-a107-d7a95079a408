import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { message_id, feedback_type, feedback_text } = await request.json()

    if (!message_id || !feedback_type) {
      return NextResponse.json(
        { error: 'message_id and feedback_type are required' },
        { status: 400 }
      )
    }

    if (!['thumbs_up', 'thumbs_down'].includes(feedback_type)) {
      return NextResponse.json(
        { error: 'feedback_type must be thumbs_up or thumbs_down' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the message exists and belongs to a conversation the user has access to
    const { data: message, error: messageError } = await supabase
      .from('chat_messages')
      .select(`
        id,
        conversation_id,
        role,
        chat_conversations!inner(user_id)
      `)
      .eq('id', message_id)
      .single()

    if (messageError || !message) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      )
    }

    // Check if user owns the conversation or is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.role === 'admin'
    const isOwner = (message.chat_conversations as any)?.user_id === user.id

    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Only allow feedback on assistant messages
    if (message.role !== 'assistant') {
      return NextResponse.json(
        { error: 'Feedback can only be provided on AI responses' },
        { status: 400 }
      )
    }

    // Insert or update feedback (upsert to handle duplicate feedback)
    const { data: feedback, error: feedbackError } = await supabase
      .from('chat_feedback')
      .upsert(
        {
          message_id,
          user_id: user.id,
          feedback_type,
          feedback_text: feedback_text || null,
        },
        {
          onConflict: 'message_id,user_id'
        }
      )
      .select()
      .single()

    if (feedbackError) {
      console.error('Feedback insert error:', feedbackError)
      return NextResponse.json(
        { error: 'Failed to save feedback' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      feedback
    })

  } catch (error) {
    console.error('Feedback API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const messageId = searchParams.get('message_id')

    if (!messageId) {
      return NextResponse.json(
        { error: 'message_id parameter is required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get existing feedback for this message by this user
    const { data: feedback, error } = await supabase
      .from('chat_feedback')
      .select('*')
      .eq('message_id', messageId)
      .eq('user_id', user.id)
      .maybeSingle()

    if (error) {
      console.error('Feedback fetch error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch feedback' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      feedback: feedback || null
    })

  } catch (error) {
    console.error('Feedback GET API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const messageId = searchParams.get('message_id')

    if (!messageId) {
      return NextResponse.json(
        { error: 'message_id parameter is required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Delete feedback for this message by this user
    const { error } = await supabase
      .from('chat_feedback')
      .delete()
      .eq('message_id', messageId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Feedback delete error:', error)
      return NextResponse.json(
        { error: 'Failed to delete feedback' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('Feedback DELETE API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
