import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import OpenAI from 'openai'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  created_at: string
  metadata?: {
    citations?: Array<{
      title: string
      section?: string
      document_type?: string
      url?: string
      jurisdiction?: string
    }>
  }
}

interface ChatConversation {
  id: string
  address: string
  rule_type: string
  jurisdiction_name: string
  created_at: string
  user_id: string
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(
  request: NextRequest,
  { params }: { params: { conversationId: string } }
) {
  try {
    const { conversationId } = params
    const { includeCitations = false } = await request.json()

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's tier and check access
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'free'

    if (!hasFeatureAccess(userTier, 'enableChat') || !isFeatureEnabled('CHAT_ENABLED')) {
      return NextResponse.json(
        {
          error: 'Note generation requires Pro tier or higher',
          upgradeRequired: true,
          suggestedTier: 'pro'
        },
        { status: 403 }
      )
    }

    // Get conversation details
    const { data: conversation, error: conversationError } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('id', conversationId)
      .single()

    if (conversationError || !conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Check if user owns this conversation
    if (conversation.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Get all messages for this conversation
    const { data: messages, error: messagesError } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })

    if (messagesError) {
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      )
    }

    if (!messages || messages.length === 0) {
      return NextResponse.json(
        { error: 'No messages found in conversation' },
        { status: 400 }
      )
    }

    // Generate appraisal note using AI
    const generatedNote = await generateAppraisalNote(
      conversation,
      messages,
      includeCitations
    )

    // Save the generated note to database
    const { data: savedNote, error: saveError } = await supabase
      .from('chat_generated_notes')
      .insert({
        conversation_id: conversationId,
        user_id: user.id,
        note_content: generatedNote,
        included_citations: includeCitations,
        generation_metadata: {
          message_count: messages.length,
          jurisdiction: conversation.jurisdiction_name,
          rule_type: conversation.rule_type,
          ai_model: 'gpt-4o-mini'
        }
      })
      .select()
      .single()

    if (saveError) {
      console.error('Error saving generated note:', saveError)
      // Continue without failing - note generation succeeded even if save failed
    }

    return NextResponse.json({
      note: generatedNote,
      conversation: {
        id: conversation.id,
        address: conversation.address,
        jurisdiction_name: conversation.jurisdiction_name
      },
      generated_at: new Date().toISOString(),
      saved: !saveError,
      note_id: savedNote?.id
    })

  } catch (error) {
    console.error('Note generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate appraisal note' },
      { status: 500 }
    )
  }
}

async function generateAppraisalNote(
  conversation: ChatConversation,
  messages: ChatMessage[],
  includeCitations: boolean
): Promise<string> {
  // Prepare conversation context for AI
  const conversationText = messages
    .map(msg => `${msg.role.toUpperCase()}: ${msg.content}`)
    .join('\n\n')

  // Extract citations from assistant messages
  const allCitations = messages
    .filter(msg => msg.role === 'assistant' && msg.metadata?.citations)
    .flatMap(msg => msg.metadata?.citations || [])

  // Create unique citations list
  const uniqueCitations = allCitations.reduce((acc, citation) => {
    const key = `${citation.title}-${citation.section || ''}`
    if (!acc.find(c => `${c.title}-${c.section || ''}` === key)) {
      acc.push(citation)
    }
    return acc
  }, [] as typeof allCitations)

  // Build prompt for appraisal note generation
  const prompt = `You are an expert real estate appraiser assistant. Based on the following compliance conversation about a property, generate a concise, professional note suitable for the "observations" or "conditions" section of a 1004 appraisal report.

PROPERTY INFORMATION:
Address: ${conversation.address}
Jurisdiction: ${conversation.jurisdiction_name}

CONVERSATION CONTENT:
${conversationText}

INSTRUCTIONS:
1. Create a concise, professional note (2-5 sentences maximum)
2. Focus on compliance findings, requirements, or concerns relevant to property valuation
3. Use formal appraisal language appropriate for a 1004 form
4. Highlight any code compliance issues, permit requirements, or safety concerns
5. ${includeCitations ? 'Include minimal, clean references to sources when relevant' : 'Do not include citations or reference numbers'}
6. If no significant compliance issues were found, note that compliance appears satisfactory

FORMAT: Return only the note text, no additional commentary or formatting.

EXAMPLE STYLE: "Property's smoke alarm installation meets 2018 IRC requirements with hardwired detectors on each floor. Deck guardrail height of 34 inches is below the required 36-inch minimum per local building code, which may present a safety concern requiring attention."

Generate the appraisal note:`

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are an expert real estate appraiser assistant specializing in compliance documentation for 1004 appraisal forms.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 300,
      temperature: 0.3, // Low temperature for consistent, factual output
    })

    const generatedNote = completion.choices[0]?.message?.content?.trim()

    if (!generatedNote) {
      throw new Error('No note generated by AI')
    }

    return generatedNote

  } catch (error) {
    console.error('OpenAI API error:', error)
    throw new Error('Failed to generate note using AI')
  }
}
