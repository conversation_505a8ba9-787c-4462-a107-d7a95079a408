import { NextRequest, NextResponse } from 'next/server'
import { createServerClient as createSSRServerClient } from '@supabase/ssr'

/**
 * 💬 CHAT MESSAGES API
 *
 * Handles chat message creation and retrieval, integrates with municipal research API
 * Flow: Chat UI → /api/chat/messages → /api/research → Municipal API
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const conversationId = searchParams.get('conversation_id')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      )
    }

    // Create Supabase client with proper cookie handling (same as middleware)
    const supabase = createSSRServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // We don't need to set cookies in GET requests
          },
        },
      }
    )

    // More robust auth check
    let user = null
    try {
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
      if (authError) {
        console.error('Auth error in messages GET:', authError)
      }
      user = authUser
    } catch (error) {
      console.error('Exception getting user in messages GET:', error)
    }

    if (!user) {
      console.log('❌ No user found in messages GET')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('✅ User authenticated in messages GET:', user.email)

    // Verify conversation belongs to user
    const { data: conversation, error: convError } = await supabase
      .from('chat_conversations')
      .select('id')
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .single()

    if (convError || !conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 })
    }

    // Get messages with pagination
    const offset = (page - 1) * limit
    const { data: messages, error: messagesError, count } = await supabase
      .from('chat_messages')
      .select('*', { count: 'exact' })
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1)

    if (messagesError) {
      throw new Error('Failed to fetch messages')
    }

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      success: true,
      messages: messages || [],
      pagination: {
        currentPage: page,
        totalPages,
        totalMessages: count || 0,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    })

  } catch (error) {
    console.error('Get messages error:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch messages',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { conversation_id, content } = await request.json()

    if (!conversation_id || !content) {
      return NextResponse.json(
        { error: 'Conversation ID and content are required' },
        { status: 400 }
      )
    }

    // Create Supabase client with proper cookie handling (same as middleware)
    const supabase = createSSRServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // We don't need to set cookies in POST requests for this endpoint
          },
        },
      }
    )

    // More robust auth check
    let user = null
    try {
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
      if (authError) {
        console.error('Auth error in messages POST:', authError)
      }
      user = authUser
    } catch (error) {
      console.error('Exception getting user in messages POST:', error)
    }

    if (!user) {
      console.log('❌ No user found in messages POST')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('✅ User authenticated in messages POST:', user.email)

    // Get conversation details for address context
    const { data: conversation, error: convError } = await supabase
      .from('chat_conversations')
      .select('address, jurisdiction_name, user_id')
      .eq('id', conversation_id)
      .eq('user_id', user.id)
      .single()

    if (convError || !conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 })
    }

    // TODO: Add usage limit checking for chat messages based on subscription tier
    // For now, we'll count messages directly from the database in the account page

    // Save user message
    const { data: userMessage, error: userMsgError } = await supabase
      .from('chat_messages')
      .insert({
        conversation_id,
        role: 'user',
        content: content.trim(),
        metadata: {}
      })
      .select()
      .single()

    if (userMsgError) {
      throw new Error('Failed to save user message')
    }

    // Call municipal research API
    const researchResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/research`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('cookie') || ''
      },
      body: JSON.stringify({
        query: content.trim(),
        address: conversation.address,
        context: {
          sessionId: conversation_id,
          conversationHistory: [], // TODO: Add recent message history
          responseType: 'chat'
        }
      })
    })

    if (!researchResponse.ok) {
      const errorData = await researchResponse.json().catch(() => ({ error: 'Unknown error' }))
      console.error('🚨 Research API failed:', errorData)

      // Provide a fallback response instead of completely failing
      const fallbackResponse = {
        answer: "I'm currently experiencing technical difficulties with the municipal research system. Please try your question again in a few moments, or contact support if the issue persists.",
        confidence: 0,
        sources: [],
        metadata: {
          jurisdiction: conversation.jurisdiction_name || 'Unknown',
          processingTimeMs: 0,
          usedCache: false,
          fallback: true,
          error: errorData.error || 'Research API unavailable'
        },
        researchQuality: 'unavailable'
      }

      // Save AI response with fallback message
      const { data: aiMessage, error: aiMsgError } = await supabase
        .from('chat_messages')
        .insert({
          conversation_id,
          role: 'assistant',
          content: fallbackResponse.answer,
          metadata: {
            confidence: fallbackResponse.confidence,
            sources: [],
            citations: [],
            jurisdiction: fallbackResponse.metadata.jurisdiction,
            processingTimeMs: fallbackResponse.metadata.processingTimeMs,
            usedCache: fallbackResponse.metadata.usedCache,
            municipal_api: false,
            research_quality: fallbackResponse.researchQuality,
            fallback: true,
            error: fallbackResponse.metadata.error
          }
        })
        .select()
        .single()

      if (aiMsgError) {
        throw new Error('Failed to save fallback message')
      }

      return NextResponse.json({
        success: true,
        userMessage,
        aiMessage,
        research: {
          confidence: fallbackResponse.confidence,
          sources: fallbackResponse.sources,
          jurisdiction: fallbackResponse.metadata.jurisdiction,
          fallback: true
        }
      })
    }

    const researchData = await researchResponse.json()

    // Transform sources for chat UI compatibility
    const chatSources = (researchData.sources || []).map((source: any, index: number) => {
      // Handle both string URLs and object sources
      if (typeof source === 'string') {
        // Extract domain name for title
        const url = source
        const domain = url.match(/https?:\/\/([^\/]+)/)?.[1] || 'Municipal Source'
        const title = domain.replace('www.', '').replace('.gov', '').replace('.com', '')

        return {
          title: `${title} - Municipal Document`,
          section: '',
          document_type: 'ordinance',
          url: url,
          jurisdiction: researchData.metadata?.jurisdiction || conversation.jurisdiction_name,
          verified: true,
          authority: 'primary'
        }
      } else {
        // Handle object sources (for backward compatibility)
        return {
          title: source.title || 'Municipal Source',
          section: source.section || '',
          document_type: 'ordinance',
          url: source.url,
          jurisdiction: researchData.metadata?.jurisdiction || conversation.jurisdiction_name,
          verified: source.verified || false,
          authority: source.authority || 'primary'
        }
      }
    })

    // Save AI response
    const { data: aiMessage, error: aiMsgError } = await supabase
      .from('chat_messages')
      .insert({
        conversation_id,
        role: 'assistant',
        content: researchData.answer,
        metadata: {
          confidence: researchData.confidence,
          sources: chatSources,
          citations: chatSources, // For compatibility with existing UI
          jurisdiction: researchData.metadata?.jurisdiction,
          processingTimeMs: researchData.metadata?.processingTimeMs,
          usedCache: researchData.metadata?.usedCache,
          municipal_api: true,
          research_quality: researchData.researchQuality
        }
      })
      .select()
      .single()

    if (aiMsgError) {
      throw new Error('Failed to save AI message')
    }

    return NextResponse.json({
      success: true,
      userMessage,
      aiMessage,
      research: {
        confidence: researchData.confidence,
        sources: researchData.sources,
        jurisdiction: researchData.metadata?.jurisdiction
      }
    })

  } catch (error) {
    console.error('Chat messages error:', error)
    return NextResponse.json(
      {
        error: 'Failed to process message',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
