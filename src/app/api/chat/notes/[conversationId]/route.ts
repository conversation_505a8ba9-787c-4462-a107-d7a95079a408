import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'

export async function GET(
  request: NextRequest,
  { params }: { params: { conversationId: string } }
) {
  try {
    const { conversationId } = params

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's tier and check access
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'free'

    if (!hasFeatureAccess(userTier, 'enableChat') || !isFeatureEnabled('CHAT_ENABLED')) {
      return NextResponse.json(
        {
          error: 'Note access requires Pro tier or higher',
          upgradeRequired: true,
          suggestedTier: 'pro'
        },
        { status: 403 }
      )
    }

    // Verify conversation exists and user has access
    const { data: conversation, error: conversationError } = await supabase
      .from('chat_conversations')
      .select('id, user_id')
      .eq('id', conversationId)
      .single()

    if (conversationError || !conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    if (conversation.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Get all generated notes for this conversation
    const { data: notes, error: notesError } = await supabase
      .from('chat_generated_notes')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: false })

    if (notesError) {
      console.error('Error fetching notes:', notesError)
      return NextResponse.json(
        { error: 'Failed to fetch notes' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      notes: notes || [],
      conversation_id: conversationId
    })

  } catch (error) {
    console.error('Notes API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { conversationId: string } }
) {
  try {
    const { conversationId } = params
    const { noteId } = await request.json()

    if (!conversationId || !noteId) {
      return NextResponse.json(
        { error: 'Conversation ID and Note ID are required' },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Delete the note (RLS will ensure user can only delete their own notes)
    const { error: deleteError } = await supabase
      .from('chat_generated_notes')
      .delete()
      .eq('id', noteId)
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting note:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete note' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Note deleted successfully'
    })

  } catch (error) {
    console.error('Delete note API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
