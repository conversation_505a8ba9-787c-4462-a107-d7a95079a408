import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import type { PromptTemplate, PromptTemplatesResponse } from '@/lib/types/chat'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const activeOnly = searchParams.get('active_only') !== 'false' // Default to true

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's tier
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'trial'

    // Check if user has access to chat feature (and thus prompt templates)
    if (!hasFeatureAccess(userTier, 'enableChat') || !isFeatureEnabled('CHAT_ENABLED')) {
      return NextResponse.json(
        {
          error: 'Prompt templates require Pro tier or higher',
          upgradeRequired: true,
          suggestedTier: 'pro'
        },
        { status: 403 }
      )
    }

    // Build query for prompt templates
    let query = supabase
      .from('prompt_templates')
      .select('*')
      .order('category', { ascending: true })
      .order('name', { ascending: true })

    // Filter by active status
    if (activeOnly) {
      query = query.eq('is_active', true)
    }

    // Filter by category if specified
    if (category) {
      query = query.eq('category', category)
    }

    const { data: templates, error: templatesError } = await query

    if (templatesError) {
      console.error('Error fetching prompt templates:', templatesError)
      return NextResponse.json(
        { error: 'Failed to fetch prompt templates' },
        { status: 500 }
      )
    }

    // Get unique categories for response
    const categories = [...new Set(templates?.map(t => t.category) || [])]

    const response: PromptTemplatesResponse = {
      templates: templates || [],
      categories
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Prompt templates API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
