import { NextRequest, NextResponse } from 'next/server'

/**
 * 🔄 COMPATIBILITY LAYER FOR SEARCH PAGE
 * 
 * This endpoint provides backward compatibility for the existing search page
 * while using the new bulletproof research API under the hood.
 * 
 * It transforms the new research API response format to match what the
 * search page expects, ensuring no breaking changes.
 */

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json()
    const { lat, lng, address, userQuery, enhanceQuality = false } = body

    // Call the new bulletproof research API
    const researchResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/research`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward authorization header if present
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
      body: JSON.stringify({
        query: userQuery,
        lat,
        lng,
        address,
        context: {
          type: 'search',
          requireHighConfidence: enhanceQuality
        }
      })
    })

    if (!researchResponse.ok) {
      const errorData = await researchResponse.json()
      return NextResponse.json(errorData, { status: researchResponse.status })
    }

    const researchData = await researchResponse.json()

    // Transform the new API response to match the old format expected by search page
    const compatibilityResponse = {
      summary: {
        jurisdiction_name: researchData.metadata?.jurisdiction || 'Unknown',
        jurisdiction_level: 'municipal', // Default value
        summary: researchData.answer || '',
        citations: researchData.sources?.map((source: any) => ({
          title: source.title,
          regulation_text: source.excerpt || source.content || '',
          url: source.url || '',
          verified: source.verified || false
        })) || [],
        confidence_score: researchData.confidence || 0,
        source_url: researchData.sources?.[0]?.url || '',
        cached: researchData.metadata?.usedCache || false,
        triggered_research: researchData.metadata?.triggeredResearch || false,
        data_source: researchData.metadata?.usedCache ? 'rag' : 'real_time',
        last_updated: new Date().toISOString(),
        tier_used: researchData.metadata?.tier || 'free',
        sourceGrounding: {
          isFullyGrounded: researchData.sources?.length > 0,
          groundingScore: researchData.confidence || 0,
          sourceUtilization: 1.0,
          ungroundedClaims: [],
          recommendations: []
        }
      },
      // Include research quality metrics if available
      researchQuality: researchData.researchQuality,
      metadata: researchData.metadata
    }

    return NextResponse.json(compatibilityResponse)

  } catch (error) {
    console.error('❌ [COMPATIBILITY] Error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
