import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { analyzeOrdinanceContent, combineDocumentSources, OrdinanceAnalysisInput } from '@/lib/ordinance-analysis'
import { analyzeOrdinanceWithTier } from '@/lib/enhanced-ordinance-analysis'
import { getTierConfig, isFeatureEnabled } from '@/lib/tier-config'
import { trackUsage, checkUsageLimit } from '@/lib/usage-tracking'
import type { SupabaseClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const { lat, lng, ruleType, address } = await request.json()

    if (!ruleType) {
      return NextResponse.json({ error: 'Rule type is required' }, { status: 400 })
    }

    // Check authentication and usage limits
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    // Get user's tier for enhanced features
    let userTier = 'trial'
    if (user) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_tier')
        .eq('id', user.id)
        .single()

      userTier = profile?.subscription_tier || 'trial'
    }

    if (user) {
      // Check usage limits for authenticated users
      const usageStatus = await checkUsageLimit(user.id)

      if (!usageStatus.canProceed) {
        return NextResponse.json(
          {
            error: 'Usage limit exceeded',
            message: usageStatus.message,
            usageStatus,
            upgradeRequired: true,
          },
          { status: 429 }
        )
      }

      // Track usage before processing
      await trackUsage(user.id)

      // Check if user needs usage alert after tracking (Day 2 Marketing Growth Engine)
      if (userTier === 'trial') {
        try {
          const alertResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/usage-alerts`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ user_id: user.id })
          })
          // Don't block search if alert check fails
          if (!alertResponse.ok) {
            console.warn('Usage alert check failed, continuing with search')
          }
        } catch (alertError) {
          console.warn('Usage alert check error:', alertError)
          // Don't block search if alert fails
        }
      }
    }

    let coordinates
    let jurisdiction: { name: string; level: string; state?: string; state_code?: string } | null = null

    // Handle both coordinate and address inputs
    if (lat && lng) {
      coordinates = { lat: parseFloat(lat), lng: parseFloat(lng) }
      console.log(`📍 Using provided coordinates: ${coordinates.lat}, ${coordinates.lng}`)
    } else if (address && typeof address === 'string') {
      console.log(`🏠 Getting coordinates from address: "${address}"`)
      coordinates = await getCoordinatesFromAddress(address)
      if (!coordinates) {
        return NextResponse.json({
          error: 'Could not get coordinates from address'
        }, { status: 400 })
      }
    } else {
      return NextResponse.json({
        error: 'Either coordinates (lat/lng) or address is required'
      }, { status: 400 })
    }

    // Use the working jurisdiction lookup from test-jurisdiction
    console.log(`🔍 Looking up jurisdiction for coordinates: ${coordinates.lat}, ${coordinates.lng}`)
    jurisdiction = await getJurisdictionFromGeoapify(coordinates.lat, coordinates.lng)

    if (!jurisdiction) {
      return NextResponse.json({
        error: 'Could not determine jurisdiction from coordinates',
        coordinates
      }, { status: 400 })
    }

    // Try to get cached ordinance data first
    const cachedData = await getCachedOrdinanceData(jurisdiction.name, ruleType)

    if (cachedData) {
      console.log(`✓ Cache hit for ${jurisdiction.name} - ${ruleType}`)

      // Enhance cached data with tier-specific features
      const enhancedCachedData = await enhanceCachedData(cachedData, userTier, jurisdiction, ruleType, supabase)

      const cachedResponseData = {
        summary: {
          jurisdiction_name: jurisdiction.name,
          jurisdiction_level: jurisdiction.level,
          summary: cachedData.content || cachedData.summary, // Handle both content and summary fields
          citations: cachedData.citations,
          confidence_score: cachedData.confidence_score,
          source_url: cachedData.source_url,
          zoning_info: cachedData.zoning_info,
          permit_info: cachedData.permit_info,
          regulation_callout: cachedData.regulation_callout,
          specific_requirements: cachedData.specific_requirements,
          cached: true,
          last_updated: cachedData.updated_at,
          // Enhanced tier-based features
          red_flags: enhancedCachedData.red_flags,
          clauses: enhancedCachedData.clauses,
          tier_used: userTier,
          sources_analyzed: enhancedCachedData.sources_analyzed
        }
      }

      // Save search history for authenticated users (Epic 9.1.1)
      if (user) {
        await saveSearchHistory(user.id, address || `${coordinates.lat}, ${coordinates.lng}`, ruleType, jurisdiction.name, coordinates, cachedResponseData.summary)

        // Track search for email automation if user is on trial tier (Epic 4.4.2)
        if (userTier === 'trial') {
          try {
            await supabase.rpc('track_search_without_upgrade', {
              user_id_param: user.id,
              rule_type_param: ruleType,
              address_param: address || `${coordinates.lat}, ${coordinates.lng}`
            })

            // Schedule abandonment nudge email
            await supabase.rpc('schedule_abandonment_nudge', {
              user_id_param: user.id,
              rule_type_param: ruleType,
              address_param: address || `${coordinates.lat}, ${coordinates.lng}`,
              delay_hours: 36 // 36 hours delay
            })
          } catch (emailError) {
            console.error('Error tracking search for email automation:', emailError)
            // Don't fail the search if email tracking fails
          }
        }
      }

      return NextResponse.json(cachedResponseData)
    }

    // Cache miss - research ordinance in real-time with tier enhancements
    console.log(`⚡ Cache miss for ${jurisdiction.name} - ${ruleType}. Starting enhanced real-time research...`)

    // Add timeout wrapper for research process
    const researchedData = await Promise.race([
      researchOrdinanceRealTimeWithTier(jurisdiction, ruleType, userTier, undefined),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Research timeout exceeded')), 55000) // 55 second timeout
      )
    ]) as Awaited<ReturnType<typeof researchOrdinanceRealTimeWithTier>>

    // Cache the results for future use
    await cacheOrdinanceData(jurisdiction.name, ruleType, researchedData)

    const responseData = {
      summary: {
        jurisdiction_name: jurisdiction.name,
        jurisdiction_level: jurisdiction.level,
        summary: researchedData.content,
        citations: researchedData.citations,
        confidence_score: researchedData.confidence_score,
        source_url: researchedData.sourceUrl,
        zoning_info: researchedData.zoningInfo,
        permit_info: researchedData.permitInfo,
        regulation_callout: researchedData.regulation_callout,
        specific_requirements: researchedData.specific_requirements,
        cached: false,
        research_time_ms: researchedData.research_time_ms,
        // Enhanced tier-based features
        red_flags: researchedData.red_flags,
        clauses: researchedData.clauses,
        tier_used: userTier,
        sources_analyzed: researchedData.sources_analyzed
      }
    }

    // Save search history for authenticated users (Epic 9.1.1)
    if (user) {
      await saveSearchHistory(user.id, address || `${coordinates.lat}, ${coordinates.lng}`, ruleType, jurisdiction.name, coordinates, responseData.summary)

      // Track search for email automation if user is on trial tier (Epic 4.4.2)
      if (userTier === 'trial') {
        try {
          await supabase.rpc('track_search_without_upgrade', {
            user_id_param: user.id,
            rule_type_param: ruleType,
            address_param: address || `${coordinates.lat}, ${coordinates.lng}`
          })

          // Schedule abandonment nudge email
          await supabase.rpc('schedule_abandonment_nudge', {
            user_id_param: user.id,
            rule_type_param: ruleType,
            address_param: address || `${coordinates.lat}, ${coordinates.lng}`,
            delay_hours: 36 // 36 hours delay
          })
        } catch (emailError) {
          console.error('Error tracking search for email automation:', emailError)
          // Don't fail the search if email tracking fails
        }
      }
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Summary API error:', error)

    // Handle timeout errors specifically
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json({
        error: 'Research timeout - this ordinance type requires more time to analyze. Please try again.',
        timeout: true
      }, { status: 408 }) // Request Timeout
    }

    return NextResponse.json({
      error: 'Failed to generate compliance summary'
    }, { status: 500 })
  }
}

/**
 * Enhance cached data with tier-specific features
 */
async function enhanceCachedData(cachedData: Record<string, unknown>, userTier: string, jurisdiction: { name: string; state?: string }, ruleType: string, supabase: SupabaseClient) {
  const tierConfig = getTierConfig(userTier as any)
  const enhanced: { red_flags: unknown[]; clauses: unknown[]; sources_analyzed: number } = {
    red_flags: [],
    clauses: [],
    sources_analyzed: 3 // Default for cached data
  }

  // Add red flags for Pro+ tiers
  if (tierConfig.enableRedFlags && isFeatureEnabled('RED_FLAGS_ENABLED')) {
    try {
      const { data: redFlags } = await supabase
        .from('red_flags')
        .select('*')
        .eq('jurisdiction_name', jurisdiction.name)
        .eq('rule_type', ruleType)

      enhanced.red_flags = redFlags || []
    } catch (error) {
      console.error('Failed to fetch cached red flags:', error)
    }
  }

  // Add detailed clauses for Appraiser tier
  if (tierConfig.enableClauseBrowser && isFeatureEnabled('CLAUSE_BROWSER_ENABLED')) {
    try {
      const { data: clauses } = await supabase
        .from('ordinance_clauses')
        .select('*')
        .eq('jurisdiction_name', jurisdiction.name)
        .eq('rule_type', ruleType)

      enhanced.clauses = clauses || []
    } catch (error) {
      console.error('Failed to fetch cached clauses:', error)
    }
  }

  return enhanced
}

// Working geocoding functions from test-jurisdiction
async function getCoordinatesFromAddress(address: string) {
  try {
    // Use Geoapify for geocoding (same as address autocomplete)
    if (!process.env.GEOAPIFY_API_KEY) {
      throw new Error('Geoapify API key not configured')
    }

    const response = await fetch(
      `https://api.geoapify.com/v1/geocode/search?text=${encodeURIComponent(address)}&apiKey=${process.env.GEOAPIFY_API_KEY}`
    )

    if (!response.ok) {
      throw new Error(`Geoapify error: ${response.status}`)
    }

    const data = await response.json()

    if (data.features && data.features.length > 0) {
      const feature = data.features[0]
      return {
        lat: feature.geometry.coordinates[1],
        lng: feature.geometry.coordinates[0]
      }
    }

    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}

async function getJurisdictionFromGeoapify(lat: number, lng: number) {
  try {
    // Try Geoapify reverse geocoding first (since we know it works)
    if (process.env.GEOAPIFY_API_KEY) {
      console.log(`🌍 Geoapify Reverse Geocoding: ${lat}, ${lng}`)
      const response = await fetch(
        `https://api.geoapify.com/v1/geocode/reverse?lat=${lat}&lon=${lng}&apiKey=${process.env.GEOAPIFY_API_KEY}`
      )

      if (response.ok) {
        const data = await response.json()
        console.log(`📊 Geoapify returned ${data.features?.length || 0} results`)

        if (data.features && data.features.length > 0) {
          const feature = data.features[0]
          const props = feature.properties

          console.log(`📍 Geoapify result:`, JSON.stringify(props, null, 2))

          // Extract jurisdiction from Geoapify response with township support
          let jurisdiction = null
          let level = 'city'

          // Priority order: township > city > town > village > county
          // Special handling for Michigan townships and other jurisdictions
          if (props.district && props.district.toLowerCase().includes('township')) {
            jurisdiction = props.district
            level = 'township'
          } else if (props.district && props.county && props.district !== props.city) {
            // Check if district might be a township (common in Michigan)
            // Georgetown Township is a common case where district="Georgetown" but it's actually Georgetown Township
            if (props.state_code === 'MI' && props.district && !props.district.toLowerCase().includes('township')) {
              jurisdiction = `${props.district} Township`
              level = 'township'
            } else {
              jurisdiction = props.district
              level = 'district'
            }
          } else if (props.city) {
            jurisdiction = props.city
            level = 'city'
          } else if (props.town) {
            jurisdiction = props.town
            level = 'town'
          } else if (props.village) {
            jurisdiction = props.village
            level = 'village'
          } else if (props.county) {
            jurisdiction = props.county
            level = 'county'
          }

          const state = props.state
          const stateCode = props.state_code

          if (jurisdiction && state) {
            console.log(`✅ Geoapify found jurisdiction: ${jurisdiction}, ${state} (${level})`)
            return {
              name: jurisdiction,
              level: level,
              state: state,
              state_code: stateCode
            }
          }
        }
      }
    }

    // Fallback to Google if Geoapify fails
    const googleGeocodingKey = process.env.GOOGLE_GEOCODING_API_KEY || process.env.GOOGLE_SEARCH_API_KEY
    if (!googleGeocodingKey) {
      console.error('❌ No geocoding APIs available')
      return null
    }

    console.log(`🌍 Trying Google Reverse Geocoding: ${lat}, ${lng}`)
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${googleGeocodingKey}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.status === 'REQUEST_DENIED') {
      console.error(`❌ Google Geocoding API not enabled: ${data.error_message}`)
      return null
    }

    if (data.results && data.results.length > 0) {
      const result = data.results[0]
      console.log(`📍 Google formatted address: ${result.formatted_address}`)

      // Simple extraction from Google
      let city = null
      let state = null
      let stateCode = null

      for (const component of result.address_components) {
        if (component.types.includes('locality') || component.types.includes('administrative_area_level_3')) {
          city = component.long_name
        } else if (component.types.includes('administrative_area_level_1')) {
          state = component.long_name
          stateCode = component.short_name
        }
      }

      if (city && state) {
        console.log(`✅ Google found jurisdiction: ${city}, ${state}`)
        return {
          name: city,
          level: 'city',
          state: state,
          state_code: stateCode
        }
      }
    }

    return null
  } catch (error) {
    console.error('Jurisdiction lookup failed:', error)
    return null
  }
}

// Removed complex boundary discovery - not needed for ordinance research
// PostGIS will be populated later when we need geographic boundaries

















// In-memory cache for demonstration (replace with database when table is created)
const memoryCache = new Map<string, { data: Record<string, unknown>, expires: number }>()

// Initialize empty cache - no hardcoded data
const initializeCache = () => {
  console.log('✓ Initialized empty memory cache - all data will be researched dynamically')
}

// Initialize cache on module load
initializeCache()

// Cache management functions
async function getCachedOrdinanceData(jurisdictionName: string, ruleType: string) {
  try {
    console.log(`🔍 Looking for cached data: ${jurisdictionName} - ${ruleType}`)

    // First try in-memory cache
    const cacheKey = `${jurisdictionName}:${ruleType}`
    const cached = memoryCache.get(cacheKey)

    if (cached && cached.expires > Date.now()) {
      console.log(`✅ Memory cache hit: Found data for ${jurisdictionName} - ${ruleType}`)
      return cached.data
    }

    // Try database cache
    const supabase = await createServerClient()

    const { data, error } = await supabase
      .from('ordinance_cache')
      .select('*')
      .eq('jurisdiction_name', jurisdictionName)
      .eq('rule_type', ruleType)
      .gt('expires_at', new Date().toISOString())
      .single()

    console.log(`Database cache lookup for "${jurisdictionName}" - "${ruleType}":`, {
      hasData: !!data,
      error: error?.message,
      dataContent: data ? Object.keys(data) : null
    })

    if (error || !data) {
      console.log(`❌ Cache miss: ${error?.message || 'No data found'}`)
      return null
    }

    console.log(`✅ Database cache hit: Found data for ${jurisdictionName} - ${ruleType}`)

    // Store in memory cache for faster future access
    memoryCache.set(cacheKey, {
      data: data.ordinance_data,
      expires: Date.now() + 5 * 60 * 1000 // 5 minutes
    })

    return data.ordinance_data
  } catch (error) {
    console.error('Cache lookup error:', error)
    return null
  }
}

async function cacheOrdinanceData(jurisdictionName: string, ruleType: string, ordinanceData: Record<string, unknown>) {
  try {
    // Store in memory cache immediately
    const cacheKey = `${jurisdictionName}:${ruleType}`
    const cacheData = {
      content: ordinanceData.content,
      citations: ordinanceData.citations,
      confidence_score: ordinanceData.confidence_score,
      source_url: ordinanceData.sourceUrl,
      zoning_info: ordinanceData.zoningInfo,
      permit_info: ordinanceData.permitInfo,
      regulation_callout: ordinanceData.regulation_callout,
      specific_requirements: ordinanceData.specific_requirements
    }

    memoryCache.set(cacheKey, {
      data: cacheData,
      expires: Date.now() + 5 * 60 * 1000 // 5 minutes
    })

    console.log(`✓ Stored in memory cache: ${jurisdictionName} - ${ruleType}`)

    // Also try to store in database (will fail gracefully if table doesn't exist)
    const supabase = await createServerClient()

    const cacheEntry = {
      jurisdiction_name: jurisdictionName,
      rule_type: ruleType,
      ordinance_data: cacheData,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
      updated_at: new Date().toISOString()
    }

    const { error } = await supabase
      .from('ordinance_cache')
      .upsert(cacheEntry, {
        onConflict: 'jurisdiction_name,rule_type'
      })

    if (error) {
      console.log(`⚠ Database cache storage failed (table may not exist): ${error.message}`)
    } else {
      console.log(`✓ Cached ordinance data in database for ${jurisdictionName} - ${ruleType}`)
    }
  } catch (error) {
    console.error('Cache storage error:', error)
  }
}

// Source storage functions for top 5 search results
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function getCachedSources(jurisdictionName: string, ruleType: string) {
  try {
    const supabase = await createServerClient()

    const { data, error } = await supabase
      .from('ordinance_sources')
      .select('*')
      .eq('jurisdiction_name', jurisdictionName)
      .eq('rule_type', ruleType)
      .gt('last_updated', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Within 24 hours
      .order('rank', { ascending: true })

    if (error) {
      console.log(`⚠ Source cache lookup failed: ${error.message}`)
      return null
    }

    return data
  } catch (error) {
    console.error('Source cache lookup error:', error)
    return null
  }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function storeSources(jurisdictionName: string, ruleType: string, searchQuery: string, searchResults: Array<{ title: string; link: string; snippet?: string }>) {
  try {
    const supabase = await createServerClient()

    // First, delete existing sources for this jurisdiction/rule type
    await supabase
      .from('ordinance_sources')
      .delete()
      .eq('jurisdiction_name', jurisdictionName)
      .eq('rule_type', ruleType)

    // Store new sources
    const sourcesToStore = searchResults.slice(0, 5).map((result, index) => ({
      jurisdiction_name: jurisdictionName,
      rule_type: ruleType,
      search_query: searchQuery,
      source_url: result.link,
      source_title: result.title,
      source_content: result.snippet || '',
      source_type: result.link.toLowerCase().includes('.pdf') ? 'pdf' : 'html',
      rank: index + 1,
      last_updated: new Date().toISOString()
    }))

    const { error } = await supabase
      .from('ordinance_sources')
      .insert(sourcesToStore)

    if (error) {
      console.log(`⚠ Source storage failed: ${error.message}`)
    } else {
      console.log(`✓ Stored ${sourcesToStore.length} sources for ${jurisdictionName} - ${ruleType}`)
    }
  } catch (error) {
    console.error('Source storage error:', error)
  }
}

// Enhanced real-time ordinance research with tier-based features
async function researchOrdinanceRealTimeWithTier(
  jurisdiction: { name: string; state?: string; state_code?: string },
  ruleType: string,
  userTier: string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  customDescription?: string // Unused parameter - reserved for future custom project analysis
) {
  const startTime = Date.now()

  // Complex rule types that may need more time
  const complexRuleTypes = ['addition', 'garage', 'other']
  const isComplexRule = complexRuleTypes.includes(ruleType)
  const timeoutMs = isComplexRule ? 80000 : 45000 // 80s for complex, 45s for simple - increased for reliability

  try {
    console.log(`🔍 Researching ${ruleType} ordinances for ${jurisdiction.name} (${userTier} tier)...`)
    console.log(`🏛️ Jurisdiction: ${jurisdiction.name}, State: ${jurisdiction.state || jurisdiction.state_code}`)
    console.log(`⏱️ Timeout: ${timeoutMs}ms (${isComplexRule ? 'complex' : 'simple'} rule type)`)

    // Step 1: Simple Google search for ordinance documents
    const searchResults = await searchOrdinanceDocuments(jurisdiction.name, jurisdiction.state || jurisdiction.state_code || '', ruleType)
    const searchQuery = `${jurisdiction.name} ${jurisdiction.state || jurisdiction.state_code} ${ruleType} ordinance`

    // Step 2: Fetch content from search results (tier-based limits)
    const tierConfig = getTierConfig(userTier as any)
    let limitedResults = searchResults.slice(0, tierConfig.maxSources)
    let documentSources = await fetchAllDocumentContent(limitedResults, searchQuery)

    // Step 3: Enhanced analysis with tier-specific features
    const documentText = combineDocumentSources(documentSources)

    const analysisInput: OrdinanceAnalysisInput = {
      region: `${jurisdiction.name}, ${jurisdiction.state || jurisdiction.state_code}`,
      project_type: ruleType,
      document_text: documentText
    }

    // Use enhanced analysis for Pro+ tiers, regular analysis for trial
    let sharedAnalysis
    if (userTier !== 'trial' && isFeatureEnabled('EPIC6_ENABLED')) {
      sharedAnalysis = await analyzeOrdinanceWithTier(analysisInput, userTier as any, {
        enableLogging: true,
        fallbackOnError: true,
        maxSources: tierConfig.maxSources
      })
    } else {
      sharedAnalysis = await analyzeOrdinanceContent(analysisInput, {
        enableLogging: true,
        fallbackOnError: true
      })
    }

    // Enhanced retry logic with geographic validation awareness
    const confidenceScore = sharedAnalysis.confidence_score ?? 0
    const hasGeographicIssues = sharedAnalysis.geographic_validation?.sources_validated === false ||
                               (sharedAnalysis.geographic_validation?.rejected_sources?.length ?? 0) > 0

    // Require higher confidence (90%) for geographic accuracy, retry if low confidence or geographic issues
    if ((confidenceScore < 0.9 || hasGeographicIssues) && searchResults.length > limitedResults.length) {
      const issueType = hasGeographicIssues ? 'geographic validation issues' : 'low confidence'
      console.log(`🔄 ${issueType} (${Math.round(confidenceScore * 100)}%), retrying with more targeted sources...`)

      // Use up to 10 sources for retry (or all available if less)
      const retrySourceCount = Math.min(10, searchResults.length)
      const retryResults = searchResults.slice(0, retrySourceCount)
      const retryDocumentSources = await fetchAllDocumentContent(retryResults, searchQuery)
      const retryDocumentText = combineDocumentSources(retryDocumentSources)

      const retryAnalysisInput: OrdinanceAnalysisInput = {
        region: `${jurisdiction.name}, ${jurisdiction.state || jurisdiction.state_code}`,
        project_type: ruleType,
        document_text: retryDocumentText
      }

      // Retry analysis with more sources
      let retryAnalysis
      if (userTier !== 'trial' && isFeatureEnabled('EPIC6_ENABLED')) {
        retryAnalysis = await analyzeOrdinanceWithTier(retryAnalysisInput, userTier as any, {
          enableLogging: true,
          fallbackOnError: true,
          maxSources: retrySourceCount
        })
      } else {
        retryAnalysis = await analyzeOrdinanceContent(retryAnalysisInput, {
          enableLogging: true,
          fallbackOnError: true
        })
      }

      // Use retry results if confidence improved
      if ((retryAnalysis.confidence_score ?? 0) > (sharedAnalysis.confidence_score ?? 0)) {
        console.log(`✅ Improved confidence: ${Math.round((sharedAnalysis.confidence_score ?? 0) * 100)}% → ${Math.round((retryAnalysis.confidence_score ?? 0) * 100)}%`)
        sharedAnalysis = retryAnalysis
        documentSources = retryDocumentSources
      } else {
        console.log(`📊 No improvement in confidence, using original results`)
      }
    }

    // Convert shared analysis format to compliance summary format
    const extractedData = {
      content: sharedAnalysis.summary,
      citations: sharedAnalysis.citations.map((citation, index) => ({
        source_number: index + 1,
        title: citation,
        url: sharedAnalysis.source_links[index] || '',
        regulation_text: citation,
        section: citation
      })),
      sourceUrl: sharedAnalysis.source_links[0] || '',
      source_document: {
        title: sharedAnalysis.source_links[0] ? 'Primary Ordinance Document' : 'No document available',
        url: sharedAnalysis.source_links[0] || '',
        type: 'ordinance'
      },
      regulation_callout: sharedAnalysis.citations.length > 0 ? {
        source_number: 1,
        title: sharedAnalysis.citations[0] || 'Key Regulation',
        text: sharedAnalysis.requirements[0] || sharedAnalysis.summary,
        url: sharedAnalysis.source_links[0] || ''
      } : null,
      zoningInfo: {
        allowedZones: sharedAnalysis.permit_required ? ['Permitted with approval'] : ['Allowed'],
        restrictions: sharedAnalysis.requirements
      },
      permitInfo: {
        required: sharedAnalysis.permit_required,
        office: sharedAnalysis.contact_info.department || `${jurisdiction.name} Planning Department`,
        phone: sharedAnalysis.contact_info.phone || '',
        address: '',
        fee: '',
        note: sharedAnalysis.permit_process
      },
      specific_requirements: {
        setbacks: sharedAnalysis.requirements.find(req => req.toLowerCase().includes('setback')) || '',
        height_limits: sharedAnalysis.requirements.find(req => req.toLowerCase().includes('height')) || '',
        size_limits: sharedAnalysis.requirements.find(req => req.toLowerCase().includes('size')) || '',
        quantity_limits: sharedAnalysis.requirements.find(req => req.toLowerCase().includes('number') || req.toLowerCase().includes('limit')) || '',
        lot_size_requirements: sharedAnalysis.requirements.find(req => req.toLowerCase().includes('lot')) || ''
      },
      confidence_score: sharedAnalysis.confidence_score || 0.5,
      sources_analyzed: documentSources.length,
      sources_with_content: documentSources.filter(s => s.fetchSuccess).length,
      // Enhanced tier-based features
      red_flags: (sharedAnalysis as { red_flags?: unknown[] }).red_flags || [],
      clauses: (sharedAnalysis as { clauses?: unknown[] }).clauses || []
    }

    // Store red flags and clauses in database for future use
    if (extractedData.red_flags.length > 0) {
      await storeRedFlags(extractedData.red_flags as Array<{ type: string; title: string; description: string; severity: string; citation?: string; source_url?: string }>, jurisdiction.name, ruleType)
    }

    if (extractedData.clauses.length > 0) {
      await storeClauses(extractedData.clauses as Array<{ section_number?: string; title: string; full_text: string; summary?: string; tags: string[]; source_url?: string; document_title?: string }>, jurisdiction.name, ruleType, userTier)
    }

    const researchTime = Date.now() - startTime

    return {
      ...extractedData,
      research_time_ms: researchTime,
      confidence_score: extractedData.confidence_score || 0.8
    }
  } catch (error) {
    console.error('Enhanced real-time research error:', error)
    throw new Error(`Failed to research ${ruleType} ordinances for ${jurisdiction.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Removed unused fallback function - using enhanced tier-based research only



// Enhanced search function with geographic filtering
async function searchOrdinanceDocuments(jurisdiction: string, state: string, projectType: string) {
  try {
    if (!process.env.GOOGLE_SEARCH_API_KEY || !process.env.GOOGLE_SEARCH_ENGINE_ID) {
      throw new Error('Google Search API not configured')
    }

    // Enhanced search query with flexible project type matching
    const baseProjectType = projectType.split(' ')[0] // e.g., "5ft fence" becomes "fence"
    const searchQuery = `${jurisdiction} ${state} ${baseProjectType} ordinance OR zoning OR permit site:gov OR site:org OR site:us`
    console.log(`🔍 Searching Google for: "${searchQuery}"`)

    const url = `https://www.googleapis.com/customsearch/v1?key=${process.env.GOOGLE_SEARCH_API_KEY}&cx=${process.env.GOOGLE_SEARCH_ENGINE_ID}&q=${encodeURIComponent(searchQuery)}&num=10`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Google Search API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.items) {
      const rawResults = data.items.map((item: { title: string; link: string; snippet?: string }) => ({
        title: item.title,
        link: item.link,
        snippet: item.snippet || ''
      }))

      // Filter results for geographic relevance
      const filteredResults = filterGeographicallyRelevantSources(rawResults, jurisdiction, state)
      console.log(`📍 Filtered ${rawResults.length} results to ${filteredResults.length} geographically relevant sources`)

      return filteredResults
    }

    return []
  } catch (error) {
    console.error('Search error:', error)
    return []
  }
}

/**
 * Filter search results to only include sources that are geographically relevant
 */
function filterGeographicallyRelevantSources(
  results: Array<{ title: string; link: string; snippet?: string }>,
  jurisdiction: string,
  state: string
): Array<{ title: string; link: string; snippet?: string }> {
  const jurisdictionLower = jurisdiction.toLowerCase()
  const stateLower = state.toLowerCase()

  return results.filter(result => {
    const titleLower = result.title.toLowerCase()
    const linkLower = result.link.toLowerCase()
    const snippetLower = (result.snippet || '').toLowerCase()

    // Check for official government domains
    const isOfficialDomain = linkLower.includes('.gov') ||
                            linkLower.includes('.org') ||
                            linkLower.includes('.us')

    // Check for jurisdiction name in title, URL, or snippet
    const hasJurisdictionName = titleLower.includes(jurisdictionLower) ||
                               linkLower.includes(jurisdictionLower) ||
                               snippetLower.includes(jurisdictionLower)

    // Check for state name in title, URL, or snippet
    const hasStateName = titleLower.includes(stateLower) ||
                        linkLower.includes(stateLower) ||
                        snippetLower.includes(stateLower)

    // Reject sources that clearly mention other states or jurisdictions
    const otherStates = ['alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 'connecticut', 'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 'illinois', 'indiana', 'iowa', 'kansas', 'kentucky', 'louisiana', 'maine', 'maryland', 'massachusetts', 'michigan', 'minnesota', 'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 'new hampshire', 'new jersey', 'new mexico', 'new york', 'north carolina', 'north dakota', 'ohio', 'oklahoma', 'oregon', 'pennsylvania', 'rhode island', 'south carolina', 'south dakota', 'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 'west virginia', 'wisconsin', 'wyoming']

    const mentionsOtherState = otherStates.some(otherState =>
      otherState !== stateLower &&
      (titleLower.includes(otherState) || linkLower.includes(otherState) || snippetLower.includes(otherState))
    )

    // Score the relevance
    let relevanceScore = 0
    if (isOfficialDomain) relevanceScore += 3
    if (hasJurisdictionName) relevanceScore += 2
    if (hasStateName) relevanceScore += 1
    if (mentionsOtherState) relevanceScore -= 5

    return relevanceScore > 0
  })
}

async function fetchAllDocumentContent(searchResults: Array<{ title: string; link: string; snippet?: string }>, searchQuery: string = '') {
  console.log(`📥 Fetching content from ${searchResults.length} documents...`)

  const documentSources = []

  for (let i = 0; i < searchResults.length; i++) {
    const result = searchResults[i]
    console.log(`📄 Fetching: ${result.title}`)

    try {
      const content = await fetchDocumentContent(result.link)

      documentSources.push({
        position: i + 1,
        title: result.title,
        url: result.link,
        snippet: result.snippet,
        content: content,
        searchQuery: searchQuery,
        contentLength: content ? content.length : 0,
        fetchSuccess: !!content
      })

      console.log(`✅ Fetched ${content ? content.length : 0} characters from: ${result.title}`)
    } catch (error) {
      console.log(`❌ Failed to fetch ${result.link}: ${error instanceof Error ? error.message : 'Unknown error'}`)

      // Still include the result even if we can't fetch content
      documentSources.push({
        position: i + 1,
        title: result.title,
        url: result.link,
        snippet: result.snippet,
        content: `Failed to fetch content: ${error instanceof Error ? error.message : 'Unknown error'}`,
        searchQuery: searchQuery,
        contentLength: 0,
        fetchSuccess: false
      })
    }
  }

  return documentSources
}

async function fetchDocumentContent(url: string): Promise<string | null> {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 10000)

  try {
    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Ordrly/1.0; +https://ordrly.com/bot)',
        'Accept': 'text/html,application/xhtml+xml,application/xml,application/pdf;q=0.9,*/*;q=0.8'
      }
    })

    clearTimeout(timeoutId)

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}`)
  }

  const contentType = response.headers.get('content-type') || ''

  if (contentType.includes('application/pdf')) {
    // Handle PDF content - re-enabled with working implementation
    try {
      console.log(`📄 Attempting to parse PDF: ${url}`)
      const arrayBuffer = await response.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      // Try to import and use pdf2json
      const PDFParser = (await import('pdf2json')).default

      console.log(`📄 PDF buffer size: ${buffer.length} bytes`)

      // Create a promise-based wrapper for pdf2json
      const extractText = () => new Promise((resolve, reject) => {
        const pdfParser = new PDFParser()

        pdfParser.on('pdfParser_dataError', (errData) => {
          reject(new Error(errData.parserError instanceof Error ? errData.parserError.message : String(errData.parserError)))
        })

        pdfParser.on('pdfParser_dataReady', (pdfData) => {
          try {
            // Extract text from all pages
            let fullText = ''
            if (pdfData.Pages && Array.isArray(pdfData.Pages)) {
              for (const page of pdfData.Pages) {
                if (page.Texts && Array.isArray(page.Texts)) {
                  for (const textItem of page.Texts) {
                    if (textItem.R && Array.isArray(textItem.R)) {
                      for (const run of textItem.R) {
                        if (run.T) {
                          // Decode URI component to get readable text
                          fullText += decodeURIComponent(run.T) + ' '
                        }
                      }
                    }
                  }
                  fullText += '\n\n' // Add page break
                }
              }
            }
            resolve(fullText.trim())
          } catch (error) {
            reject(error)
          }
        })

        // Parse the PDF buffer
        pdfParser.parseBuffer(buffer)
      })

      const fullText = await extractText() as string
      console.log(`📄 PDF parsed successfully: ${fullText.length} characters`)

      if (fullText && fullText.length > 100) {
        return `[PDF Document - Text Successfully Extracted]

Title: ${url.split('/').pop() || 'Unknown PDF'}
URL: ${url}
Content-Type: ${contentType}
Size: ${buffer.length} bytes

EXTRACTED TEXT:
${fullText.length > 15000 ? fullText.substring(0, 15000) + '\n\n[Content truncated - document contains additional text]' : fullText}`
      } else {
        throw new Error('PDF text extraction returned empty or minimal content')
      }

    } catch (pdfError) {
      console.error(`❌ PDF parsing failed for ${url}:`, pdfError instanceof Error ? pdfError.message : 'Unknown error')

      // Fallback to enhanced metadata
      const contentLength = response.headers.get('content-length') || 'unknown'
      const fileName = url.split('/').pop() || 'Unknown PDF'

      return `[PDF Document - Text Extraction Failed]

Title: ${fileName}
URL: ${url}
Content-Type: ${contentType}
Size: ${contentLength} bytes
Error: ${pdfError instanceof Error ? pdfError.message : 'Unknown error'}

This PDF document could not be automatically parsed for text extraction. The document likely contains ordinance information but requires manual review.`
    }
  } else {
    // Handle HTML content
    const content = await response.text()

    // Extract meaningful text content
    const cleanContent = content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()

    if (cleanContent.length > 100) {
      return cleanContent.substring(0, 15000) // Limit content size
    } else {
      throw new Error('HTML content too short')
    }
  }
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

// Old analyzeOrdinanceContent function removed - now using shared function from @/lib/ordinance-analysis

// Old functions removed - using simplified approach above

// Old helper functions removed - using simplified approach

// Old functions removed - using new simplified approach above

/**
 * Store red flags in database for caching
 */
async function storeRedFlags(redFlags: Array<{ type: string; title: string; description: string; severity: string; citation?: string; source_url?: string }>, jurisdictionName: string, ruleType: string) {
  try {
    const supabase = await createServerClient()

    const flagsToInsert = redFlags.map(flag => ({
      jurisdiction_name: jurisdictionName,
      rule_type: ruleType,
      flag_type: flag.type,
      title: flag.title,
      description: flag.description,
      severity: flag.severity,
      ordinance_citation: flag.citation,
      source_url: flag.source_url
    }))

    const { error } = await supabase
      .from('red_flags')
      .upsert(flagsToInsert, {
        onConflict: 'jurisdiction_name,rule_type,flag_type,title'
      })

    if (error) {
      console.error('Failed to store red flags:', error)
    } else {
      console.log(`✓ Stored ${flagsToInsert.length} red flags for ${jurisdictionName} - ${ruleType}`)
    }
  } catch (error) {
    console.error('Red flag storage error:', error)
  }
}

/**
 * Store ordinance clauses in database for caching
 */
async function storeClauses(clauses: Array<{ section_number?: string; title: string; full_text: string; summary?: string; tags: string[]; source_url?: string; document_title?: string }>, jurisdictionName: string, ruleType: string, userTier: string) {
  try {
    const supabase = await createServerClient()

    const clausesToInsert = clauses.map(clause => ({
      jurisdiction_name: jurisdictionName,
      rule_type: ruleType,
      section_number: clause.section_number,
      title: clause.title,
      full_text: clause.full_text,
      summary: clause.summary,
      tags: clause.tags,
      source_url: clause.source_url,
      document_title: clause.document_title,
      tier_access: userTier === 'appraiser' ? 'appraiser' : 'pro'
    }))

    const { error } = await supabase
      .from('ordinance_clauses')
      .upsert(clausesToInsert, {
        onConflict: 'jurisdiction_name,rule_type,title'
      })

    if (error) {
      console.error('Failed to store clauses:', error)
    } else {
      console.log(`✓ Stored ${clausesToInsert.length} clauses for ${jurisdictionName} - ${ruleType}`)
    }
  } catch (error) {
    console.error('Clause storage error:', error)
  }
}

// Simplified approach - no complex URL verification needed since we use actual search results

/**
 * Save search history for Epic 9.1.1
 */
async function saveSearchHistory(
  userId: string,
  address: string,
  projectType: string,
  jurisdictionName: string,
  coordinates: { lat: number; lng: number },
  resultSummary: Record<string, unknown>
) {
  try {
    const supabase = await createServerClient()

    const searchParams = {
      lat: coordinates.lat,
      lng: coordinates.lng,
      address: address
    }

    const summaryForHistory = {
      jurisdiction_name: resultSummary.jurisdiction_name,
      jurisdiction_level: resultSummary.jurisdiction_level,
      confidence_score: resultSummary.confidence_score,
      cached: resultSummary.cached,
      tier_used: resultSummary.tier_used,
      sources_analyzed: resultSummary.sources_analyzed
    }

    const { error } = await supabase
      .from('search_history')
      .insert({
        user_id: userId,
        address: address,
        project_type: projectType,
        jurisdiction_name: jurisdictionName,
        search_params: searchParams,
        result_summary: summaryForHistory
      })

    if (error) {
      console.error('Failed to save search history:', error)
    } else {
      console.log(`✓ Saved search history for user ${userId}: ${address} - ${projectType}`)
    }
  } catch (error) {
    console.error('Search history save error:', error)
  }
}
