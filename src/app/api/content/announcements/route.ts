import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Validation schemas
const createAnnouncementSchema = z.object({
  action: z.literal('create'),
  title: z.string().min(1, 'Title is required'),
  platform: z.enum(['twitter', 'linkedin', 'facebook', 'instagram']),
  contentTemplate: z.string().min(1, 'Content template is required'),
  contentData: z.record(z.any()).optional(),
  scheduledFor: z.string().datetime().optional()
})

const generateLaunchThreadSchema = z.object({
  action: z.literal('generate_launch_thread'),
  platform: z.enum(['twitter', 'linkedin']),
  includeScreenshots: z.boolean().default(true),
  includeTestimonials: z.boolean().default(true),
  customMessage: z.string().optional()
})

const updateAnnouncementSchema = z.object({
  action: z.literal('update'),
  announcementId: z.string().uuid(),
  updates: z.object({
    title: z.string().optional(),
    contentTemplate: z.string().optional(),
    contentData: z.record(z.any()).optional(),
    scheduledFor: z.string().datetime().optional(),
    status: z.enum(['draft', 'scheduled', 'published', 'failed']).optional()
  })
})

// Launch thread templates
const LAUNCH_THREAD_TEMPLATES = {
  twitter: {
    intro: `🚀 Ordrly is now LIVE!

After months of development, we're excited to launch the first AI-powered compliance research platform for property owners, contractors, and real estate professionals.

🧵 Here's the story behind Ordrly and what makes it special:

1/8`,

    problem: `❌ The Problem:
Property compliance research used to take hours of digging through municipal websites, calling city offices, and deciphering complex ordinances.

One simple question like "Can I build a fence?" would turn into a day-long research project.

2/8`,

    solution: `✅ The Solution:
Ordrly uses AI to quickly research local ordinances and provide clear, actionable compliance summaries.

Just enter your address and project type - get detailed requirements in seconds, not hours.

3/8`,

    features: `🔥 Key Features:
• Fast compliance research for any US address
• AI-powered ordinance analysis
• Professional documentation for permits
• Unlimited searches for Pro users
• Expert chat support

4/8`,

    testimonial: `💬 Early User Feedback:
"Ordrly saved me 6 hours of research for my deck project. The AI found requirements I never would have discovered on my own." - Sarah M., Homeowner

5/8`,

    screenshot: `📸 See it in action:
[Screenshot of compliance summary]

Clean, professional results you can trust for your next project.

6/8`,

    cta: `🎯 Try it now:
✅ Free tier: 5 searches/month
✅ Pro tier: Unlimited searches + expert chat
✅ Works for any property type

👉 ordrly.com

7/8`,

    closing: `🙏 Thank you to everyone who supported us during development!

Follow @OrdrlyApp for updates and tips on property compliance.

RT if you think this could help someone with their next project! 🏠

8/8`
  },

  linkedin: {
    intro: `🚀 Excited to announce that Ordrly is now LIVE!

After months of development, we've launched the first AI-powered compliance research platform designed specifically for property owners, contractors, and real estate professionals.`,

    problem: `The Challenge We're Solving:
Property compliance research has always been a time-consuming, frustrating process. A simple question like "What are the setback requirements for my fence?" could take hours of research across multiple municipal websites.`,

    solution: `Our Solution:
Ordrly leverages advanced AI to quickly research local ordinances and deliver clear, actionable compliance summaries. What used to take hours now takes seconds.`,

    features: `Key Features:
• Fast compliance research for any US address
• AI-powered ordinance analysis and interpretation
• Professional documentation ready for permit applications
• Unlimited searches for Pro subscribers
• Expert chat support for complex questions`,

    testimonial: `Early feedback has been incredible:
"Ordrly saved me 6 hours of research for my deck project. The AI found requirements I never would have discovered on my own." - Sarah M.`,

    cta: `Ready to streamline your next project?
👉 Try Ordrly at ordrly.ai
✅ Free tier available
✅ Pro tier with unlimited access

#PropTech #RealEstate #Construction #AI #Compliance`
  }
}

// GET /api/content/announcements - Get launch announcements
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const platform = searchParams.get('platform')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Build query
    let query = supabase
      .from('launch_announcements')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (platform) {
      query = query.eq('platform', platform)
    }
    if (status) {
      query = query.eq('status', status)
    }

    const { data: announcements, error } = await query

    if (error) {
      console.error('Error fetching announcements:', error)
      return NextResponse.json(
        { error: 'Failed to fetch announcements' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: announcements
    })
  } catch (error) {
    console.error('Error in GET /api/content/announcements:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/content/announcements - Handle announcement actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Handle create announcement action
    if (body.action === 'create') {
      const validation = createAnnouncementSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { title, platform, contentTemplate, contentData, scheduledFor } = validation.data

      const { data: announcement, error } = await supabase
        .from('launch_announcements')
        .insert({
          title,
          platform,
          content_template: contentTemplate,
          content_data: contentData || {},
          scheduled_for: scheduledFor,
          status: scheduledFor ? 'scheduled' : 'draft'
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating announcement:', error)
        return NextResponse.json(
          { error: 'Failed to create announcement' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: announcement
      })
    }

    // Handle generate launch thread action
    if (body.action === 'generate_launch_thread') {
      const validation = generateLaunchThreadSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { platform, includeScreenshots, includeTestimonials, customMessage } = validation.data

      // Get template for platform
      const template = LAUNCH_THREAD_TEMPLATES[platform]
      if (!template) {
        return NextResponse.json(
          { error: 'Template not found for platform' },
          { status: 400 }
        )
      }

      // Build thread content
      let threadContent = ''

      if (platform === 'twitter') {
        const twitterTemplate = template as typeof LAUNCH_THREAD_TEMPLATES.twitter
        threadContent = [
          twitterTemplate.intro,
          twitterTemplate.problem,
          twitterTemplate.solution,
          twitterTemplate.features,
          includeTestimonials ? twitterTemplate.testimonial : null,
          includeScreenshots ? twitterTemplate.screenshot : null,
          twitterTemplate.cta,
          twitterTemplate.closing
        ].filter(Boolean).join('\n\n')
      } else {
        threadContent = [
          template.intro,
          template.problem,
          template.solution,
          template.features,
          includeTestimonials ? template.testimonial : null,
          template.cta
        ].filter(Boolean).join('\n\n')
      }

      // Add custom message if provided
      if (customMessage) {
        threadContent = `${customMessage}\n\n${threadContent}`
      }

      // Create announcement
      const { data: announcement, error } = await supabase
        .from('launch_announcements')
        .insert({
          title: `Launch Thread - ${platform.charAt(0).toUpperCase() + platform.slice(1)}`,
          platform,
          content_template: threadContent,
          content_data: {
            includeScreenshots,
            includeTestimonials,
            customMessage,
            generatedAt: new Date().toISOString()
          },
          status: 'draft'
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating launch thread:', error)
        return NextResponse.json(
          { error: 'Failed to create launch thread' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: announcement,
        message: 'Launch thread generated successfully'
      })
    }

    // Handle update announcement action
    if (body.action === 'update') {
      const validation = updateAnnouncementSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { announcementId, updates } = validation.data

      const { data: announcement, error } = await supabase
        .from('launch_announcements')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', announcementId)
        .select()
        .single()

      if (error) {
        console.error('Error updating announcement:', error)
        return NextResponse.json(
          { error: 'Failed to update announcement' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: announcement
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error in POST /api/content/announcements:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
