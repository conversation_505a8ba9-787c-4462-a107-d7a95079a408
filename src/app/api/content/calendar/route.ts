import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Validation schemas
const getCalendarSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)')
})

const updateCalendarSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  notes: z.string().optional(),
  theme: z.string().optional(),
  status: z.enum(['planning', 'ready', 'published', 'completed']).optional()
})

// GET /api/content/calendar - Get content calendar for date range
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      )
    }

    const validation = getCalendarSchema.safeParse({ startDate, endDate })
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid date format', details: validation.error.issues },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get content calendar data
    const { data: calendarData, error } = await supabase.rpc('get_content_calendar', {
      start_date: startDate,
      end_date: endDate
    })

    if (error) {
      console.error('Error fetching content calendar:', error)
      return NextResponse.json(
        { error: 'Failed to fetch content calendar' },
        { status: 500 }
      )
    }

    // Fill in missing dates with empty entries
    const start = new Date(startDate)
    const end = new Date(endDate)
    const dateMap = new Map()

    // Create map of existing calendar data
    calendarData?.forEach((item: { calendar_date: string }) => {
      dateMap.set(item.calendar_date, item)
    })

    // Fill in missing dates
    const completeCalendar = []
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      if (dateMap.has(dateStr)) {
        completeCalendar.push(dateMap.get(dateStr))
      } else {
        completeCalendar.push({
          calendar_date: dateStr,
          content_items: [],
          notes: null,
          theme: null,
          status: 'planning'
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        calendar: completeCalendar,
        dateRange: {
          startDate,
          endDate
        }
      }
    })
  } catch (error) {
    console.error('Error in GET /api/content/calendar:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/content/calendar - Update calendar entry
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validation = updateCalendarSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: validation.error.issues },
        { status: 400 }
      )
    }

    const { date, notes, theme, status } = validation.data

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Update or create calendar entry
    const { data: calendarEntry, error } = await supabase
      .from('content_calendar')
      .upsert({
        calendar_date: date,
        notes,
        theme,
        status,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'calendar_date'
      })
      .select()
      .single()

    if (error) {
      console.error('Error updating calendar entry:', error)
      return NextResponse.json(
        { error: 'Failed to update calendar entry' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: calendarEntry
    })
  } catch (error) {
    console.error('Error in POST /api/content/calendar:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
