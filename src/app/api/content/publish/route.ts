import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// This endpoint processes scheduled content for publishing
// It should be called by a cron job or scheduled task

export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron job)
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.CRON_SECRET || 'dev-secret'

    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = await createServerClient()

    // Get pending content for publishing
    const { data: pendingContent, error: contentError } = await supabase.rpc('get_pending_content')

    if (contentError) {
      console.error('Error fetching pending content:', contentError)
      return NextResponse.json(
        { error: 'Failed to fetch pending content' },
        { status: 500 }
      )
    }

    if (!pendingContent || pendingContent.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No pending content to publish',
        processed: 0
      })
    }

    let processed = 0
    let errors = 0

    // Process each content item
    for (const content of pendingContent) {
      try {
        let publishSuccess = false

        // Handle different content types
        switch (content.content_type) {
          case 'video':
            publishSuccess = await publishVideoContent(content)
            break
          case 'announcement':
            publishSuccess = await publishAnnouncementContent(content)
            break
          case 'meme':
          case 'quote':
          case 'screenshot':
            publishSuccess = await publishImageContent(content)
            break
          default:
            console.warn(`Unknown content type: ${content.content_type}`)
            publishSuccess = false
            break
        }

        // Mark content as published or failed
        await supabase.rpc('mark_content_published', {
          item_id: content.item_id,
          platform_name: content.platform?.[0] || 'unknown',
          success: publishSuccess,
          error_msg: publishSuccess ? null : 'Publishing failed'
        })

        if (publishSuccess) {
          processed++
        } else {
          errors++
        }
      } catch (publishError) {
        console.error(`Error publishing content ${content.item_id}:`, publishError)

        // Mark content as failed
        await supabase.rpc('mark_content_published', {
          item_id: content.item_id,
          platform_name: content.platform?.[0] || 'unknown',
          success: false,
          error_msg: publishError instanceof Error ? publishError.message : 'Unknown error'
        })
        errors++
      }
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${processed} content items successfully, ${errors} errors`,
      processed,
      errors,
      totalContent: pendingContent.length
    })
  } catch (error) {
    console.error('Error in POST /api/content/publish:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to publish video content
async function publishVideoContent(content: { title: string; description?: string; content_data: Record<string, unknown>; platform: string[] }): Promise<boolean> {
  try {
    console.log(`Publishing video content: ${content.title}`)

    // Extract video data
    const videoData = content.content_data
    const platforms = content.platform || []

    // For now, we'll simulate publishing by logging the content
    // In a real implementation, you would integrate with social media APIs

    for (const platform of platforms) {
      console.log(`Publishing to ${platform}:`)
      console.log(`Title: ${content.title}`)
      console.log(`Caption: ${videoData.caption || content.description}`)
      console.log(`Hashtags: ${(videoData.hashtags as string[])?.join(' ') || ''}`)
      console.log(`CTA: ${videoData.cta_text || 'Check your property on Ordrly.com'}`)

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return true
  } catch (error) {
    console.error('Error publishing video content:', error)
    return false
  }
}

// Helper function to publish announcement content
async function publishAnnouncementContent(content: { title: string; description?: string; content_data: Record<string, unknown>; platform: string[] }): Promise<boolean> {
  try {
    console.log(`Publishing announcement: ${content.title}`)

    const platforms = content.platform || []

    for (const platform of platforms) {
      console.log(`Publishing announcement to ${platform}:`)
      console.log(content.content_data.content || 'No content available')

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return true
  } catch (error) {
    console.error('Error publishing announcement:', error)
    return false
  }
}

// Helper function to publish image content (memes, quotes, screenshots)
async function publishImageContent(content: { title: string; description?: string; content_data: Record<string, unknown>; platform: string[]; content_type: string }): Promise<boolean> {
  try {
    console.log(`Publishing image content: ${content.title}`)

    const platforms = content.platform || []
    const contentData = content.content_data

    for (const platform of platforms) {
      console.log(`Publishing to ${platform}:`)
      console.log(`Type: ${content.content_type}`)
      console.log(`Caption: ${contentData.caption || content.description}`)
      console.log(`Image URL: ${contentData.image_url || 'No image URL'}`)

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return true
  } catch (error) {
    console.error('Error publishing image content:', error)
    return false
  }
}

// GET /api/content/publish - Get publishing status and logs
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get recent published content
    const { data: recentContent, error } = await supabase
      .from('content_items')
      .select('*')
      .eq('status', 'published')
      .order('published_at', { ascending: false })
      .limit(20)

    if (error) {
      console.error('Error fetching published content:', error)
      return NextResponse.json(
        { error: 'Failed to fetch published content' },
        { status: 500 }
      )
    }

    // Get pending content count
    const { data: pendingContent } = await supabase.rpc('get_pending_content')
    const pendingCount = pendingContent?.length || 0

    // Get content stats
    const { data: stats } = await supabase
      .from('content_items')
      .select('status')

    const statusCounts = stats?.reduce((acc: Record<string, number>, item: { status: string }) => {
      acc[item.status] = (acc[item.status] || 0) + 1
      return acc
    }, {}) || {}

    return NextResponse.json({
      success: true,
      data: {
        recentContent,
        pendingCount,
        stats: statusCounts
      }
    })
  } catch (error) {
    console.error('Error in GET /api/content/publish:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
