import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Validation schemas
const createContentSchema = z.object({
  action: z.literal('create'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  contentType: z.enum(['video', 'meme', 'quote', 'announcement', 'screenshot', 'testimonial']),
  platform: z.array(z.enum(['twitter', 'linkedin', 'tiktok', 'instagram', 'youtube', 'facebook'])),
  contentData: z.record(z.any()).optional()
})

const scheduleContentSchema = z.object({
  action: z.literal('schedule'),
  itemId: z.string().uuid(),
  scheduledFor: z.string().datetime(),
  calendarDate: z.string().optional()
})

const updateContentSchema = z.object({
  action: z.literal('update'),
  itemId: z.string().uuid(),
  updates: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    contentData: z.record(z.any()).optional(),
    status: z.enum(['draft', 'scheduled', 'published', 'archived']).optional()
  })
})

// GET /api/content - Get content items with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const contentType = searchParams.get('type')
    const status = searchParams.get('status')
    const platform = searchParams.get('platform')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Build query
    let query = supabase
      .from('content_items')
      .select(`
        id,
        title,
        description,
        content_type,
        platform,
        status,
        content_data,
        scheduled_for,
        published_at,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (contentType) {
      query = query.eq('content_type', contentType)
    }
    if (status) {
      query = query.eq('status', status)
    }
    if (platform) {
      query = query.contains('platform', [platform])
    }

    const { data: contentItems, error } = await query

    if (error) {
      console.error('Error fetching content items:', error)
      return NextResponse.json(
        { error: 'Failed to fetch content items' },
        { status: 500 }
      )
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('content_items')
      .select('*', { count: 'exact', head: true })

    if (contentType) countQuery = countQuery.eq('content_type', contentType)
    if (status) countQuery = countQuery.eq('status', status)
    if (platform) countQuery = countQuery.contains('platform', [platform])

    const { count } = await countQuery

    return NextResponse.json({
      success: true,
      data: {
        items: contentItems,
        pagination: {
          total: count || 0,
          limit,
          offset,
          hasMore: (count || 0) > offset + limit
        }
      }
    })
  } catch (error) {
    console.error('Error in GET /api/content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/content - Handle content management actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Handle create content action
    if (body.action === 'create') {
      const validation = createContentSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { title, description, contentType, platform, contentData } = validation.data

      const { data: contentItem, error } = await supabase
        .from('content_items')
        .insert({
          title,
          description,
          content_type: contentType,
          platform,
          content_data: contentData || {},
          created_by: user.id
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating content item:', error)
        return NextResponse.json(
          { error: 'Failed to create content item' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: contentItem
      })
    }

    // Handle schedule content action
    if (body.action === 'schedule') {
      const validation = scheduleContentSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { itemId, scheduledFor, calendarDate } = validation.data

      const { data: success, error } = await supabase.rpc('schedule_content_item', {
        item_id: itemId,
        schedule_date: scheduledFor,
        calendar_date_param: calendarDate ? new Date(calendarDate).toISOString().split('T')[0] : null
      })

      if (error) {
        console.error('Error scheduling content:', error)
        return NextResponse.json(
          { error: 'Failed to schedule content' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: !!success,
        message: 'Content scheduled successfully'
      })
    }

    // Handle update content action
    if (body.action === 'update') {
      const validation = updateContentSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { itemId, updates } = validation.data

      const { data: contentItem, error } = await supabase
        .from('content_items')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', itemId)
        .select()
        .single()

      if (error) {
        console.error('Error updating content item:', error)
        return NextResponse.json(
          { error: 'Failed to update content item' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: contentItem
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error in POST /api/content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/content - Delete content item
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const itemId = searchParams.get('id')

    if (!itemId) {
      return NextResponse.json({ error: 'Content item ID required' }, { status: 400 })
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { error } = await supabase
      .from('content_items')
      .delete()
      .eq('id', itemId)

    if (error) {
      console.error('Error deleting content item:', error)
      return NextResponse.json(
        { error: 'Failed to delete content item' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Content item deleted successfully'
    })
  } catch (error) {
    console.error('Error in DELETE /api/content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
