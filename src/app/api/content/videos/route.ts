import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Validation schemas
const createVideoSchema = z.object({
  action: z.literal('create'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  fileUrl: z.string().url('Invalid file URL'),
  thumbnailUrl: z.string().url().optional(),
  duration: z.number().positive().optional(),
  fileSize: z.number().positive().optional(),
  platforms: z.array(z.enum(['tiktok', 'instagram', 'youtube', 'facebook'])).optional(),
  ctaText: z.string().optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional()
})

const createHOAKarenVideoSchema = z.object({
  action: z.literal('create_hoa_karen'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  videoUrl: z.string().url('Invalid video URL'),
  thumbnailUrl: z.string().url().optional(),
  duration: z.number().positive().optional()
})

const updateVideoSchema = z.object({
  action: z.literal('update'),
  videoId: z.string().uuid(),
  updates: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    ctaText: z.string().optional(),
    tags: z.array(z.string()).optional(),
    metadata: z.record(z.any()).optional(),
    status: z.enum(['uploaded', 'processing', 'ready', 'published', 'archived']).optional()
  })
})

// GET /api/content/videos - Get video assets
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const platform = searchParams.get('platform')
    const tag = searchParams.get('tag')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Build query
    let query = supabase
      .from('video_assets')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (status) {
      query = query.eq('status', status)
    }
    if (platform) {
      query = query.contains('platforms', [platform])
    }
    if (tag) {
      query = query.contains('tags', [tag])
    }

    const { data: videos, error } = await query

    if (error) {
      console.error('Error fetching video assets:', error)
      return NextResponse.json(
        { error: 'Failed to fetch video assets' },
        { status: 500 }
      )
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('video_assets')
      .select('*', { count: 'exact', head: true })

    if (status) countQuery = countQuery.eq('status', status)
    if (platform) countQuery = countQuery.contains('platforms', [platform])
    if (tag) countQuery = countQuery.contains('tags', [tag])

    const { count } = await countQuery

    return NextResponse.json({
      success: true,
      data: {
        videos,
        pagination: {
          total: count || 0,
          limit,
          offset,
          hasMore: (count || 0) > offset + limit
        }
      }
    })
  } catch (error) {
    console.error('Error in GET /api/content/videos:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/content/videos - Handle video management actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Handle create video action
    if (body.action === 'create') {
      const validation = createVideoSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { 
        title, 
        description, 
        fileUrl, 
        thumbnailUrl, 
        duration, 
        fileSize, 
        platforms, 
        ctaText, 
        tags, 
        metadata 
      } = validation.data

      const { data: video, error } = await supabase
        .from('video_assets')
        .insert({
          title,
          description,
          file_url: fileUrl,
          thumbnail_url: thumbnailUrl,
          duration,
          file_size: fileSize,
          platforms: platforms || ['tiktok', 'instagram', 'youtube'],
          cta_text: ctaText || 'Check your property on Ordrly.com',
          tags: tags || [],
          metadata: metadata || {}
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating video asset:', error)
        return NextResponse.json(
          { error: 'Failed to create video asset' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: video
      })
    }

    // Handle create HOA Karen video action
    if (body.action === 'create_hoa_karen') {
      const validation = createHOAKarenVideoSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { title, description, videoUrl, thumbnailUrl, duration } = validation.data

      const { data: contentId, error } = await supabase.rpc('create_hoa_karen_video', {
        video_title: title,
        video_description: description,
        video_url: videoUrl,
        thumbnail_url: thumbnailUrl,
        duration_seconds: duration
      })

      if (error) {
        console.error('Error creating HOA Karen video:', error)
        return NextResponse.json(
          { error: 'Failed to create HOA Karen video' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: { contentId },
        message: 'HOA Karen video created successfully'
      })
    }

    // Handle update video action
    if (body.action === 'update') {
      const validation = updateVideoSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { videoId, updates } = validation.data

      const { data: video, error } = await supabase
        .from('video_assets')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', videoId)
        .select()
        .single()

      if (error) {
        console.error('Error updating video asset:', error)
        return NextResponse.json(
          { error: 'Failed to update video asset' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: video
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error in POST /api/content/videos:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/content/videos - Delete video asset
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const videoId = searchParams.get('id')

    if (!videoId) {
      return NextResponse.json({ error: 'Video ID required' }, { status: 400 })
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { error } = await supabase
      .from('video_assets')
      .delete()
      .eq('id', videoId)

    if (error) {
      console.error('Error deleting video asset:', error)
      return NextResponse.json(
        { error: 'Failed to delete video asset' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Video asset deleted successfully'
    })
  } catch (error) {
    console.error('Error in DELETE /api/content/videos:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
