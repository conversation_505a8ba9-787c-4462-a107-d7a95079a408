import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Parse form data
    const formData = await request.formData()
    const jurisdiction = formData.get('jurisdiction') as string
    const state = formData.get('state') as string
    const ordinanceType = formData.get('ordinanceType') as string
    const description = formData.get('description') as string
    const contactEmail = formData.get('contactEmail') as string

    // Validate required fields
    if (!jurisdiction || !state || !ordinanceType) {
      return NextResponse.json(
        { error: 'Missing required fields: jurisdiction, state, and ordinanceType are required' },
        { status: 400 }
      )
    }

    // Process uploaded files
    const files = []
    const fileEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('file_'))
    
    for (const [key, file] of fileEntries) {
      if (file instanceof File && file.size > 0) {
        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
          return NextResponse.json(
            { error: `File ${file.name} exceeds 10MB limit` },
            { status: 400 }
          )
        }

        // Validate file type
        const allowedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain'
        ]
        
        if (!allowedTypes.includes(file.type)) {
          return NextResponse.json(
            { error: `File ${file.name} has unsupported type. Please use PDF, DOC, DOCX, or TXT files.` },
            { status: 400 }
          )
        }

        // Convert file to base64 for storage
        const arrayBuffer = await file.arrayBuffer()
        const base64 = Buffer.from(arrayBuffer).toString('base64')
        
        files.push({
          name: file.name,
          type: file.type,
          size: file.size,
          content: base64
        })
      }
    }

    // Store contribution in database
    const { data: contribution, error: contributionError } = await supabase
      .from('user_contributions')
      .insert({
        jurisdiction,
        state,
        ordinance_type: ordinanceType,
        description,
        contact_email: contactEmail,
        files_count: files.length,
        status: 'pending',
        submitted_at: new Date().toISOString()
      })
      .select()
      .single()

    if (contributionError) {
      console.error('Error storing contribution:', contributionError)
      return NextResponse.json(
        { error: 'Failed to store contribution' },
        { status: 500 }
      )
    }

    // Store files separately if any were uploaded
    if (files.length > 0) {
      const fileRecords = files.map(file => ({
        contribution_id: contribution.id,
        filename: file.name,
        file_type: file.type,
        file_size: file.size,
        file_content: file.content
      }))

      const { error: filesError } = await supabase
        .from('contribution_files')
        .insert(fileRecords)

      if (filesError) {
        console.error('Error storing files:', filesError)
        // Don't fail the request, just log the error
      }
    }

    // Send notification email (optional - could be implemented later)
    try {
      await sendNotificationEmail({
        jurisdiction,
        state,
        ordinanceType,
        description,
        contactEmail,
        filesCount: files.length,
        contributionId: contribution.id
      })
    } catch (emailError) {
      console.error('Error sending notification email:', emailError)
      // Don't fail the request for email errors
    }

    return NextResponse.json({
      success: true,
      message: 'Contribution submitted successfully',
      contributionId: contribution.id
    })

  } catch (error) {
    console.error('Contribution submission error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to send notification email
async function sendNotificationEmail(data: {
  jurisdiction: string
  state: string
  ordinanceType: string
  description: string
  contactEmail: string
  filesCount: number
  contributionId: string
}) {
  // This could be implemented with Resend or another email service
  // For now, just log the notification
  console.log('📧 New contribution received:', {
    id: data.contributionId,
    jurisdiction: `${data.jurisdiction}, ${data.state}`,
    type: data.ordinanceType,
    files: data.filesCount,
    contact: data.contactEmail || 'No contact provided'
  })

  // TODO: Implement actual email notification
  // const { Resend } = require('resend')
  // const resend = new Resend(process.env.RESEND_API_KEY)
  // 
  // await resend.emails.send({
  //   from: '<EMAIL>',
  //   to: '<EMAIL>',
  //   subject: `New Ordinance Contribution: ${data.jurisdiction}, ${data.state}`,
  //   html: `
  //     <h2>New Ordinance Contribution</h2>
  //     <p><strong>Jurisdiction:</strong> ${data.jurisdiction}, ${data.state}</p>
  //     <p><strong>Type:</strong> ${data.ordinanceType}</p>
  //     <p><strong>Files:</strong> ${data.filesCount}</p>
  //     <p><strong>Contact:</strong> ${data.contactEmail || 'Not provided'}</p>
  //     <p><strong>Description:</strong></p>
  //     <p>${data.description}</p>
  //     <p><strong>Contribution ID:</strong> ${data.contributionId}</p>
  //   `
  // })
}
