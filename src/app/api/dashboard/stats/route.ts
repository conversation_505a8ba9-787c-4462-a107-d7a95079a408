import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ 
        total_searches: 0,
        recent_searches: 0,
        saved_searches: 0,
        frequent_areas: 0
      })
    }

    // Get total searches
    const { count: totalSearches } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    // Get recent searches (last 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    
    const { count: recentSearches } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .gte('created_at', sevenDaysAgo.toISOString())

    // Get saved searches count
    const { count: savedSearches } = await supabase
      .from('saved_searches')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    // Get frequent areas count
    const { data: searchHistory } = await supabase
      .from('search_history')
      .select('address')
      .eq('user_id', user.id)
      .limit(100)

    // Count unique areas
    const areas = new Set()
    if (searchHistory) {
      searchHistory.forEach(search => {
        const addressParts = search.address.split(',')
        if (addressParts.length >= 2) {
          const area = addressParts[addressParts.length - 2].trim()
          areas.add(area)
        }
      })
    }

    return NextResponse.json({
      total_searches: totalSearches || 0,
      recent_searches: recentSearches || 0,
      saved_searches: savedSearches || 0,
      frequent_areas: areas.size
    })

  } catch (error) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json({
      total_searches: 0,
      recent_searches: 0,
      saved_searches: 0,
      frequent_areas: 0
    })
  }
}
