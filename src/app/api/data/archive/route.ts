import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { archive_type = 'auto', cutoff_days = 90, dry_run = false } = body

    // Calculate cutoff date
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - cutoff_days)

    const archiveResults = {
      search_history: { archived: 0, errors: 0 },
      chat_messages: { archived: 0, errors: 0 },
      saved_searches: { archived: 0, errors: 0 },
      total_archived: 0,
      total_errors: 0,
      dry_run
    }

    // Archive old search history
    try {
      const { data: oldSearches } = await supabase
        .from('search_history')
        .select('id, created_at')
        .eq('user_id', user.id)
        .lt('created_at', cutoffDate.toISOString())
        .order('created_at', { ascending: true })

      if (oldSearches && oldSearches.length > 0) {
        if (!dry_run) {
          // Move to archive table
          const archiveData = oldSearches.map(search => ({
            user_id: user.id,
            original_table: 'search_history',
            original_id: search.id,
            archived_at: new Date().toISOString(),
            archive_reason: archive_type,
            data: search
          }))

          const { error: archiveError } = await supabase
            .from('archived_data')
            .insert(archiveData)

          if (!archiveError) {
            // Delete from original table
            const { error: deleteError } = await supabase
              .from('search_history')
              .delete()
              .eq('user_id', user.id)
              .lt('created_at', cutoffDate.toISOString())

            if (!deleteError) {
              archiveResults.search_history.archived = oldSearches.length
            } else {
              archiveResults.search_history.errors = oldSearches.length
            }
          } else {
            archiveResults.search_history.errors = oldSearches.length
          }
        } else {
          archiveResults.search_history.archived = oldSearches.length
        }
      }
    } catch (error) {
      console.error('Error archiving search history:', error)
      archiveResults.search_history.errors = 1
    }

    // Archive old chat messages (if they exist)
    try {
      const { data: oldChats } = await supabase
        .from('chat_messages')
        .select('id, created_at')
        .eq('user_id', user.id)
        .lt('created_at', cutoffDate.toISOString())
        .order('created_at', { ascending: true })

      if (oldChats && oldChats.length > 0) {
        if (!dry_run) {
          const archiveData = oldChats.map(chat => ({
            user_id: user.id,
            original_table: 'chat_messages',
            original_id: chat.id,
            archived_at: new Date().toISOString(),
            archive_reason: archive_type,
            data: chat
          }))

          const { error: archiveError } = await supabase
            .from('archived_data')
            .insert(archiveData)

          if (!archiveError) {
            const { error: deleteError } = await supabase
              .from('chat_messages')
              .delete()
              .eq('user_id', user.id)
              .lt('created_at', cutoffDate.toISOString())

            if (!deleteError) {
              archiveResults.chat_messages.archived = oldChats.length
            } else {
              archiveResults.chat_messages.errors = oldChats.length
            }
          } else {
            archiveResults.chat_messages.errors = oldChats.length
          }
        } else {
          archiveResults.chat_messages.archived = oldChats.length
        }
      }
    } catch (error) {
      console.error('Error archiving chat messages:', error)
      // Chat table might not exist, don't count as error
    }

    // Calculate totals
    archiveResults.total_archived =
      archiveResults.search_history.archived +
      archiveResults.chat_messages.archived +
      archiveResults.saved_searches.archived

    archiveResults.total_errors =
      archiveResults.search_history.errors +
      archiveResults.chat_messages.errors +
      archiveResults.saved_searches.errors

    // Update user's last archival date
    if (!dry_run && archiveResults.total_archived > 0) {
      await supabase
        .from('profiles')
        .update({
          last_data_archival: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
    }

    return NextResponse.json({
      success: true,
      message: dry_run ?
        `Would archive ${archiveResults.total_archived} items` :
        `Successfully archived ${archiveResults.total_archived} items`,
      results: archiveResults,
      cutoff_date: cutoffDate.toISOString()
    })

  } catch (error) {
    console.error('Data archival error:', error)
    return NextResponse.json(
      { error: 'Failed to archive data' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const cutoffDays = parseInt(searchParams.get('cutoff_days') || '90')

    // Calculate what would be archived
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - cutoffDays)

    const analysis = {
      cutoff_date: cutoffDate.toISOString(),
      cutoff_days: cutoffDays,
      archivable_items: {
        search_history: 0,
        chat_messages: 0,
        saved_searches: 0
      },
      total_archivable: 0,
      storage_savings_estimate: 0
    }

    // Count archivable search history
    const { count: searchCount } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .lt('created_at', cutoffDate.toISOString())

    analysis.archivable_items.search_history = searchCount || 0

    // Count archivable chat messages
    try {
      const { count: chatCount } = await supabase
        .from('chat_messages')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .lt('created_at', cutoffDate.toISOString())

      analysis.archivable_items.chat_messages = chatCount || 0
    } catch {
      // Chat table might not exist
      analysis.archivable_items.chat_messages = 0
    }

    analysis.total_archivable =
      analysis.archivable_items.search_history +
      analysis.archivable_items.chat_messages +
      analysis.archivable_items.saved_searches

    // Estimate storage savings (rough calculation)
    analysis.storage_savings_estimate =
      (analysis.archivable_items.search_history * 1024) + // ~1KB per search
      (analysis.archivable_items.chat_messages * 512) +   // ~0.5KB per chat
      (analysis.archivable_items.saved_searches * 2048)   // ~2KB per saved search

    return NextResponse.json(analysis)

  } catch (error) {
    console.error('Archive analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze archival data' },
      { status: 500 }
    )
  }
}
