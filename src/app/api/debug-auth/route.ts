import { NextRequest, NextResponse } from 'next/server'
import { createServerClient as createSSRServerClient } from '@supabase/ssr'
import { createServiceClient } from '@/lib/supabase/server'

/**
 * Debug authentication endpoint to check what's happening with auth
 */

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug Auth - Checking authentication state...')
    
    // Check cookies
    const cookies = request.cookies.getAll()
    console.log('🍪 Available cookies:', cookies.map(c => c.name))
    
    // Create Supabase client with same pattern as middleware
    const supabase = createSSRServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // Not needed for GET
          },
        },
      }
    )

    // Try to get user
    const { data: { user }, error } = await supabase.auth.getUser()
    
    console.log('👤 User result:', user ? `${user.email} (${user.id})` : 'No user')
    console.log('❌ Auth error:', error?.message || 'None')

    // If we have a user, get their profile
    let profile = null
    let profileError = null
    let serviceRoleProfile = null
    if (user) {
      // Try with regular client (uses RLS)
      const { data: profileData, error: profileErr } = await supabase
        .from('profiles')
        .select('subscription_tier, role, name, subscription_status')
        .eq('id', user.id)
        .single()

      profile = profileData
      profileError = profileErr
      console.log('📋 Profile:', profile)
      console.log('❌ Profile error:', profileErr?.message || 'None')
      console.log('❌ Profile error details:', profileErr)

      // Also try with service role (bypasses RLS) to see if profile exists
      try {
        const { createClient } = await import('@supabase/supabase-js')
        const serviceClient = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        )

        const { data: serviceData } = await serviceClient
          .from('profiles')
          .select('subscription_tier, role, name, subscription_status')
          .eq('id', user.id)
          .single()

        serviceRoleProfile = serviceData
      } catch (err) {
        console.log('Service role query failed:', err)
      }
    }

    return NextResponse.json({
      authenticated: !!user,
      user: user ? {
        id: user.id,
        email: user.email,
        created_at: user.created_at
      } : null,
      profile,
      serviceRoleProfile,
      profileError: profileError ? {
        message: profileError.message,
        code: profileError.code,
        details: profileError.details,
        hint: profileError.hint
      } : null,
      cookies: cookies.map(c => ({ name: c.name, value: c.value.substring(0, 20) + '...' })),
      error: error?.message || null
    })

  } catch (error) {
    console.error('🚨 Debug auth error:', error)
    return NextResponse.json({
      authenticated: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      cookies: []
    })
  }
}
