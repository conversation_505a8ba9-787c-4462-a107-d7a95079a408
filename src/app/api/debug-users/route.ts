import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = createServiceClient()

    console.log('Checking test users...')

    // Check if users exist
    const testEmails = ['<EMAIL>', '<EMAIL>']
    const userResults = []

    try {
      const { data: allUsers, error } = await supabase.auth.admin.listUsers()

      if (error) {
        console.error('Error listing users:', error)
        return NextResponse.json({
          success: false,
          error: error.message
        }, { status: 500 })
      }

      for (const email of testEmails) {
        const user = allUsers.users.find(u => u.email === email)

        if (user) {
          userResults.push({
            email,
            exists: true,
            id: user.id,
            created_at: user.created_at,
            email_confirmed_at: user.email_confirmed_at,
            last_sign_in_at: user.last_sign_in_at
          })
        } else {
          userResults.push({
            email,
            exists: false,
            error: 'User not found'
          })
        }
      }
    } catch (err) {
      console.error('Exception listing users:', err)
      return NextResponse.json({
        success: false,
        error: err instanceof Error ? err.message : 'Unknown error'
      }, { status: 500 })
    }

    // Also check profiles table
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, name, first_time_user')
      .in('email', testEmails)

    return NextResponse.json({
      success: true,
      users: userResults,
      profiles: profiles || [],
      profilesError: profilesError?.message
    })

  } catch (error) {
    console.error('Debug users error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
