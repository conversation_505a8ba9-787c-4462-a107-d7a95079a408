import { NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const envCheck = {
      // Auth
      NEXTAUTH_URL: !!process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET: !!process.env.NEXTAUTH_SECRET,

      // Supabase
      NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,

      // Actual values for debugging
      SUPABASE_URL_VALUE: process.env.NEXT_PUBLIC_SUPABASE_URL,
      MUNICIPAL_API_URL_VALUE: process.env.MUNICIPAL_API_URL,
      
      // Stripe
      STRIPE_SECRET_KEY: !!process.env.STRIPE_SECRET_KEY,
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
      STRIPE_WEBHOOK_SECRET: !!process.env.STRIPE_WEBHOOK_SECRET,
      
      // Municipal API
      MUNICIPAL_API_KEY: !!process.env.MUNICIPAL_API_KEY,
      MUNICIPAL_API_URL: !!process.env.MUNICIPAL_API_URL,
      
      // Other
      NODE_ENV: process.env.NODE_ENV,
      VERCEL_ENV: process.env.VERCEL_ENV,
    }

    // Check for critical missing variables
    const missing = Object.entries(envCheck)
      .filter(([key, value]) => !value && key !== 'NODE_ENV' && key !== 'VERCEL_ENV')
      .map(([key]) => key)

    return NextResponse.json({
      status: missing.length === 0 ? 'OK' : 'MISSING_VARS',
      environment: envCheck,
      missing_variables: missing,
      critical_missing: missing.filter(key => 
        ['NEXTAUTH_URL', 'STRIPE_SECRET_KEY', 'MUNICIPAL_API_KEY'].includes(key)
      )
    })
  } catch (error) {
    return NextResponse.json({
      status: 'ERROR',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
