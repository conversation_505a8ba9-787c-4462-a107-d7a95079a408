import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

/**
 * Development-only auth helper
 * Automatically signs in a test user for easier development
 */

export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 })
  }

  try {
    const supabase = await createServerClient()

    // Try to sign in with real test user
    let { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Next7216@74431'
    })

    if (error && error.message.includes('Invalid login credentials')) {
      console.log('Test user not found, creating...')

      // Create the test user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'Next7216@74431',
        options: {
          data: {
            full_name: '<PERSON>'
          }
        }
      })

      if (signUpError) {
        console.error('Failed to create test user:', signUpError)
        return NextResponse.json({
          success: false,
          error: `Failed to create test user: ${signUpError.message}`
        }, { status: 400 })
      }

      // Create profile
      if (signUpData.user) {
        await supabase.from('profiles').upsert({
          id: signUpData.user.id,
          email: '<EMAIL>',
          full_name: 'Brandon Allen',
          subscription_tier: 'pro',
          role: 'user'
        })
      }

      // @ts-ignore - Dev auth type compatibility
      data = signUpData
    } else if (error) {
      console.error('Dev auth error:', error)
      return NextResponse.json({
        success: false,
        error: error.message
      }, { status: 400 })
    }

    console.log('✅ Dev auth successful:', data.user?.email)

    return NextResponse.json({
      success: true,
      user: {
        id: data.user?.id,
        email: data.user?.email
      }
    })

  } catch (error) {
    console.error('Dev auth exception:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

export async function GET() {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 })
  }

  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    return NextResponse.json({
      authenticated: !!user,
      user: user ? {
        id: user.id,
        email: user.email
      } : null
    })

  } catch (error) {
    console.error('Dev auth check error:', error)
    return NextResponse.json({
      authenticated: false,
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
