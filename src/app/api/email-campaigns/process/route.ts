import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { sendAbandonmentNudgeEmail, sendUpgradeReminderEmail } from '@/lib/email/sender'
import crypto from 'crypto'

// This endpoint processes scheduled email campaigns
// It should be called by a cron job or scheduled task

export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron job)
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.CRON_SECRET || 'dev-secret'
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = await createServerClient()

    // Get pending email campaigns
    const { data: campaigns, error: campaignsError } = await supabase.rpc('get_pending_email_campaigns')

    if (campaignsError) {
      console.error('Error fetching pending campaigns:', campaignsError)
      return NextResponse.json(
        { error: 'Failed to fetch pending campaigns' },
        { status: 500 }
      )
    }

    if (!campaigns || campaigns.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No pending campaigns to process',
        processed: 0
      })
    }

    let processed = 0
    let errors = 0

    // Process each campaign
    for (const campaign of campaigns) {
      try {
        // Generate unsubscribe token
        const unsubscribeToken = crypto
          .createHash('sha256')
          .update(`${campaign.user_id}-${campaign.campaign_id}-${process.env.NEXTAUTH_SECRET}`)
          .digest('hex')

        let emailSent = false

        // Send email based on campaign type
        switch (campaign.campaign_type) {
          case 'abandonment_nudge':
            await sendAbandonmentNudgeEmail(
              campaign.user_email,
              campaign.rule_type || 'project',
              campaign.address || 'your property',
              unsubscribeToken,
              extractNameFromEmail(campaign.user_email)
            )
            emailSent = true
            break

          case 'upgrade_reminder':
            await sendUpgradeReminderEmail(
              campaign.user_email,
              campaign.rule_type || 'project',
              unsubscribeToken,
              extractNameFromEmail(campaign.user_email)
            )
            emailSent = true
            break

          default:
            console.warn(`Unknown campaign type: ${campaign.campaign_type}`)
            break
        }

        if (emailSent) {
          // Mark campaign as sent
          await supabase.rpc('mark_email_campaign_sent', {
            campaign_id_param: campaign.campaign_id,
            success: true
          })
          processed++
        } else {
          // Mark as failed
          await supabase.rpc('mark_email_campaign_sent', {
            campaign_id_param: campaign.campaign_id,
            success: false,
            error_msg: 'Unknown campaign type'
          })
          errors++
        }
      } catch (emailError) {
        console.error(`Error sending email for campaign ${campaign.campaign_id}:`, emailError)
        
        // Mark campaign as failed
        await supabase.rpc('mark_email_campaign_sent', {
          campaign_id_param: campaign.campaign_id,
          success: false,
          error_msg: emailError instanceof Error ? emailError.message : 'Unknown error'
        })
        errors++
      }
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${processed} campaigns successfully, ${errors} errors`,
      processed,
      errors,
      totalCampaigns: campaigns.length
    })
  } catch (error) {
    console.error('Error in POST /api/email-campaigns/process:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to extract name from email
function extractNameFromEmail(email: string): string {
  const localPart = email.split('@')[0]
  // Convert common patterns like john.doe or john_doe to John Doe
  return localPart
    .split(/[._-]/)
    .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
    .join(' ')
}
