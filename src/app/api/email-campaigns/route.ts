import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Validation schemas
const trackSearchSchema = z.object({
  action: z.literal('track_search'),
  ruleType: z.string().min(1, 'Rule type is required'),
  address: z.string().min(1, 'Address is required')
})

const scheduleNudgeSchema = z.object({
  action: z.literal('schedule_nudge'),
  ruleType: z.string().min(1, 'Rule type is required'),
  address: z.string().min(1, 'Address is required'),
  delayHours: z.number().min(1).max(168).optional() // 1 hour to 1 week
})

const updatePreferencesSchema = z.object({
  action: z.literal('update_preferences'),
  preferences: z.object({
    marketing: z.boolean().optional(),
    nudges: z.boolean().optional(),
    updates: z.boolean().optional()
  })
})

// GET /api/email-campaigns - Get user's email campaign data
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's email preferences and campaign history
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('email_preferences, last_search_without_upgrade, last_search_rule_type, last_search_address')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('Error fetching profile:', profileError)
      return NextResponse.json(
        { error: 'Failed to fetch profile' },
        { status: 500 }
      )
    }

    // Get recent email campaigns
    const { data: campaigns, error: campaignsError } = await supabase
      .from('email_campaigns')
      .select(`
        id,
        campaign_type,
        rule_type,
        address,
        scheduled_for,
        sent_at,
        opened_at,
        clicked_at,
        status,
        created_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(20)

    if (campaignsError) {
      console.error('Error fetching campaigns:', campaignsError)
      return NextResponse.json(
        { error: 'Failed to fetch campaigns' },
        { status: 500 }
      )
    }

    // Calculate campaign stats
    const totalCampaigns = campaigns?.length || 0
    const sentCampaigns = campaigns?.filter(c => c.sent_at).length || 0
    const openedCampaigns = campaigns?.filter(c => c.opened_at).length || 0
    const clickedCampaigns = campaigns?.filter(c => c.clicked_at).length || 0

    return NextResponse.json({
      success: true,
      data: {
        emailPreferences: profile.email_preferences || {
          marketing: true,
          nudges: true,
          updates: true
        },
        lastSearch: {
          timestamp: profile.last_search_without_upgrade,
          ruleType: profile.last_search_rule_type,
          address: profile.last_search_address
        },
        campaignStats: {
          total: totalCampaigns,
          sent: sentCampaigns,
          opened: openedCampaigns,
          clicked: clickedCampaigns,
          openRate: sentCampaigns > 0 ? Math.round((openedCampaigns / sentCampaigns) * 100) : 0,
          clickRate: sentCampaigns > 0 ? Math.round((clickedCampaigns / sentCampaigns) * 100) : 0
        },
        recentCampaigns: campaigns?.map(c => ({
          id: c.id,
          type: c.campaign_type,
          ruleType: c.rule_type,
          address: c.address,
          scheduledFor: c.scheduled_for,
          sentAt: c.sent_at,
          openedAt: c.opened_at,
          clickedAt: c.clicked_at,
          status: c.status,
          createdAt: c.created_at
        }))
      }
    })
  } catch (error) {
    console.error('Error in GET /api/email-campaigns:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/email-campaigns - Handle email campaign actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Handle track search action
    if (body.action === 'track_search') {
      const validation = trackSearchSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { ruleType, address } = validation.data

      // Track the search
      const { error } = await supabase.rpc('track_search_without_upgrade', {
        user_id_param: user.id,
        rule_type_param: ruleType,
        address_param: address
      })

      if (error) {
        console.error('Error tracking search:', error)
        return NextResponse.json(
          { error: 'Failed to track search' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Search tracked successfully'
      })
    }

    // Handle schedule nudge action
    if (body.action === 'schedule_nudge') {
      const validation = scheduleNudgeSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { ruleType, address, delayHours = 36 } = validation.data

      // Schedule the nudge email
      const { data: campaignId, error } = await supabase.rpc('schedule_abandonment_nudge', {
        user_id_param: user.id,
        rule_type_param: ruleType,
        address_param: address,
        delay_hours: delayHours
      })

      if (error) {
        console.error('Error scheduling nudge:', error)
        return NextResponse.json(
          { error: 'Failed to schedule nudge' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        campaignId,
        message: campaignId ? 'Nudge email scheduled successfully' : 'User not eligible for nudge emails'
      })
    }

    // Handle update preferences action
    if (body.action === 'update_preferences') {
      const validation = updatePreferencesSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { preferences } = validation.data

      // Update email preferences
      const { error } = await supabase.rpc('update_email_preferences', {
        user_id_param: user.id,
        preferences: preferences
      })

      if (error) {
        console.error('Error updating preferences:', error)
        return NextResponse.json(
          { error: 'Failed to update preferences' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Email preferences updated successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error in POST /api/email-campaigns:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
