import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/email-campaigns/track - Track email opens (pixel tracking)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const campaignId = searchParams.get('c')
    const type = searchParams.get('t') || 'open'

    if (!campaignId) {
      // Return a 1x1 transparent pixel even if no campaign ID
      return new Response(
        Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'),
        {
          headers: {
            'Content-Type': 'image/gif',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        }
      )
    }

    const supabase = await createServerClient()

    // Track the email open
    if (type === 'open') {
      await supabase.rpc('track_email_open', {
        campaign_id_param: campaignId
      })
    }

    // Return a 1x1 transparent pixel
    return new Response(
      Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'),
      {
        headers: {
          'Content-Type': 'image/gif',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    )
  } catch (error) {
    console.error('Error tracking email open:', error)
    
    // Still return a pixel even if tracking fails
    return new Response(
      Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64'),
      {
        headers: {
          'Content-Type': 'image/gif',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    )
  }
}

// POST /api/email-campaigns/track - Track email clicks
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { campaignId, url } = body

    if (!campaignId) {
      return NextResponse.json({ error: 'Campaign ID required' }, { status: 400 })
    }

    const supabase = await createServerClient()

    // Track the email click
    await supabase.rpc('track_email_click', {
      campaign_id_param: campaignId
    })

    // Return the redirect URL
    return NextResponse.json({
      success: true,
      redirectUrl: url || 'https://ordrly.ai'
    })
  } catch (error) {
    console.error('Error tracking email click:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
