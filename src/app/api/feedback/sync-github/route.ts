import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

interface GitHubIssue {
  title: string
  body: string
  labels: string[]
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()

    // Get feedback ID from request
    const { feedbackId } = await request.json()

    if (!feedbackId) {
      return NextResponse.json(
        { error: 'Feedback ID is required' },
        { status: 400 }
      )
    }

    // Fetch feedback details
    const { data: feedback, error: fetchError } = await supabase
      .from('feedback')
      .select(`
        *,
        profiles:user_id (email, name)
      `)
      .eq('id', feedbackId)
      .single()

    if (fetchError || !feedback) {
      return NextResponse.json(
        { error: 'Feedback not found' },
        { status: 404 }
      )
    }

    // Prepare GitHub issue data
    const issueData: GitHubIssue = {
      title: `[${feedback.category.toUpperCase()}] ${feedback.title}`,
      body: createIssueBody(feedback),
      labels: getLabelsFor<PERSON>ategory(feedback.category)
    }

    // Create GitHub issue
    const githubResponse = await createGitHubIssue(issueData)

    if (!githubResponse.success) {
      throw new Error(githubResponse.error || 'Failed to create GitHub issue')
    }

    // Update feedback with GitHub issue URL
    const { error: updateError } = await supabase
      .from('feedback')
      .update({
        github_issue_url: githubResponse.issueUrl,
        status: 'in_progress'
      })
      .eq('id', feedbackId)

    if (updateError) {
      console.error('Error updating feedback with GitHub URL:', updateError)
      // Don't fail the request if we can't update the feedback
    }

    return NextResponse.json({
      success: true,
      issueUrl: githubResponse.issueUrl,
      message: 'Feedback synced to GitHub successfully'
    })

  } catch (error) {
    console.error('Error syncing feedback to GitHub:', error)
    return NextResponse.json(
      { error: 'Failed to sync feedback to GitHub' },
      { status: 500 }
    )
  }
}

function createIssueBody(feedback: {
  category: string
  created_at: string
  description: string
  id: string
  profiles?: { name?: string; email?: string }
  metadata?: { user_agent?: string; url?: string }
}): string {
  const userInfo = feedback.profiles
  const metadata = feedback.metadata || {}

  return `## Feedback Details

**Category:** ${feedback.category}
**Submitted by:** ${userInfo?.name || 'Anonymous'} (${userInfo?.email || 'No email'})
**Submitted at:** ${new Date(feedback.created_at).toLocaleString()}

## Description

${feedback.description}

## Technical Details

- **User Agent:** ${metadata.user_agent || 'Not provided'}
- **Page URL:** ${metadata.url || 'Not provided'}
- **Feedback ID:** ${feedback.id}

---

*This issue was automatically created from user feedback submitted through the Ordrly platform.*`
}

function getLabelsForCategory(category: string): string[] {
  const baseLabels = ['user-feedback']

  switch (category) {
    case 'bug':
      return [...baseLabels, 'bug', 'needs-investigation']
    case 'feature_request':
      return [...baseLabels, 'enhancement', 'feature-request']
    case 'general':
      return [...baseLabels, 'question', 'general']
    default:
      return baseLabels
  }
}

async function createGitHubIssue(issueData: GitHubIssue): Promise<{ success: boolean; issueUrl?: string; error?: string }> {
  const githubToken = process.env.GITHUB_TOKEN
  const githubRepo = process.env.GITHUB_REPO // Format: "owner/repo"

  if (!githubToken || !githubRepo) {
    console.warn('GitHub integration not configured. Set GITHUB_TOKEN and GITHUB_REPO environment variables.')
    return {
      success: false,
      error: 'GitHub integration not configured'
    }
  }

  try {
    const response = await fetch(`https://api.github.com/repos/${githubRepo}/issues`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${githubToken}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json',
        'User-Agent': 'Ordrly-Feedback-Bot'
      },
      body: JSON.stringify(issueData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`GitHub API error: ${errorData.message || response.statusText}`)
    }

    const issue = await response.json()

    return {
      success: true,
      issueUrl: issue.html_url
    }
  } catch (error) {
    console.error('GitHub API error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Auto-sync endpoint that can be called by a webhook or cron job
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()

    // Get recent feedback that hasn't been synced to GitHub
    const { data: pendingFeedback, error } = await supabase
      .from('feedback')
      .select('id')
      .is('github_issue_url', null)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
      .limit(10)

    if (error) {
      throw error
    }

    const results = []

    for (const feedback of pendingFeedback || []) {
      try {
        const syncResponse = await fetch(`${request.nextUrl.origin}/api/feedback/sync-github`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ feedbackId: feedback.id })
        })

        const syncResult = await syncResponse.json()
        results.push({
          feedbackId: feedback.id,
          success: syncResult.success,
          error: syncResult.error
        })
      } catch (error) {
        results.push({
          feedbackId: feedback.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      success: true,
      processed: results.length,
      results
    })

  } catch (error) {
    console.error('Error in auto-sync:', error)
    return NextResponse.json(
      { error: 'Failed to auto-sync feedback' },
      { status: 500 }
    )
  }
}
