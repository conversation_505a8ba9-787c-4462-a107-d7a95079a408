import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabase = createServiceClient()

    console.log('🔧 Starting RLS security fixes...')

    // Step 1: Fix compliance_knowledge table - enable RLS
    console.log('1. Fixing compliance_knowledge table RLS...')
    const { error: complianceError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on compliance_knowledge table (has policies but RLS disabled)
        ALTER TABLE public.compliance_knowledge ENABLE ROW LEVEL SECURITY;
      `
    })

    if (complianceError) {
      console.error('Error fixing compliance_knowledge RLS:', complianceError)
      throw complianceError
    }

    console.log('✓ compliance_knowledge RLS enabled')

    // Step 2: Enable RLS on core user-specific tables
    console.log('2. Enabling RLS on user-specific tables...')
    const { error: userTablesError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on user-specific tables
        ALTER TABLE public.conversation_sessions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.trial_codes ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.municipal_api_usage ENABLE ROW LEVEL SECURITY;
      `
    })

    if (userTablesError) {
      console.error('Error enabling RLS on user tables:', userTablesError)
      throw userTablesError
    }

    console.log('✓ User-specific tables RLS enabled')

    // Step 3: Enable RLS on public read-only tables
    console.log('3. Enabling RLS on public read-only tables...')
    const { error: publicTablesError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on public read-only tables
        ALTER TABLE public.municipal_research_cache ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.municipal_sources ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.jurisdiction_hierarchy ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.jurisdiction_filters ENABLE ROW LEVEL SECURITY;
      `
    })

    if (publicTablesError) {
      console.error('Error enabling RLS on public tables:', publicTablesError)
      throw publicTablesError
    }

    console.log('✓ Public read-only tables RLS enabled')

    // Step 4: Enable RLS on system/admin tables
    console.log('4. Enabling RLS on system/admin tables...')
    const { error: systemTablesError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on system/admin tables
        ALTER TABLE public.source_verification_log ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.quality_alerts ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.source_quality_metrics ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.content_change_log ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.research_quality_metrics ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.municipal_research_analytics ENABLE ROW LEVEL SECURITY;
      `
    })

    if (systemTablesError) {
      console.error('Error enabling RLS on system tables:', systemTablesError)
      throw systemTablesError
    }

    console.log('✓ System/admin tables RLS enabled')

    // Step 5: Enable RLS on development/testing tables
    console.log('5. Enabling RLS on development/testing tables...')
    const { error: devTablesError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on development/testing tables
        ALTER TABLE public.confidence_thresholds ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.test_execution_results ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.research_sessions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.research_test_cases ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.quality_metrics ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.research_topics ENABLE ROW LEVEL SECURITY;
      `
    })

    if (devTablesError) {
      console.error('Error enabling RLS on dev tables:', devTablesError)
      throw devTablesError
    }

    console.log('✓ Development/testing tables RLS enabled')

    // Step 6: Handle spatial_ref_sys (PostGIS system table)
    console.log('6. Handling spatial_ref_sys table...')
    const { error: spatialError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on spatial_ref_sys and create public read policy
        ALTER TABLE public.spatial_ref_sys ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for public read access to spatial reference systems
        DROP POLICY IF EXISTS "Public read access to spatial reference systems" ON public.spatial_ref_sys;
        CREATE POLICY "Public read access to spatial reference systems" 
        ON public.spatial_ref_sys FOR SELECT USING (true);
      `
    })

    if (spatialError) {
      console.error('Error handling spatial_ref_sys:', spatialError)
      throw spatialError
    }

    console.log('✓ spatial_ref_sys RLS enabled with public read policy')

    return NextResponse.json({
      success: true,
      message: 'RLS security fixes applied successfully',
      details: {
        compliance_knowledge: 'RLS enabled',
        user_tables: 'RLS enabled',
        public_tables: 'RLS enabled',
        system_tables: 'RLS enabled',
        dev_tables: 'RLS enabled',
        spatial_ref_sys: 'RLS enabled with public read policy'
      }
    })

  } catch (error) {
    console.error('❌ RLS fixes failed:', error)
    return NextResponse.json({
      success: false,
      error: 'RLS fixes failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
