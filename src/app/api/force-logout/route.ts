import { NextRequest, NextResponse } from 'next/server'
import { createServerClient as createSSRServerClient } from '@supabase/ssr'

/**
 * Force logout endpoint to clear all authentication state
 */

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Force logout - clearing all auth state...')
    
    // Create Supabase client with proper cookie handling
    const supabase = createSSRServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // We'll handle cookie setting in the response
          },
        },
      }
    )

    // Force sign out
    await supabase.auth.signOut()
    
    // Create response that clears all Supabase cookies
    const response = NextResponse.json({ 
      success: true, 
      message: 'All authentication state cleared' 
    })
    
    // Clear all Supabase-related cookies
    const cookiesToClear = [
      'sb-qxiryfbdruydrofclmvz-auth-token',
      'sb-qxiryfbdruydrofclmvz-auth-token.0',
      'sb-qxiryfbdruydrofclmvz-auth-token.1',
      'sb-qxiryfbdruydrofclmvz-auth-token-code-verifier',
      'sb-api-auth-token.0',
      'sb-api-auth-token.1'
    ]
    
    cookiesToClear.forEach(cookieName => {
      response.cookies.set(cookieName, '', {
        expires: new Date(0),
        path: '/',
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })
    })
    
    console.log('✅ Force logout completed - all cookies cleared')
    
    return response

  } catch (error) {
    console.error('🚨 Force logout error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
