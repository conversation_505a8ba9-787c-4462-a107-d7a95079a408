import { NextRequest, NextResponse } from 'next/server'
import { ordrlyMunicipalAdapter } from '@/lib/services/ordrly-municipal-adapter'

/**
 * 🏥 HEALTH CHECK ENDPOINT
 *
 * Checks the health of:
 * - Ordrly API server
 * - Municipal API connection
 */

export async function GET(request: NextRequest) {
  const startTime = Date.now()

  try {
    // Check municipal API health
    const municipalHealth = await ordrlyMunicipalAdapter.healthCheck()

    const response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        ordrly: {
          status: 'healthy',
          responseTime: Date.now() - startTime
        },
        municipalApi: {
          status: municipalHealth.status,
          url: process.env.MUNICIPAL_API_URL || 'http://localhost:3001',
          details: municipalHealth.municipalApi
        }
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    const response = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      error: error instanceof Error ? error.message : 'Unknown error',
      services: {
        ordrly: {
          status: 'healthy',
          responseTime: Date.now() - startTime
        },
        municipalApi: {
          status: 'unhealthy',
          url: process.env.MUNICIPAL_API_URL || 'http://localhost:3001',
          error: error instanceof Error ? error.message : 'Connection failed'
        }
      }
    }

    return NextResponse.json(response, { status: 503 })
  }
}
