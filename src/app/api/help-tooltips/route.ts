import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

interface TooltipUpdateData {
  title?: string
  content?: string
  position?: string
  is_active?: boolean
  updated_at: string
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { searchParams } = new URL(request.url)
    
    const elementId = searchParams.get('element_id')

    let query = supabase
      .from('help_tooltips')
      .select('*')
      .eq('is_active', true)

    if (elementId) {
      query = query.eq('element_id', elementId)
    }

    const { data: tooltips, error } = await query

    if (error) {
      throw error
    }

    if (elementId) {
      const tooltip = tooltips?.[0] || null
      return NextResponse.json({
        success: true,
        tooltip
      })
    }

    return NextResponse.json({
      success: true,
      tooltips: tooltips || []
    })

  } catch (error) {
    console.error('Help tooltips fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch tooltips'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { element_id, title, content, position } = body

    if (!element_id || !title || !content) {
      return NextResponse.json({
        success: false,
        error: 'Element ID, title, and content are required'
      }, { status: 400 })
    }

    const tooltipData = {
      element_id,
      title,
      content,
      position: position || 'top'
    }

    const { data: tooltip, error } = await supabase
      .from('help_tooltips')
      .upsert(tooltipData, { onConflict: 'element_id' })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      tooltip
    })

  } catch (error) {
    console.error('Help tooltip create error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create tooltip'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { element_id, title, content, position, is_active } = body

    if (!element_id) {
      return NextResponse.json({
        success: false,
        error: 'Element ID is required'
      }, { status: 400 })
    }

    const updateData: TooltipUpdateData = {
      updated_at: new Date().toISOString()
    }

    if (title !== undefined) updateData.title = title
    if (content !== undefined) updateData.content = content
    if (position !== undefined) updateData.position = position
    if (is_active !== undefined) updateData.is_active = is_active

    const { data: tooltip, error } = await supabase
      .from('help_tooltips')
      .update(updateData)
      .eq('element_id', element_id)
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      tooltip
    })

  } catch (error) {
    console.error('Help tooltip update error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update tooltip'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const elementId = searchParams.get('element_id')

    if (!elementId) {
      return NextResponse.json({
        success: false,
        error: 'Element ID is required'
      }, { status: 400 })
    }

    const { error } = await supabase
      .from('help_tooltips')
      .delete()
      .eq('element_id', elementId)

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      message: 'Tooltip deleted successfully'
    })

  } catch (error) {
    console.error('Help tooltip delete error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete tooltip'
    }, { status: 500 })
  }
}
