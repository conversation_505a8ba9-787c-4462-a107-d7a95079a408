import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's conversations with message counts and preview messages
    const { data: conversations, error } = await supabase
      .from('chat_conversations')
      .select(`
        id,
        address,
        rule_type,
        jurisdiction_name,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching conversations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch conversations' },
        { status: 500 }
      )
    }

    // For each conversation, get message count and preview messages
    const conversationsWithDetails = await Promise.all(
      conversations.map(async (conversation) => {
        // Get message count
        const { count: messageCount } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', conversation.id)

        // Get first 2 messages for preview
        const { data: previewMessages } = await supabase
          .from('chat_messages')
          .select('id, role, content, created_at')
          .eq('conversation_id', conversation.id)
          .order('created_at', { ascending: true })
          .limit(2)

        // Get last message
        const { data: lastMessage } = await supabase
          .from('chat_messages')
          .select('id, role, content, created_at')
          .eq('conversation_id', conversation.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .single()

        return {
          ...conversation,
          message_count: messageCount || 0,
          preview_messages: previewMessages || [],
          last_message: lastMessage
        }
      })
    )

    return NextResponse.json({ 
      conversations: conversationsWithDetails,
      total: conversations.length 
    })

  } catch (error) {
    console.error('Chat conversations history error:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch conversation history',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
