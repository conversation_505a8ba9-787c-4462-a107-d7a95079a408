import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify search belongs to user and delete it
    const { data: deletedSearch, error } = await supabase
      .from('search_history')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error deleting search:', error)
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Search not found' },
          { status: 404 }
        )
      }
      return NextResponse.json(
        { error: 'Failed to delete search' },
        { status: 500 }
      )
    }

    if (!deletedSearch) {
      return NextResponse.json(
        { error: 'Search not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ 
      success: true,
      message: 'Search deleted successfully' 
    })

  } catch (error) {
    console.error('Delete search error:', error)
    return NextResponse.json(
      {
        error: 'Failed to delete search',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
