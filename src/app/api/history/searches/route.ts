import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

/**
 * GET /api/history/searches - Retrieve user's search history (Epic 9.1.2)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Fetch user's search history
    const { data: searchHistory, error } = await supabase
      .from('search_history')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching search history:', error)
      return NextResponse.json(
        { error: 'Failed to fetch search history' },
        { status: 500 }
      )
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    if (countError) {
      console.error('Error counting search history:', countError)
    }

    return NextResponse.json({
      searches: searchHistory || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })

  } catch (error) {
    console.error('Search history API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch search history' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/history/searches - Delete search history entries (Epic 9.3.1)
 */
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const searchId = searchParams.get('id')
    const deleteAll = searchParams.get('all') === 'true'

    if (deleteAll) {
      // Delete all search history for user
      const { error } = await supabase
        .from('search_history')
        .delete()
        .eq('user_id', user.id)

      if (error) {
        console.error('Error deleting all search history:', error)
        return NextResponse.json(
          { error: 'Failed to delete search history' },
          { status: 500 }
        )
      }

      return NextResponse.json({ message: 'All search history deleted successfully' })
    } else if (searchId) {
      // Delete specific search history entry
      const { error } = await supabase
        .from('search_history')
        .delete()
        .eq('id', searchId)
        .eq('user_id', user.id) // Ensure user can only delete their own records

      if (error) {
        console.error('Error deleting search history entry:', error)
        return NextResponse.json(
          { error: 'Failed to delete search history entry' },
          { status: 500 }
        )
      }

      return NextResponse.json({ message: 'Search history entry deleted successfully' })
    } else {
      return NextResponse.json(
        { error: 'Either id or all=true parameter is required' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Delete search history API error:', error)
    return NextResponse.json(
      { error: 'Failed to delete search history' },
      { status: 500 }
    )
  }
}
