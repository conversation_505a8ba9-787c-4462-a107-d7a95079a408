import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const lat = searchParams.get('lat')
  const lng = searchParams.get('lng')

  if (!lat || !lng) {
    return NextResponse.json({
      error: 'Latitude and longitude are required'
    }, { status: 400 })
  }

  try {
    const supabase = await createServerClient()

    const { data, error } = await supabase.rpc('get_jurisdictions', {
      lat: parseFloat(lat),
      lng: parseFloat(lng)
    })

    if (error) {
      console.error('Jurisdictions query error:', error)
      throw error
    }

    return NextResponse.json({ jurisdictions: data || [] })
  } catch (error) {
    console.error('Jurisdictions API error:', error)
    return NextResponse.json({
      error: 'Failed to fetch jurisdictions',
      jurisdictions: []
    }, { status: 500 })
  }
}
