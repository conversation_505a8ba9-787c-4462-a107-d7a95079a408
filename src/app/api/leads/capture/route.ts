import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// POST /api/leads/capture - Capture new leads from forms
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, source, campaign, name, phone, company, message } = body

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    const supabase = await createServerClient()

    // Create or update lead record
    const leadData = {
      email,
      name: name || null,
      phone: phone || null,
      company: company || null,
      source: source || 'website',
      campaign: campaign || 'organic',
      message: message || null,
      status: 'new',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Note: leads table already exists in database schema

    // Insert or update lead
    const { data: existingLead } = await supabase
      .from('leads')
      .select('id, email')
      .eq('email', email)
      .single()

    let result
    if (existingLead) {
      // Update existing lead
      const { data, error } = await supabase
        .from('leads')
        .update({
          ...leadData,
          status: 'updated',
          last_contact: new Date().toISOString()
        })
        .eq('email', email)
        .select()
        .single()

      result = { data, error }
    } else {
      // Create new lead
      const { data, error } = await supabase
        .from('leads')
        .insert(leadData)
        .select()
        .single()

      result = { data, error }
    }

    if (result.error) {
      console.error('Error saving lead:', result.error)
      return NextResponse.json(
        { error: 'Failed to save lead' },
        { status: 500 }
      )
    }

    // Trigger lead notification (mock for now)
    await triggerLeadNotification(leadData)

    return NextResponse.json({
      success: true,
      message: 'Lead captured successfully',
      leadId: result.data.id
    })
  } catch (error) {
    console.error('Error in POST /api/leads/capture:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/leads/capture - Get leads (admin only)
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const source = searchParams.get('source')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query
    let query = supabase
      .from('leads')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status) {
      query = query.eq('status', status)
    }
    if (source) {
      query = query.eq('source', source)
    }

    const { data: leads, error } = await query

    if (error) {
      console.error('Error fetching leads:', error)
      return NextResponse.json(
        { error: 'Failed to fetch leads' },
        { status: 500 }
      )
    }

    // Get total count
    let countQuery = supabase
      .from('leads')
      .select('*', { count: 'exact', head: true })

    if (status) {
      countQuery = countQuery.eq('status', status)
    }
    if (source) {
      countQuery = countQuery.eq('source', source)
    }

    const { count } = await countQuery

    return NextResponse.json({
      success: true,
      leads: leads || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })
  } catch (error) {
    console.error('Error in GET /api/leads/capture:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Note: createLeadsTableIfNotExists function removed as leads table already exists in database schema

async function triggerLeadNotification(leadData: Record<string, unknown>) {
  try {
    // In a real implementation, this would:
    // 1. Send notification email to sales team
    // 2. Add to CRM system
    // 3. Trigger marketing automation
    // 4. Log to analytics

    console.log('New lead captured:', {
      email: leadData.email,
      source: leadData.source,
      campaign: leadData.campaign
    })

    // Mock notification logic
    return true
  } catch (error) {
    console.error('Error sending lead notification:', error)
    return false
  }
}
