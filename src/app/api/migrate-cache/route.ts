import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    console.log('Creating ordinance cache table manually...')
    const supabase = await createServerClient()

    // First, try to create the table using direct SQL
    const { error: createError } = await supabase
      .from('ordinance_cache')
      .select('id')
      .limit(1)

    if (createError && createError.message.includes('does not exist')) {
      console.log('Table does not exist, need to create it manually')

      // Since we can't use exec_sql, let's try a different approach
      // We'll create a simple test insert that will fail but tell us about the table
      const { error: testError } = await supabase
        .from('ordinance_cache')
        .insert({
          jurisdiction_name: 'test',
          rule_type: 'test',
          ordinance_data: {},
          expires_at: new Date().toISOString()
        })

      console.log('Test insert error:', testError)

      return NextResponse.json({
        success: false,
        message: 'Table does not exist and cannot be created via API',
        error: createError.message,
        instructions: 'Please create the table manually in Supabase dashboard with this SQL:\n\nCREATE TABLE public.ordinance_cache (\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n  jurisdiction_name TEXT NOT NULL,\n  rule_type TEXT NOT NULL,\n  ordinance_data JSONB NOT NULL,\n  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,\n  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  UNIQUE(jurisdiction_name, rule_type)\n);'
      })
    }

    // If we get here, table exists, let's insert the Georgetown data
    const georgetownFenceData = {
      jurisdiction_name: 'Georgetown Charter Township',
      rule_type: 'fence',
      ordinance_data: {
        content: "Georgetown Charter Township fence regulations: Maximum 3 feet in front setback areas, 6 feet in other areas. Front setback fences up to 4 feet permitted if they don't obscure traffic visibility (Zoning Administrator approval). Double frontage lots: 6-foot rear fences allowed with 5-foot setback from rear property line. No barbed wire in residential zones (LDR, MDR, LMR, MHP). Security fences in non-residential zones may extend to 7 feet with barbed arm.",
        citations: [
          { section: "Section 3.8", title: "Fences - Georgetown Charter Township Zoning Ordinance" },
          { section: "Revised 10/14/2013", title: "Security Fence Provisions" },
          { section: "Revised 6/25/2001", title: "Barbed Wire Restrictions" }
        ],
        confidence_score: 0.95,
        source_url: "https://georgetown.municipalcodeonline.com/book?type=ordinances#name=Sec_3.8_FENCES",
        zoning_info: {
          allowedZones: ["All zoning districts (LDR, MDR, LMR, MHP, Commercial, Industrial)"],
          restrictions: ["3 feet max in front setback", "6 feet max in other areas", "4 feet max in front with visibility approval", "No barbed wire in residential zones", "Double frontage: 5-foot rear setback required"]
        },
        permit_info: {
          required: false,
          note: "No permit required for standard fences. Zoning Administrator approval needed for 4-foot front setback fences.",
          office: "Georgetown Charter Township Building & Zoning Department",
          phone: "(*************",
          address: "1515 Baldwin Street, Jenison, MI 49429"
        }
      },
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    }

    const { error: insertError } = await supabase
      .from('ordinance_cache')
      .upsert(georgetownFenceData, {
        onConflict: 'jurisdiction_name,rule_type'
      })

    if (insertError) {
      console.error('Insert error:', insertError)
    } else {
      console.log('✓ Georgetown Charter Township fence data inserted')
    }

    return NextResponse.json({
      success: true,
      message: 'Table exists and data inserted',
      insertError: insertError?.message || null
    })

  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json({
      error: 'Failed to migrate cache',
      details: error
    }, { status: 500 })
  }
}
