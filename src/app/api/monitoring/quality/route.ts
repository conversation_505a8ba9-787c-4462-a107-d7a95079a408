/**
 * Quality Monitoring API Endpoint
 * Provides real-time quality metrics and alerts for the research system
 * Part of Phase 5: Quality Assurance & Monitoring
 */

import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { ResearchQualityMonitor } from '@/lib/monitoring/research-quality-monitor'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeframe = searchParams.get('timeframe') as 'hour' | 'day' | 'week' || 'day'
    const includeHistory = searchParams.get('history') === 'true'
    const includeAlerts = searchParams.get('alerts') === 'true'

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user has admin access (you may want to implement proper role checking)
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'free'
    
    // For now, allow pro+ users to access monitoring
    if (userTier === 'free') {
      return NextResponse.json(
        { error: 'Monitoring access requires Pro tier or higher' },
        { status: 403 }
      )
    }

    const monitor = new ResearchQualityMonitor(supabase)

    // Collect current metrics
    console.log(`📊 Collecting quality metrics for timeframe: ${timeframe}`)
    const currentMetrics = await monitor.collectQualityMetrics(timeframe)

    const response: any = {
      current_metrics: currentMetrics,
      thresholds: monitor.getThresholds(),
      status: determineSystemStatus(currentMetrics, monitor.getThresholds())
    }

    // Include historical data if requested
    if (includeHistory) {
      console.log('📈 Including metrics history')
      const history = await monitor.getMetricsHistory(7) // Last 7 days
      response.history = history
    }

    // Include active alerts if requested
    if (includeAlerts) {
      console.log('🚨 Including active alerts')
      const alerts = await monitor.getActiveAlerts()
      response.active_alerts = alerts
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Quality monitoring API error:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch quality metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, alert_id, thresholds } = await request.json()

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check admin access
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'trial'

    if (userTier === 'trial') {
      return NextResponse.json(
        { error: 'Monitoring access requires Starter tier or higher' },
        { status: 403 }
      )
    }

    const monitor = new ResearchQualityMonitor(supabase)

    switch (action) {
      case 'resolve_alert':
        if (!alert_id) {
          return NextResponse.json(
            { error: 'alert_id is required for resolve_alert action' },
            { status: 400 }
          )
        }

        const resolved = await monitor.resolveAlert(alert_id)
        if (resolved) {
          console.log(`✅ Resolved alert: ${alert_id}`)
          return NextResponse.json({ success: true, message: 'Alert resolved' })
        } else {
          return NextResponse.json(
            { error: 'Failed to resolve alert' },
            { status: 500 }
          )
        }

      case 'update_thresholds':
        if (!thresholds) {
          return NextResponse.json(
            { error: 'thresholds object is required for update_thresholds action' },
            { status: 400 }
          )
        }

        monitor.updateThresholds(thresholds)
        console.log('⚙️ Updated quality thresholds:', thresholds)
        return NextResponse.json({ 
          success: true, 
          message: 'Thresholds updated',
          new_thresholds: monitor.getThresholds()
        })

      case 'trigger_collection':
        console.log('🔄 Manually triggering metrics collection')
        const metrics = await monitor.collectQualityMetrics('day')
        return NextResponse.json({
          success: true,
          message: 'Metrics collection triggered',
          metrics
        })

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Quality monitoring POST error:', error)
    return NextResponse.json(
      {
        error: 'Failed to process monitoring action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Determine overall system status based on metrics and thresholds
 */
function determineSystemStatus(metrics: any, thresholds: any): {
  status: 'healthy' | 'warning' | 'critical'
  message: string
  issues: string[]
} {
  const issues: string[] = []
  let status: 'healthy' | 'warning' | 'critical' = 'healthy'

  // Check critical thresholds
  if (metrics.accuracy_metrics.pass_rate < thresholds.min_pass_rate) {
    issues.push(`Pass rate below threshold: ${(metrics.accuracy_metrics.pass_rate * 100).toFixed(1)}%`)
    status = 'critical'
  }

  if (metrics.performance_metrics.error_rate > thresholds.max_error_rate) {
    issues.push(`Error rate above threshold: ${(metrics.performance_metrics.error_rate * 100).toFixed(1)}%`)
    if (status !== 'critical') status = 'critical'
  }

  // Check warning thresholds
  if (metrics.accuracy_metrics.average_confidence < thresholds.min_average_confidence) {
    issues.push(`Average confidence below threshold: ${(metrics.accuracy_metrics.average_confidence * 100).toFixed(1)}%`)
    if (status === 'healthy') status = 'warning'
  }

  if (metrics.performance_metrics.average_response_time > thresholds.max_response_time) {
    issues.push(`Response time above threshold: ${metrics.performance_metrics.average_response_time}ms`)
    if (status === 'healthy') status = 'warning'
  }

  if (metrics.performance_metrics.cache_hit_rate < thresholds.min_cache_hit_rate) {
    issues.push(`Cache hit rate below threshold: ${(metrics.performance_metrics.cache_hit_rate * 100).toFixed(1)}%`)
    if (status === 'healthy') status = 'warning'
  }

  // Determine message
  let message: string
  switch (status) {
    case 'healthy':
      message = 'All systems operating within normal parameters'
      break
    case 'warning':
      message = `${issues.length} performance issue${issues.length > 1 ? 's' : ''} detected`
      break
    case 'critical':
      message = `${issues.length} critical issue${issues.length > 1 ? 's' : ''} requiring immediate attention`
      break
  }

  return { status, message, issues }
}

/**
 * Get quality monitoring dashboard data (internal helper function)
 */
async function getQualityDashboard(timeframe: 'hour' | 'day' | 'week' = 'day') {
  const supabase = await createServerClient()
  const monitor = new ResearchQualityMonitor(supabase)

  try {
    const [currentMetrics, history, alerts] = await Promise.all([
      monitor.collectQualityMetrics(timeframe),
      monitor.getMetricsHistory(7),
      monitor.getActiveAlerts()
    ])

    const thresholds = monitor.getThresholds()
    const status = determineSystemStatus(currentMetrics, thresholds)

    return {
      current_metrics: currentMetrics,
      history,
      active_alerts: alerts,
      thresholds,
      status,
      summary: {
        research_level_accuracy: currentMetrics.accuracy_metrics.pass_rate >= 0.99,
        confidence_score: currentMetrics.accuracy_metrics.average_confidence,
        response_time: currentMetrics.performance_metrics.average_response_time,
        cache_efficiency: currentMetrics.performance_metrics.cache_hit_rate,
        data_quality_score: (
          currentMetrics.data_quality.sources_with_links +
          currentMetrics.data_quality.verified_sources +
          currentMetrics.data_quality.public_domain_ratio +
          currentMetrics.data_quality.legal_citation_coverage
        ) / 4
      }
    }
  } catch (error) {
    console.error('Error getting quality dashboard:', error)
    throw error
  }
}
