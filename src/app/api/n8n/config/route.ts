import { NextResponse } from 'next/server'

// GET /api/n8n/config - Secure config endpoint for n8n workflows
export async function GET(request: Request) {
  try {
    // Simple security check - you can enhance this with API key validation
    const { searchParams } = new URL(request.url)
    const source = searchParams.get('source')
    
    if (source !== 'n8n-workflow') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      )
    }

    // Return configuration for n8n workflows
    const config = {
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        service_key: process.env.SUPABASE_SERVICE_ROLE_KEY
      },
      resend: {
        api_key: process.env.RESEND_API_KEY
      },
      site: {
        url: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.ordrly.ai'
      },
      api: {
        key: process.env.N8N_API_KEY
      }
    }

    // Validate that required keys exist
    if (!config.supabase.service_key || !config.resend.api_key) {
      return NextResponse.json(
        { error: 'Missing required environment variables' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      config
    })

  } catch (error) {
    console.error('Error in n8n config endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Optional: Add POST method for more secure token-based access
export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // You could implement token-based authentication here
    if (body.token !== process.env.N8N_CONFIG_TOKEN) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    // Same config as GET method
    const config = {
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        service_key: process.env.SUPABASE_SERVICE_ROLE_KEY
      },
      resend: {
        api_key: process.env.RESEND_API_KEY
      },
      site: {
        url: process.env.NEXT_PUBLIC_SITE_URL || 'https://ordrly.ai'
      }
    }

    return NextResponse.json({
      success: true,
      config
    })

  } catch (error) {
    console.error('Error in n8n config POST endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
