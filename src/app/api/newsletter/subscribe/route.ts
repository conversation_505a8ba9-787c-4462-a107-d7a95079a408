import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'

// POST /api/newsletter/subscribe - Subscribe to newsletter
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, name, preferences, source } = body

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 })
    }

    const supabase = await createServerClient()

    // Create newsletter subscription record
    const subscriptionData = {
      email,
      name: name || null,
      preferences: preferences || {
        marketing: true,
        updates: true,
        newsletters: true
      },
      source: source || 'website',
      status: 'active',
      subscribed_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Note: newsletter_subscribers table already exists in database schema

    // Check if email already exists
    const { data: existingSubscriber } = await supabase
      .from('newsletter_subscribers')
      .select('id, email, status')
      .eq('email', email)
      .single()

    let result
    if (existingSubscriber) {
      if (existingSubscriber.status === 'active') {
        return NextResponse.json({
          success: true,
          message: 'Already subscribed to newsletter',
          subscriberId: existingSubscriber.id
        })
      } else {
        // Reactivate subscription
        const { data, error } = await supabase
          .from('newsletter_subscribers')
          .update({
            status: 'active',
            preferences: subscriptionData.preferences,
            resubscribed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('email', email)
          .select()
          .single()

        result = { data, error }
      }
    } else {
      // Create new subscription
      const { data, error } = await supabase
        .from('newsletter_subscribers')
        .insert(subscriptionData)
        .select()
        .single()

      result = { data, error }
    }

    if (result.error) {
      console.error('Error saving newsletter subscription:', result.error)
      return NextResponse.json(
        { error: 'Failed to subscribe to newsletter' },
        { status: 500 }
      )
    }

    // Send welcome email (mock for now)
    await sendWelcomeEmail(email)

    // Also capture as lead if not already a user
    await captureAsLead(supabase, email, name, 'newsletter')

    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter',
      subscriberId: result.data.id
    })
  } catch (error) {
    console.error('Error in POST /api/newsletter/subscribe:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/newsletter/subscribe - Unsubscribe from newsletter
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    searchParams.get('token') // For secure unsubscribe links

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    const supabase = await createServerClient()

    // Update subscription status
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .update({
        status: 'unsubscribed',
        unsubscribed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('email', email)
      .select()
      .single()

    if (error) {
      console.error('Error unsubscribing from newsletter:', error)
      return NextResponse.json(
        { error: 'Failed to unsubscribe' },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully unsubscribed from newsletter'
    })
  } catch (error) {
    console.error('Error in DELETE /api/newsletter/subscribe:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/newsletter/subscribe - Get newsletter subscribers (admin only)
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'active'
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get subscribers
    const { data: subscribers, error } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching newsletter subscribers:', error)
      return NextResponse.json(
        { error: 'Failed to fetch subscribers' },
        { status: 500 }
      )
    }

    // Get total count
    const { count } = await supabase
      .from('newsletter_subscribers')
      .select('*', { count: 'exact', head: true })
      .eq('status', status)

    return NextResponse.json({
      success: true,
      subscribers: subscribers || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })
  } catch (error) {
    console.error('Error in GET /api/newsletter/subscribe:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Note: createNewsletterTableIfNotExists function removed as newsletter_subscribers table already exists in database schema

async function sendWelcomeEmail(email: string) {
  try {
    // In a real implementation, this would send an actual welcome email
    console.log('Sending welcome email to:', email)
    return true
  } catch (error) {
    console.error('Error sending welcome email:', error)
    return false
  }
}

async function captureAsLead(supabase: SupabaseClient, email: string, name?: string, source = 'newsletter') {
  try {
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single()

    if (existingUser) {
      return // User already exists, don't create lead
    }

    // Create lead record
    const leadData = {
      email,
      name: name || null,
      source,
      campaign: 'newsletter_signup',
      status: 'new',
      created_at: new Date().toISOString()
    }

    await supabase
      .from('leads')
      .insert(leadData)

  } catch (error) {
    console.error('Error capturing newsletter subscriber as lead:', error)
  }
}
