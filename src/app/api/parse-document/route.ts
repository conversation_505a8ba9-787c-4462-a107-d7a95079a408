import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { url, title } = await request.json()

    if (!url) {
      return NextResponse.json({
        error: 'URL is required'
      }, { status: 400 })
    }

    console.log(`📄 Parsing document: ${url}`)

    // Determine content type from URL
    const contentType = getContentType(url)

    let content = ''
    let error = null

    try {
      // Use the proven fetchDocumentContent function from compliance API
      const fetchedContent = await fetchDocumentContent(url)
      content = fetchedContent || 'No content available'
    } catch (fetchError) {
      error = `Failed to fetch content: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`
      console.error(`❌ Document parsing failed for ${url}:`, fetchError)
    }

    // Determine the actual content type based on what we processed
    let actualContentType = contentType
    if (content && (content.includes('[PDF Document') || content.includes('EXTRACTED TEXT:'))) {
      actualContentType = 'pdf'
    }

    return NextResponse.json({
      url,
      title: title || 'Unknown Document',
      content: content || '',
      contentType: actualContentType,
      error
    })

  } catch (error) {
    console.error('Document parsing error:', error)
    return NextResponse.json({
      error: `Document parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
}

function getContentType(url: string): 'html' | 'pdf' | 'text' {
  const urlLower = url.toLowerCase()

  if (urlLower.includes('.pdf') || urlLower.includes('pdf')) {
    return 'pdf'
  }

  if (urlLower.includes('.txt') || urlLower.includes('text')) {
    return 'text'
  }

  return 'html'
}

// Use the proven fetchDocumentContent function from compliance API
async function fetchDocumentContent(url: string): Promise<string | null> {
  const response = await fetch(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (compatible; Ordrly/1.0; +https://ordrly.com/bot)',
      'Accept': 'text/html,application/xhtml+xml,application/xml,application/pdf;q=0.9,*/*;q=0.8'
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}`)
  }

  const contentType = response.headers.get('content-type') || ''

  // Check if it's a PDF by content-type or URL
  const isPdf = contentType.includes('application/pdf') ||
                contentType.includes('pdf') ||
                url.toLowerCase().includes('.pdf')

  if (isPdf) {
    // Handle PDF content - attempt text extraction
    try {
      console.log(`📄 Attempting to parse PDF: ${url}`)
      const arrayBuffer = await response.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      // Try to import and use pdf2json
      const PDFParser = (await import('pdf2json')).default

      console.log(`📄 PDF buffer size: ${buffer.length} bytes`)

      // Create a promise-based wrapper for pdf2json
      const extractText = () => new Promise((resolve, reject) => {
        const pdfParser = new PDFParser()

        pdfParser.on('pdfParser_dataError', (errData) => {
          reject(new Error(errData.parserError instanceof Error ? errData.parserError.message : String(errData.parserError)))
        })

        pdfParser.on('pdfParser_dataReady', (pdfData) => {
          try {
            // Extract text from all pages
            let fullText = ''
            if (pdfData.Pages && Array.isArray(pdfData.Pages)) {
              for (const page of pdfData.Pages) {
                if (page.Texts && Array.isArray(page.Texts)) {
                  for (const textItem of page.Texts) {
                    if (textItem.R && Array.isArray(textItem.R)) {
                      for (const run of textItem.R) {
                        if (run.T) {
                          // Decode URI component to get readable text
                          fullText += decodeURIComponent(run.T) + ' '
                        }
                      }
                    }
                  }
                  fullText += '\n\n' // Add page break
                }
              }
            }
            resolve(fullText.trim())
          } catch (error) {
            reject(error)
          }
        })

        // Parse the PDF buffer
        pdfParser.parseBuffer(buffer)
      })

      const fullText = await extractText() as string
      console.log(`📄 PDF parsed successfully: ${fullText.length} characters`)

      if (fullText && fullText.length > 100) {
        return `[PDF Document - Text Successfully Extracted]

Title: ${url.split('/').pop() || 'Unknown PDF'}
URL: ${url}
Content-Type: ${contentType}
Size: ${buffer.length} bytes

EXTRACTED TEXT:
${fullText.length > 15000 ? fullText.substring(0, 15000) + '\n\n[Content truncated - document contains additional text]' : fullText}`
      } else {
        throw new Error('PDF text extraction returned empty or minimal content')
      }

    } catch (pdfError) {
      console.error(`❌ PDF parsing failed for ${url}:`, pdfError instanceof Error ? pdfError.message : 'Unknown error')

      // Fallback to enhanced metadata
      const contentLength = response.headers.get('content-length') || 'unknown'
      const fileName = url.split('/').pop() || 'Unknown PDF'

      return `[PDF Document - Text Extraction Failed]

Title: ${fileName}
URL: ${url}
Content-Type: ${contentType}
Size: ${contentLength} bytes
Error: ${pdfError instanceof Error ? pdfError.message : 'Unknown error'}

This PDF document could not be automatically parsed for text extraction. Common reasons:
- PDF contains scanned images without OCR text layer
- PDF is password protected or encrypted
- PDF uses complex formatting that prevents text extraction
- Technical parsing error (${pdfError instanceof Error ? pdfError.message : 'Unknown error'})

MANUAL REVIEW RECOMMENDED:
This PDF likely contains the complete ordinance text with specific requirements, measurements, and section numbers. For accurate compliance information, please:

1. Download the PDF directly from: ${url}
2. Review manually for specific ordinance sections
3. Look for section numbers, setback requirements, and permit procedures
4. Note any contact information for follow-up questions

The AI analysis will proceed with available HTML sources, but this PDF may contain the most authoritative information.`
    }
  } else {
    // Handle HTML content
    const content = await response.text()

    // Extract meaningful text content
    const cleanContent = content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()

    if (cleanContent.length > 100) {
      return cleanContent.substring(0, 15000) // Limit content size
    } else {
      throw new Error('HTML content too short')
    }
  }
}
