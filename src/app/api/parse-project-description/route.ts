import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import { getAIConfig, logAIUsage } from '@/lib/ai-config'



interface ParsedProject {
  projectType: string
  originalDescription: string
  confidence: number
  tags: string[]
  metadata: {
    dimensions?: string
    materials?: string[]
    location?: string
    specialRequirements?: string[]
  }
}

export async function POST(request: NextRequest) {
  try {
    const { description } = await request.json()

    if (!description || typeof description !== 'string') {
      return NextResponse.json(
        { error: 'Project description is required' },
        { status: 400 }
      )
    }

    if (description.length > 1000) {
      return NextResponse.json(
        { error: 'Project description too long (max 1000 characters)' },
        { status: 400 }
      )
    }

    // Check authentication and tier access
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's tier
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    const userTier = profile?.subscription_tier || 'free'

    // Check if user has access to custom input feature
    if (!hasFeatureAccess(userTier, 'enableCustomInput') || !isFeatureEnabled('CUSTOM_PROJECT_ENABLED')) {
      return NextResponse.json(
        {
          error: 'Custom project input requires Pro tier or higher',
          upgradeRequired: true,
          suggestedTier: 'pro'
        },
        { status: 403 }
      )
    }

    // Parse the project description with AI
    const parsedProject = await parseProjectWithAI(description)

    // Log feature usage
    await supabase.from('feature_usage').insert({
      user_id: user.id,
      feature_type: 'custom_project_input',
      metadata: {
        original_description: description,
        parsed_type: parsedProject.projectType,
        confidence: parsedProject.confidence
      }
    })

    return NextResponse.json(parsedProject)

  } catch (error) {
    console.error('Project description parsing error:', error)
    return NextResponse.json(
      {
        error: 'Failed to parse project description',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

async function parseProjectWithAI(description: string): Promise<ParsedProject> {
  try {
    // Validate OpenAI API key
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_key_here') {
      throw new Error('OpenAI API not configured')
    }

    const { OpenAI } = await import('openai')
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY })
    const aiConfig = getAIConfig()

    const prompt = `Analyze this project description and extract structured information:

"${description}"

Extract:
1. A clean, specific project type that accurately describes what the user wants to do
2. Confidence level (0.0 to 1.0)
3. Relevant tags and keywords
4. Specific details like dimensions, materials, location, special requirements

Return JSON in this exact format:
{
  "projectType": "specific_project_description (e.g., 'boiler replacement', 'tankless water heater installation', '6-foot privacy fence')",
  "confidence": 0.85,
  "tags": ["relevant", "keywords"],
  "metadata": {
    "dimensions": "extracted dimensions if mentioned",
    "materials": ["material1", "material2"],
    "location": "property location details if mentioned",
    "specialRequirements": ["requirement1", "requirement2"]
  }
}

IMPORTANT: Use the most specific and accurate project description possible. Don't force it into generic categories.
Examples:
- "I need to replace my old boiler" → "boiler replacement"
- "Installing a tankless water heater" → "tankless water heater installation"
- "Building a 6x8 shed in my backyard" → "6x8 shed construction"
- "Adding a 240V outlet for EV charging" → "240V electrical outlet installation"`

    logAIUsage(prompt, aiConfig, 'for project description parsing')

    const response = await openai.chat.completions.create({
      model: aiConfig.model,
      messages: [
        {
          role: 'system',
          content: 'You are an expert at analyzing construction and home improvement project descriptions. Extract structured information accurately and conservatively.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000
    })

    const aiResponse = response.choices[0]?.message?.content
    if (!aiResponse) {
      throw new Error('No response from AI parsing')
    }

    try {
      const parsed = JSON.parse(aiResponse)

      // Validate the response structure - no longer force into predefined categories
      if (!parsed.projectType || typeof parsed.projectType !== 'string') {
        // Fallback to cleaned description if AI parsing fails
        parsed.projectType = description.trim()
        parsed.confidence = 0.5
      }

      return {
        projectType: parsed.projectType,
        originalDescription: description,
        confidence: Math.max(0, Math.min(1, parsed.confidence || 0.5)),
        tags: Array.isArray(parsed.tags) ? parsed.tags : [],
        metadata: {
          dimensions: parsed.metadata?.dimensions || undefined,
          materials: Array.isArray(parsed.metadata?.materials) ? parsed.metadata.materials : [],
          location: parsed.metadata?.location || undefined,
          specialRequirements: Array.isArray(parsed.metadata?.specialRequirements) ? parsed.metadata.specialRequirements : []
        }
      }

    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError)
      // Fallback to simple keyword matching
      return createFallbackParsing(description)
    }

  } catch (error) {
    console.error('AI parsing failed:', error)
    // Fallback to simple keyword matching
    return createFallbackParsing(description)
  }
}

function createFallbackParsing(description: string): ParsedProject {
  const lowerDesc = description.toLowerCase()

  // Use the original description directly instead of forcing into categories
  const projectType = description.trim()

  // Extract basic information using simple patterns
  const dimensionMatch = lowerDesc.match(/(\d+[\s-]*(?:foot|feet|ft|'|inch|inches|"|meter|m)\s*(?:x|by|×)\s*\d+[\s-]*(?:foot|feet|ft|'|inch|inches|"|meter|m))/i)
  const materialKeywords = ['wood', 'vinyl', 'metal', 'steel', 'aluminum', 'concrete', 'brick', 'stone', 'composite']
  const foundMaterials = materialKeywords.filter(material => lowerDesc.includes(material))

  return {
    projectType: projectType,
    originalDescription: description,
    confidence: 0.6, // Lower confidence for fallback
    tags: foundMaterials,
    metadata: {
      dimensions: dimensionMatch ? dimensionMatch[1] : undefined,
      materials: foundMaterials,
      location: lowerDesc.includes('back') ? 'backyard' : lowerDesc.includes('front') ? 'front yard' : undefined,
      specialRequirements: []
    }
  }
}