import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'

interface SearchHistoryItem {
  address: string
  created_at: string
  rule_type?: string
}

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's search history to analyze frequent areas
    const { data: searchHistory, error: historyError } = await supabase
      .from('search_history')
      .select('address, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (historyError) {
      console.error('Error fetching search history:', historyError)
      return NextResponse.json({ areas: [] })
    }

    // Analyze frequent areas
    const areas = analyzeFrequentAreas(searchHistory || [])

    return NextResponse.json({ areas })

  } catch (error) {
    console.error('Frequent areas error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function analyzeFrequentAreas(searchHistory: SearchHistoryItem[]) {
  const locationCounts: Record<string, number> = {}
  const cityCounts: Record<string, number> = {}
  const stateCounts: Record<string, number> = {}

  searchHistory.forEach(search => {
    const addressParts = search.address.split(',').map(part => part.trim())
    
    if (addressParts.length >= 2) {
      // City (usually second to last part)
      const city = addressParts[addressParts.length - 2]
      if (city && city.length > 1) {
        cityCounts[city] = (cityCounts[city] || 0) + 1
      }
    }

    if (addressParts.length >= 1) {
      // State (usually last part)
      const state = addressParts[addressParts.length - 1]
      if (state && state.length > 1) {
        stateCounts[state] = (stateCounts[state] || 0) + 1
      }
    }

    // Full location (city, state)
    if (addressParts.length >= 2) {
      const location = `${addressParts[addressParts.length - 2]}, ${addressParts[addressParts.length - 1]}`
      locationCounts[location] = (locationCounts[location] || 0) + 1
    }
  })

  // Convert to arrays and sort by frequency
  const frequentLocations = Object.entries(locationCounts)
    .filter(([, count]) => count > 1) // Only include areas searched more than once
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 10) // Top 10 frequent areas
    .map(([location, count]) => ({
      name: location,
      count: count as number,
      type: 'location',
      last_searched: getLastSearchedDate(searchHistory, location)
    }))

  const frequentCities = Object.entries(cityCounts)
    .filter(([, count]) => count > 2) // Only include cities searched more than twice
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 5) // Top 5 frequent cities
    .map(([city, count]) => ({
      name: city,
      count: count as number,
      type: 'city',
      last_searched: getLastSearchedDateByCity(searchHistory, city)
    }))

  const frequentStates = Object.entries(stateCounts)
    .filter(([, count]) => count > 3) // Only include states searched more than 3 times
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 3) // Top 3 frequent states
    .map(([state, count]) => ({
      name: state,
      count: count as number,
      type: 'state',
      last_searched: getLastSearchedDateByState(searchHistory, state)
    }))

  // Combine and sort all areas by count
  const allAreas = [...frequentLocations, ...frequentCities, ...frequentStates]
    .sort((a, b) => b.count - a.count)
    .slice(0, 8) // Return top 8 overall

  return allAreas
}

function getLastSearchedDate(searchHistory: SearchHistoryItem[], location: string) {
  const search = searchHistory.find(s => s.address.includes(location))
  return search ? search.created_at : null
}

function getLastSearchedDateByCity(searchHistory: SearchHistoryItem[], city: string) {
  const search = searchHistory.find(s => s.address.includes(city))
  return search ? search.created_at : null
}

function getLastSearchedDateByState(searchHistory: SearchHistoryItem[], state: string) {
  const search = searchHistory.find(s => s.address.endsWith(state))
  return search ? search.created_at : null
}
