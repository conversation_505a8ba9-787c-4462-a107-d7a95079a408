import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'

interface SearchHistoryItem {
  address: string
  rule_type: string
  created_at: string
}

interface SavedSearch {
  id: string
  name: string
  address: string
}

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's search history to generate recommendations
    const { data: searchHistory, error: historyError } = await supabase
      .from('search_history')
      .select('address, rule_type, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(50)

    if (historyError) {
      console.error('Error fetching search history:', historyError)
      return NextResponse.json({ recommendations: [] })
    }

    // Get user's saved searches
    const { data: savedSearches, error: savedError } = await supabase
      .from('saved_searches')
      .select('id, name, address, rule_type, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(20)

    if (savedError) {
      console.error('Error fetching saved searches:', savedError)
    }

    // Generate recommendations based on user behavior
    const recommendations = generateRecommendations(searchHistory || [], savedSearches || [])

    return NextResponse.json({ recommendations })

  } catch (error) {
    console.error('Recommendations error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateRecommendations(searchHistory: SearchHistoryItem[], savedSearches: SavedSearch[]) {
  const recommendations: Array<{
    id: string
    title: string
    description: string
    type: string
    action_url: string
    priority: 'high' | 'medium' | 'low'
  }> = []

  // Analyze search patterns
  const ruleTypeCounts: Record<string, number> = {}
  const locationPatterns: Record<string, number> = {}
  
  searchHistory.forEach(search => {
    // Count rule types
    ruleTypeCounts[search.rule_type] = (ruleTypeCounts[search.rule_type] || 0) + 1
    
    // Extract location patterns (simplified)
    const location = search.address.split(',')[1]?.trim()
    if (location) {
      locationPatterns[location] = (locationPatterns[location] || 0) + 1
    }
  })

  // Most searched rule type recommendation
  const mostSearchedType = Object.entries(ruleTypeCounts)
    .sort(([,a], [,b]) => (b as number) - (a as number))[0]

  if (mostSearchedType && mostSearchedType[1] > 2) {
    recommendations.push({
      id: 'rule-type-focus',
      title: `Explore more ${mostSearchedType[0]} properties`,
      description: `You've searched for ${mostSearchedType[1]} ${mostSearchedType[0]} properties recently`,
      type: 'rule_type',
      action_url: `/search?rule_type=${mostSearchedType[0]}`,
      priority: 'high'
    })
  }

  // Frequent location recommendation
  const mostSearchedLocation = Object.entries(locationPatterns)
    .sort(([,a], [,b]) => (b as number) - (a as number))[0]

  if (mostSearchedLocation && mostSearchedLocation[1] > 1) {
    recommendations.push({
      id: 'location-focus',
      title: `Discover more properties in ${mostSearchedLocation[0]}`,
      description: `You've shown interest in this area with ${mostSearchedLocation[1]} searches`,
      type: 'location',
      action_url: `/search?location=${encodeURIComponent(mostSearchedLocation[0])}`,
      priority: 'medium'
    })
  }

  // Time-based recommendations
  const recentSearches = searchHistory.slice(0, 10)
  const hasRecentActivity = recentSearches.length > 0

  if (hasRecentActivity) {
    const lastSearchDate = new Date(recentSearches[0].created_at)
    const daysSinceLastSearch = Math.floor((Date.now() - lastSearchDate.getTime()) / (1000 * 60 * 60 * 24))

    if (daysSinceLastSearch > 7) {
      recommendations.push({
        id: 'return-reminder',
        title: 'Continue your property research',
        description: `It's been ${daysSinceLastSearch} days since your last search. Check for new listings!`,
        type: 'engagement',
        action_url: '/search',
        priority: 'low'
      })
    }
  }

  // Saved searches recommendations
  if (savedSearches.length > 0) {
    recommendations.push({
      id: 'saved-search-update',
      title: 'Check your saved searches for updates',
      description: `You have ${savedSearches.length} saved searches that might have new results`,
      type: 'saved_searches',
      action_url: '/saved-searches',
      priority: 'medium'
    })
  }

  // Diversification recommendations
  const uniqueRuleTypes = Object.keys(ruleTypeCounts)
  if (uniqueRuleTypes.length === 1 && searchHistory.length > 5) {
    const currentType = uniqueRuleTypes[0]
    const suggestedTypes = ['residential', 'commercial', 'industrial'].filter(type => type !== currentType)
    
    if (suggestedTypes.length > 0) {
      recommendations.push({
        id: 'diversify-search',
        title: `Explore ${suggestedTypes[0]} properties`,
        description: `Broaden your search beyond ${currentType} properties`,
        type: 'diversification',
        action_url: `/search?rule_type=${suggestedTypes[0]}`,
        priority: 'low'
      })
    }
  }

  // Trending areas (mock data for now)
  if (searchHistory.length > 0) {
    recommendations.push({
      id: 'trending-areas',
      title: 'Explore trending areas',
      description: 'Discover popular neighborhoods with high search activity',
      type: 'trending',
      action_url: '/search?trending=true',
      priority: 'low'
    })
  }

  // Sort by priority and return top 5
  const priorityOrder: Record<'high' | 'medium' | 'low', number> = { high: 3, medium: 2, low: 1 }
  return recommendations
    .sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority])
    .slice(0, 5)
}
