import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

interface ShortcutUpdateData {
  name?: string
  url?: string
  icon?: string
  position?: number
  updated_at: string
}

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: shortcuts, error } = await supabase
      .from('user_shortcuts')
      .select('*')
      .eq('user_id', user.id)
      .order('position', { ascending: true })

    if (error) {
      console.error('Error fetching shortcuts:', error)
      return NextResponse.json({ shortcuts: [] })
    }

    return NextResponse.json({ shortcuts: shortcuts || [] })

  } catch (error) {
    console.error('Shortcuts GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { name, url, icon } = await request.json()

    if (!name || !url) {
      return NextResponse.json(
        { error: 'Name and URL are required' },
        { status: 400 }
      )
    }

    // Get current max position
    const { data: maxPosition } = await supabase
      .from('user_shortcuts')
      .select('position')
      .eq('user_id', user.id)
      .order('position', { ascending: false })
      .limit(1)
      .single()

    const nextPosition = maxPosition ? maxPosition.position + 1 : 0

    const { data: shortcut, error } = await supabase
      .from('user_shortcuts')
      .insert({
        user_id: user.id,
        name,
        url,
        icon: icon || 'link',
        position: nextPosition
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating shortcut:', error)
      return NextResponse.json({ error: 'Failed to create shortcut' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      shortcut,
      message: 'Shortcut created successfully' 
    })

  } catch (error) {
    console.error('Shortcuts POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id, name, url, icon, position } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: 'Shortcut ID is required' },
        { status: 400 }
      )
    }

    const updateData: ShortcutUpdateData = { updated_at: new Date().toISOString() }
    
    if (name !== undefined) updateData.name = name
    if (url !== undefined) updateData.url = url
    if (icon !== undefined) updateData.icon = icon
    if (position !== undefined) updateData.position = position

    const { data: shortcut, error } = await supabase
      .from('user_shortcuts')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating shortcut:', error)
      return NextResponse.json({ error: 'Failed to update shortcut' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      shortcut,
      message: 'Shortcut updated successfully' 
    })

  } catch (error) {
    console.error('Shortcuts PATCH error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Shortcut ID is required' },
        { status: 400 }
      )
    }

    const { error } = await supabase
      .from('user_shortcuts')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting shortcut:', error)
      return NextResponse.json({ error: 'Failed to delete shortcut' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'Shortcut deleted successfully' 
    })

  } catch (error) {
    console.error('Shortcuts DELETE error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
