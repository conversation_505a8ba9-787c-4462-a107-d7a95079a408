import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get total searches
    const { count: totalSearches, error: totalError } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    if (totalError) {
      console.error('Error fetching total searches:', totalError)
    }

    // Get searches this week
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
    
    const { count: thisWeekSearches, error: weekError } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .gte('created_at', oneWeekAgo.toISOString())

    if (weekError) {
      console.error('Error fetching week searches:', weekError)
    }

    // Get searches this month
    const oneMonthAgo = new Date()
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
    
    const { count: thisMonthSearches, error: monthError } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .gte('created_at', oneMonthAgo.toISOString())

    if (monthError) {
      console.error('Error fetching month searches:', monthError)
    }

    // Get most searched rule type
    const { data: ruleTypeData, error: ruleTypeError } = await supabase
      .from('search_history')
      .select('rule_type')
      .eq('user_id', user.id)

    let mostSearchedType = 'residential' // default

    if (!ruleTypeError && ruleTypeData) {
      const ruleTypeCounts: Record<string, number> = {}
      ruleTypeData.forEach(item => {
        ruleTypeCounts[item.rule_type] = (ruleTypeCounts[item.rule_type] || 0) + 1
      })

      if (Object.keys(ruleTypeCounts).length > 0) {
        mostSearchedType = Object.entries(ruleTypeCounts)
          .sort(([,a], [,b]) => (b as number) - (a as number))[0][0]
      }
    }

    const stats = {
      total_searches: totalSearches || 0,
      this_week: thisWeekSearches || 0,
      this_month: thisMonthSearches || 0,
      most_searched_type: mostSearchedType
    }

    return NextResponse.json({ stats })

  } catch (error) {
    console.error('Stats error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
