import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user preferences
    const { data: preferences, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching preferences:', error)
      return NextResponse.json({ error: 'Failed to fetch preferences' }, { status: 500 })
    }

    // If no preferences exist, return defaults
    if (!preferences) {
      const defaultPreferences = {
        theme: 'light',
        notifications: {
          email: true,
          push: true,
          marketing: true
        },
        language: 'en',
        timezone: 'UTC',
        auto_save_searches: true,
        search_suggestions: true
      }

      return NextResponse.json({ preferences: defaultPreferences })
    }

    return NextResponse.json({ preferences })

  } catch (error) {
    console.error('Preferences GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const preferences = await request.json()

    // Validate preferences structure
    const validatedPreferences = {
      theme: preferences.theme || 'light',
      notifications: preferences.notifications || {
        email: true,
        push: true,
        marketing: true
      },
      language: preferences.language || 'en',
      timezone: preferences.timezone || 'UTC',
      auto_save_searches: preferences.auto_save_searches !== undefined ? preferences.auto_save_searches : true,
      search_suggestions: preferences.search_suggestions !== undefined ? preferences.search_suggestions : true
    }

    // Upsert user preferences
    const { data, error } = await supabase
      .from('user_preferences')
      .upsert({
        user_id: user.id,
        ...validatedPreferences,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      })
      .select()
      .single()

    if (error) {
      console.error('Error saving preferences:', error)
      return NextResponse.json({ error: 'Failed to save preferences' }, { status: 500 })
    }

    // Also update profile table for theme and notifications
    await supabase
      .from('profiles')
      .update({
        theme: validatedPreferences.theme,
        notification_preferences: validatedPreferences.notifications,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)

    return NextResponse.json({ 
      success: true, 
      preferences: data,
      message: 'Preferences saved successfully' 
    })

  } catch (error) {
    console.error('Preferences POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
