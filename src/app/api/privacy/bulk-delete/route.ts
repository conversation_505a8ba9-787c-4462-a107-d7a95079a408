import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { delete_type } = await request.json()

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Validate delete type
    if (!['searches', 'chats', 'all'].includes(delete_type)) {
      return NextResponse.json(
        { error: 'Invalid delete type' },
        { status: 400 }
      )
    }

    const deletedCounts = {
      searches: 0,
      conversations: 0,
      messages: 0
    }

    // Delete search history
    if (delete_type === 'searches' || delete_type === 'all') {
      const { count: searchCount, error: searchError } = await supabase
        .from('search_history')
        .delete({ count: 'exact' })
        .eq('user_id', user.id)

      if (searchError) {
        console.error('Error deleting search history:', searchError)
        return NextResponse.json(
          { error: 'Failed to delete search history' },
          { status: 500 }
        )
      }

      deletedCounts.searches = searchCount || 0
    }

    // Delete chat history
    if (delete_type === 'chats' || delete_type === 'all') {
      // First, get all conversation IDs for this user
      const { data: conversations, error: conversationFetchError } = await supabase
        .from('chat_conversations')
        .select('id')
        .eq('user_id', user.id)

      if (conversationFetchError) {
        console.error('Error fetching conversations:', conversationFetchError)
        return NextResponse.json(
          { error: 'Failed to fetch conversations' },
          { status: 500 }
        )
      }

      if (conversations && conversations.length > 0) {
        const conversationIds = conversations.map(c => c.id)

        // Delete all messages for these conversations
        const { count: messageCount, error: messageError } = await supabase
          .from('chat_messages')
          .delete({ count: 'exact' })
          .in('conversation_id', conversationIds)

        if (messageError) {
          console.error('Error deleting messages:', messageError)
          return NextResponse.json(
            { error: 'Failed to delete chat messages' },
            { status: 500 }
          )
        }

        deletedCounts.messages = messageCount || 0

        // Delete all conversations for this user
        const { count: conversationCount, error: conversationError } = await supabase
          .from('chat_conversations')
          .delete({ count: 'exact' })
          .eq('user_id', user.id)

        if (conversationError) {
          console.error('Error deleting conversations:', conversationError)
          return NextResponse.json(
            { error: 'Failed to delete conversations' },
            { status: 500 }
          )
        }

        deletedCounts.conversations = conversationCount || 0
      }
    }

    // Log the bulk delete activity
    await supabase.from('feature_usage').insert({
      user_id: user.id,
      feature_type: 'bulk_delete',
      metadata: {
        delete_type,
        deleted_counts: deletedCounts,
        timestamp: new Date().toISOString()
      }
    })

    // Return success with counts
    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${delete_type} data`,
      deleted_counts: deletedCounts
    })

  } catch (error) {
    console.error('Bulk delete error:', error)
    return NextResponse.json(
      {
        error: 'Failed to delete data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
