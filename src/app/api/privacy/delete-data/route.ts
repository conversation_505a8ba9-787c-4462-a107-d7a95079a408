import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data_type } = await request.json()

    if (!data_type) {
      return NextResponse.json(
        { error: 'Data type is required' },
        { status: 400 }
      )
    }

    let deletedCount = 0
    const errors = []

    try {
      switch (data_type) {
        case 'searches':
          const { count: searchCount, error: searchError } = await supabase
            .from('search_history')
            .delete()
            .eq('user_id', user.id)

          if (searchError) {
            errors.push(`Failed to delete search history: ${searchError.message}`)
          } else {
            deletedCount += searchCount || 0
          }
          break

        case 'chats':
          const { count: chatCount, error: chatError } = await supabase
            .from('chat_history')
            .delete()
            .eq('user_id', user.id)

          if (chatError) {
            errors.push(`Failed to delete chat history: ${chatError.message}`)
          } else {
            deletedCount += chatCount || 0
          }
          break

        case 'saved_searches':
          const { count: savedCount, error: savedError } = await supabase
            .from('saved_searches')
            .delete()
            .eq('user_id', user.id)

          if (savedError) {
            errors.push(`Failed to delete saved searches: ${savedError.message}`)
          } else {
            deletedCount += savedCount || 0
          }
          break

        case 'all':
          // Delete all user data
          const tables = ['search_history', 'chat_history', 'saved_searches', 'user_preferences', 'user_shortcuts', 'search_suggestions', 'saved_search_folders']
          
          for (const table of tables) {
            try {
              const { count, error } = await supabase
                .from(table)
                .delete()
                .eq('user_id', user.id)

              if (error) {
                errors.push(`Failed to delete from ${table}: ${error.message}`)
              } else {
                deletedCount += count || 0
              }
            } catch (tableError) {
              errors.push(`Error deleting from ${table}: ${tableError}`)
            }
          }
          break

        default:
          return NextResponse.json(
            { error: 'Invalid data type. Must be: searches, chats, saved_searches, or all' },
            { status: 400 }
          )
      }

      // Update storage usage after deletion
      try {
        await supabase
          .from('storage_usage')
          .delete()
          .eq('user_id', user.id)
      } catch (storageError) {
        // Non-critical error
        console.warn('Failed to update storage usage after deletion:', storageError)
      }

      // Log the deletion
      await supabase.from('feature_usage').insert({
        user_id: user.id,
        feature_type: 'data_deletion',
        metadata: {
          data_type,
          deleted_count: deletedCount,
          errors: errors.length > 0 ? errors : null,
          timestamp: new Date().toISOString()
        }
      })

      if (errors.length > 0) {
        return NextResponse.json({
          success: false,
          message: 'Data deletion completed with some errors',
          deleted_count: deletedCount,
          errors
        }, { status: 207 }) // 207 Multi-Status
      }

      return NextResponse.json({
        success: true,
        message: `Successfully deleted ${deletedCount} records`,
        deleted_count: deletedCount,
        data_type
      })

    } catch (deletionError) {
      console.error('Data deletion error:', deletionError)
      return NextResponse.json(
        { error: 'Failed to delete data', details: deletionError },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Delete data error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
