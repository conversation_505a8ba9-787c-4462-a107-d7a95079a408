import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { export_type = 'full', file_format = 'json' } = await request.json()

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Validate export type
    if (!['full', 'searches', 'chats'].includes(export_type)) {
      return NextResponse.json(
        { error: 'Invalid export type' },
        { status: 400 }
      )
    }

    // Get user's export data using the database function
    const { data: exportData, error: exportError } = await supabase
      .rpc('get_user_export_data', {
        user_uuid: user.id,
        export_type: export_type
      })

    if (exportError) {
      console.error('Export error:', exportError)
      return NextResponse.json(
        { error: 'Failed to export data' },
        { status: 500 }
      )
    }

    // Log the export activity
    await supabase.from('feature_usage').insert({
      user_id: user.id,
      feature_type: 'data_export',
      metadata: {
        export_type,
        file_format,
        timestamp: new Date().toISOString()
      }
    })

    // Create the export file
    const exportContent = {
      ...exportData,
      export_metadata: {
        generated_at: new Date().toISOString(),
        format_version: '1.0',
        ordrly_version: '1.0.0'
      }
    }

    if (file_format === 'json') {
      const jsonContent = JSON.stringify(exportContent, null, 2)

      return new NextResponse(jsonContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="ordrly-${export_type}-export-${new Date().toISOString().split('T')[0]}.json"`,
          'Content-Length': Buffer.byteLength(jsonContent, 'utf8').toString()
        }
      })
    } else if (file_format === 'csv') {
      // Convert to CSV format for searches
      if (export_type === 'searches' && exportData.search_history) {
        const csvRows = [
          'Date,Address,Project Type,Original Description',
          ...exportData.search_history.map((search: { created_at: string; address: string; project_type: string; metadata?: { originalDescription?: string } }) =>
            `"${search.created_at}","${search.address}","${search.project_type}","${search.metadata?.originalDescription || ''}"`
          )
        ]
        const csvContent = csvRows.join('\n')

        return new NextResponse(csvContent, {
          status: 200,
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="ordrly-${export_type}-export-${new Date().toISOString().split('T')[0]}.csv"`,
            'Content-Length': Buffer.byteLength(csvContent, 'utf8').toString()
          }
        })
      } else {
        return NextResponse.json(
          { error: 'CSV format only supported for search history exports' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Unsupported file format' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Export error:', error)
    return NextResponse.json(
      {
        error: 'Failed to export data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
