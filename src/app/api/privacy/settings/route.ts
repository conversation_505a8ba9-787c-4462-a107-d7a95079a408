import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's privacy settings
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('privacy_settings, last_cleanup_at')
      .eq('id', user.id)
      .single()

    if (error) {
      console.error('Error fetching privacy settings:', error)
      return NextResponse.json(
        { error: 'Failed to fetch privacy settings' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      privacy_settings: profile?.privacy_settings || {
        save_search_history: true,
        save_chat_history: true,
        retention_days: 365,
        auto_delete_enabled: false,
        export_enabled: true
      },
      last_cleanup_at: profile?.last_cleanup_at
    })

  } catch (error) {
    console.error('Privacy settings fetch error:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch privacy settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  return PUT(request)
}

export async function PUT(request: NextRequest) {
  try {
    const { privacy_settings } = await request.json()

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Validate privacy settings structure
    const requiredFields = ['save_search_history', 'save_chat_history', 'retention_days', 'auto_delete_enabled', 'export_enabled']
    for (const field of requiredFields) {
      if (!(field in privacy_settings)) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Validate retention_days
    if (privacy_settings.retention_days !== -1 && (privacy_settings.retention_days < 1 || privacy_settings.retention_days > 3650)) {
      return NextResponse.json(
        { error: 'Retention days must be between 1 and 3650, or -1 for never delete' },
        { status: 400 }
      )
    }

    // Update user's privacy settings
    const { error } = await supabase
      .from('profiles')
      .update({
        privacy_settings,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)

    if (error) {
      console.error('Error updating privacy settings:', error)
      return NextResponse.json(
        { error: 'Failed to update privacy settings' },
        { status: 500 }
      )
    }

    // Log the settings change
    await supabase.from('feature_usage').insert({
      user_id: user.id,
      feature_type: 'privacy_settings_updated',
      metadata: {
        new_settings: privacy_settings,
        timestamp: new Date().toISOString()
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Privacy settings updated successfully',
      privacy_settings
    })

  } catch (error) {
    console.error('Privacy settings update error:', error)
    return NextResponse.json(
      {
        error: 'Failed to update privacy settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
