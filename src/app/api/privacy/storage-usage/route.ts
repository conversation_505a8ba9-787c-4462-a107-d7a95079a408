import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'
import type { SupabaseClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get or create storage usage record
    const { data: initialUsage, error: storageError } = await supabase
      .from('storage_usage')
      .select('*')
      .eq('user_id', user.id)
      .single()

    let usage = initialUsage

    if (storageError && storageError.code === 'PGRST116') {
      // No record exists, create one
      const calculatedUsage = await calculateStorageUsage(supabase, user.id)
      
      const { data: newUsage, error: insertError } = await supabase
        .from('storage_usage')
        .insert({
          user_id: user.id,
          ...calculatedUsage,
          last_calculated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (insertError) {
        console.error('Error creating storage usage record:', insertError)
        return NextResponse.json({ error: 'Failed to calculate storage usage' }, { status: 500 })
      }

      usage = newUsage
    } else if (storageError) {
      console.error('Error fetching storage usage:', storageError)
      return NextResponse.json({ error: 'Failed to fetch storage usage' }, { status: 500 })
    }

    // Check if we need to recalculate (older than 1 hour)
    const lastCalculated = new Date(usage.last_calculated_at)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)

    if (lastCalculated < oneHourAgo) {
      const calculatedUsage = await calculateStorageUsage(supabase, user.id)
      
      const { data: updatedUsage, error: updateError } = await supabase
        .from('storage_usage')
        .update({
          ...calculatedUsage,
          last_calculated_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .select()
        .single()

      if (!updateError) {
        usage = updatedUsage
      }
    }

    return NextResponse.json({ usage })

  } catch (error) {
    console.error('Storage usage error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function calculateStorageUsage(supabase: SupabaseClient, userId: string) {
  try {
    // Count searches
    const { count: searchesCount } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    // Count chats
    const { count: chatsCount } = await supabase
      .from('chat_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    // Count saved searches
    const { count: savedSearchesCount } = await supabase
      .from('saved_searches')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    // Estimate total size (rough calculation)
    const avgSearchSize = 500 // bytes per search record
    const avgChatSize = 1000 // bytes per chat message
    const avgSavedSearchSize = 300 // bytes per saved search

    const totalSizeBytes = 
      (searchesCount || 0) * avgSearchSize +
      (chatsCount || 0) * avgChatSize +
      (savedSearchesCount || 0) * avgSavedSearchSize

    return {
      searches_count: searchesCount || 0,
      chats_count: chatsCount || 0,
      saved_searches_count: savedSearchesCount || 0,
      total_size_bytes: totalSizeBytes
    }

  } catch (error) {
    console.error('Error calculating storage usage:', error)
    return {
      searches_count: 0,
      chats_count: 0,
      saved_searches_count: 0,
      total_size_bytes: 0
    }
  }
}
