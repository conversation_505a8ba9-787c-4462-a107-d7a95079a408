import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe/config'

export async function POST(request: NextRequest) {
  try {
    // Get request body for flexible credit packages
    const body = await request.json()
    const { credits = 5, priceInCents = 500 } = body

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify user is on free plan
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier, stripe_customer_id, email')
      .eq('id', user.id)
      .single()

    if (profile?.subscription_tier !== 'free') {
      return NextResponse.json(
        { error: 'Credit purchases are only available for free plan users' },
        { status: 400 }
      )
    }

    // Get or create Stripe customer
    let customerId = profile?.stripe_customer_id

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email!,
        metadata: {
          supabase_user_id: user.id,
        },
      })
      customerId = customer.id

      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id)
    }

    // Create one-time payment session for credits
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `${credits} Additional Searches`,
              description: `Add ${credits} more searches to your free plan`,
            },
            unit_amount: priceInCents,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}&credits=${credits}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/checkout/error?error=canceled&credits=${credits}`,
      metadata: {
        supabase_user_id: user.id,
        type: 'pull_pack',
        credits: credits.toString(),
      },
    })

    return NextResponse.json({ sessionId: session.id })
  } catch (error) {
    console.error('Error creating pull pack checkout:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
}
