import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'

interface SearchHistoryItem {
  address: string
  rule_type: string
  project_type?: string
  created_at: string
}

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ recommendations: [] })
    }

    // Get user's search history for analysis
    const { data: searchHistory } = await supabase
      .from('search_history')
      .select('address, rule_type, created_at, metadata')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(50)

    if (!searchHistory || searchHistory.length === 0) {
      return NextResponse.json({ recommendations: [] })
    }

    const recommendations = []

    // 1. Frequent Areas Analysis
    const areaFrequency = analyzeFrequentAreas(searchHistory)
    if (areaFrequency.length > 0) {
      recommendations.push({
        id: `frequent-area-${Date.now()}`,
        type: 'frequent_area',
        title: `You often search in ${areaFrequency[0].area}`,
        description: `Based on ${areaFrequency[0].count} recent searches in this area`,
        address: areaFrequency[0].area,
        confidence_score: Math.min(areaFrequency[0].count / 10, 1),
        reason: 'Frequently searched location',
        action_url: `/search?area=${encodeURIComponent(areaFrequency[0].area)}`
      })
    }

    // 2. Popular Project Types
    const projectFrequency = analyzeProjectTypes(searchHistory)
    if (projectFrequency.length > 0) {
      recommendations.push({
        id: `popular-project-${Date.now()}`,
        type: 'popular_project',
        title: `${projectFrequency[0].type} projects are popular`,
        description: `You've searched for ${projectFrequency[0].type} ${projectFrequency[0].count} times`,
        project_type: projectFrequency[0].type,
        confidence_score: Math.min(projectFrequency[0].count / 5, 1),
        reason: 'Most searched project type',
        action_url: `/search?project=${encodeURIComponent(projectFrequency[0].type)}`
      })
    }

    // 3. Recent Trends
    const recentTrends = analyzeRecentTrends(searchHistory)
    recommendations.push(...recentTrends)

    // 4. Suggested Similar Searches
    const similarSearches = generateSimilarSearches(searchHistory)
    recommendations.push(...similarSearches)

    // Sort by confidence score and limit results
    const sortedRecommendations = recommendations
      .sort((a, b) => b.confidence_score - a.confidence_score)
      .slice(0, 10)

    return NextResponse.json({
      recommendations: sortedRecommendations,
      total: sortedRecommendations.length,
      generated_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Personalized recommendations error:', error)
    return NextResponse.json({ recommendations: [] })
  }
}

function analyzeFrequentAreas(searchHistory: SearchHistoryItem[]) {
  const areaCount = new Map<string, number>()

  searchHistory.forEach(search => {
    // Extract city/area from address
    const addressParts = search.address.split(',')
    if (addressParts.length >= 2) {
      const area = addressParts[addressParts.length - 2].trim()
      areaCount.set(area, (areaCount.get(area) || 0) + 1)
    }
  })

  return Array.from(areaCount.entries())
    .map(([area, count]) => ({ area, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 3)
}

function analyzeProjectTypes(searchHistory: SearchHistoryItem[]) {
  const typeCount = new Map<string, number>()

  searchHistory.forEach(search => {
    const type = search.rule_type || search.project_type
    if (type) {
      typeCount.set(type, (typeCount.get(type) || 0) + 1)
    }
  })

  return Array.from(typeCount.entries())
    .map(([type, count]) => ({ type, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 3)
}

function analyzeRecentTrends(searchHistory: SearchHistoryItem[]) {
  const recommendations: Array<{
    id: string
    type: string
    title: string
    description: string
    confidence_score: number
    reason: string
    action_url: string
  }> = []
  const recentSearches = searchHistory.slice(0, 10)

  // Look for patterns in recent searches
  const recentTypes = new Set(recentSearches.map(s => s.rule_type))

  if (recentTypes.size > 1) {
    recommendations.push({
      id: `trend-diverse-${Date.now()}`,
      type: 'recent_trend',
      title: 'You\'re exploring diverse projects',
      description: `Recent searches include ${Array.from(recentTypes).slice(0, 3).join(', ')}`,
      confidence_score: 0.7,
      reason: 'Diverse recent search activity',
      action_url: '/search'
    })
  }

  return recommendations
}

function generateSimilarSearches(searchHistory: SearchHistoryItem[]) {
  const recommendations: Array<{
    id: string
    type: string
    title: string
    description: string
    address: string
    project_type: string
    confidence_score: number
    reason: string
    action_url: string
  }> = []

  if (searchHistory.length > 0) {
    const lastSearch = searchHistory[0]
    const addressParts = lastSearch.address.split(',')

    if (addressParts.length >= 2) {
      const area = addressParts[addressParts.length - 2].trim()

      // Suggest related project types in the same area
      const relatedProjects = getRelatedProjects(lastSearch.rule_type)

      relatedProjects.forEach((project, index) => {
        recommendations.push({
          id: `similar-${project}-${Date.now()}-${index}`,
          type: 'suggested_search',
          title: `Try ${project} in ${area}`,
          description: `Similar to your recent ${lastSearch.rule_type} search`,
          address: area,
          project_type: project,
          confidence_score: 0.6 - (index * 0.1),
          reason: 'Related to recent search',
          action_url: `/search?area=${encodeURIComponent(area)}&project=${encodeURIComponent(project)}`
        })
      })
    }
  }

  return recommendations.slice(0, 3)
}

function getRelatedProjects(projectType: string): string[] {
  const relatedMap: { [key: string]: string[] } = {
    'fence': ['deck', 'shed', 'patio'],
    'deck': ['fence', 'pergola', 'patio'],
    'shed': ['fence', 'garage', 'deck'],
    'pool': ['deck', 'fence', 'patio'],
    'driveway': ['garage', 'fence', 'mailbox'],
    'garage': ['driveway', 'shed', 'fence'],
    'patio': ['deck', 'pergola', 'fence'],
    'pergola': ['deck', 'patio', 'fence']
  }

  return relatedMap[projectType?.toLowerCase()] || ['deck', 'fence', 'shed']
}
