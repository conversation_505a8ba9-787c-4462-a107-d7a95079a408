import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { sendReferralCreditsEarnedEmail } from '@/lib/email/sender'

// POST /api/referrals/award-credits - Award credits for verified referral
export async function POST() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user's email is verified
    if (!user.email_confirmed_at) {
      return NextResponse.json(
        { error: 'Email not verified' },
        { status: 400 }
      )
    }

    // Award referral credits
    const { data: success, error } = await supabase.rpc(
      'award_referral_credits',
      { referee_id_param: user.id }
    )

    if (error) {
      console.error('Error awarding referral credits:', error)
      return NextResponse.json(
        { error: 'Failed to award credits' },
        { status: 500 }
      )
    }

    if (!success) {
      return NextResponse.json({
        success: false,
        message: 'No pending referral found or credits already awarded'
      })
    }

    // Get updated referrer info for notification
    const { data: referralInfo } = await supabase
      .from('referrals')
      .select(`
        referrer_id,
        credits_awarded
      `)
      .eq('referee_id', user.id)
      .eq('status', 'credited')
      .single()

    // Get referrer profile separately for cleaner typing
    let emailSent = false
    if (referralInfo?.referrer_id) {
      const { data: referrerProfile } = await supabase
        .from('profiles')
        .select('email, name, referral_code, extra_credits')
        .eq('id', referralInfo.referrer_id)
        .single()

      if (referrerProfile?.email && referrerProfile?.referral_code) {
        try {
          const referralUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/signup?ref=${referrerProfile.referral_code}`
          await sendReferralCreditsEarnedEmail(
            referrerProfile.email,
            referrerProfile.extra_credits || 0,
            referralUrl,
            referrerProfile.name
          )
          emailSent = true
        } catch (emailError) {
          console.error('Failed to send referral credits email:', emailError)
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Referral credits awarded successfully',
      creditsAwarded: 5,
      referrerNotified: emailSent
    })
  } catch (error) {
    console.error('Error in POST /api/referrals/award-credits:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
