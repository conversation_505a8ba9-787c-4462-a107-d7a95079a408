import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Validation schemas
const generateReferralSchema = z.object({
  action: z.literal('generate')
})

const trackReferralSchema = z.object({
  action: z.literal('track'),
  referralCode: z.string().min(1, 'Referral code is required')
})

// GET /api/referrals - Get user's referral dashboard data
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's referral code and stats
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('referral_code, extra_credits')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('Error fetching profile:', profileError)
      return NextResponse.json(
        { error: 'Failed to fetch profile' },
        { status: 500 }
      )
    }

    // Get referral statistics
    const { data: referrals, error: referralsError } = await supabase
      .from('referrals')
      .select(`
        id,
        referee_id,
        status,
        credits_awarded,
        created_at,
        verified_at,
        credited_at
      `)
      .eq('referrer_id', user.id)
      .order('created_at', { ascending: false })

    if (referralsError) {
      console.error('Error fetching referrals:', referralsError)
      return NextResponse.json(
        { error: 'Failed to fetch referrals' },
        { status: 500 }
      )
    }

    // Calculate stats
    const totalReferrals = referrals?.length || 0
    const verifiedReferrals = referrals?.filter(r => r.status === 'credited').length || 0
    const pendingReferrals = referrals?.filter(r => r.status === 'pending').length || 0
    const totalCreditsEarned = referrals?.reduce((sum, r) => sum + (r.credits_awarded || 0), 0) || 0

    return NextResponse.json({
      success: true,
      data: {
        referralCode: profile.referral_code,
        extraCredits: profile.extra_credits || 0,
        stats: {
          totalReferrals,
          verifiedReferrals,
          pendingReferrals,
          totalCreditsEarned
        },
        referrals: referrals?.map(r => ({
          id: r.id,
          status: r.status,
          creditsAwarded: r.credits_awarded,
          createdAt: r.created_at,
          verifiedAt: r.verified_at,
          creditedAt: r.credited_at,
          // Don't expose referee email for privacy - just show status
          refereeEmail: r.status === 'credited' ?
            'Verified user' :
            'Pending verification'
        }))
      }
    })
  } catch (error) {
    console.error('Error in GET /api/referrals:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/referrals - Generate referral code or track referral
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Handle generate referral code
    if (body.action === 'generate') {
      const validation = generateReferralSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      // Get or create referral code
      const { data: referralCode, error } = await supabase.rpc(
        'get_or_create_referral_code',
        { user_id_param: user.id }
      )

      if (error) {
        console.error('Error generating referral code:', error)
        return NextResponse.json(
          { error: 'Failed to generate referral code' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        referralCode,
        referralUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/signup?ref=${referralCode}`
      })
    }

    // Handle track referral (for signup process)
    if (body.action === 'track') {
      const validation = trackReferralSchema.safeParse(body)
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request', details: validation.error.issues },
          { status: 400 }
        )
      }

      const { referralCode } = validation.data

      // Process referral signup
      const { data: success, error } = await supabase.rpc(
        'process_referral_signup',
        {
          referee_id_param: user.id,
          referral_code_param: referralCode
        }
      )

      if (error) {
        console.error('Error processing referral:', error)
        return NextResponse.json(
          { error: 'Failed to process referral' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: !!success,
        message: success ? 'Referral tracked successfully' : 'Invalid or duplicate referral'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error in POST /api/referrals:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
