import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { trackUsage, checkUsageLimit } from '@/lib/usage-tracking'
// Remove Geoapify dependency - use simple address parsing
import { ordrlyMunicipalAdapter } from '@/lib/services/ordrly-municipal-adapter'

/**
 * 🏛️ ORDRLY RESEARCH API - CHAT UI ENDPOINT
 *
 * Single public endpoint for Chat UI with auth/middleware:
 * 1. Authenticates user (session or API key)
 * 2. Checks user permissions and usage limits
 * 3. Calls municipal-research-api (port 3001) as backend
 * 4. Returns response formatted for Chat UI
 *
 * Flow: Chat UI → /api/research (auth) → Municipal API → Response
 */

interface ResearchRequest {
  query: string
  jurisdiction?: string
  address?: string
  lat?: number
  lng?: number
  context?: {
    type?: 'search' | 'chat' | 'api'
    sessionId?: string
    conversationHistory?: Array<{role: string, content: string}>
    requireHighConfidence?: boolean
  }
}

interface ResearchResponse {
  success: boolean
  answer: string
  confidence: number
  sources: Array<{
    title: string
    section?: string
    url?: string
    verified: boolean
    lastUpdated: string
    authority: 'primary' | 'secondary' | 'tertiary'
    excerpt: string
  }>
  researchQuality: {
    sourcesChecked: number
    primarySources: number
    freshnessScore: number
    completeness: number
    verificationStatus: string
  }
  metadata: {
    usedCache: boolean
    triggeredResearch: boolean
    processingTimeMs: number
    conversationContext: string
    jurisdiction: string
    tier: string
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    // 1. PARSE REQUEST
    const body: ResearchRequest = await request.json()
    const { query, jurisdiction, address, lat, lng, context = {} } = body

    if (!query) {
      return NextResponse.json({
        success: false,
        error: 'Query is required'
      }, { status: 400 })
    }

    // 2. AUTHENTICATION & AUTHORIZATION MIDDLEWARE
    const supabase = await createServerClient()
    const { authenticatedUserId, userTier, apiKey } = await authenticateUser(request, supabase)

    // 3. CHECK USER PERMISSIONS & USAGE LIMITS
    if (authenticatedUserId) {
      const usageStatus = await checkUsageLimit(authenticatedUserId)
      if (!usageStatus.canProceed) {
        return NextResponse.json({
          success: false,
          error: 'Usage limit exceeded',
          message: usageStatus.message,
          upgradeRequired: true,
        }, { status: 429 })
      }
      await trackUsage(authenticatedUserId)
    }

    // 4. VALIDATE INPUT - Just ensure we have address
    if (!address) {
      return NextResponse.json({
        success: false,
        error: 'Address is required'
      }, { status: 400 })
    }

    // 5. ANALYZE CONVERSATION CONTEXT
    const conversationContext = await analyzeConversationContext(
      supabase,
      authenticatedUserId,
      context.sessionId || null,
      query,
      context.conversationHistory || [],
      jurisdiction || 'TBD' // Municipal API will determine jurisdiction
    )

    // 6. CALL MUNICIPAL RESEARCH API (THE ACTUAL RESEARCH)
    // Let Municipal API handle all geocoding and jurisdiction resolution
    const researchResult = await performResearch(
      supabase,
      query,
      address, // Pass original address - Municipal API handles everything
      conversationContext,
      apiKey
    )

    // Record quality metrics
    await recordQualityMetrics(
      supabase,
      conversationContext.sessionId,
      query,
      researchResult.jurisdiction || jurisdiction || 'Unknown',
      researchResult
    )

    // Format response based on context type
    const response = formatResponse(
      researchResult,
      conversationContext,
      { jurisdiction: researchResult.jurisdiction || jurisdiction || 'Unknown' },
      userTier,
      context.type || 'api',
      Date.now() - startTime
    )

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ [RESEARCH API] Error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * 🔐 Authentication Helper
 */
async function authenticateUser(request: NextRequest, supabase: any) {
  let user = null
  let apiKeyUserId = null
  let apiKey = null

  // Try session authentication first
  try {
    const { data: { user: authUser } } = await supabase.auth.getUser()
    user = authUser
  } catch (error) {
    // Session auth failed, continue to API key
  }

  // Try API key authentication if no session
  if (!user) {
    const authHeader = request.headers.get('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      apiKey = authHeader.substring(7)

      if (apiKey.startsWith('ordrly_')) {
        try {
          const encoder = new TextEncoder()
          const data = encoder.encode(apiKey)
          const hashBuffer = await crypto.subtle.digest('SHA-256', data)
          const hashArray = Array.from(new Uint8Array(hashBuffer))
          const keyHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')

          const { data: apiKeyData, error } = await supabase
            .from('api_keys')
            .select('user_id, profiles!inner(subscription_tier)')
            .eq('key_hash', keyHash)
            .eq('is_active', true)
            .single()

          if (!error && apiKeyData) {
            apiKeyUserId = apiKeyData.user_id
          }
        } catch (error) {
          console.log('🔑 [AUTH] API key validation failed:', error)
        }
      }
    }
  }

  const authenticatedUserId = user?.id || apiKeyUserId

  // Get user tier
  let userTier = 'free'
  if (authenticatedUserId) {
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', authenticatedUserId)
      .single()

    userTier = profile?.subscription_tier || 'free'
  }

  return { authenticatedUserId, userTier, apiKey }
}

// Location resolution removed - Municipal API handles all geocoding

/**
 * 🧠 Conversation Context Analysis
 */
async function analyzeConversationContext(
  supabase: any,
  userId: string | null,
  sessionId: string | null,
  query: string,
  conversationHistory: any[],
  jurisdiction: string
) {
  // For now, implement basic context analysis
  // This will be enhanced with the database functions we created
  
  const isFollowUp = conversationHistory.length > 0
  const intent = detectQueryIntent(query)
  const needsFreshData = intent === 'permission' || query.toLowerCase().includes('can i')
  
  return {
    sessionId: sessionId || null,
    isFollowUp,
    intent,
    needsFreshData,
    confidenceThreshold: needsFreshData ? 0.95 : 0.88,
    conversationLength: conversationHistory.length
  }
}

/**
 * 🔍 Query Intent Detection
 */
function detectQueryIntent(query: string): string {
  const q = query.toLowerCase()

  if (q.includes('can i') || q.includes('am i allowed') || q.includes('is it legal')) {
    return 'permission'
  } else if (q.includes('how to') || q.includes('process') || q.includes('steps')) {
    return 'process'
  } else if (q.includes('requirement') || q.includes('rule') || q.includes('regulation')) {
    return 'requirement'
  } else {
    return 'clarification'
  }
}

/**
 * 🏛️ MUNICIPAL API CALL - The actual research happens here
 *
 * This function calls the standalone municipal-research-api (port 3001)
 * after all auth/middleware checks have passed
 */
async function performResearch(
  supabase: any,
  query: string,
  address: string,
  conversationContext: any,
  apiKey: string | null
) {
  console.log(`🔗 [PROXY] Calling municipal-research-api for: "${query}" at ${address}`)

  // Call the standalone municipal research API with the original address
  const researchResult = await ordrlyMunicipalAdapter.performResearch({
    query,
    address, // Use the original address from the request
    conversationContext,
    apiKey: apiKey ?? undefined
  })

  console.log(`✅ [PROXY] Municipal API responded with confidence: ${researchResult.confidence}`)
  return researchResult
}

/**
 * 📊 Quality Metrics Recording
 */
async function recordQualityMetrics(
  supabase: any,
  sessionId: string | null,
  query: string,
  jurisdiction: string,
  researchResult: any
) {
  try {
    if (!sessionId) {
      console.log(`📊 [METRICS] No session ID, skipping quality metrics recording`)
      return
    }

    // Create query hash for deduplication
    const encoder = new TextEncoder()
    const data = encoder.encode(query + jurisdiction)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    const queryHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')

    // Extract concepts from query
    const concepts = extractConcepts(query)

    // Calculate quality scores
    const freshnessScore = calculateFreshnessScore(researchResult.sources || [])
    const authorityScore = calculateAuthorityScore(researchResult.sources || [])
    const completenessScore = calculateCompletenessScore(researchResult.content || '')
    const consistencyScore = calculateConsistencyScore(researchResult.sources || [])

    await supabase
      .from('research_quality_metrics')
      .insert({
        session_id: sessionId,
        query_hash: queryHash,
        jurisdiction: jurisdiction,
        user_query: query,
        query_intent: detectQueryIntent(query),
        query_concepts: concepts,
        rag_confidence: researchResult.cached ? researchResult.confidence : null,
        research_confidence: !researchResult.cached ? researchResult.confidence : null,
        final_confidence: researchResult.confidence,
        sources_checked: researchResult.sources?.length || 0,
        primary_sources: researchResult.sources?.filter((s: any) => s.authority === 'primary').length || 0,
        verified_sources: researchResult.sources?.filter((s: any) => s.verified).length || 0,
        freshness_score: freshnessScore,
        authority_score: authorityScore,
        completeness_score: completenessScore,
        consistency_score: consistencyScore,
        processing_time_ms: researchResult.processingTimeMs || 0,
        cache_hit: researchResult.cached || false,
        triggered_research: !researchResult.cached,
        used_municipal_api: true,
        municipal_cached: researchResult.cached,
        municipal_topic: researchResult.topic
      })

    console.log(`📊 [METRICS] Research quality metrics recorded successfully`)
  } catch (error) {
    console.error('❌ [METRICS] Error recording research quality metrics:', error)
  }
}

/**
 * 🔍 Helper Functions for Quality Metrics
 */
function extractConcepts(query: string): string[] {
  const concepts = []
  const q = query.toLowerCase()

  const conceptMap = {
    'fence': ['fence', 'fencing', 'barrier'],
    'deck': ['deck', 'patio', 'platform'],
    'shed': ['shed', 'storage', 'outbuilding'],
    'pool': ['pool', 'swimming', 'spa'],
    'setback': ['setback', 'distance', 'spacing'],
    'height': ['height', 'tall', 'high'],
    'permit': ['permit', 'approval', 'license']
  }

  for (const [concept, keywords] of Object.entries(conceptMap)) {
    if (keywords.some(keyword => q.includes(keyword))) {
      concepts.push(concept)
    }
  }

  return concepts
}

function calculateFreshnessScore(sources: any[]): number {
  if (sources.length === 0) return 0

  const now = new Date()
  const scores = sources.map(source => {
    if (!source.lastUpdated) return 0.5 // Default score for unknown dates

    const sourceDate = new Date(source.lastUpdated)
    const ageInDays = (now.getTime() - sourceDate.getTime()) / (1000 * 60 * 60 * 24)

    // Decay over 365 days
    return Math.max(0, 1 - (ageInDays / 365))
  })

  return scores.reduce((sum, score) => sum + score, 0) / scores.length
}

function calculateAuthorityScore(sources: any[]): number {
  if (sources.length === 0) return 0

  const authorityWeights: { [key: string]: number } = { primary: 1.0, secondary: 0.7, tertiary: 0.4 }
  const scores = sources.map(source => authorityWeights[source.authority] || 0.2)

  return scores.reduce((sum, score) => sum + score, 0) / scores.length
}

function calculateCompletenessScore(content: string): number {
  if (!content) return 0

  // Score based on content length and structure
  const length = content.length
  let score = Math.min(1.0, length / 500) // Base score from length

  // Bonus for structured content
  if (content.includes('per ') || content.includes('according to')) score += 0.1
  if (content.includes('section') || content.includes('chapter')) score += 0.1
  if (content.includes('ordinance') || content.includes('code')) score += 0.1

  return Math.min(1.0, score)
}

function calculateConsistencyScore(sources: any[]): number {
  if (sources.length <= 1) return sources.length > 0 ? 1.0 : 0

  // Simple consistency based on number of sources
  // More sources generally indicate better consistency
  return Math.min(1.0, sources.length / 3.0)
}

/**
 * 🎨 Response Formatting
 */
function formatResponse(
  researchResult: any,
  conversationContext: any,
  locationData: any,
  userTier: string,
  responseType: string,
  processingTimeMs: number
): ResearchResponse {

  // Transform municipal API response to Ordrly format
  const sources = (researchResult.sources || []).map((source: any) => {
    // Handle both string URLs and object sources
    if (typeof source === 'string') {
      // Extract domain name for title
      const url = source
      const domain = url.match(/https?:\/\/([^\/]+)/)?.[1] || 'Municipal Source'
      const title = domain.replace('www.', '').replace('.gov', '').replace('.com', '')

      return {
        title: `${title} - Municipal Document`,
        section: extractSectionFromUrl(url),
        url: url,
        verified: true,
        lastUpdated: new Date().toISOString(),
        authority: 'primary' as const,
        excerpt: `Source from ${researchResult.jurisdiction || 'municipal authority'}`
      }
    } else {
      // Handle object sources (for backward compatibility)
      return {
        title: source.title || 'Municipal Source',
        section: extractSectionFromUrl(source.url),
        url: source.url,
        verified: source.verified || false,
        lastUpdated: new Date().toISOString(),
        authority: source.authority || 'primary',
        excerpt: `Source from ${researchResult.jurisdiction || 'municipal authority'}`
      }
    }
  })

  const baseResponse: ResearchResponse = {
    success: researchResult.success,
    answer: researchResult.answer || '',
    confidence: researchResult.confidence || 0,
    sources,
    researchQuality: {
      sourcesChecked: sources.length,
      primarySources: sources.filter((s: any) => s.authority === 'primary').length,
      freshnessScore: 0.95, // Municipal API provides fresh data
      completeness: Math.min(1.0, (researchResult.answer?.length || 0) / 500),
      verificationStatus: researchResult.cached ? 'cached' : 'fresh-research'
    },
    metadata: {
      usedCache: researchResult.cached || false,
      triggeredResearch: !researchResult.cached,
      processingTimeMs: researchResult.processingTimeMs || processingTimeMs,
      conversationContext: conversationContext.isFollowUp ? 'follow-up-question' : 'new-topic',
      jurisdiction: researchResult.jurisdiction || locationData.jurisdiction,
      tier: userTier
    }
  }

  // Add response type specific formatting
  if (responseType === 'chat') {
    // Add conversational elements for chat UI
    return {
      ...baseResponse,
      // Add chat-specific fields here
    }
  }

  return baseResponse
}

/**
 * Extract section info from URL
 */
function extractSectionFromUrl(url: string): string {
  if (!url) return ''

  try {
    const urlObj = new URL(url)
    const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0)
    const lastPart = pathParts[pathParts.length - 1]

    // Look for section patterns
    if (lastPart.includes('section') || lastPart.includes('sec')) {
      return lastPart.replace(/[-_]/g, ' ').replace(/\.(html|php|aspx?)$/i, '')
    }

    return ''
  } catch {
    return ''
  }
}
