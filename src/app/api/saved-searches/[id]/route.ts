import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

interface SavedSearchUpdateData {
  name?: string
  description?: string
  folder_id?: string
  tags?: string[]
  is_public?: boolean
  updated_at: string
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const { name, description, folder_id, tags, is_public } = body

    // Build update object
    const updateData: SavedSearchUpdateData = { updated_at: new Date().toISOString() }
    
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (folder_id !== undefined) updateData.folder_id = folder_id
    if (tags !== undefined) updateData.tags = tags
    if (is_public !== undefined) updateData.is_public = is_public

    const { data: savedSearch, error } = await supabase
      .from('saved_searches')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating saved search:', error)
      return NextResponse.json({ error: 'Failed to update saved search' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      search: savedSearch,
      message: 'Saved search updated successfully' 
    })

  } catch (error) {
    console.error('Update saved search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const { error } = await supabase
      .from('saved_searches')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting saved search:', error)
      return NextResponse.json({ error: 'Failed to delete saved search' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'Saved search deleted successfully' 
    })

  } catch (error) {
    console.error('Delete saved search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
