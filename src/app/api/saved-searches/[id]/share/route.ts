import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { randomBytes } from 'crypto'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Generate a unique share token
    const shareToken = randomBytes(16).toString('hex')

    // Update the saved search with share token and make it public
    const { error } = await supabase
      .from('saved_searches')
      .update({
        share_token: shareToken,
        is_public: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error generating share token:', error)
      return NextResponse.json({ error: 'Failed to generate share link' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      share_token: shareToken,
      share_url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/shared-search/${shareToken}`,
      message: 'Share link generated successfully' 
    })

  } catch (error) {
    console.error('Share saved search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerClient()

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Remove share token and make private
    const { error } = await supabase
      .from('saved_searches')
      .update({
        share_token: null,
        is_public: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error removing share token:', error)
      return NextResponse.json({ error: 'Failed to remove share link' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'Share link removed successfully' 
    })

  } catch (error) {
    console.error('Remove share saved search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
