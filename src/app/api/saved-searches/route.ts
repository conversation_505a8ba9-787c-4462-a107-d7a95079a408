import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

/**
 * GET /api/saved-searches - Retrieve user's saved searches
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Fetch user's saved searches with folder information
    const { data: savedSearches, error } = await supabase
      .from('saved_searches')
      .select(`
        *,
        saved_search_folders (
          id,
          name,
          color
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching saved searches:', error)
      return NextResponse.json(
        { error: 'Failed to fetch saved searches' },
        { status: 500 }
      )
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('saved_searches')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)

    if (countError) {
      console.error('Error counting saved searches:', countError)
    }

    return NextResponse.json({
      searches: savedSearches || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })

  } catch (error) {
    console.error('Saved searches API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/saved-searches - Save a new search
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      address,
      ruleType,
      complianceData,
      searchParams,
      name,
      description,
      folder_id,
      tags,
      is_public
    } = body

    if (!address || !ruleType) {
      return NextResponse.json(
        { error: 'Address and rule type are required' },
        { status: 400 }
      )
    }

    // Check user's tier for save search feature
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single()

    if (!profile || (profile.subscription_tier !== 'pro' && profile.subscription_tier !== 'appraiser')) {
      return NextResponse.json(
        { error: 'Save search feature requires Pro or Appraiser subscription' },
        { status: 403 }
      )
    }

    // Save the search
    const { data: savedSearch, error } = await supabase
      .from('saved_searches')
      .insert({
        user_id: user.id,
        name: name || `Search for ${address}`,
        address,
        rule_type: ruleType,
        compliance_data: complianceData,
        search_params: searchParams,
        description: description || null,
        folder_id: folder_id || null,
        tags: tags || null,
        is_public: is_public || false,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error saving search:', error)
      return NextResponse.json(
        { error: 'Failed to save search' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      search: savedSearch
    })

  } catch (error) {
    console.error('Save search API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/saved-searches - Delete saved searches
 */
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const searchId = searchParams.get('id')
    const searchIds = searchParams.get('ids')?.split(',')

    if (!searchId && !searchIds) {
      return NextResponse.json(
        { error: 'Search ID(s) required' },
        { status: 400 }
      )
    }

    let query = supabase
      .from('saved_searches')
      .delete()
      .eq('user_id', user.id)

    if (searchId) {
      query = query.eq('id', searchId)
    } else if (searchIds) {
      query = query.in('id', searchIds)
    }

    const { error } = await query

    if (error) {
      console.error('Error deleting saved searches:', error)
      return NextResponse.json(
        { error: 'Failed to delete saved searches' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Saved searches deleted successfully'
    })

  } catch (error) {
    console.error('Delete saved searches API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
