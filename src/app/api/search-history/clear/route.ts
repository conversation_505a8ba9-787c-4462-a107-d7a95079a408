import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function DELETE() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Clear all search history for the user
    const { error } = await supabase
      .from('search_history')
      .delete()
      .eq('user_id', user.id)

    if (error) {
      console.error('Error clearing search history:', error)
      return NextResponse.json({ error: 'Failed to clear search history' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'Search history cleared successfully' 
    })

  } catch (error) {
    console.error('Clear search history error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
