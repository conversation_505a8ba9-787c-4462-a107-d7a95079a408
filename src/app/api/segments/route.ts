import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'

// GET /api/segments - Get user segments for marketing
export async function GET() {
  try {
    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get segments
    const segments = await getUserSegments(supabase)

    return NextResponse.json({
      success: true,
      segments
    })
  } catch (error) {
    console.error('Error in GET /api/segments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/segments - Create new user segment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, criteria, type } = body

    if (!name || !criteria) {
      return NextResponse.json({
        error: 'Name and criteria are required'
      }, { status: 400 })
    }

    const supabase = await createServerClient()
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Create segment (mock for now)
    const segmentData = {
      id: `segment_${Date.now()}`,
      name,
      description: description || null,
      criteria,
      type: type || 'dynamic',
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Calculate segment size based on criteria
    const segmentSize = await calculateSegmentSize(supabase, criteria)

    return NextResponse.json({
      success: true,
      message: 'Segment created successfully',
      segment: {
        ...segmentData,
        size: segmentSize
      }
    })
  } catch (error) {
    console.error('Error in POST /api/segments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getUserSegments(supabase: SupabaseClient) {
  try {
    // Get user data for segment calculations
    const { data: profiles } = await supabase
      .from('profiles')
      .select('id, subscription_tier, created_at, pulls_this_month')

    const { data: searches } = await supabase
      .from('search_history')
      .select('user_id, created_at')
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())

    if (!profiles) {
      return getDefaultSegments()
    }

    // Calculate segment sizes
    const totalUsers = profiles.length
    const freeUsers = profiles.filter(p => p.subscription_tier === 'free')
    const proUsers = profiles.filter(p => p.subscription_tier === 'pro')
    const appraiserUsers = profiles.filter(p => p.subscription_tier === 'appraiser')

    const newUsers = profiles.filter(p =>
      new Date(p.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    )

    const activeUsers = profiles.filter(p =>
      searches?.some(s => s.user_id === p.id)
    )

    const heavyUsers = profiles.filter(p => p.pulls_this_month > 10)
    const lightUsers = profiles.filter(p => p.pulls_this_month <= 2)

    const segments = [
      {
        id: 'segment_free_users',
        name: 'Free Users',
        description: 'Users on the free subscription tier',
        criteria: {
          subscription_tier: 'free'
        },
        type: 'dynamic',
        size: freeUsers.length,
        percentage: Math.round((freeUsers.length / totalUsers) * 100),
        growth: '+12%', // Mock growth data
        lastUpdated: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'segment_pro_users',
        name: 'Pro Users',
        description: 'Users on the pro subscription tier',
        criteria: {
          subscription_tier: 'pro'
        },
        type: 'dynamic',
        size: proUsers.length,
        percentage: Math.round((proUsers.length / totalUsers) * 100),
        growth: '+8%',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'segment_appraiser_users',
        name: 'Appraiser Users',
        description: 'Users on the appraiser subscription tier',
        criteria: {
          subscription_tier: 'appraiser'
        },
        type: 'dynamic',
        size: appraiserUsers.length,
        percentage: Math.round((appraiserUsers.length / totalUsers) * 100),
        growth: '+15%',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'segment_new_users',
        name: 'New Users (7 days)',
        description: 'Users who signed up in the last 7 days',
        criteria: {
          created_at: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          }
        },
        type: 'dynamic',
        size: newUsers.length,
        percentage: Math.round((newUsers.length / totalUsers) * 100),
        growth: 'New',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'segment_active_users',
        name: 'Active Users (30 days)',
        description: 'Users who performed searches in the last 30 days',
        criteria: {
          has_recent_activity: true,
          activity_period: '30_days'
        },
        type: 'dynamic',
        size: activeUsers.length,
        percentage: Math.round((activeUsers.length / totalUsers) * 100),
        growth: '+5%',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'segment_heavy_users',
        name: 'Heavy Users',
        description: 'Users with more than 10 searches this month',
        criteria: {
          pulls_this_month: {
            gt: 10
          }
        },
        type: 'dynamic',
        size: heavyUsers.length,
        percentage: Math.round((heavyUsers.length / totalUsers) * 100),
        growth: '+3%',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'segment_light_users',
        name: 'Light Users',
        description: 'Users with 2 or fewer searches this month',
        criteria: {
          pulls_this_month: {
            lte: 2
          }
        },
        type: 'dynamic',
        size: lightUsers.length,
        percentage: Math.round((lightUsers.length / totalUsers) * 100),
        growth: '-2%',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'segment_upgrade_candidates',
        name: 'Upgrade Candidates',
        description: 'Free users approaching usage limits',
        criteria: {
          subscription_tier: 'free',
          pulls_this_month: {
            gte: 4
          }
        },
        type: 'dynamic',
        size: freeUsers.filter(u => u.pulls_this_month >= 4).length,
        percentage: Math.round((freeUsers.filter(u => u.pulls_this_month >= 4).length / totalUsers) * 100),
        growth: '+18%',
        lastUpdated: new Date().toISOString(),
        status: 'active'
      }
    ]

    return {
      segments,
      summary: {
        totalSegments: segments.length,
        totalUsers,
        averageSegmentSize: Math.round(segments.reduce((sum, s) => sum + s.size, 0) / segments.length),
        mostActiveSegment: segments.reduce((max, s) => s.size > max.size ? s : max, segments[0])
      }
    }
  } catch (error) {
    console.error('Error calculating user segments:', error)
    return getDefaultSegments()
  }
}

async function calculateSegmentSize(supabase: unknown, criteria: unknown) {
  try {
    // This would calculate the actual segment size based on criteria
    // For now, return a mock size based on criteria complexity
    const baseSize = Math.floor(Math.random() * 100) + 10

    // Add some logic based on criteria to make it more realistic
    if (criteria && typeof criteria === 'object') {
      const criteriaCount = Object.keys(criteria).length
      return Math.max(baseSize + criteriaCount * 5, 1)
    }

    return baseSize
  } catch (error) {
    console.error('Error calculating segment size:', error)
    return 0
  }
}

function getDefaultSegments() {
  return {
    segments: [],
    summary: {
      totalSegments: 0,
      totalUsers: 0,
      averageSegmentSize: 0,
      mostActiveSegment: null
    }
  }
}
