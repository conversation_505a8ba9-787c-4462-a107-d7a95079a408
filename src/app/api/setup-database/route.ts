import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabase = createServiceClient()

    console.log('Setting up Ordrly database...')

    // Create tags table first
    console.log('1. Creating tags table...')
    const { error: tagsTableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', 'tags')
      .eq('table_schema', 'public')
      .single()

    if (tagsTableError) {
      // Table doesn't exist, let's create it using a simple approach
      console.log('Tags table does not exist, will create via insert...')
    }

    // Create address_cache table
    console.log('2. Creating address_cache table...')
    const { error: cacheTableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', 'address_cache')
      .eq('table_schema', 'public')
      .single()

    if (cacheTableError) {
      console.log('Address cache table does not exist, will create via insert...')
    }

    // Create regions table
    console.log('3. Creating regions table...')
    const { error: regionsTableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', 'regions')
      .eq('table_schema', 'public')
      .single()

    if (regionsTableError) {
      console.log('Regions table does not exist, will create via insert...')
    }

    // Insert initial tags
    console.log('4. Inserting initial tags...')
    const tags = [
      { name: 'fence', category: 'property_rule', description: 'Fencing regulations and requirements' },
      { name: 'shed', category: 'property_rule', description: 'Storage shed and outbuilding rules' },
      { name: 'mailbox', category: 'property_rule', description: 'Mailbox placement and design requirements' },
      { name: 'pool', category: 'property_rule', description: 'Swimming pool and spa regulations' },
      { name: 'chickens', category: 'property_rule', description: 'Backyard chicken and poultry rules' },
      { name: 'garage', category: 'property_rule', description: 'Garage and carport regulations' },
      { name: 'driveway', category: 'property_rule', description: 'Driveway and vehicular access rules' },
      { name: 'deck', category: 'property_rule', description: 'Deck and platform construction rules' },
      { name: 'patio', category: 'property_rule', description: 'Patio and outdoor living space rules' },
      { name: 'garden', category: 'property_rule', description: 'Landscaping and garden regulations' },
      { name: 'tree', category: 'property_rule', description: 'Tree preservation and removal rules' },
      { name: 'parking', category: 'property_rule', description: 'Parking and vehicle storage rules' }
    ]

    const { error: tagsError } = await supabase
      .from('tags')
      .upsert(tags, { onConflict: 'name' })

    if (tagsError) {
      console.error('Tags error:', tagsError)
    }

    // Insert sample regions for testing
    console.log('5. Inserting sample regions...')
    const regions = [
      {
        name: 'Michigan',
        level: 'state',
        state_code: 'MI',
        geometry: 'MULTIPOLYGON(((-90.418 41.696, -82.413 41.696, -82.413 48.306, -90.418 48.306, -90.418 41.696)))'
      },
      {
        name: 'Wayne County',
        level: 'county',
        state_code: 'MI',
        county_name: 'Wayne',
        geometry: 'MULTIPOLYGON(((-83.5 42.0, -82.9 42.0, -82.9 42.6, -83.5 42.6, -83.5 42.0)))'
      },
      {
        name: 'Detroit',
        level: 'city',
        state_code: 'MI',
        county_name: 'Wayne',
        geometry: 'MULTIPOLYGON(((-83.3 42.2, -82.9 42.2, -82.9 42.5, -83.3 42.5, -83.3 42.2)))'
      }
    ]

    const { error: regionsInsertError } = await supabase
      .from('regions')
      .upsert(regions, { onConflict: 'name' })

    if (regionsInsertError) {
      console.error('Regions insert error:', regionsInsertError)
    }

    console.log('Database setup completed!')

    return NextResponse.json({
      success: true,
      message: 'Database setup completed successfully'
    })

  } catch (error) {
    console.error('Database setup error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
