import { NextResponse } from 'next/server'
import { setupEpic3TestUsers, verifyEpic3TestUsers } from '@/lib/setup-epic3-users'

// POST /api/setup-epic3-users - Create Epic 3 test users
export async function POST() {
  try {
    const result = await setupEpic3TestUsers()

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Epic 3 test users created successfully',
        results: result.results,
        summary: result.summary
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to create Epic 3 test users',
        details: result.error,
        results: result.results
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error in POST /api/setup-epic3-users:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET /api/setup-epic3-users - Verify Epic 3 test users
export async function GET() {
  try {
    const result = await verifyEpic3TestUsers()

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Epic 3 test users verified successfully',
        results: result.results,
        summary: result.summary
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to verify Epic 3 test users',
        details: result.error,
        results: result.results
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error in GET /api/setup-epic3-users:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
