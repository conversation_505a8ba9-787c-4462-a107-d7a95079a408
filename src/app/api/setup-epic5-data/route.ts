import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// Sample business data for Epic 5 features
const sampleBusinessMetrics = [
  {
    metric_type: 'revenue',
    metric_name: 'Monthly Recurring Revenue',
    value: 45250,
    unit: 'currency',
    period_start: '2024-01-01',
    period_end: '2024-01-31'
  },
  {
    metric_type: 'customers',
    metric_name: 'Total Customers',
    value: 1247,
    unit: 'number',
    period_start: '2024-01-01',
    period_end: '2024-01-31'
  },
  {
    metric_type: 'searches',
    metric_name: 'Property Searches',
    value: 15420,
    unit: 'number',
    period_start: '2024-01-01',
    period_end: '2024-01-31'
  },
  {
    metric_type: 'api_usage',
    metric_name: 'API Requests',
    value: 89420,
    unit: 'number',
    period_start: '2024-01-01',
    period_end: '2024-01-31'
  }
]

const sampleKPIDefinitions = [
  {
    name: 'Monthly Recurring Revenue',
    description: 'Total monthly recurring revenue from subscriptions',
    calculation_method: 'sum',
    target_value: 50000,
    unit: 'currency'
  },
  {
    name: 'Customer Acquisition Cost',
    description: 'Average cost to acquire a new customer',
    calculation_method: 'average',
    target_value: 100,
    unit: 'currency'
  },
  {
    name: 'Customer Lifetime Value',
    description: 'Average revenue per customer over their lifetime',
    calculation_method: 'average',
    target_value: 2500,
    unit: 'currency'
  },
  {
    name: 'Monthly Active Users',
    description: 'Number of users active in the last 30 days',
    calculation_method: 'count',
    target_value: 10000,
    unit: 'number'
  }
]

const sampleReports = [
  {
    name: 'Monthly Revenue Report',
    description: 'Comprehensive monthly revenue analysis',
    report_type: 'financial',
    configuration: {
      metrics: ['revenue', 'customers'],
      format: 'pdf',
      charts: true
    },
    is_scheduled: true,
    schedule_config: {
      frequency: 'monthly',
      day: 1,
      time: '09:00'
    }
  },
  {
    name: 'Customer Analytics Report',
    description: 'Customer behavior and analytics insights',
    report_type: 'analytics',
    configuration: {
      metrics: ['customers', 'searches'],
      format: 'excel',
      charts: true
    },
    is_scheduled: false
  }
]

// POST /api/setup-epic5-data - Create sample Epic 5 data
export async function POST() {
  try {
    const supabase = await createServerClient()

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    console.log('Setting up Epic 5 sample data...')

    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    // Insert sample business metrics
    try {
      const metricsWithUserId = sampleBusinessMetrics.map(metric => ({
        ...metric,
        user_id: user.id
      }))

      const { error: metricsError } = await supabase
        .from('business_metrics')
        .upsert(metricsWithUserId, { onConflict: 'user_id,metric_type,period_start' })

      if (metricsError) {
        console.error('Error inserting business metrics:', metricsError)
        errors.push(`Business metrics: ${metricsError.message}`)
        errorCount++
      } else {
        console.log('✅ Business metrics inserted')
        successCount++
      }
    } catch {
      console.log('⚠️ Business metrics table may not exist yet')
      errors.push('Business metrics table not found')
      errorCount++
    }

    // Insert sample KPI definitions
    try {
      const kpisWithUserId = sampleKPIDefinitions.map(kpi => ({
        ...kpi,
        user_id: user.id
      }))

      const { error: kpiError } = await supabase
        .from('kpi_definitions')
        .upsert(kpisWithUserId, { onConflict: 'user_id,name' })

      if (kpiError) {
        console.error('Error inserting KPI definitions:', kpiError)
        errors.push(`KPI definitions: ${kpiError.message}`)
        errorCount++
      } else {
        console.log('✅ KPI definitions inserted')
        successCount++
      }
    } catch {
      console.log('⚠️ KPI definitions table may not exist yet')
      errors.push('KPI definitions table not found')
      errorCount++
    }

    // Insert sample reports
    try {
      const reportsWithUserId = sampleReports.map(report => ({
        ...report,
        user_id: user.id
      }))

      const { error: reportsError } = await supabase
        .from('business_reports')
        .upsert(reportsWithUserId, { onConflict: 'user_id,name' })

      if (reportsError) {
        console.error('Error inserting business reports:', reportsError)
        errors.push(`Business reports: ${reportsError.message}`)
        errorCount++
      } else {
        console.log('✅ Business reports inserted')
        successCount++
      }
    } catch {
      console.log('⚠️ Business reports table may not exist yet')
      errors.push('Business reports table not found')
      errorCount++
    }

    // Insert sample compliance records
    try {
      const complianceRecords = [
        {
          user_id: user.id,
          compliance_type: 'gdpr',
          status: 'compliant',
          score: 95,
          audit_details: { last_review: '2024-01-10', items_checked: 25 }
        },
        {
          user_id: user.id,
          compliance_type: 'ccpa',
          status: 'compliant',
          score: 92,
          audit_details: { last_review: '2024-01-08', items_checked: 20 }
        }
      ]

      const { error: complianceError } = await supabase
        .from('compliance_records')
        .upsert(complianceRecords, { onConflict: 'user_id,compliance_type' })

      if (complianceError) {
        console.error('Error inserting compliance records:', complianceError)
        errors.push(`Compliance records: ${complianceError.message}`)
        errorCount++
      } else {
        console.log('✅ Compliance records inserted')
        successCount++
      }
    } catch {
      console.log('⚠️ Compliance records table may not exist yet')
      errors.push('Compliance records table not found')
      errorCount++
    }

    console.log(`Sample data setup complete: ${successCount} successful, ${errorCount} errors`)

    return NextResponse.json({
      success: true,
      message: `Epic 5 sample data created. ${successCount} successful, ${errorCount} errors.`,
      details: {
        successCount,
        errorCount,
        errors: errors.length > 0 ? errors : undefined
      }
    })

  } catch (error) {
    console.error('Error in POST /api/setup-epic5-data:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET /api/setup-epic5-data - Check Epic 5 data status
export async function GET() {
  try {
    const supabase = await createServerClient()

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    console.log('Checking Epic 5 sample data status...')

    const dataStatus: Record<string, number> = {}

    // Check business metrics
    try {
      const { count } = await supabase
        .from('business_metrics')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)

      dataStatus.business_metrics = count || 0
    } catch {
      dataStatus.business_metrics = -1 // Table doesn't exist
    }

    // Check KPI definitions
    try {
      const { count } = await supabase
        .from('kpi_definitions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)

      dataStatus.kpi_definitions = count || 0
    } catch {
      dataStatus.kpi_definitions = -1
    }

    // Check business reports
    try {
      const { count } = await supabase
        .from('business_reports')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)

      dataStatus.business_reports = count || 0
    } catch {
      dataStatus.business_reports = -1
    }

    return NextResponse.json({
      success: true,
      message: 'Epic 5 data status retrieved',
      details: {
        dataStatus,
        user_id: user.id
      }
    })

  } catch (error) {
    console.error('Error in GET /api/setup-epic5-data:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
