import { NextResponse } from 'next/server'
import { setupEpic5TestUsers, verifyEpic5TestUsers } from '@/lib/setup-epic5-users'

// POST /api/setup-epic5-users - Create Epic 5 test users
export async function POST() {
  try {
    const result = await setupEpic5TestUsers()

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Epic 5 test users created successfully'
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to create Epic 5 test users',
        details: result.error
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error in POST /api/setup-epic5-users:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET /api/setup-epic5-users - Verify Epic 5 test users
export async function GET() {
  try {
    const result = await verifyEpic5TestUsers()

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Epic 5 test users verified successfully'
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to verify Epic 5 test users',
        details: result.error
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error in GET /api/setup-epic5-users:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
