import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabase = createServiceClient()

    console.log('Setting up Epic 7 sample data...')

    // Insert sample knowledge base articles
    const articles = [
      {
        title: 'Getting Started with Ordrly',
        content: `Welcome to Ordrly! This guide will help you get started with our property compliance platform.

## Step 1: Enter Your Address
Start by typing any U.S. address in the search field. Our system provides autocomplete suggestions to help you find the exact location.

## Step 2: Review Compliance Results
Once you submit an address, our AI analyzes local ordinances and provides a comprehensive compliance report.

## Step 3: Download or Share
Download your compliance card as a PDF to share with contractors, or upgrade for advanced features.

## Tips for Best Results
- Type slowly for better autocomplete results
- Include city and state for accuracy
- Select from the dropdown suggestions`,
        category: 'getting-started',
        tags: ['tutorial', 'basics', 'address', 'compliance'],
        is_published: true,
        sort_order: 1
      },
      {
        title: 'Understanding Confidence Scores',
        content: `Ordrly provides confidence scores to help you understand the reliability of our compliance analysis.

## What Are Confidence Scores?
Confidence scores range from 0-100 and indicate how certain our AI is about the compliance requirements for your specific project.

## Score Ranges
- **90-100**: High confidence - Clear regulations found
- **70-89**: Good confidence - Regulations found with minor ambiguity
- **50-69**: Moderate confidence - Some uncertainty in interpretation
- **Below 50**: Low confidence - Limited or unclear regulations

## How to Use Scores
Higher scores mean you can rely more confidently on the results. For lower scores, consider consulting with local authorities or professionals.`,
        category: 'understanding-results',
        tags: ['confidence', 'scores', 'reliability'],
        is_published: true,
        sort_order: 2
      },
      {
        title: 'Supported Project Types',
        content: `Ordrly supports analysis for a wide variety of property improvement projects.

## Residential Projects
- Fences and barriers
- Sheds and outbuildings  
- Decks and patios
- Driveways and parking
- Landscaping and gardens
- Swimming pools and spas

## Commercial Projects
- Signage and displays
- Parking lots
- Accessibility improvements
- Outdoor dining areas

## What We Analyze
For each project type, we check:
- Setback requirements
- Height restrictions
- Permit requirements
- Material specifications
- Design standards

## Getting Better Results
Be specific about your project type and include relevant details like dimensions and materials when possible.`,
        category: 'project-types',
        tags: ['projects', 'residential', 'commercial', 'permits'],
        is_published: true,
        sort_order: 3
      },
      {
        title: 'How to Rate Articles',
        content: `Help us improve our knowledge base by rating articles and providing feedback.

## Rating System
- **5 stars**: Extremely helpful
- **4 stars**: Very helpful
- **3 stars**: Somewhat helpful
- **2 stars**: Not very helpful
- **1 star**: Not helpful at all

## Helpful Feedback
You can also mark articles as "helpful" or "not helpful" for quick feedback.

## Additional Comments
Use the feedback box to provide specific suggestions for improvement.`,
        category: 'using-ordrly',
        tags: ['rating', 'feedback', 'help'],
        is_published: true,
        sort_order: 4
      }
    ]

    const { error: articlesError } = await supabase
      .from('knowledge_base_articles')
      .upsert(articles, { onConflict: 'title' })

    if (articlesError) {
      console.error('Articles insert error:', articlesError)
      throw articlesError
    }

    console.log('✓ Knowledge base articles inserted')

    // Insert sample help tooltips
    const tooltips = [
      {
        element_id: 'search-input',
        title: 'Address Search',
        content: 'Enter any U.S. address to get started. Use our autocomplete suggestions for best results.',
        position: 'bottom'
      },
      {
        element_id: 'confidence-score',
        title: 'Confidence Score',
        content: 'This score indicates how certain our AI is about the compliance requirements. Higher scores mean more reliable results.',
        position: 'top'
      },
      {
        element_id: 'download-button',
        title: 'Download Results',
        content: 'Download your compliance card as a PDF to share with contractors or for permit applications.',
        position: 'top'
      },
      {
        element_id: 'upgrade-button',
        title: 'Upgrade Account',
        content: 'Upgrade to Pro for unlimited searches, AI chat support, and detailed analysis features.',
        position: 'left'
      },
      {
        element_id: 'help-widget',
        title: 'Need Help?',
        content: 'Click here to access tutorials, FAQ, and contact support.',
        position: 'left'
      }
    ]

    const { error: tooltipsError } = await supabase
      .from('help_tooltips')
      .upsert(tooltips, { onConflict: 'element_id' })

    if (tooltipsError) {
      console.error('Tooltips insert error:', tooltipsError)
      throw tooltipsError
    }

    console.log('✓ Help tooltips inserted')

    // Insert sample feature announcements
    const announcements = [
      {
        title: 'New Feature: AI Chat Support',
        content: 'Ask questions about your compliance results with our new AI chat feature! Available for Pro subscribers.',
        type: 'info',
        target_users: 'all',
        priority: 1
      },
      {
        title: 'Improved Address Autocomplete',
        content: 'We\'ve enhanced our address search with better autocomplete suggestions and faster results.',
        type: 'success',
        target_users: 'all',
        priority: 2
      },
      {
        title: 'Mobile App Coming Soon',
        content: 'Get ready for the Ordrly mobile app! Sign up for early access notifications in your account settings.',
        type: 'info',
        target_users: 'all',
        priority: 3
      }
    ]

    const { error: announcementsError } = await supabase
      .from('feature_announcements')
      .upsert(announcements, { onConflict: 'title' })

    if (announcementsError) {
      console.error('Announcements insert error:', announcementsError)
      throw announcementsError
    }

    console.log('✓ Feature announcements inserted')

    return NextResponse.json({
      success: true,
      message: 'Epic 7 sample data setup completed successfully'
    })

  } catch (error) {
    console.error('Epic 7 data setup error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
