import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabase = createServiceClient()

    console.log('Setting up Epic 7 database schema...')

    // Execute the Epic 7 migration SQL
    const migrationSQL = `
      -- Epic 7: Onboarding & Help Resources Database Schema
      
      -- Knowledge base articles table
      CREATE TABLE IF NOT EXISTS public.knowledge_base_articles (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        category TEXT NOT NULL,
        tags TEXT[] DEFAULT '{}',
        author_id UUID REFERENCES public.profiles(id),
        is_published BOOLEAN DEFAULT false,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Article ratings table
      CREATE TABLE IF NOT EXISTS public.article_ratings (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        article_id UUID REFERENCES public.knowledge_base_articles(id) ON DELETE CASCADE,
        user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        helpful BOOLEAN,
        feedback TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(article_id, user_id)
      );

      -- User progress tracking table
      CREATE TABLE IF NOT EXISTS public.user_progress (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
        feature_name TEXT NOT NULL,
        progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
        completed_steps TEXT[] DEFAULT '{}',
        total_steps INTEGER DEFAULT 0,
        last_step_completed TEXT,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, feature_name)
      );

      -- Feature announcements table
      CREATE TABLE IF NOT EXISTS public.feature_announcements (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
        is_active BOOLEAN DEFAULT true,
        target_users TEXT DEFAULT 'all' CHECK (target_users IN ('all', 'free', 'pro', 'enterprise')),
        priority INTEGER DEFAULT 0,
        expires_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Tutorial progress table
      CREATE TABLE IF NOT EXISTS public.tutorial_progress (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
        tutorial_name TEXT NOT NULL,
        current_step INTEGER DEFAULT 0,
        completed BOOLEAN DEFAULT false,
        completed_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, tutorial_name)
      );

      -- Help tooltips table
      CREATE TABLE IF NOT EXISTS public.help_tooltips (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        element_id TEXT NOT NULL UNIQUE,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        position TEXT DEFAULT 'top' CHECK (position IN ('top', 'bottom', 'left', 'right')),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Add first_time_user field to profiles if it doesn't exist
      ALTER TABLE public.profiles 
      ADD COLUMN IF NOT EXISTS first_time_user BOOLEAN DEFAULT true;

      -- Add help preferences to profiles
      ALTER TABLE public.profiles 
      ADD COLUMN IF NOT EXISTS help_preferences JSONB DEFAULT '{"show_tooltips": true, "show_announcements": true, "tutorial_completed": false}'::jsonb;

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_knowledge_base_articles_category ON public.knowledge_base_articles(category);
      CREATE INDEX IF NOT EXISTS idx_knowledge_base_articles_published ON public.knowledge_base_articles(is_published);
      CREATE INDEX IF NOT EXISTS idx_article_ratings_article_id ON public.article_ratings(article_id);
      CREATE INDEX IF NOT EXISTS idx_user_progress_user_id ON public.user_progress(user_id);
      CREATE INDEX IF NOT EXISTS idx_feature_announcements_active ON public.feature_announcements(is_active);
      CREATE INDEX IF NOT EXISTS idx_tutorial_progress_user_id ON public.tutorial_progress(user_id);

      -- Enable Row Level Security (RLS)
      ALTER TABLE public.knowledge_base_articles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.article_ratings ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.user_progress ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.feature_announcements ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.tutorial_progress ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.help_tooltips ENABLE ROW LEVEL SECURITY;
    `

    // Execute the schema creation
    const { error: schemaError } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    })

    if (schemaError) {
      console.error('Schema creation error:', schemaError)
      throw schemaError
    }

    console.log('✓ Epic 7 tables created')

    // Create RLS policies
    const policiesSQL = `
      -- RLS Policies for knowledge_base_articles
      DROP POLICY IF EXISTS "Knowledge base articles are viewable by everyone" ON public.knowledge_base_articles;
      CREATE POLICY "Knowledge base articles are viewable by everyone" ON public.knowledge_base_articles
        FOR SELECT USING (is_published = true);

      DROP POLICY IF EXISTS "Authors can manage their own articles" ON public.knowledge_base_articles;
      CREATE POLICY "Authors can manage their own articles" ON public.knowledge_base_articles
        FOR ALL USING (auth.uid() = author_id);

      DROP POLICY IF EXISTS "Authenticated users can create articles" ON public.knowledge_base_articles;
      CREATE POLICY "Authenticated users can create articles" ON public.knowledge_base_articles
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');

      -- RLS Policies for article_ratings
      DROP POLICY IF EXISTS "Users can view all ratings" ON public.article_ratings;
      CREATE POLICY "Users can view all ratings" ON public.article_ratings
        FOR SELECT USING (true);

      DROP POLICY IF EXISTS "Users can manage their own ratings" ON public.article_ratings;
      CREATE POLICY "Users can manage their own ratings" ON public.article_ratings
        FOR ALL USING (auth.uid() = user_id);

      -- RLS Policies for user_progress
      DROP POLICY IF EXISTS "Users can only access their own progress" ON public.user_progress;
      CREATE POLICY "Users can only access their own progress" ON public.user_progress
        FOR ALL USING (auth.uid() = user_id);

      -- RLS Policies for feature_announcements
      DROP POLICY IF EXISTS "Active announcements are viewable by everyone" ON public.feature_announcements;
      CREATE POLICY "Active announcements are viewable by everyone" ON public.feature_announcements
        FOR SELECT USING (is_active = true);

      -- RLS Policies for tutorial_progress
      DROP POLICY IF EXISTS "Users can only access their own tutorial progress" ON public.tutorial_progress;
      CREATE POLICY "Users can only access their own tutorial progress" ON public.tutorial_progress
        FOR ALL USING (auth.uid() = user_id);

      -- RLS Policies for help_tooltips
      DROP POLICY IF EXISTS "Active tooltips are viewable by everyone" ON public.help_tooltips;
      CREATE POLICY "Active tooltips are viewable by everyone" ON public.help_tooltips
        FOR SELECT USING (is_active = true);
    `

    const { error: policiesError } = await supabase.rpc('exec_sql', {
      sql: policiesSQL
    })

    if (policiesError) {
      console.error('Policies creation error:', policiesError)
      throw policiesError
    }

    console.log('✓ RLS policies created')

    return NextResponse.json({
      success: true,
      message: 'Epic 7 database schema setup completed successfully'
    })

  } catch (error) {
    console.error('Epic 7 schema setup error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
