import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabase = createServiceClient()

    console.log('Setting up Epic 7 test users...')

    // Create test users using Supabase Auth Admin API
    const testUsers = [
      {
        email: '<EMAIL>',
        password: 'newuser123',
        email_confirm: true,
        user_metadata: {
          name: 'New User'
        }
      },
      {
        email: '<EMAIL>',
        password: 'existing123',
        email_confirm: true,
        user_metadata: {
          name: 'Existing User'
        }
      }
    ]

    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const { data: existingUsers } = await supabase.auth.admin.listUsers()
        const existingUser = existingUsers.users.find(u => u.email === userData.email)

        if (existingUser) {
          console.log(`✓ User ${userData.email} already exists`)

          // Update the existing user's profile with Epic 7 fields
          const profileData = {
            first_time_user: userData.email === '<EMAIL>',
            help_preferences: {
              show_tooltips: true,
              show_announcements: true,
              tutorial_completed: userData.email === '<EMAIL>'
            }
          }

          const { error: profileError } = await supabase
            .from('profiles')
            .update(profileData)
            .eq('id', existingUser.id)

          if (profileError) {
            console.error(`Error updating profile for ${userData.email}:`, profileError)
          } else {
            console.log(`✓ Updated profile for ${userData.email}`)
          }
          continue
        }

        // Create the user
        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: userData.email_confirm,
          user_metadata: userData.user_metadata
        })

        if (createError) {
          console.error(`Error creating user ${userData.email}:`, createError)
          continue
        }

        console.log(`✓ Created user ${userData.email}`)

        // Create profile for the user
        if (newUser.user) {
          const profileData = {
            id: newUser.user.id,
            email: userData.email,
            name: userData.user_metadata.name,
            first_time_user: userData.email === '<EMAIL>',
            help_preferences: {
              show_tooltips: true,
              show_announcements: true,
              tutorial_completed: userData.email === '<EMAIL>'
            },
            subscription_tier: userData.email === '<EMAIL>' ? 'pro' : 'free',
            role: 'user',
            pulls_this_month: userData.email === '<EMAIL>' ? 5 : 0,
            extra_credits: 0,
            last_reset_date: new Date().toISOString().split('T')[0]
          }

          const { error: profileError } = await supabase
            .from('profiles')
            .upsert(profileData, { onConflict: 'id' })

          if (profileError) {
            console.error(`Error creating profile for ${userData.email}:`, profileError)
          } else {
            console.log(`✓ Created profile for ${userData.email}`)
          }

          // Add some sample progress for existing user
          if (userData.email === '<EMAIL>') {
            const progressData = [
              {
                user_id: newUser.user.id,
                feature_name: 'onboarding',
                progress_percentage: 100,
                completed_steps: ['welcome', 'address-search', 'results-review'],
                total_steps: 3,
                last_step_completed: 'results-review'
              },
              {
                user_id: newUser.user.id,
                feature_name: 'profile-setup',
                progress_percentage: 75,
                completed_steps: ['basic-info', 'preferences'],
                total_steps: 4,
                last_step_completed: 'preferences'
              }
            ]

            const { error: progressError } = await supabase
              .from('user_progress')
              .upsert(progressData, { onConflict: 'user_id,feature_name' })

            if (progressError) {
              console.error(`Error creating progress for ${userData.email}:`, progressError)
            } else {
              console.log(`✓ Created progress data for ${userData.email}`)
            }

            // Add tutorial progress
            const tutorialData = {
              user_id: newUser.user.id,
              tutorial_name: 'getting-started',
              current_step: 3,
              completed: true,
              completed_at: new Date().toISOString()
            }

            const { error: tutorialError } = await supabase
              .from('tutorial_progress')
              .upsert(tutorialData, { onConflict: 'user_id,tutorial_name' })

            if (tutorialError) {
              console.error(`Error creating tutorial progress for ${userData.email}:`, tutorialError)
            } else {
              console.log(`✓ Created tutorial progress for ${userData.email}`)
            }
          }
        }

      } catch (userError) {
        console.error(`Error processing user ${userData.email}:`, userError)
      }
    }

    // Create some sample article ratings from the existing user
    try {
      const { data: existingUsers } = await supabase.auth.admin.listUsers()
      const existingUser = existingUsers.users.find(u => u.email === '<EMAIL>')
      const { data: articles } = await supabase
        .from('knowledge_base_articles')
        .select('id')
        .limit(2)

      if (existingUser && articles && articles.length > 0) {
        const ratings = articles.map((article, index) => ({
          article_id: article.id,
          user_id: existingUser.id,
          rating: index === 0 ? 5 : 4,
          helpful: true,
          feedback: index === 0 ? 'Very helpful guide!' : 'Good information, thanks!'
        }))

        const { error: ratingsError } = await supabase
          .from('article_ratings')
          .upsert(ratings, { onConflict: 'article_id,user_id' })

        if (ratingsError) {
          console.error('Error creating sample ratings:', ratingsError)
        } else {
          console.log('✓ Created sample article ratings')
        }
      }
    } catch (ratingsError) {
      console.error('Error setting up sample ratings:', ratingsError)
    }

    return NextResponse.json({
      success: true,
      message: 'Epic 7 test users setup completed successfully',
      users: testUsers.map(u => ({ email: u.email, created: true }))
    })

  } catch (error) {
    console.error('Epic 7 users setup error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
