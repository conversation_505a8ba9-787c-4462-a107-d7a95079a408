import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabase = await createServerClient()

    // Create user_preferences table
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS user_preferences (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          theme VARCHAR(20) DEFAULT 'light',
          notifications JSONB DEFAULT '{"email": true, "push": true, "marketing": true}',
          language VARCHAR(10) DEFAULT 'en',
          timezone VARCHAR(50) DEFAULT 'UTC',
          auto_save_searches BOOLEAN DEFAULT true,
          search_suggestions BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id)
        );
      `
    })

    // Create user_shortcuts table
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS user_shortcuts (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          name VARCHAR(100) NOT NULL,
          url VARCHAR(500) NOT NULL,
          icon VARCHAR(50),
          position INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    })

    // Create search_suggestions table
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS search_suggestions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          address TEXT NOT NULL,
          search_count INTEGER DEFAULT 1,
          last_searched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, address)
        );
      `
    })

    // Create saved_search_folders table
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS saved_search_folders (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          color VARCHAR(20) DEFAULT 'blue',
          position INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    })

    // Update search_history table to add metadata column if it doesn't exist
    await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE search_history 
        ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';
      `
    })

    // Update saved_searches table to add organization fields
    await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE saved_searches 
        ADD COLUMN IF NOT EXISTS folder_id UUID REFERENCES saved_search_folders(id) ON DELETE SET NULL,
        ADD COLUMN IF NOT EXISTS tags TEXT[],
        ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT false,
        ADD COLUMN IF NOT EXISTS share_token VARCHAR(50),
        ADD COLUMN IF NOT EXISTS description TEXT;
      `
    })

    // Update profiles table to add Epic 9 fields
    await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE profiles 
        ADD COLUMN IF NOT EXISTS theme VARCHAR(20) DEFAULT 'light',
        ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"email": true, "push": true, "marketing": true}',
        ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{"save_search_history": true, "save_chat_history": true, "retention_days": 365, "auto_delete_enabled": false, "export_enabled": true}',
        ADD COLUMN IF NOT EXISTS personalization_settings JSONB DEFAULT '{"show_recommendations": true, "show_frequent_areas": true, "smart_defaults": true}';
      `
    })

    // Create storage_usage table
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS storage_usage (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          searches_count INTEGER DEFAULT 0,
          chats_count INTEGER DEFAULT 0,
          saved_searches_count INTEGER DEFAULT 0,
          total_size_bytes BIGINT DEFAULT 0,
          last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id)
        );
      `
    })

    // Create data_exports table
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS data_exports (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          export_type VARCHAR(50) NOT NULL,
          file_format VARCHAR(10) NOT NULL,
          status VARCHAR(20) DEFAULT 'pending',
          file_url TEXT,
          expires_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE
        );
      `
    })

    // Create indexes for performance
    await supabase.rpc('exec_sql', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_shortcuts_user_id ON user_shortcuts(user_id);
        CREATE INDEX IF NOT EXISTS idx_search_suggestions_user_id ON search_suggestions(user_id);
        CREATE INDEX IF NOT EXISTS idx_search_suggestions_count ON search_suggestions(search_count DESC);
        CREATE INDEX IF NOT EXISTS idx_saved_search_folders_user_id ON saved_search_folders(user_id);
        CREATE INDEX IF NOT EXISTS idx_storage_usage_user_id ON storage_usage(user_id);
        CREATE INDEX IF NOT EXISTS idx_data_exports_user_id ON data_exports(user_id);
        CREATE INDEX IF NOT EXISTS idx_search_history_metadata ON search_history USING GIN(metadata);
      `
    })

    // Create RLS policies
    await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
        ALTER TABLE user_shortcuts ENABLE ROW LEVEL SECURITY;
        ALTER TABLE search_suggestions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE saved_search_folders ENABLE ROW LEVEL SECURITY;
        ALTER TABLE storage_usage ENABLE ROW LEVEL SECURITY;
        ALTER TABLE data_exports ENABLE ROW LEVEL SECURITY;

        CREATE POLICY IF NOT EXISTS "Users can manage their own preferences" ON user_preferences
          FOR ALL USING (auth.uid() = user_id);
        
        CREATE POLICY IF NOT EXISTS "Users can manage their own shortcuts" ON user_shortcuts
          FOR ALL USING (auth.uid() = user_id);
        
        CREATE POLICY IF NOT EXISTS "Users can manage their own search suggestions" ON search_suggestions
          FOR ALL USING (auth.uid() = user_id);
        
        CREATE POLICY IF NOT EXISTS "Users can manage their own folders" ON saved_search_folders
          FOR ALL USING (auth.uid() = user_id);
        
        CREATE POLICY IF NOT EXISTS "Users can view their own storage usage" ON storage_usage
          FOR ALL USING (auth.uid() = user_id);
        
        CREATE POLICY IF NOT EXISTS "Users can view their own data exports" ON data_exports
          FOR ALL USING (auth.uid() = user_id);
      `
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Epic 9 database schema created successfully' 
    })

  } catch (error) {
    console.error('Epic 9 schema setup error:', error)
    return NextResponse.json(
      { error: 'Failed to setup Epic 9 schema', details: error },
      { status: 500 }
    )
  }
}
