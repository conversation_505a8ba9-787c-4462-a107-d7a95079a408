import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user shortcuts
    const { data: shortcuts, error } = await supabase
      .from('user_shortcuts')
      .select('*')
      .eq('user_id', user.id)
      .order('position', { ascending: true })

    if (error) {
      console.error('Error fetching shortcuts:', error)
      return NextResponse.json({ error: 'Failed to fetch shortcuts' }, { status: 500 })
    }

    return NextResponse.json({ shortcuts: shortcuts || [] })

  } catch (error) {
    console.error('Shortcuts API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { name, url, icon, position } = await request.json()

    // Validate required fields
    if (!name || !url) {
      return NextResponse.json({ error: 'Name and URL are required' }, { status: 400 })
    }

    // Create shortcut
    const { data: shortcut, error } = await supabase
      .from('user_shortcuts')
      .insert({
        user_id: user.id,
        name,
        url,
        icon: icon || 'link',
        position: position || 0
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating shortcut:', error)
      return NextResponse.json({ error: 'Failed to create shortcut' }, { status: 500 })
    }

    return NextResponse.json({ shortcut })

  } catch (error) {
    console.error('Shortcut creation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id, name, url, icon, position } = await request.json()

    // Validate required fields
    if (!id || !name || !url) {
      return NextResponse.json({ error: 'ID, name and URL are required' }, { status: 400 })
    }

    // Update shortcut
    const { data: shortcut, error } = await supabase
      .from('user_shortcuts')
      .update({
        name,
        url,
        icon: icon || 'link',
        position: position || 0,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating shortcut:', error)
      return NextResponse.json({ error: 'Failed to update shortcut' }, { status: 500 })
    }

    return NextResponse.json({ shortcut })

  } catch (error) {
    console.error('Shortcut update error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Shortcut ID is required' }, { status: 400 })
    }

    // Delete shortcut
    const { error } = await supabase
      .from('user_shortcuts')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting shortcut:', error)
      return NextResponse.json({ error: 'Failed to delete shortcut' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Shortcut deleted successfully' })

  } catch (error) {
    console.error('Shortcut deletion error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
