import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get or create storage usage record
    const { data: initialStorageUsage, error: fetchError } = await supabase
      .from('storage_usage')
      .select('*')
      .eq('user_id', user.id)
      .single()

    let storageUsage = initialStorageUsage

    if (fetchError && fetchError.code === 'PGRST116') {
      // Record doesn't exist, create it
      const { data: newRecord, error: createError } = await supabase
        .from('storage_usage')
        .insert({
          user_id: user.id,
          searches_count: 0,
          chats_count: 0,
          saved_searches_count: 0,
          total_size_bytes: 0
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating storage usage record:', createError)
        return NextResponse.json({ error: 'Failed to create storage usage' }, { status: 500 })
      }

      storageUsage = newRecord
    } else if (fetchError) {
      console.error('Error fetching storage usage:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch storage usage' }, { status: 500 })
    }

    // Calculate current usage
    const usageData = await calculateStorageUsage(supabase, user.id)

    // Update the record with current data
    const { error: updateError } = await supabase
      .from('storage_usage')
      .update({
        searches_count: usageData.searches_count,
        chats_count: usageData.chats_count,
        saved_searches_count: usageData.saved_searches_count,
        total_size_bytes: usageData.total_size_bytes,
        last_calculated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id)

    if (updateError) {
      console.warn('Failed to update storage usage:', updateError)
    }

    return NextResponse.json({
      usage: {
        ...storageUsage,
        ...usageData,
        last_calculated_at: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Storage usage API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

async function calculateStorageUsage(supabase: SupabaseClient, userId: string) {
  const usage = {
    searches_count: 0,
    chats_count: 0,
    saved_searches_count: 0,
    total_size_bytes: 0
  }

  try {
    // Count search history
    const { count: searchCount } = await supabase
      .from('search_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    usage.searches_count = searchCount || 0

    // Count saved searches
    const { count: savedCount } = await supabase
      .from('saved_searches')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    usage.saved_searches_count = savedCount || 0

    // Estimate total size (rough calculation)
    // Each search record ~1KB, each saved search ~2KB
    usage.total_size_bytes = (usage.searches_count * 1024) + (usage.saved_searches_count * 2048)

    // Add chat count if chat history table exists
    try {
      const { count: chatCount } = await supabase
        .from('chat_history')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      usage.chats_count = chatCount || 0
      usage.total_size_bytes += (usage.chats_count * 1536) // ~1.5KB per chat
    } catch {
      // Chat table might not exist yet
      usage.chats_count = 0
    }

  } catch (error) {
    console.error('Error calculating storage usage:', error)
  }

  return usage
}

export async function POST() {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Force recalculation of storage usage
    const usageData = await calculateStorageUsage(supabase, user.id)

    // Update or create storage usage record
    const { data: updatedUsage, error } = await supabase
      .from('storage_usage')
      .upsert({
        user_id: user.id,
        searches_count: usageData.searches_count,
        chats_count: usageData.chats_count,
        saved_searches_count: usageData.saved_searches_count,
        total_size_bytes: usageData.total_size_bytes,
        last_calculated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error updating storage usage:', error)
      return NextResponse.json({ error: 'Failed to update storage usage' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Storage usage recalculated successfully',
      usage: updatedUsage
    })

  } catch (error) {
    console.error('Storage usage recalculation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
