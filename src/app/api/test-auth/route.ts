import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// Test users for Epic 5
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'business123',
    subscription_tier: 'business',
    role: 'business',
    full_name: 'Business Test User'
  },
  {
    email: '<EMAIL>',
    password: 'enterprise123',
    subscription_tier: 'enterprise',
    role: 'enterprise',
    full_name: 'Enterprise Test User'
  },
  {
    email: '<EMAIL>',
    password: 'admin123',
    subscription_tier: 'business',
    role: 'admin',
    full_name: 'Admin Test User'
  }
]

// POST /api/test-auth - Authenticate test user and set session
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    console.log(`Test auth request for: ${email}`)

    // Find the test user
    const testUser = testUsers.find(user => user.email === email && user.password === password)

    if (!testUser) {
      return NextResponse.json({
        success: false,
        error: 'Invalid test user credentials'
      }, { status: 401 })
    }

    const supabase = await createServerClient()

    // Try to sign in the user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password
    })

    if (authError) {
      console.log(`Auth error for ${email}, trying to create user...`)

      // If sign in fails, try to create the user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: testUser.email,
        password: testUser.password,
        options: {
          data: {
            full_name: testUser.full_name,
            subscription_tier: testUser.subscription_tier,
            role: testUser.role
          }
        }
      })

      if (signUpError) {
        console.error(`Failed to create user ${email}:`, signUpError)
        return NextResponse.json({
          success: false,
          error: 'Failed to create test user',
          details: signUpError.message
        }, { status: 500 })
      }

      if (signUpData.user) {
        // Update profile with test user data
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({
            id: signUpData.user.id,
            email: testUser.email,
            full_name: testUser.full_name,
            subscription_tier: testUser.subscription_tier,
            role: testUser.role,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (profileError) {
          console.error(`Failed to create profile for ${email}:`, profileError)
        }

        console.log(`✅ Created and authenticated user: ${email}`)
        return NextResponse.json({
          success: true,
          message: 'Test user created and authenticated',
          user: {
            id: signUpData.user.id,
            email: testUser.email,
            role: testUser.role,
            subscription_tier: testUser.subscription_tier
          }
        })
      }
    } else if (authData.user) {
      // User signed in successfully, update their profile to ensure correct role/tier
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: authData.user.id,
          email: testUser.email,
          full_name: testUser.full_name,
          subscription_tier: testUser.subscription_tier,
          role: testUser.role,
          updated_at: new Date().toISOString()
        })

      if (profileError) {
        console.error(`Failed to update profile for ${email}:`, profileError)
      }

      console.log(`✅ Authenticated existing user: ${email}`)
      return NextResponse.json({
        success: true,
        message: 'Test user authenticated successfully',
        user: {
          id: authData.user.id,
          email: testUser.email,
          role: testUser.role,
          subscription_tier: testUser.subscription_tier
        }
      })
    }

    return NextResponse.json({
      success: false,
      error: 'Authentication failed'
    }, { status: 500 })

  } catch (error) {
    console.error('Error in POST /api/test-auth:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET /api/test-auth - Check current authentication status
export async function GET() {
  try {
    const supabase = await createServerClient()

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        authenticated: false,
        message: 'No user authenticated'
      })
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier, role, full_name')
      .eq('id', user.id)
      .single()

    return NextResponse.json({
      success: true,
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: profile?.role || 'user',
        subscription_tier: profile?.subscription_tier || 'free',
        full_name: profile?.full_name || user.email
      }
    })

  } catch (error) {
    console.error('Error in GET /api/test-auth:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE /api/test-auth - Sign out current user
export async function DELETE() {
  try {
    const supabase = await createServerClient()

    const { error } = await supabase.auth.signOut()

    if (error) {
      return NextResponse.json({
        success: false,
        error: 'Failed to sign out',
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Signed out successfully'
    })

  } catch (error) {
    console.error('Error in DELETE /api/test-auth:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
