import { NextRequest, NextResponse } from 'next/server'
import { notifyUserRegistration, notifyUserEvent, testAutomationConnection } from '@/lib/automation/integration'

/**
 * Test endpoint for automation integration
 * GET /api/test-automation - Test connection and functionality
 */
export async function GET(request: NextRequest) {
  try {
    const results = {
      timestamp: new Date().toISOString(),
      tests: {} as Record<string, any>
    };

    // Test 1: Health Check
    console.log('Testing automation system connectivity...');
    try {
      const isHealthy = await testAutomationConnection();
      results.tests.healthCheck = {
        success: isHealthy,
        message: isHealthy ? 'Automation system is reachable' : 'Health check failed'
      };
    } catch (error) {
      results.tests.healthCheck = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test 2: User Registration Notification
    console.log('Testing user registration notification...');
    const testUser = {
      userId: `test-api-${Date.now()}`,
      email: '<EMAIL>',
      userData: {
        signupSource: 'ordrly-main-app-api-test',
        userType: 'homeowner',
        name: 'API Test User'
      }
    };

    try {
      const registrationResult = await notifyUserRegistration(testUser);
      results.tests.userRegistration = {
        success: registrationResult.success,
        welcomeCampaignSubscribed: registrationResult.welcomeCampaign?.subscribed,
        subscriptionId: registrationResult.welcomeCampaign?.subscriptionId,
        error: registrationResult.error
      };
    } catch (error) {
      results.tests.userRegistration = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test 3: Event Notification
    console.log('Testing event notification...');
    try {
      const eventResult = await notifyUserEvent(
        testUser.userId,
        'api_test_event',
        {
          testType: 'integration',
          source: 'ordrly-main-app'
        }
      );

      results.tests.eventNotification = {
        success: eventResult.success,
        eventId: eventResult.event?.id,
        error: eventResult.error
      };
    } catch (error) {
      results.tests.eventNotification = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Overall success
    const allTestsPassed = Object.values(results.tests).every(test => test.success);
    
    return NextResponse.json({
      ...results,
      overall: {
        success: allTestsPassed,
        message: allTestsPassed 
          ? 'All automation integration tests passed' 
          : 'Some automation integration tests failed'
      }
    }, { 
      status: allTestsPassed ? 200 : 500 
    });

  } catch (error) {
    console.error('Automation integration test error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Integration test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * POST /api/test-automation - Manual test with custom data
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, email, eventType, userData } = body;

    if (!userId || !email) {
      return NextResponse.json({
        success: false,
        error: 'userId and email are required'
      }, { status: 400 });
    }

    // Test user registration notification
    const registrationResult = await notifyUserRegistration({
      userId,
      email,
      userData: {
        signupSource: 'ordrly-main-app-manual-test',
        userType: 'homeowner',
        ...userData
      }
    });

    // Test event notification if eventType provided
    let eventResult = null;
    if (eventType) {
      eventResult = await notifyUserEvent(userId, eventType, {
        source: 'ordrly-main-app-manual-test',
        ...userData
      });
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      results: {
        userRegistration: registrationResult,
        eventNotification: eventResult
      }
    });

  } catch (error) {
    console.error('Manual automation test error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Manual test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
