import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const testType = searchParams.get('type') || 'all'

    const results: { timestamp: string; testType: string; results: Record<string, unknown> } = {
      timestamp: new Date().toISOString(),
      testType,
      results: {}
    }

    if (testType === 'all' || testType === 'database') {
      results.results.database = await testDatabaseConnectivity()
    }

    if (testType === 'all' || testType === 'features') {
      results.results.features = await testEpic6Features()
    }

    if (testType === 'all' || testType === 'environment') {
      results.results.environment = await testEnvironmentVariables()
    }

    return NextResponse.json(results)

  } catch (error) {
    console.error('Epic 6 test error:', error)
    return NextResponse.json({
      error: 'Test execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

async function testDatabaseConnectivity() {
  const supabase = await createServerClient()
  const tests = []

  try {
    // Test basic connectivity
    const { error: profilesError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)

    tests.push({
      name: 'Profiles table access',
      status: profilesError ? 'fail' : 'pass',
      message: profilesError ? profilesError.message : 'Connected successfully'
    })

    // Test Epic 6 tables
    const epic6Tables = [
      'chat_conversations',
      'chat_messages',
      'red_flags',
      'ordinance_clauses',
      'feature_usage'
    ]

    for (const table of epic6Tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('id')
          .limit(1)

        tests.push({
          name: `${table} table access`,
          status: error ? 'fail' : 'pass',
          message: error ? error.message : 'Table accessible'
        })
      } catch (tableError) {
        tests.push({
          name: `${table} table access`,
          status: 'fail',
          message: tableError instanceof Error ? tableError.message : 'Unknown error'
        })
      }
    }

  } catch (error) {
    tests.push({
      name: 'Database connectivity',
      status: 'fail',
      message: error instanceof Error ? error.message : 'Connection failed'
    })
  }

  return {
    overall: tests.every(t => t.status === 'pass') ? 'pass' : 'fail',
    tests
  }
}

async function testEpic6Features() {
  const tests = []

  // Test feature flags
  const featureFlags = [
    'NEXT_PUBLIC_EPIC6_ENABLED',
    'NEXT_PUBLIC_CHAT_ENABLED',
    'NEXT_PUBLIC_RED_FLAGS_ENABLED',
    'NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED',
    'NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED'
  ]

  featureFlags.forEach(flag => {
    const value = process.env[flag]
    tests.push({
      name: `Feature flag: ${flag}`,
      status: value ? 'pass' : 'warning',
      message: value ? `Set to ${value}` : 'Not configured'
    })
  })

  // Test AI configuration
  tests.push({
    name: 'OpenAI configuration',
    status: process.env.OPENAI_API_KEY ? 'pass' : 'fail',
    message: process.env.OPENAI_API_KEY ? 'API key configured' : 'API key missing'
  })

  tests.push({
    name: 'AI model configuration',
    status: 'pass',
    message: `Using model: ${process.env.OPENAI_MODEL || 'gpt-3.5-turbo (default)'}`
  })

  // Test external services
  const externalServices = [
    { name: 'Geoapify', env: 'GEOAPIFY_API_KEY' },
    { name: 'Stripe', env: 'STRIPE_SECRET_KEY' }
  ]

  externalServices.forEach(service => {
    tests.push({
      name: `${service.name} configuration`,
      status: process.env[service.env] ? 'pass' : 'fail',
      message: process.env[service.env] ? 'API key configured' : 'API key missing'
    })
  })

  return {
    overall: tests.filter(t => t.status === 'fail').length === 0 ? 'pass' : 'fail',
    tests
  }
}

async function testEnvironmentVariables() {
  const tests: Array<{ name: string; status: string; value?: string; error?: string; message?: string }> = []

  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'OPENAI_API_KEY',
    'GEOAPIFY_API_KEY',
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
  ]

  const optionalVars = [
    'OPENAI_MODEL',
    'NEXT_PUBLIC_EPIC6_ENABLED',
    'NEXT_PUBLIC_CHAT_ENABLED',
    'NEXT_PUBLIC_RED_FLAGS_ENABLED',
    'NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED',
    'NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED'
  ]

  requiredVars.forEach(varName => {
    const value = process.env[varName]
    tests.push({
      name: `Required: ${varName}`,
      status: value ? 'pass' : 'fail',
      message: value ? 'Set' : 'Missing required environment variable'
    })
  })

  optionalVars.forEach(varName => {
    const value = process.env[varName]
    tests.push({
      name: `Optional: ${varName}`,
      status: value ? 'pass' : 'warning',
      message: value ? `Set to ${value}` : 'Not set (using default)'
    })
  })

  return {
    overall: tests.filter(t => t.status === 'fail').length === 0 ? 'pass' : 'fail',
    tests
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()

    switch (action) {
      case 'test_api':
        // Test a simple API call
        const supabase = await createServerClient()
        const { error } = await supabase
          .from('profiles')
          .select('id')
          .limit(1)

        return NextResponse.json({
          success: !error,
          message: error ? error.message : 'API test successful'
        })

      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 })
    }

  } catch (error) {
    return NextResponse.json({
      error: 'Action execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
