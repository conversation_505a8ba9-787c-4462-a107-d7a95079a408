import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let geocodingTime = 0
  let jurisdictionTime = 0
  let searchTime = 0

  try {
    const { address, projectType } = await request.json()

    if (!address || !projectType) {
      return NextResponse.json({
        error: 'Address and project type are required'
      }, { status: 400 })
    }

    console.log(`🧪 Testing pipeline: "${address}" for ${projectType}`)

    // Step 1: Address → Coordinates (using existing address autocomplete)
    const geocodingStart = Date.now()
    const coordinates = await getCoordinatesFromAddress(address)
    geocodingTime = Date.now() - geocodingStart

    if (!coordinates) {
      return NextResponse.json({
        error: 'Could not get coordinates from address'
      })
    }

    console.log(`📍 Coordinates: ${coordinates.lat}, ${coordinates.lng}`)

    // Step 2: Coordinates → Jurisdiction (using Google + AI)
    const jurisdictionStart = Date.now()
    console.log(`🔍 Attempting jurisdiction lookup for: ${coordinates.lat}, ${coordinates.lng}`)

    const jurisdiction = await getJurisdictionFromGoogle(coordinates.lat, coordinates.lng)
    jurisdictionTime = Date.now() - jurisdictionStart

    if (!jurisdiction) {
      console.error(`❌ Jurisdiction lookup failed for coordinates: ${coordinates.lat}, ${coordinates.lng}`)
      return NextResponse.json({
        error: 'Could not determine jurisdiction from coordinates',
        coordinates,
        debug: {
          googleApiKey: !!process.env.GOOGLE_SEARCH_API_KEY,
          openaiApiKey: !!process.env.OPENAI_API_KEY
        }
      })
    }

    console.log(`🏛️ Jurisdiction: ${jurisdiction.name}, ${jurisdiction.state}`)

    // Step 3: Create search query
    const searchQuery = `${jurisdiction.name} ${jurisdiction.state} ${projectType} ordinance`
    console.log(`🔍 Search query: "${searchQuery}"`)

    // Step 4: Search for sources
    const searchStart = Date.now()
    const sources = await searchOrdinanceDocuments(jurisdiction.name, jurisdiction.state, projectType)
    searchTime = Date.now() - searchStart

    console.log(`📚 Found ${sources.length} sources`)

    const totalTime = Date.now() - startTime

    return NextResponse.json({
      coordinates,
      jurisdiction,
      searchQuery,
      sources: sources.slice(0, 5), // Return top 5 sources
      timing: {
        total: totalTime,
        geocoding: geocodingTime,
        jurisdiction: jurisdictionTime,
        search: searchTime
      }
    })

  } catch (error) {
    console.error('Pipeline test error:', error)
    return NextResponse.json({
      error: `Pipeline failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
}

async function getCoordinatesFromAddress(address: string) {
  try {
    // Use Geoapify for geocoding (same as address autocomplete)
    if (!process.env.GEOAPIFY_API_KEY) {
      throw new Error('Geoapify API key not configured')
    }

    const response = await fetch(
      `https://api.geoapify.com/v1/geocode/search?text=${encodeURIComponent(address)}&apiKey=${process.env.GEOAPIFY_API_KEY}`
    )

    if (!response.ok) {
      throw new Error(`Geoapify error: ${response.status}`)
    }

    const data = await response.json()

    if (data.features && data.features.length > 0) {
      const feature = data.features[0]
      return {
        lat: feature.geometry.coordinates[1],
        lng: feature.geometry.coordinates[0]
      }
    }

    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}

async function getJurisdictionFromGoogle(lat: number, lng: number) {
  try {
    // Try Geoapify reverse geocoding first (since we know it works)
    if (process.env.GEOAPIFY_API_KEY) {
      console.log(`🌍 Geoapify Reverse Geocoding: ${lat}, ${lng}`)
      const response = await fetch(
        `https://api.geoapify.com/v1/geocode/reverse?lat=${lat}&lon=${lng}&apiKey=${process.env.GEOAPIFY_API_KEY}`
      )

      if (response.ok) {
        const data = await response.json()
        console.log(`📊 Geoapify returned ${data.features?.length || 0} results`)

        if (data.features && data.features.length > 0) {
          const feature = data.features[0]
          const props = feature.properties

          console.log(`📍 Geoapify result:`, JSON.stringify(props, null, 2))

          // Extract jurisdiction from Geoapify response with township support
          let jurisdiction = null
          let level = 'city'

          // Priority order: township > city > town > village > county
          // Special handling for Michigan townships and other jurisdictions
          if (props.district && props.district.toLowerCase().includes('township')) {
            jurisdiction = props.district
            level = 'township'
          } else if (props.district && props.county && props.district !== props.city) {
            // Check if district might be a township (common in Michigan)
            // Georgetown Township is a common case where district="Georgetown" but it's actually Georgetown Township
            if (props.state_code === 'MI' && props.district && !props.district.toLowerCase().includes('township')) {
              jurisdiction = `${props.district} Township`
              level = 'township'
            } else {
              jurisdiction = props.district
              level = 'district'
            }
          } else if (props.city) {
            jurisdiction = props.city
            level = 'city'
          } else if (props.town) {
            jurisdiction = props.town
            level = 'town'
          } else if (props.village) {
            jurisdiction = props.village
            level = 'village'
          } else if (props.county) {
            jurisdiction = props.county
            level = 'county'
          }

          const state = props.state
          const stateCode = props.state_code

          if (jurisdiction && state) {
            console.log(`✅ Geoapify found jurisdiction: ${jurisdiction}, ${state} (${level})`)
            return {
              name: jurisdiction,
              level: level,
              state: state,
              state_code: stateCode
            }
          }
        }
      }
    }

    // Fallback to Google if Geoapify fails
    const googleGeocodingKey = process.env.GOOGLE_GEOCODING_API_KEY || process.env.GOOGLE_SEARCH_API_KEY
    if (!googleGeocodingKey) {
      console.error('❌ No geocoding APIs available')
      return null
    }

    console.log(`🌍 Trying Google Reverse Geocoding: ${lat}, ${lng}`)
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${googleGeocodingKey}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.status === 'REQUEST_DENIED') {
      console.error(`❌ Google Geocoding API not enabled: ${data.error_message}`)
      return null
    }

    if (data.results && data.results.length > 0) {
      const result = data.results[0]
      console.log(`📍 Google formatted address: ${result.formatted_address}`)

      // Simple extraction from Google
      let city = null
      let state = null
      let stateCode = null

      for (const component of result.address_components) {
        if (component.types.includes('locality') || component.types.includes('administrative_area_level_3')) {
          city = component.long_name
        } else if (component.types.includes('administrative_area_level_1')) {
          state = component.long_name
          stateCode = component.short_name
        }
      }

      if (city && state) {
        console.log(`✅ Google found jurisdiction: ${city}, ${state}`)
        return {
          name: city,
          level: 'city',
          state: state,
          state_code: stateCode
        }
      }
    }

    return null
  } catch (error) {
    console.error('Jurisdiction lookup failed:', error)
    return null
  }
}

async function searchOrdinanceDocuments(jurisdiction: string, state: string, projectType: string) {
  try {
    if (!process.env.GOOGLE_SEARCH_API_KEY || !process.env.GOOGLE_SEARCH_ENGINE_ID) {
      throw new Error('Google Search API not configured')
    }

    const searchQuery = `${jurisdiction} ${state} ${projectType} ordinance`
    const url = `https://www.googleapis.com/customsearch/v1?key=${process.env.GOOGLE_SEARCH_API_KEY}&cx=${process.env.GOOGLE_SEARCH_ENGINE_ID}&q=${encodeURIComponent(searchQuery)}&num=5`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Google Search API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.items) {
      return data.items.map((item: { title: string; link: string; snippet?: string }) => ({
        title: item.title,
        link: item.link,
        snippet: item.snippet || ''
      }))
    }

    return []
  } catch (error) {
    console.error('Search error:', error)
    return []
  }
}
