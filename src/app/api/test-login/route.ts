import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json()
    const supabase = createServiceClient()

    console.log(`Testing login for: ${email}`)

    // Try to sign in with the credentials
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      console.error('Login test error:', error)
      return NextResponse.json({
        success: false,
        error: error.message,
        code: error.code
      })
    }

    console.log('Login test successful:', data.user?.email)

    return NextResponse.json({
      success: true,
      user: {
        id: data.user?.id,
        email: data.user?.email,
        created_at: data.user?.created_at
      }
    })

  } catch (error) {
    console.error('Test login exception:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
