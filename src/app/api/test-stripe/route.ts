import { NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe/config'

export async function GET() {
  try {
    // Test if <PERSON><PERSON> is working by creating a test checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO!,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/account?success=true`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/pricing?canceled=true`,
    })

    return NextResponse.json({ 
      success: true, 
      sessionId: session.id,
      url: session.url 
    })
  } catch (error) {
    console.error('Stripe test error:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
