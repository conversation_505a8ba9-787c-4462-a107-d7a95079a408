import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

/**
 * Test endpoint to simulate webhook behavior
 * This helps debug webhook issues without needing actual Stripe events
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      userId,
      planType = 'professional',
      updateType = 'subscription',
      cancel_at_period_end = false,
      subscription_period_end = null,
      subscription_status = 'active'
    } = body

    if (!userId) {
      return NextResponse.json({ error: 'userId is required' }, { status: 400 })
    }

    console.log('Test webhook - simulating update for user:', userId, 'type:', updateType, 'planType:', planType)

    const supabase = createServiceClient()

    // Prepare update data based on type
    let updateData: any = {
      updated_at: new Date().toISOString(),
    }

    if (updateType === 'cancellation') {
      // Simulate subscription cancellation (but still active until period end)
      updateData = {
        ...updateData,
        subscription_tier: planType,
        subscription_status: subscription_status,
        is_subscribed: true, // Still subscribed until period end
        cancel_at_period_end: cancel_at_period_end,
        subscription_period_end: subscription_period_end,
      }
    } else {
      // Regular subscription update
      updateData = {
        ...updateData,
        subscription_tier: planType,
        subscription_status: 'active',
        is_subscribed: true,
        first_time_user: false, // Disable onboarding for paying customers
        cancel_at_period_end: false,
      }
    }

    // Simulate the same update that the real webhook does
    const { error: profileError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', userId)

    if (profileError) {
      console.error('Error updating profile:', profileError)
      return NextResponse.json({ error: 'Failed to update profile', details: profileError }, { status: 500 })
    }

    console.log('Profile updated successfully for user:', userId, 'with tier:', planType, 'update type:', updateType)

    // Verify the update worked by fetching the profile
    const { data: updatedProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('subscription_tier, subscription_status, is_subscribed, first_time_user, cancel_at_period_end, subscription_period_end')
      .eq('id', userId)
      .single()

    if (fetchError) {
      console.error('Error fetching updated profile:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch updated profile', details: fetchError }, { status: 500 })
    }

    console.log('Profile after update:', updatedProfile)

    return NextResponse.json({ 
      success: true, 
      message: 'Profile updated successfully',
      profile: updatedProfile
    })

  } catch (error) {
    console.error('Test webhook error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Test webhook endpoint',
    usage: {
      subscription: 'POST with { userId: "user-id", planType: "pro" }',
      cancellation: 'POST with { userId: "user-id", updateType: "cancellation", cancel_at_period_end: true, subscription_period_end: "2024-07-01T00:00:00.000Z" }'
    }
  })
}
