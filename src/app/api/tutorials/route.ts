import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  // Hide tutorials API in production - this is a work-in-progress feature
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({
      success: false,
      error: 'Tutorials are currently under development'
    }, { status: 404 })
  }

  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    const { searchParams } = new URL(request.url)

    const tutorialName = searchParams.get('tutorial_name')

    // Get tutorial progress for authenticated users
    let progress = null
    if (user && tutorialName) {
      const { data: progressData } = await supabase
        .from('tutorial_progress')
        .select('*')
        .eq('user_id', user.id)
        .eq('tutorial_name', tutorialName)
        .single()

      progress = progressData
    }

    // Return tutorial data with progress
    const tutorials = [
      {
        name: 'getting-started',
        title: 'Getting Started with Ordrly',
        description: 'Learn how to use Ordrly to check property compliance requirements',
        duration: '5 minutes',
        steps: [
          {
            id: 1,
            title: 'Enter Your Address',
            description: 'Start by typing any U.S. address in the search field',
            content: 'Our system provides autocomplete suggestions to help you find the exact location. Type slowly for better results and include city and state for accuracy.',
            tips: [
              'Type slowly for better autocomplete results',
              'Include city and state for accuracy',
              'Select from the dropdown suggestions'
            ]
          },
          {
            id: 2,
            title: 'Review Compliance Results',
            description: 'Understand your compliance report and confidence scores',
            content: 'Once you submit an address, our AI analyzes local ordinances and provides a comprehensive compliance report with confidence scores.',
            tips: [
              'Higher confidence scores mean more reliable results',
              'Check all relevant project types',
              'Review setback and height requirements carefully'
            ]
          },
          {
            id: 3,
            title: 'Export or Share Results',
            description: 'Export your compliance data and share with contractors',
            content: 'Export your compliance data as CSV files to share with contractors, or upgrade for advanced features like AI chat and detailed analysis.',
            tips: [
              'CSV files can be opened in Excel for analysis',
              'Pro plans include unlimited searches',
              'Get priority support with paid plans'
            ]
          }
        ],
        progress: progress
      },
      {
        name: 'saved-searches',
        title: 'Managing Saved Searches',
        description: 'Learn how to save and organize your compliance searches',
        duration: '5 minutes',
        steps: [
          {
            id: 1,
            title: 'Saving a Search',
            description: 'Save your search criteria for future reference',
            content: 'After performing a search, click the "Save Search" button to bookmark your results and criteria.',
            tips: [
              'Give your saved searches descriptive names',
              'Organize searches by project type',
              'Review saved searches regularly'
            ]
          },
          {
            id: 2,
            title: 'Managing Your Searches',
            description: 'Access and organize your saved searches',
            content: 'Visit the Saved Searches page to view, edit, and delete your bookmarked searches.',
            tips: [
              'Use the search filter to find specific saves',
              'Update search names for better organization',
              'Delete outdated searches to keep things clean'
            ]
          }
        ],
        progress: null
      }
    ]

    if (tutorialName) {
      const tutorial = tutorials.find(t => t.name === tutorialName)
      if (!tutorial) {
        return NextResponse.json({
          success: false,
          error: 'Tutorial not found'
        }, { status: 404 })
      }

      return NextResponse.json({
        success: true,
        tutorial
      })
    }

    return NextResponse.json({
      success: true,
      tutorials
    })

  } catch (error) {
    console.error('Tutorials fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch tutorials'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  // Hide tutorials API in production - this is a work-in-progress feature
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({
      success: false,
      error: 'Tutorials are currently under development'
    }, { status: 404 })
  }

  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { tutorial_name, current_step, completed } = body

    if (!tutorial_name) {
      return NextResponse.json({
        success: false,
        error: 'Tutorial name is required'
      }, { status: 400 })
    }

    const progressData = {
      user_id: user.id,
      tutorial_name,
      current_step: current_step || 0,
      completed: completed || false,
      completed_at: completed ? new Date().toISOString() : null,
      updated_at: new Date().toISOString()
    }

    const { data: progress, error } = await supabase
      .from('tutorial_progress')
      .upsert(progressData, { onConflict: 'user_id,tutorial_name' })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      progress
    })

  } catch (error) {
    console.error('Tutorial progress update error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update tutorial progress'
    }, { status: 500 })
  }
}
