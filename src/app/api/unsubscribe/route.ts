import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

// GET /api/unsubscribe - <PERSON>le unsubscribe requests
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Unsubscribe token required' }, { status: 400 })
    }

    // For now, return a simple unsubscribe page
    // In a full implementation, you'd verify the token and show a form
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Unsubscribe - Ordrly</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #f9fafb;
          }
          .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          }
          .logo {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
          }
          .form-group {
            margin-bottom: 20px;
          }
          label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
          }
          input[type="checkbox"] {
            margin-right: 8px;
          }
          .btn {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
          }
          .btn:hover {
            background-color: #1d4ed8;
          }
          .btn-secondary {
            background-color: #6b7280;
            margin-left: 10px;
          }
          .btn-secondary:hover {
            background-color: #4b5563;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="logo">
            <h1>Ordrly</h1>
          </div>

          <h2>Manage Your Email Preferences</h2>
          <p>We're sorry to see you go! You can choose which emails you'd like to receive from us:</p>

          <form id="unsubscribe-form">
            <input type="hidden" name="token" value="${token}">

            <div class="form-group">
              <label>
                <input type="checkbox" name="marketing" checked>
                Marketing emails and product updates
              </label>
            </div>

            <div class="form-group">
              <label>
                <input type="checkbox" name="nudges">
                Helpful reminders about your searches
              </label>
            </div>

            <div class="form-group">
              <label>
                <input type="checkbox" name="updates" checked>
                Important account and service updates
              </label>
            </div>

            <div style="margin-top: 30px;">
              <button type="submit" class="btn">Update Preferences</button>
              <button type="button" class="btn btn-secondary" onclick="unsubscribeAll()">
                Unsubscribe from All
              </button>
            </div>
          </form>

          <p style="margin-top: 30px; color: #6b7280; font-size: 14px;">
            If you have any questions, please contact <NAME_EMAIL>
          </p>
        </div>

        <script>
          document.getElementById('unsubscribe-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const preferences = {
              marketing: formData.has('marketing'),
              nudges: formData.has('nudges'),
              updates: formData.has('updates')
            };

            try {
              const response = await fetch('/api/unsubscribe', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  token: formData.get('token'),
                  preferences
                })
              });

              if (response.ok) {
                alert('Your email preferences have been updated successfully!');
              } else {
                alert('There was an error updating your preferences. Please try again.');
              }
            } catch (error) {
              alert('There was an error updating your preferences. Please try again.');
            }
          });

          async function unsubscribeAll() {
            const token = document.querySelector('input[name="token"]').value;

            try {
              const response = await fetch('/api/unsubscribe', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  token,
                  preferences: {
                    marketing: false,
                    nudges: false,
                    updates: false
                  }
                })
              });

              if (response.ok) {
                alert('You have been unsubscribed from all emails.');
              } else {
                alert('There was an error processing your request. Please try again.');
              }
            } catch (error) {
              alert('There was an error processing your request. Please try again.');
            }
          }
        </script>
      </body>
      </html>
    `

    return new Response(html, {
      headers: { 'Content-Type': 'text/html' }
    })
  } catch (error) {
    console.error('Error in GET /api/unsubscribe:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/unsubscribe - Process unsubscribe requests
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token, preferences } = body

    if (!token) {
      return NextResponse.json({ error: 'Unsubscribe token required' }, { status: 400 })
    }

    // Verify the token and extract user ID
    // In a real implementation, you'd decode the token to get the user ID
    // For now, we'll implement a basic token verification

    const supabase = await createServerClient()

    // This is a simplified implementation
    // In production, you'd properly verify the token signature
    const tokenParts = token.split('-')
    if (tokenParts.length < 2) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 400 })
    }

    // For demo purposes, we'll update preferences for the current authenticated user
    // In production, you'd extract the user ID from the verified token
    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Update email preferences
    const { error } = await supabase.rpc('update_email_preferences', {
      user_id_param: user.id,
      preferences: preferences
    })

    if (error) {
      console.error('Error updating preferences:', error)
      return NextResponse.json(
        { error: 'Failed to update preferences' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Email preferences updated successfully'
    })
  } catch (error) {
    console.error('Error in POST /api/unsubscribe:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
