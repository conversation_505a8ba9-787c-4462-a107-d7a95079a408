import { NextRequest, NextResponse } from 'next/server'
import { createServerClient, createServiceClient } from '@/lib/supabase/server'
import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY)

/**
 * POST /api/usage-alerts - Check and send usage alerts for users approaching their limit
 * This endpoint can be called by n8n workflows or scheduled tasks
 */
export async function POST(request: NextRequest) {
  try {
    // For POST requests (webhooks), use service client to bypass authentication
    // This allows n8n and other external services to call this endpoint
    const supabase = createServiceClient()

    // Get request body - can include specific user_id or check all users
    const body = await request.json().catch(() => ({}))
    const { user_id, force_check = false } = body

    if (user_id) {
      // Check specific user
      const result = await checkAndSendUserAlert(supabase, user_id, force_check)
      return NextResponse.json(result)
    } else {
      // Check all users who might need alerts (for batch processing)
      const results = await checkAllUsersForAlerts(supabase)
      return NextResponse.json({
        success: true,
        message: `Processed ${results.checked} users, sent ${results.sent} alerts`,
        details: results
      })
    }

  } catch (error) {
    console.error('Usage alerts API error:', error)
    return NextResponse.json(
      { error: 'Failed to process usage alerts' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/usage-alerts - Get usage alert status for current user
 */
export async function GET() {
  try {
    const supabase = await createServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user needs an alert
    const { data: alertData, error } = await supabase.rpc('check_usage_alert_needed', {
      user_id_param: user.id
    })

    if (error) {
      console.error('Error checking usage alert:', error)
      return NextResponse.json({ error: 'Failed to check alert status' }, { status: 500 })
    }

    const alertInfo = alertData?.[0]
    return NextResponse.json({
      needs_alert: alertInfo?.needs_alert || false,
      alert_type: alertInfo?.alert_type || '',
      current_usage: alertInfo?.current_usage || 0,
      user_email: alertInfo?.user_email || '',
      user_name: alertInfo?.user_name || ''
    })

  } catch (error) {
    console.error('Usage alerts GET error:', error)
    return NextResponse.json(
      { error: 'Failed to get alert status' },
      { status: 500 }
    )
  }
}

async function checkAndSendUserAlert(supabase: any, userId: string, forceCheck: boolean = false) {
  try {
    console.log('Checking usage alert for user:', userId)

    // Check if user needs an alert
    const { data: alertData, error: checkError } = await supabase.rpc('check_usage_alert_needed', {
      user_id_param: userId
    })

    console.log('Raw alertData:', alertData)
    console.log('Check error:', checkError)

    if (checkError) {
      console.error('Error checking usage alert for user:', userId, checkError)
      return { success: false, error: 'Failed to check alert status' }
    }

    const alertInfo = alertData?.[0]
    console.log('Alert info from database:', alertInfo)

    if (!alertInfo?.needs_alert && !forceCheck) {
      return {
        success: true,
        message: 'No alert needed',
        current_usage: alertInfo?.current_usage || 0
      }
    }

    // Get email template
    const { data: templateData, error: templateError } = await supabase.rpc('get_usage_alert_email_template', {
      alert_type_param: alertInfo.alert_type,
      user_name_param: alertInfo.user_name,
      current_usage_param: alertInfo.current_usage
    })

    if (templateError) {
      console.error('Error getting email template:', templateError)
      return { success: false, error: 'Failed to get email template' }
    }

    const template = templateData?.[0]
    if (!template) {
      return { success: false, error: 'No email template found' }
    }

    // Send email using Resend
    let emailSent = false
    try {
      console.log('Attempting to send email to:', alertInfo.user_email)
      console.log('Email subject:', template.subject)

      const emailResult = await resend.emails.send({
        from: 'Ordrly <<EMAIL>>',
        to: [alertInfo.user_email],
        subject: template.subject,
        html: template.html_body,
        text: template.text_body,
      })

      console.log('Resend API response:', emailResult)
      emailSent = !!emailResult.data?.id
      console.log('Usage alert email sent:', emailResult.data?.id)
    } catch (emailError) {
      console.error('Error sending usage alert email:', emailError)
      emailSent = false
    }

    // Record that alert was sent (or attempted)
    const { error: recordError } = await supabase.rpc('record_usage_alert', {
      user_id_param: userId,
      alert_type_param: alertInfo.alert_type,
      searches_count_param: alertInfo.current_usage,
      email_sent_param: emailSent
    })

    if (recordError) {
      console.error('Error recording usage alert:', recordError)
    }

    return {
      success: true,
      message: emailSent ? 'Alert sent successfully' : 'Alert recorded but email failed',
      alert_type: alertInfo.alert_type,
      current_usage: alertInfo.current_usage,
      email_sent: emailSent
    }

  } catch (error) {
    console.error('Error in checkAndSendUserAlert:', error)
    return { success: false, error: 'Internal error' }
  }
}

async function checkAllUsersForAlerts(supabase: any) {
  try {
    // Get all free tier users who might need alerts (8+ searches, no recent alert)
    const { data: users, error } = await supabase
      .from('profiles')
      .select('id, email, name, pulls_this_month, subscription_tier')
      .eq('subscription_tier', 'free')
      .gte('pulls_this_month', 8)

    if (error) {
      console.error('Error fetching users for alerts:', error)
      return { checked: 0, sent: 0, errors: 1 }
    }

    let checked = 0
    let sent = 0
    let errors = 0

    for (const user of users || []) {
      checked++
      try {
        const result = await checkAndSendUserAlert(supabase, user.id)
        if (result.success && result.email_sent) {
          sent++
        }
      } catch (error) {
        console.error(`Error processing alert for user ${user.id}:`, error)
        errors++
      }
    }

    return { checked, sent, errors }

  } catch (error) {
    console.error('Error in checkAllUsersForAlerts:', error)
    return { checked: 0, sent: 0, errors: 1 }
  }
}
