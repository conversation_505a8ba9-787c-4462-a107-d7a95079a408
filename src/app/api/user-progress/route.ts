import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const featureName = searchParams.get('feature_name')

    let query = supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', user.id)

    if (featureName) {
      query = query.eq('feature_name', featureName)
    }

    const { data: progress, error } = await query

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      progress: progress || []
    })

  } catch (error) {
    console.error('User progress fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch progress'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { feature_name, progress_percentage, completed_steps, total_steps, last_step_completed } = body

    if (!feature_name) {
      return NextResponse.json({
        success: false,
        error: 'Feature name is required'
      }, { status: 400 })
    }

    // Validate progress percentage
    if (progress_percentage !== undefined && (progress_percentage < 0 || progress_percentage > 100)) {
      return NextResponse.json({
        success: false,
        error: 'Progress percentage must be between 0 and 100'
      }, { status: 400 })
    }

    const progressData = {
      user_id: user.id,
      feature_name,
      progress_percentage: progress_percentage || 0,
      completed_steps: completed_steps || [],
      total_steps: total_steps || 0,
      last_step_completed: last_step_completed || null,
      updated_at: new Date().toISOString()
    }

    const { data: newProgress, error } = await supabase
      .from('user_progress')
      .upsert(progressData, { onConflict: 'user_id,feature_name' })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      progress: newProgress
    })

  } catch (error) {
    console.error('User progress update error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update progress'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { feature_name, step_name } = body

    if (!feature_name || !step_name) {
      return NextResponse.json({
        success: false,
        error: 'Feature name and step name are required'
      }, { status: 400 })
    }

    // Get current progress
    const { data: currentProgress } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', user.id)
      .eq('feature_name', feature_name)
      .single()

    const completedSteps = currentProgress?.completed_steps || []
    
    // Add step if not already completed
    if (!completedSteps.includes(step_name)) {
      completedSteps.push(step_name)
    }

    const totalSteps = currentProgress?.total_steps || completedSteps.length
    const progressPercentage = totalSteps > 0 ? Math.round((completedSteps.length / totalSteps) * 100) : 0

    const progressData = {
      user_id: user.id,
      feature_name,
      progress_percentage: progressPercentage,
      completed_steps: completedSteps,
      total_steps: totalSteps,
      last_step_completed: step_name,
      updated_at: new Date().toISOString()
    }

    const { data: updatedProgress, error } = await supabase
      .from('user_progress')
      .upsert(progressData, { onConflict: 'user_id,feature_name' })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      progress: updatedProgress
    })

  } catch (error) {
    console.error('User progress step completion error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to complete step'
    }, { status: 500 })
  }
}
