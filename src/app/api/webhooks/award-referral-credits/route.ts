import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { sendReferralCreditsEarnedEmail } from '@/lib/email/sender'

// Create Supabase client with service role key for webhook operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// POST /api/webhooks/award-referral-credits - Award credits for verified referral (webhook endpoint)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { referee_id } = body

    if (!referee_id) {
      return NextResponse.json(
        { error: 'referee_id is required' },
        { status: 400 }
      )
    }

    console.log('🎯 Webhook: Awarding referral credits for referee:', referee_id)

    // Award referral credits using the database function
    const { data: success, error } = await supabase.rpc(
      'award_referral_credits',
      { referee_id_param: referee_id }
    )

    if (error) {
      console.error('Error awarding referral credits:', error)
      return NextResponse.json(
        { error: 'Failed to award credits' },
        { status: 500 }
      )
    }

    if (!success) {
      console.log('⚠️ No pending referral found or credits already awarded')
      return NextResponse.json({
        success: false,
        message: 'No pending referral found or credits already awarded'
      })
    }

    console.log('✅ Referral credits awarded successfully')

    // Get updated referrer info for notification
    const { data: referralInfo } = await supabase
      .from('referrals')
      .select(`
        referrer_id,
        credits_awarded
      `)
      .eq('referee_id', referee_id)
      .eq('status', 'credited')
      .single()

    // Get referrer profile for email notification
    let emailSent = false
    if (referralInfo?.referrer_id) {
      const { data: referrerProfile } = await supabase
        .from('profiles')
        .select('email, name, referral_code, extra_credits')
        .eq('id', referralInfo.referrer_id)
        .single()

      if (referrerProfile?.email && referrerProfile?.referral_code) {
        try {
          const referralUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/signup?ref=${referrerProfile.referral_code}`
          await sendReferralCreditsEarnedEmail(
            referrerProfile.email,
            referrerProfile.extra_credits || 0,
            referralUrl,
            referrerProfile.name
          )
          emailSent = true
          console.log('📧 Referral credits email sent to:', referrerProfile.email)
        } catch (emailError) {
          console.error('Failed to send referral credits email:', emailError)
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Referral credits awarded successfully',
      creditsAwarded: 5,
      referrerNotified: emailSent
    })
  } catch (error) {
    console.error('Error in POST /api/webhooks/award-referral-credits:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
