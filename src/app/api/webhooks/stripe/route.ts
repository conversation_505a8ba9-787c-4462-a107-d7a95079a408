import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe/config'
import { createServiceClient } from '@/lib/supabase/server'
import { sendCreditsAddedEmail, sendSubscriptionConfirmedEmail, sendSubscriptionCanceledEmail, sendPaymentFailedEmail } from '@/lib/email/sender'
import <PERSON><PERSON> from 'stripe'

// Helper function to safely access subscription properties
function getSubscriptionPeriods(subscription: Record<string, unknown>) {
  const start = subscription.current_period_start as number
  const end = subscription.current_period_end as number

  return {
    current_period_start: (start && start > 0) ? start : Math.floor(Date.now() / 1000),
    current_period_end: (end && end > 0) ? end : Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // Default to 30 days from now
    cancel_at_period_end: (subscription.cancel_at_period_end as boolean) || false,
  }
}

// Helper function to safely convert timestamp to ISO string
function safeTimestampToISO(timestamp: number): string {
  try {
    if (!timestamp || timestamp <= 0) {
      return new Date().toISOString()
    }
    const date = new Date(timestamp * 1000)
    if (isNaN(date.getTime())) {
      return new Date().toISOString()
    }
    return date.toISOString()
  } catch (error) {
    console.error('Error converting timestamp to ISO:', timestamp, error)
    return new Date().toISOString()
  }
}

export async function POST(request: NextRequest) {
  const body = await request.text()
  const headersList = await headers()
  const signature = headersList.get('stripe-signature')

  console.log('Webhook received:', {
    hasSignature: !!signature,
    bodyLength: body.length,
    timestamp: new Date().toISOString()
  })

  if (!signature) {
    console.error('No Stripe signature found in headers')
    return NextResponse.json({ error: 'No signature' }, { status: 400 })
  }

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
    console.log('Webhook event verified:', event.type, event.id)
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
  }

  const supabase = createServiceClient()

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        const userId = session.metadata?.supabase_user_id
        const planType = session.metadata?.plan_type
        const type = session.metadata?.type

        console.log('Processing checkout.session.completed:', {
          sessionId: session.id,
          userId,
          planType,
          type,
          paymentStatus: session.payment_status,
          subscriptionId: session.subscription,
          metadata: session.metadata
        })

        if (userId) {
          if (type === 'pull_pack') {
            // Handle pull pack purchase
            const credits = parseInt(session.metadata?.credits || '5')

            // Get current profile for email
            const { data: profile } = await supabase
              .from('profiles')
              .select('email, name, extra_credits')
              .eq('id', userId)
              .single()

            // Use the custom function to increment credits
            await supabase.rpc('increment_extra_credits', {
              user_id_param: userId,
              credits_to_add: credits
            })

            // Send email notification
            if (profile?.email) {
              await sendCreditsAddedEmail(
                profile.email,
                credits,
                (profile.extra_credits || 0) + credits,
                profile.name
              )
            }
          } else if (planType) {
            // Handle subscription
            console.log('Updating user subscription:', { userId, planType })

            // Get current profile for email
            const { data: profile } = await supabase
              .from('profiles')
              .select('email, name')
              .eq('id', userId)
              .single()

            const { error: profileError } = await supabase
              .from('profiles')
              .update({
                subscription_tier: planType,
                subscription_status: 'active',
                is_subscribed: true,
                first_time_user: false, // Disable onboarding for paying customers
                updated_at: new Date().toISOString(),
              })
              .eq('id', userId)

            if (profileError) {
              console.error('Error updating profile:', profileError)
              throw profileError
            }

            // Send subscription confirmation email
            if (profile?.email && session.amount_total) {
              try {
                const planName = planType.charAt(0).toUpperCase() + planType.slice(1)
                const amount = `$${(session.amount_total / 100).toFixed(2)}`

                await sendSubscriptionConfirmedEmail(
                  profile.email,
                  planName,
                  amount,
                  profile.name || profile.email.split('@')[0]
                )
                console.log('Subscription confirmation email sent to:', profile.email)
              } catch (emailError) {
                console.error('Error sending subscription confirmation email:', emailError)
                // Don't fail the webhook if email sending fails
              }
            }

            console.log('Profile updated successfully for user:', userId, 'with tier:', planType)

            // Verify the update worked by fetching the profile
            const { data: updatedProfile } = await supabase
              .from('profiles')
              .select('subscription_tier, subscription_status, is_subscribed, first_time_user')
              .eq('id', userId)
              .single()

            console.log('Profile after update:', updatedProfile)

            // Create subscription record
            if (session.subscription) {
              console.log('Creating subscription record for:', session.subscription)

              const subscription = await stripe.subscriptions.retrieve(
                session.subscription as string
              )

              // Check for existing active subscriptions for this customer
              const existingSubscriptions = await stripe.subscriptions.list({
                customer: subscription.customer as string,
                status: 'active',
                limit: 10
              })

              // If there are multiple active subscriptions, log a warning
              if (existingSubscriptions.data.length > 1) {
                console.warn(`⚠️  Customer ${subscription.customer} has ${existingSubscriptions.data.length} active subscriptions:`,
                  existingSubscriptions.data.map(sub => ({ id: sub.id, created: sub.created }))
                )

                // Optionally, cancel older subscriptions (uncomment if you want automatic cleanup)
                // const sortedSubs = existingSubscriptions.data.sort((a, b) => b.created - a.created)
                // const duplicates = sortedSubs.slice(1) // Keep the newest, cancel the rest
                // for (const duplicate of duplicates) {
                //   console.log(`Canceling duplicate subscription: ${duplicate.id}`)
                //   await stripe.subscriptions.cancel(duplicate.id)
                // }
              }

              const periods = getSubscriptionPeriods(subscription as unknown as Record<string, unknown>)

              const { error: subscriptionError } = await supabase.from('subscriptions').upsert({
                user_id: userId,
                stripe_subscription_id: subscription.id,
                stripe_customer_id: subscription.customer as string,
                status: subscription.status,
                price_id: subscription.items.data[0]?.price.id,
                current_period_start: safeTimestampToISO(periods.current_period_start),
                current_period_end: safeTimestampToISO(periods.current_period_end),
                cancel_at_period_end: periods.cancel_at_period_end,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })

              if (subscriptionError) {
                console.error('Error creating subscription record:', subscriptionError)
                throw subscriptionError
              }

              console.log('Subscription record created successfully')
            } else {
              console.log('No subscription ID found in session')
            }
          }
        } else {
          console.log('No user ID found in session metadata')
        }
        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        const userId = await getUserIdFromCustomer(subscription.customer as string)

        console.log('Processing customer.subscription.updated:', {
          subscriptionId: subscription.id,
          userId,
          status: subscription.status,
          cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
          currentPeriodEnd: (subscription as any).current_period_end,
          rawTimestamp: (subscription as any).current_period_end
        })

        if (userId) {
          const planType = getPlanTypeFromPriceId(subscription.items.data[0]?.price.id)
          const periods = getSubscriptionPeriods(subscription as unknown as Record<string, unknown>)

          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              subscription_tier: planType,
              subscription_status: subscription.status,
              subscription_period_end: safeTimestampToISO(periods.current_period_end),
              cancel_at_period_end: periods.cancel_at_period_end,
              updated_at: new Date().toISOString(),
            })
            .eq('id', userId)

          if (profileError) {
            console.error('Error updating profile for subscription update:', profileError)
          } else {
            console.log('Profile updated for subscription change:', {
              userId,
              status: subscription.status,
              cancelAtPeriodEnd: periods.cancel_at_period_end
            })
          }

          const { error: subscriptionError } = await supabase
            .from('subscriptions')
            .update({
              status: subscription.status,
              current_period_start: safeTimestampToISO(periods.current_period_start),
              current_period_end: safeTimestampToISO(periods.current_period_end),
              cancel_at_period_end: periods.cancel_at_period_end,
              updated_at: new Date().toISOString(),
            })
            .eq('stripe_subscription_id', subscription.id)

          if (subscriptionError) {
            console.error('Error updating subscription record:', subscriptionError)
          }
        }
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        const userId = await getUserIdFromCustomer(subscription.customer as string)

        if (userId) {
          // Get profile for email before updating
          const { data: profile } = await supabase
            .from('profiles')
            .select('email, name, subscription_period_end')
            .eq('id', userId)
            .single()

          await supabase
            .from('profiles')
            .update({
              subscription_tier: 'free',
              subscription_status: 'canceled',
              is_subscribed: false,
              updated_at: new Date().toISOString(),
            })
            .eq('id', userId)

          await supabase
            .from('subscriptions')
            .update({
              status: 'canceled',
              ended_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })
            .eq('stripe_subscription_id', subscription.id)

          // Send cancellation email
          if (profile?.email) {
            try {
              const endDate = profile.subscription_period_end
                ? new Date(profile.subscription_period_end).toLocaleDateString()
                : new Date().toLocaleDateString()

              await sendSubscriptionCanceledEmail(
                profile.email,
                endDate,
                profile.name || profile.email.split('@')[0]
              )
              console.log('Subscription cancellation email sent to:', profile.email)
            } catch (emailError) {
              console.error('Error sending cancellation email:', emailError)
              // Don't fail the webhook if email sending fails
            }
          }
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        const userId = await getUserIdFromCustomer(invoice.customer as string)

        if (userId) {
          // Get profile for email
          const { data: profile } = await supabase
            .from('profiles')
            .select('email, name, subscription_tier')
            .eq('id', userId)
            .single()

          if (profile?.email) {
            try {
              const planName = profile.subscription_tier?.charAt(0).toUpperCase() + profile.subscription_tier?.slice(1) || 'Pro'
              const retryDate = invoice.next_payment_attempt
                ? new Date(invoice.next_payment_attempt * 1000).toLocaleDateString()
                : new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString() // 3 days from now

              await sendPaymentFailedEmail(
                profile.email,
                planName,
                retryDate,
                profile.name || profile.email.split('@')[0]
              )
              console.log('Payment failed email sent to:', profile.email)
            } catch (emailError) {
              console.error('Error sending payment failed email:', emailError)
              // Don't fail the webhook if email sending fails
            }
          }
        }
        break
      }

      case 'charge.refunded': {
        const charge = event.data.object as Stripe.Charge
        const userId = await getUserIdFromCustomer(charge.customer as string)

        console.log('Processing charge.refunded:', {
          chargeId: charge.id,
          userId,
          amountRefunded: charge.amount_refunded,
          refunded: charge.refunded,
          customerId: charge.customer
        })

        if (userId) {
          // Get profile for logging
          const { data: profile } = await supabase
            .from('profiles')
            .select('email, subscription_tier, subscription_status')
            .eq('id', userId)
            .single()

          console.log('Refund processed for user:', {
            userId,
            email: profile?.email,
            currentTier: profile?.subscription_tier,
            currentStatus: profile?.subscription_status,
            refundAmount: charge.amount_refunded / 100 // Convert from cents
          })

          // Note: We don't automatically downgrade on refund
          // The subscription cancellation webhook will handle access changes
          // This event is mainly for logging and potential future email notifications
        }
        break
      }

      case 'invoice.payment_action_required': {
        const invoice = event.data.object as Stripe.Invoice
        const userId = await getUserIdFromCustomer(invoice.customer as string)

        console.log('Processing invoice.payment_action_required:', {
          invoiceId: invoice.id,
          userId,
          status: invoice.status,
          customerId: invoice.customer
        })

        if (userId) {
          // Get profile for potential email notification
          const { data: profile } = await supabase
            .from('profiles')
            .select('email, name, subscription_tier')
            .eq('id', userId)
            .single()

          console.log('Payment action required for user:', {
            userId,
            email: profile?.email,
            invoiceId: invoice.id
          })

          // Future: Could send email notification about required payment action
        }
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 })
  }
}

async function getUserIdFromCustomer(customerId: string): Promise<string | null> {
  const supabase = createServiceClient()
  const { data } = await supabase
    .from('profiles')
    .select('id')
    .eq('stripe_customer_id', customerId)
    .single()

  return data?.id || null
}

function getPlanTypeFromPriceId(priceId: string): string {
  // New pricing structure
  if (priceId === process.env.STRIPE_PRICE_STARTER_MONTHLY || priceId === process.env.STRIPE_PRICE_STARTER_ANNUAL) return 'starter'
  if (priceId === process.env.STRIPE_PRICE_PROFESSIONAL_MONTHLY || priceId === process.env.STRIPE_PRICE_PROFESSIONAL_ANNUAL) return 'professional'

  // Legacy pricing for backward compatibility
  if (priceId === process.env.STRIPE_PRICE_PRO) return 'pro'
  if (priceId === process.env.STRIPE_PRICE_APPRAISER) return 'appraiser'

  return 'free'
}
