import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const type = searchParams.get('type')
  const next = searchParams.get('next') ?? '/chat'



  if (code) {
    const supabase = await createServerClient()

    const { data, error } = await supabase.auth.exchangeCodeForSession(code)

    if (!error && data.user) {
      // Check if profile exists, create if not
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', data.user.id)
        .single()

      if (!existingProfile) {
        // Generate unique referral code
        const userReferralCode = `${data.user.email?.split('@')[0]?.toUpperCase() || 'USER'}${Math.random().toString(36).substr(2, 4).toUpperCase()}`

        // Create profile for OAuth user with 7-day trial
        await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            email: data.user.email,
            name: data.user.user_metadata?.full_name || data.user.user_metadata?.name,
            subscription_tier: 'trial',
            trial_start_date: new Date().toISOString(),
            trial_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            pulls_this_month: 0,
            extra_credits: 0,
            referral_code: userReferralCode,
            first_time_user: true,
          })
      }

      // Handle password reset flow
      if (type === 'recovery') {
        const resetUrl = new URL(`${origin}/reset-password`)
        const response = NextResponse.redirect(resetUrl.toString())
        response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
        response.headers.set('Pragma', 'no-cache')
        response.headers.set('Expires', '0')
        return response
      }

      // Simple redirect without auth_callback parameter
      const redirectUrl = new URL(`${origin}${next}`)

      // Force a cache revalidation to ensure fresh auth state
      const response = NextResponse.redirect(redirectUrl.toString())
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', '0')

      return response
    }
  }

  // Return the user to an error page with instructions
  const errorUrl = `${origin}/login?error=Authentication failed`
  return NextResponse.redirect(errorUrl)
}
