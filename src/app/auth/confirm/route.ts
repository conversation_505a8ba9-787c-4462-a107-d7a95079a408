import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { sendWelcomeEmail } from '@/lib/email/sender'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type')
  const next = searchParams.get('next') ?? '/account'

  // Create redirect link without the secret token
  const redirectTo = new URL(request.url)
  redirectTo.pathname = next
  redirectTo.searchParams.delete('token_hash')
  redirectTo.searchParams.delete('type')
  redirectTo.searchParams.delete('next')

  if (token_hash && type) {
    const supabase = await createServerClient()

    const { error, data } = await supabase.auth.verifyOtp({
      type: type as 'email',
      token_hash,
    })

    if (!error && data.user) {
      // Award referral credits if this is an email verification
      if (type === 'email') {
        try {
          await supabase.rpc('award_referral_credits', {
            referee_id_param: data.user.id
          })
        } catch (referralError) {
          console.error('Error awarding referral credits:', referralError)
          // Don't fail the email verification if referral credit awarding fails
        }

        // Send welcome email after email verification
        try {
          await sendWelcomeEmail(
            data.user.email!,
            data.user.user_metadata?.name || data.user.email?.split('@')[0]
          )
          console.log('Welcome email sent after email verification to:', data.user.email)
        } catch (emailError) {
          console.error('Error sending welcome email after verification:', emailError)
          // Don't fail the verification if email sending fails
        }
      }

      redirectTo.searchParams.delete('next')
      redirectTo.searchParams.set('refresh', 'true')
      return NextResponse.redirect(redirectTo)
    }
  }

  // Return the user to an error page with some instructions
  redirectTo.pathname = '/login'
  redirectTo.searchParams.set('error', 'Email confirmation failed')
  return NextResponse.redirect(redirectTo)
}
