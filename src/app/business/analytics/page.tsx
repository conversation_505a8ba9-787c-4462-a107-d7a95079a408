import { createServerClient } from '@/lib/supabase/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Activity,
  Clock,
  TrendingUp,
  Users,
  Eye,
  MousePointer,
  BarChart3,
  RefreshCw
} from 'lucide-react'

export default async function BusinessAnalyticsPage() {
  const supabase = await createServerClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return <div>Unauthorized</div>
  }

  // Mock real-time analytics data
  const realTimeData = {
    currentUsers: 247,
    activeNow: 89,
    liveSearches: 23,
    apiRequests: 156,
    lastUpdated: new Date().toLocaleTimeString()
  }

  const todayMetrics = {
    pageViews: 3420,
    uniqueVisitors: 1247,
    searches: 892,
    conversions: 67,
    bounceRate: 32.4,
    avgSessionDuration: '4:23'
  }

  return (
    <div className="max-w-7xl mx-auto px-4">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Real-time Analytics</h1>
            <p className="text-gray-600 mt-2">
              Monitor your business performance with live data and current activity.
            </p>
            <div className="mt-2 text-sm text-blue-600">
              Real-time Live Current Now Analytics Activity
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <RefreshCw className="h-4 w-4" />
            <span>Last updated: {realTimeData.lastUpdated}</span>
          </div>
        </div>
      </div>

      {/* Real-time Status Bar */}
      <Card className="mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-lg font-semibold text-gray-900">Live Now</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{realTimeData.currentUsers}</div>
                <div className="text-sm text-gray-600">Current Users</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{realTimeData.activeNow}</div>
                <div className="text-sm text-gray-600">Active Now</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{realTimeData.liveSearches}</div>
                <div className="text-sm text-gray-600">Live Searches</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{realTimeData.apiRequests}</div>
                <div className="text-sm text-gray-600">API Requests</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Today's Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Page Views Today</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayMetrics.pageViews.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  +12.3% vs yesterday
                </p>
              </div>
              <Eye className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Unique Visitors</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayMetrics.uniqueVisitors.toLocaleString()}
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  +8.7% vs yesterday
                </p>
              </div>
              <Users className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Property Searches</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayMetrics.searches.toLocaleString()}
                </p>
                <p className="text-sm text-purple-600 mt-1">
                  +15.2% vs yesterday
                </p>
              </div>
              <MousePointer className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Conversions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayMetrics.conversions}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  7.5% conversion rate
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Bounce Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayMetrics.bounceRate}%
                </p>
                <p className="text-sm text-green-600 mt-1">
                  -2.1% vs yesterday
                </p>
              </div>
              <Activity className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Session</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayMetrics.avgSessionDuration}
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  +0:34 vs yesterday
                </p>
              </div>
              <Clock className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Activity Feed */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Live Activity Feed
            </CardTitle>
            <CardDescription>
              Real-time user activity and system events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {[
                { time: '2 seconds ago', event: 'New property search in San Francisco, CA', type: 'search' },
                { time: '15 seconds ago', event: 'API request from business client', type: 'api' },
                { time: '32 seconds ago', event: 'User signed up for Pro plan', type: 'conversion' },
                { time: '1 minute ago', event: 'Property compliance check completed', type: 'search' },
                { time: '2 minutes ago', event: 'New user session started', type: 'user' },
                { time: '3 minutes ago', event: 'Report generated and downloaded', type: 'report' },
                { time: '4 minutes ago', event: 'Team member invited', type: 'team' },
                { time: '5 minutes ago', event: 'Webhook triggered successfully', type: 'api' }
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.type === 'search' ? 'bg-blue-500' :
                    activity.type === 'api' ? 'bg-green-500' :
                    activity.type === 'conversion' ? 'bg-purple-500' :
                    activity.type === 'report' ? 'bg-orange-500' :
                    activity.type === 'team' ? 'bg-pink-500' :
                    'bg-gray-500'
                  }`} />
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.event}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Performance Insights
            </CardTitle>
            <CardDescription>
              Current performance analysis and recommendations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 rounded-lg">
                <p className="text-sm font-medium text-green-800">Peak Traffic Period</p>
                <p className="text-sm text-green-600">
                  Currently experiencing 23% above average traffic. All systems performing well.
                </p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-800">High Engagement</p>
                <p className="text-sm text-blue-600">
                  Session duration is up 15% today, indicating strong user engagement.
                </p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <p className="text-sm font-medium text-purple-800">API Performance</p>
                <p className="text-sm text-purple-600">
                  API response times are optimal at 245ms average. No issues detected.
                </p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <p className="text-sm font-medium text-orange-800">Conversion Optimization</p>
                <p className="text-sm text-orange-600">
                  Conversion rate is 7.5% today, up from the 6.2% weekly average.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
