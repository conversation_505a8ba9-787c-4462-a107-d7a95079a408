import { createServerClient } from '@/lib/supabase/server'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { BarChart3, TrendingUp, Clock, AlertCircle, Activity, Zap, Globe, Shield } from 'lucide-react'

export default async function APIAnalyticsPage() {
  // SECURITY: All business routes now require proper authentication
  const supabase = await createServerClient()

  const {
    data: { user: authUser },
  } = await supabase.auth.getUser()

  if (!authUser) {
    return <div>Unauthorized</div>
  }

  // Mock API analytics data
  const apiStats = {
    totalRequests: 89420,
    requestsToday: 3240,
    successRate: 99.7,
    avgResponseTime: 245,
    rateLimitHits: 12,
    quota: {
      used: 18940,
      limit: 50000,
      percentage: 37.9
    }
  }

  const endpointStats = [
    {
      endpoint: '/api/v1/search',
      requests: 45230,
      avgResponseTime: 180,
      successRate: 99.8,
      errors: 89
    },
    {
      endpoint: '/api/v1/properties',
      requests: 23450,
      avgResponseTime: 220,
      successRate: 99.5,
      errors: 117
    },
    {
      endpoint: '/api/v1/analytics',
      requests: 12340,
      avgResponseTime: 340,
      successRate: 99.9,
      errors: 12
    },
    {
      endpoint: '/api/v1/reports',
      requests: 8400,
      avgResponseTime: 890,
      successRate: 98.9,
      errors: 92
    }
  ]

  const recentErrors = [
    {
      id: 1,
      endpoint: '/api/v1/search',
      error: '429 Rate Limit Exceeded',
      timestamp: '2024-01-15 14:30:00',
      ip: '*************'
    },
    {
      id: 2,
      endpoint: '/api/v1/properties',
      error: '500 Internal Server Error',
      timestamp: '2024-01-15 13:45:00',
      ip: '*********'
    },
    {
      id: 3,
      endpoint: '/api/v1/analytics',
      error: '401 Unauthorized',
      timestamp: '2024-01-15 12:20:00',
      ip: '***********'
    }
  ]

  return (
    <div className="max-w-7xl mx-auto px-4">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">API Usage Analytics</h1>
            <p className="text-gray-600 mt-2">
              Monitor API performance, usage patterns, and rate limits for your applications.
            </p>
            <div className="mt-2 text-sm text-blue-600">
              Requests Rate Limit Usage Quota Performance Analytics Monitoring
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              View Charts
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Activity className="h-4 w-4 mr-2" />
              Real-time Monitor
            </Button>
          </div>
        </div>
      </div>

      {/* API Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold text-gray-900">
                  {apiStats.totalRequests.toLocaleString()}
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  +{apiStats.requestsToday.toLocaleString()} today
                </p>
              </div>
              <Globe className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{apiStats.successRate}%</p>
                <p className="text-sm text-green-600 mt-1">Excellent performance</p>
              </div>
              <Shield className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                <p className="text-2xl font-bold text-gray-900">{apiStats.avgResponseTime}ms</p>
                <p className="text-sm text-green-600 mt-1">Fast response</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rate Limit Hits</p>
                <p className="text-2xl font-bold text-gray-900">{apiStats.rateLimitHits}</p>
                <p className="text-sm text-orange-600 mt-1">Monitor closely</p>
              </div>
              <AlertCircle className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage Quota */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Monthly Usage Quota
          </CardTitle>
          <CardDescription>
            Track your API usage against monthly limits and quotas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">API Requests</span>
              <span className="text-sm text-gray-600">
                {apiStats.quota.used.toLocaleString()} / {apiStats.quota.limit.toLocaleString()}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${apiStats.quota.percentage}%` }}
              ></div>
            </div>
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{apiStats.quota.percentage}% used</span>
              <span>{(apiStats.quota.limit - apiStats.quota.used).toLocaleString()} remaining</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Endpoint Performance */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Endpoint Performance</CardTitle>
              <CardDescription>
                Performance metrics for your most used API endpoints
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              View Trends
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {endpointStats.map((endpoint, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{endpoint.endpoint}</h3>
                  <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                    <span>{endpoint.requests.toLocaleString()} requests</span>
                    <span>•</span>
                    <span>{endpoint.avgResponseTime}ms avg</span>
                    <span>•</span>
                    <span>{endpoint.successRate}% success</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Badge variant={endpoint.successRate > 99 ? "default" : "secondary"}>
                    {endpoint.successRate}%
                  </Badge>
                  <Badge variant={endpoint.errors > 50 ? "destructive" : "outline"}>
                    {endpoint.errors} errors
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Errors and Monitoring */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              Recent Errors
            </CardTitle>
            <CardDescription>
              Latest API errors and issues requiring attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentErrors.map((error) => (
                <div key={error.id} className="p-3 border border-red-200 bg-red-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-red-800">{error.error}</h4>
                    <Badge variant="destructive">Error</Badge>
                  </div>
                  <p className="text-sm text-red-600 mb-1">{error.endpoint}</p>
                  <div className="flex items-center space-x-4 text-xs text-red-500">
                    <span>{new Date(error.timestamp).toLocaleString()}</span>
                    <span>IP: {error.ip}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Real-time Monitoring
            </CardTitle>
            <CardDescription>
              Live API performance and health metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-green-800">API Health</h4>
                  <Badge className="bg-green-100 text-green-800">Healthy</Badge>
                </div>
                <p className="text-sm text-green-600">All endpoints responding normally</p>
              </div>

              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-blue-800">Current Load</h4>
                  <Badge className="bg-blue-100 text-blue-800">Normal</Badge>
                </div>
                <p className="text-sm text-blue-600">47 requests/minute</p>
              </div>

              <div className="p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-yellow-800">Rate Limits</h4>
                  <Badge className="bg-yellow-100 text-yellow-800">Monitor</Badge>
                </div>
                <p className="text-sm text-yellow-600">12 rate limit hits today</p>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <Button className="w-full" variant="outline">
                  <Clock className="h-4 w-4 mr-2" />
                  View Detailed Metrics
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
