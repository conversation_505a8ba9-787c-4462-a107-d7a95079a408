import { createServerClient } from '@/lib/supabase/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DollarSign,
  Users,
  TrendingUp,
  BarChart3,
  Eye,
  Target,
  FileText
} from 'lucide-react'

export default async function BusinessDashboardPage() {
  // SECURITY: All business routes now require proper authentication
  const supabase = await createServerClient()

  const {
    data: { user: authUser },
  } = await supabase.auth.getUser()

  if (!authUser) {
    return <div>Unauthorized</div>
  }

  // Mock business metrics data (will be replaced with real data later)
  const businessMetrics = {
    revenue: {
      current: 45250,
      previous: 38900,
      growth: 16.3
    },
    customers: {
      total: 1247,
      new: 89,
      growth: 7.7
    },
    searches: {
      total: 15420,
      thisMonth: 3240,
      growth: 12.4
    },
    apiUsage: {
      requests: 89420,
      thisMonth: 18940,
      growth: 23.1
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4" data-testid="business-dashboard">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Business Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Monitor your business performance, revenue, customers, and growth metrics.
        </p>
        <div className="mt-2 text-sm text-blue-600">
          Revenue Customers Growth Metrics Performance Analytics
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 business-metrics">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${businessMetrics.revenue.current.toLocaleString()}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  +{businessMetrics.revenue.growth}% from last month
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {businessMetrics.customers.total.toLocaleString()}
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  +{businessMetrics.customers.new} new this month
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Property Searches</p>
                <p className="text-2xl font-bold text-gray-900">
                  {businessMetrics.searches.total.toLocaleString()}
                </p>
                <p className="text-sm text-purple-600 mt-1">
                  +{businessMetrics.searches.growth}% growth
                </p>
              </div>
              <Eye className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">API Requests</p>
                <p className="text-2xl font-bold text-gray-900">
                  {businessMetrics.apiUsage.requests.toLocaleString()}
                </p>
                <p className="text-sm text-orange-600 mt-1">
                  +{businessMetrics.apiUsage.growth}% this month
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Growth Metrics
            </CardTitle>
            <CardDescription>
              Key performance indicators and growth trends
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Revenue Growth</span>
                <span className="font-semibold text-green-600">+{businessMetrics.revenue.growth}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Customer Growth</span>
                <span className="font-semibold text-blue-600">+{businessMetrics.customers.growth}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Search Volume Growth</span>
                <span className="font-semibold text-purple-600">+{businessMetrics.searches.growth}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">API Usage Growth</span>
                <span className="font-semibold text-orange-600">+{businessMetrics.apiUsage.growth}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Business Insights
            </CardTitle>
            <CardDescription>
              Key insights and recommendations for your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 rounded-lg">
                <p className="text-sm font-medium text-green-800">Strong Revenue Growth</p>
                <p className="text-sm text-green-600">
                  Your revenue is up {businessMetrics.revenue.growth}% this month, exceeding industry averages.
                </p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-800">Customer Acquisition</p>
                <p className="text-sm text-blue-600">
                  {businessMetrics.customers.new} new customers acquired this month.
                </p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <p className="text-sm font-medium text-purple-800">API Performance</p>
                <p className="text-sm text-purple-600">
                  API usage is growing rapidly at {businessMetrics.apiUsage.growth}% monthly growth.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common business tasks and management functions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <FileText className="h-6 w-6 text-blue-600 mb-2" />
              <h3 className="font-medium text-gray-900">Generate Report</h3>
              <p className="text-sm text-gray-600">Create custom business reports</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <Users className="h-6 w-6 text-green-600 mb-2" />
              <h3 className="font-medium text-gray-900">Manage Team</h3>
              <p className="text-sm text-gray-600">Add or manage team members</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <BarChart3 className="h-6 w-6 text-purple-600 mb-2" />
              <h3 className="font-medium text-gray-900">View Analytics</h3>
              <p className="text-sm text-gray-600">Detailed analytics and insights</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
