import { createServerClient } from '@/lib/supabase/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Calendar,
  Trash2,
  Archive,
  Shield,
  Clock,
  Database,
  Settings,
  AlertTriangle
} from 'lucide-react'

export default async function BusinessDataRetentionPage() {
  const supabase = await createServerClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return <div>Unauthorized</div>
  }

  // Mock retention policies
  const retentionPolicies = [
    {
      id: 1,
      name: 'User Activity Logs',
      dataType: 'Activity Logs',
      retention: '90 days',
      action: 'Delete',
      status: 'Active',
      lastRun: '2024-01-15',
      itemsProcessed: 1247
    },
    {
      id: 2,
      name: 'Search History',
      dataType: 'Search Data',
      retention: '2 years',
      action: 'Archive',
      status: 'Active',
      lastRun: '2024-01-14',
      itemsProcessed: 892
    },
    {
      id: 3,
      name: 'API Request Logs',
      dataType: 'API Logs',
      retention: '6 months',
      action: 'Delete',
      status: 'Active',
      lastRun: '2024-01-13',
      itemsProcessed: 5420
    }
  ]

  return (
    <div className="max-w-7xl mx-auto px-4">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Data Retention Policies</h1>
            <p className="text-gray-600 mt-2">
              Configure automated data retention, archival, and deletion policies.
            </p>
            <div className="mt-2 text-sm text-blue-600">
              Retention Policy Delete Archive Data Management
            </div>
          </div>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Settings className="h-4 w-4 mr-2" />
            Create Policy
          </Button>
        </div>
      </div>

      {/* Retention Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Policies</p>
                <p className="text-2xl font-bold text-gray-900">3</p>
              </div>
              <Shield className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Data Archived</p>
                <p className="text-2xl font-bold text-gray-900">2.3 TB</p>
              </div>
              <Archive className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Data Deleted</p>
                <p className="text-2xl font-bold text-gray-900">847 GB</p>
              </div>
              <Trash2 className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Storage Saved</p>
                <p className="text-2xl font-bold text-gray-900">3.1 TB</p>
              </div>
              <Database className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Retention Policies */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Retention Policies
          </CardTitle>
          <CardDescription>
            Automated data lifecycle management policies
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {retentionPolicies.map((policy) => (
              <div key={policy.id} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="font-medium text-gray-900">{policy.name}</h3>
                    <p className="text-sm text-gray-600">{policy.dataType}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      policy.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {policy.status}
                    </span>
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Retention Period:</span>
                    <p className="font-medium">{policy.retention}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Action:</span>
                    <div className="flex items-center space-x-1">
                      {policy.action === 'Delete' ? (
                        <Trash2 className="h-4 w-4 text-red-500" />
                      ) : (
                        <Archive className="h-4 w-4 text-blue-500" />
                      )}
                      <span className="font-medium">{policy.action}</span>
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-600">Last Run:</span>
                    <p className="font-medium">{policy.lastRun}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Items Processed:</span>
                    <p className="font-medium">{policy.itemsProcessed.toLocaleString()}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Policy Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Create New Policy
            </CardTitle>
            <CardDescription>
              Set up a new data retention policy
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Policy Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., User Session Logs"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Data Type
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="">Select data type</option>
                  <option value="activity_logs">Activity Logs</option>
                  <option value="search_data">Search Data</option>
                  <option value="api_logs">API Logs</option>
                  <option value="user_data">User Data</option>
                  <option value="system_logs">System Logs</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Retention Period
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="30"
                  />
                  <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="days">Days</option>
                    <option value="months">Months</option>
                    <option value="years">Years</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Action
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="radio" name="action" value="archive" className="border-gray-300" />
                    <Archive className="h-4 w-4 ml-2 mr-1 text-blue-500" />
                    <span className="text-sm text-gray-700">Archive (move to cold storage)</span>
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="action" value="delete" className="border-gray-300" />
                    <Trash2 className="h-4 w-4 ml-2 mr-1 text-red-500" />
                    <span className="text-sm text-gray-700">Delete permanently</span>
                  </label>
                </div>
              </div>

              <Button className="w-full">
                Create Policy
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Upcoming Actions
            </CardTitle>
            <CardDescription>
              Scheduled retention actions and warnings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800">Upcoming Deletion</h4>
                    <p className="text-sm text-yellow-600">
                      1,247 activity log entries will be deleted in 7 days
                    </p>
                    <p className="text-xs text-yellow-500 mt-1">Policy: User Activity Logs</p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start space-x-2">
                  <Archive className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-800">Upcoming Archive</h4>
                    <p className="text-sm text-blue-600">
                      892 search records will be archived in 14 days
                    </p>
                    <p className="text-xs text-blue-500 mt-1">Policy: Search History</p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-start space-x-2">
                  <Clock className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-green-800">Recent Completion</h4>
                    <p className="text-sm text-green-600">
                      API logs cleanup completed successfully
                    </p>
                    <p className="text-xs text-green-500 mt-1">5,420 items processed</p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3">Policy Schedule</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Daily cleanup</span>
                    <span className="font-medium">2:00 AM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Weekly archive</span>
                    <span className="font-medium">Sunday 3:00 AM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monthly review</span>
                    <span className="font-medium">1st of month</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
