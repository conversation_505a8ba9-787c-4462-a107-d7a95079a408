import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { BusinessNavigation } from '@/components/business/BusinessNavigation'


export default async function BusinessLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // SECURITY: All business routes now require proper authentication
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile to check subscription tier and role
  const { data: profile } = await supabase
    .from('profiles')
    .select('subscription_tier, role, full_name')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has business or enterprise access
  const hasBusinessAccess = profile.subscription_tier === 'business' ||
                           profile.subscription_tier === 'enterprise' ||
                           profile.role === 'business' ||
                           profile.role === 'enterprise' ||
                           profile.role === 'admin'

  if (!hasBusinessAccess) {
    redirect('/pricing?upgrade=business')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <BusinessNavigation profile={profile} />
      <main className="py-8">
        {children}
      </main>
    </div>
  )
}
