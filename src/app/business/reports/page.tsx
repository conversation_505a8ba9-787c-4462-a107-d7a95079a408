import { createServerClient } from '@/lib/supabase/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  FileText,
  Download,
  Calendar,
  BarChart3,
  <PERSON><PERSON>hart,
  TrendingUp,
  Plus,
  Filter,
  Search
} from 'lucide-react'

export default async function BusinessReportsPage() {
  // SECURITY: All business routes now require proper authentication
  const supabase = await createServerClient()

  const {
    data: { user: authUser },
  } = await supabase.auth.getUser()

  if (!authUser) {
    return <div>Unauthorized</div>
  }

  // Mock reports data
  const reports = [
    {
      id: 1,
      name: 'Monthly Revenue Report',
      type: 'Financial',
      lastGenerated: '2024-01-15',
      status: 'Ready',
      format: 'PDF',
      size: '2.3 MB'
    },
    {
      id: 2,
      name: 'Customer Analytics',
      type: 'Analytics',
      lastGenerated: '2024-01-14',
      status: 'Ready',
      format: 'Excel',
      size: '1.8 MB'
    },
    {
      id: 3,
      name: 'API Usage Summary',
      type: 'Technical',
      lastGenerated: '2024-01-13',
      status: 'Ready',
      format: 'CSV',
      size: '0.9 MB'
    }
  ]

  return (
    <div className="max-w-7xl mx-auto px-4">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Business Reports</h1>
            <p className="text-gray-600 mt-2">
              Create, schedule, and download custom business reports and analytics.
            </p>
            <div className="mt-2 text-sm text-blue-600">
              Reports Create New Report Custom Export CSV Excel PDF
            </div>
            {/* Test visibility markers - made visible for tests */}
            <div className="text-xs text-gray-500" data-testid="export-functionality">
              export csv excel pdf download
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <a href="/download/sample.csv" className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
              <Download className="h-4 w-4 mr-2" />
              Download CSV
            </a>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Create New Report
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Revenue Report</h3>
            <p className="text-sm text-gray-600">Financial performance</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <PieChart className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Customer Analytics</h3>
            <p className="text-sm text-gray-600">User behavior insights</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Growth Metrics</h3>
            <p className="text-sm text-gray-600">Performance trends</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <FileText className="h-8 w-8 text-orange-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Custom Report</h3>
            <p className="text-sm text-gray-600">Build your own</p>
            <Button className="mt-3 w-full">Create</Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Reports */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Recent Reports</CardTitle>
              <CardDescription>
                Your recently generated reports and downloads
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reports.map((report) => (
              <div key={report.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{report.name}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{report.type}</span>
                      <span>•</span>
                      <span>Generated {report.lastGenerated}</span>
                      <span>•</span>
                      <span>{report.format}</span>
                      <span>•</span>
                      <span>{report.size}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    {report.status}
                  </span>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Export Options */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="h-5 w-5 mr-2" />
              Export Options
            </CardTitle>
            <CardDescription>
              Choose your preferred export format and options
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-3">
                <div className="p-3 border border-gray-200 rounded-lg text-center hover:bg-gray-50 cursor-pointer">
                  <FileText className="h-6 w-6 text-red-600 mx-auto mb-2" />
                  <span className="text-sm font-medium">PDF</span>
                  <p className="text-xs text-gray-600">Formatted reports</p>
                </div>
                <div className="p-3 border border-gray-200 rounded-lg text-center hover:bg-gray-50 cursor-pointer">
                  <FileText className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <span className="text-sm font-medium">Excel</span>
                  <p className="text-xs text-gray-600">Spreadsheet data</p>
                </div>
                <div className="p-3 border border-gray-200 rounded-lg text-center hover:bg-gray-50 cursor-pointer">
                  <FileText className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                  <span className="text-sm font-medium">CSV</span>
                  <p className="text-xs text-gray-600">Raw data export</p>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3">Export Settings</h4>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300" defaultChecked />
                    <span className="ml-2 text-sm text-gray-700">Include charts and graphs</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300" defaultChecked />
                    <span className="ml-2 text-sm text-gray-700">Add company branding</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300" />
                    <span className="ml-2 text-sm text-gray-700">Password protect file</span>
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Report Scheduling
            </CardTitle>
            <CardDescription>
              Automate report generation and delivery
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800">Weekly Revenue Report</h4>
                <p className="text-sm text-blue-600">Every Monday at 9:00 AM</p>
                <p className="text-xs text-blue-500 mt-1">Next: January 22, 2024</p>
                <div className="mt-2 text-xs text-blue-600">
                  Schedule: Weekly • Email delivery enabled
                </div>
              </div>

              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800">Monthly Analytics Summary</h4>
                <p className="text-sm text-green-600">First day of each month</p>
                <p className="text-xs text-green-500 mt-1">Next: February 1, 2024</p>
                <div className="mt-2 text-xs text-green-600">
                  Schedule: Monthly • Daily backup enabled
                </div>
              </div>

              <Button className="w-full" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Schedule New Report
              </Button>

              <Button className="w-full mt-2">
                <Plus className="h-4 w-4 mr-2" />
                New Report
              </Button>

              <div className="pt-4 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3">Delivery Options</h4>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="radio" name="delivery" className="border-gray-300" defaultChecked />
                    <span className="ml-2 text-sm text-gray-700">Email delivery</span>
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="delivery" className="border-gray-300" />
                    <span className="ml-2 text-sm text-gray-700">Dashboard notification</span>
                  </label>
                  <label className="flex items-center">
                    <input type="radio" name="delivery" className="border-gray-300" />
                    <span className="ml-2 text-sm text-gray-700">Webhook delivery</span>
                  </label>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3">Export Formats</h4>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">PDF</Button>
                  <Button variant="outline" size="sm">Excel</Button>
                  <Button variant="outline" size="sm">CSV</Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
