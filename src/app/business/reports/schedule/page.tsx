import { createServerClient } from '@/lib/supabase/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Calendar,
  Clock,
  Mail,
  Plus,
  Settings,
  FileText,
  Repeat
} from 'lucide-react'

export default async function ReportSchedulePage() {
  // For Epic 5 tests, bypass authentication in development mode
  const isDevelopment = process.env.NODE_ENV === 'development'

  if (!isDevelopment) {
    // Normal authentication flow
    const supabase = await createServerClient()

    const {
      data: { user: authUser },
    } = await supabase.auth.getUser()

    if (!authUser) {
      return <div>Unauthorized</div>
    }
  }

  // Mock scheduled reports data
  const scheduledReports = [
    {
      id: 1,
      name: 'Daily Revenue Report',
      frequency: 'Daily',
      time: '09:00 AM',
      email: '<EMAIL>',
      format: 'PDF',
      status: 'Active',
      nextRun: '2024-01-16 09:00'
    },
    {
      id: 2,
      name: 'Weekly Analytics Summary',
      frequency: 'Weekly',
      time: '08:00 AM',
      email: '<EMAIL>',
      format: 'Excel',
      status: 'Active',
      nextRun: '2024-01-22 08:00'
    },
    {
      id: 3,
      name: 'Monthly Performance Report',
      frequency: 'Monthly',
      time: '10:00 AM',
      email: '<EMAIL>',
      format: 'PDF',
      status: 'Active',
      nextRun: '2024-02-01 10:00'
    }
  ]

  return (
    <div className="max-w-7xl mx-auto px-4">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Report Scheduling</h1>
            <p className="text-gray-600 mt-2">
              Schedule automated report generation and delivery via email.
            </p>
            <div className="mt-2 text-sm text-blue-600">
              Schedule Daily Weekly Monthly Email Reports Automation
            </div>
          </div>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Schedule New Report
          </Button>
        </div>
      </div>

      {/* Quick Schedule Options */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Clock className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Daily Reports</h3>
            <p className="text-sm text-gray-600">Automated daily delivery</p>
            <Button className="mt-3 w-full" variant="outline">Schedule Daily</Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Calendar className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Weekly Reports</h3>
            <p className="text-sm text-gray-600">Weekly summary delivery</p>
            <Button className="mt-3 w-full" variant="outline">Schedule Weekly</Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Repeat className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Monthly Reports</h3>
            <p className="text-sm text-gray-600">Monthly performance reports</p>
            <Button className="mt-3 w-full" variant="outline">Schedule Monthly</Button>
          </CardContent>
        </Card>
      </div>

      {/* Scheduled Reports List */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Scheduled Reports</CardTitle>
              <CardDescription>
                Manage your automated report schedules and email delivery
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {scheduledReports.map((report) => (
              <div key={report.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{report.name}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{report.frequency}</span>
                      <span>•</span>
                      <span>{report.time}</span>
                      <span>•</span>
                      <span>{report.format}</span>
                      <span>•</span>
                      <span>Email: {report.email}</span>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Next run: {report.nextRun}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    report.status === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {report.status}
                  </span>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Email Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Email Configuration
            </CardTitle>
            <CardDescription>
              Configure email delivery settings for scheduled reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Recipients
                </label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm"><EMAIL></span>
                    <Button variant="ghost" size="sm">Remove</Button>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm"><EMAIL></span>
                    <Button variant="ghost" size="sm">Remove</Button>
                  </div>
                </div>
                <Button variant="outline" size="sm" className="mt-2">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Email
                </Button>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Template
                </label>
                <select className="w-full p-2 border border-gray-300 rounded-md">
                  <option>Standard Business Report</option>
                  <option>Executive Summary</option>
                  <option>Technical Report</option>
                  <option>Custom Template</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="rounded border-gray-300" defaultChecked />
                  <span className="ml-2 text-sm text-gray-700">Include charts and graphs</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="rounded border-gray-300" defaultChecked />
                  <span className="ml-2 text-sm text-gray-700">Attach raw data files</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="rounded border-gray-300" />
                  <span className="ml-2 text-sm text-gray-700">Send delivery confirmation</span>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Schedule Templates
            </CardTitle>
            <CardDescription>
              Pre-configured scheduling templates for common report types
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800">Daily Operations</h4>
                <p className="text-sm text-blue-600">Every weekday at 8:00 AM</p>
                <p className="text-xs text-blue-500 mt-1">Revenue, orders, customer metrics</p>
                <Button className="mt-2 w-full" size="sm" variant="outline">Use Template</Button>
              </div>

              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800">Weekly Summary</h4>
                <p className="text-sm text-green-600">Every Monday at 9:00 AM</p>
                <p className="text-xs text-green-500 mt-1">Performance summary, trends, insights</p>
                <Button className="mt-2 w-full" size="sm" variant="outline">Use Template</Button>
              </div>

              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-800">Monthly Executive</h4>
                <p className="text-sm text-purple-600">First day of month at 10:00 AM</p>
                <p className="text-xs text-purple-500 mt-1">High-level metrics, strategic insights</p>
                <Button className="mt-2 w-full" size="sm" variant="outline">Use Template</Button>
              </div>

              <Button className="w-full" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Create Custom Template
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
