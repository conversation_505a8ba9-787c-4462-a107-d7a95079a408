'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import type { SubscriptionTier } from '@/lib/tier-config'
import { ChatPageLayout } from '@/components/chat/ChatPageLayout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageCircle, Crown, ArrowRight } from 'lucide-react'
import Link from 'next/link'


interface User {
  id: string
  email?: string
}

interface UserProfile {
  subscription_tier: SubscriptionTier
  subscription_status: string
}

export default function ChatPage() {
  console.log('🎯 ChatPage component rendering')

  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    checkUserAccess()

    // Add chat page body class to prevent scrolling
    document.body.classList.add('chat-page-body')

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('chat-page-body')
    }
  }, [])

  const checkUserAccess = async () => {
    try {
      const supabase = createClient()

      // Simple auth check - trust that middleware already handled auth
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()

      if (authError || !authUser) {
        // If no user, middleware should have redirected us, but just in case:
        router.push('/login?redirectTo=/chat')
        return
      }

      setUser(authUser)

      // Get user profile and subscription tier
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('subscription_tier, subscription_status')
        .eq('id', authUser.id)
        .single()

      if (profileError) {
        // If profile doesn't exist, create a default one
        if (profileError.code === 'PGRST116') {
          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert({
              id: authUser.id,
              email: authUser.email,
              subscription_tier: 'trial',
              subscription_status: 'active',
              trial_start_date: new Date().toISOString(),
              trial_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              pulls_this_month: 0,
              extra_credits: 0,
              first_time_user: true,
            })
            .select('subscription_tier, subscription_status')
            .single()

          if (createError) {
            setError('Failed to create user profile')
            setIsLoading(false)
            return
          }

          setUserProfile(newProfile)
        } else {
          setError('Failed to load user profile')
          setIsLoading(false)
          return
        }
      } else {
        setUserProfile(profile)
      }

      // Check if user has access to chat feature
      const currentProfile = profile || (userProfile || { subscription_tier: 'trial' })
      const userTier = currentProfile.subscription_tier || 'trial'
      const hasAccess = hasFeatureAccess(userTier, 'enableChat') && isFeatureEnabled('CHAT_ENABLED')

      if (!hasAccess) {
        setError('upgrade_required')
        setIsLoading(false)
        return
      }

      setIsLoading(false)
    } catch (err) {
      setError('Failed to verify access')
      setIsLoading(false)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-muted-foreground">Loading chat interface...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error && error !== 'upgrade_required') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-destructive">Chat Access Error</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">{error}</p>

            <div className="space-y-2">
              <Button
                onClick={() => window.location.reload()}
                className="w-full"
              >
                Refresh Page
              </Button>

              <Button
                onClick={() => router.push('/login')}
                variant="outline"
                className="w-full"
              >
                Login Again
              </Button>

              <Button
                onClick={() => router.push('/search')}
                variant="ghost"
                className="w-full"
              >
                Return to Search
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Upgrade required state
  if (error === 'upgrade_required') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-lg mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <Crown className="w-8 h-8 text-primary" />
            </div>
            <CardTitle className="text-2xl">Pro Feature Required</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-6">
            <div>
              <p className="text-muted-foreground mb-4">
                The AI Chat feature is available exclusively to Pro subscribers. 
                Upgrade now to unlock interactive compliance conversations.
              </p>
              
              <div className="bg-muted/50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-center mb-3">
                  <MessageCircle className="w-6 h-6 text-primary mr-2" />
                  <span className="font-semibold">Pro Chat Features</span>
                </div>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• Interactive compliance conversations</li>
                  <li>• Address-specific jurisdiction context</li>
                  <li>• Real-time source citations</li>
                  <li>• Unlimited chat sessions</li>
                  <li>• Conversation history</li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Link href="/checkout/pro" className="flex-1">
                <Button className="w-full">
                  <Crown className="w-4 h-4 mr-2" />
                  Upgrade to Pro
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
              <Button variant="outline" onClick={() => router.push('/search')}>
                Back to Search
              </Button>
            </div>

            <p className="text-xs text-muted-foreground">
              Already a Pro subscriber? Try refreshing the page or{' '}
              <Link href="/account" className="text-primary hover:underline">
                check your account status
              </Link>
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Main chat interface for Pro users
  return (
    <ChatPageLayout />
  )
}
