import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { CreditsCheckoutPage } from '@/components/checkout/CreditsCheckoutPage'

export default async function CreditsCheckoutPageRoute() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login?redirect=/checkout/credits')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Only allow trial tier users to purchase credits
  if (profile.subscription_tier !== 'trial') {
    redirect('/account?no_credits_needed=true')
  }

  const creditPackages = [
    {
      id: 'pack_5',
      name: '5 Additional Searches',
      credits: 5,
      price: '$5.00',
      priceInCents: 500,
      description: 'Perfect for occasional use',
      popular: false
    },
    {
      id: 'pack_15',
      name: '15 Additional Searches',
      credits: 15,
      price: '$12.00',
      priceInCents: 1200,
      description: 'Great value for multiple projects',
      popular: true
    },
    {
      id: 'pack_30',
      name: '30 Additional Searches',
      credits: 30,
      price: '$20.00',
      priceInCents: 2000,
      description: 'Best value for heavy users',
      popular: false
    }
  ]

  // Ensure user email is defined for the component
  const userWithEmail = {
    ...user,
    email: user.email || '', // Provide fallback for undefined email
  }

  return (
    <CreditsCheckoutPage
      packages={creditPackages}
      user={userWithEmail}
      profile={profile}
    />
  )
}
