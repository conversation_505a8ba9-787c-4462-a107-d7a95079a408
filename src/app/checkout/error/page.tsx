import { Suspense } from 'react'
import { XCircle, ArrowLeft, RefreshCw, MessageCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

async function ErrorContent({ searchParams }: { searchParams: Promise<{ [key: string]: string | string[] | undefined }> }) {
  const params = await searchParams
  const errorCode = params.error as string
  const errorMessage = params.message as string

  const getErrorDetails = (code: string) => {
    switch (code) {
      case 'payment_failed':
        return {
          title: 'Payment Failed',
          description: 'Your payment could not be processed. Please check your payment method and try again.',
          suggestion: 'Verify your card details and ensure you have sufficient funds.'
        }
      case 'card_declined':
        return {
          title: 'Card Declined',
          description: 'Your card was declined by your bank. Please try a different payment method.',
          suggestion: 'Contact your bank or try a different card.'
        }
      case 'session_expired':
        return {
          title: 'Session Expired',
          description: 'Your checkout session has expired. Please start the checkout process again.',
          suggestion: 'Return to the pricing page and select your plan again.'
        }
      case 'canceled':
        return {
          title: 'Payment Canceled',
          description: 'You canceled the payment process. No charges were made to your account.',
          suggestion: 'You can try again whenever you\'re ready.'
        }
      case 'subscription_failed':
        return {
          title: 'Subscription Setup Failed',
          description: 'Payment succeeded but subscription setup failed. Please contact support.',
          suggestion: 'Your payment was processed successfully, but we encountered an issue setting up your subscription. Our support team will help resolve this quickly.'
        }
      default:
        return {
          title: 'Payment Error',
          description: errorMessage || 'An unexpected error occurred during payment processing.',
          suggestion: 'Please try again or contact support if the problem persists.'
        }
    }
  }

  const errorDetails = getErrorDetails(errorCode)

  return (
    <div className="max-w-2xl mx-auto text-center">
      <div className="mb-8">
        <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <XCircle className="w-8 h-8 text-red-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2" data-testid="error-title">
          {errorDetails.title}
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300" data-testid="error-message">
          {errorDetails.description}
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>What can you do?</CardTitle>
          <CardDescription>
            {errorDetails.suggestion}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link href="/pricing">
              <Button className="w-full bg-[#1DA1F2] hover:bg-[#1a91da] text-white" size="lg">
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" className="w-full border-[#1DA1F2] text-[#1DA1F2] hover:bg-[#1DA1F2] hover:text-white" size="lg">
                <MessageCircle className="w-4 h-4 mr-2" />
                Contact Support
              </Button>
            </Link>
          </div>

          <div className="pt-4 border-t">
            <Link href="/">
              <Button variant="ghost" className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Return to Home
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <p className="text-sm text-gray-500 mb-4">
          Need help? Our support team is here to assist you.
        </p>
        <div className="space-y-2">
          <p className="text-xs text-gray-400">
            Common solutions:
          </p>
          <ul className="text-xs text-gray-400 space-y-1">
            <li>• Check that your card has sufficient funds</li>
            <li>• Verify your billing address matches your card</li>
            <li>• Try a different browser or device</li>
            <li>• Contact your bank if your card keeps getting declined</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutErrorPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ErrorContent searchParams={searchParams} />
    </Suspense>
  )
}
