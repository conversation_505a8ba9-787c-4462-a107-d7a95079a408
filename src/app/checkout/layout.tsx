import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { CheckoutHeader } from '@/components/checkout/CheckoutHeader'
import { CheckoutFooter } from '@/components/checkout/CheckoutFooter'

export default async function CheckoutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login?redirect=/checkout')
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <CheckoutHeader />
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
      <CheckoutFooter />
    </div>
  )
}
