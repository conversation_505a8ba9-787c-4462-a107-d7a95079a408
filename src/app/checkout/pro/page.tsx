import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { CheckoutPage } from '@/components/checkout/CheckoutPage'

export default async function ProCheckoutPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login?redirect=/checkout/pro')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user is already on Pro or higher
  if (profile.subscription_tier === 'pro' || profile.subscription_tier === 'appraiser') {
    redirect('/account?already_subscribed=true')
  }

  const planDetails = {
    name: 'Pro',
    price: '$99',
    interval: 'month',
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO!,
    features: [
      'Unlimited property searches',
      'Advanced compliance analysis',
      'Priority support',
      'Export reports to PDF',
      'Email notifications',
      'Search history & favorites'
    ],
    description: 'Perfect for homeowners and small contractors who need regular access to compliance information.'
  }

  return (
    <CheckoutPage 
      plan={planDetails}
      user={user}
      profile={profile}
    />
  )
}
