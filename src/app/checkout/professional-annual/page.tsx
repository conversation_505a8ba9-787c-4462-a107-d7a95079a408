import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { CheckoutPage } from '@/components/checkout/CheckoutPage'

export default async function ProfessionalAnnualCheckoutPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login?redirect=/checkout/professional-annual')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user is already on Professional or higher
  if (profile.subscription_tier === 'professional' || profile.subscription_tier === 'appraiser') {
    redirect('/account?already_subscribed=true')
  }

  const planDetails = {
    name: 'Professional',
    price: '$990',
    interval: 'year',
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_ANNUAL!,
    features: [
      'Unlimited searches',
      'AI chat assistance',
      'Priority support',
      'Faster response times',
      'Enhanced source analysis',
      '7-day free trial included',
      'Save 17% with annual billing'
    ],
    description: 'Ideal for contractors, developers, and professionals who need unlimited access to municipal compliance data. Save 17% with annual billing.'
  }

  return (
    <CheckoutPage 
      plan={planDetails}
      user={user}
      profile={profile}
    />
  )
}
