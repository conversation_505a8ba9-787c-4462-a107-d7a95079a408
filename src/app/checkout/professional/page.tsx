import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { CheckoutPage } from '@/components/checkout/CheckoutPage'

export default async function ProfessionalCheckoutPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login?redirect=/checkout/professional')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user is already on Professional or higher
  if (profile.subscription_tier === 'professional' || profile.subscription_tier === 'appraiser') {
    redirect('/account?already_subscribed=true')
  }

  const planDetails = {
    name: 'Professional',
    price: '$99',
    interval: 'month',
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_PROFESSIONAL_MONTHLY!,
    features: [
      'Unlimited searches',
      'AI chat assistance',
      'Priority support',
      'Faster response times',
      'Enhanced source analysis',
      '7-day free trial included'
    ],
    description: 'Ideal for contractors, developers, and professionals who need unlimited access to municipal compliance data.'
  }

  return (
    <CheckoutPage 
      plan={planDetails}
      user={user}
      profile={profile}
    />
  )
}
