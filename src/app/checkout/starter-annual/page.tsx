import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { CheckoutPage } from '@/components/checkout/CheckoutPage'

export default async function StarterAnnualCheckoutPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login?redirect=/checkout/starter-annual')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user is already on Starter or higher
  if (profile.subscription_tier === 'starter' || profile.subscription_tier === 'professional' || profile.subscription_tier === 'pro' || profile.subscription_tier === 'appraiser') {
    redirect('/account?already_subscribed=true')
  }

  const planDetails = {
    name: 'Starter',
    price: '$490',
    interval: 'year',
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_STARTER_ANNUAL!,
    features: [
      '1,000 searches per month',
      'AI chat assistance',
      'Municipal compliance research',
      'Save & organize searches',
      'Standard email support',
      '7-day free trial included',
      'Save 17% with annual billing'
    ],
    description: 'Perfect for homeowners and small contractors who need regular access to compliance information. Save 17% with annual billing.'
  }

  return (
    <CheckoutPage 
      plan={planDetails}
      user={user}
      profile={profile}
    />
  )
}
