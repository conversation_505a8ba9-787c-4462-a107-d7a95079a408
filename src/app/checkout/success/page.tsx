import { Suspense } from 'react'
import { CheckCircle, Home, CreditCard } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'
import { createServerClient } from '@/lib/supabase/server'

async function SuccessContent({ searchParams }: { searchParams: Promise<{ [key: string]: string | string[] | undefined }> }) {
  const params = await searchParams
  const sessionId = params.session_id as string
  const planType = params.plan_type as string
  const creditsAdded = params.credits as string

  // Development fallback: Ensure subscription is updated if webhook didn't fire
  if (planType && !creditsAdded && process.env.NODE_ENV === 'development') {
    try {
      const supabase = await createServerClient()
      const { data: { user } } = await supabase.auth.getUser()

      if (user) {
        // Check if user is still on trial (webhook didn't fire)
        const { data: profile } = await supabase
          .from('profiles')
          .select('subscription_tier')
          .eq('id', user.id)
          .single()

        if (profile?.subscription_tier === 'trial') {
          console.log('🔧 Development fallback: Updating subscription tier to', planType)
          await supabase
            .from('profiles')
            .update({
              subscription_tier: planType,
              subscription_status: 'active',
              is_subscribed: true,
              first_time_user: false,
              updated_at: new Date().toISOString(),
            })
            .eq('id', user.id)
        }
      }
    } catch (error) {
      console.error('Development fallback error:', error)
    }
  }

  return (
    <div className="max-w-2xl mx-auto text-center">
      <div className="mb-8">
        <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
          <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2" data-testid="success-title">
          Payment Successful!
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300" data-testid="success-message">
          {creditsAdded
            ? `${creditsAdded} search credits have been added to your account`
            : (
              <>
                Welcome to <span className="text-[#1DA1F2] font-semibold">Ordrly</span> {planType || 'Pro'}! Your subscription is now active.
              </>
            )
          }
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>What&apos;s Next?</CardTitle>
          <CardDescription>
            Here are some things you can do now
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {creditsAdded ? (
            <>
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div className="text-left">
                  <p className="font-medium text-green-900">Credits Added</p>
                  <p className="text-sm text-green-700">{creditsAdded} additional searches</p>
                </div>
                <div className="text-2xl font-bold text-green-600">+{creditsAdded}</div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link href="/chat">
                  <Button className="w-full bg-[#1DA1F2] hover:bg-[#1a91da] text-white" size="lg">
                    <Home className="w-4 h-4 mr-2" />
                    Start Searching
                  </Button>
                </Link>
                <Link href="/account">
                  <Button variant="outline" className="w-full border-[#1DA1F2] text-[#1DA1F2] hover:bg-[#1DA1F2] hover:text-white" size="lg">
                    <CreditCard className="w-4 h-4 mr-2" />
                    View Account
                  </Button>
                </Link>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="text-left">
                  <p className="font-medium text-blue-900">Subscription Active</p>
                  <p className="text-sm text-blue-700">Ordrly {planType || 'Pro'} Plan</p>
                </div>
                <div className="text-sm font-medium text-blue-600">Active</div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link href="/chat">
                  <Button className="w-full bg-[#1DA1F2] hover:bg-[#1a91da] text-white" size="lg">
                    <Home className="w-4 h-4 mr-2" />
                    Start Searching
                  </Button>
                </Link>
                <Link href="/account">
                  <Button variant="outline" className="w-full border-[#1DA1F2] text-[#1DA1F2] hover:bg-[#1DA1F2] hover:text-white" size="lg">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Manage Subscription
                  </Button>
                </Link>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div className="text-center">
        <p className="text-sm text-gray-500 mb-4">
          Questions? Check out our <Link href="/faq" className="text-blue-600 hover:underline">FAQ</Link> or <Link href="/contact" className="text-blue-600 hover:underline">contact support</Link>.
        </p>
        {sessionId && (
          <p className="text-xs text-gray-400">
            Transaction ID: {sessionId}
          </p>
        )}
      </div>
    </div>
  )
}

export default function CheckoutSuccessPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SuccessContent searchParams={searchParams} />
    </Suspense>
  )
}
