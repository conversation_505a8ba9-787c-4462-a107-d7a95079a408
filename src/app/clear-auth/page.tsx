'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'

export default function ClearAuthPage() {
  const [status, setStatus] = useState<string>('Ready')
  const supabase = createClient()

  const clearEverything = async () => {
    setStatus('Clearing all auth data...')
    
    try {
      // 1. Sign out from Supabase
      setStatus('Signing out from Supabase...')
      await supabase.auth.signOut()
      
      // 2. Clear all localStorage
      setStatus('Clearing localStorage...')
      localStorage.clear()
      
      // 3. Clear all sessionStorage
      setStatus('Clearing sessionStorage...')
      sessionStorage.clear()
      
      // 4. Clear all cookies (best effort)
      setStatus('Clearing cookies...')
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
      });
      
      setStatus('✅ All auth data cleared! Redirecting to home...')
      
      // 5. Redirect to home page
      setTimeout(() => {
        window.location.href = '/'
      }, 2000)
      
    } catch (error) {
      setStatus(`❌ Error: ${error}`)
    }
  }

  const checkAuthState = async () => {
    setStatus('Checking current auth state...')
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const { data: { user } } = await supabase.auth.getUser()
      
      setStatus(`Session: ${session ? 'Found' : 'None'} | User: ${user ? user.email : 'None'}`)
    } catch (error) {
      setStatus(`Error checking auth: ${error}`)
    }
  }

  useEffect(() => {
    checkAuthState()
  }, [])

  return (
    <div className="container mx-auto p-8 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">Clear Authentication Data</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg mb-6">
        <p className="text-yellow-800">
          <strong>Warning:</strong> This will completely clear all authentication data and sign you out.
          Use this if you're experiencing persistent auth issues.
        </p>
      </div>
      
      <div className="space-y-4">
        <div className="bg-white border p-4 rounded-lg">
          <h2 className="font-semibold mb-2">Current Status:</h2>
          <p className="text-sm font-mono bg-gray-100 p-2 rounded">{status}</p>
        </div>
        
        <div className="space-x-4">
          <Button onClick={checkAuthState} variant="outline">
            Check Auth State
          </Button>
          
          <Button onClick={clearEverything} variant="destructive">
            Clear All Auth Data
          </Button>
        </div>
        
        <div className="text-sm text-gray-600">
          <p>This will:</p>
          <ul className="list-disc list-inside ml-4">
            <li>Sign out from Supabase</li>
            <li>Clear localStorage</li>
            <li>Clear sessionStorage</li>
            <li>Clear cookies</li>
            <li>Redirect to home page</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
