import { Mail, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Contact Us - Get Help with Property Compliance | Ordrly',
  description: 'Need help with property compliance questions? Contact our expert team for personalized assistance with building regulations, permits, and zoning requirements.',
  keywords: ['contact support', 'property compliance help', 'building permit assistance', 'zoning questions', 'ordinance help'],
  openGraph: {
    title: 'Contact Us - Get Help with Property Compliance | Ordrly',
    description: 'Need help with property compliance questions? Contact our expert team for personalized assistance with building regulations, permits, and zoning requirements.',
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: 'Contact Us - Get Help with Property Compliance | Ordrly',
    description: 'Need help with property compliance questions? Contact our expert team for personalized assistance.',
  },
}

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-background py-16 px-4">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
            <Mail className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-4">Contact Us</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Have questions about property compliance? We&apos;re here to help you navigate
            building regulations and get the answers you need.
          </p>
        </div>

        {/* Contact Card */}
        <div className="bg-card border border-border rounded-2xl p-8 text-center shadow-lg">
          <div className="bg-primary/10 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
            <Mail className="h-10 w-10 text-primary" />
          </div>
          <h2 className="text-2xl font-semibold text-card-foreground mb-4">Get in Touch</h2>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Get personalized help with your compliance questions, property research,
            and platform support from our team.
          </p>

          <div className="space-y-4">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center text-primary hover:text-primary/80 font-semibold text-lg transition-colors"
            >
              <EMAIL>
              <ArrowRight className="h-5 w-5 ml-2" />
            </a>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-lg font-semibold transition-all duration-200"
              >
                <Mail className="h-5 w-5 mr-2" />
                Send us an email
              </a>
              <Link href="/chat">
                <Button variant="outline" size="lg" className="px-6">
                  Try Ordrly Chat
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
              </Link>
            </div>

            <p className="text-sm text-muted-foreground mt-4">
              We typically respond within 24 hours
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
