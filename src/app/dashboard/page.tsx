'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  TrendingUp,
  MapPin,
  Clock,
  Star,
  Search,
  BarChart3,
  Target,
  Zap,
  Heart,
  Plus,
  ArrowRight
} from 'lucide-react'

interface RecentSearch {
  address: string
  created_at: string
  project_type?: string
}

interface SavedSearch {
  name: string
  address: string
  created_at: string
}

interface FrequentArea {
  name: string
  count: number
}

interface Recommendation {
  title: string
  description: string
  type: string
}

interface Shortcut {
  name: string
  url: string
  icon: string
}

interface DashboardData {
  recentSearches: RecentSearch[]
  savedSearches: SavedSearch[]
  frequentAreas: FrequentArea[]
  recommendations: Recommendation[]
  shortcuts: Shortcut[]
  searchStats: {
    total_searches: number
    this_week: number
    this_month: number
    most_searched_type: string
  }
}

export default function DashboardPage() {
  const [loading, setLoading] = useState(true)
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    recentSearches: [],
    savedSearches: [],
    frequentAreas: [],
    recommendations: [],
    shortcuts: [],
    searchStats: {
      total_searches: 0,
      this_week: 0,
      this_month: 0,
      most_searched_type: 'residential'
    }
  })
  const router = useRouter()
  const supabase = createClient()

  const loadDashboardData = useCallback(async () => {
    try {
      const [
        recentResponse,
        savedResponse,
        frequentResponse,
        recommendationsResponse,
        shortcutsResponse,
        statsResponse
      ] = await Promise.all([
        fetch('/api/history/searches?limit=5'),
        fetch('/api/saved-searches?limit=5'),
        fetch('/api/personalization/frequent-areas'),
        fetch('/api/personalization/recommendations'),
        fetch('/api/personalization/shortcuts'),
        fetch('/api/personalization/stats')
      ])

      const data = {
        recentSearches: recentResponse.ok ? (await recentResponse.json()).searches || [] : [],
        savedSearches: savedResponse.ok ? (await savedResponse.json()).searches || [] : [],
        frequentAreas: frequentResponse.ok ? (await frequentResponse.json()).areas || [] : [],
        recommendations: recommendationsResponse.ok ? (await recommendationsResponse.json()).recommendations || [] : [],
        shortcuts: shortcutsResponse.ok ? (await shortcutsResponse.json()).shortcuts || [] : [],
        searchStats: statsResponse.ok ? (await statsResponse.json()).stats || dashboardData.searchStats : dashboardData.searchStats
      }

      setDashboardData(data)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }, [dashboardData.searchStats])

  const checkAuthAndLoadDashboard = useCallback(async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        router.push('/login')
        return
      }

      await loadDashboardData()
    } catch (err) {
      console.error('Auth check error:', err)
      router.push('/login')
    }
  }, [loadDashboardData]) // Remove router and supabase.auth dependencies to prevent infinite loops

  useEffect(() => {
    checkAuthAndLoadDashboard()
  }, [checkAuthAndLoadDashboard])

  const addShortcut = async (name: string, url: string, icon: string) => {
    try {
      const response = await fetch('/api/personalization/shortcuts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, url, icon })
      })

      if (response.ok) {
        await loadDashboardData()
      }
    } catch (error) {
      console.error('Failed to add shortcut:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
          <p className="text-gray-600">Your personalized property research overview</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Search Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Search Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {dashboardData.searchStats.total_searches}
                    </div>
                    <div className="text-sm text-gray-500">Total Searches</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {dashboardData.searchStats.this_week}
                    </div>
                    <div className="text-sm text-gray-500">This Week</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {dashboardData.searchStats.this_month}
                    </div>
                    <div className="text-sm text-gray-500">This Month</div>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-blue-700">
                      Most searched: {dashboardData.searchStats.most_searched_type}
                    </span>
                    <Badge variant="secondary">{dashboardData.searchStats.most_searched_type}</Badge>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700">
                      <strong>Personalized recommendations</strong> are available based on your search patterns
                    </span>
                    <Button variant="outline" size="sm" onClick={() => {
                      const recommendationsTab = document.querySelector('[value="recommendations"]') as HTMLElement
                      if (recommendationsTab) {
                        recommendationsTab.click()
                      }
                    }}>
                      View Recommendations
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Tabs defaultValue="recent" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="recent">Recent Searches</TabsTrigger>
                <TabsTrigger value="saved">Saved Searches</TabsTrigger>
                <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
              </TabsList>

              <TabsContent value="recent" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 mr-2" />
                        Recent Searches
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push('/history')}
                      >
                        View All
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {dashboardData.recentSearches.length === 0 ? (
                      <div className="text-center py-8">
                        <Search className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500">No recent searches</p>
                        <Button
                          className="mt-2"
                          onClick={() => router.push('/search')}
                        >
                          Start Searching
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {dashboardData.recentSearches.slice(0, 5).map((search, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <MapPin className="h-4 w-4 text-blue-600" />
                              <div>
                                <div className="font-medium">{search.address}</div>
                                <div className="text-sm text-gray-500">
                                  {new Date(search.created_at).toLocaleDateString()}
                                </div>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/search?address=${encodeURIComponent(search.address)}`)}
                            >
                              <ArrowRight className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="saved" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Star className="h-5 w-5 mr-2" />
                        Saved Searches
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push('/saved-searches')}
                      >
                        View All
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {dashboardData.savedSearches.length === 0 ? (
                      <div className="text-center py-8">
                        <Heart className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500">No saved searches</p>
                        <Button
                          className="mt-2"
                          onClick={() => router.push('/search')}
                        >
                          Save Your First Search
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {dashboardData.savedSearches.slice(0, 5).map((search, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <div>
                                <div className="font-medium">{search.name}</div>
                                <div className="text-sm text-gray-500">{search.address}</div>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/search?address=${encodeURIComponent(search.address)}`)}
                            >
                              <ArrowRight className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="recommendations" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Target className="h-5 w-5 mr-2" />
                      Recommended for You
                    </CardTitle>
                    <CardDescription>
                      Personalized recommendations based on your search history and preferences. These suggestions are recommended for you.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {dashboardData.recommendations.length === 0 ? (
                      <div className="text-center py-8">
                        <TrendingUp className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500">No recommendations yet</p>
                        <p className="text-sm text-gray-400 mt-1">
                          Search more properties to get personalized recommendations based on your activity
                        </p>
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm text-blue-700">
                            <strong>Personalized recommendations:</strong> Try searching for properties in your area to get recommendations based on your search patterns and preferences.
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {dashboardData.recommendations.map((rec, index) => (
                          <div key={index} className="p-3 bg-blue-50 rounded-lg">
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium text-blue-900">Recommended: {rec.title}</div>
                                <div className="text-sm text-blue-700">Based on your activity: {rec.description}</div>
                              </div>
                              <Button variant="outline" size="sm">
                                Explore
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="h-5 w-5 mr-2" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  className="w-full justify-start"
                  onClick={() => router.push('/search')}
                >
                  <Search className="h-4 w-4 mr-2" />
                  New Search
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push('/saved-searches')}
                >
                  <Star className="h-4 w-4 mr-2" />
                  Saved Searches
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push('/history')}
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Search History
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push('/preferences')}
                >
                  <Target className="h-4 w-4 mr-2" />
                  Preferences
                </Button>
              </CardContent>
            </Card>

            {/* Custom Shortcuts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Zap className="h-5 w-5 mr-2" />
                    Shortcuts
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => addShortcut('Favorite Area', '/search?area=downtown', 'map-pin')}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {dashboardData.shortcuts.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">No shortcuts yet</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => addShortcut('Quick Search', '/search', 'search')}
                    >
                      Add Shortcut
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {dashboardData.shortcuts.map((shortcut, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => router.push(shortcut.url)}
                      >
                        <MapPin className="h-4 w-4 mr-2" />
                        {shortcut.name}
                      </Button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Frequent Areas */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Frequent Areas
                </CardTitle>
              </CardHeader>
              <CardContent>
                {dashboardData.frequentAreas.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">No frequent areas yet</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {dashboardData.frequentAreas.map((area, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{area.name}</div>
                          <div className="text-sm text-gray-500">{area.count} searches</div>
                        </div>
                        <Progress value={(area.count / 10) * 100} className="w-16" />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
