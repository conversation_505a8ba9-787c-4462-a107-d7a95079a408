'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { User } from '@supabase/supabase-js'

export default function DebugAuthPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [sessionInfo, setSessionInfo] = useState<any>(null)
  const [authLogs, setAuthLogs] = useState<string[]>([])
  const supabase = createClient()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setAuthLogs(prev => [...prev, `${timestamp}: ${message}`])
    console.log(`DEBUG AUTH: ${message}`)
  }

  useEffect(() => {
    let mounted = true

    const checkAuth = async () => {
      addLog('Starting auth check...')
      
      try {
        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        if (sessionError) {
          addLog(`Session error: ${sessionError.message}`)
        } else {
          addLog(`Session found: ${session ? 'YES' : 'NO'}`)
          if (session) {
            addLog(`Session user: ${session.user?.email}`)
            addLog(`Session expires: ${new Date(session.expires_at! * 1000).toLocaleString()}`)
          }
          setSessionInfo(session)
        }

        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        if (userError) {
          addLog(`User error: ${userError.message}`)
        } else {
          addLog(`User found: ${user ? 'YES' : 'NO'}`)
          if (user) {
            addLog(`User email: ${user.email}`)
            addLog(`User ID: ${user.id}`)
            addLog(`User created: ${user.created_at}`)
          }
        }

        if (mounted) {
          setUser(user)
          setLoading(false)
        }
      } catch (error) {
        addLog(`Unexpected error: ${error}`)
        if (mounted) {
          setLoading(false)
        }
      }
    }

    checkAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        addLog(`Auth state change: ${event}`)
        if (session?.user) {
          addLog(`New session user: ${session.user.email}`)
        } else {
          addLog('No user in new session')
        }
        
        if (mounted) {
          setUser(session?.user ?? null)
          setSessionInfo(session)
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  const handleRefresh = () => {
    window.location.reload()
  }

  const handleClearStorage = () => {
    localStorage.clear()
    sessionStorage.clear()
    addLog('Cleared local and session storage')
  }

  const handleSignOut = async () => {
    addLog('Starting sign out...')
    await supabase.auth.signOut()
    addLog('Sign out completed')
  }

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current State */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
          <h2 className="text-lg font-semibold mb-4">Current Auth State</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {user ? 'Authenticated' : 'Not authenticated'}</p>
            {user && (
              <>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Created:</strong> {user.created_at}</p>
              </>
            )}
          </div>
        </div>

        {/* Session Info */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
          <h2 className="text-lg font-semibold mb-4">Session Info</h2>
          {sessionInfo ? (
            <div className="space-y-2">
              <p><strong>Access Token:</strong> {sessionInfo.access_token ? 'Present' : 'Missing'}</p>
              <p><strong>Refresh Token:</strong> {sessionInfo.refresh_token ? 'Present' : 'Missing'}</p>
              <p><strong>Expires:</strong> {sessionInfo.expires_at ? new Date(sessionInfo.expires_at * 1000).toLocaleString() : 'N/A'}</p>
            </div>
          ) : (
            <p>No session found</p>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="mt-6 space-x-4">
        <button 
          onClick={handleRefresh}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Refresh Page
        </button>
        <button 
          onClick={handleClearStorage}
          className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
        >
          Clear Storage
        </button>
        {user && (
          <button 
            onClick={handleSignOut}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            Sign Out
          </button>
        )}
      </div>

      {/* Logs */}
      <div className="mt-6">
        <h2 className="text-lg font-semibold mb-4">Auth Logs</h2>
        <div className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg max-h-96 overflow-y-auto">
          {authLogs.map((log, index) => (
            <div key={index} className="text-sm font-mono mb-1">
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
