import { Metadata } from 'next'

export const metadata: Metadata = {
  title: "Municipal Research API for Developers | Ordrly",
  description: "Transform municipal research with our cutting-edge API. Get instant access to zoning laws, building codes, and compliance data across thousands of jurisdictions. Built for developers, trusted by professionals.",
  keywords: [
    "municipal research API", 
    "building codes API", 
    "zoning API", 
    "compliance API", 
    "developer tools", 
    "municipal data",
    "government API",
    "real estate API",
    "property compliance API"
  ],
  openGraph: {
    title: "Municipal Research API for Developers",
    description: "Transform municipal research with our cutting-edge API. Get instant access to zoning laws, building codes, and compliance data across thousands of jurisdictions.",
    type: "website",
    url: "/developers",
  },
  twitter: {
    card: "summary_large_image",
    title: "Municipal Research API for Developers",
    description: "Transform municipal research with our cutting-edge API. Get instant access to zoning laws, building codes, and compliance data across thousands of jurisdictions.",
  },
  alternates: {
    canonical: '/developers',
  },
}

export default function DevelopersLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
