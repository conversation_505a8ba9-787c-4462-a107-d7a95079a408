"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  ArrowRight, Zap, Shield, Clock, Database, Globe, Terminal,
  Code, Layers, Search, Cpu, Eye, BarChart3, CheckCircle,
  Star, Users, TrendingUp, <PERSON>, <PERSON>rk<PERSON>, Rocket
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { OrdrlyLogo } from '@/components/ui/ordrly-logo'

// Theme system that works with light/dark mode
const theme = {
  container: "relative z-10 container mx-auto px-6",
  card: "bg-card/50 backdrop-blur-sm border border-border hover-lift",
  text: {
    primary: "text-foreground",
    secondary: "text-muted-foreground",
    accent: "text-primary",
    success: "text-green-500 dark:text-green-400"
  },
  button: {
    primary: "bg-primary hover:bg-primary/90 text-primary-foreground border border-primary/20",
    secondary: "border-border text-muted-foreground hover:bg-muted/50",
    success: "bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white"
  },
  border: {
    blue: "border-primary/30",
    green: "border-green-500/30",
    purple: "border-purple-500/30"
  },
  background: {
    page: "min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 relative overflow-hidden",
    orb: "dark:bg-primary/10 light:bg-primary/5"
  }
}

// Animated counter component
const AnimatedCounter = ({ end, duration = 2000 }: { end: number, duration?: number }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      setCount(Math.floor(progress * end))
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    requestAnimationFrame(animate)
  }, [end, duration])

  return <span>{count.toLocaleString()}</span>
}

export default function DevelopersPage() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <div className={theme.background.page}>
      {/* Background Grid */}
      <div className="absolute inset-0 grid-pattern opacity-10 dark:opacity-20" />

      {/* Floating Orbs - Responsive to theme */}
      <div className="absolute w-32 h-32 bg-primary/5 dark:bg-primary/10 rounded-full blur-xl neural-pulse top-20 left-20" />
      <div className="absolute w-24 h-24 bg-green-500/10 dark:bg-green-500/15 rounded-full blur-xl neural-pulse top-40 right-32" style={{ animationDelay: '1s' }} />
      <div className="absolute w-40 h-40 bg-muted/20 dark:bg-slate-500/10 rounded-full blur-xl neural-pulse bottom-32 left-1/3" style={{ animationDelay: '2s' }} />
      <div className="absolute w-28 h-28 bg-purple-500/5 dark:bg-purple-500/10 rounded-full blur-xl neural-pulse bottom-20 right-20" style={{ animationDelay: '3s' }} />

      {/* Hero Section */}
        <section className={`${theme.container} py-24 md:py-32`}>
          <div className={`mx-auto max-w-6xl text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="inline-flex items-center gap-6 mb-8">
              <OrdrlyLogo size="xl" className="w-32 h-32 md:w-40 md:h-40" />
              <div className="text-left">
                <h1 className="text-5xl md:text-7xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-primary/80 bg-clip-text text-transparent">
                  Municipal Research
                </h1>
                <div className={`text-2xl md:text-3xl font-exo font-light ${theme.text.success} mt-2`}>
                  Powered by Ordrly
                </div>
              </div>
            </div>

            <p className={`text-xl md:text-2xl ${theme.text.secondary} font-space max-w-4xl mx-auto leading-relaxed mb-12`}>
              Transform municipal research with our cutting-edge API. Get instant access to zoning laws,
              building codes, and compliance data across thousands of jurisdictions.
              <span className={`${theme.text.accent}`}> Built for developers, trusted by professionals.</span>
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
              <Link href="/search">
                <Button size="lg" className={`${theme.button.primary} px-8 py-4 text-lg font-exo`}>
                  <Rocket className="w-5 h-5 mr-3" />
                  Start Free Trial
                  <ArrowRight className="w-5 h-5 ml-3" />
                </Button>
              </Link>
              <Link href="/docs">
                <Button variant="outline" size="lg" className={`${theme.button.secondary} px-8 py-4 text-lg font-exo`}>
                  <Code className="w-5 h-5 mr-3" />
                  View API Docs
                </Button>
              </Link>
            </div>

            {/* Live Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className={`text-3xl font-orbitron font-bold ${theme.text.accent} mb-2`}>
                  <AnimatedCounter end={50000} />+
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>API Calls Processed</div>
              </div>
              <div className="text-center">
                <div className={`text-3xl font-orbitron font-bold ${theme.text.success} mb-2`}>
                  <AnimatedCounter end={2500} />+
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>Jurisdictions Covered</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-orbitron font-bold text-purple-500 dark:text-purple-400 mb-2">
                  <AnimatedCounter end={99} />.9%
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>Uptime Guarantee</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-orbitron font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                  &lt;<AnimatedCounter end={10} />s
                </div>
                <div className={`text-sm ${theme.text.secondary} font-space`}>Average Response</div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-orbitron font-bold bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent mb-6 leading-tight pb-2">
                Why Choose Ordrly?
              </h2>
              <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto`}>
                Municipal research technology powered by Ordrly that delivers accurate, real-time data
                with enterprise-grade reliability and developer-friendly integration.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <Card className={`${theme.card} ${theme.border.blue} text-center`}>
                <CardHeader>
                  <div className="w-16 h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4 quantum-glow">
                    <Zap className={`w-8 h-8 ${theme.text.accent}`} />
                  </div>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Lightning Fast</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Get municipal data in seconds, not hours. Our optimized API delivers
                    comprehensive research results with sub-10 second response times.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.green} text-center`}>
                <CardHeader>
                  <div className="w-16 h-16 bg-green-500/10 dark:bg-green-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Shield className={`w-8 h-8 ${theme.text.success}`} />
                  </div>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Highly Accurate</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Research powered by Ordrly with verified sources and official documentation.
                    Every result includes citations and confidence scores for reliability.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.purple} text-center`}>
                <CardHeader>
                  <div className="w-16 h-16 bg-purple-500/10 dark:bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-purple-500 dark:text-purple-400" />
                  </div>
                  <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Always Updated</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                    Real-time data updates ensure you always have the latest municipal
                    information. Our system monitors changes across all jurisdictions.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Advanced Features Grid - Removed Enterprise Security, Developer First, Real-time Analytics */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className={`${theme.card} border-border`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Globe className="w-6 h-6 text-cyan-600 dark:text-cyan-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>Global Coverage</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    2,500+ jurisdictions across North America with expanding international coverage.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-border`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Layers className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>Smart Caching</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    Intelligent caching system reduces costs and improves response times over time.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-border`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Sparkles className="w-6 h-6 text-pink-600 dark:text-pink-400" />
                    <CardTitle className={`font-exo text-lg ${theme.text.primary}`}>AI-Tailored</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className={`text-sm ${theme.text.secondary} font-space`}>
                    Advanced AI models tailored specifically for municipal research and compliance analysis.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl text-center">
            <div className="mb-8">
              <h2 className="text-4xl md:text-5xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-green-600 dark:to-green-400 bg-clip-text text-transparent mb-6">
                Ready to Transform Your Municipal Research?
              </h2>
              <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto leading-relaxed`}>
                Join the future of municipal research. Start with our free trial and experience
                the power of AI-driven compliance data at your fingertips.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12">
              <Link href="/search">
                <Button size="lg" className={`${theme.button.success} px-10 py-4 text-lg font-exo`}>
                  <CheckCircle className="w-5 h-5 mr-3" />
                  Start Free Trial
                  <ArrowRight className="w-5 h-5 ml-3" />
                </Button>
              </Link>
              <Link href="/demo">
                <Button variant="outline" size="lg" className={`${theme.button.secondary} px-10 py-4 text-lg font-exo`}>
                  <Eye className="w-5 h-5 mr-3" />
                  View Live Demo
                </Button>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className={`${theme.text.secondary} font-space`}>
                <CheckCircle className={`w-5 h-5 ${theme.text.success} mx-auto mb-2`} />
                <span className="text-sm">No credit card required</span>
              </div>
              <div className={`${theme.text.secondary} font-space`}>
                <CheckCircle className={`w-5 h-5 ${theme.text.success} mx-auto mb-2`} />
                <span className="text-sm">7-day free trial</span>
              </div>
              <div className={`${theme.text.secondary} font-space`}>
                <CheckCircle className={`w-5 h-5 ${theme.text.success} mx-auto mb-2`} />
                <span className="text-sm">Cancel anytime</span>
              </div>
            </div>
          </div>
        </section>
    </div>
  )
}
