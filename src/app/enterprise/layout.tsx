import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { EnterpriseNavigation } from '@/components/enterprise/EnterpriseNavigation'
import { User } from '@supabase/supabase-js'

export default async function EnterpriseLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // SECURITY: All enterprise routes now require proper authentication
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile to check subscription tier and role
  const { data: profile } = await supabase
    .from('profiles')
    .select('subscription_tier, role, full_name')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has enterprise access
  const hasEnterpriseAccess = profile.subscription_tier === 'enterprise' ||
                             profile.role === 'enterprise' ||
                             profile.role === 'admin'

  if (!hasEnterpriseAccess) {
    redirect('/pricing?upgrade=enterprise')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <EnterpriseNavigation profile={profile} />
      <main className="py-8">
        {children}
      </main>
    </div>
  )
}
