'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw, Home, Mail } from 'lucide-react'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error)
  }, [error])

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950 dark:to-orange-950 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center space-y-8">
        {/* Error Illustration */}
        <div className="space-y-4">
          <div className="bg-red-100 dark:bg-red-900 rounded-full w-20 h-20 flex items-center justify-center mx-auto">
            <AlertTriangle className="h-10 w-10 text-red-600 dark:text-red-400" />
          </div>
          <h1 className="text-3xl font-bold text-foreground">Something went wrong</h1>
          <p className="text-lg text-muted-foreground">
            We encountered an unexpected error while processing your request.
          </p>
        </div>

        {/* Error Details (Development only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 text-left">
            <h3 className="font-semibold text-sm text-foreground mb-2">Error Details:</h3>
            <p className="text-xs text-muted-foreground font-mono break-all">
              {error.message}
            </p>
            {error.digest && (
              <p className="text-xs text-muted-foreground mt-2">
                Error ID: {error.digest}
              </p>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button onClick={reset} className="w-full sm:w-auto">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <Button
              variant="outline"
              className="w-full sm:w-auto"
              onClick={() => window.location.href = '/'}
            >
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Button>
          </div>
        </div>

        {/* Help Section */}
        <div className="pt-8 border-t border-border">
          <p className="text-sm text-muted-foreground mb-4">
            If this problem persists, please contact our support team.
          </p>
          <Button
            variant="ghost"
            className="w-full sm:w-auto"
            onClick={() => window.location.href = 'mailto:<EMAIL>'}
          >
            <Mail className="h-4 w-4 mr-2" />
            Contact Support
          </Button>
        </div>

        {/* Additional Help Links */}
        <div className="flex flex-wrap justify-center gap-4 text-sm">
          <a href="/faq" className="text-primary hover:underline">
            FAQ
          </a>
          <a href="/contact" className="text-primary hover:underline">
            Help Center
          </a>
          <a href="https://twitter.com/ordrlyAI" className="text-primary hover:underline">
            Status Updates
          </a>
        </div>
      </div>
    </div>
  )
}
