import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Frequently Asked Questions - Property Compliance Help | Ordrly',
  description: 'Find answers to common questions about property compliance, building permits, zoning regulations, and using Ordrly for your construction projects.',
  keywords: ['FAQ', 'property compliance questions', 'building permit help', 'zoning FAQ', 'ordinance questions', 'construction regulations help'],
  openGraph: {
    title: 'Frequently Asked Questions - Property Compliance Help | Ordrly',
    description: 'Find answers to common questions about property compliance, building permits, zoning regulations, and using Ordrly.',
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: 'Frequently Asked Questions - Property Compliance Help | Ordrly',
    description: 'Find answers to common questions about property compliance, building permits, and zoning regulations.',
  },
}

export default function FAQLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
