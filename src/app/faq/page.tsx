'use client'

/* eslint-disable react/no-unescaped-entities */
import { useState, useEffect, useMemo, useCallback } from 'react'

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'
import { Search, ChevronDown, ChevronUp, HelpCircle } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/form'
import Link from 'next/link'

interface FAQ {
  id: string
  question: string
  answer: string
  category: string
  tags: string[]
}

export default function FAQPage() {
  const [faqs, setFaqs] = useState<FAQ[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(true)
  const supabase = createClient()

  const fetchFAQs = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .select('*')
        .eq('is_published', true)
        .order('sort_order', { ascending: true })

      if (error) throw error
      setFaqs(data || [])
    } catch (error) {
      console.error('Error fetching FAQs:', error)
    } finally {
      setIsLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    fetchFAQs()
  }, [fetchFAQs])

  const categories = useMemo(() => {
    const cats = ['all', ...new Set(faqs.map(faq => faq.category))]
    return cats
  }, [faqs])

  const filteredFAQs = useMemo(() => {
    return faqs.filter(faq => {
      const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory
      const matchesSearch = searchQuery === '' ||
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      return matchesCategory && matchesSearch
    })
  }, [faqs, selectedCategory, searchQuery])

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedItems(newExpanded)
  }

  const formatCategory = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">Loading FAQs...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background py-16 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="bg-primary/10 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-8">
            <HelpCircle className="h-10 w-10 text-primary" />
          </div>
          <h1 className="text-5xl font-bold text-foreground mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-4 leading-relaxed">
            Find answers to common questions about <span className="text-primary font-semibold">Ordrly</span>, property compliance research,
            and how to use our AI-powered platform to navigate building regulations.
          </p>
          <p className="text-base text-muted-foreground/90 max-w-2xl mx-auto mb-6">
            Browse by categories and topics to find exactly what you need, or use the search to find specific information.
          </p>
          <p className="text-sm text-muted-foreground/70">
            💡 Click any question to expand the detailed answer
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-gradient-to-r from-card to-card/80 border border-border/50 rounded-3xl p-8 mb-12 shadow-2xl backdrop-blur-sm animate-slide-up">
          <div className="flex flex-col sm:flex-row gap-6">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search FAQs, topics, or keywords..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 h-12 text-base rounded-xl border-border/50 focus:border-primary/50 focus:ring-primary/20"
              />
            </div>

            {/* Category Filter */}
            <div className="sm:w-56">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full h-12 px-4 py-2 border border-border/50 bg-background rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary/50 text-foreground text-base"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {formatCategory(category)}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {searchQuery && (
            <div className="mt-6 p-4 bg-primary/5 rounded-xl border border-primary/10">
              <div className="text-sm text-muted-foreground">
                <span className="font-medium text-primary">{filteredFAQs.length}</span> result{filteredFAQs.length !== 1 ? 's' : ''} found for
                <span className="font-medium text-foreground"> "{searchQuery}"</span>
              </div>
            </div>
          )}
        </div>

        {/* FAQ List */}
        <div className="space-y-6">
          {filteredFAQs.length === 0 ? (
            <div className="bg-gradient-to-br from-card to-card/80 border border-border/50 rounded-3xl p-12 text-center shadow-2xl animate-fade-in">
              <div className="bg-muted/20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <HelpCircle className="h-10 w-10 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold text-card-foreground mb-3">No FAQs found</h3>
              <p className="text-muted-foreground text-base">
                {searchQuery || selectedCategory !== 'all'
                  ? 'Try adjusting your search or filter criteria to find what you\'re looking for.'
                  : 'No FAQs are available at the moment. Check back soon for updates!'
                }
              </p>
            </div>
          ) : (
            filteredFAQs.map((faq, index) => (
              <div
                key={faq.id}
                className="bg-gradient-to-r from-card to-card/90 border border-border/50 rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 animate-slide-up hover:scale-[1.01]"
                style={{ animationDelay: `${index * 75}ms` }}
              >
                <button
                  onClick={() => toggleExpanded(faq.id)}
                  className="w-full px-8 py-6 text-left hover:bg-gradient-to-r hover:from-muted/30 hover:to-muted/10 transition-all duration-200 collapsible-header group"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-card-foreground mb-3 section-title group-hover:text-primary transition-colors leading-relaxed">
                        {faq.question}
                      </h3>
                      <div className="flex items-center flex-wrap gap-2">
                        <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-primary/15 text-primary border border-primary/20">
                          {formatCategory(faq.category)}
                        </span>
                        {faq.tags.slice(0, 3).map(tag => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary/40 text-secondary-foreground border border-border/30 hover:bg-secondary/60 transition-colors"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="ml-6">
                      {expandedItems.has(faq.id) ? (
                        <ChevronUp className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-all duration-200 transform group-hover:scale-110" />
                      ) : (
                        <ChevronDown className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-all duration-200 transform group-hover:scale-110" />
                      )}
                    </div>
                  </div>
                </button>

                {expandedItems.has(faq.id) && (
                  <div className="px-8 pb-6">
                    <div className="border-t border-border/30 pt-6">
                      <div className="prose prose-base max-w-none text-muted-foreground leading-relaxed">
                        {faq.answer.split('\n').map((paragraph, index) => (
                          <p key={index} className="mb-3 last:mb-0 text-base">
                            {paragraph}
                          </p>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Contact Section */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-br from-primary/5 via-card to-blue-50/20 dark:to-blue-950/20 border border-border/50 rounded-3xl p-12 max-w-5xl mx-auto shadow-2xl animate-fade-in backdrop-blur-sm">
            <div className="bg-primary/10 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-8">
              <HelpCircle className="h-12 w-12 text-primary" />
            </div>
            <h2 className="text-3xl font-bold text-card-foreground mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
              Still have questions?
            </h2>
            <p className="text-lg text-muted-foreground mb-4 max-w-2xl mx-auto leading-relaxed">
              Can't find what you're looking for? Our team is here to help you navigate property compliance
              and get the specific answers you need for your project.
            </p>
            <p className="text-base text-muted-foreground/90 mb-10 max-w-xl mx-auto">
              Whether it's about using Ordrly, understanding compliance requirements, or getting help with your specific project,
              we're here to support you every step of the way.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/contact">
                <Button size="lg" className="px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-200 hover:scale-[1.02] bg-primary hover:bg-primary/90">
                  Contact Support
                </Button>
              </Link>
              <Link href="/chat">
                <Button variant="outline" size="lg" className="px-10 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02] border-primary/20 hover:border-primary/40">
                  Try Ordrly Chat
                </Button>
              </Link>
            </div>
            <p className="text-sm text-muted-foreground/70 mt-6">
              💬 Get personalized help from our team • 🚀 Start your free trial today
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
