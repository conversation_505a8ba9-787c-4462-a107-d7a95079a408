'use client'

import { useState } from 'react'
import { Plus, ThumbsUp, MessageCircle, Clock, CheckCircle, Star, TrendingUp } from 'lucide-react'

export default function FeatureRequestsPage() {
  const [showForm, setShowForm] = useState(false)
  const [newRequest, setNewRequest] = useState({
    title: '',
    description: '',
    category: 'general',
    priority: 'medium'
  })

  // Mock data - in real implementation, this would come from API
  const featureRequests = [
    {
      id: 1,
      title: 'Mobile App for iOS and Android',
      description: 'A native mobile app would make it easier to check compliance on the go, especially for contractors working in the field.',
      category: 'Mobile',
      priority: 'high',
      status: 'in-progress',
      votes: 127,
      comments: 23,
      author: '<PERSON>',
      created_at: '2024-01-15',
      userVoted: false
    },
    {
      id: 2,
      title: 'Bulk Address Processing',
      description: 'Allow users to upload a CSV file with multiple addresses and get compliance reports for all of them at once.',
      category: 'Features',
      priority: 'medium',
      status: 'planned',
      votes: 89,
      comments: 15,
      author: '<PERSON>',
      created_at: '2024-01-20',
      userVoted: true
    },
    {
      id: 3,
      title: 'Integration with Popular Design Software',
      description: 'Direct integration with AutoCAD, SketchUp, and other design tools to automatically check compliance during the design process.',
      category: 'Integrations',
      priority: 'medium',
      status: 'under-review',
      votes: 76,
      comments: 12,
      author: 'Mike R.',
      created_at: '2024-01-25',
      userVoted: false
    },
    {
      id: 4,
      title: 'Historical Ordinance Changes',
      description: 'Track how ordinances have changed over time and show when specific rules were last updated.',
      category: 'Data',
      priority: 'low',
      status: 'completed',
      votes: 45,
      comments: 8,
      author: 'Lisa K.',
      created_at: '2024-01-10',
      userVoted: false
    }
  ]

  const categories = ['General', 'Mobile', 'Features', 'Integrations', 'Data', 'UI/UX']
  const priorities = ['Low', 'Medium', 'High']

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'in-progress': return 'bg-blue-100 text-blue-800'
      case 'planned': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600'
      case 'medium': return 'text-yellow-600'
      default: return 'text-green-600'
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Submit new feature request
    console.log('Submitting feature request:', newRequest)
    setShowForm(false)
    setNewRequest({ title: '', description: '', category: 'general', priority: 'medium' })
  }

  const handleVote = (requestId: number) => {
    // Toggle vote for feature request
    console.log('Voting for request:', requestId)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Feature Requests & Suggestions
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl">
                Help shape the future of Ordrly! Vote on existing feature requests
                or suggest new features you&apos;d like to see.
              </p>
            </div>
            <button
              onClick={() => setShowForm(true)}
              className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-5 w-5 mr-2" />
              Suggest Feature
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Stats */}
        <div className="grid gap-6 md:grid-cols-4 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Star className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-bold text-gray-900">127</p>
                <p className="text-sm text-gray-600">Total Requests</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-bold text-gray-900">23</p>
                <p className="text-sm text-gray-600">Completed</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-bold text-gray-900">15</p>
                <p className="text-sm text-gray-600">In Progress</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-2xl font-bold text-gray-900">1,247</p>
                <p className="text-sm text-gray-600">Total Votes</p>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Request Form Modal */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">
                  Suggest a New Feature
                </h2>
                <p className="text-gray-600 mt-1">
                  Tell us about a feature you&apos;d like to see in Ordrly
                </p>
              </div>
              
              <form onSubmit={handleSubmit} className="p-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Feature Title
                    </label>
                    <input
                      type="text"
                      value={newRequest.title}
                      onChange={(e) => setNewRequest({...newRequest, title: e.target.value})}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Brief, descriptive title for your feature request"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={newRequest.description}
                      onChange={(e) => setNewRequest({...newRequest, description: e.target.value})}
                      required
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Describe the feature in detail. How would it work? How would it help you?"
                    />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category
                      </label>
                      <select
                        value={newRequest.category}
                        onChange={(e) => setNewRequest({...newRequest, category: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {categories.map(category => (
                          <option key={category} value={category.toLowerCase()}>
                            {category}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Priority
                      </label>
                      <select
                        value={newRequest.priority}
                        onChange={(e) => setNewRequest({...newRequest, priority: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {priorities.map(priority => (
                          <option key={priority} value={priority.toLowerCase()}>
                            {priority}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-4 mt-8">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Submit Request
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Feature Requests List */}
        <div className="space-y-6">
          {featureRequests.map((request) => (
            <div key={request.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {request.title}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                        {request.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3">
                      {request.description}
                    </p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>By {request.author}</span>
                      <span>•</span>
                      <span>{request.created_at}</span>
                      <span>•</span>
                      <span className="capitalize">{request.category}</span>
                      <span>•</span>
                      <span className={`font-medium ${getPriorityColor(request.priority)}`}>
                        {request.priority.charAt(0).toUpperCase() + request.priority.slice(1)} Priority
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <button
                      onClick={() => handleVote(request.id)}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                        request.userVoted
                          ? 'bg-blue-100 text-blue-700 border border-blue-200'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <ThumbsUp className="h-4 w-4" />
                      <span className="font-medium">{request.votes}</span>
                      <span>Vote</span>
                    </button>
                    
                    <div className="flex items-center space-x-2 text-gray-600">
                      <MessageCircle className="h-4 w-4" />
                      <span>{request.comments} comments</span>
                    </div>
                  </div>

                  <button className="text-blue-600 hover:text-blue-700 font-medium">
                    View Details →
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Guidelines */}
        <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-semibold text-blue-900 mb-3">
            Feature Request Guidelines
          </h3>
          <ul className="space-y-2 text-blue-800">
            <li>• Be specific and detailed in your description</li>
            <li>• Search existing requests before submitting to avoid duplicates</li>
            <li>• Vote on existing requests that you&apos;d also like to see</li>
            <li>• Explain how the feature would benefit you and other users</li>
            <li>• Consider the technical feasibility and complexity</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
