'use client'

import { useState, useEffect } from 'react'
import { MessageCircle, Star, Send, CheckCircle, Lightbulb, Bug, Heart } from 'lucide-react'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

export default function FeedbackPage() {
  const [feedbackType, setFeedbackType] = useState('general')
  const [rating, setRating] = useState<number | null>(null)
  const [subject, setSubject] = useState('')
  const [message, setMessage] = useState('')
  const [email, setEmail] = useState('')
  const [submitted, setSubmitted] = useState(false)
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [authLoading, setAuthLoading] = useState(true)

  const supabase = createClient()
  const router = useRouter()

  // Trust middleware for auth - just get user for display
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setAuthLoading(false)
    }
    getUser()
  }, [])

  const feedbackTypes = [
    {
      id: 'general',
      title: 'General Feedback',
      description: 'Share your thoughts about Ordrly',
      icon: MessageCircle,
      color: 'primary'
    },
    {
      id: 'feature_request',
      title: 'Feature Suggestion',
      description: 'Suggest new features or improvements',
      icon: Lightbulb,
      color: 'warning'
    },
    {
      id: 'bug',
      title: 'Report a Bug',
      description: 'Let us know about technical issues',
      icon: Bug,
      color: 'destructive'
    },
    {
      id: 'general',
      title: 'Compliment',
      description: 'Tell us what you love about Ordrly',
      icon: Heart,
      color: 'success'
    }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: feedbackType,
          title: subject.trim(),
          description: message.trim(),
          email: email.trim() || undefined,
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      })

      if (response.ok) {
        setSubmitted(true)
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit feedback')
      }
    } catch (err) {
      console.error('Failed to submit feedback:', err)
      alert(`Failed to submit feedback: ${err instanceof Error ? err.message : 'Please try again.'}`)
    } finally {
      setLoading(false)
    }
  }

  const renderStars = () => {
    return Array.from({ length: 5 }, (_, i) => (
      <button
        key={i}
        type="button"
        onClick={() => setRating(i + 1)}
        onMouseEnter={() => setRating(i + 1)}
        className={`h-8 w-8 ${
          rating && i < rating ? 'text-yellow-400 fill-current' : 'text-muted-foreground'
        } hover:text-yellow-400 transition-colors`}
        aria-label={`${i + 1} star${i === 0 ? '' : 's'}`}
      >
        <Star className="h-full w-full" />
      </button>
    ))
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to login
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center px-4">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-card border border-border rounded-2xl shadow-premium p-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-green-500/10 rounded-full">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-card-foreground mb-4">
              Thank You!
            </h2>
            <p className="text-muted-foreground mb-6">
              Your feedback has been submitted successfully. We appreciate you taking the time to help us improve Ordrly.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => {
                  setSubmitted(false)
                  setFeedbackType('general')
                  setRating(null)
                  setSubject('')
                  setMessage('')
                  setEmail('')
                }}
                className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Submit More Feedback
              </button>
              <Link
                href="/"
                className="block w-full px-4 py-2 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors text-center"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b border-border shadow-premium">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
              <MessageCircle className="h-8 w-8 text-primary" />
            </div>
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Share Your Feedback
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Your insights help us improve. Choose a feedback type, rate your experience, and provide details below.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-card border border-border rounded-2xl shadow-premium overflow-hidden">
          <form onSubmit={handleSubmit}>
            {/* Feedback Type Selection */}
            <div className="p-8 border-b border-border">
              <h2 className="text-2xl font-semibold text-card-foreground mb-6">
                What type of feedback do you have?
              </h2>
              <div className="grid gap-3 grid-cols-2 lg:grid-cols-4">
                {feedbackTypes.map((type) => {
                  const isSelected = feedbackType === type.id
                  const colorClasses = {
                    primary: isSelected ? 'border-2 border-blue-400 bg-primary/10' : 'border-border hover:border-blue-400 hover:bg-muted/50',
                    warning: isSelected ? 'border-2 border-orange-400 bg-orange-500/10' : 'border-border hover:border-orange-400 hover:bg-muted/50',
                    destructive: isSelected ? 'border-2 border-red-400 bg-destructive/10' : 'border-border hover:border-red-400 hover:bg-muted/50',
                    success: isSelected ? 'border-2 border-green-400 bg-green-500/10' : 'border-border hover:border-green-400 hover:bg-muted/50'
                  }

                  return (
                    <button
                      key={type.id}
                      type="button"
                      onClick={() => setFeedbackType(type.id)}
                      className={`p-4 rounded-xl border transition-all duration-200 hover:scale-[1.02] ${colorClasses[type.color as keyof typeof colorClasses]}`}
                    >
                      <div className="flex flex-col items-center text-center">
                        <div className={`p-2 rounded-lg mb-3 ${
                          isSelected
                            ? type.color === 'primary' ? 'bg-primary/20' :
                              type.color === 'warning' ? 'bg-orange-500/20' :
                              type.color === 'destructive' ? 'bg-destructive/20' :
                              'bg-green-500/20'
                            : 'bg-muted'
                        }`}>
                          <type.icon className={`h-5 w-5 ${
                            isSelected
                              ? type.color === 'primary' ? 'text-primary' :
                                type.color === 'warning' ? 'text-orange-600' :
                                type.color === 'destructive' ? 'text-destructive' :
                                'text-green-600'
                              : 'text-muted-foreground'
                          }`} />
                        </div>
                        <h3 className="font-medium text-card-foreground mb-1 text-sm">
                          {type.title}
                        </h3>
                        <p className="text-xs text-muted-foreground leading-tight">
                          {type.description}
                        </p>
                      </div>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Rating Section */}
            <div className="p-6 border-b border-border">
              <h2 className="text-2xl font-semibold text-card-foreground mb-4">
                How would you rate your overall experience?
              </h2>
              <div className="flex items-center space-x-2">
                {renderStars()}
                {rating && (
                  <span className="ml-4 text-muted-foreground">
                    {rating} out of 5 stars
                  </span>
                )}
              </div>
            </div>

            {/* Feedback Form */}
            <div className="p-6">
              <div className="space-y-6">
                {/* Subject */}
                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-card-foreground mb-2">
                    Subject*
                  </label>
                  <input
                    type="text"
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    required
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder={
                      feedbackType === 'bug' ? 'Briefly describe the issue' :
                      feedbackType === 'feature_request' ? 'What feature would you like to see?' :
                      'Brief summary of your feedback'
                    }
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Brief summary (e.g., 'Difficulty logging in').
                  </p>
                </div>

                {/* Message */}
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-card-foreground mb-2">
                    Message*
                  </label>
                  <textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    required
                    rows={8}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none min-h-40"
                    placeholder={
                      feedbackType === 'bug' ? 'Please describe the issue in detail. Include steps to reproduce if possible.' :
                      feedbackType === 'feature_request' ? 'Describe your feature idea and how it would help you.' :
                      'Share your thoughts, ideas, or concerns in detail.'
                    }
                  />
                </div>

                {/* Email (Optional) */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-card-foreground mb-2">
                    Email (Optional)
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="<EMAIL>"
                  />
                  <p className="mt-2 text-sm text-muted-foreground">
                    Provide your email if you&apos;d like us to follow up with you about your feedback.
                  </p>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-4">
                  <button
                    type="submit"
                    disabled={loading || !subject.trim() || !message.trim()}
                    className={`flex items-center px-8 py-3 rounded-lg font-semibold transition-all duration-200 ${
                      loading || !subject.trim() || !message.trim()
                        ? 'bg-muted text-muted-foreground cursor-not-allowed opacity-50'
                        : 'bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg'
                    }`}
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Submit Feedback
                      </>
                    )}
                  </button>
                  {(loading || !subject.trim() || !message.trim()) && (
                    <p className="text-xs text-muted-foreground mt-2 mr-4">
                      Please select a feedback type and fill in both subject and message to submit.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Additional Help */}
        <div className="mt-6 bg-primary/5 border border-primary/20 border-t border-gray-700 rounded-2xl p-6">
          <h3 className="font-semibold text-foreground mb-3">
            Need immediate help?
          </h3>
          <p className="text-muted-foreground mb-6">
            If you&apos;re experiencing urgent issues or need immediate assistance,
            try these resources:
          </p>
          <div className="flex flex-wrap gap-4">
            <Link
              href="/faq"
              className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-lg hover:shadow-xl hover:scale-[1.02]"
            >
              FAQ & Help
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-6 py-3 bg-secondary text-secondary-foreground border border-border rounded-lg hover:bg-muted transition-all duration-200 font-medium shadow-lg hover:shadow-xl hover:scale-[1.02]"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
