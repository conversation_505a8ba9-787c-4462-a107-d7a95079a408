import Link from 'next/link'
import { ForgotPasswordForm } from '@/components/auth/ForgotPasswordForm'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { OrdrlyLogo } from '@/components/ui/ordrly-logo'

export default async function ForgotPasswordPage({
  searchParams,
}: {
  searchParams: Promise<{ error?: string; message?: string }>
}) {
  const params = await searchParams

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gradient-to-br from-background via-background/95 to-muted/20 relative overflow-hidden flex items-center justify-center p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 dark:bg-primary/20 rounded-full filter blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary/15 dark:bg-primary/25 rounded-full filter blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-primary/5 dark:bg-primary/10 rounded-full filter blur-3xl animate-pulse delay-500"></div>
      </div>

      <Card className="w-full max-w-md relative z-10 bg-card/50 backdrop-blur-sm border border-border shadow-2xl">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-6">
            <OrdrlyLogo size="xl" className="w-16 h-16" />
          </div>
          <CardTitle className="text-3xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-primary/80 bg-clip-text text-transparent">
            Reset Password
          </CardTitle>
          <CardDescription className="text-muted-foreground font-space">
            Enter your email address and we'll send you a link to reset your password.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {params.error && (
            <Alert className="border-red-500/50 bg-red-500/10 dark:bg-red-500/20 backdrop-blur-sm">
              <AlertDescription className="text-red-500 dark:text-red-400 font-space">{params.error}</AlertDescription>
            </Alert>
          )}

          {params.message && (
            <Alert className="border-green-500/50 bg-green-500/10 dark:bg-green-500/20 backdrop-blur-sm">
              <AlertDescription className="text-green-600 dark:text-green-400 font-space">{params.message}</AlertDescription>
            </Alert>
          )}

          <ForgotPasswordForm />

          <div className="text-center space-y-3">
            <Link
              href="/login"
              className="text-sm text-primary hover:text-primary/80 transition-colors font-space"
            >
              ← Back to sign in
            </Link>
            <div className="text-sm text-muted-foreground font-space">
              Don't have an account?{' '}
              <Link
                href="/signup"
                className="text-primary hover:text-primary/80 transition-colors font-medium"
              >
                Sign up
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
