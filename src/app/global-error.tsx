'use client'

import { useEffect } from 'react'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global application error:', error)
  }, [error])

  return (
    <html>
      <body>
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center px-4">
          <div className="max-w-md w-full text-center space-y-8">
            {/* Critical Error Illustration */}
            <div className="space-y-4">
              <div className="text-6xl">⚠️</div>
              <h1 className="text-3xl font-bold text-gray-900">Critical Error</h1>
              <p className="text-lg text-gray-600">
                We&apos;re experiencing technical difficulties. Please try refreshing the page.
              </p>
            </div>

            {/* Error Details (Development only) */}
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-gray-100 rounded-lg p-4 text-left">
                <h3 className="font-semibold text-sm text-gray-900 mb-2">Error Details:</h3>
                <p className="text-xs text-gray-600 font-mono break-all">
                  {error.message}
                </p>
                {error.digest && (
                  <p className="text-xs text-gray-600 mt-2">
                    Error ID: {error.digest}
                  </p>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-4">
              <button
                onClick={reset}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Go to Homepage
              </button>
            </div>

            {/* Support Contact */}
            <div className="pt-8 border-t border-gray-200">
              <p className="text-sm text-gray-600 mb-4">
                If this problem continues, please contact our support team.
              </p>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}
