@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Exo+2:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Background & Foreground */
    --background: 255 255 255; /* white */
    --foreground: 15 23 42; /* slate-900 */

    /* Primary Brand Colors (Trustworthy Teal-Blue) */
    --primary-50: 240 249 255; /* sky-50 */
    --primary-100: 224 242 254; /* sky-100 */
    --primary-200: 186 230 253; /* sky-200 */
    --primary-300: 125 211 252; /* sky-300 */
    --primary-400: 56 189 248; /* sky-400 */
    --primary-500: 14 165 233; /* sky-500 */
    --primary-600: 2 132 199; /* sky-600 */
    --primary-700: 3 105 161; /* sky-700 */
    --primary-800: 7 89 133; /* sky-800 */
    --primary-900: 12 74 110; /* sky-900 */
    --primary-950: 8 47 73; /* sky-950 */
    --primary-foreground: 255 255 255; /* white */

    /* Secondary Neutrals (Enhanced Grays) */
    --secondary-50: 248 250 252; /* slate-50 */
    --secondary-100: 241 245 249; /* slate-100 */
    --secondary-200: 226 232 240; /* slate-200 */
    --secondary-300: 203 213 225; /* slate-300 */
    --secondary-400: 148 163 184; /* slate-400 */
    --secondary-500: 100 116 139; /* slate-500 */
    --secondary-600: 71 85 105; /* slate-600 */
    --secondary-700: 51 65 85; /* slate-700 */
    --secondary-800: 30 41 59; /* slate-800 */
    --secondary-900: 15 23 42; /* slate-900 */
    --secondary-950: 2 6 23; /* slate-950 */
    --secondary-foreground: 255 255 255; /* white */

    /* Semantic Colors */
    --success-50: 240 253 244; /* green-50 */
    --success-100: 220 252 231; /* green-100 */
    --success-200: 187 247 208; /* green-200 */
    --success-300: 134 239 172; /* green-300 */
    --success-400: 74 222 128; /* green-400 */
    --success-500: 34 197 94; /* green-500 */
    --success-600: 22 163 74; /* green-600 */
    --success-700: 21 128 61; /* green-700 */
    --success-800: 22 101 52; /* green-800 */
    --success-900: 20 83 45; /* green-900 */
    --success-950: 5 46 22; /* green-950 */
    --success-foreground: 255 255 255; /* white */

    --warning-50: 255 251 235; /* amber-50 */
    --warning-100: 254 243 199; /* amber-100 */
    --warning-200: 253 230 138; /* amber-200 */
    --warning-300: 252 211 77; /* amber-300 */
    --warning-400: 251 191 36; /* amber-400 */
    --warning-500: 245 158 11; /* amber-500 */
    --warning-600: 217 119 6; /* amber-600 */
    --warning-700: 180 83 9; /* amber-700 */
    --warning-800: 146 64 14; /* amber-800 */
    --warning-900: 120 53 15; /* amber-900 */
    --warning-950: 69 26 3; /* amber-950 */
    --warning-foreground: 255 255 255; /* white */

    --error-50: 254 242 242; /* red-50 */
    --error-100: 254 226 226; /* red-100 */
    --error-200: 254 202 202; /* red-200 */
    --error-300: 252 165 165; /* red-300 */
    --error-400: 248 113 113; /* red-400 */
    --error-500: 239 68 68; /* red-500 */
    --error-600: 220 38 38; /* red-600 */
    --error-700: 185 28 28; /* red-700 */
    --error-800: 153 27 27; /* red-800 */
    --error-900: 127 29 29; /* red-900 */
    --error-950: 69 10 10; /* red-950 */
    --error-foreground: 255 255 255; /* white */

    --info-50: 239 246 255; /* blue-50 */
    --info-100: 219 234 254; /* blue-100 */
    --info-200: 191 219 254; /* blue-200 */
    --info-300: 147 197 253; /* blue-300 */
    --info-400: 96 165 250; /* blue-400 */
    --info-500: 59 130 246; /* blue-500 */
    --info-600: 37 99 235; /* blue-600 */
    --info-700: 29 78 216; /* blue-700 */
    --info-800: 30 64 175; /* blue-800 */
    --info-900: 30 58 138; /* blue-900 */
    --info-950: 23 37 84; /* blue-950 */
    --info-foreground: 255 255 255; /* white */

    /* UI Component Colors */
    --card: 255 255 255; /* white */
    --card-foreground: 15 23 42; /* slate-900 */
    --popover: 255 255 255; /* white */
    --popover-foreground: 15 23 42; /* slate-900 */
    --muted: 248 250 252; /* slate-50 */
    --muted-foreground: 100 116 139; /* slate-500 */
    --accent: 248 250 252; /* slate-50 */
    --accent-foreground: 15 23 42; /* slate-900 */
    --destructive: 239 68 68; /* red-500 */
    --destructive-foreground: 255 255 255; /* white */
    --border: 226 232 240; /* slate-200 */
    --input: 226 232 240; /* slate-200 */
    --ring: 14 165 233; /* sky-500 */

    /* Border Radius */
    --radius: 0.75rem; /* 12px - increased from default for premium feel */
  }

  .dark {
    /* Background & Foreground - Much better contrast */
    --background: 2 6 23; /* slate-950 - true dark background */
    --foreground: 248 250 252; /* slate-50 - high contrast white */

    /* Primary Brand Colors (Trustworthy Teal-Blue) - optimized for dark mode */
    --primary-50: 8 47 73; /* sky-950 */
    --primary-100: 12 74 110; /* sky-900 */
    --primary-200: 7 89 133; /* sky-800 */
    --primary-300: 3 105 161; /* sky-700 */
    --primary-400: 2 132 199; /* sky-600 */
    --primary-500: 56 189 248; /* sky-400 - bright and accessible */
    --primary-600: 125 211 252; /* sky-300 */
    --primary-700: 186 230 253; /* sky-200 */
    --primary-800: 224 242 254; /* sky-100 */
    --primary-900: 240 249 255; /* sky-50 */
    --primary-950: 240 249 255; /* sky-50 */
    --primary-foreground: 2 6 23; /* slate-950 - dark text on bright primary */

    /* Secondary Neutrals (Enhanced Grays) - Much better contrast */
    --secondary-50: 2 6 23; /* slate-950 */
    --secondary-100: 15 23 42; /* slate-900 */
    --secondary-200: 30 41 59; /* slate-800 */
    --secondary-300: 51 65 85; /* slate-700 */
    --secondary-400: 71 85 105; /* slate-600 */
    --secondary-500: 100 116 139; /* slate-500 */
    --secondary-600: 148 163 184; /* slate-400 */
    --secondary-700: 203 213 225; /* slate-300 */
    --secondary-800: 226 232 240; /* slate-200 */
    --secondary-900: 241 245 249; /* slate-100 */
    --secondary-950: 248 250 252; /* slate-50 */
    --secondary-foreground: 2 6 23; /* slate-950 */

    /* UI Component Colors - Much better visibility and contrast */
    --card: 15 23 42; /* slate-900 - good contrast against background */
    --card-foreground: 248 250 252; /* slate-50 - high contrast text */
    --popover: 15 23 42; /* slate-900 */
    --popover-foreground: 248 250 252; /* slate-50 */
    --muted: 30 41 59; /* slate-800 - visible but subdued */
    --muted-foreground: 148 163 184; /* slate-400 - much more readable */
    --accent: 30 41 59; /* slate-800 */
    --accent-foreground: 248 250 252; /* slate-50 */
    --destructive: 248 113 113; /* red-400 - brighter for dark mode */
    --destructive-foreground: 2 6 23; /* slate-950 */
    --border: 51 65 85; /* slate-700 - visible borders */
    --input: 30 41 59; /* slate-800 - good contrast for inputs */
    --ring: 125 211 252; /* sky-300 - bright focus rings */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    scrollbar-width: thin;
    scrollbar-color: #9ca3af #f3f4f6;
  }

  .dark body {
    scrollbar-color: #6b7280 #374151;
  }

  body::-webkit-scrollbar {
    width: 14px;
  }

  body::-webkit-scrollbar-track {
    background: #f3f4f6;
  }

  .dark body::-webkit-scrollbar-track {
    background: #374151;
  }

  body::-webkit-scrollbar-thumb {
    background-color: #9ca3af;
    border-radius: 7px;
    border: 2px solid #f3f4f6;
  }

  .dark body::-webkit-scrollbar-thumb {
    background-color: #6b7280;
    border: 2px solid #374151;
  }

  body::-webkit-scrollbar-thumb:hover {
    background-color: #6b7280;
  }

  .dark body::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
  }
}

/* Animation and Transition Classes */
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Hover Effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Explicit hover states for testing */
.hoverable:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

.navigation-link:hover {
  color: rgb(59, 130, 246);
  transform: scale(1.05);
}

.cta-button:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Micro-interactions */
.interactive {
  transition: all 0.2s ease-in-out;
}

.interactive:hover {
  transform: translateY(-1px);
}

.interactive:active {
  transform: translateY(0);
}

/* Button hover effects */
.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Card hover effects */
.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Focus states */
.focus-ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideInFromTop {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

@keyframes slideInFromBottom {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes pulse-once {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.3s ease-out;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-pulse-once {
  animation: pulse-once 1.5s ease-in-out;
}

/* Text truncation utilities */
.truncate-two-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Custom Scrollbar Styles - Native-like appearance */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #9ca3af #f3f4f6;
}

.dark .chat-scrollbar {
  scrollbar-color: #6b7280 #374151;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 6px;
}

.dark .chat-scrollbar::-webkit-scrollbar-track {
  background: #374151;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: #9ca3af;
  border-radius: 6px;
  border: 2px solid #f3f4f6;
}

.dark .chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: #6b7280;
  border: 2px solid #374151;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

.dark .chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition,
  .transition-all,
  .transition-colors,
  .transition-transform,
  .transition-opacity {
    transition-duration: 0.01ms !important;
  }

  .animate-fade-in,
  .animate-slide-in-right,
  .animate-slide-in-left,
  .animate-slide-in-top,
  .animate-slide-in-bottom,
  .animate-slide-up,
  .animate-pulse,
  .animate-bounce {
    animation: none !important;
  }

  .hover-lift:hover,
  .hover-scale:hover,
  .interactive:hover,
  .btn-hover:hover,
  .card-hover:hover {
    transform: none !important;
  }
}

/* ========================================
   CHAT PAGE SPECIFIC FIXES
   ======================================== */

/* Prevent scrolling on chat page */
.chat-page-body {
  overflow: hidden !important;
  height: 100vh !important;
}

/* Mobile Safari viewport fix */
@supports (-webkit-touch-callout: none) {
  .chat-page-body {
    height: -webkit-fill-available !important;
  }

  /* Fix for chat layout containers */
  .h-screen {
    height: -webkit-fill-available !important;
  }

  /* Ensure chat input area is always visible */
  .fixed.inset-0 {
    height: -webkit-fill-available !important;
  }
}

/* Additional mobile viewport units for better support */
.h-screen {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100); /* Fallback for mobile Safari */
  height: 100dvh; /* Dynamic viewport height */
}

/* Mobile-specific chat layout fixes */
@media (max-width: 768px) {
  /* Ensure chat container uses safe area */
  .chat-container {
    padding-bottom: env(safe-area-inset-bottom, 0px);
    min-height: -webkit-fill-available;
  }

  /* Fix for input area on mobile Safari */
  .chat-input-container {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 1rem);
    /* Ensure input stays above Safari bottom bar */
    margin-bottom: env(keyboard-inset-height, 0px);
  }

  /* Additional Safari-specific fixes */
  @supports (-webkit-touch-callout: none) {
    .chat-input-container {
      padding-bottom: calc(env(safe-area-inset-bottom, 20px) + 1rem);
    }
  }
}

/* ========================================
   CRITICAL UI FIXES - Button Touch Targets & Visibility
   ======================================== */

/* Fix button cursor and touch targets globally */
button:not([data-radix-collection-item]):not([role="switch"]),
.btn,
[role="button"]:not([role="switch"]),
input[type="submit"],
input[type="button"],
a:not([data-radix-collection-item]),
[tabindex]:not([data-radix-collection-item]):not([role="switch"]),
[onclick]:not([data-radix-collection-item]):not([role="switch"]) {
  cursor: pointer !important;
  min-height: 44px !important;
  min-width: 44px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Exception for switch components - preserve their intended dimensions */
[role="switch"],
[data-radix-collection-item] {
  min-height: unset !important;
  min-width: unset !important;
  display: unset !important;
}

/* Ensure all buttons are visible - Light mode */
button:not([class*="bg-"]):not([style*="background"]),
[role="button"]:not([class*="bg-"]):not([style*="background"]) {
  background-color: #f3f4f6 !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

button:empty,
[role="button"]:empty {
  min-height: 44px !important;
  min-width: 44px !important;
  background-color: #e5e7eb !important;
  border: 1px solid #d1d5db !important;
}

/* Dark mode button visibility */
.dark button:not([class*="bg-"]):not([style*="background"]),
.dark [role="button"]:not([class*="bg-"]):not([style*="background"]) {
  background-color: #374151 !important;
  border: 1px solid #6b7280 !important;
  color: #f9fafb !important;
}

.dark button:empty,
.dark [role="button"]:empty {
  background-color: #4b5563 !important;
  border: 1px solid #6b7280 !important;
  color: #f9fafb !important;
}

/* Ensure icon-only buttons are properly sized and visible */
button:has(svg):not(:has(span:not(.sr-only))),
[role="button"]:has(svg):not(:has(span:not(.sr-only))) {
  min-height: 44px !important;
  min-width: 44px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Force all buttons to have minimum touch targets */
button,
[role="button"] {
  min-height: 44px !important;
  min-width: 44px !important;
}

/* Ensure CTA buttons are prominent and accessible */
.cta-button {
  cursor: pointer !important;
  min-height: 48px !important;
  padding: 14px 24px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
}

/* Fix small icon buttons and navigation elements */
button[aria-label],
.icon-button,
.help-widget,
.theme-toggle {
  min-height: 44px !important;
  min-width: 44px !important;
  padding: 10px !important;
}

/* Improve button hover states */
.btn-hover {
  cursor: pointer !important;
  min-height: 44px !important;
  transition: all 0.2s ease-in-out;
}

/* Fix navigation buttons */
.navigation-link {
  min-height: 44px !important;
  padding: 10px 16px !important;
  display: inline-flex !important;
  align-items: center !important;
}

/* Mobile-specific button improvements */
@media (max-width: 768px) {
  button:not([data-radix-collection-item]):not([role="switch"]),
  .btn,
  [role="button"]:not([role="switch"]),
  input[type="submit"],
  input[type="button"],
  a:not([data-radix-collection-item]),
  [tabindex]:not([data-radix-collection-item]):not([role="switch"]),
  [onclick]:not([data-radix-collection-item]):not([role="switch"]) {
    min-height: 48px !important;
    min-width: 48px !important;
    padding: 14px 20px !important;
    font-size: 16px !important;
  }

  .cta-button {
    min-height: 52px !important;
    padding: 16px 28px !important;
    font-size: 18px !important;
  }

  /* Ensure navigation links are touch-friendly */
  nav a,
  .navigation-link {
    min-height: 48px !important;
    padding: 12px 16px !important;
  }

  /* Help widget and small buttons */
  .help-widget button,
  .icon-button {
    min-height: 48px !important;
    min-width: 48px !important;
  }

  /* Fix any empty or invisible buttons */
  button:empty,
  [role="button"]:empty {
    min-height: 48px !important;
    min-width: 48px !important;
    background-color: #e5e7eb !important;
    border: 1px solid #d1d5db !important;
  }

  /* Ensure all clickable elements have proper touch targets */
  * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}

/* ========================================
   SEARCH RESULTS UI FIXES
   ======================================== */

/* Fix hover states to prevent white-out issues */
.hover-safe:hover {
  background-color: rgba(var(--muted), 0.3) !important;
  border-color: rgba(var(--primary), 0.3) !important;
}

/* Ensure confidence scores are always visible */
.confidence-score {
  background-color: rgba(var(--muted), 0.5) !important;
  border: 1px solid rgba(var(--border), 0.5) !important;
  color: rgb(var(--card-foreground)) !important;
  font-weight: 700 !important;
  padding: 0.5rem 1rem !important;
  border-radius: 9999px !important;
}

/* Improve research quality metrics visibility */
.research-metric {
  background-color: rgb(var(--card)) !important;
  border: 1px solid rgb(var(--border)) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* Better section title contrast */
.section-title {
  color: rgb(var(--card-foreground)) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
}

/* Improve "View Official" button visibility - prevent white background */
.view-official-btn,
button[class*="bg-muted/20"] {
  background-color: rgba(var(--muted), 0.2) !important;
  border: 1px solid rgb(var(--border)) !important;
  color: rgb(var(--card-foreground)) !important;
  font-weight: 600 !important;
  transition: all 0.2s ease-in-out !important;
}

.view-official-btn:hover,
button[class*="bg-muted/20"]:hover {
  background-color: rgba(var(--muted), 0.4) !important;
  border-color: rgba(var(--primary), 0.4) !important;
  transform: none !important;
}

/* Sources panel button improvements */
.sources-panel button {
  transition: all 0.2s ease-in-out;
}

.sources-panel button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Fix collapsible panel hover states */
.collapsible-header:hover {
  background-color: rgba(var(--muted), 0.5) !important;
}

/* Dark mode specific improvements */
.dark .confidence-score {
  background-color: rgba(var(--muted), 0.8) !important;
  border-color: rgba(var(--border), 0.8) !important;
}

.dark .research-metric {
  background-color: rgb(var(--card)) !important;
  border-color: rgb(var(--border)) !important;
}

.dark .section-title {
  color: rgb(var(--foreground)) !important;
}

/* Theme Toggle Visibility Fix */
.dark button[aria-label*="mode"],
.dark .theme-toggle {
  background-color: rgb(51 65 85) !important; /* slate-700 */
  border: 1px solid rgb(71 85 105) !important; /* slate-600 */
  color: rgb(203 213 225) !important; /* slate-300 */
}

.dark button[aria-label*="mode"]:hover,
.dark .theme-toggle:hover {
  background-color: rgb(71 85 105) !important; /* slate-600 */
  color: rgb(248 250 252) !important; /* slate-50 */
}

/* Ensure buttons never white out */
button:disabled {
  background-color: rgb(var(--muted)) !important;
  color: rgb(var(--muted-foreground)) !important;
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* Reduced motion utility class */
.reduce-motion {
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

/* ========================================
   ENHANCED ORDRLY-API STYLING
   ======================================== */

/* Futuristic Typography */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.font-exo {
  font-family: 'Exo 2', sans-serif;
}

.font-space {
  font-family: 'Space Grotesk', sans-serif;
}

/* Custom Animations */
@keyframes neural-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes quantum-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--primary-500), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--primary-500), 0.6), 0 0 30px rgba(var(--primary-500), 0.4);
  }
}

@keyframes quantum-glow-light {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--primary-500), 0.2);
  }
  50% {
    box-shadow: 0 0 15px rgba(var(--primary-500), 0.4), 0 0 25px rgba(var(--primary-500), 0.3);
  }
}

.neural-pulse {
  animation: neural-pulse 2s ease-in-out infinite;
}

.quantum-glow {
  animation: quantum-glow 3s ease-in-out infinite;
}

.light .quantum-glow {
  animation: quantum-glow-light 3s ease-in-out infinite;
}

/* Glass Morphism Effects - Light/Dark Mode Compatible */
.glass-morphism {
  backdrop-filter: blur(12px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Dark mode glass morphism */
.dark .glass-morphism {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Light mode glass morphism */
.light .glass-morphism {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Subtle Grid Pattern - Light/Dark Mode Compatible */
.grid-pattern {
  background-size: 20px 20px;
}

/* Dark mode grid pattern */
.dark .grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
}

/* Light mode grid pattern */
.light .grid-pattern {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
}

/* Advanced Hover Effects - Light/Dark Mode Compatible */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode hover effects */
.dark .hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Light mode hover effects */
.light .hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}
