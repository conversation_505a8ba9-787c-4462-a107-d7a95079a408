import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default async function HistoricalDataPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has access to historical data
  const hasAccess = profile.subscription_tier === 'appraiser'

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Historical Data Access
            </h1>
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Appraiser Access Required
              </h2>
              <p className="text-gray-600 mb-6">
                Historical data access is available for Appraiser subscribers only.
              </p>
              <div className="space-y-4">
                <Link href="/pricing">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Upgrade to Appraiser
                  </Button>
                </Link>
                <div>
                  <Link href="/search">
                    <Button variant="outline">
                      Back to Search
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Historical Data & Timeline Analysis
          </h1>
          <p className="text-gray-600 mb-8">
            Access comprehensive historical property data, past sales records, and timeline analysis for professional appraisals.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Data Search */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Search Historical Data</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Address
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="Enter property address..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Range
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="date"
                      className="border border-gray-300 rounded-md px-3 py-2"
                    />
                    <input
                      type="date"
                      className="border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Data Type
                  </label>
                  <select className="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option>All Data Types</option>
                    <option>Sales History</option>
                    <option>Tax Records</option>
                    <option>Permit History</option>
                    <option>Ownership Changes</option>
                    <option>Zoning Changes</option>
                  </select>
                </div>

                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  Search Historical Data
                </Button>
              </div>
            </div>

            {/* Available Data Types */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Available Data</h2>

              <div className="bg-gray-50 p-6 rounded-lg">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Sales History</span>
                    <span className="text-sm text-green-600">Available</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Tax Assessment Records</span>
                    <span className="text-sm text-green-600">Available</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Building Permits</span>
                    <span className="text-sm text-green-600">Available</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Ownership History</span>
                    <span className="text-sm text-green-600">Available</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Zoning Changes</span>
                    <span className="text-sm text-green-600">Available</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Market Trends</span>
                    <span className="text-sm text-green-600">Available</span>
                  </div>
                </div>
              </div>

              <div className="text-sm text-gray-600">
                <p className="mb-2">Data Coverage:</p>
                <ul className="space-y-1 ml-4">
                  <li>• Records dating back to 1990</li>
                  <li>• Updated monthly</li>
                  <li>• Nationwide coverage</li>
                  <li>• Multiple data sources</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Recent Searches */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Recent Historical Searches
            </h2>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="text-center text-gray-500">
                <p>No recent searches found.</p>
                <p className="text-sm mt-1">Your historical data searches will appear here.</p>
              </div>
            </div>
          </div>

          {/* Export Options */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Export Options
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="border border-gray-200 p-4 rounded-lg text-center">
                <h3 className="font-semibold text-gray-900 mb-2">CSV Export</h3>
                <p className="text-sm text-gray-600 mb-3">Export data in spreadsheet format</p>
                <Button variant="outline" className="w-full">Export CSV</Button>
              </div>
              <div className="border border-gray-200 p-4 rounded-lg text-center">
                <h3 className="font-semibold text-gray-900 mb-2">PDF Report</h3>
                <p className="text-sm text-gray-600 mb-3">Generate formatted PDF reports</p>
                <Button variant="outline" className="w-full">Export PDF</Button>
              </div>
              <div className="border border-gray-200 p-4 rounded-lg text-center">
                <h3 className="font-semibold text-gray-900 mb-2">API Access</h3>
                <p className="text-sm text-gray-600 mb-3">Programmatic data access</p>
                <Button variant="outline" className="w-full">View API Docs</Button>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <Link href="/reports">
              <Button variant="outline">
                Professional Reports
              </Button>
            </Link>
            <Link href="/api-access">
              <Button className="bg-green-600 hover:bg-green-700">
                API Access
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
