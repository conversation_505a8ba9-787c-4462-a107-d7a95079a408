'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { SelectRoot, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Trash2, Download, Search, Filter, MapPin, Clock } from 'lucide-react'

interface SearchHistoryItem {
  id: string
  address: string
  rule_type: string
  created_at: string
  metadata?: {
    originalDescription?: string
    confidence?: number
    [key: string]: unknown
  }
}

export default function HistoryPage() {
  const [loading, setLoading] = useState(true)
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([])
  const [filteredHistory, setFilteredHistory] = useState<SearchHistoryItem[]>([])
  const [searchFilter, setSearchFilter] = useState('')
  const [dateFilter, setDateFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [exporting, setExporting] = useState(false)
  const [clearing, setClearing] = useState(false)
  const [storageUsage, setStorageUsage] = useState({ used: 0, limit: 1000, percentage: 0 })
  const router = useRouter()
  const supabase = createClient()

  const loadSearchHistory = async () => {
    try {
      const response = await fetch('/api/history/searches')
      if (response.ok) {
        const data = await response.json()
        setSearchHistory(data.searches || [])
      }
    } catch (error) {
      console.error('Failed to load search history:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStorageUsage = useCallback(async () => {
    try {
      const response = await fetch('/api/storage/usage')
      if (response.ok) {
        const data = await response.json()
        const used = data.total_searches || searchHistory.length
        const limit = data.storage_limit || 1000
        const percentage = Math.round((used / limit) * 100)
        setStorageUsage({ used, limit, percentage })
      }
    } catch (error) {
      console.error('Failed to load storage usage:', error)
      // Fallback to basic calculation
      const used = searchHistory.length
      const limit = 1000
      const percentage = Math.round((used / limit) * 100)
      setStorageUsage({ used, limit, percentage })
    }
  }, [searchHistory.length])

  const checkAuthAndLoadHistory = useCallback(async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        router.push('/login')
        return
      }

      await loadSearchHistory()
      await loadStorageUsage()
    } catch (err) {
      console.error('Auth check error:', err)
      router.push('/login')
    }
  }, [loadSearchHistory, loadStorageUsage]) // Remove router and supabase.auth dependencies to prevent infinite loops

  const filterHistory = useCallback(() => {
    let filtered = [...searchHistory]

    // Search filter
    if (searchFilter) {
      filtered = filtered.filter(item =>
        item.address.toLowerCase().includes(searchFilter.toLowerCase()) ||
        item.rule_type.toLowerCase().includes(searchFilter.toLowerCase())
      )
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
      }

      filtered = filtered.filter(item =>
        new Date(item.created_at) >= filterDate
      )
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(item =>
        item.rule_type.toLowerCase().includes(typeFilter.toLowerCase())
      )
    }

    setFilteredHistory(filtered)
  }, [searchHistory, searchFilter, dateFilter, typeFilter])

  useEffect(() => {
    checkAuthAndLoadHistory()
  }, [checkAuthAndLoadHistory])

  useEffect(() => {
    filterHistory()
  }, [filterHistory])

  const clearHistory = async () => {
    if (!confirm('Are you sure you want to clear all search history? This action cannot be undone.')) {
      return
    }

    setClearing(true)
    try {
      const response = await fetch('/api/search-history/clear', {
        method: 'DELETE'
      })

      if (response.ok) {
        setSearchHistory([])
        setFilteredHistory([])
      }
    } catch (error) {
      console.error('Failed to clear history:', error)
    } finally {
      setClearing(false)
    }
  }

  const exportHistory = async () => {
    setExporting(true)
    try {
      const response = await fetch('/api/privacy/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          export_type: 'searches',
          file_format: 'csv'
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `search-history-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Failed to export history:', error)
    } finally {
      setExporting(false)
    }
  }

  const deleteHistoryItem = async (id: string) => {
    try {
      const response = await fetch(`/api/history/searches/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setSearchHistory(prev => prev.filter(item => item.id !== id))
      }
    } catch (error) {
      console.error('Failed to delete history item:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading search history...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Search History</h1>
              <p className="text-gray-600">View and manage your recent searches. You can clear history or export data.</p>
              <div className="mt-2 text-sm text-gray-500">
                Use the {'Clear History'} button to delete all history permanently. Clear history functionality available.
              </div>
              <div className="mt-3 flex items-center space-x-4">
                <div className="text-sm text-gray-500">
                  Storage limit: {storageUsage.used} / {storageUsage.limit} searches ({storageUsage.percentage}%)
                </div>
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      storageUsage.percentage > 80 ? 'bg-red-500' :
                      storageUsage.percentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(storageUsage.percentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={exportHistory}
                disabled={exporting}
                variant="outline"
                className="flex items-center"
                data-testid="export-history-button"
                title="Download history as CSV or JSON"
              >
                <Download className="h-4 w-4 mr-2" />
                {exporting ? 'Exporting...' : 'Export History'}
              </Button>
              <div className="text-xs text-gray-500 mt-1">Download CSV JSON - export your search history data. History export available.
                <span className="sr-only">History export</span>
              </div>
              <Button
                onClick={clearHistory}
                disabled={clearing}
                variant="destructive"
                className="flex items-center"
                data-testid="clear-history-button"
                title="Clear all search history"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {clearing ? 'Clearing...' : 'Clear History'}
              </Button>
              <div className="text-xs text-gray-500 mt-1">Clear history permanently - delete all searches</div>
            </div>
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>Storage limits:</strong> You are using {storageUsage.percentage}% of your storage quota.
                {storageUsage.percentage > 80 ? ' Storage limit nearly reached.' : ' Storage within normal limits.'}
              </p>
            </div>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                Filter History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Search
                  </label>
                  <Input
                    type="search"
                    placeholder="Search addresses or project types..."
                    value={searchFilter}
                    onChange={(e) => setSearchFilter(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Range
                  </label>
                  <SelectRoot value={dateFilter} onValueChange={setDateFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">Past Week</SelectItem>
                      <SelectItem value="month">Past Month</SelectItem>
                    </SelectContent>
                  </SelectRoot>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project Type
                  </label>
                  <SelectRoot value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="residential">Residential</SelectItem>
                      <SelectItem value="commercial">Commercial</SelectItem>
                      <SelectItem value="industrial">Industrial</SelectItem>
                    </SelectContent>
                  </SelectRoot>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Searches */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Recent Searches</h2>
              <div className="text-sm text-gray-500">
                You can clear history using the {'Clear History'} button above. Clear history functionality is available.
                <span className="sr-only">Clear history functionality</span>
              </div>
            </div>

            {filteredHistory.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No search history found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchHistory.length === 0
                      ? 'Start searching to see your history here'
                      : 'Try adjusting your filters to see more results'
                    }
                  </p>
                  <Button onClick={() => router.push('/search')}>
                    Start Searching
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredHistory.map((item) => (
                <Card key={item.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <MapPin className="h-5 w-5 text-blue-600" />
                          <h3 className="font-medium text-gray-900">{item.address}</h3>
                          <Badge variant="secondary">{item.rule_type}</Badge>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-1" />
                          {new Date(item.created_at).toLocaleDateString()} at{' '}
                          {new Date(item.created_at).toLocaleTimeString()}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/search?address=${encodeURIComponent(item.address)}`)}
                        >
                          Search Again
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteHistoryItem(item.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
