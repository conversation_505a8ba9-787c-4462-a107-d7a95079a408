'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ChevronLeft, ThumbsUp, ThumbsDown, Star, Clock, Tag, Share2, Bookmark } from 'lucide-react'

export default function GettingStartedArticle() {
  const [rating, setRating] = useState<number | null>(null)
  const [helpful, setHelpful] = useState<boolean | null>(null)
  const [feedback, setFeedback] = useState('')
  const [showFeedback, setShowFeedback] = useState(false)

  // Mock article data - in real implementation, this would be fetched from API
  const article = {
    id: 'getting-started-guide',
    title: 'Getting Started with Ordrly',
    content: `Welcome to Ordrly! This comprehensive guide will help you get started with our property compliance platform and make the most of our features.

## Step 1: Enter Your Address

Start by typing any U.S. address in the search field. Our system provides autocomplete suggestions to help you find the exact location.

### Tips for Better Results:
- Type slowly for better autocomplete results
- Include city and state for accuracy
- Select from the dropdown suggestions rather than typing the full address

## Step 2: Review Compliance Results

Once you submit an address, our AI analyzes local ordinances and provides a comprehensive compliance report.

### Understanding Your Results:
- **Confidence Score**: Indicates how certain our AI is about the requirements
- **Requirements List**: Specific rules and regulations for your project
- **Citations**: References to the actual ordinance text

## Step 3: Export or Share

Export your compliance data as CSV files for analysis, or upgrade for advanced features.

### What You Can Do:
- Export compliance data as CSV files
- Share results via email or link
- Upgrade to Pro for unlimited searches
- Access AI chat for detailed questions

## Getting Better Results

To get the most accurate compliance information:

1. **Be Specific**: Include as much detail as possible about your project
2. **Check Multiple Sources**: Use our results as a starting point, not the final word
3. **Consult Professionals**: For complex projects, always consult with local professionals
4. **Stay Updated**: Ordinances can change, so check for updates regularly

## Need More Help?

If you're still having trouble or have specific questions:
- Browse our [Knowledge Base](/knowledge-base) for more articles
- Watch our [Video Tutorials](/tutorials) for step-by-step guidance
- Try our [Interactive Demo](/demo) to practice
- Contact our [Support Team](/contact) for personalized assistance`,
    category: 'getting-started',
    tags: ['tutorial', 'basics', 'address', 'compliance'],
    created_at: '2024-01-15T10:00:00Z',
    author: 'Ordrly Team'
  }

  const relatedArticles = [
    {
      title: 'Understanding Confidence Scores',
      excerpt: 'Learn what confidence scores mean and how to interpret them',
      category: 'understanding-results',
      link: '/knowledge-base/confidence-scores'
    },
    {
      title: 'Supported Project Types',
      excerpt: 'Complete list of project types we can analyze',
      category: 'project-types',
      link: '/knowledge-base/project-types'
    },
    {
      title: 'How to Rate Articles',
      excerpt: 'Help us improve by rating and providing feedback',
      category: 'using-ordrly',
      link: '/knowledge-base/rating-articles'
    }
  ]

  const submitRating = async () => {
    try {
      const response = await fetch('/api/article-ratings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          article_id: article.id,
          rating,
          helpful,
          feedback: feedback.trim() || null
        })
      })

      if (response.ok) {
        setShowFeedback(false)
        // Show success message
      }
    } catch (err) {
      console.error('Failed to submit rating:', err)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const renderStars = () => {
    return Array.from({ length: 5 }, (_, i) => (
      <button
        key={i}
        onClick={() => setRating(i + 1)}
        className={`h-6 w-6 ${
          rating && i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        } hover:text-yellow-400 transition-colors`}
      >
        <Star className="h-full w-full" />
      </button>
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link
              href="/knowledge-base"
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              Back to Knowledge Base
            </Link>
            <div className="flex items-center space-x-4">
              <button className="flex items-center text-gray-600 hover:text-gray-900">
                <Share2 className="h-5 w-5 mr-1" />
                Share
              </button>
              <button className="flex items-center text-gray-600 hover:text-gray-900">
                <Bookmark className="h-5 w-5 mr-1" />
                Save
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid gap-8 lg:grid-cols-4">
          {/* Article Content */}
          <div className="lg:col-span-3">
            <article className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Article Header */}
              <div className="p-8 border-b border-gray-200">
                <div className="flex items-center space-x-2 mb-4">
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded">
                    {article.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1" />
                    {formatDate(article.created_at)}
                  </div>
                </div>

                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  {article.title}
                </h1>

                <div className="flex items-center justify-between">
                  <p className="text-gray-600">
                    By {article.author}
                  </p>

                  {/* Tags */}
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 text-gray-400 mr-2" />
                    <div className="flex space-x-1">
                      {article.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Article Body */}
              <div className="p-8">
                <div className="prose max-w-none">
                  {article.content.split('\n\n').map((paragraph, index) => {
                    if (paragraph.startsWith('## ')) {
                      return (
                        <h2 key={index} className="text-xl font-bold text-gray-900 mt-8 mb-4">
                          {paragraph.replace('## ', '')}
                        </h2>
                      )
                    } else if (paragraph.startsWith('### ')) {
                      return (
                        <h3 key={index} className="text-lg font-semibold text-gray-900 mt-6 mb-3">
                          {paragraph.replace('### ', '')}
                        </h3>
                      )
                    } else if (paragraph.startsWith('- ')) {
                      const items = paragraph.split('\n').filter(line => line.startsWith('- '))
                      return (
                        <ul key={index} className="list-disc list-inside space-y-1 mb-4">
                          {items.map((item, i) => (
                            <li key={i} className="text-gray-700">
                              {item.replace('- ', '')}
                            </li>
                          ))}
                        </ul>
                      )
                    } else if (paragraph.match(/^\d+\./)) {
                      const items = paragraph.split('\n').filter(line => line.match(/^\d+\./))
                      return (
                        <ol key={index} className="list-decimal list-inside space-y-1 mb-4">
                          {items.map((item, i) => (
                            <li key={i} className="text-gray-700">
                              {item.replace(/^\d+\.\s*/, '')}
                            </li>
                          ))}
                        </ol>
                      )
                    } else {
                      return (
                        <p key={index} className="text-gray-700 mb-4 leading-relaxed">
                          {paragraph.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-700">$1</a>')}
                        </p>
                      )
                    }
                  })}
                </div>
              </div>

              {/* Rating Section */}
              <div className="p-8 border-t border-gray-200 bg-gray-50">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Was this article helpful?
                </h3>

                {/* Star Rating */}
                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">Rate this article:</p>
                  <div className="flex items-center space-x-1">
                    {renderStars()}
                  </div>
                </div>

                {/* Helpful/Not Helpful */}
                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">Did you find this helpful?</p>
                  <div className="flex space-x-4">
                    <button
                      onClick={() => setHelpful(true)}
                      className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                        helpful === true
                          ? 'bg-green-100 text-green-800 border border-green-200'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <ThumbsUp className="h-4 w-4 mr-2" />
                      Yes, helpful
                    </button>
                    <button
                      onClick={() => {
                        setHelpful(false)
                        setShowFeedback(true)
                      }}
                      className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                        helpful === false
                          ? 'bg-red-100 text-red-800 border border-red-200'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <ThumbsDown className="h-4 w-4 mr-2" />
                      Not helpful
                    </button>
                  </div>
                </div>

                {/* Feedback Form */}
                {(showFeedback || helpful === false) && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      How can we improve this article?
                    </label>
                    <textarea
                      value={feedback}
                      onChange={(e) => setFeedback(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Your feedback helps us improve..."
                    />
                  </div>
                )}

                {/* Submit Button */}
                {(rating || helpful !== null || feedback.trim()) && (
                  <button
                    onClick={submitRating}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Submit Feedback
                  </button>
                )}
              </div>
            </article>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h3 className="font-semibold text-gray-900 mb-4">Related Articles & Similar Topics</h3>
              <div className="space-y-4">
                {relatedArticles.map((relatedArticle, index) => (
                  <Link
                    key={index}
                    href={relatedArticle.link}
                    className="block p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
                  >
                    <h4 className="font-medium text-gray-900 text-sm mb-1">
                      {relatedArticle.title}
                    </h4>
                    <p className="text-xs text-gray-600 mb-2">
                      {relatedArticle.excerpt}
                    </p>
                    <span className="text-xs text-blue-600">
                      {relatedArticle.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </Link>
                ))}
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3">You might also like</h4>
                <div className="space-y-2">
                  <Link href="/tutorials" className="block text-sm text-blue-600 hover:text-blue-700">
                    → Video Tutorials
                  </Link>
                  <Link href="/demo" className="block text-sm text-blue-600 hover:text-blue-700">
                    → Interactive Demo
                  </Link>
                  <Link href="/faq" className="block text-sm text-blue-600 hover:text-blue-700">
                    → Help Center
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
