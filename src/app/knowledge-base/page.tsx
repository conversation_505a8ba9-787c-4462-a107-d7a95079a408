'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { Search, Filter, ThumbsUp, Star, Clock, Tag, ChevronRight } from 'lucide-react'

interface Article {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  created_at: string
  sort_order: number
}

interface ArticleRating {
  totalRatings: number
  averageRating: number
  helpfulCount: number
  notHelpfulCount: number
  helpfulPercentage: number
}

export default function KnowledgeBasePage() {
  const [articles, setArticles] = useState<Article[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [ratings, setRatings] = useState<Record<string, ArticleRating>>({})

  const fetchArticles = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (searchQuery) params.append('search', searchQuery)
      if (selectedCategory !== 'all') params.append('category', selectedCategory)

      const response = await fetch(`/api/knowledge-base?${params}`)
      const data = await response.json()

      if (data.success) {
        setArticles(data.articles)
        setCategories(['all', ...data.categories])

        // Fetch ratings for each article
        for (const article of data.articles) {
          fetchArticleRating(article.id)
        }
      } else {
        setError(data.error || 'Failed to load articles')
      }
    } catch {
      setError('Failed to load articles')
    } finally {
      setLoading(false)
    }
  }, [searchQuery, selectedCategory])

  useEffect(() => {
    fetchArticles()
  }, [fetchArticles])

  const fetchArticleRating = async (articleId: string) => {
    try {
      const response = await fetch(`/api/article-ratings?article_id=${articleId}`)
      const data = await response.json()

      if (data.success) {
        setRatings(prev => ({
          ...prev,
          [articleId]: data.statistics
        }))
      }
    } catch {
      console.error('Failed to fetch rating for article:', articleId)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchArticles()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getExcerpt = (content: string, maxLength: number = 150) => {
    const plainText = content.replace(/[#*`]/g, '').replace(/\n/g, ' ')
    return plainText.length > maxLength
      ? plainText.substring(0, maxLength) + '...'
      : plainText
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.round(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  if (loading && articles.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading knowledge base...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Knowledge Base
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              Browse our comprehensive collection of articles, guides, and documentation
              organized by categories and topics to learn everything about property compliance.
            </p>

            {/* Search and Filter */}
            <div className="max-w-4xl mx-auto">
              <form onSubmit={handleSearch} className="mb-6">
                <div className="relative">
                  <Search className="absolute left-4 top-4 h-5 w-5 text-gray-400" />
                  <input
                    type="search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search articles, topics, or categories..."
                    className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </form>

              {/* Category Filter */}
              <div className="flex items-center justify-center space-x-2 flex-wrap">
                <Filter className="h-5 w-5 text-gray-400" />
                <span className="text-sm text-gray-600 mr-2">Categories:</span>
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {category === 'all' ? 'All Topics' : category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {error ? (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={fetchArticles}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        ) : articles.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 mb-4">
              {searchQuery || selectedCategory !== 'all'
                ? 'No articles found matching your criteria.'
                : 'No articles available.'}
            </p>
            {(searchQuery || selectedCategory !== 'all') && (
              <button
                onClick={() => {
                  setSearchQuery('')
                  setSelectedCategory('all')
                }}
                className="text-blue-600 hover:text-blue-700"
              >
                Clear filters
              </button>
            )}
          </div>
        ) : (
          <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
            {articles.map((article) => (
              <div
                key={article.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
              >
                <div className="p-6">
                  {/* Article Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                        {article.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <div className="flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(article.created_at)}
                      </div>
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2">
                    {article.title}
                  </h3>

                  {/* Excerpt */}
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {getExcerpt(article.content)}
                  </p>

                  {/* Tags */}
                  {article.tags && article.tags.length > 0 && (
                    <div className="flex items-center mb-4">
                      <Tag className="h-4 w-4 text-gray-400 mr-2" />
                      <div className="flex flex-wrap gap-1">
                        {article.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                          >
                            {tag}
                          </span>
                        ))}
                        {article.tags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{article.tags.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Rating */}
                  {ratings[article.id] && (
                    <div className="flex items-center justify-between mb-4 text-sm">
                      <div className="flex items-center">
                        <div className="flex items-center mr-2">
                          {renderStars(ratings[article.id].averageRating)}
                        </div>
                        <span className="text-gray-600">
                          ({ratings[article.id].totalRatings} ratings)
                        </span>
                      </div>
                      <div className="flex items-center text-gray-500">
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        <span>{ratings[article.id].helpfulPercentage}% helpful</span>
                      </div>
                    </div>
                  )}

                  {/* Read More Link */}
                  <Link
                    href={`/knowledge-base/${article.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Read full article
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Related Resources */}
        <div className="mt-16 bg-white rounded-lg shadow-md p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Explore More Resources
            </h2>
            <p className="text-gray-600">
              Can&apos;t find what you&apos;re looking for? Try these other helpful resources.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Link
              href="/tutorials"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <div className="p-2 bg-blue-100 rounded-lg mr-4">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Video Tutorials</h3>
                <p className="text-sm text-gray-600">Step-by-step video guides</p>
              </div>
            </Link>

            <Link
              href="/faq"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <div className="p-2 bg-blue-100 rounded-lg mr-4">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Help Center</h3>
                <p className="text-sm text-gray-600">Support and FAQ</p>
              </div>
            </Link>

            <Link
              href="/demo"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <div className="p-2 bg-blue-100 rounded-lg mr-4">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Interactive Demo</h3>
                <p className="text-sm text-gray-600">Try it in sandbox mode</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
