import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ConditionalLayout } from "@/components/layout/ConditionalLayout";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AccessibilityWrapper } from "@/components/ui/AccessibilityWrapper";
import { KeyboardShortcutsProvider } from "@/components/ui/KeyboardShortcutsProvider";
import { ToastProvider } from "@/components/ui/toast";
import { NetworkErrorHandler } from "@/components/error/NetworkErrorHandler";
import { Toaster } from "sonner";
import { ErrorBoundary } from "@/components/error-boundary/ErrorBoundary";
import { AnalyticsProvider } from "@/components/analytics/Analytics";
import { Suspense } from "react";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Ordrly - The Fastest Way to Research Municipal Ordinances for Appraisers & Real Estate Pros",
    template: "%s | Ordrly"
  },
  description: "Stop digging through city websites and 500-page PDFs. Get instant, sourced answers from municipal codes so you can complete your work faster and with more confidence. Save hours on every report.",
  keywords: ["appraiser research tool", "real estate compliance", "municipal research", "building codes", "zoning laws", "property compliance", "appraisal research", "real estate professional tools"],
  authors: [{ name: "Ordrly Team" }],
  creator: "Ordrly",
  publisher: "Ordrly",
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://ordrly.ai'),
  alternates: {
    canonical: '/',
  },
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://ordrly.ai',
    siteName: 'Ordrly',
    title: "Ordrly - Know Before You Build",
    description: "Check local property compliance rules quickly. Get clear answers about permits, setbacks, and regulations before you start your project.",
    images: [
      {
        url: "/images/og-image.png",
        width: 1200,
        height: 630,
        alt: "Ordrly - Property Compliance Made Simple",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@ordrlyAI",
    creator: "@ordrlyAI",
    title: "Ordrly - Know Before You Build",
    description: "Check local property compliance rules quickly. Get clear answers about permits, setbacks, and regulations before you start your project.",
    images: ["/images/twitter-card.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
  icons: {
    icon: [
      { url: '/logo.svg', type: 'image/svg+xml' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#1DA1F2' },
    ],
  },
  manifest: '/manifest.json',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0a0a0a' }
  ]
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Ordrly",
    "description": "Check local property compliance rules quickly. Get clear answers about permits, setbacks, and regulations before you start your project.",
    "url": "https://ordrly.ai",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "browserRequirements": "Requires JavaScript. Requires HTML5.",
    "softwareVersion": "1.0",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "creator": {
      "@type": "Organization",
      "name": "Ordrly",
      "url": "https://ordrly.ai",
      "sameAs": [
        "https://twitter.com/ordrlyAI"
      ]
    },
    "featureList": [
      "Property compliance checking",
      "Building permit information",
      "Zoning regulation lookup",
      "Local ordinance search",
      "Real estate due diligence"
    ],
    "screenshot": "https://ordrly.ai/images/og-image.png",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "127"
    }
  }

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} antialiased min-h-screen flex flex-col`}
      >
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
        <AnalyticsProvider>
          <ThemeProvider defaultTheme="system">
            <ToastProvider>
              <KeyboardShortcutsProvider>
                <AccessibilityWrapper skipLink={true}>
                  <ErrorBoundary>
                    <NetworkErrorHandler>
                      <ConditionalLayout>
                        {children}
                      </ConditionalLayout>
                      <Toaster
                        position="top-right"
                        expand={true}
                        richColors={true}
                        closeButton={true}
                      />
                    </NetworkErrorHandler>
                  </ErrorBoundary>
                </AccessibilityWrapper>
              </KeyboardShortcutsProvider>
            </ToastProvider>
          </ThemeProvider>
        </AnalyticsProvider>
      </body>
    </html>
  );
}
