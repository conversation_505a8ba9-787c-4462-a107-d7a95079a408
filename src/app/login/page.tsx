import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert'
import { Mail, Lock, Building2, ArrowRight } from 'lucide-react'
import { login } from '@/lib/auth/actions'
import { OrdrlyLogo } from '@/components/ui/ordrly-logo'

export default async function LoginPage({
  searchParams,
}: {
  searchParams: Promise<{ error?: string; message?: string }>
}) {
  const params = await searchParams

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gradient-to-br from-background via-background/95 to-muted/20 relative overflow-hidden flex items-center justify-center p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 dark:bg-primary/20 rounded-full filter blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary/15 dark:bg-primary/25 rounded-full filter blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-primary/5 dark:bg-primary/10 rounded-full filter blur-3xl animate-pulse delay-500"></div>
      </div>

      <Card className="w-full max-w-md relative z-10 bg-card/50 backdrop-blur-sm border border-border shadow-2xl">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-6">
            <OrdrlyLogo size="xl" className="w-16 h-16" />
          </div>
          <CardTitle className="text-3xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-primary/80 bg-clip-text text-transparent">
            Welcome Back
          </CardTitle>
          <CardDescription className="text-muted-foreground font-space">
            Sign in to your Ordrly account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {params.error && (
            <Alert className="border-red-500/50 bg-red-500/10 dark:bg-red-500/20 backdrop-blur-sm">
              <AlertDescription className="text-red-500 dark:text-red-400 font-space">{params.error}</AlertDescription>
            </Alert>
          )}

          {params.message && (
            <Alert className="border-green-500/50 bg-green-500/10 dark:bg-green-500/20 backdrop-blur-sm">
              <AlertDescription className="text-green-600 dark:text-green-400 font-space">{params.message}</AlertDescription>
            </Alert>
          )}



          {/* Email Form */}
          <form action={login} className="space-y-5">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-foreground font-space">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  className="pl-11 bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary/50 focus:ring-primary/20 backdrop-blur-sm font-space"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-foreground font-space">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your password"
                  className="pl-11 bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary/50 focus:ring-primary/20 backdrop-blur-sm font-space"
                  required
                />
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground border border-primary/20 transition-all duration-300 hover:scale-105 font-exo"
            >
              Sign In
              <ArrowRight className="w-5 h-5 ml-3" />
            </Button>
          </form>

          <div className="text-center space-y-3">
            <Link
              href="/forgot-password"
              className="text-sm text-primary hover:text-primary/80 transition-colors font-space"
            >
              Forgot your password?
            </Link>
            <div className="text-sm text-muted-foreground font-space">
              Don't have an account?{' '}
              <Link
                href="/signup"
                className="text-primary hover:text-primary/80 transition-colors font-medium"
              >
                Sign up
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
