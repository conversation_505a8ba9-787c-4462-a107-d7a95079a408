/**
 * @jest-environment jsdom
 */
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import ManifestoPage from '../page'

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => {
    return <a href={href}>{children}</a>
  }
})

describe('ManifestoPage', () => {

  it('renders the page title and subtitle', () => {
    render(<ManifestoPage />)
    
    expect(screen.getByText('The Verifiable Asset')).toBeInTheDocument()
    expect(screen.getByText(/A Vision for a Transparent, Intelligent, and Liquid Global Real Estate Market/)).toBeInTheDocument()
  })

  it('displays author information', () => {
    render(<ManifestoPage />)

    expect(screen.getByText('By <PERSON> Allen')).toBeInTheDocument()
    expect(screen.getAllByText('July 3, 2025')).toHaveLength(2) // Appears in hero and author sections
  })

  it('shows executive summary', () => {
    render(<ManifestoPage />)
    
    expect(screen.getByText('Executive Summary')).toBeInTheDocument()
    expect(screen.getByText(/digital twins, blockchain, and AI can unlock global real estate markets/)).toBeInTheDocument()
  })

  it('has download button', () => {
    render(<ManifestoPage />)

    const downloadButton = screen.getByRole('button', { name: /Download Manifesto \(PDF\)/ })
    expect(downloadButton).toBeInTheDocument()
  })

  it('displays back to home link', () => {
    render(<ManifestoPage />)
    
    const backLink = screen.getByText('Back to Home')
    expect(backLink).toBeInTheDocument()
    expect(backLink.closest('a')).toHaveAttribute('href', '/')
  })

  it('shows contact information', () => {
    render(<ManifestoPage />)

    expect(screen.getByText('Questions or Media Inquiries?')).toBeInTheDocument()
    expect(screen.getByText(/Email Brandon: <EMAIL>/)).toBeInTheDocument()
  })

  it('displays disclaimer about AI assistance', () => {
    render(<ManifestoPage />)
    
    expect(screen.getByText('About this Document')).toBeInTheDocument()
    expect(screen.getByText(/Portions of this work were drafted and refined with the help of AI-powered research/)).toBeInTheDocument()
  })

  it('has proper document structure with headings', () => {
    render(<ManifestoPage />)
    
    // Check for main heading
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('The Verifiable Asset')
    
    // Check for section headings
    expect(screen.getByRole('heading', { level: 2, name: 'Executive Summary' })).toBeInTheDocument()
    expect(screen.getByRole('heading', { level: 3, name: 'About the Author' })).toBeInTheDocument()
  })
})
