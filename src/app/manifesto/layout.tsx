import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'The Verifiable Asset Manifesto - A Vision for Transparent Real Estate',
  description: '<PERSON>\'s manifesto outlining how digital twins, blockchain, and AI can unlock global real estate markets, making them transparent, programmable, and liquid.',
  keywords: [
    'real estate manifesto', 
    'digital twins', 
    'blockchain real estate', 
    'AI property', 
    'transparent real estate', 
    '<PERSON>', 
    'ordrly',
    'PropTech',
    'real estate technology',
    'verifiable assets',
    'property digitization'
  ],
  authors: [{ name: '<PERSON>' }],
  creator: '<PERSON>',
  publisher: 'Ordrly',
  openGraph: {
    title: 'The Verifiable Asset Manifesto',
    description: 'A vision for a transparent, intelligent, and liquid global real estate market by <PERSON>, founder of Ordrly',
    type: 'article',
    publishedTime: '2025-07-03T00:00:00.000Z',
    authors: ['<PERSON>'],
    url: '/manifesto',
    siteName: 'Ordrly',
    images: [
      {
        url: '/images/manifesto-og.png', // Future: Create a specific OG image for the manifesto
        width: 1200,
        height: 630,
        alt: 'The Verifiable Asset Manifesto by <PERSON>',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Verifiable Asset Manifesto',
    description: 'A vision for a transparent, intelligent, and liquid global real estate market',
    creator: '@ordrlyAI',
    images: ['/images/manifesto-twitter.png'], // Future: Create a specific Twitter card image
  },
  alternates: {
    canonical: '/manifesto',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function ManifestoLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "The Verifiable Asset: A Vision for a Transparent, Intelligent, and Liquid Global Real Estate Market",
    "description": "A manifesto outlining how digital twins, blockchain, and AI can unlock global real estate markets, making them transparent, programmable, and liquid.",
    "author": {
      "@type": "Person",
      "name": "Brandon Allen",
      "jobTitle": "Founder & CEO",
      "worksFor": {
        "@type": "Organization",
        "name": "Ordrly",
        "url": "https://ordrly.ai"
      }
    },
    "publisher": {
      "@type": "Organization",
      "name": "Ordrly",
      "url": "https://ordrly.ai",
      "logo": {
        "@type": "ImageObject",
        "url": "https://ordrly.ai/logo.svg"
      }
    },
    "datePublished": "2025-07-03T00:00:00.000Z",
    "dateModified": "2025-07-03T00:00:00.000Z",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://ordrly.ai/manifesto"
    },
    "articleSection": "Technology",
    "keywords": [
      "real estate",
      "blockchain",
      "digital twins",
      "AI",
      "PropTech",
      "transparency",
      "liquidity"
    ],
    "about": [
      {
        "@type": "Thing",
        "name": "Real Estate Technology"
      },
      {
        "@type": "Thing", 
        "name": "Blockchain"
      },
      {
        "@type": "Thing",
        "name": "Digital Twins"
      },
      {
        "@type": "Thing",
        "name": "Artificial Intelligence"
      }
    ]
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      {children}
    </>
  )
}
