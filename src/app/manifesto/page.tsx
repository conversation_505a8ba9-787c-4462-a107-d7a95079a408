'use client'

import { <PERSON>ada<PERSON> } from 'next'
import { useState, useEffect } from 'react'
import { ArrowLeft, Download, FileText, ExternalLink } from 'lucide-react'
import Link from 'next/link'

// Note: For static metadata, we'll need to export this separately
// export const metadata: Metadata = {
//   title: 'The Verifiable Asset Manifesto - A Vision for Transparent Real Estate',
//   description: '<PERSON>\'s manifesto outlining how digital twins, blockchain, and AI can unlock global real estate markets, making them transparent, programmable, and liquid.',
//   keywords: ['real estate manifesto', 'digital twins', 'blockchain real estate', 'AI property', 'transparent real estate', 'Brandon Allen', 'ordrly'],
//   openGraph: {
//     title: 'The Verifiable Asset Manifesto',
//     description: 'A vision for a transparent, intelligent, and liquid global real estate market',
//     type: 'article',
//     publishedTime: '2025-07-03T00:00:00.000Z',
//     authors: ['<PERSON>'],
//   },
// }

export default function ManifestoPage() {
  const [pdfError, setPdfError] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = '/The Verifiable Asset_ A Vision for a Transparent, Intelligent, and Liquid Global Real Estate Market.pdf'
    link.download = 'The-Verifiable-Asset-Manifesto.pdf'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 relative overflow-hidden">
      {/* Navigation */}
      <div className="relative z-10 container mx-auto px-6 py-6">
        <Link
          href="/"
          className="inline-flex items-center text-muted-foreground hover:text-foreground transition-colors duration-200"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Link>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-16 px-6">
        <div className="relative z-10 container mx-auto max-w-4xl text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
            <FileText className="w-8 h-8 text-primary" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
            The Verifiable Asset
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-4">
            A Vision for a Transparent, Intelligent, and Liquid Global Real Estate Market
          </p>

          {/* Author Info - Inline */}
          <div className="mb-8">
            <p className="text-lg font-medium text-foreground">
              By Brandon Allen
            </p>
            <p className="text-muted-foreground">
              July 3, 2025
            </p>
          </div>

          {/* Download Button */}
          <button
            onClick={handleDownload}
            className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground font-semibold rounded-lg border border-primary/20 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <Download className="w-5 h-5 mr-2" />
            Download Manifesto (PDF)
          </button>
        </div>
      </div>

      {/* Executive Summary */}
      <div className="relative z-10 container mx-auto max-w-4xl px-6 py-12">
        <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-8 shadow-sm hover-lift">
          <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center">
            <div className="w-2 h-8 bg-primary rounded-full mr-3"></div>
            Executive Summary
          </h2>
          <div className="prose prose-lg max-w-none text-muted-foreground">
            <p className="text-lg leading-relaxed">
              The Verifiable Asset manifesto outlines how a new synthesis of digital twins, blockchain, and AI can unlock global real estate markets, making them transparent, programmable, and liquid for the first time. This vision presents a future where every property becomes a verifiable digital asset with complete transparency, enabling unprecedented market efficiency and accessibility.
            </p>
            <p className="text-lg leading-relaxed mt-4">
              By combining immutable property records, intelligent automation, and real-time market data, we can transform real estate from an opaque, friction-heavy market into a transparent, efficient, and globally accessible asset class that serves both institutional and individual investors.
            </p>
          </div>
        </div>
      </div>

      {/* PDF Viewer Section */}
      <div className="relative z-10 container mx-auto max-w-6xl px-6 py-8">
        <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg overflow-hidden shadow-sm hover-lift">
          <div className="bg-muted/50 px-6 py-4 border-b border-border">
            <h3 className="text-lg font-semibold text-foreground flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Document Preview
            </h3>
          </div>
          
          <div className="p-6">
            {isClient && !pdfError ? (
              <div className="w-full">
                <iframe
                  src="/The Verifiable Asset_ A Vision for a Transparent, Intelligent, and Liquid Global Real Estate Market.pdf"
                  className="w-full h-[800px] border border-border rounded-lg"
                  title="The Verifiable Asset Manifesto"
                  onError={() => setPdfError(true)}
                />
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-muted rounded-full mb-4">
                  <FileText className="w-8 h-8 text-muted-foreground" />
                </div>
                <h4 className="text-lg font-medium text-foreground mb-2">
                  PDF Preview Unavailable
                </h4>
                <p className="text-muted-foreground mb-6">
                  {pdfError 
                    ? "Unable to display PDF preview in your browser." 
                    : "Loading document preview..."}
                </p>
                <button
                  onClick={handleDownload}
                  className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground font-medium rounded-lg border border-primary/20 transition-all duration-200"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF to View
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Author Information */}
      <div className="relative z-10 container mx-auto max-w-4xl px-6 py-8">
        <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-8 shadow-sm hover-lift">
          <h3 className="text-xl font-bold text-foreground mb-4 flex items-center">
            <div className="w-2 h-6 bg-secondary rounded-full mr-3"></div>
            About the Author
          </h3>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="md:w-2/3">
              <p className="text-lg text-muted-foreground leading-relaxed">
                <span className="font-semibold text-foreground">Brandon Allen</span> is the founder and CEO of Ordrly, 
                a PropTech platform that leverages AI to streamline municipal research for real estate professionals. 
                With a background in technology and a passion for solving complex problems, Brandon has dedicated his career 
                to making real estate markets more efficient and accessible.
              </p>
            </div>
            <div className="md:w-1/3">
              <div className="bg-muted/50 rounded-lg p-4">
                <p className="text-sm font-medium text-foreground mb-1">Published</p>
                <p className="text-sm text-muted-foreground">July 3, 2025</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Disclaimer */}
      <div className="relative z-10 container mx-auto max-w-4xl px-6 py-8">
        <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-amber-900 dark:text-amber-100 mb-3">
            About this Document
          </h4>
          <p className="text-amber-800 dark:text-amber-200 leading-relaxed">
            Portions of this work were drafted and refined with the help of AI-powered research and writing assistants, 
            enabling comprehensive coverage and accelerated synthesis of sources. All strategic insights, opinions, 
            and conclusions are solely those of the author.
          </p>
        </div>
      </div>

      {/* Contact Section */}
      <div className="relative z-10 container mx-auto max-w-4xl px-6 py-12">
        <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-foreground mb-4">
            Questions or Media Inquiries?
          </h3>
          <p className="text-lg text-muted-foreground mb-6">
            Interested in discussing the ideas presented in this manifesto or exploring collaboration opportunities?
          </p>
          <a
            href="mailto:<EMAIL>"
            className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground font-medium rounded-lg border border-primary/20 transition-all duration-200"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Email Brandon: <EMAIL>
          </a>
        </div>
      </div>

      {/* Future Extensions Comments */}
      {/* 
        Future enhancement areas:
        1. Video section: Add embedded video introduction or explanation
        2. Image gallery: Screenshots, diagrams, or infographics from the manifesto
        3. Interactive elements: Timeline, interactive charts, or concept visualizations
        4. Social sharing: Share buttons for LinkedIn, Twitter, etc.
        5. Related content: Links to blog posts, case studies, or implementation guides
        6. Newsletter signup: Capture emails for updates on manifesto-related developments
        7. Comments/discussion: Community discussion section
        8. Translations: Multi-language support for global reach
      */}
    </div>
  )
}
