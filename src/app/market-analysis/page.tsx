import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default async function MarketAnalysisPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has access to market analysis
  const hasAccess = profile.subscription_tier === 'appraiser'

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Market Analysis
            </h1>
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Appraiser Access Required
              </h2>
              <p className="text-gray-600 mb-6">
                Advanced regulatory analysis tools are available for Appraiser subscribers only.
              </p>
              <div className="space-y-4">
                <Link href="/pricing">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Upgrade to Appraiser
                  </Button>
                </Link>
                <div>
                  <Link href="/search">
                    <Button variant="outline">
                      Back to Search
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Regulatory Market Analysis
          </h1>
          <p className="text-gray-600 mb-8">
            Analysis of regulatory trends, zoning changes, and ordinance impacts on property development.
          </p>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This tool focuses on regulatory and compliance market trends,
              not property valuation or real estate market analysis.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Regulatory Trends */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Regulatory Trends
              </h2>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Zoning Updates</span>
                  <span className="font-semibold text-blue-600">12 this month</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">New Ordinances</span>
                  <span className="font-semibold text-green-600">5 enacted</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Code Changes</span>
                  <span className="font-semibold text-orange-600">3 pending</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Compliance Issues</span>
                  <span className="font-semibold text-red-600">8 reported</span>
                </div>
              </div>
            </div>

            {/* Recent Compliance Cases */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Recent Compliance Cases
              </h2>
              <div className="space-y-3">
                <div className="text-sm">
                  <p className="font-medium">Downtown Zoning Update</p>
                  <p className="text-gray-600">Mixed-use development restrictions</p>
                  <p className="text-gray-500">Updated 15 days ago</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">Residential Setback Changes</p>
                  <p className="text-gray-600">New 25ft front yard requirements</p>
                  <p className="text-gray-500">Updated 22 days ago</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">Building Height Ordinance</p>
                  <p className="text-gray-600">35ft limit in residential zones</p>
                  <p className="text-gray-500">Updated 31 days ago</p>
                </div>
              </div>
              <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700" disabled>
                View All Cases
              </Button>
            </div>
          </div>

          {/* Regulatory Reports */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Regulatory Reports
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="border border-gray-200 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Quarterly Compliance Report</h3>
                <p className="text-sm text-gray-600 mb-3">Q4 2024 regulatory changes and updates</p>
                <Button variant="outline" className="w-full" disabled>Coming Soon</Button>
              </div>
              <div className="border border-gray-200 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Ordinance Forecast</h3>
                <p className="text-sm text-gray-600 mb-3">2025 regulatory predictions and trends</p>
                <Button variant="outline" className="w-full" disabled>Coming Soon</Button>
              </div>
              <div className="border border-gray-200 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Custom Analysis</h3>
                <p className="text-sm text-gray-600 mb-3">Generate custom compliance reports</p>
                <Button variant="outline" className="w-full" disabled>Coming Soon</Button>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <Link href="/valuation">
              <Button variant="outline">
                Compliance Tools
              </Button>
            </Link>
            <Link href="/reports">
              <Button className="bg-green-600 hover:bg-green-700">
                Compliance Reports
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
