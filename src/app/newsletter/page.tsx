'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Mail,
  Check,
  TrendingUp,
  Users,
  Bell,
  Star,
  ArrowRight,
  Shield,
  Clock
} from 'lucide-react'

export default function NewsletterSignup() {
  const [email, setEmail] = useState('')
  const [name, setName] = useState('')
  const [preferences, setPreferences] = useState({
    marketing: true,
    updates: true,
    newsletters: true
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      setError('Email is required')
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name: name || undefined,
          preferences,
          source: 'newsletter_page'
        }),
      })

      const data = await response.json()

      if (data.success) {
        setIsSubscribed(true)
      } else {
        setError(data.error || 'Failed to subscribe')
      }
    } catch {
      setError('An error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubscribed) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4">
        <div className="max-w-md w-full">
          <Card className="text-center">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome to Ordrly Newsletter!
              </h1>
              <p className="text-gray-600 mb-6">
                Thank you for subscribing! You&apos;ll receive our latest real estate insights,
                product updates, and exclusive content directly in your inbox.
              </p>
              <div className="space-y-3 text-sm text-gray-500">
                <div className="flex items-center justify-center space-x-2">
                  <Mail className="h-4 w-4" />
                  <span>Confirmation email sent to {email}</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>First newsletter arrives next week</span>
                </div>
              </div>
              <Button
                className="mt-6 w-full"
                onClick={() => window.location.href = '/'}
              >
                Continue to Ordrly
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4">
            <Mail className="h-3 w-3 mr-1" />
            Newsletter
          </Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Stay Ahead in Real Estate
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get exclusive insights, market trends, and product updates delivered
            straight to your inbox. Join thousands of real estate professionals.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Newsletter Benefits */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                  Market Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Weekly market analysis, trend reports, and data-driven insights
                  to help you make informed real estate decisions.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2 text-green-600" />
                  Product Updates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Be the first to know about new Ordrly features, improvements,
                  and tools that can enhance your workflow.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="h-5 w-5 mr-2 text-purple-600" />
                  Exclusive Content
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Access to subscriber-only content, case studies, and expert
                  interviews from industry leaders.
                </p>
              </CardContent>
            </Card>

            {/* Social Proof */}
            <div className="bg-white rounded-lg p-6 border">
              <div className="flex items-center space-x-4 mb-4">
                <Users className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="font-semibold text-gray-900">Join 5,000+ Subscribers</p>
                  <p className="text-sm text-gray-600">Real estate professionals trust our insights</p>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-blue-600">98%</p>
                  <p className="text-xs text-gray-600">Open Rate</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">4.9</p>
                  <p className="text-xs text-gray-600">Rating</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-600">Weekly</p>
                  <p className="text-xs text-gray-600">Delivery</p>
                </div>
              </div>
            </div>
          </div>

          {/* Signup Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Subscribe to Our Newsletter</CardTitle>
                <CardDescription>
                  Get started with your free subscription today
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Name (Optional)
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Your name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Email Preferences
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={preferences.newsletters}
                          onChange={(e) => setPreferences(prev => ({
                            ...prev,
                            newsletters: e.target.checked
                          }))}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Weekly newsletter with market insights
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={preferences.updates}
                          onChange={(e) => setPreferences(prev => ({
                            ...prev,
                            updates: e.target.checked
                          }))}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Product updates and new features
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={preferences.marketing}
                          onChange={(e) => setPreferences(prev => ({
                            ...prev,
                            marketing: e.target.checked
                          }))}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Special offers and promotions
                        </span>
                      </label>
                    </div>
                  </div>

                  {error && (
                    <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
                      {error}
                    </div>
                  )}

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Subscribing...
                      </>
                    ) : (
                      <>
                        <Mail className="h-4 w-4 mr-2" />
                        Subscribe Now
                      </>
                    )}
                  </Button>

                  <div className="flex items-center justify-center space-x-2 text-xs text-gray-500 mt-4">
                    <Shield className="h-3 w-3" />
                    <span>We respect your privacy. Unsubscribe at any time.</span>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Sample Newsletter Preview */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">What to Expect</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium">Market Spotlight</p>
                      <p className="text-gray-600">Weekly analysis of key market movements</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium">Feature Highlights</p>
                      <p className="text-gray-600">New tools and improvements in Ordrly</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-600 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium">Industry News</p>
                      <p className="text-gray-600">Curated news and regulatory updates</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
