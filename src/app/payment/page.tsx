import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { PaymentForm } from '@/components/billing/PaymentForm'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default async function PaymentPage({
  searchParams,
}: {
  searchParams: Promise<{ plan?: string; return_url?: string }>
}) {
  const params = await searchParams
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Payment submission will be handled by the PaymentForm component internally

  return (
    <div className="max-w-2xl mx-auto py-16 px-4">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete Payment</h1>
            <p className="text-gray-600">
              {params.plan ? `Upgrading to ${params.plan} plan` : 'Secure payment processing'}
            </p>
          </div>
          <Link href={params.return_url || '/pricing'}>
            <Button variant="outline">
              ← Back
            </Button>
          </Link>
        </div>
      </div>

      {/* Plan Summary */}
      {params.plan && (
        <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Plan Summary</h3>
          <div className="text-sm text-blue-800">
            {params.plan === 'pro' && (
              <>
                <p>Pro Plan - $19/month</p>
                <p>✓ Unlimited searches</p>
                <p>✓ Advanced filters</p>
                <p>✓ Data export</p>
                <p>✓ Premium support</p>
              </>
            )}
            {params.plan === 'appraiser' && (
              <>
                <p>Appraiser Plan - $59/month</p>
                <p>✓ All Pro features</p>
                <p>✓ Professional tools</p>
                <p>✓ API access</p>
                <p>✓ Market analysis</p>
                <p>✓ Enterprise support</p>
              </>
            )}
          </div>
        </div>
      )}

      <div data-testid="payment-form">
        <PaymentForm />
      </div>

      {/* Security Notice */}
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>🔒 Your payment information is encrypted and secure</p>
        <p>Powered by Stripe • PCI DSS compliant</p>
        <p>Payment validation and processing available</p>
      </div>
    </div>
  )
}
