"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import {
  ArrowRight, Check, X, Zap, Shield, Clock, Database, Globe,
  Terminal, Code, Layers, Search, Cpu, Eye, BarChart3,
  CheckCircle, Star, Users, TrendingUp, Lock, Sparkles,
  Rocket, Crown, Building, Briefcase
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { OrdrlyLogoSVG } from '@/components/ui/ordrly-logo'
import { UpgradeButton } from '@/components/billing/UpgradeButton'

// Sophisticated theme system that works with light/dark mode
const theme = {
  container: "relative z-10 container mx-auto px-6",
  card: "bg-card/50 backdrop-blur-sm border border-border hover-lift",
  text: {
    primary: "text-foreground",
    secondary: "text-muted-foreground",
    accent: "text-primary",
    success: "text-green-500 dark:text-green-400"
  },
  button: {
    primary: "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground border border-primary/20",
    secondary: "border-border text-muted-foreground hover:bg-muted/50",
    success: "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white",
    premium: "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white border border-purple-500/50"
  },
  border: {
    blue: "border-primary/30",
    green: "border-green-500/30",
    purple: "border-purple-500/30",
    gold: "border-yellow-500/30"
  },
  background: {
    page: "min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 relative overflow-hidden"
  }
}

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly')

  const plans = [
    {
      name: "Starter",
      icon: Rocket,
      price: { monthly: 49, annual: 490 },
      description: "Perfect for homeowners and small projects",
      features: [
        "500 messages per month",
        "AI chat assistance",
        "Municipal compliance research",
        "Save & organize searches",
        "Source links to ordinances",
        "Mobile-friendly interface",
        "Standard email support"
      ],
      limitations: [
        "Limited to 500 messages/month",
        "Standard support response times"
      ],
      buttonText: "Start 7-Day Free Trial",
      buttonStyle: theme.button.primary,
      popular: false,
      border: theme.border.blue,
      planType: 'starter' as const
    },
    {
      name: "Professional",
      icon: Briefcase,
      price: { monthly: 99, annual: 990 },
      description: "Ideal for contractors and frequent builders",
      features: [
        "2,000 messages per month",
        "AI chat assistance",
        "Municipal compliance research",
        "Save & organize searches",
        "Priority email support",
        "Faster response times",
        "Enhanced source analysis",
        "Mobile-friendly interface"
      ],
      limitations: [
        "No API access",
        "No white-label options"
      ],
      buttonText: "Start 7-Day Free Trial",
      buttonStyle: theme.button.success,
      popular: true,
      border: theme.border.green,
      planType: 'professional' as const
    },
    {
      name: "Business",
      icon: Building,
      price: { monthly: "Custom", annual: "Custom" },
      description: "For enterprises and API integration needs",
      features: [
        "Everything in Professional",
        "API access & documentation",
        "White-label solutions",
        "Custom integrations",
        "Dedicated account manager",
        "99.9% SLA guarantee",
        "Custom data exports",
        "Priority feature requests",
        "Volume discounts available"
      ],
      limitations: [],
      buttonText: "Contact Sales",
      buttonStyle: theme.button.premium,
      popular: false,
      border: theme.border.purple,
      planType: 'business' as const,
      isCustomPricing: true
    }
  ]

  return (
    <div className={theme.background.page}>
      {/* Background Grid */}
      <div className="absolute inset-0 grid-pattern opacity-10 dark:opacity-20" />

      {/* Floating Orbs - Responsive to theme */}
      <div className="absolute w-32 h-32 bg-primary/5 dark:bg-primary/10 rounded-full blur-xl neural-pulse top-20 left-20" />
      <div className="absolute w-24 h-24 bg-green-500/10 dark:bg-green-500/15 rounded-full blur-xl neural-pulse top-40 right-32" style={{ animationDelay: '1s' }} />
      <div className="absolute w-40 h-40 bg-muted/20 dark:bg-slate-500/10 rounded-full blur-xl neural-pulse bottom-32 left-1/3" style={{ animationDelay: '2s' }} />
      <div className="absolute w-28 h-28 bg-purple-500/5 dark:bg-purple-500/10 rounded-full blur-xl neural-pulse bottom-20 right-20" style={{ animationDelay: '3s' }} />

      {/* Hero Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl text-center">
            <div className="inline-flex items-center gap-6 mb-8">
              <div className="w-20 h-20 relative flex items-center justify-center">
                <Image
                  src="/logo512.png"
                  alt="Ordrly"
                  width={80}
                  height={80}
                  className="drop-shadow-lg"
                />
              </div>
              <div className="text-left">
                <h1 className="text-5xl md:text-6xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-primary/80 bg-clip-text text-transparent leading-tight pb-2">
                  Simple Pricing
                </h1>
                <div className={`text-xl md:text-2xl font-exo font-light ${theme.text.success} mt-2`}>
                  powered by Ordrly
                </div>
              </div>
            </div>

            <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto leading-relaxed mb-12`}>
              Start with a <span className={theme.text.success}>7-day free trial</span> and scale as you grow.
              <span className={theme.text.accent}> All plans include our core municipal research capabilities.</span>
            </p>

            {/* Billing Toggle - FIXED */}
            <div className="flex items-center justify-center mb-4">
              <div className="w-20"></div> {/* Left spacer */}
              <div className="flex items-center gap-4">
                <span className={`text-base font-medium ${billingCycle === 'monthly' ? 'text-foreground' : 'text-muted-foreground'} transition-colors`}>
                  Monthly
                </span>
                <div
                  onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'annual' : 'monthly')}
                  className={`relative cursor-pointer rounded-full transition-colors duration-300 ease-in-out focus:outline-none ${
                    billingCycle === 'annual'
                      ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                  style={{ width: '48px', height: '24px' }}
                >
                  <div
                    className="absolute bg-white rounded-full shadow-md"
                    style={{
                      width: '20px',
                      height: '20px',
                      top: '2px',
                      left: billingCycle === 'annual' ? '26px' : '2px',
                      transition: 'left 0.3s ease-in-out'
                    }}
                  />
                </div>
                <span className={`text-base font-medium ${billingCycle === 'annual' ? 'text-foreground' : 'text-muted-foreground'} transition-colors`}>
                  Annually
                </span>
              </div>
              <div className="w-20 ml-4 flex justify-start">
                {billingCycle === 'annual' && (
                  <span className="bg-green-500/20 text-green-500 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium animate-in fade-in duration-300">
                    Save 17%
                  </span>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section className={`${theme.container} pb-24`}>
          <div className="mx-auto max-w-7xl">
            <div className="grid md:grid-cols-3 gap-8">
              {plans.map((plan, index) => (
                <Card key={plan.name} className={`${theme.card} ${plan.border} relative ${plan.popular ? 'scale-105' : ''}`}>
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-1 rounded-full text-sm font-exo">
                        Most Popular
                      </div>
                    </div>
                  )}

                  <CardHeader className="text-center pb-6">
                    <div className={`w-16 h-16 ${plan.popular ? 'bg-green-500/20' : 'bg-gray-500/20'} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                      <plan.icon className={`w-8 h-8 ${plan.popular ? 'text-green-400' : 'text-gray-400'}`} />
                    </div>
                    <CardTitle className={`font-orbitron text-2xl ${theme.text.primary} mb-2`}>
                      {plan.name}
                    </CardTitle>
                    <CardDescription className={`${theme.text.secondary} font-space mb-4`}>
                      {plan.description}
                    </CardDescription>
                    <div className="text-center">
                      <div className={`text-4xl font-orbitron font-bold ${theme.text.primary} mb-2`}>
                        {plan.isCustomPricing ? (
                          plan.price.monthly
                        ) : (
                          <>
                            ${plan.price[billingCycle]}
                            <span className={`text-lg ${theme.text.secondary} font-space`}>
                              /{billingCycle === 'monthly' ? 'mo' : 'yr'}
                            </span>
                          </>
                        )}
                      </div>
                      {!plan.isCustomPricing && billingCycle === 'annual' && typeof plan.price.annual === 'number' && (
                        <div className={`text-sm ${theme.text.secondary} font-space`}>
                          ${Math.round(plan.price.annual / 12)}/month billed annually
                        </div>
                      )}
                      {!plan.isCustomPricing && (
                        <div className={`text-sm ${theme.text.success} font-space mt-2`}>
                          7-day free trial included
                        </div>
                      )}
                      {plan.isCustomPricing && (
                        <div className={`text-sm ${theme.text.secondary} font-space mt-2`}>
                          Contact us for pricing
                        </div>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    {plan.isCustomPricing ? (
                      <Button
                        className={`w-full ${plan.buttonStyle} font-exo`}
                        onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Business Plan Inquiry'}
                      >
                        {plan.buttonText}
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    ) : (
                      <UpgradeButton planType={plan.planType} billingCycle={billingCycle} className={`w-full ${plan.buttonStyle} font-exo`}>
                        {plan.buttonText}
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </UpgradeButton>
                    )}

                    <div className="space-y-3">
                      <h4 className={`font-exo font-semibold ${theme.text.primary} text-sm`}>
                        What's included:
                      </h4>
                      <ul className="space-y-2">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-3">
                            <Check className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                            <span className={`text-sm ${theme.text.secondary} font-space`}>
                              {feature}
                            </span>
                          </li>
                        ))}
                      </ul>

                      {plan.limitations.length > 0 && (
                        <div className="pt-3 border-t border-gray-700/50">
                          <h4 className={`font-exo font-semibold ${theme.text.secondary} text-sm mb-2`}>
                            Limitations:
                          </h4>
                          <ul className="space-y-2">
                            {plan.limitations.map((limitation, limitIndex) => (
                              <li key={limitIndex} className="flex items-start gap-3">
                                <X className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                                <span className={`text-sm ${theme.text.secondary} font-space`}>
                                  {limitation}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-orbitron font-bold bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent mb-6">
                Frequently Asked Questions
              </h2>
              <p className={`text-xl ${theme.text.secondary} font-space`}>
                Everything you need to know about our municipal research pricing
              </p>
            </div>

            <div className="grid gap-6">
              <Card className={`${theme.card} ${theme.border.blue}`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    What happens during the free trial?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    You get full access to our Starter plan for 7 days with 1,000 API calls included.
                    No credit card required. You can upgrade, downgrade, or cancel anytime.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.green}`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    How accurate is the municipal data?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    Our AI-powered system achieves 95%+ accuracy by cross-referencing multiple official sources.
                    Every response includes confidence scores and source citations for verification.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} ${theme.border.purple}`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    Can I change plans anytime?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately,
                    and we'll prorate any billing adjustments.
                  </p>
                </CardContent>
              </Card>

              <Card className={`${theme.card} border-border`}>
                <CardHeader>
                  <CardTitle className={`font-exo ${theme.text.primary}`}>
                    What if I exceed my API call limit?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${theme.text.secondary} font-space`}>
                    We'll notify you when you reach 80% of your limit. If you exceed it, we'll temporarily
                    throttle requests but won't charge overage fees. Simply upgrade to continue.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className={`${theme.container} py-24`}>
          <div className="mx-auto max-w-4xl text-center">
            <Card className={`${theme.card} ${theme.border.green}`}>
              <CardHeader>
                <CardTitle className="text-3xl font-orbitron font-bold bg-gradient-to-r from-foreground to-green-400 bg-clip-text text-transparent mb-4">
                  Ready to Get Started?
                </CardTitle>
                <CardDescription className={`text-lg ${theme.text.secondary} font-space`}>
                  Join thousands of developers who trust Ordrly for municipal research
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                  <UpgradeButton planType="starter" billingCycle={billingCycle} className={`${theme.button.success} px-8 py-4 text-lg font-exo`}>
                    <CheckCircle className="w-5 h-5 mr-3" />
                    Start Free Trial
                    <ArrowRight className="w-5 h-5 ml-3" />
                  </UpgradeButton>
                  <Button variant="outline" size="lg" className={`${theme.button.secondary} px-8 py-4 text-lg font-exo`}>
                    <Eye className="w-5 h-5 mr-3" />
                    Schedule Demo
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center pt-6">
                  <div className={`${theme.text.secondary} font-space text-sm`}>
                    <CheckCircle className="w-4 h-4 text-green-400 mx-auto mb-1" />
                    7-day free trial
                  </div>
                  <div className={`${theme.text.secondary} font-space text-sm`}>
                    <CheckCircle className="w-4 h-4 text-green-400 mx-auto mb-1" />
                    No setup fees
                  </div>
                  <div className={`${theme.text.secondary} font-space text-sm`}>
                    <CheckCircle className="w-4 h-4 text-green-400 mx-auto mb-1" />
                    Cancel anytime
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
    </div>
  )
}
