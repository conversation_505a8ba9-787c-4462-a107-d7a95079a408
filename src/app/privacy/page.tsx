'use client'

import { Shield, Lock, Eye, Database, Users, <PERSON>ie, Mail, FileText, Settings, Download, Bell, ArrowUp } from 'lucide-react'
import Link from 'next/link'
import { useState, useEffect } from 'react'

export default function PrivacyPage() {
  const [showBackToTop, setShowBackToTop] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > window.innerHeight / 2)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
  return (
    <div className="min-h-screen bg-background py-16 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Back to Account Link */}
        <div className="pb-4">
          <Link href="/account" className="text-primary hover:underline text-sm">
            ← Back to Account
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
            <Shield className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-2">
            Privacy Policy
          </h1>
          <p className="text-sm text-muted-foreground/80 italic mb-4">
            Last updated: July 2, 2025
          </p>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            This Privacy Policy explains what data Ordrly collects, how we use it, and the choices you have regarding your information.
          </p>
        </div>

        <div className="space-y-6">
          {/* Information We Collect */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <div className="flex items-center mb-6">
              <Database className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Information We Collect</h2>
            </div>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-card-foreground mb-3">Account Information</h3>
                <ul className="list-disc pl-5 text-muted-foreground space-y-2 marker:text-green-400">
                  <li>Email address and name (when you create an account or sign in with Google)</li>
                  <li>Password (encrypted) or OAuth authentication tokens</li>
                  <li>Subscription tier and billing information</li>
                  <li>Account preferences and settings</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium text-card-foreground mb-3">Research and Chat Data</h3>
                <ul className="list-disc pl-5 text-muted-foreground space-y-2 marker:text-green-400">
                  <li>Municipal research queries and questions</li>
                  <li>Jurisdiction and location information for research context</li>
                  <li>Chat conversations and AI responses (if enabled in your privacy settings)</li>
                  <li>Research history and session data (if enabled in your privacy settings)</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium text-card-foreground mb-3">Usage and Analytics Data</h3>
                <ul className="list-disc pl-5 text-muted-foreground space-y-2 marker:text-green-400">
                  <li>Device information, browser type, and IP address</li>
                  <li>Pages visited, time spent, and user interactions</li>
                  <li>Search patterns and feature usage analytics</li>
                  <li>Error logs and performance metrics</li>
                </ul>
              </div>
            </div>
          </div>

          {/* How We Use Your Information */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <p className="text-xs text-muted-foreground/60">Use & Processing</p>
            <div className="flex items-center mb-6 mt-1">
              <Settings className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-lg font-semibold text-card-foreground">How We Use Your Information</h2>
            </div>
            <div className="space-y-4">
              <p className="text-muted-foreground mb-4">
                We use the information we collect to provide and improve our services:
              </p>
              <ul className="list-disc pl-5 text-muted-foreground space-y-2 marker:text-green-400">
                <li>Provide municipal ordinance research and compliance information</li>
                <li>Power our AI chat interface to answer your municipal research questions</li>
                <li>Process payments and manage your subscription</li>
                <li>Save your chat history and research sessions (with your permission)</li>
                <li>Improve our service accuracy and user experience</li>
                <li>Send important service updates and notifications</li>
                <li>Provide customer support and respond to your inquiries</li>
                <li>Detect and prevent fraud or abuse</li>
              </ul>
            </div>
          </div>

          {/* Third-Party Services and Information Sharing */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <div className="flex items-center mb-6">
              <Users className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Third-Party Services and Information Sharing</h2>
            </div>
            <div className="space-y-6">
              <p className="text-muted-foreground">
                We work with trusted third-party services to provide our platform. We do not sell your personal information to anyone.
              </p>
              <div>
                <h3 className="text-lg font-medium text-card-foreground mb-3">Service Providers We Use</h3>
                <ul className="list-disc pl-5 text-muted-foreground space-y-1 marker:text-green-400">
                  <li><strong>Supabase:</strong> Database hosting, user authentication, and data storage</li>
                  <li><strong>Stripe:</strong> Payment processing and subscription management</li>
                  <li><strong>OpenAI:</strong> AI-powered ordinance analysis and chat assistance</li>
                  <li><strong>Google Services:</strong> OAuth authentication, analytics, and search APIs</li>
                  <li><strong>Vercel:</strong> Website hosting and content delivery</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium text-card-foreground mb-3">When We Share Information</h3>
                <ul className="list-disc pl-5 text-muted-foreground space-y-1 marker:text-green-400">
                  <li>With service providers who help us operate our platform (under strict data protection agreements)</li>
                  <li>When required by law or to protect our rights and safety</li>
                  <li>In connection with a business transfer (with your consent)</li>
                  <li>With your explicit consent for specific purposes</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Data Security */}
          <div className="bg-card border border-border rounded-2xl p-8 shadow-premium animate-slide-up">
            <div className="flex items-center mb-6">
              <Lock className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Data Security</h2>
            </div>
            <div className="space-y-4">
              <p className="text-muted-foreground mb-4">
                We implement industry-standard security measures to protect your personal information:
              </p>
              <ul className="list-disc pl-6 text-muted-foreground space-y-2">
                <li>Encryption in transit (HTTPS/TLS) and at rest for sensitive data</li>
                <li>Secure authentication with password hashing and OAuth integration</li>
                <li>Regular security audits and vulnerability assessments</li>
                <li>Access controls and monitoring for our systems</li>
                <li>Secure third-party providers with SOC 2 compliance</li>
                <li>Regular backups and disaster recovery procedures</li>
              </ul>
              <p className="text-muted-foreground mt-4">
                While we strive to protect your information, no method of transmission over the internet is 100% secure.
                We encourage you to use strong passwords and keep your account information confidential.
              </p>
            </div>
          </div>

          {/* Your Rights and Choices */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <div className="flex items-center mb-6">
              <Eye className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Your Rights and Choices</h2>
            </div>
            <div className="space-y-6">
              <p className="text-muted-foreground">
                You have control over your personal information and how we use it:
              </p>
              <div>
                <h3 className="text-lg font-medium text-card-foreground mb-3">Privacy Controls</h3>
                <ul className="list-disc pl-5 text-muted-foreground space-y-2 marker:text-green-400">
                  <li>Control whether we save your search history and chat conversations</li>
                  <li>Set data retention periods and enable auto-deletion</li>
                  <li>Manage email preferences and notifications</li>
                  <li>Access these settings in your <Link href="/account" className="text-primary hover:text-primary/80 underline">account dashboard</Link></li>
                </ul>
              </div>

              <div className="border-t border-muted/20 my-4"></div>

              <div>
                <h3 className="text-lg font-semibold text-card-foreground mb-3">Data Rights</h3>
                <ul className="list-disc pl-5 text-muted-foreground space-y-2 marker:text-green-400">
                  <li><strong>Access:</strong> Request a copy of your personal data</li>
                  <li><strong>Export:</strong> Download your search history and chat data</li>
                  <li><strong>Delete:</strong> Remove specific searches, chats, or your entire account</li>
                  <li><strong>Correct:</strong> Update inaccurate information in your profile</li>
                  <li><strong>Portability:</strong> Transfer your data to another service</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Data Retention */}
          <div className="bg-card border border-border rounded-2xl p-8 shadow-premium animate-slide-up">
            <div className="flex items-center mb-6">
              <FileText className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Data Retention</h2>
            </div>
            <div className="space-y-4">
              <p className="text-muted-foreground mb-4">
                We retain your information for different periods based on the type of data and your preferences:
              </p>
              <ul className="list-disc pl-6 text-muted-foreground space-y-2">
                <li><strong>Account Information:</strong> Retained while your account is active, deleted within 30 days of account closure</li>
                <li><strong>Search History:</strong> Retained based on your privacy settings (30 days to 2 years, or until manually deleted)</li>
                <li><strong>Chat Conversations:</strong> Retained based on your privacy settings (30 days to 2 years, or until manually deleted)</li>
                <li><strong>Payment Information:</strong> Billing records retained for 7 years for tax and legal compliance</li>
                <li><strong>Analytics Data:</strong> Aggregated and anonymized data retained for up to 2 years</li>
                <li><strong>Support Communications:</strong> Retained for 3 years to improve our service</li>
              </ul>
              <p className="text-muted-foreground mt-4">
                You can configure auto-deletion settings in your account to automatically remove old data based on your preferences.
              </p>
            </div>
          </div>

          {/* Cookies and Analytics */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <div className="flex items-center mb-6">
              <Cookie className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Cookies and Analytics</h2>
            </div>
            <div className="space-y-6">
              <div>
                <p className="text-blue-300 text-sm font-semibold">Essential Cookies</p>
                <p className="text-muted-foreground mb-2 mt-1">
                  We use essential cookies to make our website work properly:
                </p>
                <ul className="list-disc list-inside text-muted-foreground space-y-1 marker:text-green-400">
                  <li>Authentication and session management</li>
                  <li>Security and fraud prevention</li>
                  <li>User preferences and settings</li>
                </ul>
              </div>
              <div>
                <p className="text-blue-300 text-sm font-semibold">Analytics</p>
                <p className="text-muted-foreground mb-2 mt-1">
                  We use Google Analytics to understand how our website is used:
                </p>
                <ul className="list-disc list-inside text-muted-foreground space-y-1 marker:text-green-400">
                  <li>Page views, user interactions, and website performance</li>
                  <li>Aggregated usage patterns to improve our service</li>
                  <li>No personally identifiable information is shared with Google</li>
                </ul>
              </div>
              <p className="text-muted-foreground">
                You can control cookies through your browser settings or opt out of Google Analytics using their
                <a href="https://tools.google.com/dlpage/gaoptout" target="_blank" rel="noopener noreferrer"
                   className="text-primary hover:text-primary/80 underline ml-1">opt-out tool</a>.
              </p>
            </div>
          </div>

          {/* Children's Privacy */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <div className="flex items-center justify-center mb-6">
              <Shield className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Children's Privacy</h2>
            </div>
            <p className="text-muted-foreground">
              Our service is not intended for children under 13 years of age. We do not knowingly collect personal
              information from children under 13. If you are a parent or guardian and believe your child has provided
              us with personal information, please contact us so we can delete such information.
            </p>
          </div>

          {/* Changes to This Policy */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <div className="flex items-center justify-center mb-6">
              <Bell className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Changes to This Policy</h2>
            </div>
            <p className="text-muted-foreground">
              We may update this Privacy Policy from time to time to reflect changes in our practices or for legal reasons.
              We will notify you of any material changes by posting the new policy on this page and updating the "Last updated"
              date. For significant changes, we may also send you an email notification. We encourage you to review this policy
              periodically to stay informed about how we protect your information.
            </p>
          </div>

          {/* Contact Us */}
          <div className="bg-card border border-border rounded-2xl p-6 shadow-premium animate-slide-up">
            <div className="flex items-center mb-6">
              <Mail className="h-6 w-6 text-primary mr-3" />
              <h2 className="text-2xl font-semibold text-card-foreground">Contact Us</h2>
            </div>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                If you have any questions about this Privacy Policy, want to exercise your data rights, or need help
                with your privacy settings, please don't hesitate to contact us:
              </p>
              <div className="bg-muted/20 rounded-lg p-4 border border-border">
                <p className="text-card-foreground font-medium mb-2">Support Team</p>
                <p className="text-muted-foreground">
                  Email: <a href="mailto:<EMAIL>" className="text-primary hover:text-primary/80 underline">
                    <EMAIL>
                  </a>
                </p>
                <p className="text-muted-foreground mt-2">
                  For general support: <Link href="/contact"
                    className="inline-flex items-center px-4 py-2 bg-primary border border-primary rounded-md text-white hover:bg-primary/90 transition-colors font-medium">
                    Contact Support
                  </Link>
                </p>
                <p className="text-xs text-muted-foreground/80 mt-3">
                  We'll respond to your request within 30 days.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Callout */}
        <div className="text-center text-xs text-muted-foreground/60 mb-8">
          Remember: Always verify ordinance information with your local jurisdiction.
        </div>

        {/* Back to Top Button */}
        {showBackToTop && (
          <button
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 bg-primary hover:bg-primary/90 text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110 z-50"
            aria-label="Back to top"
          >
            <ArrowUp className="h-5 w-5" />
          </button>
        )}
      </div>
    </div>
  )
}
