import { Metadata } from 'next'

export const metadata: Metadata = {
  title: "The Fastest Way to Research Municipal Ordinances for Appraisers & Real Estate Pros | Ordrly",
  description: "Stop digging through city websites and 500-page PDFs. Get instant, sourced answers from municipal codes so you can complete your work faster and with more confidence. Save hours on every report.",
  keywords: [
    "appraiser tools", 
    "real estate research", 
    "municipal codes", 
    "zoning research", 
    "building permits", 
    "compliance research", 
    "property research", 
    "real estate professionals",
    "appraisal research",
    "municipal research"
  ],
  openGraph: {
    title: "The Fastest Way to Research Municipal Ordinances for Appraisers & Real Estate Pros",
    description: "Stop digging through city websites and 500-page PDFs. Get instant, sourced answers from municipal codes. Save hours on every report.",
    type: "website",
    url: "/professionals",
  },
  twitter: {
    card: "summary_large_image",
    title: "The Fastest Way to Research Municipal Ordinances for Appraisers & Real Estate Pros",
    description: "Stop digging through city websites and 500-page PDFs. Get instant, sourced answers from municipal codes. Save hours on every report.",
  },
  alternates: {
    canonical: '/professionals',
  },
}

export default function ProfessionalsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
