"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  ArrowRight, Clock, CheckCircle, Star, Users, TrendingUp, 
  FileText, Search, Zap, Shield, Calendar, Phone
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { OrdrlyLogo } from '@/components/ui/ordrly-logo'
import { ChatUIShowcase } from '@/components/professionals/ChatUIShowcase'

// Theme system that works with light/dark mode
const theme = {
  container: "relative z-10 container mx-auto px-6",
  card: "bg-card/50 backdrop-blur-sm border border-border hover-lift",
  text: {
    primary: "text-foreground",
    secondary: "text-muted-foreground",
    accent: "text-primary",
    success: "text-green-500 dark:text-green-400"
  },
  button: {
    primary: "bg-primary hover:bg-primary/90 text-primary-foreground border border-primary/20",
    secondary: "border-border text-muted-foreground hover:bg-muted/50",
    success: "bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white"
  },
  border: {
    blue: "border-primary/30",
    green: "border-green-500/30",
    purple: "border-purple-500/30"
  },
  background: {
    page: "min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/20 relative overflow-hidden",
    orb: "dark:bg-primary/10 light:bg-primary/5"
  }
}

// Animated counter component
const AnimatedCounter = ({ end, duration = 2000 }: { end: number, duration?: number }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      setCount(Math.floor(progress * end))
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    requestAnimationFrame(animate)
  }, [end, duration])

  return <span>{count.toLocaleString()}</span>
}

export default function ProfessionalsPage() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <div className={theme.background.page}>
      {/* Background Grid */}
      <div className="absolute inset-0 grid-pattern opacity-10 dark:opacity-20" />

      {/* Floating Orbs - Responsive to theme */}
      <div className="absolute w-32 h-32 bg-primary/5 dark:bg-primary/10 rounded-full blur-xl neural-pulse top-20 left-20" />
      <div className="absolute w-24 h-24 bg-green-500/10 dark:bg-green-500/15 rounded-full blur-xl neural-pulse top-40 right-32" style={{ animationDelay: '1s' }} />
      <div className="absolute w-40 h-40 bg-muted/20 dark:bg-slate-500/10 rounded-full blur-xl neural-pulse bottom-32 left-1/3" style={{ animationDelay: '2s' }} />
      <div className="absolute w-28 h-28 bg-purple-500/5 dark:bg-purple-500/10 rounded-full blur-xl neural-pulse bottom-20 right-20" style={{ animationDelay: '3s' }} />

      {/* Hero Section */}
      <section className={`${theme.container} py-24 md:py-32`}>
        <div className={`mx-auto max-w-6xl text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="inline-flex items-center gap-6 mb-8">
            <OrdrlyLogo size="xl" className="w-32 h-32 md:w-40 md:h-40" />
            <div className="text-left">
              <h1 className="text-5xl md:text-7xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-primary/80 bg-clip-text text-transparent">
                The Fastest Way to Research Municipal Ordinances
              </h1>
              <div className={`text-2xl md:text-3xl font-exo font-light ${theme.text.success} mt-2`}>
                for Appraisers & Real Estate Pros
              </div>
            </div>
          </div>

          <p className={`text-xl md:text-2xl ${theme.text.secondary} font-space max-w-4xl mx-auto leading-relaxed mb-12`}>
            Stop digging through city websites and 500-page PDFs. Get instant, sourced answers from municipal codes 
            so you can complete your work faster and with more confidence.
            <span className={`${theme.text.accent}`}> Save hours on every report.</span>
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
            <Link href="/signup">
              <Button size="lg" className={`${theme.button.success} px-8 py-4 text-lg font-exo`}>
                <Calendar className="w-5 h-5 mr-3" />
                Book a 15-Min Demo
                <ArrowRight className="w-5 h-5 ml-3" />
              </Button>
            </Link>
            <Link href="/signup">
              <Button variant="outline" size="lg" className={`${theme.button.secondary} px-8 py-4 text-lg font-exo`}>
                <CheckCircle className="w-5 h-5 mr-3" />
                Start 7-Day Free Trial
              </Button>
            </Link>
          </div>

          {/* Live Stats - Professional Focus */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className={`text-3xl font-orbitron font-bold ${theme.text.accent} mb-2`}>
                <AnimatedCounter end={3} />+ hrs
              </div>
              <div className={`text-sm ${theme.text.secondary} font-space`}>Saved Per Report</div>
            </div>
            <div className="text-center">
              <div className={`text-3xl font-orbitron font-bold ${theme.text.success} mb-2`}>
                <AnimatedCounter end={2500} />+
              </div>
              <div className={`text-sm ${theme.text.secondary} font-space`}>Cities Covered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-orbitron font-bold text-purple-500 dark:text-purple-400 mb-2">
                &lt;<AnimatedCounter end={30} />s
              </div>
              <div className={`text-sm ${theme.text.secondary} font-space`}>Average Response</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-orbitron font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                <AnimatedCounter end={99} />.9%
              </div>
              <div className={`text-sm ${theme.text.secondary} font-space`}>Accuracy Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Chat UI Showcase Section */}
      <section className={`${theme.container} py-16`}>
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-orbitron font-bold bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent mb-4">
              See It In Action
            </h2>
            <p className={`text-lg ${theme.text.secondary} font-space max-w-2xl mx-auto`}>
              Ask any compliance question and get instant, sourced answers with official citations
            </p>
          </div>
          
          <ChatUIShowcase />
        </div>
      </section>

      {/* Features Section - Professional Focus */}
      <section className={`${theme.container} py-24`}>
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-orbitron font-bold bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent mb-6 leading-tight pb-2">
              Built for Professionals
            </h2>
            <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto`}>
              Everything you need to research municipal codes quickly and accurately, 
              with the documentation quality your clients expect.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <Card className={`${theme.card} ${theme.border.blue} text-center`}>
              <CardHeader>
                <div className="w-16 h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4 quantum-glow">
                  <Clock className={`w-8 h-8 ${theme.text.accent}`} />
                </div>
                <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Save Hours Per Report</CardTitle>
              </CardHeader>
              <CardContent>
                <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                  No more hunting through municipal websites. Get comprehensive research 
                  results in seconds, not hours. Focus on analysis, not data gathering.
                </p>
              </CardContent>
            </Card>

            <Card className={`${theme.card} ${theme.border.green} text-center`}>
              <CardHeader>
                <div className="w-16 h-16 bg-green-500/10 dark:bg-green-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Shield className={`w-8 h-8 ${theme.text.success}`} />
                </div>
                <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Professional Documentation</CardTitle>
              </CardHeader>
              <CardContent>
                <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                  Every answer includes official citations and source links. 
                  Professional-grade documentation ready for client reports and permit applications.
                </p>
              </CardContent>
            </Card>

            <Card className={`${theme.card} ${theme.border.purple} text-center`}>
              <CardHeader>
                <div className="w-16 h-16 bg-purple-500/10 dark:bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-purple-500 dark:text-purple-400" />
                </div>
                <CardTitle className={`font-exo text-2xl ${theme.text.primary}`}>Instant Compliance Answers</CardTitle>
              </CardHeader>
              <CardContent>
                <p className={`${theme.text.secondary} font-space leading-relaxed`}>
                  Ask questions in plain English about setbacks, permits, zoning restrictions, 
                  and building codes. Get clear, actionable answers immediately.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>



      {/* Final CTA Section */}
      <section className={`${theme.container} py-24`}>
        <div className="mx-auto max-w-4xl text-center">
          <div className="mb-8">
            <h2 className="text-4xl md:text-5xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-green-600 dark:to-green-400 bg-clip-text text-transparent mb-6">
              Ready to Streamline Your Research?
            </h2>
            <p className={`text-xl ${theme.text.secondary} font-space max-w-3xl mx-auto leading-relaxed`}>
              Join professionals who are already saving hours on every project. 
              Start with our free trial and experience the difference.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12">
            <Link href="/signup">
              <Button size="lg" className={`${theme.button.success} px-10 py-4 text-lg font-exo`}>
                <Calendar className="w-5 h-5 mr-3" />
                Book a 15-Min Demo
                <ArrowRight className="w-5 h-5 ml-3" />
              </Button>
            </Link>
            <Link href="/signup">
              <Button variant="outline" size="lg" className={`${theme.button.secondary} px-10 py-4 text-lg font-exo`}>
                <CheckCircle className="w-5 h-5 mr-3" />
                Start 7-Day Free Trial
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className={`${theme.text.secondary} font-space`}>
              <CheckCircle className={`w-5 h-5 ${theme.text.success} mx-auto mb-2`} />
              <span className="text-sm">No credit card required</span>
            </div>
            <div className={`${theme.text.secondary} font-space`}>
              <CheckCircle className={`w-5 h-5 ${theme.text.success} mx-auto mb-2`} />
              <span className="text-sm">7-day free trial</span>
            </div>
            <div className={`${theme.text.secondary} font-space`}>
              <CheckCircle className={`w-5 h-5 ${theme.text.success} mx-auto mb-2`} />
              <span className="text-sm">Cancel anytime</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
