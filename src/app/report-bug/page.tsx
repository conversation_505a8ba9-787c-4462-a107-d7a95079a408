'use client'

import { useState } from 'react'
import { Bug, AlertTriangle, Info, CheckCircle, Upload, X, HelpCircle, MessageCircle, Mail, Keyboard, Zap } from 'lucide-react'
import Link from 'next/link'

export default function ReportBugPage() {
  const [bugReport, setBugReport] = useState({
    title: '',
    description: '',
    severity: 'medium',
    category: 'general',
    steps: '',
    expected: '',
    actual: '',
    browser: '',
    device: '',
    email: ''
  })
  const [attachments, setAttachments] = useState<File[]>([])
  const [submitted, setSubmitted] = useState(false)
  const [loading, setLoading] = useState(false)
  const [helpMenuOpen, setHelpMenuOpen] = useState(false)

  const severityLevels = [
    {
      id: 'low',
      title: 'Low',
      description: 'Minor issue that doesn&apos;t affect core functionality',
      icon: Info,
      color: 'green'
    },
    {
      id: 'medium',
      title: 'Medium',
      description: 'Issue affects functionality but has workarounds',
      icon: AlertTriangle,
      color: 'yellow'
    },
    {
      id: 'high',
      title: 'High',
      description: 'Major issue that significantly impacts functionality',
      icon: Bug,
      color: 'red'
    },
    {
      id: 'critical',
      title: 'Critical',
      description: 'System is unusable or data loss may occur',
      icon: AlertTriangle,
      color: 'red'
    }
  ]

  const categories = [
    'General',
    'Search & Results',
    'Account & Billing',
    'Performance',
    'Mobile',
    'API',
    'Security'
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // In real implementation, this would upload attachments and submit the bug report
      const formData = new FormData()
      Object.entries(bugReport).forEach(([key, value]) => {
        formData.append(key, value)
      })
      
      attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file)
      })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setSubmitted(true)
    } catch (err) {
      console.error('Failed to submit bug report:', err)
      alert('Failed to submit bug report. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setAttachments(prev => [...prev, ...files])
  }

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index))
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-card rounded-2xl shadow-lg border border-border p-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-green-500/10 rounded-full">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              Bug Report Submitted
            </h2>
            <p className="text-muted-foreground mb-6">
              Thank you for reporting this issue! We&apos;ve received your bug report
              and our team will investigate it promptly.
            </p>
            <div className="bg-primary/5 border border-primary/20 rounded-lg p-4 mb-6">
              <p className="text-sm text-foreground">
                <strong>Ticket ID:</strong> BUG-{Date.now().toString().slice(-6)}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                Save this ID for future reference.
              </p>
            </div>
            <div className="space-y-3">
              <button
                onClick={() => {
                  setSubmitted(false)
                  setBugReport({
                    title: '', description: '', severity: 'medium', category: 'general',
                    steps: '', expected: '', actual: '', browser: '', device: '', email: ''
                  })
                  setAttachments([])
                }}
                className="w-full px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
              >
                Report Another Bug
              </button>
              <Link
                href="/"
                className="block w-full px-4 py-3 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors text-center font-medium"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background relative">
      {/* Help Menu Button */}
      <button
        onClick={() => setHelpMenuOpen(true)}
        className="fixed top-20 right-4 z-40 bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:bg-primary/90 transition-colors"
        aria-label="Open help menu"
      >
        <HelpCircle className="h-5 w-5" />
      </button>

      {/* Slide-out Help Menu */}
      {helpMenuOpen && (
        <>
          <div
            className="fixed inset-0 bg-black/50 z-50"
            onClick={() => setHelpMenuOpen(false)}
          />
          <div className="fixed top-0 right-0 h-full w-80 bg-card border-l border-border shadow-xl z-50 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-card-foreground">Help & Support</h3>
                <button
                  onClick={() => setHelpMenuOpen(false)}
                  className="p-2 hover:bg-muted rounded-lg transition-colors"
                  aria-label="Close help menu"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                <Link
                  href="/faq"
                  className="flex items-center p-3 rounded-lg hover:bg-muted transition-colors"
                  onClick={() => setHelpMenuOpen(false)}
                >
                  <HelpCircle className="h-5 w-5 text-primary mr-3" />
                  <div>
                    <div className="font-medium text-card-foreground">FAQ</div>
                    <div className="text-sm text-muted-foreground">Find answers to common questions</div>
                  </div>
                </Link>

                <Link
                  href="/contact"
                  className="flex items-center p-3 rounded-lg hover:bg-muted transition-colors"
                  onClick={() => setHelpMenuOpen(false)}
                >
                  <Mail className="h-5 w-5 text-primary mr-3" />
                  <div>
                    <div className="font-medium text-card-foreground">Contact Support</div>
                    <div className="text-sm text-muted-foreground">Get help from our team</div>
                  </div>
                </Link>

                <Link
                  href="/feedback"
                  className="flex items-center p-3 rounded-lg hover:bg-muted transition-colors"
                  onClick={() => setHelpMenuOpen(false)}
                >
                  <MessageCircle className="h-5 w-5 text-primary mr-3" />
                  <div>
                    <div className="font-medium text-card-foreground">Send Feedback</div>
                    <div className="text-sm text-muted-foreground">Share your thoughts</div>
                  </div>
                </Link>

                <div className="border-t border-border pt-4">
                  <h4 className="font-medium text-card-foreground mb-3">Keyboard Shortcuts</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Submit form</span>
                      <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+Enter</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Clear form</span>
                      <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+R</kbd>
                    </div>
                  </div>
                </div>

                <div className="border-t border-border pt-4">
                  <h4 className="font-medium text-card-foreground mb-3">Quick Actions</h4>
                  <div className="space-y-2">
                    <button
                      onClick={() => {
                        setBugReport({
                          title: '', description: '', severity: 'medium', category: 'general',
                          steps: '', expected: '', actual: '', browser: '', device: '', email: ''
                        })
                        setAttachments([])
                        setHelpMenuOpen(false)
                      }}
                      className="w-full text-left p-2 rounded-lg hover:bg-muted transition-colors text-sm"
                    >
                      <div className="flex items-center">
                        <Zap className="h-4 w-4 text-primary mr-2" />
                        <span className="text-card-foreground">Clear Form</span>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Header */}
      <div className="bg-card shadow-sm border-b border-border">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-destructive/10 rounded-full">
                <Bug className="h-8 w-8 text-destructive" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-foreground mb-4">
              Report a Bug or Issue
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Use this form to submit detailed information about any technical issues you've encountered.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-card rounded-2xl shadow-lg border border-border overflow-hidden">
          <form onSubmit={handleSubmit}>
            {/* Severity Selection */}
            <div className="p-6 border-b border-border">
              <h2 className="text-xl font-semibold text-card-foreground mb-4">
                How severe is this issue?
              </h2>
              <div className="grid gap-3 grid-cols-2 lg:grid-cols-4">
                {severityLevels.map((level) => {
                  const isSelected = bugReport.severity === level.id
                  const colorClasses = {
                    green: isSelected ? 'border-green-500 bg-green-500/10' : 'border-border hover:border-green-500 hover:bg-muted/50',
                    yellow: isSelected ? 'border-orange-500 bg-orange-500/10' : 'border-border hover:border-orange-500 hover:bg-muted/50',
                    red: isSelected ? 'border-destructive bg-destructive/10' : 'border-border hover:border-destructive hover:bg-muted/50'
                  }

                  return (
                    <button
                      key={level.id}
                      type="button"
                      onClick={() => setBugReport({...bugReport, severity: level.id})}
                      className={`p-4 rounded-xl border-2 transition-all duration-200 hover:scale-[1.02] ${colorClasses[level.color as keyof typeof colorClasses]}`}
                    >
                      <div className="flex flex-col items-center text-center">
                        <div className={`p-2 rounded-lg mb-3 ${
                          isSelected
                            ? level.color === 'green' ? 'bg-green-500/20' :
                              level.color === 'yellow' ? 'bg-orange-500/20' :
                              'bg-destructive/20'
                            : 'bg-muted'
                        }`}>
                          <level.icon className={`h-5 w-5 ${
                            isSelected
                              ? level.color === 'green' ? 'text-green-600' :
                                level.color === 'yellow' ? 'text-orange-600' :
                                'text-destructive'
                              : 'text-muted-foreground'
                          }`} />
                        </div>
                        <h3 className="font-medium text-card-foreground mb-1 text-sm">
                          {level.title}
                        </h3>
                        <p className="text-xs text-muted-foreground leading-tight">
                          {level.description}
                        </p>
                      </div>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Bug Details */}
            <div className="p-6">
              <div className="space-y-6">
                {/* Title and Category */}
                <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-card-foreground mb-2">
                      Bug Title *
                    </label>
                    <input
                      type="text"
                      value={bugReport.title}
                      onChange={(e) => setBugReport({...bugReport, title: e.target.value})}
                      required
                      className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                      placeholder="Brief description of the problem"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-card-foreground mb-2">
                      Category
                    </label>
                    <select
                      value={bugReport.category}
                      onChange={(e) => setBugReport({...bugReport, category: e.target.value})}
                      className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    >
                      {categories.map(category => (
                        <option key={category} value={category.toLowerCase().replace(' & ', '-')}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-card-foreground mb-2">
                    Detailed Description *
                  </label>
                  <textarea
                    value={bugReport.description}
                    onChange={(e) => setBugReport({...bugReport, description: e.target.value})}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none min-h-32"
                    placeholder="Describe the issue in detail. What happened? When did it occur?"
                  />
                </div>

                {/* Steps to Reproduce */}
                <div>
                  <label className="block text-sm font-medium text-card-foreground mb-2">
                    Steps to Reproduce
                  </label>
                  <textarea
                    value={bugReport.steps}
                    onChange={(e) => setBugReport({...bugReport, steps: e.target.value})}
                    rows={6}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none min-h-32"
                    placeholder="1. Go to...&#10;2. Click on...&#10;3. Enter...&#10;4. See error"
                  />
                </div>

                {/* Expected vs Actual */}
                <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-card-foreground mb-2">
                      Expected Behavior
                    </label>
                    <textarea
                      value={bugReport.expected}
                      onChange={(e) => setBugReport({...bugReport, expected: e.target.value})}
                      rows={3}
                      className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none"
                      placeholder="What should have happened?"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-card-foreground mb-2">
                      Actual Behavior
                    </label>
                    <textarea
                      value={bugReport.actual}
                      onChange={(e) => setBugReport({...bugReport, actual: e.target.value})}
                      rows={3}
                      className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none"
                      placeholder="What actually happened?"
                    />
                  </div>
                </div>

                {/* Environment */}
                <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-card-foreground mb-2">
                      Browser/App Version
                    </label>
                    <input
                      type="text"
                      value={bugReport.browser}
                      onChange={(e) => setBugReport({...bugReport, browser: e.target.value})}
                      className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                      placeholder="e.g., Chrome 120.0, Safari 17.1, iOS App 1.2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-card-foreground mb-2">
                      Device/OS
                    </label>
                    <input
                      type="text"
                      value={bugReport.device}
                      onChange={(e) => setBugReport({...bugReport, device: e.target.value})}
                      className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                      placeholder="e.g., iPhone 15, Windows 11, macOS Sonoma"
                    />
                  </div>
                </div>

                {/* Attachments */}
                <div>
                  <label className="block text-sm font-medium text-card-foreground mb-2">
                    Screenshots or Files
                  </label>
                  <p className="text-xs text-muted-foreground mb-3">
                    Attach screenshots, error logs, or other relevant files (PNG, JPG, or PDF, up to 10MB).
                  </p>
                  <div className="border-2 border-dashed border-border rounded-lg p-6">
                    <div className="text-center">
                      <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground mb-2">
                        Upload screenshots, error logs, or other relevant files
                      </p>
                      <input
                        type="file"
                        multiple
                        accept="image/*,.txt,.log,.pdf"
                        onChange={handleFileUpload}
                        className="hidden"
                        id="file-upload"
                      />
                      <label
                        htmlFor="file-upload"
                        className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 cursor-pointer transition-colors shadow-md hover:shadow-lg"
                      >
                        Choose Files
                      </label>
                    </div>

                    {attachments.length > 0 && (
                      <div className="mt-4 space-y-2">
                        {attachments.map((file, index) => (
                          <div key={index} className="flex items-center justify-between bg-muted p-2 rounded">
                            <span className="text-sm text-foreground">{file.name}</span>
                            <button
                              type="button"
                              onClick={() => removeAttachment(index)}
                              className="text-destructive hover:text-destructive/80 transition-colors"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Contact Email */}
                <div>
                  <label className="block text-sm font-medium text-card-foreground mb-2">
                    Email (Optional)
                  </label>
                  <input
                    type="email"
                    value={bugReport.email}
                    onChange={(e) => setBugReport({...bugReport, email: e.target.value})}
                    className="w-full px-4 py-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="<EMAIL>"
                  />
                  <p className="mt-2 text-sm text-muted-foreground">
                    Provide your email if you&apos;d like updates on this bug report.
                  </p>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-4">
                  <button
                    type="submit"
                    disabled={loading || !bugReport.title.trim() || !bugReport.description.trim()}
                    className={`flex items-center px-8 py-3 rounded-lg font-semibold transition-all duration-200 ${
                      loading || !bugReport.title.trim() || !bugReport.description.trim()
                        ? 'bg-muted text-muted-foreground cursor-not-allowed opacity-50'
                        : 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg'
                    }`}
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Bug className="h-4 w-4 mr-2" />
                        Submit Bug Report
                      </>
                    )}
                  </button>
                  {(loading || !bugReport.title.trim() || !bugReport.description.trim()) && (
                    <p className="text-xs text-muted-foreground mt-2 mr-4">
                      All required fields (marked with *) must be filled out before you can submit.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Tips */}
        <div className="mt-6 bg-gray-800 border border-gray-700 rounded-2xl p-6">
          <h3 className="font-semibold text-foreground mb-3">
            Tips for Better Bug Reports
          </h3>
          <ul className="space-y-2 text-muted-foreground list-inside list-disc marker:text-green-400">
            <li>✓ Include specific steps to reproduce the issue</li>
            <li>✓ Attach screenshots or screen recordings when possible</li>
            <li>✓ Mention your browser, device, and operating system</li>
            <li>✓ Describe what you expected to happen vs. what actually happened</li>
            <li>✓ Check if the issue persists in an incognito/private window</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
