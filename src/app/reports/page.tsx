import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default async function ReportsPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has access to professional reports
  const hasAccess = profile.subscription_tier === 'appraiser'

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Professional Reports
            </h1>
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Appraiser Access Required
              </h2>
              <p className="text-gray-600 mb-6">
                Professional report generation is available for Appraiser subscribers only.
              </p>
              <div className="space-y-4">
                <Link href="/pricing">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Upgrade to Appraiser
                  </Button>
                </Link>
                <div>
                  <Link href="/search">
                    <Button variant="outline">
                      Back to Search
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Compliance Reports & Documentation
          </h1>
          <p className="text-gray-600 mb-8">
            Generate regulatory compliance reports, ordinance summaries, and research documentation.
          </p>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
            <p className="text-sm text-yellow-800">
              <strong>Disclaimer:</strong> These are compliance research reports only.
              This tool does not generate professional appraisal reports or USPAP-compliant documentation.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Report Templates */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Report Templates</h2>

              <div className="space-y-4">
                <div className="border border-gray-200 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">Ordinance Summary Report</h3>
                  <p className="text-sm text-gray-600 mb-3">Comprehensive summary of applicable ordinances and regulations</p>
                  <div className="flex space-x-2">
                    <Button className="bg-blue-600 hover:bg-blue-700" disabled>Coming Soon</Button>
                    <Button variant="outline" disabled>Preview Template</Button>
                  </div>
                </div>

                <div className="border border-gray-200 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">Regulatory Research Report</h3>
                  <p className="text-sm text-gray-600 mb-3">Detailed research findings on zoning and building requirements</p>
                  <div className="flex space-x-2">
                    <Button className="bg-blue-600 hover:bg-blue-700" disabled>Coming Soon</Button>
                    <Button variant="outline" disabled>Preview Template</Button>
                  </div>
                </div>

                <div className="border border-gray-200 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">Compliance Summary</h3>
                  <p className="text-sm text-gray-600 mb-3">Property compliance analysis with regulatory findings</p>
                  <div className="flex space-x-2">
                    <Button className="bg-blue-600 hover:bg-blue-700" disabled>Coming Soon</Button>
                    <Button variant="outline" disabled>Preview Template</Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Report Features */}
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Report Features</h2>

              <div className="bg-gray-50 p-6 rounded-lg">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-700">Professional PDF formatting</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-700">Custom branding and logos</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-700">Digital signatures</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-700">Photo integration</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-700">Automated data population</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-700">Export to multiple formats</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Reports */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Recent Reports
            </h2>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="text-center text-gray-500">
                <p>No reports generated yet.</p>
                <p className="text-sm mt-1">Create your first professional report using the templates above.</p>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <Link href="/market-analysis">
              <Button variant="outline">
                Market Analysis
              </Button>
            </Link>
            <Link href="/historical-data">
              <Button className="bg-green-600 hover:bg-green-700">
                Historical Data
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
