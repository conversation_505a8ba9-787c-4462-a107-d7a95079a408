import Link from 'next/link'
import { ResetPasswordForm } from '@/components/auth/ResetPasswordForm'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { OrdrlyLogo } from '@/components/ui/ordrly-logo'
import { createServerClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

export default async function ResetPasswordPage({
  searchParams,
}: {
  searchParams: Promise<{
    error?: string;
    message?: string;
    error_code?: string;
    error_description?: string;
    access_denied?: string;
  }>
}) {
  const params = await searchParams

  // Check if user has a valid session for password reset
  // This prevents people from accessing the reset page without a valid reset link
  const supabase = await createServerClient()
  const { data: { user }, error: sessionError } = await supabase.auth.getUser()

  // If no valid session and no error parameters, redirect to forgot password
  if (!user && !params.error && !params.error_code && !params.access_denied) {
    redirect('/forgot-password?error=Please use a valid reset link from your email.')
  }

  // Handle specific error cases from URL parameters
  let displayError = params.error
  if (params.error_code === 'otp_expired' || params.error_description?.includes('expired')) {
    displayError = 'Your reset link has expired. Please request a new one.'
  } else if (params.access_denied || params.error === 'access_denied') {
    displayError = 'Access denied. Your reset link may be invalid or expired.'
  }

  return (
    <div className="min-h-[calc(100vh-4rem)] bg-gradient-to-br from-background via-background/95 to-muted/20 relative overflow-hidden flex items-center justify-center p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 dark:bg-primary/20 rounded-full filter blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary/15 dark:bg-primary/25 rounded-full filter blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-primary/5 dark:bg-primary/10 rounded-full filter blur-3xl animate-pulse delay-500"></div>
      </div>

      <Card className="w-full max-w-md relative z-10 bg-card/50 backdrop-blur-sm border border-border shadow-2xl">
        <CardHeader className="space-y-1 text-center">
          <div className="flex justify-center mb-6">
            <OrdrlyLogo size="xl" className="w-16 h-16" />
          </div>
          <CardTitle className="text-3xl font-orbitron font-bold bg-gradient-to-r from-foreground via-primary to-primary/80 bg-clip-text text-transparent">
            Set New Password
          </CardTitle>
          <CardDescription className="text-muted-foreground font-space">
            Enter your new password below. Make sure it's at least 6 characters long.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {displayError && (
            <Alert className="border-red-500/50 bg-red-500/10 dark:bg-red-500/20 backdrop-blur-sm">
              <AlertDescription className="text-red-500 dark:text-red-400 font-space">
                <div className="space-y-3">
                  <p>{displayError}</p>
                  <Link href="/forgot-password">
                    <Button variant="outline" size="sm" className="font-space">
                      Request New Reset Link
                    </Button>
                  </Link>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {params.message && (
            <Alert className="border-green-500/50 bg-green-500/10 dark:bg-green-500/20 backdrop-blur-sm">
              <AlertDescription className="text-green-600 dark:text-green-400 font-space">{params.message}</AlertDescription>
            </Alert>
          )}

          <ResetPasswordForm />

          <div className="text-center">
            <Link
              href="/login"
              className="text-sm text-primary hover:text-primary/80 transition-colors font-space"
            >
              ← Back to sign in
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
