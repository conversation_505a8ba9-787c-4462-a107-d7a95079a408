'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { SelectRoot, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

import { Trash2, Edit, Share, Folder, Plus, Search, Star, MapPin, Calendar } from 'lucide-react'

interface SavedSearch {
  id: string
  name: string
  address: string
  rule_type: string
  description?: string
  folder_id?: string
  tags?: string[]
  is_public: boolean
  share_token?: string
  created_at: string
  updated_at: string
}

interface SearchFolder {
  id: string
  name: string
  description?: string
  color: string
  position: number
  created_at: string
}

export default function SavedSearchesPage() {
  const [loading, setLoading] = useState(true)
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([])
  const [folders, setFolders] = useState<SearchFolder[]>([])
  const [filteredSearches, setFilteredSearches] = useState<SavedSearch[]>([])
  const [searchFilter, setSearchFilter] = useState('')
  const [folderFilter, setFolderFilter] = useState('all')
  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingSearch, setEditingSearch] = useState<SavedSearch | null>(null)
  const [newFolderName, setNewFolderName] = useState('')
  const [newFolderDescription, setNewFolderDescription] = useState('')
  const [newFolderColor, setNewFolderColor] = useState('blue')
  const router = useRouter()
  const supabase = createClient()

  const loadSavedSearches = async () => {
    try {
      const response = await fetch('/api/saved-searches')
      if (response.ok) {
        const data = await response.json()
        setSavedSearches(data.searches || [])
      }
    } catch (error) {
      console.error('Failed to load saved searches:', error)
    }
  }

  const loadFolders = async () => {
    try {
      const response = await fetch('/api/saved-searches/folders')
      if (response.ok) {
        const data = await response.json()
        setFolders(data.folders || [])
      }
    } catch (error) {
      console.error('Failed to load folders:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterSearches = useCallback(() => {
    let filtered = [...savedSearches]

    // Search filter
    if (searchFilter) {
      filtered = filtered.filter(search =>
        search.name.toLowerCase().includes(searchFilter.toLowerCase()) ||
        search.address.toLowerCase().includes(searchFilter.toLowerCase()) ||
        search.description?.toLowerCase().includes(searchFilter.toLowerCase())
      )
    }

    // Folder filter
    if (folderFilter !== 'all') {
      if (folderFilter === 'none') {
        filtered = filtered.filter(search => !search.folder_id)
      } else {
        filtered = filtered.filter(search => search.folder_id === folderFilter)
      }
    }

    setFilteredSearches(filtered)
  }, [savedSearches, searchFilter, folderFilter])

  const checkAuthAndLoadData = useCallback(async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        router.push('/login')
        return
      }

      await Promise.all([loadSavedSearches(), loadFolders()])
    } catch (err) {
      console.error('Auth check error:', err)
      router.push('/login')
    }
  }, [router, supabase.auth])

  useEffect(() => {
    checkAuthAndLoadData()
  }, [checkAuthAndLoadData])

  useEffect(() => {
    filterSearches()
  }, [savedSearches, searchFilter, folderFilter, filterSearches])

  const createFolder = async () => {
    try {
      const response = await fetch('/api/saved-searches/folders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newFolderName,
          description: newFolderDescription,
          color: newFolderColor
        })
      })

      if (response.ok) {
        await loadFolders()
        setShowNewFolderDialog(false)
        setNewFolderName('')
        setNewFolderDescription('')
        setNewFolderColor('blue')
      }
    } catch (error) {
      console.error('Failed to create folder:', error)
    }
  }

  const updateSearch = async () => {
    if (!editingSearch) return

    try {
      const response = await fetch(`/api/saved-searches/${editingSearch.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: editingSearch.name,
          description: editingSearch.description,
          folder_id: editingSearch.folder_id,
          tags: editingSearch.tags,
          is_public: editingSearch.is_public
        })
      })

      if (response.ok) {
        await loadSavedSearches()
        setShowEditDialog(false)
        setEditingSearch(null)
      }
    } catch (error) {
      console.error('Failed to update search:', error)
    }
  }

  const deleteSearch = async (id: string) => {
    if (!confirm('Are you sure you want to delete this saved search?')) {
      return
    }

    try {
      const response = await fetch(`/api/saved-searches/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setSavedSearches(prev => prev.filter(search => search.id !== id))
      }
    } catch (error) {
      console.error('Failed to delete search:', error)
    }
  }

  const shareSearch = async (search: SavedSearch) => {
    if (search.share_token) {
      const shareUrl = `${window.location.origin}/shared-search/${search.share_token}`
      await navigator.clipboard.writeText(shareUrl)
      alert('Share link copied to clipboard!')
    } else {
      // Generate share token
      try {
        const response = await fetch(`/api/saved-searches/${search.id}/share`, {
          method: 'POST'
        })

        if (response.ok) {
          const data = await response.json()
          const shareUrl = `${window.location.origin}/shared-search/${data.share_token}`
          await navigator.clipboard.writeText(shareUrl)
          alert('Share link copied to clipboard!')
          await loadSavedSearches()
        }
      } catch (error) {
        console.error('Failed to generate share link:', error)
      }
    }
  }

  const getFolderName = (folderId?: string) => {
    if (!folderId) return 'Uncategorized'
    const folder = folders.find(f => f.id === folderId)
    return folder?.name || 'Unknown Folder'
  }

  const getFolderColor = (folderId?: string) => {
    if (!folderId) return 'gray'
    const folder = folders.find(f => f.id === folderId)
    return folder?.color || 'gray'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading saved searches...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background py-16 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Saved Searches
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Organize and manage your saved compliance searches. Access your bookmarked results and share searches with others.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <h2 className="text-2xl font-semibold text-foreground">Your Searches</h2>
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                {savedSearches.length} saved
              </Badge>
            </div>
            <div className="flex space-x-3">
              <Dialog open={showNewFolderDialog} onOpenChange={setShowNewFolderDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="flex items-center border-border hover:bg-accent">
                    <Folder className="h-4 w-4 mr-2" />
                    New Folder
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Folder</DialogTitle>
                    <DialogDescription>
                      Organize your saved searches into folders
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="folder-name">Folder Name</Label>
                      <Input
                        id="folder-name"
                        value={newFolderName}
                        onChange={(e) => setNewFolderName(e.target.value)}
                        placeholder="Enter folder name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="folder-description">Description (Optional)</Label>
                      <Textarea
                        id="folder-description"
                        value={newFolderDescription}
                        onChange={(e) => setNewFolderDescription(e.target.value)}
                        placeholder="Enter folder description"
                      />
                    </div>
                    <div>
                      <Label htmlFor="folder-color">Color</Label>
                      <SelectRoot value={newFolderColor} onValueChange={setNewFolderColor}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="blue">Blue</SelectItem>
                          <SelectItem value="green">Green</SelectItem>
                          <SelectItem value="red">Red</SelectItem>
                          <SelectItem value="yellow">Yellow</SelectItem>
                          <SelectItem value="purple">Purple</SelectItem>
                        </SelectContent>
                      </SelectRoot>
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={() => setShowNewFolderDialog(false)}>
                        Cancel
                      </Button>
                      <Button onClick={createFolder} disabled={!newFolderName.trim()}>
                        Create Folder
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                onClick={() => router.push('/search')}
                className="bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Search
              </Button>
            </div>
          </div>

          {/* Filters */}
          <Card className="mb-8 bg-card border-border shadow-premium animate-slide-up">
            <CardHeader>
              <CardTitle className="flex items-center text-card-foreground">
                <Search className="h-5 w-5 mr-2 text-primary" />
                Filter Searches
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-card-foreground mb-2">
                    Search
                  </label>
                  <Input
                    type="search"
                    placeholder="Search saved searches..."
                    value={searchFilter}
                    onChange={(e) => setSearchFilter(e.target.value)}
                    className="w-full bg-background border-border focus:border-primary focus:ring-primary"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-card-foreground mb-2">
                    Folder
                  </label>
                  <SelectRoot value={folderFilter} onValueChange={setFolderFilter}>
                    <SelectTrigger className="bg-background border-border">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Folders</SelectItem>
                      <SelectItem value="none">Uncategorized</SelectItem>
                      {folders.map(folder => (
                        <SelectItem key={folder.id} value={folder.id}>
                          {folder.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </SelectRoot>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Saved Searches */}
          <div className="space-y-6">
            {filteredSearches.length === 0 ? (
              <Card className="bg-card border-border shadow-premium animate-fade-in">
                <CardContent className="text-center py-16">
                  <Star className="h-16 w-16 text-muted-foreground/50 mx-auto mb-6" />
                  <h3 className="text-xl font-semibold text-card-foreground mb-3">No saved searches found</h3>
                  <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                    {savedSearches.length === 0
                      ? "Save your first compliance search to access it quickly later"
                      : "Try adjusting your filters to see more results"
                    }
                  </p>
                  <Button
                    onClick={() => router.push('/search')}
                    className="bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    Start Searching
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredSearches.map((search) => (
                <Card key={search.id} className="bg-card border-border shadow-premium hover:shadow-premium-xl transition-all duration-300 animate-slide-up">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <Star className="h-5 w-5 text-primary" />
                          <h3 className="font-semibold text-card-foreground text-lg">{search.name}</h3>
                          <Badge
                            variant="secondary"
                            className="bg-primary/10 text-primary"
                          >
                            {getFolderName(search.folder_id)}
                          </Badge>
                          {search.is_public ? (
                            <Badge variant="outline" className="bg-success/10 text-success border-success/20">
                              Public Share
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-muted text-muted-foreground">
                              Private
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {search.address}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(search.created_at).toLocaleDateString()}
                          </div>
                        </div>
                        {search.description && (
                          <p className="text-sm text-muted-foreground mb-3">{search.description}</p>
                        )}
                        {search.tags && search.tags.length > 0 && (
                          <div className="flex flex-wrap gap-2 mt-3">
                            {search.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs bg-accent text-accent-foreground">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/search?address=${encodeURIComponent(search.address)}`)}
                          className="border-border hover:bg-accent"
                        >
                          Search Again
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => shareSearch(search)}
                          data-testid="share-search-button"
                          title="Share this search publicly or privately"
                          className="hover:bg-accent"
                        >
                          <Share className="h-4 w-4 mr-1" />
                          Share
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingSearch(search)
                            setShowEditDialog(true)
                          }}
                          className="hover:bg-accent"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteSearch(search.id)}
                          className="text-destructive hover:text-destructive hover:bg-destructive/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* CTA Section */}
          {savedSearches.length > 0 && (
            <div className="mt-16 text-center">
              <div className="bg-card border border-border rounded-2xl p-8 max-w-4xl mx-auto shadow-premium animate-fade-in">
                <h2 className="text-2xl font-bold text-card-foreground mb-4">
                  Need more searches?
                </h2>
                <p className="text-muted-foreground mb-8">
                  Upgrade to Pro for unlimited searches, AI assistance, and advanced features.
                </p>
                <Button
                  onClick={() => router.push('/pricing')}
                  size="lg"
                  className="px-8 py-4 text-xl font-bold shadow-xl hover:shadow-2xl transition-all duration-200 hover:scale-[1.02]"
                >
                  View Pricing Plans
                </Button>
              </div>
            </div>
          )}

          {/* Edit Search Dialog */}
          <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Saved Search</DialogTitle>
                <DialogDescription>
                  Update your saved search details
                </DialogDescription>
              </DialogHeader>
              {editingSearch && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="search-name">Name</Label>
                    <Input
                      id="search-name"
                      value={editingSearch.name}
                      onChange={(e) => setEditingSearch({
                        ...editingSearch,
                        name: e.target.value
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="search-description">Description</Label>
                    <Textarea
                      id="search-description"
                      value={editingSearch.description || ''}
                      onChange={(e) => setEditingSearch({
                        ...editingSearch,
                        description: e.target.value
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="search-folder">Folder</Label>
                    <SelectRoot
                      value={editingSearch.folder_id || 'none'}
                      onValueChange={(value) => setEditingSearch({
                        ...editingSearch,
                        folder_id: value === 'none' ? undefined : value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">Uncategorized</SelectItem>
                        {folders.map(folder => (
                          <SelectItem key={folder.id} value={folder.id}>
                            {folder.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </SelectRoot>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={updateSearch}>
                      Save Changes
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  )
}
