import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Property Compliance Search - Check Building Regulations | Ordrly',
  description: 'Search property compliance requirements for any US address. Get instant access to building permits, zoning rules, setbacks, and local ordinances before you build.',
  keywords: ['property search', 'building permits', 'zoning compliance', 'ordinance lookup', 'construction regulations', 'property compliance check'],
  openGraph: {
    title: 'Property Compliance Search - Check Building Regulations | Ordrly',
    description: 'Search property compliance requirements for any US address. Get instant access to building permits, zoning rules, setbacks, and local ordinances.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Property Compliance Search - Check Building Regulations | Ordrly',
    description: 'Search property compliance requirements for any US address. Get instant access to building permits and zoning rules.',
  },
}

export default function SearchLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
