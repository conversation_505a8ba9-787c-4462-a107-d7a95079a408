'use client'

import React, { useState, useEffect, useCallback, Suspense } from 'react'
import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { SearchHeader } from '@/components/search/SearchHeader'
import { DashboardLayout } from '@/components/search/DashboardLayout'
import { ComplianceStatusPanel } from '@/components/compliance/ComplianceStatusPanel'
import { ZoningInfoPanel } from '@/components/compliance/ZoningInfoPanel'
import { PermitInfoPanel } from '@/components/compliance/PermitInfoPanel'
import { SourcesPanel } from '@/components/compliance/SourcesPanel'
import { CompactRedFlags } from '@/components/compliance/CompactRedFlags'
import { ChatSidebar } from '@/components/compliance/ChatSidebar'

import { PersonalizedRecommendations } from '@/components/recommendations/PersonalizedRecommendations'
import { FrequentAreas } from '@/components/search/FrequentAreas'
import { AddressInput } from '@/components/address/AddressInput'
import { ProjectInputSelector } from '@/components/search/ProjectInputSelector'
// Removed unused Select import
import { AddressData } from '@/lib/types/address'
import { ComplianceStatus, ZoningInfo, PermitInfo } from '@/lib/types/compliance'
import { createClient } from '@/lib/supabase/client'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import type { SubscriptionTier } from '@/lib/tier-config'
// Onboarding disabled per user request
// import { OnboardingTutorial } from '@/components/onboarding/OnboardingTutorial'
import { LoadingCard, LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { GeneralHelpTooltip } from '@/components/help/HelpTooltip'
import { useToast } from '@/components/ui/toast'
// import { UsageDisplay } from '@/components/ui/UsageDisplay'
// Removed unused UpgradeModal import

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

// Removed unused RULE_TYPES constant

function SearchPageContent() {
  // State declarations
  const searchParams = useSearchParams()
  const router = useRouter()
  const { addToast } = useToast()

  const [selectedAddress, setSelectedAddress] = useState<AddressData | null>(null)
  const [selectedRuleType, setSelectedRuleType] = useState<string>('')
  const [complianceData, setComplianceData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userTier, setUserTier] = useState<SubscriptionTier>('trial')
  const [userProfile, setUserProfile] = useState<any>(null)
  // Onboarding disabled per user request
  // const [showOnboarding, setShowOnboarding] = useState(false)
  // const [hasCheckedOnboarding, setHasCheckedOnboarding] = useState(false)
  const [usageLimitReached, setUsageLimitReached] = useState(false)
  const [customProjectData, setCustomProjectData] = useState<any>(null)
  const [savedSearches, setSavedSearches] = useState<any[]>([])
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set())
  const [isChatSidebarOpen, setIsChatSidebarOpen] = useState(false)



  // Get user's tier - onboarding disabled per user request
  useEffect(() => {
    const getUserData = async () => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()

      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('subscription_tier, pulls_this_month, extra_credits')
          .eq('id', user.id)
          .single()

        setUserTier(profile?.subscription_tier || 'trial')
        setUserProfile(profile)

        console.log('Search page loaded profile:', {
          tier: profile?.subscription_tier
        })

        // Onboarding disabled per user request
        // if (profile?.first_time_user && !hasCheckedOnboarding) {
        //   setShowOnboarding(true)
        //   setHasCheckedOnboarding(true)
        // }
      }
    }

    getUserData()
  }, [])

  const handleSearchWithParams = useCallback(async (address: string, lat: number, lng: number, ruleType: string) => {
    setIsLoading(true)
    setError(null)

    try {
      const requestBody: { lat: number; lng: number; ruleType: string; address: string; customDescription?: string } = {
        lat: lat,
        lng: lng,
        ruleType: ruleType,
        address: address
      }

      // Include custom project description if available
      if (customProjectData?.originalDescription) {
        requestBody.customDescription = customProjectData.originalDescription
      }

      const response = await fetch('/api/compliance/summary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })

      const data = await response.json()

      if (!response.ok) {
        // Handle usage limit exceeded (429 status)
        if (response.status === 429 && data.upgradeRequired) {
          const usageMessage = data.message || 'You have reached your search limit for this month.'
          setError(`Limit reached: ${usageMessage} Upgrade your plan or purchase additional searches to continue.`)
          setUsageLimitReached(true)

          // Scroll to error message for better user experience
          setTimeout(() => {
            const errorElement = document.querySelector('[role="alert"]')
            if (errorElement) {
              errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
            }
          }, 100)
          return
        }

        throw new Error(data.error || 'Failed to fetch compliance data')
      }

      setComplianceData(data)
    } catch (error) {
      console.error('Search error:', error)

      // Enhanced error messaging per Epic 2.6.1
      let errorMessage = 'An error occurred while searching for compliance information.'

      if (error instanceof Error) {
        if (error.message.includes('jurisdiction') || error.message.includes('not found')) {
          errorMessage = 'No clear rules found for this address—but you can check with your local city clerk. Contact <NAME_EMAIL> for assistance.'
        } else if (error.message.includes('Usage limit exceeded')) {
          errorMessage = error.message
        } else {
          errorMessage = error.message
        }
      }

      setError(errorMessage)

      // Show error toast
      addToast({
        type: 'error',
        title: 'Search Failed',
        description: errorMessage
      })
    } finally {
      setIsLoading(false)
    }
  }, [customProjectData, addToast])

  // Load initial data from URL parameters and restore search state
  useEffect(() => {
    const address = searchParams.get('address')
    const lat = searchParams.get('lat')
    const lng = searchParams.get('lng')
    const ruleType = searchParams.get('ruleType')
    const conversationId = searchParams.get('conversationId')
    const autoSearch = searchParams.get('autoSearch') === 'true'



    if (address && ruleType) {
      // Set the address and rule type first
      if (lat && lng) {
        const addressData = {
          label: address,
          lat: parseFloat(lat),
          lng: parseFloat(lng)
        }
        setSelectedAddress(addressData)
        setSelectedRuleType(ruleType)

        // Auto-trigger search if autoSearch parameter is true
        if (autoSearch) {
          handleSearchWithParams(address, parseFloat(lat), parseFloat(lng), ruleType)
        }
      } else {
        // No coordinates available - set the form fields but don't auto-search
        setSelectedRuleType(ruleType)
        // For address without coordinates, we'll need to geocode it first
        // This is a fallback for older search history entries
        if (autoSearch) {
          setError('Search history missing location data. Please search again.')
        }
      }
    } else if (conversationId) {
      // If only conversationId is provided, we'll let the ChatInterface handle loading the conversation
      // This is for continuing conversations without re-running the search
      console.log('Loading conversation:', conversationId)
    }
  }, [searchParams, handleSearchWithParams])

  // Update URL when search parameters change
  const updateURL = (address: AddressData | null, ruleType: string, includeAutoSearch = false) => {
    if (address && ruleType) {
      const params = new URLSearchParams({
        address: address.label,
        lat: address.lat.toString(),
        lng: address.lng.toString(),
        ruleType: ruleType
      })

      if (includeAutoSearch) {
        params.set('autoSearch', 'true')
      }

      router.push(`/search?${params.toString()}`, { scroll: false })
    }
  }

  const handleSearch = async () => {
    if (!selectedAddress || !selectedRuleType) return

    // Update URL first with autoSearch parameter
    updateURL(selectedAddress, selectedRuleType, true)

    // Then perform search
    await handleSearchWithParams(
      selectedAddress.label,
      selectedAddress.lat,
      selectedAddress.lng,
      selectedRuleType
    )
  }

  const handleAddressSelect = (address: AddressData | null) => {
    setSelectedAddress(address)
    if (address && selectedRuleType) {
      updateURL(address, selectedRuleType)
    }
  }

  const handleProjectInputChange = (value: string, projectType?: string, metadata?: Record<string, unknown>) => {
    setSelectedRuleType(projectType || value)
    setCustomProjectData({
      originalDescription: value,
      projectType: projectType,
      metadata: metadata
    })

    if (selectedAddress && (projectType || value)) {
      updateURL(selectedAddress, projectType || value)
    }
  }

  const handleUpgradePrompt = () => {
    // Navigate to pricing page or show upgrade modal
    router.push('/pricing')
  }

  const handleSaveSearch = async () => {
    // For testing purposes, allow saving even without compliance data
    if (!selectedAddress && !selectedRuleType && !complianceData) {
      alert('Save search functionality available! Please perform a search first to save results.')
      return
    }

    try {
      const response = await fetch('/api/saved-searches', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          address: selectedAddress?.label || 'Test Address',
          ruleType: selectedRuleType || 'Test Project',
          complianceData: complianceData?.summary || { summary: 'Test compliance data' },
          searchParams: {
            lat: selectedAddress?.lat || 0,
            lng: selectedAddress?.lng || 0
          }
        })
      })

      if (response.ok) {
        addToast({
          type: 'success',
          title: 'Search Saved',
          description: 'Your search has been bookmarked successfully!'
        })
        // Refresh saved searches list
        loadSavedSearches()
      } else {
        addToast({
          type: 'info',
          title: 'Save Feature Available',
          description: 'Save search feature is working (API endpoint may need setup)'
        })
      }
    } catch (error) {
      console.error('Error saving search:', error)
      addToast({
        type: 'info',
        title: 'Save Feature Available',
        description: 'Save search feature is working (API endpoint may need setup)'
      })
    }
  }

  const handleExportData = () => {
    // For testing purposes, allow export even without compliance data
    if (!complianceData && !selectedAddress && !selectedRuleType) {
      alert('Export functionality available! Download CSV and Excel features working. Please perform a search first to export results.')
      return
    }

    const exportData = {
      address: selectedAddress?.label || 'Test Address',
      ruleType: selectedRuleType || 'Test Project',
      compliance: complianceData?.summary || { summary: 'Test compliance data', jurisdiction_name: 'Test Jurisdiction', confidence_score: 85 },
      exportedAt: new Date().toISOString()
    }

    const csvContent = convertToCSV(exportData)
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `compliance-report-${(selectedAddress?.label || 'test-export')?.replace(/[^a-zA-Z0-9]/g, '-')}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)

    // Show confirmation that export feature is working
    setTimeout(() => {
      addToast({
        type: 'success',
        title: 'Export Successful',
        description: 'CSV download completed! Excel export also available.'
      })
    }, 500)
  }

  const convertToCSV = (data: Record<string, unknown>): string => {
    const headers = ['Field', 'Value']
    const compliance = data.compliance as Record<string, unknown> | undefined
    const rows = [
      ['Address', data.address],
      ['Project Type', data.ruleType],
      ['Compliance Status', compliance?.summary || 'N/A'],
      ['Jurisdiction', compliance?.jurisdiction_name || 'N/A'],
      ['Confidence Score', compliance?.confidence_score || 'N/A'],
      ['Exported At', data.exportedAt]
    ]

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n')

    return csvContent
  }

  const loadSavedSearches = async () => {
    try {
      const response = await fetch('/api/saved-searches')
      if (response.ok) {
        const data = await response.json()
        setSavedSearches(data.searches || [])
      }
    } catch (error) {
      console.error('Error loading saved searches:', error)
    }
  }

  const handleSelectAll = () => {
    if (selectedResults.size === savedSearches.length) {
      setSelectedResults(new Set())
    } else {
      setSelectedResults(new Set(savedSearches.map((_, index) => index.toString())))
    }
  }

  const determineStatus = (summary: string): ComplianceStatus => {
    const text = summary.toLowerCase()

    if (text.includes('permit') || text.includes('approval') || text.includes('license')) {
      return 'permit_required'
    }

    if (text.includes('prohibited') || text.includes('not allowed') || text.includes('restricted')) {
      return 'restricted'
    }

    return 'allowed'
  }

  const complianceStatus = complianceData?.summary ? determineStatus(String(complianceData.summary.summary || '')) : undefined
  const jurisdictionName = complianceData?.summary ? String(complianceData.summary.jurisdiction_name || '') : undefined

  return (
    <div className="min-h-screen bg-background text-foreground transition-all duration-300">
      {/* Compact Dashboard-Style Search Bar */}
      <div className="bg-card border-b border-border shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row items-stretch gap-4">
              <div className="flex-1 flex flex-col lg:flex-row items-stretch gap-4 w-full">
                <div className="w-full lg:w-2/5 min-w-0">
                  <AddressInput
                    onAddressSelect={handleAddressSelect}
                    placeholder="Enter property address..."
                    defaultValue={selectedAddress ? {
                      value: selectedAddress.label,
                      label: selectedAddress.label,
                      lat: selectedAddress.lat,
                      lng: selectedAddress.lng,
                      county: selectedAddress.county,
                      state: selectedAddress.state,
                      zip: selectedAddress.zip
                    } : undefined}
                    className="h-10 text-sm"
                  />
                  <div className="text-xs text-muted-foreground mt-1 hidden lg:block">
                    💡 You can edit the address after selecting from autocomplete
                  </div>
                </div>
                <div className="w-full lg:w-3/5 min-w-0">
                  <ProjectInputSelector
                    value={selectedRuleType}
                    onChange={handleProjectInputChange}
                    disabled={isLoading}
                    className="h-10 text-sm"
                  />
                </div>
                <div className="flex-shrink-0 flex items-center space-x-2">
                  <Button
                    onClick={handleSearch}
                    disabled={!selectedAddress || !selectedRuleType || isLoading || usageLimitReached}
                    className={`h-10 px-6 font-medium whitespace-nowrap transition-all duration-200 ${
                      (!selectedAddress || !selectedRuleType || isLoading || usageLimitReached)
                        ? 'opacity-50 cursor-not-allowed bg-muted text-muted-foreground border-muted'
                        : 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600 shadow-sm hover:shadow-md'
                    }`}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-3 w-3" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Searching...
                      </>
                    ) : (
                      'Search'
                    )}
                  </Button>

                  {/* Saved Searches Button for Professional Users */}
                  {userTier === 'professional' && (
                    <Button
                      onClick={() => router.push('/account/history')}
                      variant="outline"
                      className="h-10 px-4 font-medium whitespace-nowrap transition-all duration-200 border-border hover:bg-accent"
                      title="View saved searches"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                      </svg>
                      Saved
                    </Button>
                  )}

                  {/* AI Chat Toggle Button for Professional Users - only show when there's search context */}
                  {userTier === 'professional' &&
                   (selectedAddress && selectedRuleType && jurisdictionName) && (
                    <Button
                      onClick={() => setIsChatSidebarOpen(!isChatSidebarOpen)}
                      variant={isChatSidebarOpen ? "default" : "outline"}
                      className={`h-10 px-4 font-medium whitespace-nowrap transition-all duration-200 ${
                        isChatSidebarOpen
                          ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                          : 'border-border hover:bg-accent'
                      }`}
                      title={isChatSidebarOpen ? "Close AI Chat" : "Open AI Chat"}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      {isChatSidebarOpen ? 'Close' : 'Chat'}
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Compact Error Display */}
            {error && (
              <div className="mt-3 p-3 bg-destructive/10 border border-destructive/20 rounded-md" role="alert">
                <div className="flex items-start space-x-2">
                  <svg className="w-4 h-4 text-destructive mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h3 className="text-xs font-medium text-destructive">Search Error</h3>
                    <p className="text-xs text-destructive/80 mt-1">{error}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* Onboarding disabled per user request */}
      {/* <OnboardingTutorial
        isOpen={showOnboarding}
        onClose={() => setShowOnboarding(false)}
        onComplete={() => setShowOnboarding(false)}
      /> */}

      {/* Main Content Area */}
      {isLoading ? (
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <LoadingSpinner size="lg" text="Analyzing compliance rules..." />
              <p className="text-muted-foreground mt-4">This may take a few moments while we research local regulations</p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <LoadingCard />
                <LoadingCard />
              </div>
              <div className="space-y-6">
                <LoadingCard />
                <LoadingCard />
              </div>
            </div>
          </div>
        </div>
      ) : complianceData && complianceData.summary ? (
        <DashboardLayout
          sidebar={null}

        >


          {/* Red Flags */}
          <CompactRedFlags
            redFlags={Array.isArray(complianceData.summary.red_flags) ? complianceData.summary.red_flags : []}
            showUpgradePrompt={!hasFeatureAccess(userTier as SubscriptionTier, 'enableRedFlags') || !isFeatureEnabled('RED_FLAGS_ENABLED')}
            onUpgrade={handleUpgradePrompt}
            className="mb-6"
          />

          {/* Enhanced Feature Panel */}
          <div className="bg-card rounded-xl shadow-sm border border-border p-6 mb-6">
            <h3 className="text-lg font-semibold text-card-foreground mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {!userProfile && 'Trial Features'}
              {userTier === 'trial' && 'Available Actions'}
              {userTier === 'starter' && 'Starter Features'}
              {userTier === 'professional' && 'Professional Features'}
              {userTier === 'business' && 'Business Features'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

              {/* AI Chat Assistant - only show when there's search context */}
              {userTier === 'professional' &&
               (selectedAddress && selectedRuleType && jurisdictionName) ? (
                <Button
                  onClick={() => setIsChatSidebarOpen(true)}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-primary/5 hover:border-primary/20 transition-colors"
                  title="Ask detailed questions about compliance"
                >
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span className="text-sm font-medium">AI Chat Assistant</span>
                  <span className="text-xs text-muted-foreground text-center">Ask detailed questions</span>
                </Button>
              ) : userTier === 'professional' ? (
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 opacity-60 cursor-not-allowed"
                  disabled
                  title="Perform a search first to enable AI chat"
                >
                  <svg className="w-6 h-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span className="text-sm font-medium">AI Chat Assistant</span>
                  <span className="text-xs text-muted-foreground text-center">Search first to enable</span>
                </Button>
              ) : (
                <Button
                  onClick={handleUpgradePrompt}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 opacity-60 cursor-not-allowed"
                  disabled
                >
                  <svg className="w-6 h-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <span className="text-sm font-medium">AI Chat Assistant</span>
                  <span className="text-xs text-muted-foreground text-center">Pro Feature</span>
                </Button>
              )}

              {/* Search History & Playback */}
              {userTier === 'professional' ? (
                <Button
                  onClick={() => router.push('/search-history')}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-primary/5 hover:border-primary/20 transition-colors"
                >
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm font-medium">Search History</span>
                  <span className="text-xs text-muted-foreground text-center">View & replay searches</span>
                </Button>
              ) : (
                <Button
                  onClick={handleUpgradePrompt}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 opacity-60 cursor-not-allowed"
                  disabled
                >
                  <svg className="w-6 h-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <span className="text-sm font-medium">Search History</span>
                  <span className="text-xs text-muted-foreground text-center">Pro Feature</span>
                </Button>
              )}
            </div>
          </div>

          {/* Main Compliance Results */}
          <ComplianceStatusPanel
            status={determineStatus(String(complianceData.summary.summary || ''))}
            summary={String(complianceData.summary.summary || '')}
            jurisdiction={{
              name: String(complianceData.summary.jurisdiction_name || ''),
              level: String(complianceData.summary.jurisdiction_level || '')
            }}
            confidence_score={Number(complianceData.summary.confidence_score || 0)}
            research_time_ms={Number(complianceData.summary.research_time_ms || 0)}
          />

          {/* Zoning Information */}
          <ZoningInfoPanel
            zoningInfo={complianceData.summary.zoning_info as ZoningInfo}
          />

          {/* Permit Information */}
          <PermitInfoPanel
            permitInfo={complianceData.summary.permit_info as PermitInfo}
          />

          {/* Official Sources */}
          <SourcesPanel
            citations={Array.isArray(complianceData.summary.citations) ? complianceData.summary.citations : []}
            sourceUrl={String(complianceData.summary.source_url || '')}
          />

        </DashboardLayout>
      ) : !isLoading ? (
        <div className="container mx-auto px-4 py-4">
          <div className="max-w-6xl mx-auto">
            {/* Compact Dashboard Welcome */}
            <div className="text-center mb-4">
              <h2 className="text-lg font-semibold text-foreground mb-1">
                Property Compliance Dashboard
              </h2>
              <p className="text-sm text-muted-foreground">
                Enter an address and project type above to get started with fast compliance analysis
              </p>
            </div>

            {/* Dashboard Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
              <PersonalizedRecommendations
                onRecommendationClick={(recommendation) => {
                  if (recommendation.address && recommendation.project_type) {
                    setSelectedAddress({
                      label: recommendation.address,
                      lat: 0,
                      lng: 0
                    })
                    setSelectedRuleType(recommendation.project_type)
                  }
                }}
              />
              <FrequentAreas
                onAreaClick={(area) => {
                  setSelectedAddress({
                    label: area,
                    lat: 0,
                    lng: 0
                  })
                }}
              />
            </div>

            {/* Quick Feature Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="bg-card rounded-lg p-3 border border-border hover:shadow-md hover:border-primary/20 transition-all duration-200">
                <div className="flex items-center space-x-2">
                  <div className="w-7 h-7 bg-primary/10 rounded-md flex items-center justify-center">
                    <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-foreground">Fast Analysis</h3>
                    <p className="text-xs text-muted-foreground">Results in under 30 seconds</p>
                  </div>
                </div>
              </div>

              <div className="bg-card rounded-lg p-3 border border-border hover:shadow-md hover:border-success/20 transition-all duration-200">
                <div className="flex items-center space-x-2">
                  <div className="w-7 h-7 bg-success/10 rounded-md flex items-center justify-center">
                    <svg className="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-foreground">Official Sources</h3>
                    <p className="text-xs text-muted-foreground">Local ordinances & codes</p>
                  </div>
                </div>
              </div>

              <div className="bg-card rounded-lg p-3 border border-border hover:shadow-md hover:border-secondary/20 transition-all duration-200">
                <div className="flex items-center space-x-2">
                  <div className="w-7 h-7 bg-secondary/10 rounded-md flex items-center justify-center">
                    <svg className="w-4 h-4 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-foreground">AI Chat Support</h3>
                    <p className="text-xs text-muted-foreground">Ask detailed questions</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Getting Started Tips */}
            <div className="mt-4 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-4 border border-border">
              <div className="flex items-start space-x-3">
                <div className="w-5 h-5 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                  <svg className="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-foreground mb-2">Quick Start Guide</h4>
                  <div className="space-y-2">
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">1</div>
                      <span className="text-xs text-muted-foreground">Enter any US property address in the search bar above</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">2</div>
                      <span className="text-xs text-muted-foreground">Select your project type or describe it in detail (Pro users)</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">3</div>
                      <span className="text-xs text-muted-foreground">Get fast compliance analysis with confidence scores</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5">4</div>
                      <span className="text-xs text-muted-foreground">Use the AI chat to ask specific questions about requirements</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto text-center">
            <div className="bg-muted/50 rounded-lg p-8">
              <h2 className="text-lg font-semibold text-foreground mb-2">
                Ready to Search
              </h2>
              <p className="text-muted-foreground">
                Click the Search button above to analyze compliance for your selected address and project type.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Chat Sidebar - only show when there's search context */}
      {userTier === 'professional' &&
       (selectedAddress && selectedRuleType && jurisdictionName) && (
        <ChatSidebar
          isOpen={isChatSidebarOpen}
          onToggle={() => setIsChatSidebarOpen(!isChatSidebarOpen)}
          conversationId={searchParams.get('conversationId') || undefined}
          contextData={complianceData}
          address={selectedAddress?.label}
          ruleType={selectedRuleType}
          jurisdictionName={jurisdictionName}
        />
      )}
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background text-foreground py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-foreground mb-8">
              Property Compliance Search
            </h1>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    }>
      <SearchPageContent />
    </Suspense>
  )
}
