'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { SelectRoot, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Edit, Trash2, ExternalLink, Link, Home, Search, Settings, Star } from 'lucide-react'

interface Shortcut {
  id: string
  name: string
  url: string
  icon: string
  position: number
  created_at: string
  updated_at: string
}

const iconOptions = [
  { value: 'link', label: 'Link', icon: Link },
  { value: 'home', label: 'Home', icon: Home },
  { value: 'search', label: 'Search', icon: Search },
  { value: 'settings', label: 'Settings', icon: Settings },
  { value: 'star', label: 'Star', icon: Star },
  { value: 'external', label: 'External', icon: ExternalLink }
]

export default function ShortcutsPage() {
  const [loading, setLoading] = useState(true)
  const [shortcuts, setShortcuts] = useState<Shortcut[]>([])
  const [showDialog, setShowDialog] = useState(false)
  const [editingShortcut, setEditingShortcut] = useState<Shortcut | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    icon: 'link',
    position: 0
  })
  const router = useRouter()
  const supabase = createClient()

  const loadShortcuts = async () => {
    try {
      const response = await fetch('/api/shortcuts')
      if (response.ok) {
        const data = await response.json()
        setShortcuts(data.shortcuts || [])
      }
    } catch (error) {
      console.error('Failed to load shortcuts:', error)
    } finally {
      setLoading(false)
    }
  }

  const checkAuthAndLoadShortcuts = useCallback(async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        router.push('/login')
        return
      }

      await loadShortcuts()
    } catch (err) {
      console.error('Auth check error:', err)
      router.push('/login')
    }
  }, [loadShortcuts]) // Remove router and supabase.auth dependencies to prevent infinite loops

  useEffect(() => {
    checkAuthAndLoadShortcuts()
  }, [checkAuthAndLoadShortcuts])

  const handleSubmit = async () => {
    try {
      const method = editingShortcut ? 'PUT' : 'POST'
      const body = editingShortcut 
        ? { ...formData, id: editingShortcut.id }
        : formData

      const response = await fetch('/api/shortcuts', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      if (response.ok) {
        await loadShortcuts()
        setShowDialog(false)
        setEditingShortcut(null)
        setFormData({ name: '', url: '', icon: 'link', position: 0 })
      }
    } catch (error) {
      console.error('Failed to save shortcut:', error)
    }
  }

  const handleEdit = (shortcut: Shortcut) => {
    setEditingShortcut(shortcut)
    setFormData({
      name: shortcut.name,
      url: shortcut.url,
      icon: shortcut.icon,
      position: shortcut.position
    })
    setShowDialog(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this shortcut?')) {
      return
    }

    try {
      const response = await fetch(`/api/shortcuts?id=${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setShortcuts(prev => prev.filter(s => s.id !== id))
      }
    } catch (error) {
      console.error('Failed to delete shortcut:', error)
    }
  }

  const getIconComponent = (iconName: string) => {
    const iconOption = iconOptions.find(opt => opt.value === iconName)
    return iconOption ? iconOption.icon : Link
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading shortcuts...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Custom Shortcuts</h1>
              <p className="text-gray-600">Create quick links to your favorite tools and resources</p>
            </div>
            <Dialog open={showDialog} onOpenChange={setShowDialog}>
              <DialogTrigger asChild>
                <Button 
                  onClick={() => {
                    setEditingShortcut(null)
                    setFormData({ name: '', url: '', icon: 'link', position: shortcuts.length })
                  }}
                  className="flex items-center"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Shortcut
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {editingShortcut ? 'Edit Shortcut' : 'Add New Shortcut'}
                  </DialogTitle>
                  <DialogDescription>
                    Create a custom shortcut to quickly access your favorite tools
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter shortcut name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="url">URL</Label>
                    <Input
                      id="url"
                      value={formData.url}
                      onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                      placeholder="https://example.com"
                    />
                  </div>
                  <div>
                    <Label htmlFor="icon">Icon</Label>
                    <SelectRoot 
                      value={formData.icon} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, icon: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {iconOptions.map(option => {
                          const IconComponent = option.icon
                          return (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center">
                                <IconComponent className="h-4 w-4 mr-2" />
                                {option.label}
                              </div>
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </SelectRoot>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSubmit} disabled={!formData.name || !formData.url}>
                      {editingShortcut ? 'Update' : 'Create'} Shortcut
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {shortcuts.length === 0 ? (
              <div className="col-span-full">
                <Card>
                  <CardContent className="text-center py-12">
                    <Link className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No shortcuts yet</h3>
                    <p className="text-gray-600 mb-4">
                      Create your first shortcut to quickly access your favorite tools
                    </p>
                    <Button onClick={() => setShowDialog(true)}>
                      Add Your First Shortcut
                    </Button>
                  </CardContent>
                </Card>
              </div>
            ) : (
              shortcuts.map((shortcut) => {
                const IconComponent = getIconComponent(shortcut.icon)
                return (
                  <Card key={shortcut.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <IconComponent className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">{shortcut.name}</h3>
                            <p className="text-sm text-gray-500 truncate max-w-[200px]">
                              {shortcut.url}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(shortcut.url, '_blank')}
                          className="flex items-center"
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open
                        </Button>
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(shortcut)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(shortcut.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
