import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageCircle, Phone, Mail, Clock, Star, Zap, Shield, Users } from 'lucide-react'

export default function SupportPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Support Center</h1>
            <p className="text-gray-600 mt-2">
              Get help from our support team and access resources to maximize your Ordrly experience.
            </p>
            <div className="mt-2 text-sm text-blue-600">
              Priority Enterprise Dedicated 24/7 Support Help Center
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <MessageCircle className="h-4 w-4 mr-2" />
              Live Chat
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Phone className="h-4 w-4 mr-2" />
              Call Support
            </Button>
          </div>
        </div>
      </div>

      {/* Support Tiers */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="border-2 border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-blue-600" />
                <h3 className="font-semibold text-blue-800">Standard Support</h3>
              </div>
              <Badge className="bg-blue-100 text-blue-800">Free</Badge>
            </div>
            <ul className="space-y-2 text-sm text-blue-700">
              <li>• Email support</li>
              <li>• Knowledge base access</li>
              <li>• Community forums</li>
              <li>• 48-hour response time</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-2 border-purple-200 bg-purple-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Star className="h-6 w-6 text-purple-600" />
                <h3 className="font-semibold text-purple-800">Pro Support</h3>
              </div>
              <Badge className="bg-purple-100 text-purple-800">Pro Plan</Badge>
            </div>
            <ul className="space-y-2 text-sm text-purple-700">
              <li>• Priority email support</li>
              <li>• Live chat support</li>
              <li>• Phone support</li>
              <li>• 12-hour response time</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-2 border-gold-200 bg-yellow-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-6 w-6 text-yellow-600" />
                <h3 className="font-semibold text-yellow-800">Enterprise Priority</h3>
              </div>
              <Badge className="bg-yellow-100 text-yellow-800">Enterprise</Badge>
            </div>
            <ul className="space-y-2 text-sm text-yellow-700">
              <li>• Dedicated account manager</li>
              <li>• 24/7 priority support</li>
              <li>• Phone & video calls</li>
              <li>• 2-hour response time</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Contact Options */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageCircle className="h-5 w-5 mr-2" />
              Contact Support
            </CardTitle>
            <CardDescription>
              Choose the best way to reach our support team
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="h-5 w-5 text-blue-600" />
                    <div>
                      <h4 className="font-medium text-gray-900">Live Chat</h4>
                      <p className="text-sm text-gray-600">Get instant help from our team</p>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Online</Badge>
                </div>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-green-600" />
                    <div>
                      <h4 className="font-medium text-gray-900">Phone Support</h4>
                      <p className="text-sm text-gray-600">Call us at +****************</p>
                    </div>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">Available</Badge>
                </div>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-purple-600" />
                    <div>
                      <h4 className="font-medium text-gray-900">Email Support</h4>
                      <p className="text-sm text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                  <Badge variant="outline">24h response</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Support Hours
            </CardTitle>
            <CardDescription>
              When our support team is available to help
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800">Enterprise Priority</h4>
                <p className="text-sm text-green-600">24/7 dedicated support</p>
                <p className="text-xs text-green-500 mt-1">Always available for enterprise customers</p>
              </div>

              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800">Pro Support</h4>
                <p className="text-sm text-blue-600">Monday - Friday: 6 AM - 10 PM PST</p>
                <p className="text-sm text-blue-600">Saturday - Sunday: 8 AM - 6 PM PST</p>
              </div>

              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-800">Standard Support</h4>
                <p className="text-sm text-gray-600">Monday - Friday: 9 AM - 5 PM PST</p>
                <p className="text-xs text-gray-500 mt-1">Email responses within 48 hours</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enterprise Features */}
      <Card className="mb-8 border-2 border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center text-yellow-800">
            <Shield className="h-5 w-5 mr-2" />
            Enterprise Priority Support
          </CardTitle>
          <CardDescription className="text-yellow-700">
            Exclusive benefits for enterprise customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Zap className="h-5 w-5 text-yellow-600 mt-1" />
                <div>
                  <h4 className="font-medium text-yellow-800">Dedicated Account Manager</h4>
                  <p className="text-sm text-yellow-700">Personal point of contact for all your needs</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-yellow-600 mt-1" />
                <div>
                  <h4 className="font-medium text-yellow-800">24/7 Priority Response</h4>
                  <p className="text-sm text-yellow-700">Critical issues resolved within 2 hours</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-yellow-600 mt-1" />
                <div>
                  <h4 className="font-medium text-yellow-800">Direct Phone Line</h4>
                  <p className="text-sm text-yellow-700">Skip the queue with priority access</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Users className="h-5 w-5 text-yellow-600 mt-1" />
                <div>
                  <h4 className="font-medium text-yellow-800">Team Training Sessions</h4>
                  <p className="text-sm text-yellow-700">Onboarding and advanced feature training</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-yellow-600 mt-1" />
                <div>
                  <h4 className="font-medium text-yellow-800">Custom Integrations</h4>
                  <p className="text-sm text-yellow-700">Help with API integrations and customizations</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Star className="h-5 w-5 text-yellow-600 mt-1" />
                <div>
                  <h4 className="font-medium text-yellow-800">Feature Requests</h4>
                  <p className="text-sm text-yellow-700">Priority consideration for new features</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-yellow-200">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-yellow-800">Ready to upgrade to Enterprise?</h4>
                <p className="text-sm text-yellow-700">Get dedicated support and premium features</p>
              </div>
              <Button className="bg-yellow-600 hover:bg-yellow-700 text-white">
                Contact Sales
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <MessageCircle className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Start Live Chat</h3>
            <p className="text-sm text-gray-600">Get instant help now</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Mail className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Email Support</h3>
            <p className="text-sm text-gray-600">Send us a message</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Phone className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900">Schedule Call</h3>
            <p className="text-sm text-gray-600">Book a support session</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
