'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { User } from '@supabase/supabase-js'

export default function TestAuthFlowPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [logs, setLogs] = useState<string[]>([])
  const supabase = createClient()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `${timestamp}: ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(`TEST AUTH: ${message}`)
  }

  useEffect(() => {
    let mounted = true

    const checkAuth = async () => {
      addLog('Starting auth check...')
      
      try {
        const { data: { user }, error } = await supabase.auth.getUser()
        
        if (mounted) {
          setUser(user)
          setLoading(false)
          addLog(user ? `✅ User found: ${user.email}` : '❌ No user found')
        }
      } catch (error) {
        addLog(`❌ Auth check error: ${error}`)
        if (mounted) {
          setLoading(false)
        }
      }
    }

    checkAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        addLog(`🔄 Auth state change: ${event} - ${session?.user?.email || 'No user'}`)
        if (mounted) {
          setUser(session?.user ?? null)
          setLoading(false)
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  const testLogin = async () => {
    addLog('Testing login with test credentials...')
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword'
      })
      
      if (error) {
        addLog(`❌ Login error: ${error.message}`)
      } else {
        addLog(`✅ Login successful: ${data.user?.email}`)
      }
    } catch (error) {
      addLog(`❌ Login exception: ${error}`)
    }
  }

  const testLogout = async () => {
    addLog('Testing logout...')
    
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        addLog(`❌ Logout error: ${error.message}`)
      } else {
        addLog('✅ Logout successful')
      }
    } catch (error) {
      addLog(`❌ Logout exception: ${error}`)
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  const refreshAuth = async () => {
    addLog('Manually refreshing auth state...')
    setLoading(true)
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      addLog(user ? `✅ Refresh found user: ${user.email}` : '❌ Refresh found no user')
    } catch (error) {
      addLog(`❌ Refresh error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Auth Flow Test Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Current State */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
          <h2 className="text-lg font-semibold mb-4">Current State</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {user ? `✅ ${user.email}` : '❌ Not authenticated'}</p>
            {user && (
              <>
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Created:</strong> {user.created_at}</p>
              </>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
          <h2 className="text-lg font-semibold mb-4">Test Actions</h2>
          <div className="space-y-2">
            <Button onClick={testLogin} className="w-full" variant="outline">
              Test Login
            </Button>
            <Button onClick={testLogout} className="w-full" variant="outline">
              Test Logout
            </Button>
            <Button onClick={refreshAuth} className="w-full" variant="outline">
              Refresh Auth
            </Button>
            <Button onClick={clearLogs} className="w-full" variant="ghost">
              Clear Logs
            </Button>
          </div>
        </div>
      </div>

      {/* Navigation Test */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border mb-6">
        <h2 className="text-lg font-semibold mb-4">Navigation Test</h2>
        <p className="text-sm text-gray-600 mb-4">
          Test if the header updates properly when navigating between pages:
        </p>
        <div className="space-x-2">
          <Button onClick={() => window.location.href = '/'} variant="outline" size="sm">
            Go to Home
          </Button>
          <Button onClick={() => window.location.href = '/search'} variant="outline" size="sm">
            Go to Search
          </Button>
          <Button onClick={() => window.location.href = '/account'} variant="outline" size="sm">
            Go to Account
          </Button>
          <Button onClick={() => window.location.href = '/login'} variant="outline" size="sm">
            Go to Login
          </Button>
        </div>
      </div>

      {/* Logs */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border">
        <h2 className="text-lg font-semibold mb-4">Auth Logs</h2>
        <div className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg max-h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500 text-sm">No logs yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-sm font-mono mb-1">
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
