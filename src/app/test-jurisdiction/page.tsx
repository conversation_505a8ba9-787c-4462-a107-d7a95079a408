'use client'

import { useState } from 'react'

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

interface JurisdictionResult {
  coordinates?: { lat: number; lng: number }
  jurisdiction?: {
    name: string
    state: string
    state_code: string
    level: string
  }
  searchQuery?: string
  sources?: Array<{
    title: string
    link: string
    snippet: string
  }>
  error?: string
  timing?: {
    total: number
    geocoding: number
    jurisdiction: number
    search: number
  }
}

interface DocumentContent {
  url: string
  title: string
  content: string
  contentType: 'html' | 'pdf' | 'text'
  error?: string
}

interface OrdinanceAnalysis {
  summary: string
  permit_required: boolean
  requirements: string[]
  prohibited_practices: string[]
  permit_process: string
  citations: string[]
  source_links: string[]
  contact_info: {
    department?: string
    phone?: string
    email?: string
    website?: string
  }
  tags: string[]
  confidence_score?: number
  error?: boolean
  error_message?: string
}

export default function TestJurisdictionPage() {
  const [address, setAddress] = useState('152 Graham Road Northwest Grand Rapids, MI, 49504')
  const [projectType, setProjectType] = useState('chickens')
  const [result, setResult] = useState<JurisdictionResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [documentContents, setDocumentContents] = useState<DocumentContent[]>([])
  const [ordinanceAnalysis, setOrdinanceAnalysis] = useState<OrdinanceAnalysis | null>(null)
  const [parsingLoading, setParsingLoading] = useState(false)
  const [analysisLoading, setAnalysisLoading] = useState(false)

  const testPipeline = async () => {
    setLoading(true)
    setResult(null)
    setDocumentContents([])
    setOrdinanceAnalysis(null)

    try {
      const response = await fetch('/api/test-jurisdiction', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address,
          projectType
        })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        error: `Request failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    } finally {
      setLoading(false)
    }
  }

  const parseDocuments = async () => {
    if (!result?.sources || result.sources.length === 0) {
      return
    }

    setParsingLoading(true)
    setDocumentContents([])

    try {
      const parsePromises = result.sources.map(async (source) => {
        try {
          const response = await fetch('/api/parse-document', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              url: source.link,
              title: source.title
            })
          })

          const data = await response.json()
          return data
        } catch (error) {
          return {
            url: source.link,
            title: source.title,
            content: '',
            contentType: 'text' as const,
            error: `Failed to parse: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }
      })

      const contents = await Promise.all(parsePromises)
      setDocumentContents(contents)
    } catch (error) {
      console.error('Document parsing failed:', error)
    } finally {
      setParsingLoading(false)
    }
  }

  const analyzeOrdinances = async () => {
    if (documentContents.length === 0) {
      return
    }

    setAnalysisLoading(true)
    setOrdinanceAnalysis(null)

    try {
      // Combine all document contents into a single text
      const combinedDocumentText = documentContents
        .filter(doc => !doc.error && doc.content.length > 100)
        .map(doc => `=== DOCUMENT: ${doc.title} ===
URL: ${doc.url}
Type: ${doc.contentType}

${doc.content}

=== END DOCUMENT ===`)
        .join('\n\n')

      const response = await fetch('/api/analyze-ordinance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          region: `${result?.jurisdiction?.name}, ${result?.jurisdiction?.state}`,
          project_type: projectType,
          document_text: combinedDocumentText
        })
      })

      const data = await response.json()
      setOrdinanceAnalysis(data)
    } catch (error) {
      console.error('Ordinance analysis failed:', error)
    } finally {
      setAnalysisLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          🧪 Jurisdiction Pipeline Test
        </h1>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Input</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address
              </label>
              <input
                type="text"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter full address..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project Type
              </label>
              <select
                value={projectType}
                onChange={(e) => setProjectType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="chickens">Chickens</option>
                <option value="fence">Fence</option>
                <option value="shed">Shed</option>
                <option value="pool">Pool</option>
              </select>
            </div>

            <button
              onClick={testPipeline}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '🔄 Testing Pipeline...' : '🚀 Test Pipeline'}
            </button>
          </div>
        </div>

        {result && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Pipeline Results</h2>

            {result.error ? (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <h3 className="text-red-800 font-medium">❌ Error</h3>
                <p className="text-red-700 mt-1">{result.error}</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Step 1: Coordinates */}
                {result.coordinates && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h3 className="text-blue-800 font-medium">📍 Step 1: Address → Coordinates</h3>
                    <p className="text-blue-700 mt-1">
                      Lat: {result.coordinates.lat}, Lng: {result.coordinates.lng}
                    </p>
                  </div>
                )}

                {/* Step 2: Jurisdiction */}
                {result.jurisdiction && (
                  <div className="bg-green-50 border border-green-200 rounded-md p-4">
                    <h3 className="text-green-800 font-medium">🏛️ Step 2: Coordinates → Jurisdiction</h3>
                    <div className="text-green-700 mt-1">
                      <p><strong>Name:</strong> {result.jurisdiction.name}</p>
                      <p><strong>State:</strong> {result.jurisdiction.state} ({result.jurisdiction.state_code})</p>
                      <p><strong>Level:</strong> {result.jurisdiction.level}</p>
                    </div>
                  </div>
                )}

                {/* Step 3: Search Query */}
                {result.searchQuery && (
                  <div className="bg-purple-50 border border-purple-200 rounded-md p-4">
                    <h3 className="text-purple-800 font-medium">🔍 Step 3: Search Query</h3>
                    <p className="text-purple-700 mt-1 font-mono">&quot;{result.searchQuery}&quot;</p>
                  </div>
                )}

                {/* Step 4: Sources */}
                {result.sources && result.sources.length > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <h3 className="text-yellow-800 font-medium">📚 Step 4: Found {result.sources.length} Sources</h3>
                    <div className="mt-3 space-y-3">
                      {result.sources.map((source, index) => (
                        <div key={index} className="bg-white border border-gray-200 rounded p-3">
                          <h4 className="font-medium text-gray-900 mb-1">
                            {index + 1}. {source.title}
                          </h4>
                          <a
                            href={source.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm break-all"
                          >
                            {source.link}
                          </a>
                          {source.snippet && (
                            <p className="text-gray-600 text-sm mt-2">{source.snippet}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Timing */}
                {result.timing && (
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                    <h3 className="text-gray-800 font-medium">⏱️ Performance</h3>
                    <div className="text-gray-700 mt-1 text-sm">
                      <p>Total: {result.timing.total}ms</p>
                      <p>Geocoding: {result.timing.geocoding}ms</p>
                      <p>Jurisdiction: {result.timing.jurisdiction}ms</p>
                      <p>Search: {result.timing.search}ms</p>
                    </div>
                  </div>
                )}

                {/* Next Steps Buttons */}
                {result.sources && result.sources.length > 0 && (
                  <div className="bg-indigo-50 border border-indigo-200 rounded-md p-4">
                    <h3 className="text-indigo-800 font-medium mb-3">🚀 Next Steps</h3>
                    <div className="space-y-2">
                      <button
                        onClick={parseDocuments}
                        disabled={parsingLoading}
                        className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {parsingLoading ? '📄 Parsing Documents...' : '📄 Step 5: Parse Documents'}
                      </button>

                      {documentContents.length > 0 && (
                        <button
                          onClick={analyzeOrdinances}
                          disabled={analysisLoading}
                          className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {analysisLoading ? '🤖 Analyzing Ordinances...' : '🤖 Step 6: AI Analysis'}
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Document Contents */}
        {documentContents.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">📄 Document Contents</h2>
            <div className="space-y-4">
              {documentContents.map((doc, index) => (
                <div key={index} className="border border-gray-200 rounded-md p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">{doc.title}</h3>
                    <span className={`px-2 py-1 text-xs rounded ${
                      doc.contentType === 'pdf' ? 'bg-red-100 text-red-800' :
                      doc.contentType === 'html' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {doc.contentType.toUpperCase()}
                    </span>
                  </div>

                  <a
                    href={doc.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm break-all block mb-2"
                  >
                    {doc.url}
                  </a>

                  {doc.error ? (
                    <div className="bg-red-50 border border-red-200 rounded p-2">
                      <p className="text-red-700 text-sm">❌ {doc.error}</p>
                    </div>
                  ) : (
                    <div className="bg-gray-50 rounded p-3">
                      <p className="text-sm text-gray-600 mb-1">
                        Content length: {doc.content.length} characters
                      </p>
                      <div className="max-h-32 overflow-y-auto">
                        <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                          {doc.content.substring(0, 500)}
                          {doc.content.length > 500 && '...'}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* AI Analysis Results */}
        {ordinanceAnalysis && (
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">🤖 AI Ordinance Analysis</h2>

            <div className="space-y-6">
              {/* Summary */}
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="text-blue-800 font-medium mb-2">📋 Summary</h3>
                <p className="text-blue-700">{ordinanceAnalysis.summary}</p>
              </div>

              {/* Permit Required */}
              <div className={`border rounded-md p-4 ${
                ordinanceAnalysis.permit_required
                  ? 'bg-orange-50 border-orange-200'
                  : 'bg-green-50 border-green-200'
              }`}>
                <h3 className={`font-medium mb-2 ${
                  ordinanceAnalysis.permit_required
                    ? 'text-orange-800'
                    : 'text-green-800'
                }`}>
                  {ordinanceAnalysis.permit_required ? '⚠️ Permit Required' : '✅ No Permit Required'}
                </h3>
              </div>

              {/* Requirements */}
              {ordinanceAnalysis.requirements.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <h3 className="text-yellow-800 font-medium mb-3">📝 Requirements</h3>
                  <div className="space-y-2">
                    {ordinanceAnalysis.requirements.map((req, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded p-3">
                        <p className="text-gray-700 text-sm">{req}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Prohibited Practices */}
              {ordinanceAnalysis.prohibited_practices.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <h3 className="text-red-800 font-medium mb-3">🚫 Prohibited Practices</h3>
                  <div className="space-y-2">
                    {ordinanceAnalysis.prohibited_practices.map((practice, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded p-3">
                        <p className="text-gray-700 text-sm">{practice}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Permit Process */}
              {ordinanceAnalysis.permit_process && (
                <div className="bg-indigo-50 border border-indigo-200 rounded-md p-4">
                  <h3 className="text-indigo-800 font-medium mb-2">📋 Permit Process</h3>
                  <p className="text-indigo-700">{ordinanceAnalysis.permit_process}</p>
                </div>
              )}

              {/* Citations */}
              {ordinanceAnalysis.citations.length > 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                  <h3 className="text-gray-800 font-medium mb-3">📚 Citations</h3>
                  <div className="space-y-2">
                    {ordinanceAnalysis.citations.map((citation, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded p-3">
                        <p className="text-gray-700 text-sm font-mono">{citation}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Source Links */}
              {ordinanceAnalysis.source_links.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h3 className="text-blue-800 font-medium mb-3">🔗 Source Links</h3>
                  <div className="space-y-2">
                    {ordinanceAnalysis.source_links.map((link, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded p-3">
                        <a
                          href={link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-sm break-all"
                        >
                          {link}
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Tags */}
              {ordinanceAnalysis.tags.length > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <h3 className="text-green-800 font-medium mb-3">🏷️ Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {ordinanceAnalysis.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Contact Info */}
              {ordinanceAnalysis.contact_info && (
                <div className="bg-purple-50 border border-purple-200 rounded-md p-4">
                  <h3 className="text-purple-800 font-medium mb-2">📞 Contact Information</h3>
                  <div className="text-purple-700 space-y-1">
                    {ordinanceAnalysis.contact_info.department && (
                      <p><strong>Department:</strong> {ordinanceAnalysis.contact_info.department}</p>
                    )}
                    {ordinanceAnalysis.contact_info.phone && (
                      <p><strong>Phone:</strong> {ordinanceAnalysis.contact_info.phone}</p>
                    )}
                    {ordinanceAnalysis.contact_info.email && (
                      <p><strong>Email:</strong> {ordinanceAnalysis.contact_info.email}</p>
                    )}
                    {ordinanceAnalysis.contact_info.website && (
                      <p><strong>Website:</strong>
                        <a
                          href={ordinanceAnalysis.contact_info.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-purple-600 hover:text-purple-800 ml-1"
                        >
                          {ordinanceAnalysis.contact_info.website}
                        </a>
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Error Display */}
              {ordinanceAnalysis.error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <h3 className="text-red-800 font-medium mb-2">❌ Analysis Error</h3>
                  <p className="text-red-700">{ordinanceAnalysis.error_message}</p>
                </div>
              )}

              {/* Confidence */}
              <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                <h3 className="text-gray-800 font-medium mb-2">🎯 Analysis Confidence</h3>
                <div className="flex items-center">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        (ordinanceAnalysis.confidence_score || 0) >= 0.8 ? 'bg-green-500' :
                        (ordinanceAnalysis.confidence_score || 0) >= 0.6 ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${((ordinanceAnalysis.confidence_score || 0) * 100)}%` }}
                    ></div>
                  </div>
                  <span className="ml-3 text-gray-700 font-medium">
                    {Math.round((ordinanceAnalysis.confidence_score || 0) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
