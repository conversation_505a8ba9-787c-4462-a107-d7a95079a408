'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ChevronLeft, ChevronRight, CheckCircle, Lightbulb } from 'lucide-react'

interface TutorialStep {
  id: number
  title: string
  description: string
  content: string
  videoUrl?: string
  tips: string[]
}

interface Tutorial {
  name: string
  title: string
  description: string
  duration: string
  steps: TutorialStep[]
  progress?: {
    current_step: number
    completed: boolean
  }
}

export default function GettingStartedTutorial() {
  // Hide tutorials in production - this is a work-in-progress feature
  if (process.env.NODE_ENV === 'production') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Coming Soon</h1>
          <p className="text-muted-foreground mb-6">
            Tutorials are currently under development. Check back soon!
          </p>
          <a href="/" className="text-primary hover:underline">
            ← Back to Home
          </a>
        </div>
      </div>
    )
  }

  const [tutorial, setTutorial] = useState<Tutorial | null>(null)
  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTutorial()
  }, [])

  const fetchTutorial = async () => {
    try {
      const response = await fetch('/api/tutorials?tutorial_name=getting-started')
      const data = await response.json()
      
      if (data.success) {
        setTutorial(data.tutorial)
        setCurrentStep(data.tutorial.progress?.current_step || 0)
      } else {
        setError(data.error || 'Failed to load tutorial')
      }
    } catch {
      setError('Failed to load tutorial')
    } finally {
      setLoading(false)
    }
  }

  const updateProgress = async (stepIndex: number, completed: boolean = false) => {
    try {
      await fetch('/api/tutorials', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tutorial_name: 'getting-started',
          current_step: stepIndex,
          completed
        })
      })
    } catch (err) {
      console.error('Failed to update progress:', err)
    }
  }

  const nextStep = () => {
    if (tutorial && currentStep < tutorial.steps.length - 1) {
      const newStep = currentStep + 1
      setCurrentStep(newStep)
      updateProgress(newStep)
    } else if (tutorial && currentStep === tutorial.steps.length - 1) {
      updateProgress(tutorial.steps.length, true)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1
      setCurrentStep(newStep)
      updateProgress(newStep)
    }
  }

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex)
    updateProgress(stepIndex)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading tutorial...</p>
        </div>
      </div>
    )
  }

  if (error || !tutorial) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || 'Tutorial not found'}</p>
          <Link 
            href="/tutorials"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Back to Tutorials
          </Link>
        </div>
      </div>
    )
  }

  const step = tutorial.steps[currentStep]
  const isLastStep = currentStep === tutorial.steps.length - 1

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link 
                href="/tutorials"
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ChevronLeft className="h-5 w-5 mr-1" />
                Back to Tutorials
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{tutorial.title}</h1>
                <p className="text-gray-600">{tutorial.description}</p>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              Step {currentStep + 1} of {tutorial.steps.length}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{Math.round(((currentStep + 1) / tutorial.steps.length) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / tutorial.steps.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid gap-8 lg:grid-cols-4">
          {/* Step Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h3 className="font-semibold text-gray-900 mb-4">Tutorial Steps</h3>
              <nav className="space-y-2">
                {tutorial.steps.map((tutorialStep, index) => (
                  <button
                    key={tutorialStep.id}
                    onClick={() => goToStep(index)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      index === currentStep
                        ? 'bg-blue-100 text-blue-900 border border-blue-200'
                        : index < currentStep
                        ? 'bg-green-50 text-green-900 border border-green-200'
                        : 'bg-gray-50 text-gray-600 border border-gray-200'
                    }`}
                  >
                    <div className="flex items-center">
                      {index < currentStep ? (
                        <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                      ) : (
                        <div className={`w-5 h-5 rounded-full mr-2 flex items-center justify-center text-xs font-medium ${
                          index === currentStep ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                        }`}>
                          {index + 1}
                        </div>
                      )}
                      <span className="text-sm font-medium">{tutorialStep.title}</span>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Step Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Step Header */}
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900 mb-2">
                  Step {step.id}: {step.title}
                </h2>
                <p className="text-gray-600">{step.description}</p>
              </div>

              {/* Video Section */}
              {step.videoUrl && (
                <div className="p-6 border-b border-gray-200">
                  <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                    <iframe
                      src={step.videoUrl}
                      className="w-full h-full rounded-lg"
                      allowFullScreen
                      title={`${step.title} Tutorial Video`}
                    />
                  </div>
                </div>
              )}

              {/* Step Content */}
              <div className="p-6">
                <div className="prose max-w-none">
                  <div dangerouslySetInnerHTML={{ __html: step.content.replace(/\n/g, '<br>') }} />
                </div>

                {/* Tips Section */}
                {step.tips && step.tips.length > 0 && (
                  <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start">
                      <Lightbulb className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-900 mb-2">Pro Tips</h4>
                        <ul className="space-y-1">
                          {step.tips.map((tip, index) => (
                            <li key={index} className="text-sm text-yellow-800">
                              • {tip}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Navigation */}
              <div className="p-6 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-between items-center">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                      currentStep === 0
                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-600 text-white hover:bg-gray-700'
                    }`}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </button>

                  <div className="text-sm text-gray-600">
                    Step {currentStep + 1} of {tutorial.steps.length}
                  </div>

                  {isLastStep ? (
                    <Link
                      href="/tutorials"
                      className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      Complete Tutorial
                      <CheckCircle className="h-4 w-4 ml-1" />
                    </Link>
                  ) : (
                    <button
                      onClick={nextStep}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Next Step
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
