'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Play, Clock, CheckCircle, BookOpen, Video, Users } from 'lucide-react'

interface Tutorial {
  name: string
  title: string
  description: string
  duration: string
  steps: Array<{
    id: number
    title: string
    description: string
    content: string
    videoUrl?: string
    tips: string[]
  }>
  progress?: {
    current_step: number
    completed: boolean
  }
}

export default function TutorialsPage() {
  // Hide tutorials in production - this is a work-in-progress feature
  if (process.env.NODE_ENV === 'production') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Coming Soon</h1>
          <p className="text-muted-foreground mb-6">
            Tutorials are currently under development. Check back soon!
          </p>
          <a href="/" className="text-primary hover:underline">
            ← Back to Home
          </a>
        </div>
      </div>
    )
  }

  const [tutorials, setTutorials] = useState<Tutorial[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTutorials()
  }, [])

  const fetchTutorials = async () => {
    try {
      const response = await fetch('/api/tutorials')
      const data = await response.json()

      if (data.success) {
        setTutorials(data.tutorials)
      } else {
        setError(data.error || 'Failed to load tutorials')
      }
    } catch {
      setError('Failed to load tutorials')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading tutorials...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchTutorials}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Tutorials & How-to Guides
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Learn how to use Ordrly effectively with our step-by-step tutorials and guides.
              Master property compliance checking in minutes.
            </p>
          </div>
        </div>
      </div>

      {/* Tutorial Library */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {tutorials.map((tutorial) => (
            <div key={tutorial.name} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              {/* Tutorial Header */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <BookOpen className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {tutorial.title}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500 mt-1">
                        <Clock className="h-4 w-4 mr-1" />
                        {tutorial.duration}
                      </div>
                    </div>
                  </div>
                  {tutorial.progress?.completed && (
                    <CheckCircle className="h-6 w-6 text-green-500" />
                  )}
                </div>

                <p className="text-gray-600 mb-4">
                  {tutorial.description}
                </p>

                {/* Progress Bar */}
                {tutorial.progress && (
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>
                        {tutorial.progress.current_step}/{tutorial.steps.length} steps
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${(tutorial.progress.current_step / tutorial.steps.length) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Steps Preview */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">What you&apos;ll learn:</h4>
                  <ul className="space-y-1">
                    {tutorial.steps.slice(0, 3).map((step) => (
                      <li key={step.id} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                        {step.title}
                      </li>
                    ))}
                    {tutorial.steps.length > 3 && (
                      <li className="text-sm text-gray-500">
                        +{tutorial.steps.length - 3} more steps
                      </li>
                    )}
                  </ul>
                </div>

                {/* Action Button */}
                <Link
                  href={`/tutorials/${tutorial.name}`}
                  className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Play className="h-4 w-4 mr-2" />
                  {tutorial.progress?.completed ? 'Review Tutorial' :
                   tutorial.progress ? 'Continue Tutorial' : 'Start Tutorial'}
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Resources */}
        <div className="mt-16 bg-white rounded-lg shadow-md p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Need More Help?
            </h2>
            <p className="text-gray-600">
              Explore additional resources to get the most out of Ordrly
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-4">
            <Link
              href="/knowledge-base"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <BookOpen className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="font-semibold text-gray-900">Knowledge Base</h3>
                <p className="text-sm text-gray-600">Browse articles and guides</p>
              </div>
            </Link>

            <Link
              href="/tutorials/videos"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <Video className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="font-semibold text-gray-900">Video Tutorials</h3>
                <p className="text-sm text-gray-600">Watch step-by-step videos</p>
              </div>
            </Link>

            <Link
              href="/demo"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <Play className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="font-semibold text-gray-900">Interactive Demo</h3>
                <p className="text-sm text-gray-600">Try it out in sandbox mode</p>
              </div>
            </Link>

            <Link
              href="/help"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <Users className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="font-semibold text-gray-900">Help Center</h3>
                <p className="text-sm text-gray-600">Get support and FAQ</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
