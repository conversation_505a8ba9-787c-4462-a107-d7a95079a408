'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Play, Clock, ArrowLeft, Video } from 'lucide-react'

interface VideoTutorial {
  id: string
  title: string
  description: string
  duration: string
  thumbnail: string
  videoUrl: string
  category: string
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
}

export default function VideoTutorialsPage() {
  // Hide tutorials in production - this is a work-in-progress feature
  if (process.env.NODE_ENV === 'production') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Coming Soon</h1>
          <p className="text-muted-foreground mb-6">
            Video tutorials are currently under development. Check back soon!
          </p>
          <a href="/" className="text-primary hover:underline">
            ← Back to Home
          </a>
        </div>
      </div>
    )
  }

  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const videoTutorials: VideoTutorial[] = [
    {
      id: '1',
      title: 'Getting Started with Ordrly',
      description: 'Learn the basics of property compliance checking with Ordrly. Perfect for new users.',
      duration: '5:30',
      thumbnail: '/api/placeholder/400/225',
      videoUrl: '#',
      category: 'basics',
      difficulty: 'Beginner'
    },
    {
      id: '2',
      title: 'Understanding Compliance Reports',
      description: 'Deep dive into reading and interpreting compliance reports and confidence scores.',
      duration: '8:15',
      thumbnail: '/api/placeholder/400/225',
      videoUrl: '#',
      category: 'reports',
      difficulty: 'Intermediate'
    },
    {
      id: '3',
      title: 'Advanced Search Techniques',
      description: 'Master advanced search features and filters for complex property requirements.',
      duration: '12:45',
      thumbnail: '/api/placeholder/400/225',
      videoUrl: '#',
      category: 'search',
      difficulty: 'Advanced'
    },
    {
      id: '4',
      title: 'Working with Setback Requirements',
      description: 'Learn how to interpret and apply setback requirements for different property types.',
      duration: '7:20',
      thumbnail: '/api/placeholder/400/225',
      videoUrl: '#',
      category: 'setbacks',
      difficulty: 'Intermediate'
    },
    {
      id: '5',
      title: 'Sharing and Exporting Results',
      description: 'How to share compliance cards and export reports for contractors and officials.',
      duration: '4:50',
      thumbnail: '/api/placeholder/400/225',
      videoUrl: '#',
      category: 'sharing',
      difficulty: 'Beginner'
    },
    {
      id: '6',
      title: 'Pro Features Overview',
      description: 'Explore advanced Pro features including AI chat, priority support, and more.',
      duration: '10:30',
      thumbnail: '/api/placeholder/400/225',
      videoUrl: '#',
      category: 'pro',
      difficulty: 'Intermediate'
    }
  ]

  const categories = [
    { id: 'all', name: 'All Videos' },
    { id: 'basics', name: 'Getting Started' },
    { id: 'search', name: 'Search & Lookup' },
    { id: 'reports', name: 'Reports & Analysis' },
    { id: 'setbacks', name: 'Setbacks & Rules' },
    { id: 'sharing', name: 'Sharing & Export' },
    { id: 'pro', name: 'Pro Features' }
  ]

  const filteredVideos = selectedCategory === 'all'
    ? videoTutorials
    : videoTutorials.filter(video => video.category === selectedCategory)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800'
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'Advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center mb-6">
            <Link
              href="/tutorials"
              className="flex items-center text-blue-600 hover:text-blue-700 mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Tutorials
            </Link>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Video className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">
                Video Tutorials
              </h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Watch step-by-step video guides to master Ordrly&apos;s features.
              Learn at your own pace with our comprehensive video library.
            </p>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-wrap gap-2 mb-8">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Video Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredVideos.map((video) => (
            <div key={video.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              {/* Video Thumbnail */}
              <div className="relative aspect-video bg-gray-200">
                {/* Placeholder video element for testing */}
                <video
                  className="w-full h-full object-cover absolute inset-0"
                  poster="/api/placeholder/400/225"
                  controls={false}
                  style={{ opacity: 0.01, pointerEvents: 'none' }}
                >
                  <source src="#" type="video/mp4" />
                </video>

                {/* YouTube iframe placeholder for testing */}
                <iframe
                  src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                  className="w-full h-full absolute inset-0"
                  style={{ opacity: 0.01, pointerEvents: 'none' }}
                  title={video.title}
                />

                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-500 to-blue-600">
                  <Play className="h-12 w-12 text-white" />
                </div>
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                  {video.duration}
                </div>
                <div className="absolute top-2 left-2">
                  <span className={`text-xs px-2 py-1 rounded-full font-medium ${getDifficultyColor(video.difficulty)}`}>
                    {video.difficulty}
                  </span>
                </div>
              </div>

              {/* Video Info */}
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {video.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {video.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1" />
                    {video.duration}
                  </div>

                  <button className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                    <Play className="h-4 w-4 mr-1" />
                    Watch Video
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Coming Soon Notice */}
        <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
          <Video className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            Video Library Coming Soon
          </h3>
          <p className="text-blue-700 mb-4">
            We&apos;re currently producing high-quality video tutorials for all Ordrly features.
            Check back soon for our complete video library!
          </p>
          <Link
            href="/tutorials"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Browse Text Tutorials
          </Link>
        </div>
      </div>
    </div>
  )
}
