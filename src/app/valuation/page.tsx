import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default async function ValuationPage() {
  const supabase = await createServerClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get user profile data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has access to valuation tools
  const hasAccess = profile.subscription_tier === 'appraiser'

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Property Valuation Tools
            </h1>
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Appraiser Access Required
              </h2>
              <p className="text-gray-600 mb-6">
                Advanced compliance research tools are available for Appraiser subscribers only.
              </p>
              <div className="space-y-4">
                <Link href="/pricing">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Upgrade to Appraiser
                  </Button>
                </Link>
                <div>
                  <Link href="/search">
                    <Button variant="outline">
                      Back to Search
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Compliance Research & Analysis Tools
          </h1>
          <p className="text-gray-600 mb-8">
            Regulatory compliance research and ordinance analysis tools to support property professionals with due diligence.
          </p>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
            <p className="text-sm text-yellow-800">
              <strong>Disclaimer:</strong> This tool provides regulatory research and compliance analysis only.
              It is not professional appraisal software and does not generate USPAP-compliant appraisal reports.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Compliance Analysis */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Compliance Analysis
              </h2>
              <p className="text-gray-600 mb-4">
                Analyze property compliance with local ordinances and building codes.
              </p>
              <div className="space-y-3">
                <p className="text-sm text-gray-700">✓ Zoning Compliance Check</p>
                <p className="text-sm text-gray-700">✓ Building Code Analysis</p>
                <p className="text-sm text-gray-700">✓ Ordinance Research</p>
                <p className="text-sm text-gray-700">✓ Regulatory Summaries</p>
              </div>
              <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700" disabled>
                Coming Soon
              </Button>
            </div>

            {/* Regulatory Research */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Regulatory Research
              </h2>
              <p className="text-gray-600 mb-4">
                Research local regulations and ordinance requirements.
              </p>
              <div className="space-y-3">
                <p className="text-sm text-gray-700">✓ Municipal Code Search</p>
                <p className="text-sm text-gray-700">✓ Zoning Requirements</p>
                <p className="text-sm text-gray-700">✓ Building Restrictions</p>
                <p className="text-sm text-gray-700">✓ Permit Requirements</p>
              </div>
              <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700" disabled>
                Coming Soon
              </Button>
            </div>

            {/* Compliance Reports */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Compliance Reports
              </h2>
              <p className="text-gray-600 mb-4">
                Generate regulatory compliance summaries and research reports.
              </p>
              <div className="space-y-3">
                <p className="text-sm text-gray-700">✓ Ordinance Summaries</p>
                <p className="text-sm text-gray-700">✓ Compliance Checklists</p>
                <p className="text-sm text-gray-700">✓ Research Documentation</p>
                <p className="text-sm text-gray-700">✓ PDF Export</p>
              </div>
              <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700" disabled>
                Coming Soon
              </Button>
            </div>
          </div>

          {/* Recent Research */}
          <div className="mt-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Recent Research
            </h2>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="text-center text-gray-500">
                <p>No recent compliance research found.</p>
                <p className="text-sm mt-1">Start a new search to see your research history here.</p>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-between">
            <Link href="/search">
              <Button variant="outline">
                Back to Search
              </Button>
            </Link>
            <Link href="/market-analysis">
              <Button className="bg-green-600 hover:bg-green-700">
                Market Analysis
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
