'use client'

import { useState, useEffect, useCallback } from 'react'
import { User, CheckCircle, Clock, TrendingUp, Award } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import type { SupabaseClient } from '@supabase/supabase-js'

interface UserProfile {
  id: string
  email: string
  subscription_tier: string
  first_time_user: boolean
  pulls_this_month: number
  extra_credits: number
  created_at: string
}

interface UserProgress {
  profile_completed: boolean
  first_search_completed: boolean
  tutorial_completed: boolean
  help_center_visited: boolean
  feature_request_submitted: boolean
  total_searches: number
  completion_percentage: number
}

interface UserProgressProps {
  profile: UserProfile
}

export function UserProgress({ profile }: UserProgressProps) {
  const [progress, setProgress] = useState<UserProgress | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const calculateProgress = useCallback(async (userId: string, supabase: SupabaseClient, profileData: UserProfile) => {
    try {
      // Get search history count
      const { count: totalSearches } = await supabase
        .from('search_history')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      // Check for tutorial completion (placeholder - would need actual tutorial tracking)
      const hasCompletedTutorial = false // This would be tracked in user preferences or separate table

      // Check for feature requests (placeholder - would need actual feature request tracking)
      const hasSubmittedFeatureRequest = false // This would be tracked in a feature requests table

      const steps = [
        { name: 'Profile Setup', completed: !profileData?.first_time_user },
        { name: 'First Search', completed: (totalSearches || 0) > 0 },
        { name: 'Tutorial', completed: hasCompletedTutorial },
        { name: 'Help Center', completed: true }, // Assume visited if they're here
        { name: 'Feature Request', completed: hasSubmittedFeatureRequest }
      ]

      const completedSteps = steps.filter(step => step.completed).length
      const completionPercentage = Math.round((completedSteps / steps.length) * 100)

      return {
        profile_completed: !profileData?.first_time_user,
        first_search_completed: (totalSearches || 0) > 0,
        tutorial_completed: hasCompletedTutorial,
        help_center_visited: true,
        feature_request_submitted: hasSubmittedFeatureRequest,
        total_searches: totalSearches || 0,
        completion_percentage: completionPercentage
      }
    } catch (error) {
      console.error('Error calculating progress:', error)
      return {
        profile_completed: false,
        first_search_completed: false,
        tutorial_completed: false,
        help_center_visited: false,
        feature_request_submitted: false,
        total_searches: 0,
        completion_percentage: 0
      }
    }
  }, [])

  const loadProgressData = useCallback(async () => {
    try {
      const supabase = createClient()
      
      // Calculate user progress using the profile data
      const progressData = await calculateProgress(profile.id, supabase, profile)
      setProgress(progressData)
      setLoading(false)
    } catch (error) {
      console.error('Error loading progress:', error)
      setError('Failed to load progress data')
      setLoading(false)
    }
  }, [profile, calculateProgress])

  useEffect(() => {
    loadProgressData()
  }, [loadProgressData])

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-6 bg-muted rounded w-1/3 mb-4"></div>
        <div className="space-y-3">
          <div className="h-4 bg-muted rounded"></div>
          <div className="h-4 bg-muted rounded w-5/6"></div>
          <div className="h-4 bg-muted rounded w-4/6"></div>
        </div>
      </div>
    )
  }

  if (error || !progress) {
    return (
      <div className="text-center py-4">
        <p className="text-destructive mb-4">{error || 'Failed to load progress'}</p>
        <button
          onClick={loadProgressData}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
        >
          Try Again
        </button>
      </div>
    )
  }

  const progressSteps = [
    {
      name: 'Profile Setup',
      completed: progress.profile_completed,
      icon: User,
      description: 'Complete your account setup'
    },
    {
      name: 'First Search',
      completed: progress.first_search_completed,
      icon: TrendingUp,
      description: 'Perform your first property search'
    },
    {
      name: 'Tutorial',
      completed: progress.tutorial_completed,
      icon: Clock,
      description: 'Complete the getting started tutorial'
    },
    {
      name: 'Help Center',
      completed: progress.help_center_visited,
      icon: CheckCircle,
      description: 'Visit the help center'
    },
    {
      name: 'Feature Request',
      completed: progress.feature_request_submitted,
      icon: Award,
      description: 'Submit a feature request'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Progress Overview */}
      <div className="bg-muted/30 p-6 rounded-lg border border-border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-foreground">Account Progress</h3>
          <div className="text-2xl font-bold text-primary">
            {progress.completion_percentage}%
          </div>
        </div>
        
        <div className="w-full bg-muted rounded-full h-3 mb-4">
          <div 
            className="bg-primary h-3 rounded-full transition-all duration-500"
            style={{ width: `${progress.completion_percentage}%` }}
          ></div>
        </div>
        
        <p className="text-sm text-muted-foreground">
          Complete all steps to unlock the full Ordrly experience
        </p>
      </div>

      {/* Progress Steps */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {progressSteps.map((step, index) => {
          const Icon = step.icon
          return (
            <div
              key={index}
              className={`p-4 rounded-lg border transition-all duration-200 ${
                step.completed
                  ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800'
                  : 'bg-muted/30 border-border hover:bg-muted/50'
              }`}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-full ${
                  step.completed 
                    ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400' 
                    : 'bg-muted text-muted-foreground'
                }`}>
                  <Icon className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <h4 className={`font-medium ${
                    step.completed ? 'text-green-700 dark:text-green-300' : 'text-foreground'
                  }`}>
                    {step.name}
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {step.description}
                  </p>
                  {step.completed && (
                    <div className="flex items-center mt-2">
                      <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mr-1" />
                      <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                        Completed
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-muted/30 p-4 rounded-lg border border-border text-center">
          <div className="text-2xl font-bold text-primary mb-1">
            {progress.total_searches}
          </div>
          <div className="text-sm text-muted-foreground">Total Searches</div>
        </div>
        
        <div className="bg-muted/30 p-4 rounded-lg border border-border text-center">
          <div className="text-2xl font-bold text-primary mb-1">
            {Math.round((Date.now() - new Date(profile.created_at).getTime()) / (1000 * 60 * 60 * 24))}
          </div>
          <div className="text-sm text-muted-foreground">Days Active</div>
        </div>
        
        <div className="bg-muted/30 p-4 rounded-lg border border-border text-center">
          <div className="text-2xl font-bold text-primary mb-1">
            {profile.subscription_tier === 'free' ? 'Free' : 'Pro'}
          </div>
          <div className="text-sm text-muted-foreground">Account Tier</div>
        </div>
      </div>
    </div>
  )
}
