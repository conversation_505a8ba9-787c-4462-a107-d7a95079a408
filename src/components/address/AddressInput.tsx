'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { AddressOption, AddressInputProps } from '@/lib/types/address'

export function AddressInput({
  onSelect,
  onAddressSelect,
  onInputChange,
  placeholder = "Enter an address...",
  defaultValue,
  className = "",
  ...props
}: AddressInputProps & {
  onAddressSelect?: (address: {
    label: string
    lat: number
    lng: number
    county?: string
    state?: string
    zip?: string
  } | null) => void
  onInputChange?: (value: string) => void
  'aria-label'?: string
  'aria-describedby'?: string
  id?: string
}) {
  const [inputValue, setInputValue] = useState(defaultValue?.label || '')
  const [suggestions, setSuggestions] = useState<AddressOption[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)

  // Update input value when defaultValue changes (for URL parameter updates)
  useEffect(() => {
    if (defaultValue?.label && defaultValue.label !== inputValue) {
      setInputValue(defaultValue.label)
    }
  }, [defaultValue?.label])
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Fetch suggestions
  const fetchSuggestions = useCallback(async (value: string) => {
    if (value.length < 3) {
      setSuggestions([])
      setShowSuggestions(false)
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/address/autocomplete?q=${encodeURIComponent(value)}`)
      if (response.ok) {
        const data = await response.json()
        setSuggestions(data.suggestions || [])
        setShowSuggestions(true)
        setSelectedIndex(-1)
      }
    } catch (error) {
      console.error('Address autocomplete error:', error)
      setSuggestions([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Debounced fetch
  const debouncedFetch = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout
      return (value: string) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => fetchSuggestions(value), 300)
      }
    })(),
    [fetchSuggestions]
  )

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    onInputChange?.(value)
    debouncedFetch(value)
  }, [onInputChange, debouncedFetch])

  // Handle suggestion selection
  const selectSuggestion = useCallback((suggestion: AddressOption) => {
    setInputValue(suggestion.label)
    setShowSuggestions(false)
    setSelectedIndex(-1)

    const addressData = {
      label: suggestion.label,
      lat: suggestion.lat,
      lng: suggestion.lng,
      county: suggestion.county,
      state: suggestion.state,
      zip: suggestion.zip
    }

    // Only call callbacks, don't trigger any auto-submission
    onSelect?.(addressData)
    onAddressSelect?.(addressData)
    onInputChange?.(suggestion.label)
  }, [onSelect, onAddressSelect, onInputChange])

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Enter') {
        // Allow form submission with current text - don't prevent default
        setShowSuggestions(false)
        // If there's text but no suggestions, treat it as a manual address entry
        if (inputValue.trim()) {
          const manualAddressData = {
            label: inputValue.trim(),
            lat: 0, // Will need geocoding
            lng: 0, // Will need geocoding
            county: undefined,
            state: undefined,
            zip: undefined
          }
          onSelect?.(manualAddressData)
          onAddressSelect?.(manualAddressData)
        }
      }
      return
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          selectSuggestion(suggestions[selectedIndex])
        } else {
          // Allow form submission with current text
          setShowSuggestions(false)
        }
        break
      case 'Escape':
        setShowSuggestions(false)
        setSelectedIndex(-1)
        break
    }
  }, [showSuggestions, suggestions, selectedIndex, selectSuggestion])

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={`relative ${className}`}>
      <input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={() => {
          if (inputValue.length >= 3 && suggestions.length > 0) {
            setShowSuggestions(true)
          }
        }}
        placeholder={placeholder}
        className="w-full h-10 px-3 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-ring focus:outline-none bg-background text-foreground placeholder:text-muted-foreground text-sm font-medium"
        autoComplete="off"
        {...props}
      />

      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
        </div>
      )}

      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-popover border border-border rounded-lg shadow-lg max-h-60 overflow-y-auto chat-scrollbar"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion.value}
              className={`px-4 py-3 cursor-pointer border-b border-border last:border-b-0 ${
                index === selectedIndex
                  ? 'bg-accent text-accent-foreground'
                  : 'hover:bg-muted text-popover-foreground'
              }`}
              onClick={() => selectSuggestion(suggestion)}
              onMouseEnter={() => setSelectedIndex(index)}
            >
              {suggestion.label}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
