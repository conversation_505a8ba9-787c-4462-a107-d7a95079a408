'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Play,
  Settings,
  Filter,
  Download
} from 'lucide-react'
import { StalenessReportResponse, RefreshJobCreateRequest } from '@/lib/types/knowledge'
import { toast } from 'sonner'

interface KnowledgeStats {
  total_documents: number
  active_documents: number
  stale_documents: number
  updates_available: number
  average_staleness: number
  last_refresh: string | null
  running_jobs: number
}

interface KnowledgeManagementDashboardProps {
  stats: KnowledgeStats | null
  stalenessReport: StalenessReportResponse | null
  onRefresh: () => void
}

export function KnowledgeManagementDashboard({ 
  stats, 
  stalenessReport, 
  onRefresh 
}: KnowledgeManagementDashboardProps) {
  const [refreshing, setRefreshing] = useState(false)
  const [showRefreshForm, setShowRefreshForm] = useState(false)
  const [refreshForm, setRefreshForm] = useState({
    job_type: 'manual' as 'manual' | 'emergency',
    jurisdiction: '',
    document_type: '',
    include_inactive: false
  })

  const handleManualRefresh = async () => {
    try {
      setRefreshing(true)

      // Build scope from form
      const scope: any = {}
      if (refreshForm.jurisdiction) {
        scope.jurisdiction = [refreshForm.jurisdiction]
      }
      if (refreshForm.document_type) {
        scope.document_type = [refreshForm.document_type]
      }
      if (refreshForm.include_inactive) {
        scope.include_inactive = true
      }

      // If no specific scope, refresh everything
      if (Object.keys(scope).length === 0) {
        scope.jurisdiction = ['*'] // Wildcard for all jurisdictions
      }

      const request: RefreshJobCreateRequest = {
        job_type: refreshForm.job_type,
        scope
      }

      const response = await fetch('/api/admin/knowledge/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start refresh job')
      }

      toast.success(`Refresh job started successfully. Job ID: ${data.job_id}`)
      setShowRefreshForm(false)
      onRefresh()

    } catch (error) {
      console.error('Error starting refresh job:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to start refresh job')
    } finally {
      setRefreshing(false)
    }
  }

  const handleStalenessCheck = async () => {
    try {
      setRefreshing(true)

      const response = await fetch('/api/admin/knowledge/staleness', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update staleness tracking')
      }

      toast.success('Staleness tracking updated successfully')
      onRefresh()

    } catch (error) {
      console.error('Error updating staleness:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update staleness tracking')
    } finally {
      setRefreshing(false)
    }
  }

  const getUniqueJurisdictions = () => {
    if (!stalenessReport?.tracking) return []
    return [...new Set(stalenessReport.tracking.map(t => t.jurisdiction))].sort()
  }

  const getUniqueDocumentTypes = () => {
    if (!stalenessReport?.tracking) return []
    return [...new Set(stalenessReport.tracking.map(t => t.document_type))].sort()
  }

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Quick Actions
            <div className="flex space-x-2">
              <Button
                onClick={handleStalenessCheck}
                disabled={refreshing}
                variant="outline"
                size="sm"
              >
                <Clock className="w-4 h-4 mr-2" />
                Update Staleness
              </Button>
              <Button
                onClick={() => setShowRefreshForm(!showRefreshForm)}
                disabled={refreshing}
                size="sm"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Manual Refresh
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {showRefreshForm && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
              <h4 className="font-medium">Configure Refresh Job</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="job_type">Job Type</Label>
                  <Select
                    value={refreshForm.job_type}
                    onValueChange={(value) =>
                      setRefreshForm(prev => ({ ...prev, job_type: value as 'manual' | 'emergency' }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manual">Manual</SelectItem>
                      <SelectItem value="emergency">Emergency</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="jurisdiction">Jurisdiction (Optional)</Label>
                  <Select
                    value={refreshForm.jurisdiction}
                    onValueChange={(value) => 
                      setRefreshForm(prev => ({ ...prev, jurisdiction: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All jurisdictions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All jurisdictions</SelectItem>
                      {getUniqueJurisdictions().map(jurisdiction => (
                        <SelectItem key={jurisdiction} value={jurisdiction}>
                          {jurisdiction}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="document_type">Document Type (Optional)</Label>
                  <Select
                    value={refreshForm.document_type}
                    onValueChange={(value) => 
                      setRefreshForm(prev => ({ ...prev, document_type: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All document types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All document types</SelectItem>
                      {getUniqueDocumentTypes().map(docType => (
                        <SelectItem key={docType} value={docType}>
                          {docType}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include_inactive"
                    checked={refreshForm.include_inactive}
                    onCheckedChange={(checked) => 
                      setRefreshForm(prev => ({ ...prev, include_inactive: !!checked }))
                    }
                  />
                  <Label htmlFor="include_inactive">Include inactive documents</Label>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  onClick={handleManualRefresh}
                  disabled={refreshing}
                  size="sm"
                >
                  {refreshing ? (
                    <LoadingSpinner size="sm" className="mr-2" />
                  ) : (
                    <Play className="w-4 h-4 mr-2" />
                  )}
                  Start Refresh
                </Button>
                <Button
                  onClick={() => setShowRefreshForm(false)}
                  variant="outline"
                  size="sm"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle>System Health Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                {(stats?.stale_documents || 0) === 0 ? (
                  <CheckCircle className="w-8 h-8 text-green-500" />
                ) : (
                  <AlertTriangle className="w-8 h-8 text-orange-500" />
                )}
              </div>
              <h4 className="font-medium">Data Freshness</h4>
              <p className="text-sm text-gray-600">
                {stats?.stale_documents || 0} stale documents
              </p>
              <p className="text-xs text-gray-500">
                Avg staleness: {Math.round((stats?.average_staleness || 0) * 100)}%
              </p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                {(stats?.running_jobs || 0) === 0 ? (
                  <CheckCircle className="w-8 h-8 text-green-500" />
                ) : (
                  <Clock className="w-8 h-8 text-blue-500" />
                )}
              </div>
              <h4 className="font-medium">Background Jobs</h4>
              <p className="text-sm text-gray-600">
                {stats?.running_jobs || 0} running jobs
              </p>
              <p className="text-xs text-gray-500">
                Last refresh: {stats?.last_refresh ? 
                  new Date(stats.last_refresh).toLocaleDateString() : 'Never'
                }
              </p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                {(stats?.updates_available || 0) === 0 ? (
                  <CheckCircle className="w-8 h-8 text-green-500" />
                ) : (
                  <AlertTriangle className="w-8 h-8 text-orange-500" />
                )}
              </div>
              <h4 className="font-medium">Updates Available</h4>
              <p className="text-sm text-gray-600">
                {stats?.updates_available || 0} pending updates
              </p>
              <p className="text-xs text-gray-500">
                Total docs: {stats?.total_documents || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Staleness Issues */}
      {stalenessReport && stalenessReport.tracking.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Staleness Issues</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stalenessReport.tracking
                .filter(item => item.staleness_score > 0.3)
                .slice(0, 5)
                .map((item) => (
                  <div key={`${item.jurisdiction}-${item.document_type}`} 
                       className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h5 className="font-medium">{item.jurisdiction}</h5>
                      <p className="text-sm text-gray-600">{item.document_type}</p>
                      <p className="text-xs text-gray-500">
                        Last checked: {new Date(item.last_checked_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={
                        item.staleness_score < 0.6 ? 'secondary' : 'destructive'
                      }>
                        {Math.round(item.staleness_score * 100)}% stale
                      </Badge>
                      {item.update_available && (
                        <Badge variant="outline">Update Available</Badge>
                      )}
                    </div>
                  </div>
                ))}
              
              {stalenessReport.tracking.filter(item => item.staleness_score > 0.3).length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
                  <p>No staleness issues detected</p>
                  <p className="text-sm">All documents are up to date</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
