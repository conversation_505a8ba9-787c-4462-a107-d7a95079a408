'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { 
  Activity, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Zap,
  Target
} from 'lucide-react'
import { toast } from 'sonner'

interface PerformanceAnalytics {
  timeRange: string
  summary: {
    total_operations: number
    avg_duration_ms: number
    avg_items_per_second: number
    avg_success_rate: number
    total_items_processed: number
    total_errors: number
    fastest_operation_ms: number
    slowest_operation_ms: number
  }
  trends: {
    duration_trend: number
    throughput_trend: number
    success_rate_trend: number
    operations_trend: number
  }
  metrics: any[]
  metricsByType: Record<string, any[]>
  alerts: any[]
  alertsBySeverity: Record<string, number>
  unresolvedAlerts: number
}

interface PerformanceAnalyticsDashboardProps {
  className?: string
}

export function PerformanceAnalyticsDashboard({ className }: PerformanceAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<PerformanceAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('24h')
  const [operationType, setOperationType] = useState('')

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange, operationType])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        timeRange,
        ...(operationType && { operationType })
      })

      const response = await fetch(`/api/admin/knowledge/analytics?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const data = await response.json()
      setAnalytics(data)

    } catch (err) {
      console.error('Error fetching analytics:', err)
      setError(err instanceof Error ? err.message : 'Failed to load analytics')
    } finally {
      setLoading(false)
    }
  }

  const acknowledgeAlert = async (alertId: string, action: 'acknowledge' | 'resolve') => {
    try {
      const response = await fetch('/api/admin/knowledge/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ alertId, action }),
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} alert`)
      }

      toast.success(`Alert ${action}d successfully`)
      fetchAnalytics()

    } catch (error) {
      console.error(`Error ${action}ing alert:`, error)
      toast.error(error instanceof Error ? error.message : `Failed to ${action} alert`)
    }
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  const getTrendIcon = (trend: number) => {
    if (trend > 5) return <TrendingUp className="w-4 h-4 text-green-500" />
    if (trend < -5) return <TrendingDown className="w-4 h-4 text-red-500" />
    return <div className="w-4 h-4" />
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive'
      case 'high': return 'destructive'
      case 'medium': return 'secondary'
      case 'low': return 'outline'
      default: return 'outline'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-destructive mb-4">{error}</p>
            <Button onClick={fetchAnalytics} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!analytics) return null

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Controls */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Performance Analytics</h3>
        <div className="flex items-center space-x-4">
          <Select value={operationType} onValueChange={setOperationType}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All operations" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All operations</SelectItem>
              <SelectItem value="staleness_check">Staleness Check</SelectItem>
              <SelectItem value="auto_refresh">Auto Refresh</SelectItem>
              <SelectItem value="manual_refresh">Manual Refresh</SelectItem>
              <SelectItem value="version_update">Version Update</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Last Hour</SelectItem>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={fetchAnalytics} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Operations</p>
                <p className="text-2xl font-bold">{analytics.summary.total_operations}</p>
                <div className="flex items-center mt-1">
                  {getTrendIcon(analytics.trends.operations_trend)}
                  <span className="text-xs text-muted-foreground ml-1">
                    {analytics.trends.operations_trend > 0 ? '+' : ''}{analytics.trends.operations_trend}%
                  </span>
                </div>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Duration</p>
                <p className="text-2xl font-bold">{formatDuration(analytics.summary.avg_duration_ms)}</p>
                <div className="flex items-center mt-1">
                  {getTrendIcon(-analytics.trends.duration_trend)} {/* Negative because lower is better */}
                  <span className="text-xs text-muted-foreground ml-1">
                    {analytics.trends.duration_trend > 0 ? '+' : ''}{analytics.trends.duration_trend}%
                  </span>
                </div>
              </div>
              <Clock className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Throughput</p>
                <p className="text-2xl font-bold">{analytics.summary.avg_items_per_second}/s</p>
                <div className="flex items-center mt-1">
                  {getTrendIcon(analytics.trends.throughput_trend)}
                  <span className="text-xs text-muted-foreground ml-1">
                    {analytics.trends.throughput_trend > 0 ? '+' : ''}{analytics.trends.throughput_trend}%
                  </span>
                </div>
              </div>
              <Zap className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">{analytics.summary.avg_success_rate}%</p>
                <div className="flex items-center mt-1">
                  {getTrendIcon(analytics.trends.success_rate_trend)}
                  <span className="text-xs text-muted-foreground ml-1">
                    {analytics.trends.success_rate_trend > 0 ? '+' : ''}{analytics.trends.success_rate_trend}%
                  </span>
                </div>
              </div>
              <Target className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts Section */}
      {analytics.unresolvedAlerts > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-orange-500" />
                Active Alerts ({analytics.unresolvedAlerts})
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.alerts
                .filter(alert => !alert.resolved_at)
                .slice(0, 5)
                .map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge variant={getSeverityColor(alert.severity)}>
                          {alert.severity}
                        </Badge>
                        <span className="text-sm font-medium">{alert.alert_type.replace('_', ' ')}</span>
                      </div>
                      <p className="text-sm text-muted-foreground">{alert.message}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(alert.triggered_at).toLocaleString()}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      {!alert.acknowledged_at && (
                        <Button
                          onClick={() => acknowledgeAlert(alert.id, 'acknowledge')}
                          variant="outline"
                          size="sm"
                        >
                          Acknowledge
                        </Button>
                      )}
                      <Button
                        onClick={() => acknowledgeAlert(alert.id, 'resolve')}
                        variant="outline"
                        size="sm"
                      >
                        Resolve
                      </Button>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Operation Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.metricsByType).map(([type, metrics]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{type.replace('_', ' ')}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">{metrics.length} ops</span>
                    <span className="text-sm">
                      {formatDuration(metrics.reduce((sum, m) => sum + m.duration_ms, 0) / metrics.length)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Alert Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.alertsBySeverity).map(([severity, count]) => (
                <div key={severity} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant={getSeverityColor(severity)}>{severity}</Badge>
                  </div>
                  <span className="text-sm font-medium">{count}</span>
                </div>
              ))}
              {Object.keys(analytics.alertsBySeverity).length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
                  <p>No alerts in this time period</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
