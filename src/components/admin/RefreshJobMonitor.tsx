'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { 
  RefreshCw, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Play,
  Square,
  Trash2,
  Eye
} from 'lucide-react'
import { KnowledgeRefreshJob, RefreshJobStatusResponse } from '@/lib/types/knowledge'
import { toast } from 'sonner'

interface RefreshJobMonitorProps {
  jobs: KnowledgeRefreshJob[]
  onRefresh: () => void
}

export function RefreshJobMonitor({ jobs, onRefresh }: RefreshJobMonitorProps) {
  const [selectedJob, setSelectedJob] = useState<string | null>(null)
  const [jobDetails, setJobDetails] = useState<KnowledgeRefreshJob | null>(null)
  const [loading, setLoading] = useState(false)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Auto-refresh running jobs every 5 seconds
  useEffect(() => {
    const runningJobs = jobs.filter(job => ['pending', 'running'].includes(job.status))
    if (runningJobs.length === 0) return

    const interval = setInterval(() => {
      onRefresh()
    }, 5000)

    return () => clearInterval(interval)
  }, [jobs, onRefresh])

  const fetchJobDetails = async (jobId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/knowledge/status/${jobId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch job details')
      }

      const data: RefreshJobStatusResponse = await response.json()
      setJobDetails(data.job)
      setSelectedJob(jobId)
    } catch (error) {
      console.error('Error fetching job details:', error)
      toast.error('Failed to fetch job details')
    } finally {
      setLoading(false)
    }
  }

  const cancelJob = async (jobId: string) => {
    try {
      setActionLoading(jobId)
      const response = await fetch(`/api/admin/knowledge/status/${jobId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'cancel' }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to cancel job')
      }

      toast.success('Job cancelled successfully')
      onRefresh()
      
      if (selectedJob === jobId) {
        setSelectedJob(null)
        setJobDetails(null)
      }
    } catch (error) {
      console.error('Error cancelling job:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to cancel job')
    } finally {
      setActionLoading(null)
    }
  }

  const deleteJob = async (jobId: string) => {
    try {
      setActionLoading(jobId)
      const response = await fetch(`/api/admin/knowledge/status/${jobId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to delete job')
      }

      toast.success('Job deleted successfully')
      onRefresh()
      
      if (selectedJob === jobId) {
        setSelectedJob(null)
        setJobDetails(null)
      }
    } catch (error) {
      console.error('Error deleting job:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete job')
    } finally {
      setActionLoading(null)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'running':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'cancelled':
        return <Square className="w-4 h-4 text-gray-500" />
      default:
        return <AlertTriangle className="w-4 h-4 text-orange-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      running: 'default',
      completed: 'default',
      failed: 'destructive',
      cancelled: 'outline'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const calculateProgress = (job: KnowledgeRefreshJob) => {
    if (job.status === 'completed') return 100
    if (job.status === 'failed' || job.status === 'cancelled') return 0
    if (job.total_items === 0) return 0
    
    return Math.round((job.processed_items / job.total_items) * 100)
  }

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime)
    const end = endTime ? new Date(endTime) : new Date()
    const duration = end.getTime() - start.getTime()
    
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`
    }
    return `${seconds}s`
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Refresh Jobs
            <Button onClick={onRefresh} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {jobs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <RefreshCw className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No refresh jobs found</p>
              <p className="text-sm">Start a manual refresh to see jobs here</p>
            </div>
          ) : (
            <div className="space-y-4">
              {jobs.map((job) => (
                <div key={job.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(job.status)}
                      <div>
                        <h4 className="font-medium">
                          {job.job_type.charAt(0).toUpperCase() + job.job_type.slice(1)} Refresh
                        </h4>
                        <p className="text-sm text-gray-600">
                          Started: {new Date(job.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(job.status)}
                      <Button
                        onClick={() => fetchJobDetails(job.id)}
                        variant="outline"
                        size="sm"
                        disabled={loading}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      {['pending', 'running'].includes(job.status) && (
                        <Button
                          onClick={() => cancelJob(job.id)}
                          variant="outline"
                          size="sm"
                          disabled={actionLoading === job.id}
                        >
                          {actionLoading === job.id ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            <Square className="w-4 h-4" />
                          )}
                        </Button>
                      )}
                      {['completed', 'failed', 'cancelled'].includes(job.status) && (
                        <Button
                          onClick={() => deleteJob(job.id)}
                          variant="outline"
                          size="sm"
                          disabled={actionLoading === job.id}
                        >
                          {actionLoading === job.id ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Progress Bar */}
                  {['pending', 'running'].includes(job.status) && (
                    <div className="mb-3">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Progress</span>
                        <span>{job.processed_items} / {job.total_items}</span>
                      </div>
                      <Progress value={calculateProgress(job)} className="h-2" />
                    </div>
                  )}

                  {/* Job Scope */}
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Scope: </span>
                    {job.scope.jurisdiction ? `Jurisdiction: ${job.scope.jurisdiction.join(', ')}` : 'All jurisdictions'}
                    {job.scope.document_type && ` • Document Type: ${job.scope.document_type.join(', ')}`}
                  </div>

                  {/* Duration */}
                  {job.started_at && (
                    <div className="text-sm text-gray-600 mt-1">
                      <span className="font-medium">Duration: </span>
                      {formatDuration(job.started_at, job.completed_at || undefined)}
                    </div>
                  )}

                  {/* Error Message */}
                  {job.error_message && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                      <span className="font-medium">Error: </span>
                      {job.error_message}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Job Details Modal/Panel */}
      {selectedJob && jobDetails && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Job Details: {selectedJob.slice(0, 8)}...
              <Button
                onClick={() => {
                  setSelectedJob(null)
                  setJobDetails(null)
                }}
                variant="outline"
                size="sm"
              >
                Close
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium">Job Type</h5>
                  <p className="text-sm text-gray-600">{jobDetails.job_type}</p>
                </div>
                <div>
                  <h5 className="font-medium">Status</h5>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(jobDetails.status)}
                    <span className="text-sm">{jobDetails.status}</span>
                  </div>
                </div>
                <div>
                  <h5 className="font-medium">Progress</h5>
                  <p className="text-sm text-gray-600">
                    {jobDetails.processed_items} / {jobDetails.total_items} items
                  </p>
                </div>
                <div>
                  <h5 className="font-medium">Created</h5>
                  <p className="text-sm text-gray-600">
                    {new Date(jobDetails.created_at).toLocaleString()}
                  </p>
                </div>
              </div>

              {/* Progress Details */}
              {jobDetails.progress && Object.keys(jobDetails.progress).length > 0 && (
                <div>
                  <h5 className="font-medium mb-2">Progress Details</h5>
                  <pre className="text-sm bg-gray-50 p-3 rounded overflow-auto">
                    {JSON.stringify(jobDetails.progress, null, 2)}
                  </pre>
                </div>
              )}

              {/* Result Summary */}
              {jobDetails.result_summary && Object.keys(jobDetails.result_summary).length > 0 && (
                <div>
                  <h5 className="font-medium mb-2">Result Summary</h5>
                  <pre className="text-sm bg-gray-50 p-3 rounded overflow-auto">
                    {JSON.stringify(jobDetails.result_summary, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
