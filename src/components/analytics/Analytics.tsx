'use client'

import { useEffect } from 'react'
import Script from 'next/script'
import { Analytics } from '@vercel/analytics/react'

// Analytics configuration
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX'

// Google Analytics component
export function GoogleAnalytics() {
  useEffect(() => {
    // Initialize Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_title: document.title,
        page_location: window.location.href,
      })
    }
  }, [])

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GA_MEASUREMENT_ID}', {
            page_title: document.title,
            page_location: window.location.href,
          });
        `}
      </Script>
    </>
  )
}

// Performance monitoring component
export function PerformanceMonitoring() {
  useEffect(() => {
    // Web Vitals monitoring
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Monitor Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            
            // Track page load metrics
            if (window.gtag) {
              window.gtag('event', 'page_load_time', {
                event_category: 'Performance',
                event_label: 'Page Load',
                value: Math.round(navEntry.loadEventEnd - navEntry.loadEventStart),
                custom_map: {
                  metric_1: 'load_time'
                }
              })
            }
          }
          
          if (entry.entryType === 'largest-contentful-paint') {
            // Track LCP
            if (window.gtag) {
              window.gtag('event', 'largest_contentful_paint', {
                event_category: 'Performance',
                event_label: 'LCP',
                value: Math.round(entry.startTime),
              })
            }
          }
          
          if (entry.entryType === 'first-input') {
            // Track FID
            const fidEntry = entry as PerformanceEventTiming
            if (window.gtag) {
              window.gtag('event', 'first_input_delay', {
                event_category: 'Performance',
                event_label: 'FID',
                value: Math.round(fidEntry.processingStart - fidEntry.startTime),
              })
            }
          }
        }
      })

      // Observe performance entries
      try {
        observer.observe({ entryTypes: ['navigation', 'largest-contentful-paint', 'first-input'] })
      } catch (error) {
        console.warn('Performance observer not supported:', error)
      }

      // Cleanup
      return () => {
        observer.disconnect()
      }
    }
  }, [])

  return null
}

// Analytics tracking functions
export const analytics = {
  // Track page views
  pageView: (url: string, title?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_title: title || document.title,
        page_location: url,
      })
    }
  },

  // Track events
  event: (action: string, category: string, label?: string, value?: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value,
      })
    }
  },

  // Track search events
  search: (searchTerm: string, category?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'search', {
        search_term: searchTerm,
        event_category: category || 'Search',
      })
    }
  },

  // Track user interactions
  interaction: (element: string, action: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: 'User Interaction',
        event_label: element,
      })
    }
  },

  // Track errors
  error: (description: string, fatal: boolean = false) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: description,
        fatal: fatal,
      })
    }
  }
}

// Global gtag type declaration
declare global {
  interface Window {
    gtag: (...args: unknown[]) => void
    dataLayer: unknown[]
  }
}

// Combined Analytics Provider
export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      <GoogleAnalytics />
      <PerformanceMonitoring />
      <Analytics />
      {children}
    </>
  )
}
