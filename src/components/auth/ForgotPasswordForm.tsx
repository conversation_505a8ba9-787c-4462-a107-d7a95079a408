'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Mail, ArrowRight } from 'lucide-react'
import { forgotPassword } from '@/lib/auth/actions'

export function ForgotPasswordForm() {
  return (
    <form action={forgotPassword} className="space-y-5">
      <div className="space-y-2">
        <Label htmlFor="email" className="text-foreground font-space">Email</Label>
        <div className="relative">
          <Mail className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="Enter your email address"
            className="pl-11 bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary/50 focus:ring-primary/20 backdrop-blur-sm font-space"
            required
            autoFocus
          />
        </div>
      </div>

      <Button
        type="submit"
        className="w-full bg-primary hover:bg-primary/90 text-primary-foreground border border-primary/20 transition-all duration-300 hover:scale-105 font-exo"
      >
        Send Reset Link
        <ArrowRight className="w-5 h-5 ml-3" />
      </Button>
    </form>
  )
}
