'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input, FormField, FormItem, FormLabel, FormControl, FormMessage, useFormValidation } from '@/components/ui/form'
import { useToast } from '@/components/ui/toast'
import { login } from '@/lib/auth/actions'

interface LoginFormData extends Record<string, unknown> {
  email: string
  password: string
}

const validateLoginForm = (values: LoginFormData) => {
  const errors: Partial<Record<keyof LoginFormData, string>> = {}
  
  if (!values.email) {
    errors.email = 'Email is required'
  } else if (!/\S+@\S+\.\S+/.test(values.email)) {
    errors.email = 'Please enter a valid email address'
  }
  
  if (!values.password) {
    errors.password = 'Password is required'
  } else if (values.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
  }
  
  return errors
}

export function LoginForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { addToast } = useToast()
  
  const {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validate
  } = useFormValidation<LoginFormData>(
    { email: '', password: '' },
    validateLoginForm
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validate()) {
      addToast({
        type: 'error',
        title: 'Validation Error',
        description: 'Please fix the errors below and try again.'
      })
      return
    }

    setIsSubmitting(true)
    
    try {
      const formData = new FormData()
      formData.append('email', values.email)
      formData.append('password', values.password)

      // The login action handles redirects and error messages
      await login(formData)

    } catch (error) {
      console.error('Login error:', error)
      // Don't show toast here as the login action handles error redirects
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof LoginFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setValue(field, e.target.value)
  }

  const handleInputBlur = (field: keyof LoginFormData) => () => {
    setFieldTouched(field)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6" noValidate>
      <FormField>
        <FormItem>
          <FormLabel htmlFor="email" required>
            Email address
          </FormLabel>
          <FormControl>
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              placeholder="Enter your email"
              value={values.email}
              onChange={handleInputChange('email')}
              onBlur={handleInputBlur('email')}
              error={touched.email ? errors.email : undefined}
              success={touched.email && !errors.email && values.email.length > 0}
              aria-describedby={errors.email ? 'email-error' : undefined}
              className="transition-all duration-200"
            />
          </FormControl>
          {touched.email && errors.email && (
            <FormMessage id="email-error" type="error">
              {errors.email}
            </FormMessage>
          )}
        </FormItem>
      </FormField>

      <FormField>
        <FormItem>
          <FormLabel htmlFor="password" required>
            Password
          </FormLabel>
          <FormControl>
            <Input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              placeholder="Enter your password"
              value={values.password}
              onChange={handleInputChange('password')}
              onBlur={handleInputBlur('password')}
              error={touched.password ? errors.password : undefined}
              success={touched.password && !errors.password && values.password.length > 0}
              aria-describedby={errors.password ? 'password-error' : undefined}
              className="transition-all duration-200"
            />
          </FormControl>
          {touched.password && errors.password && (
            <FormMessage id="password-error" type="error">
              {errors.password}
            </FormMessage>
          )}
          <div className="mt-2">
            <Link
              href="/forgot-password"
              className="text-sm text-primary hover:text-primary/80 hover:underline transition-colors"
            >
              Forgot password?
            </Link>
          </div>
        </FormItem>
      </FormField>

      <Button
        type="submit"
        disabled={isSubmitting || !values.email || !values.password}
        className="w-full transition-all duration-200 hover:scale-[1.02] hover:bg-blue-600 focus:scale-[1.02] active:shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
        aria-describedby="submit-button-description"
      >
        {isSubmitting ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            Signing in...
          </div>
        ) : (
          'Sign in'
        )}
      </Button>

      {(isSubmitting || !values.email || !values.password) && (
        <p className="text-xs text-gray-400 text-center">
          {isSubmitting ? 'Please wait while we sign you in' : 'Please fill out all required fields'}
        </p>
      )}
      
      <div id="submit-button-description" className="sr-only">
        {isSubmitting ? 'Please wait while we sign you in' : 'Click to sign in to your account'}
      </div>
    </form>
  )
}
