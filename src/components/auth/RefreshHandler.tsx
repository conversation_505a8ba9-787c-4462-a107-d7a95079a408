'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'

export function RefreshHandler() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [hasProcessedRefresh, setHasProcessedRefresh] = useState(false)

  useEffect(() => {
    const refresh = searchParams.get('refresh')

    if (refresh === 'true' && !hasProcessedRefresh) {
      console.log('RefreshHandler: Processing refresh request')
      setHasProcessedRefresh(true)

      // Remove the refresh parameter from URL
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.delete('refresh')

      // Replace the URL without the refresh parameter
      window.history.replaceState({}, '', newUrl.toString())
      console.log('RefreshHandler: URL updated, triggering refresh')

      // Use router.refresh() instead of window.location.reload() for better browser compatibility
      // This is gentler and avoids the double navigation issue in Firefox
      router.refresh()
    }
  }, [searchParams, hasProcessedRefresh]) // Remove router dependency to prevent infinite loops

  return null
}
