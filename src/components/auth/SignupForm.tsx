'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input, FormField, FormItem, FormLabel, FormControl, FormMessage, useFormValidation } from '@/components/ui/form'
import { useToast } from '@/components/ui/toast'
import { signup } from '@/lib/auth/actions'

interface SignupFormData extends Record<string, unknown> {
  email: string
  password: string
  referralCode: string
}

const validateSignupForm = (values: SignupFormData) => {
  const errors: Partial<Record<keyof SignupFormData, string>> = {}
  
  if (!values.email) {
    errors.email = 'Email is required'
  } else if (!/\S+@\S+\.\S+/.test(values.email)) {
    errors.email = 'Please enter a valid email address'
  }
  
  if (!values.password) {
    errors.password = 'Password is required'
  } else if (values.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
  }
  
  return errors
}

interface SignupFormProps {
  referralCode?: string
}

export function SignupForm({ referralCode = '' }: SignupFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { addToast } = useToast()
  
  const {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validate
  } = useFormValidation<SignupFormData>(
    { email: '', password: '', referralCode },
    validateSignupForm
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validate()) {
      addToast({
        type: 'error',
        title: 'Validation Error',
        description: 'Please fix the errors below and try again.'
      })
      return
    }

    setIsSubmitting(true)
    
    try {
      const formData = new FormData()
      formData.append('email', values.email)
      formData.append('password', values.password)
      formData.append('referralCode', values.referralCode)

      // The signup action handles redirects and error messages
      await signup(formData)

    } catch (error) {
      console.error('Signup error:', error)
      // Don't show toast here as the signup action handles error redirects
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof SignupFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setValue(field, e.target.value)
  }

  const handleInputBlur = (field: keyof SignupFormData) => () => {
    setFieldTouched(field)
  }

  const isFormValid = values.email && values.password && values.password.length >= 6 && !errors.email && !errors.password

  return (
    <form onSubmit={handleSubmit} className="space-y-5" noValidate>
      <FormField>
        <FormItem>
          <FormLabel htmlFor="email" required>
            Email address
          </FormLabel>
          <FormControl>
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              placeholder="Enter your email"
              value={values.email}
              onChange={handleInputChange('email')}
              onBlur={handleInputBlur('email')}
              error={touched.email ? errors.email : undefined}
              success={touched.email && !errors.email && values.email.length > 0}
              aria-describedby={errors.email ? 'email-error' : undefined}
              className="transition-all duration-200"
            />
          </FormControl>
          {touched.email && errors.email && (
            <FormMessage id="email-error" type="error">
              {errors.email}
            </FormMessage>
          )}
        </FormItem>
      </FormField>

      <FormField>
        <FormItem>
          <FormLabel htmlFor="password" required>
            Password
          </FormLabel>
          <FormControl>
            <Input
              id="password"
              name="password"
              type="password"
              autoComplete="new-password"
              placeholder="Create a password (min 6 characters)"
              value={values.password}
              onChange={handleInputChange('password')}
              onBlur={handleInputBlur('password')}
              error={touched.password ? errors.password : undefined}
              success={touched.password && !errors.password && values.password.length > 0}
              aria-describedby={errors.password ? 'password-error' : undefined}
              className="transition-all duration-200"
            />
          </FormControl>
          <p className="text-xs text-gray-400 mt-1">
            Must be at least 6 characters.
          </p>
          {touched.password && errors.password && (
            <FormMessage id="password-error" type="error">
              {errors.password}
            </FormMessage>
          )}
        </FormItem>
      </FormField>

      <FormField>
        <FormItem>
          <FormLabel htmlFor="referralCode">
            Referral Code <span className="text-muted-foreground">(optional)</span>
          </FormLabel>
          <FormControl>
            <Input
              id="referralCode"
              name="referralCode"
              type="text"
              placeholder="Enter referral code (optional)"
              value={values.referralCode}
              onChange={handleInputChange('referralCode')}
              onBlur={handleInputBlur('referralCode')}
              style={{ textTransform: 'uppercase' }}
              className="transition-all duration-200"
            />
          </FormControl>
          {referralCode && (
            <p className="mt-2 text-xs text-success bg-success/10 border border-success/20 rounded px-2 py-1">
              ✓ Referral code applied - you&apos;ll help your friend earn 2 extra searches!
            </p>
          )}
        </FormItem>
      </FormField>

      <Button
        type="submit"
        disabled={isSubmitting || !isFormValid}
        className="w-full transition-all duration-200 hover:scale-[1.02] hover:bg-blue-600 focus:scale-[1.02] active:shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
        size="lg"
      >
        {isSubmitting ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            Creating account...
          </div>
        ) : (
          'Create Account'
        )}
      </Button>
      
      {!isFormValid && !isSubmitting && (
        <p className="text-xs text-gray-400 text-center">
          Please fill out all required fields.
        </p>
      )}
    </form>
  )
}
