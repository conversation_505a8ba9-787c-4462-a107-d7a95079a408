'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

import { Download, CreditCard, X, AlertTriangle, CheckCircle } from 'lucide-react'

interface BillingManagementProps {
  profile: {
    subscription_tier: string
    subscription_status?: string
    stripe_customer_id?: string
    cancel_at_period_end?: boolean
  }
}

export function BillingManagement({ profile }: BillingManagementProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)

  const handleManageBilling = async () => {
    setIsLoading(true)
    try {
      window.location.href = '/api/billing/portal'
    } catch (error) {
      console.error('Error accessing billing portal:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelSubscription = async () => {
    setIsLoading(true)
    try {
      // This would call an API to cancel the subscription
      const response = await fetch('/api/billing/cancel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (response.ok) {
        window.location.reload()
      } else {
        throw new Error('Failed to cancel subscription')
      }
    } catch (error) {
      console.error('Error canceling subscription:', error)
      alert('Failed to cancel subscription. Please try again or contact support.')
    } finally {
      setIsLoading(false)
      setShowCancelConfirm(false)
    }
  }

  const handleUpdatePaymentMethod = async () => {
    setIsLoading(true)
    try {
      // Redirect to Stripe portal for payment method updates
      window.location.href = '/api/billing/portal'
    } catch (error) {
      console.error('Error updating payment method:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownloadInvoice = async (invoiceId: string) => {
    try {
      const response = await fetch(`/api/billing/invoice/${invoiceId}/download`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `invoice-${invoiceId}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error downloading invoice:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Subscription Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Subscription Status
            <Badge variant={profile.subscription_status === 'active' ? 'default' : 'secondary'}>
              {profile.subscription_status || 'Active'}
            </Badge>
          </CardTitle>
          <CardDescription>
            Current plan: {profile.subscription_tier} tier
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {profile.cancel_at_period_end && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                  <span className="text-sm text-yellow-800">
                    Your subscription will cancel at the end of the current billing period.
                  </span>
                </div>
              </div>
            )}

            <div className="flex flex-wrap gap-2">
              <Button onClick={handleManageBilling} disabled={isLoading}>
                <CreditCard className="h-4 w-4 mr-2" />
                Manage Billing
              </Button>

              {profile.subscription_tier !== 'free' && !profile.cancel_at_period_end && (
                <Button
                  variant="outline"
                  onClick={() => setShowCancelConfirm(true)}
                  disabled={isLoading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel Subscription
                </Button>
              )}

              <Button
                variant="outline"
                onClick={handleUpdatePaymentMethod}
                disabled={isLoading}
              >
                Update Payment Method
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>
            View and download your past invoices, payments, and transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Mock billing history - in real app this would come from Stripe */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">Invoice #INV-001</p>
                <p className="text-sm text-gray-600">
                  December 2024 •
                  {profile.subscription_tier === 'starter' && ' $49.00'}
                  {profile.subscription_tier === 'professional' && ' $99.00'}
                  {(profile.subscription_tier === 'pro' || profile.subscription_tier === 'appraiser') && ' $19.00'}
                  {profile.subscription_tier === 'business' && ' Custom amount'}
                  {' • Payment processed'}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Paid
                </Badge>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDownloadInvoice('INV-001')}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download Invoice
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">Invoice #INV-002</p>
                <p className="text-sm text-gray-600">
                  November 2024 •
                  {profile.subscription_tier === 'starter' && ' $49.00'}
                  {profile.subscription_tier === 'professional' && ' $99.00'}
                  {(profile.subscription_tier === 'pro' || profile.subscription_tier === 'appraiser') && ' $19.00'}
                  {profile.subscription_tier === 'business' && ' Custom amount'}
                  {' • Transaction completed'}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Paid
                </Badge>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDownloadInvoice('INV-002')}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download Invoice
                </Button>
              </div>
            </div>

            <p className="text-sm text-gray-500 text-center py-4">
              Showing recent billing history and invoices. For complete payment records, use the billing portal.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Cancel Confirmation Modal */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="text-red-600">Cancel Subscription</CardTitle>
              <CardDescription>
                Are you sure you want to cancel your subscription? You&apos;ll lose access to premium features at the end of your billing period.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowCancelConfirm(false)}
                  className="flex-1"
                >
                  Keep Subscription
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleCancelSubscription}
                  disabled={isLoading}
                  className="flex-1"
                >
                  Cancel Subscription
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
