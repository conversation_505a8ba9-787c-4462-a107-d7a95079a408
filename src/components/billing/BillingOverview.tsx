'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

import {
  CreditCard,
  Calendar,
  TrendingUp,
  Download,
  ExternalLink,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpCircle,
  Settings,
  RefreshCw,
  AlertCircle,
  WifiOff
} from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from 'sonner'
import Link from 'next/link'

interface BillingData {
  subscription: {
    id?: string
    tier: string
    status: string
    current_period_start?: number
    current_period_end?: number
    cancel_at_period_end: boolean
    canceled_at?: number
    amount?: number
    currency?: string
    interval?: string
  }
  usage: {
    searches_used: number
    search_limit: number
    extra_credits: number
  }
  payment_method: {
    type: string
    card?: {
      brand: string
      last4: string
      exp_month: number
      exp_year: number
    }
  } | null
  recent_invoices: Array<{
    id: string
    number: string
    amount_paid: number
    currency: string
    status: string
    created: number
    hosted_invoice_url: string
    invoice_pdf: string
  }>
  upcoming_invoice: {
    amount_due: number
    currency: string
    period_start: number
    period_end: number
  } | null
}

export function BillingOverview() {
  const [billingData, setBillingData] = useState<BillingData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showPortalReturn, setShowPortalReturn] = useState(false)
  const [isRetrying, setIsRetrying] = useState(false)
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    // Check if user returned from billing portal
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('portal_return') === 'true') {
      setShowPortalReturn(true)
      // Remove the parameter from URL
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.delete('portal_return')
      window.history.replaceState({}, '', newUrl.toString())

      // Hide the message after 5 seconds
      setTimeout(() => setShowPortalReturn(false), 5000)
    }

    // Monitor online status
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    const fetchBillingData = async (isRetry = false) => {
      try {
        if (isRetry) {
          setIsRetrying(true)
          setError(null)
        }

        // Add timeout to prevent hanging
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

        const response = await fetch('/api/billing/overview', {
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const data = await response.json().catch(() => ({}))
          throw new Error(data.error || `HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        setBillingData(data)
        setError(null)

        if (isRetry) {
          toast.success('Billing data refreshed', {
            description: 'Your billing information has been updated.'
          })
        }
      } catch (err) {
        console.error('Error fetching billing data:', err)

        let errorMessage = 'Failed to load billing data'
        let errorDescription = 'Please try again or contact support if the problem persists.'

        if (err instanceof Error && err.name === 'AbortError') {
          errorMessage = 'Request timed out'
          errorDescription = 'Please check your internet connection and try again.'
        } else if (!isOnline) {
          errorMessage = 'No internet connection'
          errorDescription = 'Please check your connection and try again.'
        } else if (err instanceof Error) {
          errorMessage = err.message
        }

        setError(errorMessage)

        if (isRetry) {
          toast.error('Failed to refresh billing data', {
            description: errorDescription
          })
        }

        // Use fallback data for better user experience
        if (!billingData) {
          const fallbackData: BillingData = {
            subscription: {
              tier: 'trial',
              status: 'active',
              cancel_at_period_end: false,
            },
            usage: {
              searches_used: 0,
              search_limit: 500,
              extra_credits: 0,
            },
            payment_method: null,
            recent_invoices: [],
            upcoming_invoice: null,
          }
          setBillingData(fallbackData)
        }
      } finally {
        setIsLoading(false)
        setIsRetrying(false)
      }
    }

    fetchBillingData()

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [billingData, isOnline])

  const retryFetch = () => {
    const fetchBillingData = async () => {
      try {
        setIsRetrying(true)
        setError(null)

        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000)

        const response = await fetch('/api/billing/overview', {
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const data = await response.json().catch(() => ({}))
          throw new Error(data.error || `HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        setBillingData(data)
        setError(null)

        toast.success('Billing data refreshed')
      } catch (err) {
        console.error('Error fetching billing data:', err)

        let errorMessage = 'Failed to load billing data'
        if (err instanceof Error && err.name === 'AbortError') {
          errorMessage = 'Request timed out'
        } else if (!isOnline) {
          errorMessage = 'No internet connection'
        } else if (err instanceof Error) {
          errorMessage = err.message
        }

        setError(errorMessage)
        toast.error('Failed to refresh billing data')
      } finally {
        setIsRetrying(false)
      }
    }

    fetchBillingData()
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map(i => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Show error state with retry option if no billing data is available
  if (error && !billingData) {
    return (
      <div className="space-y-6">
        <Card className="border-red-200">
          <CardContent className="p-8 text-center">
            <div className="flex flex-col items-center space-y-4">
              {!isOnline ? (
                <WifiOff className="h-12 w-12 text-red-500" />
              ) : (
                <AlertCircle className="h-12 w-12 text-red-500" />
              )}

              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-red-900">
                  {!isOnline ? 'No Internet Connection' : 'Unable to Load Billing Data'}
                </h3>
                <p className="text-red-700 max-w-md">
                  {error}
                </p>
              </div>

              <Alert className="max-w-md">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {!isOnline
                    ? 'Please check your internet connection and try again.'
                    : 'This might be a temporary issue. Please try refreshing or contact support if the problem persists.'
                  }
                </AlertDescription>
              </Alert>

              <div className="flex gap-2">
                <Button
                  onClick={retryFetch}
                  disabled={isRetrying}
                  className="min-w-[120px]"
                >
                  {isRetrying ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Retrying...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Try Again
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </Button>
              </div>

              {!isOnline && (
                <div className="flex items-center text-sm text-gray-600 mt-4">
                  <WifiOff className="h-4 w-4 mr-2" />
                  <span>You appear to be offline</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!billingData) {
    // Provide fallback data structure for testing
    const fallbackData: BillingData = {
      subscription: {
        tier: 'trial',
        status: 'active',
        cancel_at_period_end: false,
      },
      usage: {
        searches_used: 0,
        search_limit: 500,
        extra_credits: 0,
      },
      payment_method: null,
      recent_invoices: [],
      upcoming_invoice: null,
    }
    setBillingData(fallbackData)
    return null // Will re-render with fallback data
  }

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100)
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'canceled':
        return <Badge variant="destructive">Canceled</Badge>
      case 'past_due':
        return <Badge className="bg-yellow-100 text-yellow-800">Past Due</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Connection Status & Error Banner */}
      {!isOnline && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-orange-800">
              <WifiOff className="h-5 w-5" />
              <div>
                <p className="font-medium">You&apos;re offline</p>
                <p className="text-sm">Some information may be outdated. Please check your connection.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Banner (when data is available but there was an error) */}
      {error && billingData && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-5 w-5" />
                <div>
                  <p className="font-medium">Data may be outdated</p>
                  <p className="text-sm">{error}</p>
                </div>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={retryFetch}
                disabled={isRetrying}
                className="border-yellow-300 text-yellow-800 hover:bg-yellow-100"
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Refresh
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Welcome Back Message */}
      {showPortalReturn && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <div>
                <p className="font-medium">Welcome back!</p>
                <p className="text-sm">Your billing information has been updated. Changes may take a moment to reflect here.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Subscription Status */}
      <Card data-testid="current-plan-section">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2" data-testid="current-plan-title">
                <TrendingUp className="h-5 w-5" />
                Current Plan
              </CardTitle>
              <CardDescription>
                Your subscription status and billing information
              </CardDescription>
            </div>
            {getStatusBadge(billingData.subscription.status)}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div data-testid="plan-details">
              <h4 className="font-semibold text-lg capitalize" data-testid="plan-name">
                {billingData.subscription.tier} Plan
              </h4>
              {billingData.subscription.amount && (
                <p className="text-2xl font-bold text-blue-600" data-testid="plan-price">
                  {formatCurrency(billingData.subscription.amount, billingData.subscription.currency)}
                  <span className="text-sm font-normal text-gray-600">
                    /{billingData.subscription.interval}
                  </span>
                </p>
              )}
            </div>

            {billingData.subscription.current_period_end && (
              <div>
                <h4 className="font-semibold flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Next Billing Date
                </h4>
                <p className="text-lg">
                  {formatDate(billingData.subscription.current_period_end)}
                </p>
              </div>
            )}
          </div>

          {billingData.subscription.cancel_at_period_end && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                <span className="text-sm text-yellow-800">
                  Your subscription will cancel at the end of the current billing period.
                </span>
              </div>
            </div>
          )}

          <div className="flex flex-wrap gap-2" data-testid="plan-actions">
            {billingData.subscription.tier === 'trial' ? (
              <>
                <Link href="/checkout/starter">
                  <Button data-testid="upgrade-to-starter">
                    <ArrowUpCircle className="h-4 w-4 mr-2" />
                    Upgrade to Starter
                  </Button>
                </Link>
                <Link href="/checkout/professional">
                  <Button variant="outline" data-testid="upgrade-to-professional">
                    Upgrade to Professional
                  </Button>
                </Link>
              </>
            ) : (
              <>
                {billingData.subscription.tier === 'starter' && (
                  <Link href="/checkout/professional">
                    <Button data-testid="upgrade-to-professional">
                      <ArrowUpCircle className="h-4 w-4 mr-2" />
                      Upgrade to Professional
                    </Button>
                  </Link>
                )}
                <a href="/api/billing/portal" target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" data-testid="manage-subscription">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Subscription
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>
                </a>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2" data-testid="usage-title">
            <TrendingUp className="h-5 w-5" />
            Usage This Month
          </CardTitle>
          <CardDescription>
            Your search usage and available credits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4" data-testid="usage-statistics">
            <div className="text-center p-4 bg-blue-50 rounded-lg" data-testid="searches-used">
              <h4 className="font-semibold text-blue-900">Searches Used</h4>
              <p className="text-2xl font-bold text-blue-600">
                {billingData.usage.searches_used}
                {billingData.usage.search_limit > 0 && (
                  <span className="text-sm font-normal">
                    / {billingData.usage.search_limit}
                  </span>
                )}
              </p>
              {billingData.usage.search_limit === -1 && (
                <p className="text-sm text-blue-700">Unlimited</p>
              )}
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg" data-testid="extra-credits">
              <h4 className="font-semibold text-green-900">Extra Credits</h4>
              <p className="text-2xl font-bold text-green-600">
                {billingData.usage.extra_credits}
              </p>
              <p className="text-sm text-green-700">Available</p>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-lg" data-testid="plan-status-widget">
              <h4 className="font-semibold text-purple-900">Plan Status</h4>
              <p className="text-lg font-bold text-purple-600 capitalize">
                {billingData.subscription.tier}
              </p>
              {billingData.subscription.tier === 'free' && (
                <Link href="/checkout/credits">
                  <Button size="sm" className="mt-2" data-testid="buy-more-credits">
                    Buy More Credits
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Method & Billing History */}
      {billingData.subscription.tier !== 'free' && (
        <>
          {/* Payment Method */}
          <Card data-testid="payment-method-section">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Method
              </CardTitle>
              <CardDescription>
                Your default payment method for subscriptions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {billingData.payment_method?.card ? (
                <div className="flex items-center justify-between p-4 border rounded-lg" data-testid="payment-method-display">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-6 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-xs font-bold uppercase">
                        {billingData.payment_method.card.brand}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">
                        •••• •••• •••• {billingData.payment_method.card.last4}
                      </p>
                      <p className="text-sm text-gray-600">
                        Expires {billingData.payment_method.card.exp_month}/{billingData.payment_method.card.exp_year}
                      </p>
                    </div>
                  </div>
                  <a href="/api/billing/portal" target="_blank" rel="noopener noreferrer">
                    <Button variant="outline" size="sm" data-testid="update-payment-method">
                      Update
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </Button>
                  </a>
                </div>
              ) : (
                <div className="text-center p-6 border-2 border-dashed border-gray-300 rounded-lg">
                  <CreditCard className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 mb-4">No payment method on file</p>
                  <a href="/api/billing/portal" target="_blank" rel="noopener noreferrer">
                    <Button data-testid="add-payment-method">
                      Add Payment Method
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </Button>
                  </a>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Invoices */}
          <Card data-testid="recent-invoices-section">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Recent Invoices
              </CardTitle>
              <CardDescription>
                Your billing history and downloadable invoices
              </CardDescription>
            </CardHeader>
            <CardContent>
              {billingData.recent_invoices.length > 0 ? (
                <div className="space-y-3" data-testid="recent-invoices">
                  {billingData.recent_invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">
                          Invoice {invoice.number || invoice.id.slice(-8)}
                        </p>
                        <p className="text-sm text-gray-600">
                          {formatDate(invoice.created)} • {formatCurrency(invoice.amount_paid, invoice.currency)}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {invoice.status === 'paid' ? (
                          <Badge className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Paid
                          </Badge>
                        ) : (
                          <Badge variant="secondary">{invoice.status}</Badge>
                        )}
                        <a href={invoice.invoice_pdf} target="_blank" rel="noopener noreferrer">
                          <Button size="sm" variant="outline" data-testid="invoice-download">
                            <Download className="h-3 w-3 mr-1" />
                            PDF
                          </Button>
                        </a>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-4">
                    <a href="/api/billing/portal" target="_blank" rel="noopener noreferrer">
                      <Button variant="outline" data-testid="view-all-invoices">
                        View All Invoices
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Button>
                    </a>
                  </div>
                </div>
              ) : (
                <div className="text-center p-6 text-gray-600">
                  <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p>No invoices yet</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upcoming Invoice */}
          {billingData.upcoming_invoice && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Next Invoice
                </CardTitle>
                <CardDescription>
                  Your upcoming billing information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">
                        Next billing period: {formatDate(billingData.upcoming_invoice.period_start)} - {formatDate(billingData.upcoming_invoice.period_end)}
                      </p>
                      <p className="text-sm text-gray-600">
                        Amount due: {formatCurrency(billingData.upcoming_invoice.amount_due, billingData.upcoming_invoice.currency)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  )
}
