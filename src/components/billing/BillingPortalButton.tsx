'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ExternalLink,
  Settings,
  CreditCard,
  FileText,
  Shield,
  ArrowRight,
  Loader2
} from 'lucide-react'

interface BillingPortalButtonProps {
  variant?: 'default' | 'outline' | 'secondary'
  size?: 'default' | 'sm' | 'lg'
  showExplanation?: boolean
  className?: string
}

export function BillingPortalButton({
  variant = 'outline',
  size = 'default',
  showExplanation = false,
  className
}: BillingPortalButtonProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handlePortalAccess = async () => {
    setIsLoading(true)

    // Add a small delay to show loading state
    setTimeout(() => {
      window.open('/api/billing/portal', '_blank', 'noopener,noreferrer')
      setIsLoading(false)
    }, 500)
  }

  if (showExplanation) {
    return (
      <Card className="border-blue-200 bg-blue-50" data-testid="billing-portal-explanation">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900" data-testid="secure-billing-title">
            <Shield className="h-5 w-5" />
            Secure Billing Management
          </CardTitle>
          <CardDescription className="text-blue-700">
            Manage your subscription securely through Stripe&apos;s billing portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3" data-testid="portal-features">
            <div className="flex items-center gap-2 text-sm text-blue-800" data-testid="update-payment-feature">
              <CreditCard className="h-4 w-4" />
              Update payment methods
            </div>
            <div className="flex items-center gap-2 text-sm text-blue-800" data-testid="download-invoices-feature">
              <FileText className="h-4 w-4" />
              Download invoices
            </div>
            <div className="flex items-center gap-2 text-sm text-blue-800" data-testid="change-subscription-feature">
              <Settings className="h-4 w-4" />
              Change subscription
            </div>
          </div>

          <div className="flex items-center justify-between p-3 bg-white border border-blue-200 rounded-lg" data-testid="security-notice">
            <div>
              <p className="font-medium text-blue-900">
                You&apos;ll be redirected to Stripe&apos;s secure portal
              </p>
              <p className="text-sm text-blue-700" data-testid="industry-standard-text">
                Industry-standard billing management used by GitHub, Slack, and more
              </p>
            </div>
            <Badge className="bg-blue-100 text-blue-800" data-testid="secure-badge">
              <Shield className="h-3 w-3 mr-1" />
              Secure
            </Badge>
          </div>

          <Button
            onClick={handlePortalAccess}
            disabled={isLoading}
            className="w-full"
            size="lg"
            data-testid="open-billing-portal"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Opening Billing Portal...
              </>
            ) : (
              <>
                <Settings className="h-4 w-4 mr-2" />
                Access Billing Portal
                <ExternalLink className="h-3 w-3 ml-2" data-testid="external-link-icon" />
              </>
            )}
          </Button>

          <p className="text-xs text-blue-600 text-center" data-testid="portal-return-message">
            You&apos;ll return to Ordrly when finished. The portal opens in a new tab for security.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handlePortalAccess}
      disabled={isLoading}
      className={className}
    >
      {isLoading ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Opening...
        </>
      ) : (
        <>
          <Settings className="h-4 w-4 mr-2" />
          Manage Subscription
          <ExternalLink className="h-3 w-3 ml-1" />
        </>
      )}
    </Button>
  )
}

interface QuickActionsProps {
  currentTier: string
  hasActiveSubscription: boolean
}

export function BillingQuickActions({ currentTier, hasActiveSubscription }: QuickActionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowRight className="h-5 w-5" />
          Quick Actions
        </CardTitle>
        <CardDescription>
          Common billing tasks and account management
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {/* Upgrade Options */}
          {currentTier === 'trial' && (
            <a href="/checkout/starter" className="block">
              <Button className="justify-start h-auto p-4 w-full">
                <div className="text-left">
                  <div className="font-semibold">Upgrade to Starter</div>
                  <div className="text-sm opacity-90">$49/month • 500 searches</div>
                </div>
              </Button>
            </a>
          )}

          {currentTier === 'pro' && (
            <div className="text-center p-4 text-muted-foreground">
              <p className="text-sm">You're on the highest tier!</p>
            </div>
          )}

          {/* Credits for free users */}
          {currentTier === 'free' && (
            <a href="/checkout/credits" className="block">
              <Button variant="outline" className="justify-start h-auto p-4 w-full">
                <div className="text-left">
                  <div className="font-semibold">Buy More Searches</div>
                  <div className="text-sm opacity-70">5, 15, or 30 additional searches</div>
                </div>
              </Button>
            </a>
          )}

          {/* Billing Portal for paid users */}
          {hasActiveSubscription && (
            <div className="md:col-span-2">
              <BillingPortalButton
                showExplanation={false}
                variant="outline"
                className="w-full justify-start h-auto p-4"
              />
            </div>
          )}
        </div>

        {/* Billing Portal Explanation for paid users */}
        {hasActiveSubscription && (
          <div className="pt-4 border-t">
            <div className="text-sm text-gray-600 space-y-2" data-testid="billing-portal-explanation">
              <p className="font-medium">Need to manage your subscription?</p>
              <p>
                Use the billing portal above to update payment methods, download invoices,
                change plans, or cancel your subscription. It&apos;s the same secure system
                used by GitHub, Slack, and other leading companies.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
