'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowUpCircle, CreditCard, Settings, Zap, Star, Crown } from 'lucide-react'
import Link from 'next/link'
import { BillingPortalButton } from './BillingPortalButton'

interface BillingQuickActionsProps {
  currentTier: string
  hasActiveSubscription: boolean
}

export function BillingQuickActions({ currentTier, hasActiveSubscription }: BillingQuickActionsProps) {
  return (
    <div className="space-y-6" data-testid="billing-quick-actions">
      {/* Current Status */}
      <Card data-testid="current-status-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {currentTier === 'trial' && <Zap className="h-5 w-5 text-blue-500" />}
            {currentTier === 'starter' && <Star className="h-5 w-5 text-green-500" />}
            {currentTier === 'professional' && <Star className="h-5 w-5 text-purple-500" />}
            Current Plan
          </CardTitle>
          <CardDescription>
            Your subscription status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <Badge
              variant={currentTier === 'trial' ? 'secondary' : 'default'}
              className="text-lg px-4 py-2 capitalize"
              data-testid="plan-badge"
            >
              {currentTier} Plan
            </Badge>
            {currentTier === 'free' && (
              <p className="text-sm text-gray-600 mt-2">
                Limited to 3 searches per month
              </p>
            )}
            {currentTier === 'pro' && (
              <p className="text-sm text-gray-600 mt-2">
                Unlimited searches - $19/month
              </p>
            )}
            {currentTier === 'appraiser' && (
              <p className="text-sm text-gray-600 mt-2">
                API access - $59/month
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card data-testid="quick-actions-card">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manage your account and subscription
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3" data-testid="quick-actions">
          {currentTier === 'free' && (
            <>
              <Link href="/checkout/pro" className="block">
                <Button className="w-full" data-testid="upgrade-to-pro-quick">
                  <ArrowUpCircle className="h-4 w-4 mr-2" />
                  Upgrade to Pro
                </Button>
              </Link>
              <Link href="/checkout/appraiser" className="block">
                <Button variant="outline" className="w-full" data-testid="upgrade-to-appraiser-quick">
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade to Appraiser
                </Button>
              </Link>
              <Link href="/checkout/credits" className="block">
                <Button variant="secondary" className="w-full" data-testid="buy-credits-quick">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Buy More Searches
                </Button>
              </Link>
            </>
          )}

          {currentTier === 'pro' && (
            <>
              <Link href="/checkout/appraiser" className="block">
                <Button className="w-full" data-testid="upgrade-to-appraiser-quick">
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade to Appraiser
                </Button>
              </Link>
              <BillingPortalButton showExplanation={false} />
            </>
          )}

          {currentTier === 'appraiser' && (
            <BillingPortalButton showExplanation={false} />
          )}
        </CardContent>
      </Card>

      {/* Plan Comparison */}
      {currentTier === 'free' && (
        <Card data-testid="plan-comparison-card">
          <CardHeader>
            <CardTitle>Why Upgrade?</CardTitle>
            <CardDescription>
              See what you get with paid plans
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-purple-600">Pro Plan - $19/month</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Unlimited searches</li>
                <li>• Priority support</li>
                <li>• Advanced features</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-gold-600">Appraiser Plan - $59/month</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Everything in Pro</li>
                <li>• API access</li>
                <li>• Bulk operations</li>
                <li>• White-label options</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing Portal Access */}
      {hasActiveSubscription && (
        <Card data-testid="billing-portal-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Subscription Management
            </CardTitle>
            <CardDescription>
              Manage your subscription securely
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BillingPortalButton showExplanation={true} />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
