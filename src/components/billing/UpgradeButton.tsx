'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { trackUpgradeButtonClick } from '@/lib/automation/events'
import { createClient } from '@/lib/supabase/client'

interface UpgradeButtonProps {
  planType: 'starter' | 'professional' | 'business' | 'pro' | 'appraiser'
  billingCycle?: 'monthly' | 'annual'
  children: React.ReactNode
  className?: string
}

export function UpgradeButton({ planType, billingCycle = 'monthly', children, className }: UpgradeButtonProps) {
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleUpgrade = async () => {
    setLoading(true)

    try {
      // Track upgrade button click for automation
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()

      if (user) {
        // Get user's current tier
        const { data: profile } = await supabase
          .from('profiles')
          .select('subscription_tier')
          .eq('id', user.id)
          .single()

        await trackUpgradeButtonClick(user.id, {
          planType,
          location: 'upgrade-button-component',
          currentTier: profile?.subscription_tier || 'trial'
        })
      }

      // Map new plan types to checkout routes with billing cycle
      const checkoutRoute = planType === 'starter' ? `/checkout/starter${billingCycle === 'annual' ? '-annual' : ''}` :
                           planType === 'professional' ? `/checkout/professional${billingCycle === 'annual' ? '-annual' : ''}` :
                           planType === 'business' ? 'mailto:<EMAIL>?subject=Business Plan Inquiry' :
                           `/checkout/${planType}`

      // Redirect to checkout page or open email for business plan
      if (planType === 'business') {
        window.location.href = checkoutRoute
      } else {
        router.push(checkoutRoute)
      }
    } catch (error) {
      console.error('Navigation error:', error)
      alert('An error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      onClick={handleUpgrade}
      disabled={loading}
      className={className}
      data-testid={`upgrade-${planType}`}
    >
      {loading ? 'Loading...' : children}
    </Button>
  )
}

interface PullPackButtonProps {
  className?: string
}

export function PullPackButton({ className }: PullPackButtonProps) {
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handlePurchase = async () => {
    setLoading(true)

    try {
      // Redirect to our custom credits checkout page
      router.push('/checkout/credits')
    } catch (error) {
      console.error('Navigation error:', error)
      alert('An error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      onClick={handlePurchase}
      disabled={loading}
      variant="outline"
      className={className}
    >
      {loading ? 'Loading...' : 'Buy More Searches'}
    </Button>
  )
}
