'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Check, Zap, Building } from 'lucide-react'
import { UpgradeButton, PullPackButton } from './UpgradeButton'
import Link from 'next/link'

interface UpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  trigger?: 'limit_reached' | 'feature_locked' | 'manual'
  currentTier?: 'free' | 'pro' | 'appraiser'
}

export function UpgradeModal({
  isOpen,
  onClose,
  trigger = 'manual'
}: UpgradeModalProps) {
  // Only Pro plan available now (Appraiser tier temporarily disabled)
  const selectedPlan = 'pro'

  const plans = {
    pro: {
      name: 'Pro',
      price: '$99',
      period: '/month',
      icon: <Zap className="h-6 w-6" />,
      color: 'bg-blue-600',
      priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID!,
      features: [
        'Unlimited searches',
        'Priority support',
        'Advanced filtering',
        'Export to PDF',
        'Custom project descriptions',
        'Red flag detection',
        'Search history & favorites',
        'Email notifications'
      ]
    }
  }

  const getModalTitle = () => {
    switch (trigger) {
      case 'limit_reached':
        return 'Upgrade Required'
      case 'feature_locked':
        return 'Unlock Premium Features'
      default:
        return 'Choose Your Plan'
    }
  }

  const getModalDescription = () => {
    switch (trigger) {
      case 'limit_reached':
        return "You've reached your monthly search limit. Upgrade to Pro for unlimited searches."
      case 'feature_locked':
        return 'This feature is available with a Pro subscription.'
      default:
        return 'Upgrade to Pro to unlock unlimited searches and premium features.'
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            {getModalTitle()}
          </DialogTitle>
          <p className="text-gray-600 text-center mt-2">
            {getModalDescription()}
          </p>
        </DialogHeader>

        <div className="mt-6">
          {trigger === 'limit_reached' && (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-semibold text-yellow-800 mb-2">Quick Option: Buy More Searches</h3>
              <p className="text-sm text-yellow-700 mb-3">
                Need just a few more searches this month? Get 5 additional searches for $5.
              </p>
              <PullPackButton className="w-full sm:w-auto" />
            </div>
          )}

          <div className="max-w-md mx-auto">
            {Object.entries(plans).map(([key, plan]) => (
              <div
                key={key}
                className="relative border-2 border-blue-500 bg-blue-50 rounded-xl p-6 transition-all"
              >
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-600">
                  Recommended
                </Badge>

                <div className="text-center mb-4">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${plan.color} text-white mb-3`}>
                    {plan.icon}
                  </div>
                  <h3 className="text-xl font-bold">{plan.name}</h3>
                  <div className="mt-2">
                    <span className="text-3xl font-bold">{plan.price}</span>
                    <span className="text-gray-600">{plan.period}</span>
                  </div>
                </div>

                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <UpgradeButton
                  planType="pro"
                  className={`w-full ${plan.color} hover:opacity-90 text-white`}
                >
                  Upgrade to {plan.name}
                </UpgradeButton>
              </div>
            ))}
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 mb-4">
              Need to compare plans?
              <Link href="/pricing" className="text-blue-600 hover:underline ml-1">
                View detailed pricing
              </Link>
            </p>
            <Button variant="outline" onClick={onClose} className="px-8">
              Maybe Later
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
