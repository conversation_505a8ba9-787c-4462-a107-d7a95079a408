'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { User } from '@supabase/supabase-js'
import {
  BarChart3,
  FileText,
  Users,
  Settings,
  Database,
  Key,
  Shield,
  Building,
  LogOut
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface BusinessNavigationProps {
  user: User
  profile: {
    subscription_tier: string
    role: string
    full_name: string
  }
}

const businessNavItems = [
  {
    name: 'Dashboard',
    href: '/business/dashboard',
    icon: BarChart3,
    description: 'Business metrics and KPIs'
  },
  {
    name: 'Analytics',
    href: '/business/analytics',
    icon: BarChart3,
    description: 'Real-time analytics'
  },
  {
    name: 'KPIs',
    href: '/business/kpis',
    icon: BarChart3,
    description: 'Key performance indicators'
  },
  {
    name: 'Reports',
    href: '/business/reports',
    icon: FileText,
    description: 'Custom reports and exports'
  },
  {
    name: 'Team',
    href: '/business/team',
    icon: Users,
    description: 'Team management'
  },
  {
    name: 'Permissions',
    href: '/business/permissions',
    icon: Shield,
    description: 'Role-based permissions'
  },
  {
    name: 'Activity',
    href: '/business/activity',
    icon: Settings,
    description: 'Team activity tracking'
  },
  {
    name: 'API',
    href: '/business/api',
    icon: Key,
    description: 'API key management'
  },
  {
    name: 'Webhooks',
    href: '/business/webhooks',
    icon: Settings,
    description: 'Webhook configuration'
  },
  {
    name: 'Data Management',
    href: '/business/data/backup',
    icon: Database,
    description: 'Backup and retention'
  },
  {
    name: 'Compliance',
    href: '/business/compliance',
    icon: Shield,
    description: 'Data compliance'
  }
]

const enterpriseNavItems = [
  {
    name: 'Branding',
    href: '/enterprise/branding',
    icon: Building,
    description: 'Custom branding'
  },
  {
    name: 'Security',
    href: '/enterprise/security',
    icon: Shield,
    description: 'Advanced security'
  }
]

export function BusinessNavigation({ profile }: Omit<BusinessNavigationProps, 'user'>) {
  const pathname = usePathname()

  const isEnterprise = profile.subscription_tier === 'enterprise' || profile.role === 'enterprise'
  const navItems = isEnterprise ? [...businessNavItems, ...enterpriseNavItems] : businessNavItems

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <Link href="/business/dashboard" className="flex items-center space-x-2">
              <Building className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">
                Business Dashboard
              </span>
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.slice(0, 6).map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={item.description}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              )
            })}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              {profile.full_name} ({profile.subscription_tier})
            </div>
            <Link href="/auth/signout">
              <Button variant="outline" size="sm">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </Link>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden pb-4">
          <div className="grid grid-cols-2 gap-2">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              )
            })}
          </div>
        </div>
      </div>
    </nav>
  )
}
