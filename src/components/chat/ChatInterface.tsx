'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Send, Bot, User, AlertCircle, ChevronUp, X, Menu, FileText } from 'lucide-react'
import { useChatMessages } from '@/hooks/useChatMessages'
import { useChatCitations } from '@/hooks/useChatCitations'
import { ChatPerformanceMonitor } from './ChatPerformanceMonitor'
import { MessageContent } from './MessageContent'
import { ComplianceDisclaimer } from './ComplianceDisclaimer'
import { StreamingIndicator } from './StreamingIndicator'
import { StalenessWarning } from './StalenessWarning'

import { MessageFeedback } from './MessageFeedback'
import { type ChatConversation } from '@/lib/types/chat'

interface ChatInterfaceProps {
  conversation: ChatConversation
  className?: string
  onToggleSidebar?: () => void
  onToggleSources?: () => void
  onCitationsUpdate?: (citations: any[]) => void
}

export function ChatInterface({
  conversation,
  className = '',
  onToggleSidebar,
  onToggleSources,
  onCitationsUpdate
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('')
  const [lastResponseTime, setLastResponseTime] = useState<number>(0)
  const [showStalenessWarning, setShowStalenessWarning] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  
  const {
    messages,
    isLoading,
    isLoadingMore,
    isSending,
    error,
    pagination,
    sendMessage,
    loadMoreMessages,
    clearError,
    cancelMessage,
    isStreaming,
    streamingContent,
  } = useChatMessages(conversation.id, 50)

  // Single citations hook based on messages - this will handle all citation logic
  const { citations: allCitations, handleStreamingCitations } = useChatCitations(messages)
  const [highlightedCitationId, setHighlightedCitationId] = useState<string | null>(null)

  // Memoize citations to prevent unnecessary updates
  const memoizedCitations = useMemo(() => allCitations, [allCitations])

  // Notify parent component when citations update - use a ref to prevent infinite loops
  const previousCitationsRef = useRef<any[]>([])
  useEffect(() => {
    // Only update if citations actually changed (deep comparison of length and IDs)
    const citationsChanged =
      memoizedCitations.length !== previousCitationsRef.current.length ||
      memoizedCitations.some((citation, index) =>
        citation.id !== previousCitationsRef.current[index]?.id
      )

    if (onCitationsUpdate && citationsChanged && memoizedCitations.length > 0) {
      console.log('📚 Citations updated, notifying parent:', memoizedCitations.length)
      onCitationsUpdate(memoizedCitations)
      previousCitationsRef.current = memoizedCitations
    }
  }, [memoizedCitations]) // Only depend on memoized citations

  // Handle citation clicks
  const handleCitationClick = (citationId: string) => {
    setHighlightedCitationId(citationId)
    // Clear highlight after 3 seconds
    setTimeout(() => setHighlightedCitationId(null), 3000)
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isSending) return

    const messageContent = inputValue.trim()
    setInputValue('')

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    // Track response time
    const startTime = Date.now()
    const success = await sendMessage(messageContent)
    const responseTime = Date.now() - startTime

    if (success) {
      setLastResponseTime(responseTime)
    } else {
      // Restore the message if sending failed
      setInputValue(messageContent)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value)

    // Auto-resize textarea
    const textarea = e.target
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
  }

  const formatTime = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } catch {
      return ''
    }
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Desktop Header - ChatGPT Style */}
      <div className="flex-shrink-0 px-6 py-4 border-b border-border/30 hidden lg:block">
        <div className="flex items-center justify-between max-w-3xl mx-auto">
          <div className="flex items-center gap-3">
            {onToggleSidebar && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleSidebar}
                className="h-8 w-8 p-0 hover:bg-muted/50"
                title="Chat Sessions"
              >
                <Menu className="h-4 w-4" />
              </Button>
            )}
            <div>
              <h1 className="text-lg font-medium text-foreground">
                {conversation.address}
              </h1>
              {conversation.jurisdiction_name !== 'TBD' && (
                <p className="text-sm text-muted-foreground">
                  {conversation.jurisdiction_name}
                </p>
              )}
            </div>
          </div>
          {onToggleSources && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleSources}
              className="h-8 w-8 p-0 hover:bg-muted/50"
              title="Sources"
            >
              <FileText className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>



      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <LoadingSpinner />
            <span className="ml-2 text-muted-foreground">Loading messages...</span>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-md mx-auto px-4">
              <Bot className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
              <h3 className="text-base font-medium text-foreground mb-2">
                How can I help with compliance for this property?
              </h3>
              <p className="text-sm text-muted-foreground">
                Ask about permits, setbacks, zoning, materials, or any other compliance requirements.
              </p>
            </div>
          </div>
        ) : (
          <>
            {/* Load More Messages Button */}
            {pagination && pagination.hasPreviousPage && (
              <div className="flex justify-center mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadMoreMessages}
                  disabled={isLoadingMore}
                  className="text-sm"
                >
                  {isLoadingMore ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Loading older messages...
                    </>
                  ) : (
                    <>
                      <ChevronUp className="h-4 w-4 mr-2" />
                      Load older messages ({pagination.totalCount - messages.length} more)
                    </>
                  )}
                </Button>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`group ${
                  message.role === 'assistant' ? 'bg-muted/20' : ''
                }`}
              >
                <div className="max-w-4xl mx-auto px-6 py-6">
                  <div className={`flex items-start gap-4 ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}>
                    {/* Avatar */}
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.role === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted border border-border'
                      }`}>
                        {message.role === 'user' ? (
                          <User className="h-4 w-4" />
                        ) : (
                          <Bot className="h-4 w-4" />
                        )}
                      </div>
                    </div>

                    {/* Message Content */}
                    <div className={`flex-1 min-w-0 ${
                      message.role === 'user' ? 'text-right' : 'text-left'
                    }`}>
                      <div className={`prose prose-sm max-w-none ${
                        message.role === 'user' ? 'text-right' : 'text-left'
                      }`}>
                        <MessageContent
                          content={message.content}
                          role={message.role}
                          citations={memoizedCitations}
                          onCitationClick={handleCitationClick}
                          highlightedCitation={highlightedCitationId}
                          metadata={message.metadata}
                        />
                      </div>
                      <div className={`flex items-center gap-2 mt-3 opacity-0 group-hover:opacity-100 transition-opacity ${
                        message.role === 'user' ? 'justify-end' : 'justify-start'
                      }`}>
                        <p className="text-xs text-muted-foreground">
                          {formatTime(message.created_at)}
                        </p>
                        {message.role === 'assistant' && (
                          <MessageFeedback
                            messageId={message.id}
                            className="ml-2"
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Streaming/Sending indicator */}
            {(isSending || isStreaming) && (
              <div className="bg-muted/30">
                <div className="max-w-3xl mx-auto px-4 py-4">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-7 h-7 rounded-sm flex items-center justify-center bg-muted text-muted-foreground">
                        <Bot className="h-4 w-4" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="prose prose-sm max-w-none">
                        {isStreaming && streamingContent ? (
                          <MessageContent
                            content={streamingContent}
                            role="assistant"
                            citations={memoizedCitations}
                            onCitationClick={handleCitationClick}
                            highlightedCitation={highlightedCitationId}
                            metadata={undefined}
                          />
                        ) : (
                          <StreamingIndicator />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex-shrink-0 p-4 bg-destructive/10 border-t border-destructive/20">
          <div className="flex items-center gap-2 text-destructive text-sm">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span>{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-auto text-destructive hover:text-destructive"
            >
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Staleness Warning - Hidden for cleaner interface */}

      {/* Input Area - ChatGPT Style */}
      <div className="flex-shrink-0 border-t border-border/30 bg-background chat-input-container">
        <div className="max-w-4xl mx-auto px-3 sm:px-6 py-4">
          <div className="relative flex items-end gap-2">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="Message Ordrly..."
              disabled={isSending}
              className="flex-1 resize-none border border-input rounded-xl px-4 py-3 text-sm focus:ring-2 focus:ring-ring focus:border-ring focus:outline-none bg-background text-foreground placeholder:text-muted-foreground min-h-[44px] max-h-[120px] shadow-sm"
              rows={1}
              maxLength={2000}
            />
            {isSending || isStreaming ? (
              <Button
                onClick={cancelMessage}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 rounded-lg flex-shrink-0"
              >
                <X className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim()}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 disabled:opacity-30 hover:bg-muted/50 rounded-lg flex-shrink-0"
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Compliance Disclaimer - Smaller */}
          <ComplianceDisclaimer className="mt-2" />
        </div>
      </div>

      {/* Performance Monitor - Disabled for cleaner interface */}
    </div>
  )
}
