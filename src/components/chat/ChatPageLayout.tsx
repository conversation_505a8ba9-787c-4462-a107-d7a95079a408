'use client'

import { useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { ChatSessionsList } from './ChatSessionsList'
import { ChatInterface } from './ChatInterface'
import { ChatSourcesPanel, type ChatCitation } from './ChatSourcesPanel'
import { Header } from '@/components/navigation/Header'
import { Menu, X, MessageSquare, FileText } from 'lucide-react'
import { cn } from '@/lib/utils'
import { type ChatConversation } from '@/lib/types/chat'
import { type Citation } from '@/lib/types/compliance'
import { useChatSessions } from '@/hooks/useChatSessions'

export function ChatPageLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false) // Start with sidebar closed for cleaner look
  const [isSourcesOpen, setIsSourcesOpen] = useState(false) // Start with sources closed
  const [isMobile, setIsMobile] = useState(false)
  const [activeSession, setActiveSession] = useState<ChatConversation | null>(null)
  const [highlightedCitation, setHighlightedCitation] = useState<string | null>(null)
  const [citations, setCitations] = useState<Citation[]>([])

  // Get chat sessions
  const { sessions, isLoading: isLoadingSessions } = useChatSessions()

  // Handle citations update from ChatInterface - use useCallback to prevent infinite re-renders
  const handleCitationsUpdate = useCallback((newCitations: any[]) => {
    setCitations(newCitations)
  }, [])

  const hasCitations = citations.length > 0
  const citationCount = citations.length

  // Determine citation category based on content
  const determineCitationCategory = (citation: Citation): ChatCitation['category'] => {
    const title = citation.title?.toLowerCase() || ''
    const section = citation.section?.toLowerCase() || ''
    const docType = citation.document_title?.toLowerCase() || ''

    if (title.includes('building') || title.includes('construction') || docType.includes('building')) return 'building'
    if (title.includes('fire') || title.includes('safety') || docType.includes('fire')) return 'fire'
    if (title.includes('zoning') || title.includes('zone') || docType.includes('zoning')) return 'zoning'
    if (title.includes('environmental') || title.includes('environment') || docType.includes('environmental')) return 'environmental'
    return 'other'
  }

  // Convert citations to ChatCitation format
  const chatCitations: ChatCitation[] = citations.map((citation, index) => ({
    id: citation.id || `citation-${index}`,
    title: citation.title,
    section: citation.section,
    url: citation.url,
    document_type: citation.document_title || 'Municipal Code',
    regulation_text: citation.regulation_text,
    category: determineCitationCategory(citation),
    message_id: citation.message_id,
    citation_number: citation.citation_number || index + 1,
  }))

  // Handle citation interactions
  const handleCitationClick = (citation: ChatCitation) => {
    if (citation.url) {
      window.open(citation.url, '_blank', 'noopener,noreferrer')
    }
    setHighlightedCitation(citation.id)

    // Clear highlight after 3 seconds
    setTimeout(() => setHighlightedCitation(null), 3000)
  }



  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
      // Auto-close sidebars on mobile
      if (window.innerWidth < 1024) {
        setIsSidebarOpen(false)
        setIsSourcesOpen(false)
      } else {
        // Auto-open sidebar on desktop, but keep sources closed by default
        setIsSidebarOpen(true)
        // Don't auto-open sources panel
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Handle mobile Safari viewport changes (keyboard appearance)
  useEffect(() => {
    if (typeof window === 'undefined') return

    const setViewportHeight = () => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }

    setViewportHeight()
    window.addEventListener('resize', setViewportHeight)
    window.addEventListener('orientationchange', setViewportHeight)

    return () => {
      window.removeEventListener('resize', setViewportHeight)
      window.removeEventListener('orientationchange', setViewportHeight)
    }
  }, [])

  // Auto-select first session when sessions are loaded
  useEffect(() => {
    if (!isLoadingSessions && sessions.length > 0 && !activeSession) {
      setActiveSession(sessions[0])
    }
  }, [sessions, isLoadingSessions, activeSession])

  // Clear citations when switching conversations
  useEffect(() => {
    setCitations([])
    setHighlightedCitation(null)
  }, [activeSession?.id])

  // Don't auto-open sources panel - keep interface clean like ChatGPT
  // Users can manually open it if needed

  return (
    <div className="h-screen flex flex-col bg-background overflow-hidden fixed inset-0 chat-container">
      {/* Main Header - Always Show */}
      <Header />

      {/* Mobile Chat Navigation */}
      {isMobile && (
        <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-card flex-shrink-0">
          <Button
            variant="ghost"
            size="lg"
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="h-14 w-14 p-0 hover:bg-muted/50"
          >
            {isSidebarOpen ? <X className="h-8 w-8" /> : <Menu className="h-8 w-8" />}
          </Button>

          {/* Address and Jurisdiction Display */}
          <div className="flex-1 text-center px-2 min-w-0">
            {activeSession ? (
              <div className="space-y-0.5">
                <div className="text-sm font-medium text-foreground truncate">
                  {activeSession.address}
                </div>
                {activeSession.jurisdiction_name && activeSession.jurisdiction_name !== 'TBD' && (
                  <div className="text-xs text-muted-foreground truncate">
                    {activeSession.jurisdiction_name}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                Select a chat
              </div>
            )}
          </div>

          <Button
            variant="ghost"
            size="lg"
            onClick={() => setIsSourcesOpen(!isSourcesOpen)}
            className="h-14 w-14 p-0 hover:bg-muted/50"
          >
            <FileText className="h-8 w-8" />
          </Button>
        </div>
      )}

      {/* Main Layout */}
      <div className="flex-1 flex overflow-hidden min-h-0 pt-0">
        {/* Left Sidebar - Chat Sessions */}
        <div
          className={cn(
            "bg-card border-r border-border transition-all duration-300 flex flex-col",
            isMobile
              ? isSidebarOpen
                ? "fixed inset-0 z-50 w-full shadow-lg"
                : "hidden"
              : isSidebarOpen
                ? "w-72"
                : "w-0 overflow-hidden"
          )}
        >
          {/* Chat Sessions List */}
          <ChatSessionsList
            activeSessionId={activeSession?.id}
            onSessionSelect={setActiveSession}
            className="flex-1"
          />

          {/* Mobile Close Button */}
          {isMobile && (
            <div className="p-4 border-t border-border">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSidebarOpen(false)}
                className="w-full h-12 text-base"
              >
                <X className="h-5 w-5 mr-2" />
                Close
              </Button>
            </div>
          )}
        </div>

        {/* Mobile Sidebar Overlay */}
        {isMobile && isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Center Panel - Main Chat */}
        <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
          {/* Chat Area */}
          <div className="flex-1 overflow-hidden">
            {activeSession ? (
              <ChatInterface
                conversation={activeSession}
                className="h-full"
                onToggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
                onToggleSources={() => setIsSourcesOpen(!isSourcesOpen)}
                onCitationsUpdate={handleCitationsUpdate}
              />
            ) : (
              <div className="flex items-center justify-center h-full p-8">
                <div className="text-center max-w-md">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageSquare className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    Welcome to Ordrly Chat
                  </h3>
                  <p className="text-sm text-muted-foreground mb-6">
                    Start a new conversation by entering a property address and asking questions about compliance requirements.
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Use the "+" button in the Chat Sessions panel to create your first chat.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar - Sources Panel */}
        <div
          className={cn(
            "bg-card border-l border-border transition-all duration-300 flex flex-col",
            isMobile
              ? isSourcesOpen
                ? "fixed inset-0 z-50 w-full shadow-lg"
                : "hidden"
              : isSourcesOpen
                ? "w-72"
                : "w-0 overflow-hidden"
          )}
        >
          {/* Sources Content */}
          <div className="flex-1 overflow-y-auto">
            {hasCitations ? (
              <ChatSourcesPanel
                citations={chatCitations}
                messages={[]}
                onCitationClick={handleCitationClick}
                highlightedCitation={highlightedCitation}
                className="h-full"
                isMobile={isMobile}
                onClose={() => setIsSourcesOpen(false)}
              />
            ) : (
              <div className="p-4">
                <div className="text-center text-muted-foreground py-8">
                  <FileText className="w-8 h-8 mx-auto mb-3 opacity-40" />
                  <p className="text-xs">Sources will appear here</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Sources Overlay */}
        {isMobile && isSourcesOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setIsSourcesOpen(false)}
          />
        )}
      </div>
    </div>
  )
}
