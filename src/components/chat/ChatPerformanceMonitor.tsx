'use client'

import { useEffect, useState } from 'react'
import { Alert<PERSON>riangle, Clock, MessageSquare, Zap } from 'lucide-react'

interface PerformanceMetrics {
  messageCount: number
  averageResponseTime: number
  lastResponseTime: number
  totalLoadTime: number
  errorCount: number
}

interface ChatPerformanceMonitorProps {
  conversationId?: string
  messageCount: number
  lastResponseTime?: number
  isLoading: boolean
  hasError: boolean
  className?: string
}

export function ChatPerformanceMonitor({
  conversationId,
  messageCount,
  lastResponseTime,
  isLoading,
  hasError,
  className = ''
}: ChatPerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    messageCount: 0,
    averageResponseTime: 0,
    lastResponseTime: 0,
    totalLoadTime: 0,
    errorCount: 0
  })
  const [loadStartTime, setLoadStartTime] = useState<number | null>(null)

  // Track loading start time
  useEffect(() => {
    if (isLoading && !loadStartTime) {
      setLoadStartTime(Date.now())
    } else if (!isLoading && loadStartTime) {
      const loadTime = Date.now() - loadStartTime
      setMetrics(prev => ({
        ...prev,
        totalLoadTime: prev.totalLoadTime + loadTime
      }))
      setLoadStartTime(null)
    }
  }, [isLoading, loadStartTime])

  // Update metrics when message count or response time changes
  useEffect(() => {
    setMetrics(prev => {
      const newAverageResponseTime = lastResponseTime 
        ? (prev.averageResponseTime * prev.messageCount + lastResponseTime) / (prev.messageCount + 1)
        : prev.averageResponseTime

      return {
        ...prev,
        messageCount,
        averageResponseTime: newAverageResponseTime,
        lastResponseTime: lastResponseTime || prev.lastResponseTime
      }
    })
  }, [messageCount, lastResponseTime])

  // Track errors
  useEffect(() => {
    if (hasError) {
      setMetrics(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }))
    }
  }, [hasError])

  // Only show in development or when there are performance issues
  const shouldShow = process.env.NODE_ENV === 'development' || 
                    metrics.averageResponseTime > 5000 || 
                    metrics.errorCount > 0

  if (!shouldShow) {
    return null
  }

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  const getPerformanceStatus = () => {
    if (metrics.errorCount > 0) return 'error'
    if (metrics.averageResponseTime > 10000) return 'warning'
    if (metrics.averageResponseTime > 5000) return 'slow'
    return 'good'
  }

  const status = getPerformanceStatus()

  return (
    <div className={`fixed bottom-4 right-4 bg-card border rounded-lg p-3 shadow-lg text-xs max-w-xs ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        {status === 'error' && <AlertTriangle className="h-4 w-4 text-red-500" />}
        {status === 'warning' && <Clock className="h-4 w-4 text-yellow-500" />}
        {status === 'slow' && <Zap className="h-4 w-4 text-orange-500" />}
        {status === 'good' && <MessageSquare className="h-4 w-4 text-green-500" />}
        <span className="font-medium text-foreground">Chat Performance</span>
      </div>
      
      <div className="space-y-1 text-muted-foreground">
        <div className="flex justify-between">
          <span>Messages:</span>
          <span>{metrics.messageCount}</span>
        </div>
        
        {metrics.lastResponseTime > 0 && (
          <div className="flex justify-between">
            <span>Last Response:</span>
            <span className={metrics.lastResponseTime > 5000 ? 'text-yellow-600' : ''}>
              {formatTime(metrics.lastResponseTime)}
            </span>
          </div>
        )}
        
        {metrics.averageResponseTime > 0 && (
          <div className="flex justify-between">
            <span>Avg Response:</span>
            <span className={metrics.averageResponseTime > 5000 ? 'text-yellow-600' : ''}>
              {formatTime(metrics.averageResponseTime)}
            </span>
          </div>
        )}
        
        {metrics.totalLoadTime > 0 && (
          <div className="flex justify-between">
            <span>Total Load:</span>
            <span>{formatTime(metrics.totalLoadTime)}</span>
          </div>
        )}
        
        {metrics.errorCount > 0 && (
          <div className="flex justify-between">
            <span>Errors:</span>
            <span className="text-red-600">{metrics.errorCount}</span>
          </div>
        )}
        
        {isLoading && (
          <div className="flex justify-between">
            <span>Status:</span>
            <span className="text-blue-600">Loading...</span>
          </div>
        )}
      </div>
      
      {status !== 'good' && (
        <div className="mt-2 pt-2 border-t border-border">
          <div className="text-xs text-muted-foreground">
            {status === 'error' && 'Multiple errors detected'}
            {status === 'warning' && 'Slow response times detected'}
            {status === 'slow' && 'Performance may be degraded'}
          </div>
        </div>
      )}
    </div>
  )
}
