'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'

import {
  MoreVertical,
  Edit2,
  Trash2,
  Check,
  X
} from 'lucide-react'
import { type ChatConversation } from '@/lib/types/chat'
// Simple date formatting utility
const formatDistanceToNow = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return 'just now'
  if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`
  return date.toLocaleDateString()
}

interface ChatSessionItemProps {
  session: ChatConversation
  isActive: boolean
  onSelect: () => void
  onDelete: () => void
  onRename: (newAddress: string) => void
}

export function ChatSessionItem({ 
  session, 
  isActive, 
  onSelect, 
  onDelete, 
  onRename 
}: ChatSessionItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(session.address)


  const handleStartEdit = () => {
    setEditValue(session.address)
    setIsEditing(true)
  }

  const handleSaveEdit = () => {
    if (editValue.trim() && editValue !== session.address) {
      onRename(editValue.trim())
    }
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setEditValue(session.address)
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit()
    } else if (e.key === 'Escape') {
      handleCancelEdit()
    }
  }

  const handleDelete = () => {
    if (window.confirm(`Are you sure you want to delete this chat session for "${session.address}"? This action cannot be undone and will permanently delete all messages in this conversation.`)) {
      onDelete()
    }
  }

  const formatLastActivity = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString))
    } catch {
      return 'Unknown'
    }
  }

  return (
    <>
      <div
        className={`
          group relative p-4 lg:p-3 rounded-lg cursor-pointer transition-colors mb-2 lg:mb-1 min-h-[60px] lg:min-h-0
          ${isActive
            ? 'bg-primary/10 border border-primary/30'
            : 'hover:bg-muted/50 border border-transparent'
          }
        `}
        onClick={!isEditing ? onSelect : undefined}
      >
        {/* Main Content */}
        <div className="flex items-start justify-between gap-3 lg:gap-2">
          <div className="flex-1 min-w-0">
            {/* Address */}
            {isEditing ? (
              <div className="flex items-center gap-2 lg:gap-1 mb-2 lg:mb-1">
                <Input
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="text-sm lg:text-sm h-8 lg:h-6"
                  autoFocus
                />
                <Button size="lg" variant="ghost" onClick={handleSaveEdit} className="h-16 w-16 lg:h-8 lg:w-8 p-0">
                  <Check className="h-8 w-8 lg:h-4 lg:w-4 text-green-600" />
                </Button>
                <Button size="lg" variant="ghost" onClick={handleCancelEdit} className="h-16 w-16 lg:h-8 lg:w-8 p-0">
                  <X className="h-8 w-8 lg:h-4 lg:w-4 text-red-600" />
                </Button>
              </div>
            ) : (
              <h4 className="text-base lg:text-sm font-medium text-foreground break-words mb-2 lg:mb-1 leading-tight">
                {session.address}
              </h4>
            )}

            {/* Last Activity */}
            <div className="text-sm lg:text-xs text-muted-foreground">
              {formatLastActivity(session.updated_at)}
            </div>
          </div>

          {/* Actions Menu */}
          {!isEditing && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="lg"
                  className="opacity-100 lg:opacity-0 lg:group-hover:opacity-100 transition-opacity h-16 w-16 lg:h-8 lg:w-8 p-0 flex-shrink-0"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="h-8 w-8 lg:h-4 lg:w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleStartEdit}>
                  <Edit2 className="h-4 w-4 lg:h-3 lg:w-3 mr-2" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDelete}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 lg:h-3 lg:w-3 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </>
  )
}
