'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { NewChatModal } from './NewChatModal'
import { ChatSessionItem } from './ChatSessionItem'
import { Plus, MessageCircle, AlertCircle } from 'lucide-react'
import { useChatSessions } from '@/hooks/useChatSessions'
import { type ChatConversation, type NewChatData } from '@/lib/types/chat'

interface ChatSessionsListProps {
  activeSessionId?: string | null
  onSessionSelect: (session: ChatConversation) => void
  className?: string
}

export function ChatSessionsList({ 
  activeSessionId, 
  onSessionSelect, 
  className = '' 
}: ChatSessionsListProps) {
  const [isNewChatModalOpen, setIsNewChatModalOpen] = useState(false)
  const { 
    sessions, 
    isLoading, 
    error, 
    createSession, 
    deleteSession, 
    updateSession 
  } = useChatSessions()

  const handleCreateChat = async (chatData: NewChatData) => {
    const newSession = await createSession(chatData)
    if (newSession) {
      onSessionSelect(newSession)
    }
  }

  const handleDeleteSession = async (sessionId: string) => {
    const success = await deleteSession(sessionId)
    if (success && activeSessionId === sessionId) {
      // If we deleted the active session, select the first remaining session
      const remainingSessions = sessions.filter(s => s.id !== sessionId)
      if (remainingSessions.length > 0) {
        onSessionSelect(remainingSessions[0])
      }
    }
  }

  const handleRenameSession = async (sessionId: string, newAddress: string) => {
    await updateSession(sessionId, { address: newAddress })
  }

  if (isLoading) {
    return (
      <div className={`flex flex-col h-full ${className}`}>
        <div className="p-4 border-b">
          <h2 className="font-semibold text-lg">Chat Sessions</h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Clean Header */}
      <div className="p-4 lg:p-3 border-b border-border/50">
        <div className="flex items-center justify-between">
          <h3 className="text-base lg:text-sm font-medium text-muted-foreground">Chats</h3>
          <Button
            variant="ghost"
            size="lg"
            onClick={() => setIsNewChatModalOpen(true)}
            className="h-16 w-16 lg:h-8 lg:w-8 p-0 hover:bg-muted/50 flex items-center justify-center text-2xl lg:text-lg font-bold"
            title="New Chat"
          >
            +
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 lg:p-3 border-b bg-destructive/10 border-destructive/20">
          <div className="flex items-center gap-2 text-destructive text-sm lg:text-xs">
            <AlertCircle className="h-4 w-4 lg:h-3 lg:w-3 flex-shrink-0" />
            {error}
          </div>
        </div>
      )}

      {/* Sessions List */}
      <div className="flex-1 overflow-y-auto">
        {sessions.length === 0 ? (
          <div className="p-6 lg:p-4 text-center">
            <div className="py-8 lg:py-6">
              <MessageCircle className="h-10 w-10 lg:h-8 lg:w-8 text-muted-foreground mx-auto mb-4 lg:mb-3 opacity-50" />
              <p className="text-sm lg:text-xs text-muted-foreground mb-4 lg:mb-3">
                No chats yet
              </p>
              <Button
                variant="ghost"
                size="lg"
                onClick={() => setIsNewChatModalOpen(true)}
                className="text-lg lg:text-sm h-16 lg:h-10 px-8 lg:px-4 flex items-center justify-center"
              >
                <span className="text-2xl lg:text-lg font-bold mr-4 lg:mr-2">+</span>
                New Chat
              </Button>
            </div>
          </div>
        ) : (
          <div className="p-1">
            {sessions.map((session) => (
              <ChatSessionItem
                key={session.id}
                session={session}
                isActive={session.id === activeSessionId}
                onSelect={() => onSessionSelect(session)}
                onDelete={() => handleDeleteSession(session.id)}
                onRename={(newAddress) => handleRenameSession(session.id, newAddress)}
              />
            ))}
          </div>
        )}
      </div>

      {/* New Chat Modal */}
      <NewChatModal
        isOpen={isNewChatModalOpen}
        onClose={() => setIsNewChatModalOpen(false)}
        onCreateChat={handleCreateChat}
      />
    </div>
  )
}
