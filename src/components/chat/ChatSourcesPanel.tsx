'use client'

import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { FileText, ExternalLink, X } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface ChatCitation {
  id: string
  title: string
  section?: string
  url?: string
  document_type?: string
  regulation_text?: string
  category?: 'building' | 'fire' | 'zoning' | 'environmental' | 'other'
  message_id?: string
  citation_number?: number
}

interface ChatSourcesPanelProps {
  citations: ChatCitation[]
  messages?: any[]
  className?: string
  onCitationClick?: (citation: ChatCitation) => void
  highlightedCitation?: string | null
  isMobile?: boolean
  onClose?: () => void
}

export function ChatSourcesPanel({
  citations,
  messages = [],
  className = '',
  onCitationClick,
  highlightedCitation,
  isMobile = false,
  onClose
}: ChatSourcesPanelProps) {
  const totalCitations = citations.length

  // Group citations by message_id
  const citationsByMessage = citations.reduce((acc, citation) => {
    const messageId = citation.message_id || 'unknown'
    if (!acc[messageId]) {
      acc[messageId] = []
    }
    acc[messageId].push(citation)
    return acc
  }, {} as Record<string, ChatCitation[]>)

  // Get unique message IDs in chronological order (based on messages array if available)
  const messageIds = messages && messages.length > 0
    ? messages
        .filter(msg => msg.role === 'assistant' && citationsByMessage[msg.id])
        .map(msg => msg.id)
    : Object.keys(citationsByMessage)



  if (totalCitations === 0) {
    return (
      <div className={`flex flex-col h-full ${className}`}>
        <div className="p-4 lg:p-3 border-b border-border/50">
          <div className="flex items-center justify-between">
            <h3 className="text-base lg:text-sm font-medium text-muted-foreground">Sources</h3>
            {isMobile && onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 lg:h-6 lg:w-6 p-0"
              >
                <X className="h-4 w-4 lg:h-3 lg:w-3" />
              </Button>
            )}
          </div>
        </div>
        <div className="flex-1 p-6 lg:p-4 overflow-y-auto">
          <div className="text-center text-muted-foreground py-12 lg:py-8">
            <FileText className="w-12 h-12 lg:w-8 lg:h-8 mx-auto mb-4 lg:mb-3 opacity-40" />
            <p className="text-sm lg:text-xs">Sources will appear here</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Clean Header */}
      <div className="p-4 lg:p-3 border-b border-border/50">
        <div className="flex items-center justify-between">
          <h3 className="text-base lg:text-sm font-medium text-muted-foreground">Sources</h3>
          <div className="flex items-center gap-2">
            <span className="text-sm lg:text-xs text-muted-foreground">{totalCitations}</span>
            {isMobile && onClose && (
              <Button
                variant="ghost"
                size="lg"
                onClick={onClose}
                className="h-16 w-16 lg:h-8 lg:w-8 p-0"
              >
                <X className="h-8 w-8 lg:h-4 lg:w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Citations Grouped by Message */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-3 lg:p-2">
          {messageIds.map((messageId, messageIndex) => {
            const messageCitations = citationsByMessage[messageId]

            return (
              <div key={messageId} className="mb-6 lg:mb-4 last:mb-0">
                {/* Message Separator */}
                {messageIndex > 0 && (
                  <div className="border-t border-border/30 mb-4 lg:mb-3"></div>
                )}

                {/* Citations for this message */}
                <div className="space-y-4 lg:space-y-3">
                  {messageCitations.map((citation, citationIndex) => (
                    <Card
                      key={citation.id}
                      className={cn(
                        "p-4 lg:p-3 bg-card/50 border border-border/50 hover:bg-card/80 transition-colors cursor-pointer min-h-[80px] lg:min-h-0",
                        highlightedCitation === citation.id && "bg-primary/10 border-primary/30"
                      )}
                      onClick={() => onCitationClick?.(citation)}
                    >
                      <div className="flex items-start gap-3 lg:gap-2">
                        <span className="text-sm lg:text-xs font-medium text-primary bg-primary/10 rounded px-2 lg:px-1.5 py-1 lg:py-0.5 flex-shrink-0">
                          {citationIndex + 1}
                        </span>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm lg:text-xs font-medium text-foreground truncate">
                            {citation.title}
                          </h4>
                          {citation.section && (
                            <p className="text-sm lg:text-xs text-muted-foreground mt-2 lg:mt-1 line-clamp-2 hidden lg:block">
                              {citation.section}
                            </p>
                          )}
                          <div className="flex items-center gap-2 lg:gap-1 mt-3 lg:mt-2">
                            <FileText className="w-4 h-4 lg:w-3 lg:h-3 text-muted-foreground" />
                            <span className="text-sm lg:text-xs text-muted-foreground truncate">
                              {citation.document_type || 'Municipal Code'}
                            </span>
                            {citation.url && (
                              <ExternalLink
                                className="w-4 h-4 lg:w-3 lg:h-3 text-muted-foreground ml-auto cursor-pointer hover:text-primary flex-shrink-0"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  window.open(citation.url, '_blank')
                                }}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
