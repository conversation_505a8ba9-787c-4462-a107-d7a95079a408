'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertTriangle, ChevronDown, ChevronUp, X } from 'lucide-react'

interface ComplianceDisclaimerProps {
  className?: string
}

export function ComplianceDisclaimer({ className = '' }: ComplianceDisclaimerProps) {
  return (
    <div className={`text-center ${className}`}>
      <p className="text-xs text-muted-foreground/70 leading-tight">
        AI responses are for informational purposes only. Always verify with official sources.
      </p>
    </div>
  )
}

// Hook for managing disclaimer preferences (kept for compatibility)
export function useDisclaimerPreferences() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const savedExpanded = localStorage.getItem('ordrly-disclaimer-expanded')
    const savedVisible = localStorage.getItem('ordrly-disclaimer-visible')

    if (savedExpanded !== null) {
      setIsExpanded(savedExpanded === 'true')
    }

    if (savedVisible !== null) {
      setIsVisible(savedVisible === 'true')
    }
  }, [])

  const toggleExpanded = () => {
    const newExpanded = !isExpanded
    setIsExpanded(newExpanded)
    localStorage.setItem('ordrly-disclaimer-expanded', newExpanded.toString())
  }

  const setVisible = (visible: boolean) => {
    setIsVisible(visible)
    localStorage.setItem('ordrly-disclaimer-visible', visible.toString())
  }

  return {
    isExpanded,
    isVisible,
    toggleExpanded,
    setVisible
  }
}


