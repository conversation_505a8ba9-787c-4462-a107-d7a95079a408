'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { FileText, Copy, Download, CheckCircle, AlertCircle, Loader2, History, Trash2 } from 'lucide-react'
import { toast } from 'sonner'

interface GenerateNoteButtonProps {
  conversationId: string
  conversationAddress: string
  disabled?: boolean
  className?: string
}

interface GeneratedNote {
  note: string
  conversation: {
    id: string
    address: string
    jurisdiction_name: string
  }
  generated_at: string
  saved?: boolean
  note_id?: string
}

interface SavedNote {
  id: string
  note_content: string
  included_citations: boolean
  created_at: string
  generation_metadata: any
}

export function GenerateNoteButton({ 
  conversationId, 
  conversationAddress,
  disabled = false, 
  className = '' 
}: GenerateNoteButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [generatedNote, setGeneratedNote] = useState<GeneratedNote | null>(null)
  const [includeCitations, setIncludeCitations] = useState(false)
  const [isCopying, setIsCopying] = useState(false)
  const [savedNotes, setSavedNotes] = useState<SavedNote[]>([])
  const [isLoadingNotes, setIsLoadingNotes] = useState(false)
  const [showHistory, setShowHistory] = useState(false)

  const handleGenerateNote = async () => {
    if (isGenerating || disabled) return

    setIsGenerating(true)
    
    try {
      const response = await fetch(`/api/chat/generate-note/${conversationId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          includeCitations
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to generate appraisal note')
      }

      const noteData: GeneratedNote = await response.json()
      setGeneratedNote(noteData)
      setIsModalOpen(true)
      
      toast.success('Appraisal note generated successfully!')
      
    } catch (error) {
      console.error('Note generation error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to generate appraisal note')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopyNote = async () => {
    if (!generatedNote || isCopying) return

    setIsCopying(true)
    
    try {
      await navigator.clipboard.writeText(generatedNote.note)
      toast.success('Note copied to clipboard!')
    } catch (error) {
      console.error('Copy error:', error)
      toast.error('Failed to copy note')
    } finally {
      setIsCopying(false)
    }
  }

  const handleDownloadNote = () => {
    if (!generatedNote) return

    const noteContent = `1004 Appraisal Note
Property: ${generatedNote.conversation.address}
Jurisdiction: ${generatedNote.conversation.jurisdiction_name}
Generated: ${new Date(generatedNote.generated_at).toLocaleString()}

${generatedNote.note}`

    const blob = new Blob([noteContent], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `appraisal-note-${conversationId.slice(0, 8)}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    toast.success('Note downloaded successfully!')
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    // Keep the generated note in state so user can regenerate without losing it
  }

  const loadSavedNotes = async () => {
    setIsLoadingNotes(true)
    try {
      const response = await fetch(`/api/chat/notes/${conversationId}`)
      if (response.ok) {
        const data = await response.json()
        setSavedNotes(data.notes || [])
      }
    } catch (error) {
      console.error('Error loading saved notes:', error)
    } finally {
      setIsLoadingNotes(false)
    }
  }

  const deleteSavedNote = async (noteId: string) => {
    try {
      const response = await fetch(`/api/chat/notes/${conversationId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ noteId })
      })

      if (response.ok) {
        setSavedNotes(prev => prev.filter(note => note.id !== noteId))
        toast.success('Note deleted successfully')
      }
    } catch (error) {
      console.error('Error deleting note:', error)
      toast.error('Failed to delete note')
    }
  }

  const loadSavedNote = (savedNote: SavedNote) => {
    const noteData: GeneratedNote = {
      note: savedNote.note_content,
      conversation: {
        id: conversationId,
        address: conversationAddress,
        jurisdiction_name: savedNote.generation_metadata?.jurisdiction || 'Unknown'
      },
      generated_at: savedNote.created_at,
      saved: true,
      note_id: savedNote.id
    }
    setGeneratedNote(noteData)
    setIncludeCitations(savedNote.included_citations)
    setShowHistory(false)
  }

  // Load saved notes when modal opens
  useEffect(() => {
    if (isModalOpen && !isLoadingNotes && savedNotes.length === 0) {
      loadSavedNotes()
    }
  }, [isModalOpen])

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={handleGenerateNote}
        disabled={disabled || isGenerating}
        className={className}
      >
        {isGenerating ? (
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
        ) : (
          <FileText className="w-4 h-4 mr-2" />
        )}
        {isGenerating ? 'Generating...' : 'Generate Note'}
      </Button>

      {/* Note Display Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-primary" />
              1004 Appraisal Note
            </DialogTitle>
          </DialogHeader>

          {generatedNote && (
            <div className="space-y-6">
              {/* Property Info */}
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="font-medium text-muted-foreground">Property:</span>
                    <p className="font-medium">{generatedNote.conversation.address}</p>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">Jurisdiction:</span>
                    <p className="font-medium">{generatedNote.conversation.jurisdiction_name}</p>
                  </div>
                  <div className="md:col-span-2">
                    <span className="font-medium text-muted-foreground">Generated:</span>
                    <p className="font-medium">{new Date(generatedNote.generated_at).toLocaleString()}</p>
                  </div>
                </div>
              </div>

              {/* Generated Note */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Generated Note</h3>
                  <Badge variant="secondary" className="text-xs">
                    Ready for 1004 Form
                  </Badge>
                </div>
                
                <div className="bg-background border rounded-lg p-4">
                  <p className="text-sm leading-relaxed whitespace-pre-wrap">
                    {generatedNote.note}
                  </p>
                </div>

                <div className="text-xs text-muted-foreground bg-muted/30 rounded p-3">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 mt-0.5 shrink-0" />
                    <div>
                      <p className="font-medium mb-1">Important Notice</p>
                      <p>This note is AI-generated based on the compliance conversation. Always verify findings with local authorities and include appropriate disclaimers in your appraisal report.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
                <Button
                  onClick={handleCopyNote}
                  disabled={isCopying}
                  className="flex-1"
                >
                  {isCopying ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Copy className="w-4 h-4 mr-2" />
                  )}
                  {isCopying ? 'Copying...' : 'Copy to Clipboard'}
                </Button>
                
                <Button
                  onClick={handleDownloadNote}
                  variant="outline"
                  className="flex-1"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download as Text
                </Button>

                <Button
                  onClick={handleCloseModal}
                  variant="outline"
                >
                  Close
                </Button>
              </div>

              {/* Regenerate Options */}
              <div className="bg-muted/30 rounded-lg p-4">
                <h4 className="font-medium text-sm mb-3">Regenerate Options</h4>
                <div className="space-y-3">
                  <label className="flex items-center gap-2 text-sm">
                    <input
                      type="checkbox"
                      checked={includeCitations}
                      onChange={(e) => setIncludeCitations(e.target.checked)}
                      className="rounded"
                    />
                    Include source citations in note
                  </label>

                  <Button
                    onClick={handleGenerateNote}
                    disabled={isGenerating}
                    size="sm"
                    variant="outline"
                    className="w-full"
                  >
                    {isGenerating ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <FileText className="w-4 h-4 mr-2" />
                    )}
                    Regenerate Note
                  </Button>
                </div>
              </div>

              {/* Note History */}
              {savedNotes.length > 0 && (
                <div className="bg-muted/30 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-sm">Previous Notes</h4>
                    <Button
                      onClick={() => setShowHistory(!showHistory)}
                      size="sm"
                      variant="ghost"
                      className="h-6 px-2"
                    >
                      <History className="w-3 h-3 mr-1" />
                      {showHistory ? 'Hide' : `Show (${savedNotes.length})`}
                    </Button>
                  </div>

                  {showHistory && (
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {savedNotes.map((savedNote) => (
                        <div
                          key={savedNote.id}
                          className="flex items-start justify-between gap-2 p-2 bg-background rounded border text-xs"
                        >
                          <div className="flex-1 min-w-0">
                            <p className="truncate font-medium">
                              {new Date(savedNote.created_at).toLocaleDateString()}
                            </p>
                            <p className="text-muted-foreground truncate">
                              {savedNote.note_content.substring(0, 60)}...
                            </p>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              onClick={() => loadSavedNote(savedNote)}
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                            >
                              <FileText className="w-3 h-3" />
                            </Button>
                            <Button
                              onClick={() => deleteSavedNote(savedNote.id)}
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
