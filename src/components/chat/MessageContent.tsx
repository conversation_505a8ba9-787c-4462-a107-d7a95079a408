'use client'

import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeSanitize from 'rehype-sanitize'
import { type Components } from 'react-markdown'
import { AlertTriangle } from 'lucide-react'

import { type Citation } from '@/lib/types/compliance'
import { isFallbackMessage, getFallbackType } from '@/lib/fallback-messages'

interface MessageContentProps {
  content: string
  role: 'user' | 'assistant'
  citations?: Citation[]
  onCitationClick?: (citationId: string) => void
  highlightedCitation?: string | null
  metadata?: any // For detecting fallback messages
}

export function MessageContent({
  content,
  role,
  citations = [],
  onCitationClick,
  highlightedCitation,
  metadata
}: MessageContentProps) {
  // Check if this is a fallback message
  const isMessageFallback = isFallbackMessage(metadata)
  const fallbackType = getFallbackType(metadata)

  // Handle citation clicks
  const handleCitationClick = React.useCallback((event: React.MouseEvent) => {
    const target = event.target as HTMLElement
    if (target.classList.contains('citation-marker')) {
      const citationId = target.getAttribute('data-citation-id')
      if (citationId && onCitationClick) {
        onCitationClick(citationId)
      }
    }
  }, [onCitationClick])

  // Custom markdown components
  const components: Components = {
    // External links with security attributes
    a: ({ href, children, ...props }) => (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline transition-colors"
        {...props}
      >
        {children}
      </a>
    ),
    
    // Code blocks with proper styling
    code: ({ className, children, ...props }: any) => {
      const isInline = !className?.includes('language-')

      if (isInline) {
        return (
          <code
            className="px-1.5 py-0.5 rounded bg-gray-100 dark:bg-gray-800 text-sm font-mono text-gray-900 dark:text-gray-100"
            {...props}
          >
            {children}
          </code>
        )
      }

      return (
        <code
          className={`block p-3 rounded-lg bg-gray-100 dark:bg-gray-800 text-sm font-mono text-gray-900 dark:text-gray-100 overflow-x-auto ${className || ''}`}
          {...props}
        >
          {children}
        </code>
      )
    },
    
    // Pre blocks for code
    pre: ({ children, ...props }) => (
      <pre
        className="my-3 p-3 rounded-lg bg-gray-100 dark:bg-gray-800 overflow-x-auto"
        {...props}
      >
        {children}
      </pre>
    ),
    
    // Lists with proper spacing and indentation
    ul: ({ children, ...props }) => (
      <ul className="list-disc list-outside ml-6 space-y-1 my-2" {...props}>
        {children}
      </ul>
    ),

    ol: ({ children, ...props }) => (
      <ol className="list-decimal list-outside ml-6 space-y-1 my-2" {...props}>
        {children}
      </ol>
    ),

    // List items with proper spacing
    li: ({ children, ...props }) => (
      <li className="text-gray-900 dark:text-gray-100 leading-relaxed" {...props}>
        {children}
      </li>
    ),
    
    // Paragraphs with spacing
    p: ({ children, ...props }) => (
      <p className="mb-3 last:mb-0 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </p>
    ),
    
    // Headings
    h1: ({ children, ...props }) => (
      <h1 className="text-xl font-bold mb-3 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h1>
    ),
    
    h2: ({ children, ...props }) => (
      <h2 className="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h2>
    ),
    
    h3: ({ children, ...props }) => (
      <h3 className="text-base font-medium mb-2 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h3>
    ),
    
    // Blockquotes
    blockquote: ({ children, ...props }) => (
      <blockquote
        className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 my-3 italic text-gray-700 dark:text-gray-300"
        {...props}
      >
        {children}
      </blockquote>
    ),
    
    // Tables
    table: ({ children, ...props }) => (
      <div className="overflow-x-auto my-3">
        <table className="min-w-full border border-gray-300 dark:border-gray-600" {...props}>
          {children}
        </table>
      </div>
    ),
    
    th: ({ children, ...props }) => (
      <th
        className="px-3 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-300 dark:border-gray-600 text-left font-medium text-gray-900 dark:text-gray-100"
        {...props}
      >
        {children}
      </th>
    ),
    
    td: ({ children, ...props }) => (
      <td
        className="px-3 py-2 border-b border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
        {...props}
      >
        {children}
      </td>
    ),
    
    // Strong and emphasis
    strong: ({ children, ...props }) => (
      <strong className="font-semibold text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </strong>
    ),
    
    em: ({ children, ...props }) => (
      <em className="italic text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </em>
    ),
  }

  // For user messages, render as plain text
  if (role === 'user') {
    return (
      <div className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
        {content}
      </div>
    )
  }

  // For fallback messages, add special styling
  if (isMessageFallback && role === 'assistant') {
    return (
      <div className="border-l-4 border-amber-400 bg-amber-50 dark:bg-amber-900/20 dark:border-amber-500 p-4 rounded-r-lg">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                Limited Information Available
              </span>
              {fallbackType && (
                <span className="text-xs px-2 py-1 bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200 rounded-full">
                  {fallbackType.replace('_', ' ')}
                </span>
              )}
            </div>
            <div className="prose prose-sm max-w-none dark:prose-invert text-amber-900 dark:text-amber-100">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeSanitize]}
                components={components}
              >
                {content}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // For assistant messages with citations, render markdown with citation processing
  if (citations.length > 0) {
    return (
      <div
        className="prose prose-sm max-w-none dark:prose-invert"
        onClick={handleCitationClick}
      >
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeSanitize]}
          components={{
            ...components,
            // Process citations in all text content while preserving original styling
            p: ({ children, ...props }) => {
              const processTextContent = (node: React.ReactNode): React.ReactNode => {
                if (typeof node === 'string') {
                  let processed = node
                  let hasCitations = false

                  citations.forEach((citation, index) => {
                    const citationNumber = index + 1
                    const marker = `[${citationNumber}]`
                    const citationId = `citation-${index}`
                    const isHighlighted = highlightedCitation === citationId
                    const highlightClass = isHighlighted ? 'bg-yellow-200 dark:bg-yellow-800' : ''

                    if (processed.includes(marker)) {
                      hasCitations = true
                      const citationSpan = `<span class="citation-marker inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors ${highlightClass}" data-citation-id="${citationId}">${marker}</span>`

                      processed = processed.replace(
                        new RegExp(`\\[${citationNumber}\\]`, 'g'),
                        citationSpan
                      )
                    }
                  })

                  if (hasCitations) {
                    return <span dangerouslySetInnerHTML={{ __html: processed }} />
                  }
                  return node
                }
                return node
              }

              const processedChildren = React.Children.map(children, processTextContent)
              // Use the original p component styling
              return (
                <p className="mb-3 last:mb-0 text-gray-900 dark:text-gray-100" {...props}>
                  {processedChildren}
                </p>
              )
            },
            // Also handle other text elements that might contain citations
            li: ({ children, ...props }) => {
              const processTextContent = (node: React.ReactNode): React.ReactNode => {
                if (typeof node === 'string') {
                  let processed = node
                  let hasCitations = false

                  citations.forEach((citation, index) => {
                    const citationNumber = index + 1
                    const marker = `[${citationNumber}]`
                    const citationId = `citation-${index}`
                    const isHighlighted = highlightedCitation === citationId
                    const highlightClass = isHighlighted ? 'bg-yellow-200 dark:bg-yellow-800' : ''

                    if (processed.includes(marker)) {
                      hasCitations = true
                      const citationSpan = `<span class="citation-marker inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors ${highlightClass}" data-citation-id="${citationId}">${marker}</span>`

                      processed = processed.replace(
                        new RegExp(`\\[${citationNumber}\\]`, 'g'),
                        citationSpan
                      )
                    }
                  })

                  if (hasCitations) {
                    return <span dangerouslySetInnerHTML={{ __html: processed }} />
                  }
                  return node
                }
                return node
              }

              const processedChildren = React.Children.map(children, processTextContent)
              // Use the original li component styling
              return (
                <li className="text-gray-900 dark:text-gray-100 leading-relaxed" {...props}>
                  {processedChildren}
                </li>
              )
            }
          }}
        >
          {content}
        </ReactMarkdown>
      </div>
    )
  }

  // For assistant messages without citations, use full markdown
  return (
    <div className="prose prose-sm max-w-none dark:prose-invert">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeSanitize]}
        components={components}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
