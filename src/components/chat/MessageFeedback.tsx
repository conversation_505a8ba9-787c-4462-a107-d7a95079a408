'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { ThumbsUp, ThumbsDown, MessageSquare, X } from 'lucide-react'
import { toast } from 'sonner'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

interface MessageFeedbackProps {
  messageId: string
  className?: string
}

interface Feedback {
  id: string
  feedback_type: 'thumbs_up' | 'thumbs_down'
  feedback_text?: string
  created_at: string
}

export function MessageFeedback({ messageId, className = '' }: MessageFeedbackProps) {
  const [feedback, setFeedback] = useState<Feedback | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showTextInput, setShowTextInput] = useState(false)
  const [feedbackText, setFeedbackText] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    loadExistingFeedback()
  }, [messageId])

  const loadExistingFeedback = async () => {
    try {
      const response = await fetch(`/api/chat/feedback?message_id=${messageId}`)
      
      if (response.ok) {
        const data = await response.json()
        if (data.feedback) {
          setFeedback(data.feedback)
          setFeedbackText(data.feedback.feedback_text || '')
        }
      }
    } catch (error) {
      console.error('Failed to load feedback:', error)
    }
  }

  const submitFeedback = async (feedbackType: 'thumbs_up' | 'thumbs_down', text?: string) => {
    setIsSubmitting(true)
    
    try {
      const response = await fetch('/api/chat/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message_id: messageId,
          feedback_type: feedbackType,
          feedback_text: text || null,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to submit feedback')
      }

      const data = await response.json()
      setFeedback(data.feedback)
      setShowTextInput(false)
      setFeedbackText(data.feedback.feedback_text || '')
      
      toast.success('Thank you for your feedback!')
      
    } catch (error) {
      console.error('Feedback submission error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to submit feedback')
    } finally {
      setIsSubmitting(false)
    }
  }

  const removeFeedback = async () => {
    setIsLoading(true)
    
    try {
      const response = await fetch(`/api/chat/feedback?message_id=${messageId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to remove feedback')
      }

      setFeedback(null)
      setFeedbackText('')
      setShowTextInput(false)
      
      toast.success('Feedback removed')
      
    } catch (error) {
      console.error('Feedback removal error:', error)
      toast.error('Failed to remove feedback')
    } finally {
      setIsLoading(false)
    }
  }

  const handleThumbsUp = () => {
    if (feedback?.feedback_type === 'thumbs_up') {
      removeFeedback()
    } else {
      submitFeedback('thumbs_up')
    }
  }

  const handleThumbsDown = () => {
    if (feedback?.feedback_type === 'thumbs_down') {
      removeFeedback()
    } else {
      setShowTextInput(true)
    }
  }

  const handleTextSubmit = () => {
    if (feedbackText.trim().length > 500) {
      toast.error('Feedback text must be 500 characters or less')
      return
    }
    
    submitFeedback('thumbs_down', feedbackText.trim())
  }

  const handleTextCancel = () => {
    setShowTextInput(false)
    setFeedbackText(feedback?.feedback_text || '')
  }

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* Thumbs Up */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleThumbsUp}
        disabled={isLoading || isSubmitting}
        className={`h-8 w-8 p-0 ${
          feedback?.feedback_type === 'thumbs_up' 
            ? 'text-green-600 bg-green-50 hover:bg-green-100' 
            : 'text-muted-foreground hover:text-green-600'
        }`}
      >
        <ThumbsUp className="h-4 w-4" />
      </Button>

      {/* Thumbs Down with Popover for text input */}
      <Popover open={showTextInput} onOpenChange={setShowTextInput}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleThumbsDown}
            disabled={isLoading || isSubmitting}
            className={`h-8 w-8 p-0 ${
              feedback?.feedback_type === 'thumbs_down' 
                ? 'text-red-600 bg-red-50 hover:bg-red-100' 
                : 'text-muted-foreground hover:text-red-600'
            }`}
          >
            <ThumbsDown className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-80" align="start">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">What could be improved?</span>
            </div>
            
            <Textarea
              placeholder="Optional: Tell us what was wrong or how we can improve..."
              value={feedbackText}
              onChange={(e) => setFeedbackText(e.target.value)}
              className="min-h-[80px] text-sm"
              maxLength={500}
            />
            
            <div className="text-xs text-muted-foreground">
              {feedbackText.length}/500 characters
            </div>
            
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleTextCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleTextSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Show feedback status if exists */}
      {feedback && (
        <div className="ml-2 text-xs text-muted-foreground">
          {feedback.feedback_type === 'thumbs_up' ? 'Helpful' : 'Needs improvement'}
        </div>
      )}
    </div>
  )
}
