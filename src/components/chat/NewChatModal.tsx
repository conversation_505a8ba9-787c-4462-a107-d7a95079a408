'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { AddressInput } from '@/components/address/AddressInput'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { AlertCircle, MapPin } from 'lucide-react'
import { type NewChatData } from '@/lib/types/chat'
import type { AddressData } from '@/lib/types/address'

interface NewChatModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateChat: (chatData: NewChatData) => Promise<void>
}

export function NewChatModal({ isOpen, onClose, onCreateChat }: NewChatModalProps) {
  const [selectedAddress, setSelectedAddress] = useState<AddressData | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleAddressSelect = (address: AddressData | null) => {
    setSelectedAddress(address)
    setError(null)
  }

  const handleCreateChat = async () => {
    if (!selectedAddress) {
      setError('Please select an address')
      return
    }

    try {
      setIsCreating(true)
      setError(null)

      // Create chat with just address - jurisdiction will be discovered like original search
      const chatData: NewChatData = {
        address: selectedAddress.label,
        rule_type: 'general', // Default rule type, can be changed later in chat
        jurisdiction_name: 'TBD', // Will be discovered during first search
        context_data: {
          coordinates: {
            lat: selectedAddress.lat,
            lng: selectedAddress.lng
          },
          county: selectedAddress.county,
          state: selectedAddress.state,
          zip: selectedAddress.zip
        }
      }

      await onCreateChat(chatData)
      handleClose()
    } catch (err) {
      console.error('Error creating chat:', err)
      setError(err instanceof Error ? err.message : 'Failed to create chat')
    } finally {
      setIsCreating(false)
    }
  }

  const handleClose = () => {
    setSelectedAddress(null)
    setError(null)
    onClose()
  }

  const isValid = selectedAddress

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-blue-600" />
            Start New Chat
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Address Input */}
          <div className="space-y-2">
            <Label htmlFor="address">Property Address</Label>
            <AddressInput
              id="address"
              placeholder="Enter property address..."
              onSelect={handleAddressSelect}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              Enter the property address to start a new compliance chat
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800 text-sm">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              {error}
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={handleClose} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={handleCreateChat} 
              disabled={!isValid || isCreating}
              className="flex-1"
            >
              {isCreating ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Creating...
                </>
              ) : (
                'Start Chat'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
