'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { BookOpen, ChevronDown, AlertCircle } from 'lucide-react'
import { usePromptTemplates } from '@/hooks/usePromptTemplates'
import type { PromptTemplate, PromptTemplateCategory } from '@/lib/types/chat'
import { PROMPT_TEMPLATE_CATEGORY_LABELS } from '@/lib/types/chat'

interface PromptTemplateMenuProps {
  onTemplateSelect: (template: PromptTemplate) => void
  disabled?: boolean
  className?: string
}

export function PromptTemplateMenu({ onTemplateSelect, disabled = false, className = '' }: PromptTemplateMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const menuRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  const { 
    templates, 
    categories, 
    isLoading, 
    error, 
    loadTemplates, 
    clearError,
    getAppraisalTemplates 
  } = usePromptTemplates({ autoLoad: true })

  // Close menu when clicking outside or pressing Escape
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false)
        buttonRef.current?.focus()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [isOpen])

  // Filter templates based on selected category
  const filteredTemplates = selectedCategory === 'all' 
    ? getAppraisalTemplates() // Show appraisal-relevant templates by default
    : templates.filter(template => template.category === selectedCategory)

  const handleTemplateClick = (template: PromptTemplate) => {
    onTemplateSelect(template)
    setIsOpen(false)
  }

  const handleRetry = () => {
    clearError()
    loadTemplates()
  }

  return (
    <div className={`relative ${className}`}>
      {/* Template Menu Button */}
      <Button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled || isLoading}
        variant="outline"
        size="sm"
        className="h-11 px-3 gap-2"
        aria-expanded={isOpen}
        aria-haspopup="menu"
        aria-label="Open prompt templates menu"
      >
        <BookOpen className="h-4 w-4" />
        Templates
        <ChevronDown className={`h-3 w-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {/* Dropdown Menu */}
      {isOpen && (
        <Card
          ref={menuRef}
          className="absolute bottom-full mb-2 left-0 w-80 max-h-96 overflow-hidden shadow-lg z-50 animate-slide-up"
        >
          <CardContent className="p-0">
            {/* Header */}
            <div className="p-3 border-b border-border bg-muted/50">
              <h3 className="font-medium text-sm">Prompt Templates</h3>
              <p className="text-xs text-muted-foreground mt-1">
                Select a template to get started with common appraisal questions
              </p>
            </div>

            {/* Category Filter */}
            <div className="p-3 border-b border-border">
              <div className="flex flex-wrap gap-1">
                <Badge
                  variant={selectedCategory === 'all' ? 'default' : 'outline'}
                  className="cursor-pointer text-xs"
                  onClick={() => setSelectedCategory('all')}
                >
                  All Appraisal
                </Badge>
                {categories.map(category => (
                  <Badge
                    key={category}
                    variant={selectedCategory === category ? 'default' : 'outline'}
                    className="cursor-pointer text-xs"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {PROMPT_TEMPLATE_CATEGORY_LABELS[category as PromptTemplateCategory] || category}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="max-h-64 overflow-y-auto">
              {isLoading ? (
                <div className="p-6 text-center">
                  <LoadingSpinner size="sm" />
                  <p className="text-sm text-muted-foreground mt-2">Loading templates...</p>
                </div>
              ) : error ? (
                <div className="p-4 text-center">
                  <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
                  <p className="text-sm text-destructive mb-3">{error}</p>
                  <Button onClick={handleRetry} size="sm" variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : filteredTemplates.length === 0 ? (
                <div className="p-4 text-center">
                  <p className="text-sm text-muted-foreground">
                    No templates found for this category
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-border" role="menu">
                  {filteredTemplates.map(template => (
                    <div
                      key={template.id}
                      onClick={() => handleTemplateClick(template)}
                      className="p-3 hover:bg-muted/50 cursor-pointer transition-colors focus:bg-muted/50 focus:outline-none"
                      role="menuitem"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault()
                          handleTemplateClick(template)
                        }
                      }}
                    >
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm truncate">
                            {template.name}
                          </h4>
                          {template.description && (
                            <p className="text-xs text-muted-foreground mt-1 overflow-hidden" style={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical'
                            }}>
                              {template.description}
                            </p>
                          )}
                        </div>
                        <Badge variant="secondary" className="text-xs shrink-0">
                          {PROMPT_TEMPLATE_CATEGORY_LABELS[template.category as PromptTemplateCategory] || template.category}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="p-2 border-t border-border bg-muted/30">
              <p className="text-xs text-muted-foreground text-center">
                {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} available
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
