'use client'

import { useState, useEffect } from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  AlertTriangle, 
  Clock, 
  Info, 
  X,
  ExternalLink
} from 'lucide-react'
import { StalenessWarning as StalenessWarningType } from '@/lib/types/knowledge'

interface StalenessWarningProps {
  jurisdiction: string
  onDismiss?: () => void
  className?: string
}

export function StalenessWarning({ jurisdiction, onDismiss, className }: StalenessWarningProps) {
  const [warnings, setWarnings] = useState<StalenessWarningType[]>([])
  const [loading, setLoading] = useState(true)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    fetchStalenessWarnings()
  }, [jurisdiction])

  const fetchStalenessWarnings = async () => {
    try {
      setLoading(true)
      
      // Fetch staleness data for the jurisdiction
      const response = await fetch(`/api/admin/knowledge/staleness?jurisdiction=${encodeURIComponent(jurisdiction)}`)
      
      if (!response.ok) {
        // If not admin or API fails, silently skip warnings
        setWarnings([])
        return
      }

      const data = await response.json()
      
      // Convert staleness tracking to warnings
      const warningsData: StalenessWarningType[] = data.tracking
        .filter((item: any) => item.staleness_score > 0.3) // Only show warnings for moderately stale data
        .map((item: any) => ({
          jurisdiction: item.jurisdiction,
          document_type: item.document_type,
          staleness_score: item.staleness_score,
          current_version: item.current_version,
          message: generateWarningMessage(item),
          severity: getSeverity(item.staleness_score),
          show_to_user: shouldShowToUser(item)
        }))
        .filter((warning: StalenessWarningType) => warning.show_to_user)

      setWarnings(warningsData)

    } catch (error) {
      console.warn('Failed to fetch staleness warnings:', error)
      setWarnings([])
    } finally {
      setLoading(false)
    }
  }

  const generateWarningMessage = (item: any): string => {
    const staleness = Math.round(item.staleness_score * 100)
    const docType = item.document_type.replace('_', ' ')
    
    if (item.update_available) {
      return `A newer version of the ${docType} may be available. Current information is ${staleness}% outdated.`
    }
    
    if (staleness > 80) {
      return `The ${docType} information may be significantly outdated (${staleness}% stale). Please verify current requirements.`
    }
    
    if (staleness > 60) {
      return `The ${docType} information may be outdated (${staleness}% stale). Consider checking for recent updates.`
    }
    
    return `The ${docType} information may need updating (${staleness}% stale).`
  }

  const getSeverity = (stalenessScore: number): 'low' | 'medium' | 'high' => {
    if (stalenessScore > 0.8) return 'high'
    if (stalenessScore > 0.6) return 'medium'
    return 'low'
  }

  const shouldShowToUser = (item: any): boolean => {
    // Only show warnings for moderately to highly stale documents
    // Don't overwhelm users with minor staleness issues
    return item.staleness_score > 0.5 || item.update_available
  }

  const getAlertVariant = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive'
      case 'medium': return 'default'
      case 'low': return 'default'
      default: return 'default'
    }
  }

  const getIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertTriangle className="h-4 w-4" />
      case 'medium': return <Clock className="h-4 w-4" />
      case 'low': return <Info className="h-4 w-4" />
      default: return <Info className="h-4 w-4" />
    }
  }

  const handleDismiss = () => {
    setDismissed(true)
    onDismiss?.()
  }

  // Don't render if loading, no warnings, or dismissed
  if (loading || warnings.length === 0 || dismissed) {
    return null
  }

  // Group warnings by severity
  const highSeverityWarnings = warnings.filter(w => w.severity === 'high')
  const mediumSeverityWarnings = warnings.filter(w => w.severity === 'medium')
  const lowSeverityWarnings = warnings.filter(w => w.severity === 'low')

  // Show only the most severe warning to avoid clutter
  const warningToShow = highSeverityWarnings[0] || mediumSeverityWarnings[0] || lowSeverityWarnings[0]

  if (!warningToShow) return null

  return (
    <Alert variant={getAlertVariant(warningToShow.severity)} className={className}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-2">
          {getIcon(warningToShow.severity)}
          <div className="flex-1">
            <AlertDescription className="text-sm">
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium">Data Freshness Notice</span>
                <Badge variant="outline" className="text-xs">
                  {warningToShow.jurisdiction}
                </Badge>
                {warnings.length > 1 && (
                  <Badge variant="secondary" className="text-xs">
                    +{warnings.length - 1} more
                  </Badge>
                )}
              </div>
              <p className="text-sm">{warningToShow.message}</p>
              {warningToShow.current_version && (
                <p className="text-xs text-muted-foreground mt-1">
                  Current version: {warningToShow.current_version}
                </p>
              )}
              <div className="flex items-center space-x-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs h-6"
                  onClick={() => window.open(`/admin/knowledge?jurisdiction=${encodeURIComponent(jurisdiction)}`, '_blank')}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Check Updates
                </Button>
                <span className="text-xs text-muted-foreground">
                  Last checked: {new Date().toLocaleDateString()}
                </span>
              </div>
            </AlertDescription>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={handleDismiss}
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </Alert>
  )
}

// Hook for checking staleness in chat context
export function useStalenessCheck(jurisdiction: string) {
  const [hasStaleData, setHasStaleData] = useState(false)
  const [stalenessScore, setStalenessScore] = useState(0)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!jurisdiction) return

    checkStaleness()
  }, [jurisdiction])

  const checkStaleness = async () => {
    try {
      setLoading(true)
      
      const response = await fetch(`/api/admin/knowledge/staleness?jurisdiction=${encodeURIComponent(jurisdiction)}`)
      
      if (!response.ok) {
        // If not admin or API fails, assume data is fresh
        setHasStaleData(false)
        setStalenessScore(0)
        return
      }

      const data = await response.json()
      
      // Calculate overall staleness for jurisdiction
      const avgStaleness = data.summary.average_staleness || 0
      const hasStale = avgStaleness > 0.3
      
      setHasStaleData(hasStale)
      setStalenessScore(avgStaleness)

    } catch (error) {
      console.warn('Staleness check failed:', error)
      setHasStaleData(false)
      setStalenessScore(0)
    } finally {
      setLoading(false)
    }
  }

  return {
    hasStaleData,
    stalenessScore,
    loading,
    recheckStaleness: checkStaleness
  }
}

// Utility function to determine if staleness warning should be shown
export function shouldShowStalenessWarning(
  jurisdiction: string,
  documentTypes: string[],
  userRole?: string
): boolean {
  // Only show to admin users or in development
  if (userRole !== 'admin' && process.env.NODE_ENV === 'production') {
    return false
  }

  // Don't show for generic or unknown jurisdictions
  if (!jurisdiction || jurisdiction === 'general' || jurisdiction === 'unknown') {
    return false
  }

  // Show for specific document types that are critical
  const criticalDocTypes = ['building_code', 'fire_code', 'electrical_code', 'ordinance']
  const hasCriticalDocs = documentTypes.some(type => criticalDocTypes.includes(type))

  return hasCriticalDocs
}
