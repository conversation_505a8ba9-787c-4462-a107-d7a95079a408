'use client'

interface StreamingIndicatorProps {
  className?: string
}

export function StreamingIndicator({ className = '' }: StreamingIndicatorProps) {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '-0.3s' }}></div>
        <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '-0.15s' }}></div>
        <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
      </div>
      <span className="text-sm text-muted-foreground">AI is typing...</span>
    </div>
  )
}
