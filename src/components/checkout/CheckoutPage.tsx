'use client'

import { useState } from 'react'
import { Check, User, Mail, Building, CreditCard, ExternalLink } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

interface PlanDetails {
  name: string
  price: string
  interval: string
  priceId: string
  features: string[]
  description: string
}

interface User {
  id: string
  email?: string
  user_metadata?: {
    full_name?: string
  }
}

interface Profile {
  name?: string
  company?: string
}

interface CheckoutPageProps {
  plan: PlanDetails
  user: User
  profile: Profile
}

export function CheckoutPage({ plan, user, profile }: CheckoutPageProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const [billingInfo, setBillingInfo] = useState({
    name: profile?.name || user?.user_metadata?.full_name || '',
    email: user?.email || '',
    company: profile?.company || '',
  })

  const handleCheckout = async () => {
    setIsLoading(true)
    try {
      console.log('🚀 Frontend Checkout Debug:')
      console.log('- plan.priceId:', plan.priceId)
      console.log('- plan.name:', plan.name)
      console.log('- planType (lowercase):', plan.name.toLowerCase())

      toast.loading('Creating checkout session...', { id: 'checkout' })

      const response = await fetch('/api/billing/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: plan.priceId,
          planType: plan.name.toLowerCase(),
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.error) {
        console.error('Error creating checkout session:', data.error)

        // Handle specific error for existing subscription
        if (data.code === 'EXISTING_SUBSCRIPTION') {
          toast.error('Subscription Already Active', {
            description: 'You already have an active subscription. Use "Manage Billing" to modify it.',
            id: 'checkout'
          })
          // Redirect to account page after a short delay
          setTimeout(() => {
            window.location.href = '/account'
          }, 2000)
        } else {
          toast.error('Checkout failed', {
            description: 'Please try again or contact support.',
            id: 'checkout'
          })
        }
        return
      }

      toast.success('Redirecting to checkout...', { id: 'checkout' })

      // Redirect to Stripe Checkout using the URL from the response
      if (data.url) {
        window.location.href = data.url
      } else {
        throw new Error('No checkout URL received')
      }
    } catch (error) {
      console.error('Checkout error:', error)
      toast.error('Checkout failed', {
        description: 'Please try again or contact support.',
        id: 'checkout'
      })
    } finally {
      setIsLoading(false)
    }
  }



  return (
    <div className="max-w-4xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Plan Summary */}
        <div className="order-2 lg:order-1">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl">Subscribe to {plan.name}</CardTitle>
                <Badge variant="secondary" className="text-lg px-3 py-1">
                  {plan.price}/{plan.interval}
                </Badge>
              </div>
              <CardDescription className="text-base">
                {plan.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <h3 className="font-semibold text-lg">What&apos;s included:</h3>
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                        <Check className="w-3 h-3 text-green-600" />
                      </div>
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Separator className="my-6" />

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>{plan.price}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Tax</span>
                    <span>Calculated at checkout</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>{plan.price}/{plan.interval}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Checkout Form */}
        <div className="order-1 lg:order-2">
          {/* Account Information */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="w-5 h-5 mr-2" />
                Account Information
              </CardTitle>
              <CardDescription>
                Confirm your account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Full Name</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="name"
                    type="text"
                    placeholder="John Doe"
                    value={billingInfo.name}
                    onChange={(e) => setBillingInfo(prev => ({ ...prev, name: e.target.value }))}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={billingInfo.email}
                    onChange={(e) => setBillingInfo(prev => ({ ...prev, email: e.target.value }))}
                    className="pl-10"
                    disabled
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="company">Company (Optional)</Label>
                <div className="relative">
                  <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="company"
                    type="text"
                    placeholder="Acme Corp"
                    value={billingInfo.company}
                    onChange={(e) => setBillingInfo(prev => ({ ...prev, company: e.target.value }))}
                    className="pl-10"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Checkout Button */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="w-5 h-5 mr-2" />
                Complete Your Purchase
              </CardTitle>
              <CardDescription>
                Click below to proceed to secure checkout
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="p-4 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                      {plan.name} Plan
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Billed {plan.interval}ly
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                      {plan.price}
                    </div>
                    <div className="text-sm text-blue-700 dark:text-blue-300">
                      per {plan.interval}
                    </div>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleCheckout}
                disabled={isLoading}
                className="w-full bg-[#1DA1F2] hover:bg-[#1a91da] text-white"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Creating checkout session...
                  </>
                ) : (
                  <>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Continue to Secure Checkout
                  </>
                )}
              </Button>

              <div className="text-center space-y-2">
                <p className="text-xs text-gray-500">
                  You will be redirected to Stripe&apos;s secure checkout
                </p>
                <div className="flex items-center justify-center space-x-4 text-xs text-gray-400">
                  <span>🔒 SSL Encrypted</span>
                  <span>•</span>
                  <span>💳 Powered by Stripe</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
