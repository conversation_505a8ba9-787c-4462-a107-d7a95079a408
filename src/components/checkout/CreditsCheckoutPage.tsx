'use client'

import { useState, useEffect } from 'react'
import { User, Mail, Star } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { StripeProvider } from '@/components/stripe/StripeProvider'
import { PaymentForm } from '@/components/stripe/PaymentForm'
import { useRouter } from 'next/navigation'

interface CreditPackage {
  id: string
  name: string
  credits: number
  price: string
  priceInCents: number
  description: string
  popular: boolean
}

interface User {
  id: string
  email: string
  user_metadata?: {
    full_name?: string
  }
}

interface Profile {
  name?: string
  subscription_tier: string
  searches_used?: number
  search_limit?: number
  extra_credits?: number
}

interface CreditsCheckoutPageProps {
  packages: CreditPackage[]
  user: User
  profile: Profile
}

export function CreditsCheckoutPage({ packages, user, profile }: CreditsCheckoutPageProps) {
  const [selectedPackage, setSelectedPackage] = useState<CreditPackage>(
    packages.find(p => p.popular) || packages[0]
  )
  const [isLoading, setIsLoading] = useState(false)
  const [clientSecret, setClientSecret] = useState<string | null>(null)

  const router = useRouter()
  const [billingInfo, setBillingInfo] = useState({
    name: profile?.name || user?.user_metadata?.full_name || '',
    email: user?.email || '',
  })

  // Create payment intent when selected package changes
  useEffect(() => {
    const createPaymentIntent = async () => {
      try {
        const response = await fetch('/api/billing/create-credits-payment-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            credits: selectedPackage.credits,
            priceInCents: selectedPackage.priceInCents,
          }),
        })

        const data = await response.json()

        if (data.error) {
          console.error('Error creating payment intent:', data.error)
          alert('Failed to initialize payment. Please try again.')
          return
        }

        setClientSecret(data.clientSecret)
      } catch (error) {
        console.error('Payment intent creation error:', error)
        alert('Failed to initialize payment. Please try again.')
      }
    }

    createPaymentIntent()
  }, [selectedPackage])

  const handlePaymentSuccess = async (paymentIntent: { id: string; status: string }) => {
    setIsLoading(true)
    try {
      // Confirm credits purchase
      const response = await fetch('/api/billing/confirm-credits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentIntentId: paymentIntent.id,
        }),
      })

      const data = await response.json()

      if (data.error) {
        console.error('Error confirming credits purchase:', data.error)
        alert('Payment succeeded but credits were not added. Please contact support.')
        return
      }

      // Redirect to success page
      router.push(`/checkout/success?credits=${selectedPackage.credits}&session_id=${paymentIntent.id}`)
    } catch (error) {
      console.error('Credits confirmation error:', error)
      alert('Payment succeeded but credits were not added. Please contact support.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error)
    router.push(`/checkout/error?error=payment_failed&message=${encodeURIComponent(error)}&credits=${selectedPackage.credits}`)
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Purchase Additional Searches
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Add more searches to your free account
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Package Selection */}
        <div className="order-2 lg:order-1">
          <Card>
            <CardHeader>
              <CardTitle>Choose Your Package</CardTitle>
              <CardDescription>
                Select the number of additional searches you need
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {packages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className={`relative p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedPackage.id === pkg.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    onClick={() => setSelectedPackage(pkg)}
                  >
                    {pkg.popular && (
                      <Badge className="absolute -top-2 left-4 bg-blue-600">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                      </Badge>
                    )}

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-lg">{pkg.name}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{pkg.description}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
                          ${(pkg.priceInCents / pkg.credits / 100).toFixed(2)} per search
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-blue-600">{pkg.price}</div>
                        <div className="text-sm text-gray-500">+{pkg.credits} searches</div>
                      </div>
                    </div>

                    <div className={`absolute left-4 top-4 w-4 h-4 rounded-full border-2 ${
                      selectedPackage.id === pkg.id
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300 dark:border-gray-600'
                    }`}>
                      {selectedPackage.id === pkg.id && (
                        <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-6" />

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Package</span>
                  <span>{selectedPackage.name}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Credits</span>
                  <span>+{selectedPackage.credits} searches</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Tax</span>
                  <span>Calculated at checkout</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>{selectedPackage.price}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Checkout Form */}
        <div className="order-1 lg:order-2">
          {/* Account Information */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="w-5 h-5 mr-2" />
                Account Information
              </CardTitle>
              <CardDescription>
                Confirm your account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Full Name</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="name"
                    type="text"
                    placeholder="John Doe"
                    value={billingInfo.name}
                    onChange={(e) => setBillingInfo(prev => ({ ...prev, name: e.target.value }))}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={billingInfo.email}
                    onChange={(e) => setBillingInfo(prev => ({ ...prev, email: e.target.value }))}
                    className="pl-10"
                    disabled
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Account Status */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Current Account</CardTitle>
              <CardDescription>
                Your account status after purchase
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-2">
                <div className="flex justify-between">
                  <span>Plan:</span>
                  <span className="capitalize">{profile.subscription_tier}</span>
                </div>
                <div className="flex justify-between">
                  <span>Current searches:</span>
                  <span>{profile.searches_used || 0} / {profile.search_limit || 3}</span>
                </div>
                <div className="flex justify-between">
                  <span>Extra credits:</span>
                  <span>{profile.extra_credits || 0}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-medium text-green-600">
                  <span>After purchase:</span>
                  <span>{(profile.extra_credits || 0) + selectedPackage.credits} extra credits</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Form */}
          {clientSecret ? (
            <StripeProvider clientSecret={clientSecret}>
              <PaymentForm
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                submitText={`Purchase ${selectedPackage.credits} Credits - ${selectedPackage.price}`}
                isLoading={isLoading}
                showBillingAddress={false}
              />
            </StripeProvider>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Initializing secure payment...</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
