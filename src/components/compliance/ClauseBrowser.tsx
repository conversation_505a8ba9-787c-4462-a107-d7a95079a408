'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronRight, ExternalLink, Search, Filter, BookOpen } from 'lucide-react'

export interface OrdinanceClause {
  id: string
  section_number?: string
  title: string
  full_text: string
  summary?: string
  tags: string[]
  source_url?: string
  document_title?: string
}

interface ClauseBrowserProps {
  clauses: OrdinanceClause[]
  className?: string
  showUpgradePrompt?: boolean
  onUpgrade?: () => void
  isCollapsible?: boolean
  defaultExpanded?: boolean
}

export function ClauseBrowser({
  clauses,
  className = "",
  showUpgradePrompt = false,
  onUpgrade,
  isCollapsible = false,
  defaultExpanded = true
}: ClauseBrowserProps) {
  const [expandedClauses, setExpandedClauses] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set())
  const [viewMode, setViewMode] = useState<'accordion' | 'table'>('accordion')
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  const toggleExpanded = (clauseId: string) => {
    const newExpanded = new Set(expandedClauses)
    if (newExpanded.has(clauseId)) {
      newExpanded.delete(clauseId)
    } else {
      newExpanded.add(clauseId)
    }
    setExpandedClauses(newExpanded)
  }

  const toggleTag = (tag: string) => {
    const newTags = new Set(selectedTags)
    if (newTags.has(tag)) {
      newTags.delete(tag)
    } else {
      newTags.add(tag)
    }
    setSelectedTags(newTags)
  }

  // Get all unique tags
  const allTags = Array.from(new Set(clauses.flatMap(clause => clause.tags)))

  // Filter clauses based on search and tags
  const filteredClauses = clauses.filter(clause => {
    const matchesSearch = !searchTerm ||
      clause.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      clause.full_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      clause.section_number?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesTags = selectedTags.size === 0 ||
      clause.tags.some(tag => selectedTags.has(tag))

    return matchesSearch && matchesTags
  })

  if (showUpgradePrompt) {
    return (
      <div className={`bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <BookOpen className="h-8 w-8 text-purple-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              📋 Full Clause Browser (Appraiser Feature)
            </h3>
            <p className="text-gray-700 mb-4">
              Upgrade to Appraiser tier to browse all relevant ordinance clauses with full text,
              searchable sections, and detailed source information.
            </p>
            <div className="bg-white rounded-lg p-4 mb-4 border border-gray-200">
              <h4 className="font-medium text-gray-900 mb-2">Clause browser includes:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Complete ordinance text with section numbers</li>
                <li>• Advanced search and filtering</li>
                <li>• Direct links to source documents</li>
                <li>• Organized by topic and relevance</li>
                <li>• Export capabilities for reports</li>
              </ul>
            </div>
            {onUpgrade && (
              <Button
                onClick={onUpgrade}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Upgrade to Appraiser - $59/month
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  if (!clauses || clauses.length === 0) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-6 text-center ${className}`}>
        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Clauses Available</h3>
        <p className="text-gray-600">
          No specific ordinance clauses were found for this project type and location.
        </p>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => isCollapsible && setIsExpanded(!isExpanded)}
          className={`flex items-center space-x-2 ${isCollapsible ? 'hover:text-purple-700 cursor-pointer' : ''}`}
        >
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <BookOpen className="h-5 w-5 text-purple-600" />
            <span>Ordinance Clauses ({filteredClauses.length})</span>
          </h3>
          {isCollapsible && (
            <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          )}
        </button>
        {isExpanded && (
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'accordion' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('accordion')}
            >
              Accordion
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              Table
            </Button>
          </div>
        )}
      </div>

      {/* Search and Filters */}
      {isExpanded && (
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search clauses by title, content, or section number..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>

          {allTags.length > 0 && (
            <div className="flex items-center space-x-2 flex-wrap">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Tags:</span>
              {allTags.map(tag => (
                <button
                  key={tag}
                  onClick={() => toggleTag(tag)}
                  className={`px-2 py-1 text-xs rounded-full border transition-colors ${
                    selectedTags.has(tag)
                      ? 'bg-purple-100 border-purple-300 text-purple-700'
                      : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {tag}
                </button>
              ))}
              {selectedTags.size > 0 && (
                <button
                  onClick={() => setSelectedTags(new Set())}
                  className="text-xs text-purple-600 hover:text-purple-700"
                >
                  Clear all
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Content */}
      {isExpanded && (
        <div>
          {viewMode === 'accordion' ? (
        <div className="space-y-3">
          {filteredClauses.map((clause) => {
            const isExpanded = expandedClauses.has(clause.id)

            return (
              <div key={clause.id} className="border border-gray-200 rounded-lg">
                <button
                  onClick={() => toggleExpanded(clause.id)}
                  className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      {clause.section_number && (
                        <span className="text-sm font-mono text-purple-600 bg-purple-100 px-2 py-1 rounded">
                          §{clause.section_number}
                        </span>
                      )}
                      <h4 className="font-medium text-gray-900">{clause.title}</h4>
                    </div>
                    {clause.summary && (
                      <p className="text-sm text-gray-600">{clause.summary}</p>
                    )}
                  </div>
                  {isExpanded ? (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  )}
                </button>

                {isExpanded && (
                  <div className="px-4 pb-4 border-t border-gray-100">
                    <div className="bg-gray-50 rounded p-3 mb-3">
                      <div className="text-sm text-gray-700 whitespace-pre-wrap">
                        {clause.full_text}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {clause.tags.map(tag => (
                          <span
                            key={tag}
                            className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>

                      {clause.source_url && (
                        <a
                          href={clause.source_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 text-sm text-purple-600 hover:text-purple-700"
                        >
                          <ExternalLink className="h-4 w-4" />
                          <span>View Source</span>
                        </a>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Section</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Title</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Tags</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Source</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredClauses.map((clause) => (
                <tr key={clause.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3">
                    {clause.section_number && (
                      <span className="text-sm font-mono text-purple-600 bg-purple-100 px-2 py-1 rounded">
                        §{clause.section_number}
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-3">
                    <div className="text-sm font-medium text-gray-900">{clause.title}</div>
                    {clause.summary && (
                      <div className="text-sm text-gray-600 mt-1">{clause.summary}</div>
                    )}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex flex-wrap gap-1">
                      {clause.tags.map(tag => (
                        <span
                          key={tag}
                          className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    {clause.source_url ? (
                      <a
                        href={clause.source_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-1 text-sm text-purple-600 hover:text-purple-700"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span>View</span>
                      </a>
                    ) : (
                      <span className="text-sm text-gray-400">N/A</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

          {filteredClauses.length === 0 && (searchTerm || selectedTags.size > 0) && (
            <div className="text-center py-8">
              <p className="text-gray-600">No clauses match your search criteria.</p>
              <Button
                variant="ghost"
                onClick={() => {
                  setSearchTerm('')
                  setSelectedTags(new Set())
                }}
                className="mt-2 text-purple-600"
              >
                Clear filters
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
