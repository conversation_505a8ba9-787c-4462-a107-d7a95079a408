'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, AlertCircle, Info, ChevronDown, ChevronUp } from 'lucide-react'
import { RedFlag } from './RedFlagsDisplay'

interface CompactRedFlagsProps {
  redFlags: RedFlag[]
  className?: string
  showUpgradePrompt?: boolean
  onUpgrade?: () => void
}

export function CompactRedFlags({
  redFlags,
  className = "",
  showUpgradePrompt = false,
  onUpgrade
}: CompactRedFlagsProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [expandedFlag, setExpandedFlag] = useState<string | null>(null)

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-destructive" />
      case 'medium':
        return <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
      case 'low':
        return <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
      default:
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'border-destructive/30 bg-destructive/10 text-destructive'
      case 'high':
        return 'border-orange-500/30 bg-orange-500/10 text-orange-600 dark:text-orange-400'
      case 'medium':
        return 'border-yellow-500/30 bg-yellow-500/10 text-yellow-600 dark:text-yellow-400'
      case 'low':
        return 'border-blue-500/30 bg-blue-500/10 text-blue-600 dark:text-blue-400'
      default:
        return 'border-border bg-muted/20 text-muted-foreground'
    }
  }

  if (showUpgradePrompt) {
    return (
      <Card className={`bg-gradient-to-r from-primary/5 to-secondary/5 border-border ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-lg text-foreground">
            <AlertTriangle className="h-5 w-5 text-primary" />
            <span>Red Flag Detection</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <p className="text-sm text-muted-foreground">
            Get AI-powered detection of potential safety issues and code violations.
          </p>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Safety hazards</li>
            <li>• Code violations</li>
            <li>• Inspection failures</li>
            <li>• Permit requirements</li>
          </ul>
          {onUpgrade && (
            <Button
              onClick={onUpgrade}
              size="sm"
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              Upgrade to Pro
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  if (!redFlags || redFlags.length === 0) {
    return (
      <Card className={`bg-card border-border ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-500/10 border border-green-500/20 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-semibold text-foreground mb-1">No Red Flags Detected</h4>
              <p className="text-xs text-muted-foreground leading-relaxed">
                Our analysis didn't identify any major safety concerns or common violation risks for this project type and location.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Sort by severity and show top 3 by default
  const sortedFlags = [...redFlags].sort((a, b) => {
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 }
    return severityOrder[a.severity] - severityOrder[b.severity]
  })

  const displayFlags = isExpanded ? sortedFlags : sortedFlags.slice(0, 3)

  return (
    <Card className={`bg-card border-border ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-lg text-foreground">
            <AlertTriangle className="h-5 w-5 text-warning" />
            <span>Red Flags</span>
            <Badge variant="secondary" className="text-xs">
              {redFlags.length}
            </Badge>
          </CardTitle>
          {redFlags.length > 3 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-xs p-1 h-auto"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {displayFlags.map((flag) => (
          <div
            key={flag.id}
            className={`border rounded-lg p-3 ${getSeverityColor(flag.severity)}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-2 flex-1">
                {getSeverityIcon(flag.severity)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-1 mb-1">
                    <h4 className="font-medium text-foreground text-sm truncate">
                      {flag.title}
                    </h4>
                    <Badge variant="outline" className="text-xs capitalize">
                      {flag.severity}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground line-clamp-2">
                    {expandedFlag === flag.id
                      ? flag.description
                      : `${flag.description.substring(0, 80)}${flag.description.length > 80 ? '...' : ''}`
                    }
                  </p>
                  {flag.recommendation && expandedFlag === flag.id && (
                    <div className="mt-2 p-2 bg-card rounded border border-border">
                      <div className="text-xs font-medium text-foreground mb-1">Recommendation:</div>
                      <div className="text-xs text-muted-foreground">{flag.recommendation}</div>
                    </div>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setExpandedFlag(expandedFlag === flag.id ? null : flag.id)}
                className="ml-1 p-1 h-auto text-muted-foreground hover:text-foreground"
              >
                {expandedFlag === flag.id ? (
                  <ChevronUp className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
            </div>
          </div>
        ))}

        {!isExpanded && redFlags.length > 3 && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(true)}
            className="w-full text-xs"
          >
            Show {redFlags.length - 3} More
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
