'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ExternalLink, Download, Share2, CheckCircle, AlertTriangle, XCircle, Camera } from 'lucide-react'
import { ComplianceCardData } from '@/lib/types/compliance'
import { LocationImagery } from '@/components/maps/LocationImagery'
import { KeyRuleBadge } from '@/components/ui/KeyRuleBadge'
import ScreenshotMode from '@/components/sharing/ScreenshotMode'

interface ComplianceCardProps extends ComplianceCardData {
  onShare?: () => void
  onDownload?: () => void
}

export function ComplianceCard({
  address,
  ruleType,
  jurisdiction,
  summary,
  onShare,
  onDownload
}: ComplianceCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showScreenshotMode, setShowScreenshotMode] = useState(false)

  const getStatusIcon = () => {
    switch (summary.status) {
      case 'allowed':
        return <CheckCircle className="h-6 w-6 text-success" />
      case 'restricted':
        return <XCircle className="h-6 w-6 text-error" />
      case 'permit_required':
        return <AlertTriangle className="h-6 w-6 text-warning" />
    }
  }

  const getStatusColor = () => {
    switch (summary.status) {
      case 'allowed':
        return 'bg-success-50 border-success-200 dark:bg-success-950 dark:border-success-800'
      case 'restricted':
        return 'bg-error-50 border-error-200 dark:bg-error-950 dark:border-error-800'
      case 'permit_required':
        return 'bg-warning-50 border-warning-200 dark:bg-warning-950 dark:border-warning-800'
    }
  }

  const getStatusText = () => {
    switch (summary.status) {
      case 'allowed':
        return 'Allowed'
      case 'restricted':
        return 'Restricted'
      case 'permit_required':
        return 'Permit Required'
    }
  }

  const getBadgeVariant = () => {
    switch (summary.status) {
      case 'allowed':
        return 'default'
      case 'restricted':
        return 'destructive'
      case 'permit_required':
        return 'secondary'
    }
  }

  return (
    <Card className={`w-full max-w-md mx-auto ${getStatusColor()} shadow-premium hover:shadow-premium-xl transition-all duration-300`} data-testid="compliance-card">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <Badge variant={getBadgeVariant()}>
              {getStatusText()}
            </Badge>
          </div>
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowScreenshotMode(true)}
              title="Download or share as image"
            >
              <Camera className="h-4 w-4" />
            </Button>
            {onShare && (
              <Button variant="ghost" size="sm" onClick={onShare}>
                <Share2 className="h-4 w-4" />
              </Button>
            )}
            {onDownload && (
              <Button variant="ghost" size="sm" onClick={onDownload}>
                <Download className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        <CardTitle className="text-lg font-semibold capitalize">
          {ruleType} Compliance
        </CardTitle>
        <p className="text-sm text-gray-600 truncate" title={address}>
          {address}
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Location Imagery */}
        {address && (
          <div className="flex justify-center">
            <LocationImagery
              lat={0} // TODO: Add coordinates to summary data
              lng={0} // TODO: Add coordinates to summary data
              address={address}
              size="md"
              defaultView="map"
              showToggle={true}
            />
          </div>
        )}

        {/* Key Rule Badge */}
        {summary.status && (
          <KeyRuleBadge
            type={summary.status === 'allowed' ? 'success' :
                  summary.status === 'restricted' ? 'critical' : 'warning'}
            title={`${ruleType} ${getStatusText()}`}
            description={summary.content.substring(0, 100) + '...'}
            animate={true}
          />
        )}

        {/* Enhanced Summary Section with Quality Indicators */}
        <div className="bg-white rounded-lg p-4 border">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-900">
              {jurisdiction.name} ({jurisdiction.level})
            </p>
            {summary.citations && summary.citations.length > 0 && (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-green-700 font-medium">
                  {summary.citations.length} Official Source{summary.citations.length > 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>
          <p className="text-sm text-gray-700 leading-relaxed">
            {summary.content}
          </p>

          {/* Research Quality Metrics */}
          {summary.confidence_score && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>Research Confidence: {Math.round(summary.confidence_score * 100)}%</span>
                {summary.research_time_ms && (
                  <span>Researched in {(summary.research_time_ms / 1000).toFixed(1)}s</span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Zoning Information */}
        {summary.zoning_info && (
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Zoning Information</h4>
            <div className="space-y-2">
              <div>
                <span className="text-xs font-medium text-blue-800">Allowed Zones:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {summary.zoning_info.allowedZones.map((zone, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {zone}
                    </Badge>
                  ))}
                </div>
              </div>
              {summary.zoning_info.restrictions.length > 0 && (
                <div>
                  <span className="text-xs font-medium text-blue-800">Key Restrictions:</span>
                  <ul className="mt-1 text-xs text-blue-700 space-y-1">
                    {summary.zoning_info.restrictions.map((restriction, index) => (
                      <li key={index} className="flex items-start">
                        <span className="mr-1">•</span>
                        <span>{restriction}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Enhanced Regulation Callout */}
        {summary.regulation_callout && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border-l-4 border-blue-500 shadow-sm">
            <h4 className="text-sm font-semibold text-blue-900 mb-3 flex items-center">
              📋 Official Regulation Text
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Verified
              </span>
            </h4>
            <div className="space-y-3">
              {summary.regulation_callout.section && (
                <div className="text-xs font-medium text-blue-800 bg-blue-100 px-2 py-1 rounded">
                  Section {summary.regulation_callout.section}: {summary.regulation_callout.title}
                </div>
              )}

              {/* Actual regulation text */}
              {summary.regulation_callout.text && (
                <div className="bg-white p-3 rounded border border-blue-200">
                  <p className="text-sm text-gray-800 leading-relaxed italic">
                    &quot;{summary.regulation_callout.text}&quot;
                  </p>
                </div>
              )}

              {/* Document source for regulation callout */}
              {summary.regulation_callout.document_title && (
                <div className="text-xs text-blue-700 bg-blue-50 p-2 rounded">
                  <span className="font-medium">Source:</span> {summary.regulation_callout.document_title}
                </div>
              )}

              {summary.regulation_callout.effective_date && (
                <div className="text-xs text-gray-600">
                  Effective: {summary.regulation_callout.effective_date}
                </div>
              )}
              {summary.regulation_callout.url && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs p-1 h-auto text-blue-600 hover:text-blue-800"
                  onClick={() => window.open(summary.regulation_callout?.url, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  View Specific Section
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Specific Requirements */}
        {summary.specific_requirements && Object.keys(summary.specific_requirements).some(key => summary.specific_requirements![key as keyof typeof summary.specific_requirements]) && (
          <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
            <h4 className="text-sm font-medium text-purple-900 mb-2">📏 Specific Requirements</h4>
            <div className="space-y-2 text-xs">
              {summary.specific_requirements.setbacks && (
                <div>
                  <span className="font-medium text-purple-800">Setbacks:</span>
                  <span className="ml-2 text-purple-700">{summary.specific_requirements.setbacks}</span>
                </div>
              )}
              {summary.specific_requirements.height_limits && (
                <div>
                  <span className="font-medium text-purple-800">Height Limit:</span>
                  <span className="ml-2 text-purple-700">{summary.specific_requirements.height_limits}</span>
                </div>
              )}
              {summary.specific_requirements.size_limits && (
                <div>
                  <span className="font-medium text-purple-800">Size Limits:</span>
                  <span className="ml-2 text-purple-700">{summary.specific_requirements.size_limits}</span>
                </div>
              )}
              {summary.specific_requirements.quantity_limits && (
                <div>
                  <span className="font-medium text-purple-800">Quantity Limit:</span>
                  <span className="ml-2 text-purple-700">{summary.specific_requirements.quantity_limits}</span>
                </div>
              )}
              {summary.specific_requirements.lot_size_requirements && (
                <div>
                  <span className="font-medium text-purple-800">Lot Size Required:</span>
                  <span className="ml-2 text-purple-700">{summary.specific_requirements.lot_size_requirements}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Enhanced Permit Information */}
        {summary.permit_info && (
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <h4 className="text-sm font-medium text-yellow-900 mb-2">📋 Permit Information</h4>
            <div className="space-y-2 text-xs">
              <div>
                <span className="font-medium text-yellow-800">Permit Required:</span>
                <span className="ml-2 text-yellow-700">
                  {typeof summary.permit_info.required === 'boolean'
                    ? (summary.permit_info.required ? 'Yes' : 'No')
                    : summary.permit_info.required}
                </span>
              </div>
              {summary.permit_info.fee && (
                <div>
                  <span className="font-medium text-yellow-800">Fee:</span>
                  <span className="ml-2 text-yellow-700">{summary.permit_info.fee}</span>
                </div>
              )}
              {summary.permit_info.office && (
                <div>
                  <span className="font-medium text-yellow-800">Contact:</span>
                  <div className="ml-2 text-yellow-700 mt-1 bg-yellow-100 p-2 rounded">
                    <div className="font-medium">{summary.permit_info.office}</div>
                    {summary.permit_info.phone && (
                      <div className="flex items-center mt-1">
                        <span className="mr-1">📞</span>
                        <a href={`tel:${summary.permit_info.phone}`} className="text-blue-600 hover:underline">
                          {summary.permit_info.phone}
                        </a>
                      </div>
                    )}
                    {summary.permit_info.address && (
                      <div className="text-xs mt-1">{summary.permit_info.address}</div>
                    )}
                  </div>
                </div>
              )}
              {summary.permit_info.note && (
                <div className="text-yellow-700 italic bg-yellow-100 p-2 rounded text-xs">
                  {summary.permit_info.note}
                  <div className="mt-1 text-xs text-yellow-600">
                    💡 Contact the department above for complete requirements and current regulations.
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {summary.citations.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-semibold text-gray-900 flex items-center">
                📚 Official Sources
                <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  {summary.citations.length} Verified
                </span>
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-xs text-blue-600 p-0 h-auto hover:text-blue-800"
              >
                {isExpanded ? 'Collapse' : 'View Details'}
              </Button>
            </div>

            {/* Always show first citation prominently */}
            {summary.citations[0] && (
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border border-green-200">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-green-900 mb-1">
                      {summary.citations[0].title}
                    </div>
                    {summary.citations[0].section && (
                      <div className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded inline-block">
                        Section {summary.citations[0].section}
                      </div>
                    )}
                  </div>
                  {summary.citations[0].url && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs p-2 h-auto text-green-600 hover:text-green-800 ml-2 bg-white border border-green-200"
                      onClick={() => window.open(summary.citations[0].url, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  )}
                </div>
                {summary.citations[0].regulation_text && (
                  <div className="text-xs text-green-800 italic bg-white p-2 rounded border border-green-200 mt-2">
                    &quot;{summary.citations[0].regulation_text}&quot;
                  </div>
                )}
              </div>
            )}

            {isExpanded && summary.citations.length > 1 && (
              <div className="space-y-2">
                {summary.citations.slice(1).map((citation, index) => (
                  <div key={index + 1} className="text-xs text-gray-600 bg-gray-50 p-3 rounded border">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <span className="font-medium text-gray-800">{citation.title}</span>
                        {citation.section && (
                          <span className="ml-2 text-gray-600">§{citation.section}</span>
                        )}
                      </div>
                      {citation.url && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-xs p-1 h-auto text-blue-600 hover:text-blue-800 ml-2"
                          onClick={() => window.open(citation.url, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      )}
                    </div>

                    {/* Document source information */}
                    {citation.document_title && (
                      <div className="text-xs text-blue-700 mb-2 bg-blue-50 p-2 rounded">
                        <span className="font-medium">Found in:</span> {citation.document_title}
                        {citation.source_url && citation.source_url !== citation.url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs p-1 h-auto text-blue-600 hover:text-blue-800 ml-2"
                            onClick={() => window.open(citation.source_url, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View Document
                          </Button>
                        )}
                      </div>
                    )}

                    {citation.regulation_text && (
                      <div className="text-xs text-gray-700 italic bg-white p-2 rounded border-l-2 border-gray-300">
                        &quot;{citation.regulation_text}&quot;
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Enhanced View Full Ordinance Button */}
        {summary.sourceUrl && (
          <div className="space-y-2">
            <Button
              variant="default"
              size="sm"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium"
              onClick={() => window.open(summary.sourceUrl, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              {summary.source_document?.title ? `View ${summary.source_document.title}` : 'View Full Ordinance'}
            </Button>
            {summary.source_document?.type && (
              <div className="text-xs text-center text-gray-500">
                {summary.source_document.type} • Official Document
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-500 text-center pt-2 border-t">
          Generated by Ordrly • {new Date().toLocaleDateString()}
        </div>
      </CardContent>

      {/* Screenshot Mode Modal */}
      <ScreenshotMode
        isOpen={showScreenshotMode}
        onClose={() => setShowScreenshotMode(false)}
        complianceData={{
          status: summary.status,
          summary: {
            summary: summary.content,
            confidence: summary.confidence_score || 0.8
          },
          address,
          ruleType,
          sources: summary.citations.map(citation => ({
            title: citation.title,
            url: citation.url || ''
          }))
        }}
      />
    </Card>
  )
}
