'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageCircle, ChevronDown, ChevronUp } from 'lucide-react'
import { ChatInterface } from '@/components/chat/ChatInterface'
import { type ChatConversation } from '@/lib/types/chat'

interface ComplianceChatWrapperProps {
  conversationId?: string
  contextData?: Record<string, unknown>
  address?: string
  ruleType?: string
  jurisdictionName?: string
  className?: string
  showUpgradePrompt?: boolean
  onUpgrade?: () => void
  variant?: 'full' | 'compact'
}

export function ComplianceChatWrapper({
  conversationId,
  contextData,
  address,
  ruleType,
  jurisdictionName,
  className = "",
  showUpgradePrompt = false,
  onUpgrade,
  variant = 'full'
}: ComplianceChatWrapperProps) {
  const [conversation, setConversation] = useState<ChatConversation | null>(null)
  const [isExpanded, setIsExpanded] = useState(variant === 'full')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Create or fetch conversation when component mounts
  useEffect(() => {
    if (conversationId) {
      // Fetch existing conversation
      fetchConversation(conversationId)
    } else if (address && ruleType && jurisdictionName && !showUpgradePrompt) {
      // Create new conversation
      createConversation()
    }
  }, [conversationId, address, ruleType, jurisdictionName, showUpgradePrompt])

  const fetchConversation = async (id: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/chat/conversations/${id}`)
      if (response.ok) {
        const data = await response.json()
        setConversation(data.conversation)
      } else {
        setError('Failed to load conversation')
      }
    } catch (error) {
      console.error('Error fetching conversation:', error)
      setError('Failed to load conversation')
    } finally {
      setIsLoading(false)
    }
  }

  const createConversation = async () => {
    if (!address || !ruleType || !jurisdictionName) return

    try {
      setIsLoading(true)
      const response = await fetch('/api/chat/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          address,
          rule_type: ruleType,
          jurisdiction_name: jurisdictionName,
          context_data: contextData
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        if (errorData.upgradeRequired) {
          setError('Chat feature requires Pro tier or higher')
          return
        }
        throw new Error(errorData.error || 'Failed to create conversation')
      }

      const data = await response.json()
      setConversation(data.conversation)
    } catch (error) {
      console.error('Error creating conversation:', error)
      setError(error instanceof Error ? error.message : 'Failed to create conversation')
    } finally {
      setIsLoading(false)
    }
  }

  // Upgrade prompt UI
  if (showUpgradePrompt) {
    const UpgradePrompt = () => (
      <div className="bg-gradient-to-r from-primary/5 to-secondary/5 border border-border rounded-lg p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <MessageCircle className="h-8 w-8 text-primary" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              💬 AI Compliance Chat (Pro Feature)
            </h3>
            <p className="text-muted-foreground mb-4">
              Upgrade to Pro to chat with our AI assistant about your specific compliance questions.
              Get quick answers about permits, setbacks, materials, and more.
            </p>
            <div className="bg-card rounded-lg p-4 mb-4 border border-border">
              <h4 className="font-medium text-foreground mb-2">Chat features include:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Ask follow-up questions about your project</li>
                <li>• Get clarification on specific requirements</li>
                <li>• Understand permit processes step-by-step</li>
                <li>• Receive personalized recommendations</li>
                <li>• Access to conversation history</li>
              </ul>
            </div>
            {onUpgrade && (
              <Button onClick={onUpgrade} className="bg-primary hover:bg-primary/90">
                Upgrade to Pro - $19/month
              </Button>
            )}
          </div>
        </div>
      </div>
    )

    return variant === 'compact' ? (
      <Card className={className}>
        <CardContent className="p-4">
          <UpgradePrompt />
        </CardContent>
      </Card>
    ) : (
      <div className={className}>
        <UpgradePrompt />
      </div>
    )
  }

  // Loading state
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading chat...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <p className="text-sm text-destructive mb-2">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline" size="sm">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  // No conversation state
  if (!conversation) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <MessageCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">No conversation available</p>
        </div>
      </div>
    )
  }

  // Compact variant with expandable interface
  if (variant === 'compact') {
    return (
      <Card className={`bg-card border-border ${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 text-lg text-foreground">
              <MessageCircle className="h-5 w-5 text-primary" />
              <span>AI Chat</span>
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 h-auto text-muted-foreground hover:text-foreground"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
          {address && (
            <p className="text-sm text-muted-foreground">
              Discussing {ruleType} project at {address}
            </p>
          )}
        </CardHeader>
        {isExpanded && (
          <CardContent className="pt-0">
            <div className="h-96 border border-border rounded-lg overflow-hidden">
              <ChatInterface conversation={conversation} className="h-full" />
            </div>
          </CardContent>
        )}
      </Card>
    )
  }

  // Full variant
  return (
    <div className={`bg-card border border-border rounded-lg ${className}`}>
      {/* Header */}
      <div className="border-b border-border p-4">
        <div className="flex items-center space-x-2">
          <MessageCircle className="h-5 w-5 text-primary" />
          <h3 className="font-semibold text-foreground">AI Compliance Assistant</h3>
        </div>
        {address && (
          <p className="text-sm text-muted-foreground mt-1">
            Discussing {ruleType} project at {address}
          </p>
        )}
      </div>
      
      {/* Chat Interface */}
      <div className="h-96">
        <ChatInterface conversation={conversation} className="h-full" />
      </div>
    </div>
  )
}
