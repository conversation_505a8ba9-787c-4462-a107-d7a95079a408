'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, AlertTriangle, XCircle, Clock, TrendingUp } from 'lucide-react'
import { CollapsiblePanel } from '@/components/ui/CollapsiblePanel'
import { motion } from 'framer-motion'

type ComplianceStatus = 'allowed' | 'permit_required' | 'restricted'

interface ComplianceStatusPanelProps {
  status: ComplianceStatus
  summary: string
  jurisdiction: {
    name: string
    level: string
  }
  confidence_score?: number
  research_time_ms?: number
  className?: string
}

export function ComplianceStatusPanel({
  status,
  summary,
  jurisdiction,
  confidence_score,
  research_time_ms,
  className = ""
}: ComplianceStatusPanelProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'allowed':
        return <CheckCircle className="h-8 w-8 text-success" />
      case 'restricted':
        return <XCircle className="h-8 w-8 text-error" />
      case 'permit_required':
        return <AlertTriangle className="h-8 w-8 text-warning" />
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'allowed':
        return 'Project Allowed'
      case 'restricted':
        return 'Project Restricted'
      case 'permit_required':
        return 'Permit Required'
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'allowed':
        return 'border-success/20 bg-success/5'
      case 'restricted':
        return 'border-error/20 bg-error/5'
      case 'permit_required':
        return 'border-warning/20 bg-warning/5'
    }
  }

  const getBadgeVariant = () => {
    switch (status) {
      case 'allowed':
        return 'default' as const
      case 'restricted':
        return 'destructive' as const
      case 'permit_required':
        return 'secondary' as const
    }
  }

  const getConfidenceColor = (score: number) => {
    if (score >= 80) return 'text-success'
    if (score >= 60) return 'text-warning'
    return 'text-error'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className={`${getStatusColor()} ${className}`}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              >
                {getStatusIcon()}
              </motion.div>
              <div>
                <CardTitle className="text-xl font-bold text-card-foreground">
                  {getStatusText()}
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {jurisdiction.name} ({jurisdiction.level})
                </p>
              </div>
            </div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Badge variant={getBadgeVariant()} className="text-sm">
                {getStatusText()}
              </Badge>
            </motion.div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Main Summary */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-card rounded-lg p-4 border border-border"
          >
            <h4 className="font-medium text-card-foreground mb-3">Compliance Summary</h4>
            <div className="text-card-foreground leading-relaxed">
              {summary.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).map((sentence, index) => (
                <div key={index} className="flex items-start space-x-2 mb-2 last:mb-0">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                  <span>{sentence.trim()}.</span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Quality Indicators */}
          {(confidence_score !== undefined || research_time_ms !== undefined) && (
            <CollapsiblePanel
              title="Research Quality Metrics"
              defaultExpanded={false}
              icon={<TrendingUp className="h-4 w-4" />}
              badge={
                confidence_score !== undefined && (
                  <Badge
                    variant="outline"
                    className={`text-xs font-bold px-3 py-1 ${getConfidenceColor(confidence_score)} border-current bg-card shadow-sm`}
                  >
                    {Math.round(confidence_score * 100)}% confidence
                  </Badge>
                )
              }
            >
              <div className="grid grid-cols-2 gap-4">
                {confidence_score !== undefined && (
                  <div className="bg-card rounded-lg p-4 border border-border shadow-sm">
                    <div className="flex items-center space-x-2 mb-2">
                      <TrendingUp className="h-4 w-4 text-primary" />
                      <span className="text-sm font-semibold text-card-foreground">Research Confidence</span>
                    </div>
                    <div className="mt-2">
                      <div className={`inline-flex items-center px-3 py-1 rounded-full text-2xl font-bold ${getConfidenceColor(confidence_score)} bg-muted/30 border border-current/20`}>
                        {Math.round(confidence_score * 100)}%
                      </div>
                      {confidence_score < 0.8 && (
                        <div className="text-xs text-warning font-medium mt-2 px-2 py-1 bg-warning/10 rounded border border-warning/20">
                          ⚠️ Lower confidence - consider additional research
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {research_time_ms !== undefined && (
                  <div className="bg-card rounded-lg p-4 border border-border shadow-sm">
                    <div className="flex items-center space-x-2 mb-2">
                      <Clock className="h-4 w-4 text-primary" />
                      <span className="text-sm font-semibold text-card-foreground">Research Time</span>
                    </div>
                    <div className="mt-2">
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-lg font-bold text-card-foreground bg-muted/30 border border-border">
                        {(research_time_ms / 1000).toFixed(1)}s
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CollapsiblePanel>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
