'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { FileText, Phone } from 'lucide-react'

interface PermitInfo {
  permit_required?: boolean
  contact?: string
  process_description?: string
  requirements?: string[]
  estimated_timeline?: string
  fees?: string
}

interface PermitInfoPanelProps {
  permitInfo?: PermitInfo
  className?: string
}

export function PermitInfoPanel({
  permitInfo,
  className = ""
}: PermitInfoPanelProps) {
  if (!permitInfo || Object.keys(permitInfo).length === 0) {
    return null
  }

  const hasContent = permitInfo.permit_required !== undefined ||
                    permitInfo.contact ||
                    permitInfo.process_description ||
                    permitInfo.requirements?.length ||
                    permitInfo.estimated_timeline ||
                    permitInfo.fees

  if (!hasContent) {
    return null
  }

  return (
    <Card className={`bg-card border-border ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-lg text-card-foreground">
            <FileText className="h-5 w-5 text-warning" />
            <span>Permit Information</span>
          </CardTitle>
          {permitInfo.permit_required && (
            <Badge variant="default" className="bg-warning text-warning-foreground border-warning font-semibold">
              Permit Required
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Process Description */}
        {permitInfo.process_description && (
          <div className="bg-warning/10 rounded-lg p-4 border border-warning/20">
            <h4 className="font-medium text-card-foreground mb-2">Process Overview</h4>
            <p className="text-sm text-muted-foreground">{permitInfo.process_description}</p>
          </div>
        )}

        {/* Contact Information */}
        {permitInfo.contact && (
          <div>
            <h4 className="font-medium text-card-foreground mb-2">Contact</h4>
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{permitInfo.contact}</span>
            </div>
          </div>
        )}

        {/* Requirements */}
        {permitInfo.requirements && permitInfo.requirements.length > 0 && (
          <div>
            <h4 className="font-medium text-card-foreground mb-2">Requirements</h4>
            <ul className="space-y-1">
              {permitInfo.requirements.map((requirement, index) => (
                <li key={index} className="text-sm text-muted-foreground flex items-start space-x-2">
                  <span className="text-muted-foreground/60 mt-1">•</span>
                  <span>{requirement}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Timeline and Fees */}
        {(permitInfo.estimated_timeline || permitInfo.fees) && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {permitInfo.estimated_timeline && (
              <div className="bg-muted/30 rounded-lg p-3">
                <h5 className="text-sm font-medium text-card-foreground mb-1">Timeline</h5>
                <p className="text-sm text-muted-foreground">{permitInfo.estimated_timeline}</p>
              </div>
            )}

            {permitInfo.fees && (
              <div className="bg-muted/30 rounded-lg p-3">
                <h5 className="text-sm font-medium text-card-foreground mb-1">Fees</h5>
                <p className="text-sm text-muted-foreground">{permitInfo.fees}</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
