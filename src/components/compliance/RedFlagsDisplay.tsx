'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { AlertTriangle, AlertCircle, Info, ChevronDown, ChevronUp } from 'lucide-react'

export interface RedFlag {
  id: string
  type: 'safety' | 'inspection' | 'compliance' | 'code_violation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  ordinance_citation?: string
  recommendation?: string
}

interface RedFlagsDisplayProps {
  redFlags: RedFlag[]
  className?: string
  showUpgradePrompt?: boolean
  onUpgrade?: () => void
}

export function RedFlagsDisplay({
  redFlags,
  className = "",
  showUpgradePrompt = false,
  onUpgrade
}: RedFlagsDisplayProps) {
  const [expandedFlags, setExpandedFlags] = useState<Set<string>>(new Set())
  const [showAll, setShowAll] = useState(false)

  const toggleExpanded = (flagId: string) => {
    const newExpanded = new Set(expandedFlags)
    if (newExpanded.has(flagId)) {
      newExpanded.delete(flagId)
    } else {
      newExpanded.add(flagId)
    }
    setExpandedFlags(newExpanded)
  }

  const getSeverityIcon = (severity: RedFlag['severity']) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-600" />
      case 'medium':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />
      case 'low':
        return <Info className="h-5 w-5 text-blue-600" />
    }
  }

  const getSeverityColor = (severity: RedFlag['severity']) => {
    switch (severity) {
      case 'critical':
        return 'border-red-200 bg-red-50'
      case 'high':
        return 'border-orange-200 bg-orange-50'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50'
      case 'low':
        return 'border-blue-200 bg-blue-50'
    }
  }

  const getTypeLabel = (type: RedFlag['type']) => {
    switch (type) {
      case 'safety':
        return 'Safety Issue'
      case 'inspection':
        return 'Inspection Point'
      case 'compliance':
        return 'Compliance Requirement'
      case 'code_violation':
        return 'Code Violation Risk'
    }
  }

  if (showUpgradePrompt) {
    return (
      <div className={`bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-8 w-8 text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              🚩 Red Flag Detection Available
            </h3>
            <p className="text-gray-700 mb-4">
              Upgrade to Pro to get AI-powered detection of potential safety issues,
              common inspection problems, and code violation risks specific to your project.
            </p>
            <div className="bg-white rounded-lg p-4 mb-4 border border-gray-200">
              <h4 className="font-medium text-gray-900 mb-2">Red flags help you identify:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Safety hazards (electrical, structural, fire)</li>
                <li>• Common code violations</li>
                <li>• Inspection failure points</li>
                <li>• Permit requirements often missed</li>
              </ul>
            </div>
            {onUpgrade && (
              <Button
                onClick={onUpgrade}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Upgrade to Pro - $19/month
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  if (!redFlags || redFlags.length === 0) {
    return (
      <div className={`bg-green-500/10 border border-green-500/20 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-6 h-6 bg-green-500/20 border border-green-500/30 rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-semibold text-green-800 dark:text-green-200">No Red Flags Detected</h4>
            <p className="text-sm text-green-700 dark:text-green-300">
              Our analysis didn&apos;t identify any major safety concerns or common violation risks for this project.
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Sort by severity (critical first)
  const sortedFlags = [...redFlags].sort((a, b) => {
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 }
    return severityOrder[a.severity] - severityOrder[b.severity]
  })

  const displayFlags = showAll ? sortedFlags : sortedFlags.slice(0, 3)

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <AlertTriangle className="h-5 w-5 text-orange-600" />
          <span>Red Flags Detected ({redFlags.length})</span>
        </h3>
        {redFlags.length > 3 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAll(!showAll)}
            className="text-blue-600 hover:text-blue-700"
          >
            {showAll ? 'Show Less' : `Show All ${redFlags.length}`}
          </Button>
        )}
      </div>

      <div className="space-y-3">
        {displayFlags.map((flag) => {
          const isExpanded = expandedFlags.has(flag.id)

          return (
            <div
              key={flag.id}
              className={`border rounded-lg p-4 ${getSeverityColor(flag.severity)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  {getSeverityIcon(flag.severity)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900">{flag.title}</h4>
                      <span className="text-xs px-2 py-1 bg-white rounded-full text-gray-600 border">
                        {getTypeLabel(flag.type)}
                      </span>
                      <span className="text-xs px-2 py-1 bg-white rounded-full text-gray-600 border capitalize">
                        {flag.severity}
                      </span>
                    </div>

                    <p className="text-sm text-gray-700 mb-2">
                      {isExpanded ? flag.description : `${flag.description.substring(0, 120)}${flag.description.length > 120 ? '...' : ''}`}
                    </p>

                    {isExpanded && (
                      <div className="space-y-2">
                        {flag.ordinance_citation && (
                          <div className="text-xs text-gray-600">
                            <strong>Citation:</strong> {flag.ordinance_citation}
                          </div>
                        )}
                        {flag.recommendation && (
                          <div className="bg-white rounded p-3 border border-gray-200">
                            <div className="text-xs font-medium text-gray-900 mb-1">Recommendation:</div>
                            <div className="text-sm text-gray-700">{flag.recommendation}</div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleExpanded(flag.id)}
                  className="ml-2 p-1 h-auto"
                >
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )
        })}
      </div>

      {!showAll && redFlags.length > 3 && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => setShowAll(true)}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            Show {redFlags.length - 3} More Red Flags
          </Button>
        </div>
      )}
    </div>
  )
}
