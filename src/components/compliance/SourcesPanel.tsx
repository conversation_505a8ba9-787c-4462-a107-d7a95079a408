'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ExternalLink, FileText, CheckCircle } from 'lucide-react'

interface Citation {
  title: string
  url?: string
  section?: string
  document_type?: string
}

interface SourcesPanelProps {
  citations?: Citation[]
  sourceUrl?: string
  className?: string
}

export function SourcesPanel({
  citations,
  sourceUrl,
  className = ""
}: SourcesPanelProps) {
  const hasCitations = citations && citations.length > 0
  const hasSourceUrl = sourceUrl && sourceUrl.trim() !== ''

  if (!hasCitations && !hasSourceUrl) {
    return null
  }

  return (
    <Card className={`bg-card border-border sources-panel ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-2 text-lg text-card-foreground">
          <FileText className="h-5 w-5 text-success" />
          <span>Official Sources</span>
          {hasCitations && (
            <Badge variant="default" className="bg-success/10 text-success border-success/20 ml-2">
              {citations.length} Verified
            </Badge>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Verification Status */}
        {hasCitations && (
          <div className="bg-success/10 rounded-lg p-3 border border-success/20">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-success" />
              <span className="text-sm font-medium text-success">
                Information verified from {citations.length} official source{citations.length > 1 ? 's' : ''}
              </span>
            </div>
          </div>
        )}

        {/* Citations List */}
        {hasCitations && (
          <div className="space-y-3">
            <h4 className="font-semibold text-card-foreground text-base">Source Documents</h4>
            {citations.map((citation, index) => (
              <div key={index} className="border border-border rounded-lg p-3 hover:bg-muted/20 hover:border-primary/30 transition-all duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h5 className="font-semibold text-card-foreground text-sm mb-1">
                      {citation.title}
                    </h5>
                    {citation.section && (
                      <p className="text-xs text-muted-foreground mb-2 font-medium">
                        Section: {citation.section}
                      </p>
                    )}
                    {citation.document_type && (
                      <Badge variant="outline" className="text-xs font-medium">
                        {citation.document_type}
                      </Badge>
                    )}
                  </div>
                  {citation.url && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(citation.url, '_blank')}
                      className="ml-2 p-2 h-auto hover:bg-primary/10 hover:text-primary transition-colors"
                      title="View official document"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Main Source URL */}
        {hasSourceUrl && (
          <div>
            <h4 className="font-semibold text-card-foreground mb-3">Primary Source</h4>
            <Button
              variant="ghost"
              onClick={() => window.open(sourceUrl, '_blank')}
              className="w-full justify-between text-left p-4 bg-muted/20 border border-border hover:bg-muted/40 hover:border-primary/40 transition-all duration-200 rounded-lg"
            >
              <span className="text-sm font-semibold text-card-foreground truncate">
                📄 View Official Regulation Text
              </span>
              <ExternalLink className="h-5 w-5 ml-2 flex-shrink-0 text-muted-foreground hover:text-primary transition-colors" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
