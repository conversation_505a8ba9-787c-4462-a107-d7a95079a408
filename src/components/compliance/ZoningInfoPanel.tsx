'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { MapPin, CheckCircle } from 'lucide-react'
import { ZoningInfo } from '@/lib/types/compliance'

interface ZoningInfoPanelProps {
  zoningInfo?: ZoningInfo
  className?: string
}

export function ZoningInfoPanel({
  zoningInfo,
  className = ""
}: ZoningInfoPanelProps) {
  if (!zoningInfo || Object.keys(zoningInfo).length === 0) {
    return null
  }

  const hasContent = zoningInfo.allowedZones?.length ||
                    zoningInfo.restrictions?.length

  if (!hasContent) {
    return null
  }

  return (
    <Card className={`bg-card border-border ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-2 text-lg font-semibold text-foreground">
          <MapPin className="h-5 w-5 text-primary" />
          <span>Zoning Information</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Allowed Zones */}
        {zoningInfo.allowedZones && zoningInfo.allowedZones.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="h-4 w-4 text-success" />
              <h4 className="font-semibold text-foreground">Allowed Zones</h4>
            </div>
            <div className="flex flex-wrap gap-2">
              {zoningInfo.allowedZones.map((zone, index) => (
                <Badge key={index} variant="default" className="bg-success/10 text-success border-success/20">
                  {zone}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Key Restrictions */}
        {zoningInfo.restrictions && zoningInfo.restrictions.length > 0 && (
          <div>
            <h4 className="font-medium text-card-foreground mb-2">Key Restrictions</h4>
            <ul className="space-y-1">
              {zoningInfo.restrictions.map((restriction, index) => (
                <li key={index} className="text-sm text-muted-foreground flex items-start space-x-2">
                  <span className="text-muted-foreground/60 mt-1">•</span>
                  <span>{restriction}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
