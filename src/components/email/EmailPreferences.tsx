'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Mail, BarChart3, Clock, CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface EmailPreferences {
  marketing: boolean
  nudges: boolean
  updates: boolean
}

interface CampaignStats {
  total: number
  sent: number
  opened: number
  clicked: number
  openRate: number
  clickRate: number
}

interface EmailCampaignData {
  emailPreferences: EmailPreferences
  campaignStats: CampaignStats
  lastSearch?: {
    timestamp: string
    ruleType: string
    address: string
  }
  recentCampaigns?: Array<{
    id: string
    type: string
    ruleType: string
    address: string
    scheduledFor: string
    sentAt?: string
    openedAt?: string
    clickedAt?: string
    status: string
    createdAt: string
  }>
}

export default function EmailPreferences() {
  const [data, setData] = useState<EmailCampaignData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preferences, setPreferences] = useState<EmailPreferences>({
    marketing: true,
    nudges: true,
    updates: true
  })

  useEffect(() => {
    fetchEmailData()
  }, [])

  const fetchEmailData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/email-campaigns')

      if (!response.ok) {
        throw new Error('Failed to fetch email data')
      }

      const result = await response.json()
      if (result.success) {
        setData(result.data)
        setPreferences(result.data.emailPreferences)
      } else {
        throw new Error(result.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const updatePreferences = async (newPreferences: EmailPreferences) => {
    try {
      setSaving(true)
      const response = await fetch('/api/email-campaigns', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_preferences',
          preferences: newPreferences
        })
      })

      const result = await response.json()
      if (result.success) {
        setPreferences(newPreferences)
        // No need to refresh all data - preferences are already updated
      } else {
        throw new Error(result.error || 'Failed to update preferences')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setSaving(false)
    }
  }

  const handlePreferenceChange = (key: keyof EmailPreferences, value: boolean) => {
    const newPreferences = { ...preferences, [key]: value }
    updatePreferences(newPreferences)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
      case 'opened':
      case 'clicked':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'scheduled':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Sent</Badge>
      case 'opened':
        return <Badge variant="default" className="bg-green-100 text-green-800">Opened</Badge>
      case 'clicked':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Clicked</Badge>
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>
      case 'cancelled':
        return <Badge variant="outline">Cancelled</Badge>
      case 'scheduled':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Scheduled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2].map(i => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchEmailData} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-foreground">Email Preferences</h2>
        <p className="text-muted-foreground mt-1">
          Manage your email notifications and view campaign statistics
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Email Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              Email Settings
            </CardTitle>
            <CardDescription>
              Choose which emails you&apos;d like to receive from us
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Marketing Emails</label>
                <p className="text-xs text-gray-500">Product updates and announcements</p>
              </div>
              <Switch
                checked={preferences.marketing}
                onCheckedChange={(checked) => handlePreferenceChange('marketing', checked)}
                disabled={saving}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Search Reminders</label>
                <p className="text-xs text-gray-500">Helpful nudges about your searches</p>
              </div>
              <Switch
                checked={preferences.nudges}
                onCheckedChange={(checked) => handlePreferenceChange('nudges', checked)}
                disabled={saving}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Account Updates</label>
                <p className="text-xs text-gray-500">Important account and service updates</p>
              </div>
              <Switch
                checked={preferences.updates}
                onCheckedChange={(checked) => handlePreferenceChange('updates', checked)}
                disabled={saving}
              />
            </div>

            {saving && (
              <div className="flex items-center justify-center py-2">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span className="text-sm text-gray-600">Saving preferences...</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Campaign Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Email Statistics
            </CardTitle>
            <CardDescription>
              Your email engagement metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-card-foreground">{data?.campaignStats.total || 0}</div>
                <div className="text-xs text-muted-foreground">Total Emails</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-card-foreground">{data?.campaignStats.sent || 0}</div>
                <div className="text-xs text-muted-foreground">Sent</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-success">{data?.campaignStats.openRate || 0}%</div>
                <div className="text-xs text-muted-foreground">Open Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{data?.campaignStats.clickRate || 0}%</div>
                <div className="text-xs text-muted-foreground">Click Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Campaigns */}
      {data?.recentCampaigns && data.recentCampaigns.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Email Campaigns</CardTitle>
            <CardDescription>
              Your recent email interactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recentCampaigns.slice(0, 5).map((campaign) => (
                <div key={campaign.id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(campaign.status)}
                    <div>
                      <p className="text-sm font-medium">
                        {campaign.type === 'abandonment_nudge' ? 'Search Reminder' :
                         campaign.type === 'upgrade_reminder' ? 'Upgrade Reminder' :
                         campaign.type}
                      </p>
                      <p className="text-xs text-gray-500">
                        {campaign.ruleType} • {campaign.address}
                      </p>
                      <p className="text-xs text-gray-400">
                        {campaign.sentAt ?
                          `Sent ${new Date(campaign.sentAt).toLocaleDateString()}` :
                          `Scheduled for ${new Date(campaign.scheduledFor).toLocaleDateString()}`
                        }
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(campaign.status)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Last Search Info */}
      {data?.lastSearch && (
        <Card>
          <CardHeader>
            <CardTitle>Last Search Activity</CardTitle>
            <CardDescription>
              Your most recent search without upgrade
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">{data.lastSearch.ruleType} compliance</p>
                <p className="text-xs text-gray-500">{data.lastSearch.address}</p>
                <p className="text-xs text-gray-400">
                  {new Date(data.lastSearch.timestamp).toLocaleString()}
                </p>
              </div>
              <div className="text-xs text-gray-500">
                {preferences.nudges ? 'Reminders enabled' : 'Reminders disabled'}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
