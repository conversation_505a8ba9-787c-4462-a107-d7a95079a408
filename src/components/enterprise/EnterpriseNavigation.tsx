'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { User } from '@supabase/supabase-js'
import {
  Building,
  Shield,
  Settings,
  LogOut,
  Palette
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface EnterpriseNavigationProps {
  user: User
  profile: {
    subscription_tier: string
    role: string
    full_name: string
  }
}

const enterpriseNavItems = [
  {
    name: 'Branding',
    href: '/enterprise/branding',
    icon: Palette,
    description: 'Custom branding and white-labeling'
  },
  {
    name: 'Security',
    href: '/enterprise/security',
    icon: Shield,
    description: 'Advanced security features'
  }
]

export function EnterpriseNavigation({ profile }: Omit<EnterpriseNavigationProps, 'user'>) {
  const pathname = usePathname()

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <Link href="/enterprise/branding" className="flex items-center space-x-2">
              <Building className="h-8 w-8 text-purple-600" />
              <span className="text-xl font-bold text-gray-900">
                Enterprise Console
              </span>
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-1">
            {enterpriseNavItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={item.description}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              )
            })}

            {/* Link back to Business Dashboard */}
            <Link
              href="/business/dashboard"
              className="flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              <Settings className="h-4 w-4" />
              <span>Business Dashboard</span>
            </Link>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              {profile.full_name} ({profile.subscription_tier})
            </div>
            <Link href="/auth/signout">
              <Button variant="outline" size="sm">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </Link>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden pb-4">
          <div className="grid grid-cols-1 gap-2">
            {enterpriseNavItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              )
            })}

            <Link
              href="/business/dashboard"
              className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              <Settings className="h-4 w-4" />
              <span>Business Dashboard</span>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}
