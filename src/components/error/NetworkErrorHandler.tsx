'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Wifi, WifiOff } from 'lucide-react'

interface NetworkErrorHandlerProps {
  children: React.ReactNode
}

export function NetworkErrorHandler({ children }: NetworkErrorHandlerProps) {
  const [isOnline, setIsOnline] = useState(true)
  const [hasNetworkError, setHasNetworkError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      setHasNetworkError(false)
      setRetryCount(0)
    }

    const handleOffline = () => {
      setIsOnline(false)
      setHasNetworkError(true)
    }

    // Listen for network status changes
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Check initial network status
    setIsOnline(navigator.onLine)

    // Intercept fetch requests to detect network errors
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args)
        
        // Reset error state on successful request
        if (response.ok) {
          setHasNetworkError(false)
          setRetryCount(0)
        }
        
        return response
      } catch (error) {
        // Check if it's a network error
        if (error instanceof TypeError && error.message.includes('fetch')) {
          setHasNetworkError(true)
          setRetryCount(prev => prev + 1)
        }
        throw error
      }
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      window.fetch = originalFetch
    }
  }, [])

  const handleRetry = () => {
    setHasNetworkError(false)
    setRetryCount(0)
    window.location.reload()
  }

  const handleGoOffline = () => {
    // For testing purposes - simulate offline mode
    setIsOnline(false)
    setHasNetworkError(true)
  }

  if (!isOnline || hasNetworkError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              {isOnline ? (
                <AlertTriangle className="h-12 w-12 text-yellow-500" />
              ) : (
                <WifiOff className="h-12 w-12 text-red-500" />
              )}
            </div>
            <CardTitle>
              {isOnline ? 'Network Error' : 'No Internet Connection'}
            </CardTitle>
            <CardDescription>
              {isOnline 
                ? 'Unable to connect to our servers. Please check your connection and try again.'
                : 'Please check your internet connection and try again.'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {retryCount > 0 && (
              <div className="text-sm text-gray-600 text-center">
                Retry attempt: {retryCount}
              </div>
            )}
            
            <div className="flex flex-col gap-2">
              <Button onClick={handleRetry} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              
              <Button variant="outline" onClick={() => window.location.href = '/'}>
                Go to Homepage
              </Button>
            </div>

            {/* Network Status Indicator */}
            <div className="flex items-center justify-center text-sm text-gray-500">
              {isOnline ? (
                <>
                  <Wifi className="h-4 w-4 mr-1 text-green-500" />
                  Connected
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 mr-1 text-red-500" />
                  Offline
                </>
              )}
            </div>

            {/* Debug controls for testing */}
            {process.env.NODE_ENV === 'development' && (
              <div className="border-t pt-4 space-y-2">
                <p className="text-xs text-gray-500 text-center">Debug Controls</p>
                <div className="flex gap-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={handleGoOffline}
                    className="flex-1"
                  >
                    Simulate Offline
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => {
                      setIsOnline(true)
                      setHasNetworkError(false)
                    }}
                    className="flex-1"
                  >
                    Simulate Online
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return <>{children}</>
}
