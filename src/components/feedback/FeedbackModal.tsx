'use client'

/* eslint-disable react/no-unescaped-entities */
import { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { X, Bug, Lightbulb, MessageCircle, Send, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/toast'
import { createClient } from '@/lib/supabase/client'

interface FeedbackModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmitted: () => void
}

type FeedbackCategory = 'bug' | 'feature_request' | 'general'

const categories = [
  {
    id: 'bug' as FeedbackCategory,
    name: 'Bug Report',
    description: 'Something isn\'t working correctly',
    icon: Bug,
    color: 'red'
  },
  {
    id: 'feature_request' as FeedbackCategory,
    name: 'Feature Request',
    description: 'Suggest a new feature or improvement',
    icon: Lightbulb,
    color: 'yellow'
  },
  {
    id: 'general' as FeedbackCategory,
    name: 'General Feedback',
    description: 'Share your thoughts or ask questions',
    icon: MessageCircle,
    color: 'blue'
  }
]

export function FeedbackModal({ isOpen, onClose, onSubmitted }: FeedbackModalProps) {
  const [selectedCategory, setSelectedCategory] = useState<FeedbackCategory>('general')
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const { addToast } = useToast()
  const supabase = createClient()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!title.trim() || !description.trim()) return

    setIsSubmitting(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()

      const feedbackData = {
        user_id: user?.id,
        category: selectedCategory,
        title: title.trim(),
        description: description.trim(),
        metadata: {
          user_agent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        }
      }

      const { error } = await supabase
        .from('feedback')
        .insert([feedbackData])

      if (error) throw error

      setIsSubmitted(true)
      addToast({
        type: 'success',
        title: 'Feedback Submitted',
        description: 'Thank you for your feedback! We\'ll review it and get back to you if needed.'
      })
      setTimeout(() => {
        onSubmitted()
        handleClose()
      }, 2000)
    } catch (error) {
      console.error('Error submitting feedback:', error)
      addToast({
        type: 'error',
        title: 'Submission Failed',
        description: 'Failed to submit feedback. Please try again.'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    setSelectedCategory('general')
    setTitle('')
    setDescription('')
    setIsSubmitting(false)
    setIsSubmitted(false)
    onClose()
  }

  if (isSubmitted) {
    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={handleClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-center align-middle shadow-xl transition-all">
                  <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <Dialog.Title className="text-lg font-semibold text-gray-900 mb-2">
                    Thank You!
                  </Dialog.Title>
                  <p className="text-gray-600">
                    Your feedback has been submitted successfully. We'll review it and get back to you if needed.
                  </p>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    )
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title className="text-lg font-semibold text-gray-900">
                    Share Your Feedback
                  </Dialog.Title>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Category Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      What type of feedback is this?
                    </label>
                    <div className="grid grid-cols-1 gap-3">
                      {categories.map((category) => {
                        const IconComponent = category.icon
                        return (
                          <button
                            key={category.id}
                            type="button"
                            onClick={() => setSelectedCategory(category.id)}
                            className={`p-3 rounded-lg border-2 text-left transition-all ${
                              selectedCategory === category.id
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <IconComponent className={`h-5 w-5 ${
                                category.color === 'red' ? 'text-red-600' :
                                category.color === 'yellow' ? 'text-yellow-600' :
                                'text-blue-600'
                              }`} />
                              <div>
                                <div className="font-medium text-gray-900">{category.name}</div>
                                <div className="text-sm text-gray-600">{category.description}</div>
                              </div>
                            </div>
                          </button>
                        )
                      })}
                    </div>
                  </div>

                  {/* Title */}
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                      Title
                    </label>
                    <input
                      type="text"
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Brief summary of your feedback"
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                      maxLength={100}
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Please provide details about your feedback..."
                      rows={4}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                      maxLength={1000}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {description.length}/1000 characters
                    </p>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end space-x-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleClose}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={!title.trim() || !description.trim() || isSubmitting}
                      className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                    >
                      <Send className="h-4 w-4" />
                      <span>{isSubmitting ? 'Submitting...' : 'Submit Feedback'}</span>
                    </Button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
