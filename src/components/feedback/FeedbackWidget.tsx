'use client'

import { useState } from 'react'
import { MessageSquare, X } from 'lucide-react'
import { FeedbackModal } from './FeedbackModal'

interface FeedbackWidgetProps {
  className?: string
}

export function FeedbackWidget({ className = '' }: FeedbackWidgetProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)

  return (
    <>
      {/* Floating Feedback Button */}
      <div className={`fixed bottom-20 right-6 z-40 ${className}`}>
        {!isMinimized ? (
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center space-x-2">
                <div className="bg-blue-100 rounded-full p-1">
                  <MessageSquare className="h-4 w-4 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900 text-sm">Feedback</h3>
              </div>
              <button
                onClick={() => setIsMinimized(true)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <p className="text-xs text-gray-600 mb-3">
              Help us improve Ordrly! Share your thoughts, report bugs, or suggest features.
            </p>
            <button
              onClick={() => setIsModalOpen(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-3 rounded-md transition-colors"
            >
              Give Feedback
            </button>
          </div>
        ) : (
          <button
            onClick={() => setIsMinimized(false)}
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-all hover:scale-105"
            title="Give Feedback"
          >
            <MessageSquare className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmitted={() => {
          setIsModalOpen(false)
          setIsMinimized(true)
        }}
      />
    </>
  )
}
