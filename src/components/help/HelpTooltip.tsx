'use client'

import { useState, useRef, useEffect, useCallback } from 'react'

interface HelpTooltipProps {
  content: string
  title?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  trigger?: 'hover' | 'click'
  icon?: 'help' | 'info'
  className?: string
  children?: React.ReactNode
}

export default function HelpTooltip({
  content,
  title,
  position = 'top',
  trigger = 'hover',
  icon = 'help',
  className = '',
  children
}: HelpTooltipProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 })
  const triggerRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)

  const updatePosition = useCallback(() => {
    if (!triggerRef.current || !tooltipRef.current) return

    const triggerRect = triggerRef.current.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }

    let top = 0
    let left = 0

    switch (position) {
      case 'top':
        top = triggerRect.top - tooltipRect.height - 8
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
        break
      case 'bottom':
        top = triggerRect.bottom + 8
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
        break
      case 'left':
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
        left = triggerRect.left - tooltipRect.width - 8
        break
      case 'right':
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
        left = triggerRect.right + 8
        break
    }

    // Keep tooltip within viewport
    if (left < 8) left = 8
    if (left + tooltipRect.width > viewport.width - 8) {
      left = viewport.width - tooltipRect.width - 8
    }
    if (top < 8) top = 8
    if (top + tooltipRect.height > viewport.height - 8) {
      top = viewport.height - tooltipRect.height - 8
    }

    setTooltipPosition({ top, left })
  }, [position])

  useEffect(() => {
    if (isVisible) {
      updatePosition()
      window.addEventListener('scroll', updatePosition)
      window.addEventListener('resize', updatePosition)
      return () => {
        window.removeEventListener('scroll', updatePosition)
        window.removeEventListener('resize', updatePosition)
      }
    }
  }, [isVisible, position, updatePosition])

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      setIsVisible(true)
    }
  }

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      setIsVisible(false)
    }
  }

  const handleClick = () => {
    if (trigger === 'click') {
      setIsVisible(!isVisible)
    }
  }

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      trigger === 'click' &&
      triggerRef.current &&
      tooltipRef.current &&
      !triggerRef.current.contains(event.target as Node) &&
      !tooltipRef.current.contains(event.target as Node)
    ) {
      setIsVisible(false)
    }
  }, [trigger])

  useEffect(() => {
    if (trigger === 'click') {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [trigger, handleClickOutside])

  return (
    <>
      <div
        ref={triggerRef}
        className={`inline-flex items-center ${className}`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
      >
        {children || (
          <button
            type="button"
            className="text-muted-foreground hover:text-card-foreground transition-colors"
            data-tooltip
          >
            {icon === 'info' ? (
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            ) : (
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" strokeWidth={2}></circle>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 17h.01"></path>
              </svg>
            )}
          </button>
        )}
      </div>

      {/* Tooltip */}
      {isVisible && (
        <div
          ref={tooltipRef}
          className="fixed z-50 tooltip animate-fade-in"
          style={{
            top: tooltipPosition.top,
            left: tooltipPosition.left
          }}
        >
          <div className="bg-popover border border-border text-popover-foreground text-sm rounded-lg shadow-premium max-w-xs">
            <div className="p-3">
              {title && (
                <div className="font-semibold mb-1 text-card-foreground">{title}</div>
              )}
              <div className="text-muted-foreground">{content}</div>
            </div>

            {/* Arrow */}
            <div
              className={`absolute w-2 h-2 bg-popover border-l border-t border-border transform rotate-45 ${
                position === 'top' ? 'bottom-[-4px] left-1/2 -translate-x-1/2' :
                position === 'bottom' ? 'top-[-4px] left-1/2 -translate-x-1/2' :
                position === 'left' ? 'right-[-4px] top-1/2 -translate-y-1/2' :
                'left-[-4px] top-1/2 -translate-y-1/2'
              }`}
            />
          </div>
        </div>
      )}
    </>
  )
}

// Predefined help tooltips for common use cases
export const SearchHelpTooltip = () => (
  <HelpTooltip
    title="Address Search"
    content="Enter any U.S. address to get compliance requirements. Type slowly for better autocomplete results and include city and state for accuracy."
    position="bottom"
  />
)

export const ConfidenceScoreTooltip = () => (
  <HelpTooltip
    title="Confidence Score"
    content="This score indicates how certain our AI is about the requirements. Higher scores (80-100) mean more reliable results. Lower scores may require additional verification."
    position="top"
  />
)

export const SetbackTooltip = () => (
  <HelpTooltip
    title="Setback Requirements"
    content="Setbacks are the minimum distances required between your structure and property lines. These vary by zoning and project type."
    position="top"
  />
)

export const HeightLimitTooltip = () => (
  <HelpTooltip
    title="Height Limits"
    content="Maximum allowed height for structures in this area. May vary by zoning district and structure type."
    position="top"
  />
)

export const GeneralHelpTooltip = ({ title, content }: { title?: string; content: string }) => (
  <HelpTooltip
    title={title || "Help"}
    content={content}
    position="top"
  />
)
