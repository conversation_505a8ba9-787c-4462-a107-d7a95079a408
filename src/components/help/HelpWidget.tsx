'use client'

import { useState } from 'react'
import Link from 'next/link'
import { X, MessageCircle, Book, Bug, Lightbulb, ExternalLink, Keyboard, HelpCircle } from 'lucide-react'

interface HelpWidgetProps {
  className?: string
}

export default function HelpWidget({ className = '' }: HelpWidgetProps) {
  const [isOpen, setIsOpen] = useState(false)

  const helpOptions = [
    {
      title: 'FAQ',
      description: 'Find answers to common questions',
      icon: HelpCircle,
      href: '/faq',
      color: 'primary'
    },
    {
      title: 'Contact Support',
      description: 'Get help from our team',
      icon: MessageCircle,
      href: '/contact',
      color: 'success'
    },
    {
      title: 'Send Feedback',
      description: 'Share your thoughts',
      icon: Lightbulb,
      href: '/feedback',
      color: 'warning'
    },
    {
      title: 'Report Bug',
      description: 'Report an issue',
      icon: Bug,
      href: '/report-bug',
      color: 'destructive'
    }
  ]

  const showKeyboardShortcuts = () => {
    // Trigger the keyboard shortcuts modal
    document.dispatchEvent(new KeyboardEvent('keydown', { key: '?', shiftKey: true }))
    setIsOpen(false)
  }

  const getColorClasses = (color: string) => {
    const colors = {
      primary: 'bg-primary/10 text-primary hover:bg-primary/20',
      success: 'bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400',
      info: 'bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400',
      warning: 'bg-orange-500/10 text-orange-600 hover:bg-orange-500/20 dark:text-orange-400',
      destructive: 'bg-destructive/10 text-destructive hover:bg-destructive/20',
      secondary: 'bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 dark:text-purple-400'
    }
    return colors[color as keyof typeof colors] || colors.primary
  }

  return (
    <>
      {/* Help Button */}
      <div className={`fixed bottom-6 right-6 z-40 ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-center w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-premium hover:bg-primary/90 transition-all duration-200 hover:scale-105 help-widget animate-fade-in"
          aria-label={isOpen ? "Close help menu" : "Open help menu"}
          aria-expanded={isOpen}
          aria-haspopup="true"
          id="help-button"
          type="button"
        >
          {isOpen ? (
            <X className="h-6 w-6" aria-hidden="true" />
          ) : (
            <HelpCircle className="h-6 w-6" aria-hidden="true" />
          )}
        </button>
      </div>

      {/* Help Panel */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 z-40 max-w-[calc(100vw-3rem)] animate-slide-up">
          <div className="bg-card border border-border rounded-2xl shadow-premium w-80 max-h-[70vh] overflow-y-auto help-widget">
            {/* Header */}
            <div className="p-6 border-b border-border bg-muted/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="bg-primary/10 rounded-full w-8 h-8 flex items-center justify-center mr-3">
                    <HelpCircle className="h-4 w-4 text-primary" />
                  </div>
                  <h3 className="font-semibold text-card-foreground">How can we help?</h3>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="flex items-center justify-center w-8 h-8 text-muted-foreground hover:text-card-foreground hover:bg-muted/50 rounded-full transition-colors"
                  aria-label="Close help menu"
                  type="button"
                >
                  <X className="h-4 w-4" aria-hidden="true" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="space-y-2">
                {helpOptions.map((option, index) => (
                  <Link
                    key={option.title}
                    href={option.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => setIsOpen(false)}
                    className="flex items-center p-3 rounded-xl hover:bg-muted/30 transition-all duration-200 group animate-slide-up"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <div className={`p-2 rounded-lg mr-3 transition-colors ${getColorClasses(option.color)}`}>
                      <option.icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-card-foreground text-sm">
                        {option.title}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {option.description}
                      </div>
                    </div>
                    <ExternalLink className="h-3 w-3 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                ))}
              </div>

              {/* Keyboard Shortcuts */}
              <div className="mt-6 pt-4 border-t border-border">
                <button
                  onClick={showKeyboardShortcuts}
                  className="flex items-center w-full p-3 rounded-xl hover:bg-muted/30 transition-colors group"
                >
                  <div className="p-2 rounded-lg mr-3 bg-muted/50 text-muted-foreground group-hover:bg-muted transition-colors">
                    <Keyboard className="h-4 w-4" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-medium text-card-foreground text-sm">
                      Keyboard Shortcuts
                    </div>
                    <div className="text-xs text-muted-foreground">
                      View all shortcuts (Shift + ?)
                    </div>
                  </div>
                </button>
              </div>

              {/* Quick Actions */}
              <div className="mt-6 pt-4 border-t border-border">
                <div className="text-xs text-muted-foreground mb-3 font-medium">Quick Actions</div>
                <div className="flex space-x-2">
                  <Link
                    href="/search"
                    onClick={() => setIsOpen(false)}
                    className="flex-1 px-3 py-2 text-xs bg-primary/10 text-primary rounded-lg hover:bg-primary/20 transition-colors text-center font-medium"
                  >
                    Start Search
                  </Link>
                  <Link
                    href="/account"
                    onClick={() => setIsOpen(false)}
                    className="flex-1 px-3 py-2 text-xs bg-muted/50 text-muted-foreground rounded-lg hover:bg-muted transition-colors text-center font-medium"
                  >
                    Account
                  </Link>
                </div>
              </div>

              {/* Contact */}
              <div className="mt-6 pt-4 border-t border-border text-center">
                <div className="text-xs text-muted-foreground mb-3 font-medium">
                  Still need help?
                </div>
                <Link
                  href="/contact"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => setIsOpen(false)}
                  className="inline-flex items-center text-xs text-primary hover:text-primary/80 font-medium transition-colors"
                >
                  Contact Support
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/20 backdrop-blur-sm"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  )
}
