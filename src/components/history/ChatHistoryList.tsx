'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { MessageCircle, Calendar, ArrowRight, Trash2, Loader2, Bo<PERSON>, User } from 'lucide-react'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  created_at: string
}

interface ChatConversation {
  id: string
  address?: string
  rule_type?: string
  jurisdiction_name?: string
  created_at: string
  updated_at: string
  message_count: number
  last_message?: ChatMessage
  preview_messages: ChatMessage[]
}

export function ChatHistoryList() {
  const [conversations, setConversations] = useState<ChatConversation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    fetchChatHistory()
  }, [])

  const fetchChatHistory = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/history/conversations')

      if (!response.ok) {
        throw new Error('Failed to fetch chat history')
      }

      const data = await response.json()
      setConversations(data.conversations || [])
    } catch (err) {
      console.error('Error fetching chat history:', err)
      setError('Failed to load chat history')
    } finally {
      setLoading(false)
    }
  }

  const handleContinueConversation = async (conversation: ChatConversation) => {
    // Navigate to search page with conversation context
    if (conversation.address && conversation.rule_type) {
      try {
        // First try to get coordinates from the address
        const coordinates = await geocodeAddress(conversation.address)

        const params = new URLSearchParams({
          address: conversation.address,
          ruleType: conversation.rule_type,
          conversationId: conversation.id
        })

        // Add coordinates if we got them
        if (coordinates) {
          params.set('lat', coordinates.lat.toString())
          params.set('lng', coordinates.lng.toString())
          params.set('autoSearch', 'true') // Auto-trigger search to restore context
        }

        router.push(`/search?${params.toString()}`)
      } catch (error) {
        console.error('Error geocoding address:', error)
        // Fallback: navigate without coordinates
        const params = new URLSearchParams({
          address: conversation.address,
          ruleType: conversation.rule_type,
          conversationId: conversation.id
        })
        router.push(`/search?${params.toString()}`)
      }
    } else {
      // If no context, just open a new search with the conversation ID
      router.push(`/search?conversationId=${conversation.id}`)
    }
  }

  // Geocoding function to get coordinates from address
  const geocodeAddress = async (address: string) => {
    try {
      const response = await fetch(`/api/address/geocode?address=${encodeURIComponent(address)}`)
      if (!response.ok) {
        throw new Error('Geocoding failed')
      }
      const data = await response.json()
      return data.coordinates
    } catch (error) {
      console.error('Geocoding error:', error)
      return null
    }
  }

  const handleDeleteConversation = async (conversationId: string) => {
    if (!confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      return
    }

    try {
      setDeletingId(conversationId)
      const response = await fetch(`/api/history/conversations/${conversationId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete conversation')
      }

      // Remove from local state
      setConversations(prev => prev.filter(conv => conv.id !== conversationId))
    } catch (err) {
      console.error('Error deleting conversation:', err)
      alert('Failed to delete conversation. Please try again.')
    } finally {
      setDeletingId(null)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <span className="text-muted-foreground">Loading chat history...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-destructive/10 border border-destructive/20 rounded-2xl p-8 text-center">
        <p className="text-destructive mb-4">{error}</p>
        <Button onClick={fetchChatHistory} variant="outline" className="transition-all duration-200 hover:scale-[1.02]">
          Try Again
        </Button>
      </div>
    )
  }

  if (conversations.length === 0) {
    return (
      <div className="bg-muted/30 border border-border rounded-2xl p-12 text-center">
        <div className="bg-primary/10 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
          <MessageCircle className="h-10 w-10 text-primary" />
        </div>
        <h3 className="text-xl font-semibold text-card-foreground mb-3">No conversations yet</h3>
        <p className="text-muted-foreground mb-4 max-w-md mx-auto">
          Your AI compliance assistant conversations will appear here.
        </p>

        <div className="mt-4 text-gray-400 italic space-y-1 text-sm mb-6">
          <p>Example prompts:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>"Do I need a permit for a fence in Lansing, MI?"</li>
            <li>"What are setback requirements for an ADU in Seattle?"</li>
            <li>"Can I store a camper in my driveway in Austin, TX?"</li>
          </ul>
        </div>

        <Button
          onClick={() => router.push('/search')}
          className="animate-pulse-once transition-all duration-200 hover:scale-[1.02]"
        >
          Start a Conversation
        </Button>

        <p className="text-sm text-gray-400 mt-4">
          Ask any compliance question—start by performing a search above.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {conversations.map((conversation, index) => (
        <div
          key={conversation.id}
          className="bg-card border border-border rounded-2xl p-6 shadow-premium hover:shadow-premium-lg transition-all duration-300 animate-slide-up"
          style={{ animationDelay: `${index * 50}ms` }}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {/* Conversation Header */}
              <div className="flex items-center space-x-3 mb-3">
                <div className="bg-primary/10 rounded-full p-2">
                  <MessageCircle className="h-4 w-4 text-primary" />
                </div>
                <h3 className="font-semibold text-card-foreground text-lg">
                  {conversation.address ? (
                    `${conversation.rule_type} at ${conversation.address}`
                  ) : (
                    'AI Compliance Conversation'
                  )}
                </h3>
              </div>

              {/* Conversation Meta */}
              <div className="flex items-center space-x-6 text-sm text-muted-foreground mb-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="font-medium">{conversation.message_count} messages</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(conversation.updated_at)}</span>
                </div>
                {conversation.jurisdiction_name && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span className="text-success font-medium">{conversation.jurisdiction_name}</span>
                  </div>
                )}
              </div>

              {/* Message Preview */}
              {conversation.preview_messages && conversation.preview_messages.length > 0 && (
                <div className="space-y-3 mb-4">
                  {conversation.preview_messages.slice(0, 2).map((message, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {message.role === 'user' ? (
                          <div className="bg-muted/50 rounded-full p-1">
                            <User className="h-3 w-3 text-muted-foreground" />
                          </div>
                        ) : (
                          <div className="bg-primary/10 rounded-full p-1">
                            <Bot className="h-3 w-3 text-primary" />
                          </div>
                        )}
                      </div>
                      <div className="bg-muted/30 border border-border rounded-lg p-3 flex-1">
                        <p className="text-sm text-card-foreground">
                          {truncateText(message.content)}
                        </p>
                      </div>
                    </div>
                  ))}
                  {conversation.message_count > 2 && (
                    <div className="flex items-center space-x-2 ml-6">
                      <div className="w-1 h-1 bg-muted-foreground rounded-full"></div>
                      <p className="text-xs text-muted-foreground">
                        +{conversation.message_count - 2} more messages
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-3 ml-6">
              <Button
                onClick={() => handleContinueConversation(conversation)}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2 transition-all duration-200 hover:scale-[1.02]"
              >
                <span>Continue</span>
                <ArrowRight className="h-4 w-4" />
              </Button>

              <Button
                onClick={() => handleDeleteConversation(conversation.id)}
                variant="ghost"
                size="sm"
                disabled={deletingId === conversation.id}
                className="text-destructive hover:text-destructive hover:bg-destructive/10 transition-all duration-200 hover:scale-[1.02]"
              >
                {deletingId === conversation.id ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>
      ))}

      {conversations.length > 0 && (
        <div className="text-center pt-6">
          <div className="bg-muted/20 rounded-lg p-4">
            <p className="text-sm text-muted-foreground">
              Showing {conversations.length} conversation{conversations.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
