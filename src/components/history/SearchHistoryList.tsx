'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { MapPin, Calendar, RotateCcw, Trash2, Loader2, <PERSON>fresh<PERSON><PERSON>, Tag, Clock } from 'lucide-react'

interface SearchHistoryItem {
  id: string
  address: string
  project_type: string
  created_at: string
  search_params?: {
    lat?: number
    lng?: number
    address?: string
  }
  metadata?: {
    lat?: number
    lng?: number
    originalDescription?: string
  }
}

export function SearchHistoryList() {
  const [searches, setSearches] = useState<SearchHistoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    fetchSearchHistory()
  }, [])

  const fetchSearchHistory = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/history/searches')

      if (!response.ok) {
        throw new Error('Failed to fetch search history')
      }

      const data = await response.json()
      setSearches(data.searches || [])
    } catch (err) {
      console.error('Error fetching search history:', err)
      setError('Failed to load search history')
    } finally {
      setLoading(false)
    }
  }

  const handleRerunSearch = (search: SearchHistoryItem) => {
    // Build URL parameters for the search
    const params = new URLSearchParams({
      address: search.address,
      ruleType: search.project_type,
      autoSearch: 'true' // Auto-trigger the search when page loads
    })

    // Add coordinates if available - check both search_params and metadata
    const lat = search.search_params?.lat || search.metadata?.lat
    const lng = search.search_params?.lng || search.metadata?.lng

    if (lat && lng) {
      params.set('lat', lat.toString())
      params.set('lng', lng.toString())
    }

    // Navigate to search page with parameters
    router.push(`/search?${params.toString()}`)
  }

  const handleDeleteSearch = async (searchId: string) => {
    if (!confirm('Are you sure you want to delete this search from your history?')) {
      return
    }

    try {
      setDeletingId(searchId)
      const response = await fetch(`/api/history/searches/${searchId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete search')
      }

      // Remove from local state
      setSearches(prev => prev.filter(search => search.id !== searchId))
    } catch (err) {
      console.error('Error deleting search:', err)
      alert('Failed to delete search. Please try again.')
    } finally {
      setDeletingId(null)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <span className="text-muted-foreground">Loading search history...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-destructive/10 border border-destructive/20 rounded-2xl p-8 text-center">
        <p className="text-destructive mb-4">{error}</p>
        <Button onClick={fetchSearchHistory} variant="outline" className="transition-all duration-200 hover:scale-[1.02]">
          Try Again
        </Button>
      </div>
    )
  }

  if (searches.length === 0) {
    return (
      <div className="bg-muted/30 border border-border rounded-2xl p-12 text-center">
        <div className="bg-primary/10 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
          <MapPin className="h-10 w-10 text-primary" />
        </div>
        <h3 className="text-xl font-semibold text-card-foreground mb-3">No address lookups yet</h3>
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
          Run a property search above to see your history.
        </p>
        <Button
          onClick={() => router.push('/search')}
          className="transition-all duration-200 hover:scale-[1.02]"
        >
          Go to Search
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {searches.map((search, index) => (
        <div
          key={search.id}
          className="bg-gray-800 p-4 rounded-lg mb-4 shadow-premium hover:shadow-premium-lg transition-all duration-300 animate-slide-up"
          style={{ animationDelay: `${index * 50}ms` }}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <div className="bg-primary/10 rounded-full p-2">
                  <MapPin className="h-4 w-4 text-primary" />
                </div>
                <h3 className="font-semibold text-card-foreground text-lg truncate-two-lines">{search.address}</h3>
              </div>

              <div className="flex items-center space-x-2 mt-1 mb-2">
                <Tag className="w-4 h-4 text-green-400" />
                <span className="text-xs text-gray-400 capitalize">{search.project_type}</span>
              </div>

              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-xs text-gray-400">{formatDate(search.created_at)}</span>
              </div>

              {search.metadata?.originalDescription && (
                <div className="bg-muted/30 border border-border rounded-lg p-3 mt-4">
                  <p className="text-sm text-card-foreground italic">
                    &ldquo;{search.metadata.originalDescription}&rdquo;
                  </p>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-3 ml-6">
              <Button
                onClick={() => handleRerunSearch(search)}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2 transition-all duration-200 hover:scale-[1.02]"
                title="Re-run this search"
                aria-label="Re-run this search"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>

              <Button
                onClick={() => handleDeleteSearch(search.id)}
                variant="ghost"
                size="sm"
                disabled={deletingId === search.id}
                className="text-destructive hover:text-destructive hover:bg-destructive/10 transition-all duration-200 hover:scale-[1.02]"
                title="Delete search from history"
                aria-label="Delete search from history"
              >
                {deletingId === search.id ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>
      ))}

      {searches.length > 0 && (
        <div className="text-center pt-6">
          <div className="bg-muted/20 rounded-lg p-4">
            <p className="text-sm text-muted-foreground">
              Showing {searches.length} search{searches.length !== 1 ? 'es' : ''}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
