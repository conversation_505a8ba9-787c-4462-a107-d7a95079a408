'use client'

import { usePathname } from 'next/navigation'
import { Head<PERSON> } from "@/components/navigation/Header"
import { Footer } from "@/components/navigation/Footer"

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()
  
  // Routes that should not have the main header/footer
  const excludedRoutes = ['/checkout', '/chat']

  // Check if current path starts with any excluded route
  const isExcludedRoute = excludedRoutes.some(route => pathname.startsWith(route))
  
  if (isExcludedRoute) {
    // Render children without header/footer for checkout pages
    return (
      <main id="main-content" className="flex-1" role="main">
        {children}
      </main>
    )
  }
  
  // Render with header/footer for all other pages
  return (
    <>
      <Header />
      <main id="main-content" className="flex-1" role="main">
        {children}
      </main>
      <Footer />
    </>
  )
}
