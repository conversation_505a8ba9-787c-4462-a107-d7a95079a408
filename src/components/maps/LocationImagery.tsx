'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MiniMap } from './MiniMap'
import { StreetView } from './StreetView'
import { Map, Camera } from 'lucide-react'

interface LocationImageryProps {
  lat: number
  lng: number
  address?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  defaultView?: 'map' | 'streetview'
  showToggle?: boolean
}

export function LocationImagery({
  lat,
  lng,
  address,
  className = "",
  size = 'md',
  defaultView = 'map',
  showToggle = true
}: LocationImageryProps) {
  const [currentView, setCurrentView] = useState<'map' | 'streetview'>(defaultView)

  const toggleView = () => {
    setCurrentView(prev => prev === 'map' ? 'streetview' : 'map')
  }

  return (
    <div className={`relative ${className}`}>
      <AnimatePresence mode="wait">
        {currentView === 'map' ? (
          <motion.div
            key="map"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <MiniMap
              lat={lat}
              lng={lng}
              address={address}
              size={size}
              showMarker={true}
            />
          </motion.div>
        ) : (
          <motion.div
            key="streetview"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <StreetView
              lat={lat}
              lng={lng}
              address={address}
              size={size}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* View toggle button */}
      {showToggle && (
        <motion.button
          className="absolute top-2 right-2 bg-white/90 hover:bg-white rounded-full p-2 shadow-sm transition-colors duration-200"
          onClick={toggleView}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          title={currentView === 'map' ? 'Switch to Street View' : 'Switch to Map'}
        >
          {currentView === 'map' ? (
            <Camera className="h-4 w-4 text-gray-700" />
          ) : (
            <Map className="h-4 w-4 text-gray-700" />
          )}
        </motion.button>
      )}

      {/* View indicator */}
      <div className="absolute bottom-2 right-2">
        <div className="bg-black/60 text-white text-xs px-2 py-1 rounded-full">
          {currentView === 'map' ? 'Map' : 'Street View'}
        </div>
      </div>
    </div>
  )
}
