'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { MapPin, ExternalLink } from 'lucide-react'
import Image from 'next/image'

interface MiniMapProps {
  lat: number
  lng: number
  address?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showMarker?: boolean
  onClick?: () => void
}

const sizeConfig = {
  sm: { width: 150, height: 100, zoom: 15 },
  md: { width: 200, height: 120, zoom: 16 },
  lg: { width: 300, height: 180, zoom: 17 }
}

export function MiniMap({
  lat,
  lng,
  address,
  className = "",
  size = 'md',
  showMarker = true,
  onClick
}: MiniMapProps) {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const config = sizeConfig[size]

  // Use environment variable for Google Maps API key
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ||
                 process.env.NEXT_PUBLIC_GOOGLE_SEARCH_API_KEY ||
                 process.env.NEXT_PUBLIC_GOOGLE_GEOCODING_API_KEY

  if (!apiKey) {
    return (
      <div
        className={`bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center ${className}`}
        style={{ width: config.width, height: config.height }}
      >
        <div className="text-center text-gray-500 text-sm">
          <MapPin className="h-6 w-6 mx-auto mb-1" />
          <div>Map unavailable</div>
        </div>
      </div>
    )
  }

  // Construct Google Static Maps URL
  const mapUrl = new URL('https://maps.googleapis.com/maps/api/staticmap')
  mapUrl.searchParams.set('center', `${lat},${lng}`)
  mapUrl.searchParams.set('zoom', config.zoom.toString())
  mapUrl.searchParams.set('size', `${config.width}x${config.height}`)
  mapUrl.searchParams.set('scale', '2') // High DPI
  mapUrl.searchParams.set('format', 'png')
  mapUrl.searchParams.set('maptype', 'roadmap')
  mapUrl.searchParams.set('key', apiKey)

  if (showMarker) {
    mapUrl.searchParams.set('markers', `color:red|${lat},${lng}`)
  }

  // Style the map with custom styling
  const mapStyle = [
    'feature:poi|visibility:off',
    'feature:transit|visibility:off',
    'feature:road.local|visibility:simplified'
  ].join('|')
  mapUrl.searchParams.set('style', mapStyle)

  const handleMapClick = () => {
    if (onClick) {
      onClick()
    } else {
      // Default: open in Google Maps
      const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`
      window.open(googleMapsUrl, '_blank')
    }
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
  }

  if (imageError) {
    return (
      <div
        className={`bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center ${className}`}
        style={{ width: config.width, height: config.height }}
      >
        <div className="text-center text-gray-500 text-sm">
          <MapPin className="h-6 w-6 mx-auto mb-1" />
          <div>Map unavailable</div>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      className={`relative rounded-lg overflow-hidden border border-gray-200 cursor-pointer group ${className}`}
      style={{ width: config.width, height: config.height }}
      onClick={handleMapClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
    >
      {/* Loading placeholder */}
      {!imageLoaded && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center">
          <MapPin className="h-6 w-6 text-gray-400" />
        </div>
      )}

      {/* Map image */}
      <Image
        src={mapUrl.toString()}
        alt={address ? `Map of ${address}` : 'Location map'}
        width={config.width}
        height={config.height}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        loading="lazy"
        unoptimized={true} // Google Maps images are already optimized
      />

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
        <motion.div
          className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          initial={{ scale: 0.8 }}
          whileHover={{ scale: 1 }}
        >
          <div className="bg-white rounded-full p-2 shadow-lg">
            <ExternalLink className="h-4 w-4 text-gray-700" />
          </div>
        </motion.div>
      </div>

      {/* Address label */}
      {address && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
          <div className="text-white text-xs font-medium truncate">
            {address}
          </div>
        </div>
      )}
    </motion.div>
  )
}
