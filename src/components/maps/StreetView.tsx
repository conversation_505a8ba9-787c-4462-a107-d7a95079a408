'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Camera, ExternalLink } from 'lucide-react'
import Image from 'next/image'

interface StreetViewProps {
  lat: number
  lng: number
  address?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
  heading?: number
  pitch?: number
  fov?: number
}

const sizeConfig = {
  sm: { width: 150, height: 100 },
  md: { width: 200, height: 120 },
  lg: { width: 300, height: 180 }
}

export function StreetView({
  lat,
  lng,
  address,
  className = "",
  size = 'md',
  heading = 0,
  pitch = 0,
  fov = 90
}: StreetViewProps) {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const config = sizeConfig[size]

  // Use environment variable for Google Maps API key
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ||
                 process.env.NEXT_PUBLIC_GOOGLE_SEARCH_API_KEY ||
                 process.env.NEXT_PUBLIC_GOOGLE_GEOCODING_API_KEY

  if (!apiKey) {
    return (
      <div
        className={`bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center ${className}`}
        style={{ width: config.width, height: config.height }}
      >
        <div className="text-center text-gray-500 text-sm">
          <Camera className="h-6 w-6 mx-auto mb-1" />
          <div>Street View unavailable</div>
        </div>
      </div>
    )
  }

  // Construct Google Street View Static API URL
  const streetViewUrl = new URL('https://maps.googleapis.com/maps/api/streetview')
  streetViewUrl.searchParams.set('location', `${lat},${lng}`)
  streetViewUrl.searchParams.set('size', `${config.width}x${config.height}`)
  streetViewUrl.searchParams.set('heading', heading.toString())
  streetViewUrl.searchParams.set('pitch', pitch.toString())
  streetViewUrl.searchParams.set('fov', fov.toString())
  streetViewUrl.searchParams.set('key', apiKey)

  const handleStreetViewClick = () => {
    // Open in Google Street View
    const googleStreetViewUrl = `https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${lat},${lng}&heading=${heading}&pitch=${pitch}&fov=${fov}`
    window.open(googleStreetViewUrl, '_blank')
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
  }

  if (imageError) {
    return (
      <div
        className={`bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center ${className}`}
        style={{ width: config.width, height: config.height }}
      >
        <div className="text-center text-gray-500 text-sm">
          <Camera className="h-6 w-6 mx-auto mb-1" />
          <div>Street View unavailable</div>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      className={`relative rounded-lg overflow-hidden border border-gray-200 cursor-pointer group ${className}`}
      style={{ width: config.width, height: config.height }}
      onClick={handleStreetViewClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
    >
      {/* Loading placeholder */}
      {!imageLoaded && (
        <div className="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center">
          <Camera className="h-6 w-6 text-gray-400" />
        </div>
      )}

      {/* Street View image */}
      <Image
        src={streetViewUrl.toString()}
        alt={address ? `Street view of ${address}` : 'Street view'}
        width={config.width}
        height={config.height}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        loading="lazy"
        unoptimized={true} // Google Street View images are already optimized
      />

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
        <motion.div
          className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          initial={{ scale: 0.8 }}
          whileHover={{ scale: 1 }}
        >
          <div className="bg-white rounded-full p-2 shadow-lg">
            <ExternalLink className="h-4 w-4 text-gray-700" />
          </div>
        </motion.div>
      </div>

      {/* Street View indicator */}
      <div className="absolute top-2 left-2">
        <div className="bg-white/90 rounded-full p-1 shadow-sm">
          <Camera className="h-3 w-3 text-gray-700" />
        </div>
      </div>

      {/* Address label */}
      {address && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
          <div className="text-white text-xs font-medium truncate">
            {address}
          </div>
        </div>
      )}
    </motion.div>
  )
}
