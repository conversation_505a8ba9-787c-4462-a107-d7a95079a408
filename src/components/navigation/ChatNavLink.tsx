'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { MessageCircle } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import type { SubscriptionTier } from '@/lib/tier-config'

interface UserProfile {
  subscription_tier: SubscriptionTier
}

interface ChatNavLinkProps {
  className?: string
  onClick?: () => void
}

export function ChatNavLink({ className, onClick }: ChatNavLinkProps) {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const pathname = usePathname()
  const supabase = createClient()

  useEffect(() => {
    let mounted = true

    const getUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (mounted) {
          setUser(user)
          
          if (user) {
            // Fetch user profile
            const { data: profile } = await supabase
              .from('profiles')
              .select('subscription_tier')
              .eq('id', user.id)
              .single()
            
            if (mounted) {
              setUserProfile(profile)
            }
          }
          setLoading(false)
        }
      } catch (error) {
        console.error('ChatNavLink: Error fetching user:', error)
        if (mounted) {
          setLoading(false)
        }
      }
    }

    getUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (mounted) {
          const newUser = session?.user || null
          setUser(newUser)
          
          if (newUser) {
            try {
              const { data: profile } = await supabase
                .from('profiles')
                .select('subscription_tier')
                .eq('id', newUser.id)
                .single()
              
              if (mounted) {
                setUserProfile(profile)
              }
            } catch (error) {
              console.error('ChatNavLink: Error fetching profile on auth change:', error)
            }
          } else {
            setUserProfile(null)
          }
          setLoading(false)
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [supabase.auth])

  // Show chat link for everyone, but handle authentication/access in the link behavior
  const userTier = userProfile?.subscription_tier || 'trial'
  const hasAccess = user && userProfile && hasFeatureAccess(userTier, 'enableChat') && isFeatureEnabled('CHAT_ENABLED')

  // Determine the href and behavior based on user state
  const getHref = () => {
    if (!user) {
      // No user - redirect to signup
      return '/signup'
    }
    if (!hasAccess) {
      // User exists but no access - redirect to pricing or login
      return '/pricing'
    }
    // User has access - go to chat
    return '/chat'
  }

  const isActive = pathname.startsWith('/chat')
  const href = getHref()

  return (
    <Link
      href={href}
      className={className || `px-4 py-2 text-sm font-medium rounded-md border transition-all duration-200 hover:scale-105 focus:scale-105 interactive navigation-link hoverable flex items-center space-x-1 ${
        isActive
          ? 'bg-primary text-primary-foreground border-primary shadow-sm'
          : 'bg-background text-muted-foreground border-border hover:bg-accent hover:text-accent-foreground hover:border-accent'
      }`}
      aria-current={isActive ? 'page' : undefined}
      data-testid="chat-navigation-link"
      title="Chat"
      onClick={onClick}
    >
      <MessageCircle className="h-4 w-4" />
      <span>Chat</span>
    </Link>
  )
}
