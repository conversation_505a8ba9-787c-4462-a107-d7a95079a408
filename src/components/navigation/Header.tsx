'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname, useRouter } from 'next/navigation'
import { Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { UserMenu } from './UserMenu'
import { ThemeToggle } from '@/components/ui/ThemeToggle'

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()

  // Navigation handler for chat page compatibility
  const handleNavigation = (href: string) => {
    if (pathname === '/chat') {
      // Clean up chat page body class before navigation
      document.body.classList.remove('chat-page-body')
      // Small delay to ensure cleanup, then navigate using router to preserve auth state
      setTimeout(() => {
        router.push(href)
      }, 50)
    } else {
      router.push(href)
    }
  }

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Pricing', href: '/pricing' },
    { name: 'Chat', href: '/chat' },
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-3">
        <div className="grid grid-cols-3 h-16 items-center">
          {/* Logo - Left Column */}
          <div className="flex justify-start">
            <Link
              href="/"
              className="flex items-center space-x-3 transition-transform duration-200 hover:scale-105 focus:scale-105"
              aria-label="Ordrly home page"
            >
              <Image
                src="/logo.svg"
                alt="Ordrly Logo"
                width={40}
                height={40}
                className="w-10 h-10"
              />
              <div className="text-2xl font-bold text-primary">Ordrly</div>
            </Link>
          </div>

          {/* Desktop Navigation - Center Column */}
          <nav className="hidden lg:flex items-center justify-center space-x-6">
            {navigation.map((item) => (
              <Button
                key={item.name}
                variant="ghost"
                size="sm"
                onClick={() => handleNavigation(item.href)}
                className={`text-sm font-medium transition-colors duration-200 cursor-pointer px-3 ${
                  isActive(item.href)
                    ? 'text-primary'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
                aria-current={isActive(item.href) ? 'page' : undefined}
                data-testid="navigation-link"
              >
                {item.name}
              </Button>
            ))}
          </nav>

          {/* Desktop CTA - Right Column */}
          <div className="hidden lg:flex items-center justify-end space-x-4">
            <UserMenu />
            <ThemeToggle />
          </div>

          {/* Mobile menu button - show on mobile and tablet */}
          <div className="lg:hidden flex items-center justify-end space-x-2">
            <div className="md:flex hidden items-center space-x-2">
              <UserMenu />
              <ThemeToggle />
            </div>
            <button
              className="p-3 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 min-h-[48px] min-w-[48px] flex items-center justify-center border border-border/20 bg-background/80 backdrop-blur-sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
              aria-label={isMenuOpen ? 'Close navigation menu' : 'Open navigation menu'}
              type="button"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" aria-hidden="true" />
              ) : (
                <Menu className="h-5 w-5" aria-hidden="true" />
              )}
              <span className="sr-only">
                {isMenuOpen ? 'Close navigation menu' : 'Open navigation menu'}
              </span>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div
            id="mobile-menu"
            className="lg:hidden border-t border-border bg-background/95 backdrop-blur-md animate-slide-in-top mobile-menu shadow-lg"
            role="navigation"
            aria-label="Mobile navigation"
          >
            <div className="px-4 pt-4 pb-6 space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-4 py-3 text-base font-medium transition-all duration-200 rounded-lg ${
                    isActive(item.href)
                      ? 'text-primary bg-primary/10 border border-primary/20'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                  aria-current={isActive(item.href) ? 'page' : undefined}
                >
                  {item.name}
                </Link>
              ))}

              <div className="px-4 py-2 flex items-center justify-between border-t border-border/50 mt-4 pt-4">
                <UserMenu />
                <ThemeToggle />
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
