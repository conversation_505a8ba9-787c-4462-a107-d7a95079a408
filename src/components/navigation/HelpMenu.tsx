'use client'

import { useState } from 'react'
import Link from 'next/link'
import { HelpCircle, ChevronDown, BookOpen, Mail, RotateCcw } from 'lucide-react'
import { resetTutorial } from '@/lib/onboarding'

export function HelpMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const [isResettingTutorial, setIsResettingTutorial] = useState(false)

  const handleReplayTutorial = async () => {
    setIsResettingTutorial(true)
    try {
      const success = await resetTutorial()
      if (success) {
        // Reload the page to trigger the tutorial
        window.location.reload()
      } else {
        alert('Failed to reset tutorial. Please try again.')
      }
    } catch (error) {
      console.error('Error resetting tutorial:', error)
      alert('Failed to reset tutorial. Please try again.')
    } finally {
      setIsResettingTutorial(false)
    }
  }

  const helpItems = [
    {
      name: 'FAQ',
      href: '/faq',
      icon: BookOpen,
      description: 'Find answers to common questions'
    },
    {
      name: 'Contact Support',
      href: '/contact',
      icon: Mail,
      description: 'Get help from our team'
    },
    {
      name: 'Replay Tutorial',
      onClick: handleReplayTutorial,
      icon: RotateCcw,
      description: 'Watch the onboarding tutorial again',
      disabled: isResettingTutorial
    }
  ]

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <HelpCircle className="h-4 w-4" />
        <span>Help</span>
        <ChevronDown className={`h-3 w-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown Menu */}
          <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-gray-200 z-20">
            <div className="py-2">
              <div className="px-4 py-2 border-b border-gray-100">
                <h3 className="text-sm font-medium text-gray-900">Help & Support</h3>
                <p className="text-xs text-gray-600">Get help using Ordrly</p>
              </div>

              <div className="py-1">
                {helpItems.map((item) => {
                  const IconComponent = item.icon

                  if (item.href) {
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className="flex items-start px-4 py-3 text-sm text-card-foreground hover:bg-muted/30 transition-colors rounded-lg mx-2"
                        onClick={() => setIsOpen(false)}
                      >
                        <IconComponent className="h-4 w-4 mt-0.5 mr-3 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-xs text-muted-foreground">{item.description}</div>
                        </div>
                      </Link>
                    )
                  }

                  return (
                    <button
                      key={item.name}
                      onClick={() => {
                        if (item.onClick && !item.disabled) {
                          item.onClick()
                          setIsOpen(false)
                        }
                      }}
                      disabled={item.disabled}
                      className={`w-full flex items-start px-4 py-3 text-sm text-left transition-colors rounded-lg mx-2 ${
                        item.disabled
                          ? 'text-muted-foreground/50 cursor-not-allowed'
                          : 'text-card-foreground hover:bg-muted/30'
                      }`}
                    >
                      <IconComponent className={`h-4 w-4 mt-0.5 mr-3 ${
                        item.disabled ? 'text-muted-foreground/30' : 'text-muted-foreground'
                      } ${isResettingTutorial && item.name === 'Replay Tutorial' ? 'animate-spin' : ''}`} />
                      <div>
                        <div className="font-medium">
                          {item.name}
                          {isResettingTutorial && item.name === 'Replay Tutorial' && (
                            <span className="ml-1">...</span>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">{item.description}</div>
                      </div>
                    </button>
                  )
                })}
              </div>

              <div className="border-t border-gray-100 pt-2 pb-1">
                <div className="px-4 py-2">
                  <p className="text-xs text-gray-500">
                    Need more help? Email us at{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-blue-600 hover:text-blue-700"
                      onClick={() => setIsOpen(false)}
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
