'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'
import { User } from '@supabase/supabase-js'


interface UserProfile {
  subscription_tier: string
  pulls_this_month: number
  extra_credits: number
  trial_start_date?: string
  trial_end_date?: string
}

export function UserMenu() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createClient()

  const userTier = userProfile?.subscription_tier || 'free'

  // Calculate trial days remaining
  const getTrialDaysRemaining = () => {
    if (userTier !== 'trial' || !userProfile?.trial_end_date) return 0
    const now = new Date()
    const endDate = new Date(userProfile.trial_end_date)
    const msRemaining = endDate.getTime() - now.getTime()
    const daysRemaining = Math.ceil(msRemaining / (1000 * 60 * 60 * 24))
    return Math.max(0, daysRemaining)
  }

  const trialDaysRemaining = getTrialDaysRemaining()

  // Navigation handler - available for both logged in and logged out users
  const handleNavigation = (href: string) => {
    console.log('🔄 UserMenu navigation clicked:', href)
    if (pathname === '/chat') {
      console.log('🔄 Using router.push for chat page navigation to preserve auth state')
      // Clean up chat page body class before navigation
      document.body.classList.remove('chat-page-body')
      // Small delay to ensure cleanup, then navigate using router to preserve auth state
      setTimeout(() => {
        router.push(href)
      }, 50)
    } else {
      router.push(href)
    }
  }

  useEffect(() => {
    let mounted = true

    const getInitialUser = async () => {
      try {
        console.log('🔍 UserMenu: Starting auth check...')
        console.log('🔍 UserMenu: Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)

        // Add timeout to prevent hanging
        const authTimeout = setTimeout(() => {
          if (mounted) {
            console.error('⚠️ UserMenu: Auth check TIMEOUT after 3 seconds - forcing loading to false')
            setLoading(false)
          }
        }, 3000) // 3 second timeout

        console.log('🔍 UserMenu: About to call getSession...')

        // Force refresh session first to ensure we have latest auth state
        const sessionStart = Date.now()
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log(`🔍 UserMenu: getSession completed in ${Date.now() - sessionStart}ms`)

        if (sessionError) {
          console.error('❌ UserMenu: Session error:', sessionError)
        } else {
          console.log('✅ UserMenu: Session check:', session ? `Found for ${session.user?.email}` : 'None')
        }

        console.log('🔍 UserMenu: About to call getUser...')

        // Get user auth state - this should be fast
        const userStart = Date.now()
        const { data: { user }, error } = await supabase.auth.getUser()
        console.log(`🔍 UserMenu: getUser completed in ${Date.now() - userStart}ms`)

        if (error) {
          console.error('❌ UserMenu: getUser error:', error)
        }

        console.log('🔍 UserMenu: Auth check result:', user ? `✅ User: ${user.email}` : '❌ No user')

        // Clear timeout since we got a response
        clearTimeout(authTimeout)

        if (mounted) {
          console.log('🔍 UserMenu: Setting user state and loading to false')
          setUser(user)
          setLoading(false) // Set loading to false immediately after auth check

          // If user exists, fetch profile data asynchronously (non-blocking)
          if (user) {
            console.log('✅ UserMenu: User authenticated, fetching profile:', user.email)

            // Fetch profile in background - don't block UI rendering
            const fetchProfile = async () => {
              try {
                const { data: profile } = await supabase
                  .from('profiles')
                  .select('subscription_tier, pulls_this_month, extra_credits, trial_start_date, trial_end_date')
                  .eq('id', user.id)
                  .single()

                if (mounted) {
                  setUserProfile(profile)
                  console.log('✅ UserMenu: Profile loaded:', profile?.subscription_tier)
                }
              } catch (profileError) {
                console.error('UserMenu: Error fetching profile:', profileError)
                // Continue without profile data - user menu will still work
              }
            }

            fetchProfile()
          } else {
            console.log('❌ UserMenu: No user found - showing sign in/up buttons')
          }
        }
      } catch (error: unknown) {
        console.error('❌ UserMenu: CAUGHT ERROR in getInitialUser:', error)

        // Handle errors gracefully
        if (error && typeof error === 'object' && 'name' in error && error.name === 'AuthSessionMissingError') {
          console.log('ℹ️ UserMenu: No auth session found (user not logged in)')
        } else if (error instanceof Error) {
          console.error('❌ UserMenu: Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack
          })
        } else {
          console.error('❌ UserMenu: Unknown error type:', typeof error, error)
        }

        if (mounted) {
          console.log('🔧 UserMenu: Setting user to null and loading to false due to error')
          setUser(null)
          setLoading(false) // Always set loading to false, even on error
        }
      }
    }

    // Get initial user state
    console.log('🚀 UserMenu: Calling getInitialUser...')
    getInitialUser()

    // Fallback timeout to ensure loading never stays true forever
    const fallbackTimeout = setTimeout(() => {
      if (mounted) {
        console.error('🚨 UserMenu: FALLBACK TIMEOUT - forcing loading to false after 5 seconds')
        setLoading(false)
      }
    }, 5000)

    // Listen for auth state changes with immediate response
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 UserMenu: Auth state changed:', event, session?.user?.email || 'No user')

        if (mounted) {
          const newUser = session?.user ?? null

          // Only update state if this is a meaningful auth change
          // Ignore spurious auth state changes that don't actually change the user
          if (event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
            console.log('🔄 UserMenu: Processing meaningful auth state change:', event)

            setUser(newUser)
            setLoading(false) // Always set loading to false on auth state change

            // Clear fallback timeout since we got auth state
            clearTimeout(fallbackTimeout)

            // Fetch user profile if user exists
            if (newUser) {
              console.log('✅ UserMenu: Auth state change - user authenticated:', newUser.email)

              // Fetch profile asynchronously
              const fetchProfile = async () => {
                try {
                  const { data: profile } = await supabase
                    .from('profiles')
                    .select('subscription_tier, pulls_this_month, extra_credits, trial_start_date, trial_end_date')
                    .eq('id', newUser.id)
                    .single()

                  if (mounted) {
                    setUserProfile(profile)
                    console.log('✅ UserMenu: Profile updated via auth change:', profile?.subscription_tier)
                  }
                } catch (profileError) {
                  console.error('UserMenu: Error fetching profile on auth change:', profileError)
                }
              }

              fetchProfile()
            } else {
              console.log('❌ UserMenu: Auth state change - no user, clearing profile')
              setUserProfile(null)
            }
          } else {
            console.log('🔄 UserMenu: Ignoring non-meaningful auth state change:', event)
          }
        }
      }
    )

    // Also listen for page visibility changes to refresh auth state
    const handleVisibilityChange = () => {
      if (!document.hidden && mounted) {
        console.log('🔄 UserMenu: Page became visible, refreshing auth state')
        getInitialUser()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      mounted = false
      subscription.unsubscribe()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      clearTimeout(fallbackTimeout)
    }
  }, []) // Remove supabase.auth dependency to prevent infinite loops

  // Force auth refresh when navigating to certain pages (like after login redirect)
  useEffect(() => {
    const shouldRefreshAuth = ['/search', '/account', '/dashboard', '/chat'].some(route =>
      pathname.startsWith(route)
    )

    if (shouldRefreshAuth && !user) {
      console.log('🔄 UserMenu: Route change detected, forcing auth refresh for:', pathname)
      // Small delay to ensure any auth state changes from server actions have propagated
      setTimeout(async () => {
        try {
          const { data: { user: refreshedUser } } = await supabase.auth.getUser()
          if (refreshedUser && refreshedUser !== user) {
            console.log('✅ UserMenu: Found user after route change:', refreshedUser.email)
            setUser(refreshedUser)
            setLoading(false)

            // Also fetch profile
            try {
              const { data: profile } = await supabase
                .from('profiles')
                .select('subscription_tier, pulls_this_month, extra_credits, trial_start_date, trial_end_date')
                .eq('id', refreshedUser.id)
                .single()
              setUserProfile(profile)
            } catch (profileError) {
              console.error('UserMenu: Error fetching profile after route refresh:', profileError)
            }
          }
        } catch (error) {
          console.error('UserMenu: Error refreshing auth on route change:', error)
        }
      }, 100)
    }
  }, [pathname, user]) // Remove supabase.auth dependency to prevent infinite loops

  // Additional effect to periodically check auth state if we think we should be logged in
  useEffect(() => {
    if (!user && (pathname.startsWith('/search') || pathname.startsWith('/account'))) {
      const interval = setInterval(async () => {
        try {
          const { data: { user: periodicUser } } = await supabase.auth.getUser()
          if (periodicUser) {
            console.log('✅ UserMenu: Periodic check found user:', periodicUser.email)
            setUser(periodicUser)
            setLoading(false)
            clearInterval(interval)
          }
        } catch (error) {
          console.error('UserMenu: Periodic auth check error:', error)
        }
      }, 1000) // Check every second for up to 5 seconds

      // Clear interval after 5 seconds
      setTimeout(() => clearInterval(interval), 5000)

      return () => clearInterval(interval)
    }
  }, [pathname, user]) // Remove supabase.auth dependency to prevent infinite loops

  // Force refresh function for debugging
  const forceRefresh = async () => {
    console.log('🔄 UserMenu: Force refresh triggered')
    setLoading(true)

    try {
      // Clear any cached auth state and refresh session
      await supabase.auth.refreshSession()

      // Re-check auth
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      console.log('🔄 UserMenu: Force refresh result:', user ? `User: ${user.email}` : 'No user')

      if (user) {
        // Fetch profile
        const { data: profile } = await supabase
          .from('profiles')
          .select('subscription_tier, pulls_this_month, extra_credits, trial_start_date, trial_end_date')
          .eq('id', user.id)
          .single()
        setUserProfile(profile)
      } else {
        setUserProfile(null)
      }
    } catch (error) {
      console.error('UserMenu: Force refresh error:', error)
    } finally {
      setLoading(false)
    }
  }

  // Show loading state only briefly
  if (loading) {
    return (
      <div className="flex items-center space-x-4">
        <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    )
  }

  if (user) {
    const handleSignOut = async () => {
      try {
        console.log('UserMenu: Starting logout process')
        await supabase.auth.signOut()
        console.log('UserMenu: Logout completed')

        // Force immediate state update
        setUser(null)
        setUserProfile(null)

        // Use router.push for all navigation to preserve SPA behavior
        if (pathname === '/chat') {
          console.log('UserMenu: Using router.push for chat page logout to preserve auth state')
          // Clean up chat page body class before navigation
          document.body.classList.remove('chat-page-body')
          // Small delay to ensure cleanup, then navigate using router
          setTimeout(() => {
            router.push('/login')
          }, 50)
        } else {
          console.log('UserMenu: Using router.push for non-chat page logout')
          router.push('/login')
        }
      } catch (error) {
        console.error('Error signing out:', error)
        // Even if there's an error, try to redirect using router
        if (pathname === '/chat') {
          console.log('UserMenu: Error fallback - using router.push for chat page')
          document.body.classList.remove('chat-page-body')
          setTimeout(() => {
            router.push('/login')
          }, 50)
        } else {
          console.log('UserMenu: Error fallback - using router.push for non-chat page')
          router.push('/login')
        }
      }
    }



    return (
      <div className="flex items-center space-x-2">
        {/* Clean Button Menu */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleNavigation('/account')}
          className="cursor-pointer text-sm"
        >
          Account
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSignOut}
          className="cursor-pointer text-sm"
        >
          Sign Out
        </Button>
      </div>
    )
  }



  return (
    <div className="flex items-center space-x-4">
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleNavigation('/login')}
        className="cursor-pointer"
      >
        Sign In
      </Button>
      <Button
        size="sm"
        className="bg-blue-600 hover:bg-blue-700 cursor-pointer"
        onClick={() => handleNavigation('/signup')}
      >
        Sign Up
      </Button>
    </div>
  )
}
