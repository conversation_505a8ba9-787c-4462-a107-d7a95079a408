'use client'

import { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { X, ChevronLeft, ChevronRight, MapPin, FileText, Share2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'

interface OnboardingTutorialProps {
  isOpen: boolean
  onClose: () => void
  onComplete: () => void
}

const tutorialSteps = [
  {
    id: 1,
    title: 'Enter Your Address & Project',
    description: 'Start by typing any U.S. address and selecting your project type. Our AI will analyze local ordinances specific to your project needs.',
    icon: MapPin,
    image: '/images/tutorial/step1-address.png',
    tips: [
      'Use the address autocomplete for accuracy',
      'Select your specific project type for targeted results',
      'Pro users can describe custom projects in detail'
    ]
  },
  {
    id: 2,
    title: 'Review Compliance Results',
    description: 'Get detailed compliance reports with confidence scores, setback requirements, permit info, and red flag warnings for potential issues.',
    icon: FileText,
    image: '/images/tutorial/step2-results.png',
    tips: [
      'Check confidence scores to gauge accuracy',
      'Pay attention to red flags for critical issues',
      'Pro users get AI chat support for specific questions'
    ]
  },
  {
    id: 3,
    title: 'Download & Share Reports',
    description: 'Download professional PDF compliance cards to share with contractors, or upgrade to Pro for unlimited searches and advanced features.',
    icon: Share2,
    image: '/images/tutorial/step3-share.png',
    tips: [
      'PDF reports are perfect for sharing with contractors',
      'Free users get 3 trial searches, then 10 after signup',
      'Pro users get unlimited searches plus AI chat support'
    ]
  }
]

export function OnboardingTutorial({ isOpen, onClose, onComplete }: OnboardingTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isCompleting, setIsCompleting] = useState(false)
  const supabase = createClient()

  const handleComplete = async () => {
    setIsCompleting(true)
    try {
      // Mark user as no longer first-time
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        await supabase
          .from('profiles')
          .update({ first_time_user: false })
          .eq('id', user.id)
      }
      onComplete()
    } catch (error) {
      console.error('Error completing tutorial:', error)
    } finally {
      setIsCompleting(false)
    }
  }

  const nextStep = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex)
  }

  const currentStepData = tutorialSteps[currentStep]
  const IconComponent = currentStepData.icon

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-xl bg-card border border-border p-8 text-left align-middle shadow-premium-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center space-x-4">
                    <div className="bg-primary/10 rounded-full p-3">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <Dialog.Title className="text-xl font-semibold text-card-foreground">
                        {currentStepData.title}
                      </Dialog.Title>
                      <p className="text-sm text-muted-foreground">
                        Step {currentStep + 1} of {tutorialSteps.length}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={onClose}
                    className="text-muted-foreground hover:text-card-foreground transition-colors rounded-lg p-2 hover:bg-muted"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Progress Bar */}
                <div className="mb-8">
                  <div className="flex space-x-2">
                    {tutorialSteps.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => goToStep(index)}
                        className={`flex-1 h-2 rounded-full transition-colors ${
                          index <= currentStep ? 'bg-primary' : 'bg-muted'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                {/* Content */}
                <div className="mb-8">
                  <p className="text-card-foreground mb-6 text-base leading-relaxed">
                    {currentStepData.description}
                  </p>

                  {/* Tips */}
                  <div className="bg-primary/5 border border-primary/20 rounded-lg p-6">
                    <h4 className="font-semibold text-primary mb-3 flex items-center">
                      <span className="text-lg mr-2">💡</span>
                      Pro Tips:
                    </h4>
                    <ul className="space-y-2">
                      {currentStepData.tips.map((tip, index) => (
                        <li key={index} className="text-sm text-card-foreground flex items-start">
                          <span className="text-primary mr-2 mt-0.5">•</span>
                          <span>{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Navigation */}
                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className="flex items-center space-x-2"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span>Previous</span>
                  </Button>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={onClose}
                    >
                      Skip Tutorial
                    </Button>
                    <Button
                      onClick={nextStep}
                      disabled={isCompleting}
                      className="flex items-center space-x-2 bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      <span>
                        {currentStep === tutorialSteps.length - 1 ? 'Get Started' : 'Next'}
                      </span>
                      {currentStep < tutorialSteps.length - 1 && (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
