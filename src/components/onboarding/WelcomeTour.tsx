'use client'

import { useState, useEffect } from 'react'
import { X, ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react'

interface TourStep {
  id: number
  title: string
  content: string
  target?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
}

interface WelcomeTourProps {
  isNewUser?: boolean
  onComplete?: () => void
  onSkip?: () => void
}

export default function WelcomeTour({ isNewUser = false, onComplete, onSkip }: WelcomeTourProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [isCompleted, setIsCompleted] = useState(false)

  const tourSteps: TourStep[] = [
    {
      id: 1,
      title: 'Welcome to Ordrly!',
      content: 'We\'re excited to help you navigate property compliance requirements. Let\'s take a quick tour to get you started with your compliance research.',
      position: 'bottom'
    },
    {
      id: 2,
      title: 'Enter Address & Project Type',
      content: 'Start by entering any U.S. address and selecting your project type. Our AI will analyze local ordinances specific to your project needs.',
      target: 'search-input',
      position: 'bottom'
    },
    {
      id: 3,
      title: 'Review Compliance Results',
      content: 'Get detailed compliance reports with confidence scores, setback requirements, permit info, and red flag warnings for potential issues.',
      position: 'top'
    },
    {
      id: 4,
      title: 'Download Professional Reports',
      content: 'Download PDF compliance cards to share with contractors. Pro users get unlimited searches plus AI chat support for specific questions.',
      position: 'top'
    },
    {
      id: 5,
      title: 'You\'re Ready to Start!',
      content: 'That\'s it! You have 3 trial searches to get started, then 10 more after signup. Upgrade to Pro for unlimited access and advanced features.',
      position: 'bottom'
    }
  ]

  useEffect(() => {
    // Show tour for new users or when explicitly triggered
    if (isNewUser) {
      const hasSeenTour = localStorage.getItem('ordrly-welcome-tour-completed')
      if (!hasSeenTour) {
        setIsVisible(true)
      }
    }
  }, [isNewUser])

  const nextStep = () => {
    if (currentStep < tourSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      completeTour()
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const completeTour = () => {
    setIsCompleted(true)
    localStorage.setItem('ordrly-welcome-tour-completed', 'true')
    setTimeout(() => {
      setIsVisible(false)
      onComplete?.()
    }, 1500)
  }

  const skipTour = () => {
    localStorage.setItem('ordrly-welcome-tour-completed', 'true')
    setIsVisible(false)
    onSkip?.()
  }

  if (!isVisible) return null

  const currentTourStep = tourSteps[currentStep]

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
        {/* Tour Modal */}
        <div className="bg-card border border-border rounded-xl shadow-premium-xl max-w-md w-full mx-4 relative">
          {/* Close Button */}
          <button
            onClick={skipTour}
            className="absolute top-4 right-4 text-muted-foreground hover:text-card-foreground transition-colors rounded-lg p-2 hover:bg-muted"
          >
            <X className="h-5 w-5" />
          </button>

          {/* Content */}
          <div className="p-6">
            {/* Step Indicator */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex space-x-2">
                {tourSteps.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index <= currentStep ? 'bg-primary' : 'bg-muted'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-muted-foreground">
                {currentStep + 1} of {tourSteps.length}
              </span>
            </div>

            {/* Step Content */}
            <div className="mb-8">
              {isCompleted ? (
                <div className="text-center">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-success/10 rounded-full">
                      <CheckCircle className="h-8 w-8 text-success" />
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-card-foreground mb-2">
                    Welcome tour complete!
                  </h3>
                  <p className="text-muted-foreground">
                    You&apos;re ready to start using Ordrly. Happy compliance checking!
                  </p>
                </div>
              ) : (
                <>
                  <h3 className="text-xl font-bold text-card-foreground mb-4">
                    {currentTourStep.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {currentTourStep.content}
                  </p>
                </>
              )}
            </div>

            {/* Navigation */}
            {!isCompleted && (
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className={`flex items-center px-3 py-2 rounded-lg transition-colors ${
                      currentStep === 0
                        ? 'text-muted-foreground cursor-not-allowed'
                        : 'text-card-foreground hover:bg-muted'
                    }`}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </button>

                  <button
                    onClick={skipTour}
                    className="px-3 py-2 text-muted-foreground hover:bg-muted hover:text-card-foreground rounded-lg transition-colors"
                  >
                    Skip tour
                  </button>
                </div>

                <button
                  onClick={nextStep}
                  className="flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                >
                  {currentStep === tourSteps.length - 1 ? 'Get Started' : 'Next'}
                  {currentStep < tourSteps.length - 1 && (
                    <ChevronRight className="h-4 w-4 ml-1" />
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

// Hook to trigger welcome tour
export function useWelcomeTour() {
  const [showTour, setShowTour] = useState(false)

  const startTour = () => setShowTour(true)
  const endTour = () => setShowTour(false)

  return {
    showTour,
    startTour,
    endTour,
    WelcomeTour: (props: Omit<WelcomeTourProps, 'isNewUser'>) => (
      <WelcomeTour {...props} isNewUser={showTour} />
    )
  }
}
