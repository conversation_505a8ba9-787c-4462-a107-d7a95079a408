'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Download, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react'

interface BulkActionsProps {
  onExport: (type: 'full' | 'searches' | 'chats') => Promise<void>
  onBulkDelete: (type: 'searches' | 'chats' | 'all') => Promise<void>
  exporting: boolean
  bulkDeleting: 'searches' | 'chats' | 'all' | null
}

export function BulkActions({ onExport, onBulkDelete, exporting, bulkDeleting }: BulkActionsProps) {
  return (
    <div className="space-y-8">
      {/* Export Data Section */}
      <div className="bg-card border border-border rounded-2xl p-8 shadow-premium">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-success/10 rounded-full p-2">
            <Download className="h-6 w-6 text-success" />
          </div>
          <h3 className="text-xl font-semibold text-card-foreground">Data Exports</h3>
        </div>
        <p className="text-sm text-gray-400 mb-6">
          Download your searches, chats, and saved data in portable formats.
        </p>

        <div className="flex flex-wrap gap-4">
          <Button
            onClick={() => onExport('searches')}
            disabled={exporting}
            variant="outline"
            className="flex-1 min-w-0 bg-gray-700 border-gray-600 text-gray-200 hover:bg-blue-600 hover:border-blue-600 hover:text-white transition-all duration-200 flex items-center space-x-2"
          >
            {exporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            <span>Export Search History</span>
          </Button>

          <Button
            onClick={() => onExport('chats')}
            disabled={exporting}
            variant="outline"
            className="flex-1 min-w-0 bg-gray-700 border-gray-600 text-gray-200 hover:bg-blue-600 hover:border-blue-600 hover:text-white transition-all duration-200 flex items-center space-x-2"
          >
            {exporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            <span>Export Chat History</span>
          </Button>

          <Button
            onClick={() => onExport('full')}
            disabled={exporting}
            variant="outline"
            className="flex-1 min-w-0 bg-red-600 border-red-600 text-white hover:bg-red-700 hover:border-red-700 transition-all duration-200 flex items-center space-x-2"
          >
            {exporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            <span>Export All Data</span>
          </Button>
        </div>
        <p className="text-xs text-gray-400 text-center mt-4">
          May take several minutes; you'll receive an email when ready.
        </p>
      </div>

      {/* Bulk Delete Section */}
      <div className="bg-gray-800 p-6 rounded-lg mb-8">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-destructive/10 rounded-full p-2">
            <Trash2 className="h-6 w-6 text-red-400" />
          </div>
          <h3 className="text-xl font-semibold text-card-foreground">Data Deletion</h3>
        </div>
        <p className="text-sm text-gray-400 mb-6">
          Make sure you export your data first—this cannot be undone.
        </p>

        <div className="bg-red-800 bg-opacity-40 border border-red-700 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-6 w-6 text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-red-100 text-sm font-medium">Warning</p>
              <p className="text-red-100 text-sm mt-2">
                Data deletion is permanent and cannot be undone. Make sure to export any data you want to keep before proceeding.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Button
            onClick={() => onBulkDelete('searches')}
            disabled={bulkDeleting !== null}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700 transition-all duration-200 flex items-center space-x-2"
          >
            {bulkDeleting === 'searches' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            <span>Delete All Search History</span>
          </Button>

          <Button
            onClick={() => onBulkDelete('chats')}
            disabled={bulkDeleting !== null}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700 transition-all duration-200 flex items-center space-x-2"
          >
            {bulkDeleting === 'chats' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            <span>Delete All Chat History</span>
          </Button>

          <Button
            onClick={() => onBulkDelete('all')}
            disabled={bulkDeleting !== null}
            variant="destructive"
            className="bg-red-700 hover:bg-red-800 ring-red-500 hover:ring-2 transition-all duration-200 flex items-center space-x-2 col-span-full sm:col-span-2"
          >
            {bulkDeleting === 'all' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            <span>Delete All Data</span>
          </Button>
        </div>

        <div className="mt-4 p-4 bg-gray-700 text-gray-200 rounded-md">
          <h4 className="text-sm font-medium text-white mb-2">What gets deleted:</h4>
          <ul className="text-xs text-gray-300 space-y-1">
            <li>• <strong>Delete All Search History:</strong> All your property compliance search history</li>
            <li>• <strong>Delete All Chat History:</strong> All your AI assistant conversations and messages</li>
            <li>• <strong>Delete All Data:</strong> Both search history and chat conversations</li>
          </ul>
          <p className="text-xs text-gray-400 mt-2">
            Your account settings, subscription, and profile information will not be affected.
          </p>
        </div>
      </div>

      {/* Data Usage Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Your Data Rights</h3>
        <div className="text-sm text-gray-700 space-y-2">
          <p>
            <strong>Access:</strong> Request a copy of all your data at any time using the buttons above.
          </p>
          <p>
            <strong>Delete:</strong> You can delete your data partially or completely using the delete options.
          </p>
          <p>
            <strong>Portability:</strong> Your exported data is in standard JSON format for easy portability.
          </p>
          <p>
            <strong>Data Retention:</strong> Configure automatic deletion in your privacy settings above.
          </p>
        </div>
      </div>
    </div>
  )
}
