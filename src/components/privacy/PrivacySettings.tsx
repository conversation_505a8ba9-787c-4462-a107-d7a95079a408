'use client'

import { useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Shield, Loader2, Search, MessageSquare, Download, Info } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'

interface PrivacySettings {
  save_search_history: boolean
  save_chat_history: boolean
  retention_days: number
  auto_delete_enabled: boolean
  export_enabled: boolean
}

export function PrivacySettings() {
  const [settings, setSettings] = useState<PrivacySettings>({
    save_search_history: true,
    save_chat_history: true,
    retention_days: 365,
    auto_delete_enabled: false,
    export_enabled: true
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const supabase = createClient()

  const fetchPrivacySettings = useCallback(async () => {
    try {
      setLoading(true)
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        setError('Authentication required')
        return
      }

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('privacy_settings')
        .eq('id', user.id)
        .single()

      if (error) {
        console.error('Error fetching privacy settings:', error)
        setError('Failed to load privacy settings')
        return
      }

      if (profile?.privacy_settings) {
        setSettings(profile.privacy_settings)
      }
    } catch (err) {
      console.error('Error:', err)
      setError('Failed to load privacy settings')
    } finally {
      setLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    fetchPrivacySettings()
  }, [fetchPrivacySettings])

  const savePrivacySettings = async () => {
    try {
      setSaving(true)
      setError(null)
      setSuccess(null)

      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        setError('Authentication required')
        return
      }

      const { error } = await supabase
        .from('profiles')
        .update({
          privacy_settings: settings,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)

      if (error) {
        console.error('Error saving privacy settings:', error)
        setError('Failed to save privacy settings')
        return
      }

      setSuccess('Privacy settings saved successfully')
    } catch (err) {
      console.error('Error:', err)
      setError('Failed to save privacy settings')
    } finally {
      setSaving(false)
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <span className="text-muted-foreground">Loading privacy settings...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="bg-primary/10 rounded-full p-2">
          <Shield className="h-6 w-6 text-primary" />
        </div>
        <h2 className="text-2xl font-bold text-card-foreground">Privacy & Data Controls</h2>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <p className="text-destructive text-sm">{error}</p>
        </div>
      )}

      {success && (
        <div className="bg-success/10 border border-success/20 rounded-lg p-4">
          <p className="text-success text-sm">{success}</p>
        </div>
      )}

      {/* Data Collection Settings */}
      <div className="bg-gray-800 p-6 rounded-lg mb-8">
        <h3 className="text-xl font-semibold text-card-foreground mb-6">Data Collection</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg border border-border">
            <div>
              <label className="font-semibold text-white flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Save Search History
              </label>
              <p className="text-sm text-gray-400 mt-1">
                When on, your past searches will be saved for easy access.
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.save_search_history}
              onChange={(e) => setSettings(prev => ({ ...prev, save_search_history: e.target.checked }))}
              className="h-5 w-5 text-primary focus:ring-primary border-border rounded transition-colors"
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg border border-border">
            <div>
              <label className="font-semibold text-white flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                Save Chat History
              </label>
              <p className="text-sm text-gray-400 mt-1">
                When on, your past searches will be saved for easy access.
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.save_chat_history}
              onChange={(e) => setSettings(prev => ({ ...prev, save_chat_history: e.target.checked }))}
              className="h-5 w-5 text-primary focus:ring-primary border-border rounded transition-colors"
            />
          </div>
        </div>
      </div>

      {/* Data Retention Settings */}
      <div className="bg-card border border-border rounded-2xl p-8 shadow-premium">
        <h3 className="text-xl font-semibold text-card-foreground mb-6">Data Retention</h3>
        <div className="space-y-6">
          <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg border border-border">
            <div>
              <label className="font-semibold text-white flex items-center">
                Auto-Delete Old Data
                <Info className="h-4 w-4 ml-2 text-gray-400" />
              </label>
              <p className="text-sm text-gray-400 mt-1">
                Automatically delete data older than retention period
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.auto_delete_enabled}
              onChange={(e) => setSettings(prev => ({ ...prev, auto_delete_enabled: e.target.checked }))}
              className="h-5 w-5 text-primary focus:ring-primary border-border rounded transition-colors"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-card-foreground mb-3">
              Data retention period
            </label>
            <select
              value={settings.retention_days}
              onChange={(e) => setSettings(prev => ({ ...prev, retention_days: parseInt(e.target.value) }))}
              disabled={!settings.auto_delete_enabled}
              className={`w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground transition-colors ${
                !settings.auto_delete_enabled ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <option value={30}>30 days</option>
              <option value={90}>90 days</option>
              <option value={180}>6 months</option>
              <option value={365}>1 year</option>
              <option value={730}>2 years</option>
              <option value={-1}>Forever</option>
            </select>
            <p className="text-sm text-gray-400 mt-2">
              How long to keep your data before automatic deletion
            </p>
            {!settings.auto_delete_enabled && (
              <p className="text-xs text-red-400 mt-1">
                Please enable auto-delete to select a retention period.
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Save Settings Button */}
      <div className="flex justify-end">
        <Button
          onClick={savePrivacySettings}
          disabled={saving}
          className="transition-all duration-200 hover:scale-[1.02]"
        >
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Saving...
            </>
          ) : (
            'Save Privacy Settings'
          )}
        </Button>
      </div>
    </div>
  )
}
