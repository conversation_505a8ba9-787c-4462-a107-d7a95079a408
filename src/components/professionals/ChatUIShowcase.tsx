'use client'

import React from 'react'
import { Bo<PERSON>, User, FileText, ExternalLink } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export function ChatUIShowcase() {
  return (
    <div className="max-w-5xl mx-auto">
      <Card className="bg-card/80 backdrop-blur-sm border border-border shadow-2xl overflow-hidden">
        {/* Chat Interface Mockup */}
        <div className="flex flex-col lg:flex-row h-[600px] lg:h-[600px]">
          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col min-h-[400px] lg:min-h-0">
            {/* Chat Header */}
            <div className="px-3 lg:px-6 py-3 lg:py-4 border-b border-border/30 bg-muted/20">
              <div className="flex items-center justify-between max-w-3xl mx-auto">
                <div className="flex items-center gap-2 lg:gap-3">
                  <div className="text-xs lg:text-sm font-medium text-muted-foreground truncate">
                    Research Chat • 123 Main St, Grand Rapids, MI
                  </div>
                </div>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto bg-background">
              {/* User Message */}
              <div className="max-w-4xl mx-auto px-3 lg:px-6 py-4 lg:py-6">
                <div className="flex items-start gap-2 lg:gap-4 flex-row-reverse">
                  <div className="flex-shrink-0">
                    <div className="w-7 h-7 lg:w-8 lg:h-8 rounded-full flex items-center justify-center bg-primary text-primary-foreground">
                      <User className="h-3 w-3 lg:h-4 lg:w-4" />
                    </div>
                  </div>
                  <div className="flex-1 text-right">
                    <div className="inline-block bg-primary text-primary-foreground rounded-2xl px-3 lg:px-4 py-2 lg:py-3 max-w-[280px] lg:max-w-md text-xs lg:text-sm">
                      What are the setback requirements for a deck addition on a residential property?
                    </div>
                  </div>
                </div>
              </div>

              {/* AI Response */}
              <div className="bg-muted/20">
                <div className="max-w-4xl mx-auto px-3 lg:px-6 py-4 lg:py-6">
                  <div className="flex items-start gap-2 lg:gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-7 h-7 lg:w-8 lg:h-8 rounded-full flex items-center justify-center bg-muted border border-border">
                        <Bot className="h-3 w-3 lg:h-4 lg:w-4" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <p className="text-foreground mb-3 lg:mb-4 text-xs lg:text-sm">
                          Based on Grand Rapids zoning ordinance, deck setback requirements are:
                        </p>
                        <ul className="text-foreground space-y-1 lg:space-y-2 mb-3 lg:mb-4 text-xs lg:text-sm">
                          <li><strong>Rear yard:</strong> 10 feet minimum from property line <sup className="text-primary cursor-pointer">[1]</sup></li>
                          <li><strong>Side yard:</strong> 5 feet minimum from property line <sup className="text-primary cursor-pointer">[2]</sup></li>
                          <li><strong>Front yard:</strong> Must maintain primary structure setback <sup className="text-primary cursor-pointer">[3]</sup></li>
                        </ul>
                        <p className="text-foreground text-xs lg:text-sm">
                          <strong>Important:</strong> Decks over 30 inches high require building permits per Chapter 8, Section 8.02 <sup className="text-primary cursor-pointer">[4]</sup>.
                          I recommend checking with the city planning department for your specific lot configuration.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Follow-up User Message */}
              <div className="max-w-4xl mx-auto px-3 lg:px-6 py-4 lg:py-6">
                <div className="flex items-start gap-2 lg:gap-4 flex-row-reverse">
                  <div className="flex-shrink-0">
                    <div className="w-7 h-7 lg:w-8 lg:h-8 rounded-full flex items-center justify-center bg-primary text-primary-foreground">
                      <User className="h-3 w-3 lg:h-4 lg:w-4" />
                    </div>
                  </div>
                  <div className="flex-1 text-right">
                    <div className="inline-block bg-primary text-primary-foreground rounded-2xl px-3 lg:px-4 py-2 lg:py-3 max-w-[280px] lg:max-w-md text-xs lg:text-sm">
                      Do I need a permit for a 24-inch high deck?
                    </div>
                  </div>
                </div>
              </div>

              {/* AI Follow-up Response */}
              <div className="bg-muted/20">
                <div className="max-w-4xl mx-auto px-3 lg:px-6 py-4 lg:py-6">
                  <div className="flex items-start gap-2 lg:gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-7 h-7 lg:w-8 lg:h-8 rounded-full flex items-center justify-center bg-muted border border-border">
                        <Bot className="h-3 w-3 lg:h-4 lg:w-4" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <p className="text-foreground text-xs lg:text-sm">
                          <strong>No permit required</strong> for a 24-inch high deck. Grand Rapids building code only requires permits for decks exceeding 30 inches in height <sup className="text-primary cursor-pointer">[4]</sup>.
                        </p>
                        <p className="text-foreground mt-2 lg:mt-3 text-xs lg:text-sm">
                          However, you still must comply with setback requirements and any HOA restrictions. The deck must also meet structural requirements per IRC Section R502 <sup className="text-primary cursor-pointer">[5]</sup>.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Input Area */}
            <div className="border-t border-border/30 bg-background">
              <div className="max-w-4xl mx-auto px-3 lg:px-6 py-3 lg:py-4">
                <div className="relative flex items-end gap-2">
                  <div className="flex-1 border border-input rounded-xl px-3 lg:px-4 py-2 lg:py-3 text-xs lg:text-sm bg-background text-muted-foreground">
                    Ask about permits, zoning, setbacks...
                  </div>
                  <Button size="sm" className="px-3 lg:px-4 py-2 lg:py-3 rounded-xl text-xs lg:text-sm" disabled>
                    Send
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Sources Panel */}
          <div className="w-full lg:w-80 border-t lg:border-t-0 lg:border-l border-border bg-muted/10 flex flex-col max-h-[200px] lg:max-h-none">
            {/* Sources Header */}
            <div className="p-2 lg:p-3 border-b border-border/50">
              <div className="flex items-center justify-between">
                <h3 className="text-xs lg:text-sm font-medium text-muted-foreground">Sources</h3>
                <span className="text-xs text-muted-foreground">5</span>
              </div>
            </div>

            {/* Sources List */}
            <div className="flex-1 overflow-y-auto p-1 lg:p-2">
              <div className="space-y-2 lg:space-y-3">
                {/* Source 1 */}
                <Card className="p-2 lg:p-3 bg-card/50 border border-border/50 hover:bg-card/80 transition-colors cursor-pointer">
                  <div className="flex items-start gap-1 lg:gap-2">
                    <span className="text-xs font-medium text-primary bg-primary/10 rounded px-1 lg:px-1.5 py-0.5 flex-shrink-0">1</span>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-xs font-medium text-foreground truncate">
                        Zoning Ordinance Chapter 5
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2 hidden lg:block">
                        Residential setback requirements for accessory structures
                      </p>
                      <div className="flex items-center gap-1 mt-1 lg:mt-2">
                        <FileText className="w-3 h-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground truncate">City of Grand Rapids</span>
                        <ExternalLink className="w-3 h-3 text-muted-foreground ml-auto flex-shrink-0" />
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Source 2 */}
                <Card className="p-2 lg:p-3 bg-card/50 border border-border/50 hover:bg-card/80 transition-colors cursor-pointer">
                  <div className="flex items-start gap-1 lg:gap-2">
                    <span className="text-xs font-medium text-primary bg-primary/10 rounded px-1 lg:px-1.5 py-0.5 flex-shrink-0">2</span>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-xs font-medium text-foreground truncate">
                        Building Code Section 8.02
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2 hidden lg:block">
                        Permit requirements for deck construction
                      </p>
                      <div className="flex items-center gap-1 mt-1 lg:mt-2">
                        <FileText className="w-3 h-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground truncate">City of Grand Rapids</span>
                        <ExternalLink className="w-3 h-3 text-muted-foreground ml-auto flex-shrink-0" />
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Source 3 */}
                <Card className="p-2 lg:p-3 bg-card/50 border border-border/50 hover:bg-card/80 transition-colors cursor-pointer">
                  <div className="flex items-start gap-1 lg:gap-2">
                    <span className="text-xs font-medium text-primary bg-primary/10 rounded px-1 lg:px-1.5 py-0.5 flex-shrink-0">3</span>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-xs font-medium text-foreground truncate">
                        Residential Design Standards
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2 hidden lg:block">
                        Front yard setback maintenance requirements
                      </p>
                      <div className="flex items-center gap-1 mt-1 lg:mt-2">
                        <FileText className="w-3 h-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground truncate">City of Grand Rapids</span>
                        <ExternalLink className="w-3 h-3 text-muted-foreground ml-auto flex-shrink-0" />
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Source 4 */}
                <Card className="p-2 lg:p-3 bg-card/50 border border-border/50 hover:bg-card/80 transition-colors cursor-pointer">
                  <div className="flex items-start gap-1 lg:gap-2">
                    <span className="text-xs font-medium text-primary bg-primary/10 rounded px-1 lg:px-1.5 py-0.5 flex-shrink-0">4</span>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-xs font-medium text-foreground truncate">
                        IRC Section R502
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2 hidden lg:block">
                        Structural requirements for deck construction
                      </p>
                      <div className="flex items-center gap-1 mt-1 lg:mt-2">
                        <FileText className="w-3 h-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground truncate">International Code</span>
                        <ExternalLink className="w-3 h-3 text-muted-foreground ml-auto flex-shrink-0" />
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Source 5 */}
                <Card className="p-2 lg:p-3 bg-card/50 border border-border/50 hover:bg-card/80 transition-colors cursor-pointer">
                  <div className="flex items-start gap-1 lg:gap-2">
                    <span className="text-xs font-medium text-primary bg-primary/10 rounded px-1 lg:px-1.5 py-0.5 flex-shrink-0">5</span>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-xs font-medium text-foreground truncate">
                        Planning Department Guidelines
                      </h4>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2 hidden lg:block">
                        Best practices for residential additions
                      </p>
                      <div className="flex items-center gap-1 mt-1 lg:mt-2">
                        <FileText className="w-3 h-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground truncate">City of Grand Rapids</span>
                        <ExternalLink className="w-3 h-3 text-muted-foreground ml-auto flex-shrink-0" />
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
