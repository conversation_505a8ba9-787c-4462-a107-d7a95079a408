'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { MapPin, TrendingUp, Clock, Star, Sparkles, Target, Zap } from 'lucide-react'

interface Recommendation {
  id: string
  type: 'frequent_area' | 'popular_project' | 'recent_trend' | 'suggested_search'
  title: string
  description: string
  address?: string
  project_type?: string
  confidence_score: number
  reason: string
  action_url?: string
}

interface PersonalizedRecommendationsProps {
  onRecommendationClick?: (recommendation: Recommendation) => void
  className?: string
}

export function PersonalizedRecommendations({ 
  onRecommendationClick, 
  className = "" 
}: PersonalizedRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadRecommendations()
  }, [])

  const loadRecommendations = async () => {
    try {
      const response = await fetch('/api/recommendations/personalized')
      if (response.ok) {
        const data = await response.json()
        setRecommendations(data.recommendations || [])
      }
    } catch (error) {
      console.error('Failed to load recommendations:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'frequent_area':
        return <MapPin className="h-4 w-4 text-primary-600 dark:text-primary-400" />
      case 'popular_project':
        return <TrendingUp className="h-4 w-4 text-success-600 dark:text-success-400" />
      case 'recent_trend':
        return <Clock className="h-4 w-4 text-warning-600 dark:text-warning-400" />
      case 'suggested_search':
        return <Sparkles className="h-4 w-4 text-info-600 dark:text-info-400" />
      default:
        return <Target className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getRecommendationColor = (type: string) => {
    switch (type) {
      case 'frequent_area':
        return 'bg-primary-600 text-white dark:bg-primary-500 dark:text-slate-900'
      case 'popular_project':
        return 'bg-success-600 text-white dark:bg-success-500 dark:text-slate-900'
      case 'recent_trend':
        return 'bg-warning-600 text-white dark:bg-warning-500 dark:text-slate-900'
      case 'suggested_search':
        return 'bg-info-600 text-white dark:bg-info-500 dark:text-slate-900'
      default:
        return 'bg-slate-600 text-white dark:bg-slate-500 dark:text-slate-900'
    }
  }

  const handleRecommendationClick = (recommendation: Recommendation) => {
    if (onRecommendationClick) {
      onRecommendationClick(recommendation)
    } else if (recommendation.action_url) {
      window.location.href = recommendation.action_url
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg font-semibold">
            <Sparkles className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
            Recommended for You
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Loading personalized recommendations...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (recommendations.length === 0) {
    return (
      <Card className={className}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg font-semibold">
            <Sparkles className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
            Recommended for You
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Based on your search history and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Target className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground font-medium mb-1">
              No recommendations yet
            </p>
            <p className="text-sm text-muted-foreground">
              Start searching to get personalized recommendations!
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center text-lg font-semibold">
          <Sparkles className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
          Recommended for You
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          Based on your search history and preferences
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {recommendations.slice(0, 5).map((recommendation) => (
            <div
              key={recommendation.id}
              className="group flex items-start space-x-3 p-4 rounded-xl border border-border bg-card hover:bg-accent/50 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-primary-200 dark:hover:border-primary-800"
              onClick={() => handleRecommendationClick(recommendation)}
            >
              <div className="flex-shrink-0 mt-0.5 p-2 rounded-lg bg-muted/50 group-hover:bg-primary-50 dark:group-hover:bg-primary-900/20 transition-colors">
                {getRecommendationIcon(recommendation.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-semibold text-foreground truncate">
                    {recommendation.title}
                  </h4>
                  <Badge
                    variant="secondary"
                    className={`text-xs font-medium ${getRecommendationColor(recommendation.type)}`}
                  >
                    {Math.round(recommendation.confidence_score * 100)}%
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-2 leading-relaxed">
                  {recommendation.description}
                </p>
                <p className="text-xs text-muted-foreground/80 mb-3">
                  {recommendation.reason}
                </p>
                {(recommendation.address || recommendation.project_type) && (
                  <div className="flex items-center gap-2 flex-wrap">
                    {recommendation.address && (
                      <Badge variant="outline" className="text-xs bg-background">
                        <MapPin className="h-3 w-3 mr-1" />
                        {recommendation.address}
                      </Badge>
                    )}
                    {recommendation.project_type && (
                      <Badge variant="outline" className="text-xs bg-background">
                        <Zap className="h-3 w-3 mr-1" />
                        {recommendation.project_type}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}

          {recommendations.length > 5 && (
            <Button
              variant="outline"
              className="w-full mt-4 hover:bg-primary-50 hover:border-primary-200 dark:hover:bg-primary-900/20 dark:hover:border-primary-800"
              onClick={() => window.location.href = '/recommendations'}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              View All Recommendations ({recommendations.length})
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
