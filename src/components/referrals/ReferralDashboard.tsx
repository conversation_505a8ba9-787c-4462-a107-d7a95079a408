'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Co<PERSON>, Share2, <PERSON>, Gift, Clock, CheckCircle } from 'lucide-react'

interface ReferralData {
  referralCode: string
  extraCredits: number
  stats: {
    totalReferrals: number
    verifiedReferrals: number
    pendingReferrals: number
    totalCreditsEarned: number
  }
  referrals: Array<{
    id: string
    status: 'pending' | 'verified' | 'credited'
    creditsAwarded: number
    createdAt: string
    verifiedAt?: string
    creditedAt?: string
    refereeEmail: string
  }>
}

export default function ReferralDashboard() {
  const [data, setData] = useState<ReferralData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [copySuccess, setCopySuccess] = useState(false)

  useEffect(() => {
    fetchReferralData()
  }, [])

  const fetchReferralData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/referrals')

      if (!response.ok) {
        throw new Error('Failed to fetch referral data')
      }

      const result = await response.json()
      if (result.success) {
        setData(result.data)
      } else {
        throw new Error(result.error || 'Unknown error')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const generateReferralCode = async () => {
    try {
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'generate' })
      })

      const result = await response.json()
      if (result.success) {
        await fetchReferralData() // Refresh data
      } else {
        throw new Error(result.error || 'Failed to generate referral code')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    }
  }

  const copyReferralLink = async () => {
    if (!data?.referralCode) return

    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || window.location.origin
    const referralUrl = `${siteUrl}/signup?ref=${data.referralCode}`

    try {
      await navigator.clipboard.writeText(referralUrl)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const shareReferralLink = async () => {
    if (!data?.referralCode) return

    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || window.location.origin
    const referralUrl = `${siteUrl}/signup?ref=${data.referralCode}`
    const shareText = "Check out Ordrly - it helps you understand property compliance rules before you build! Use my referral link to get started:"

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join Ordrly',
          text: shareText,
          url: referralUrl
        })
      } catch {
        // Fallback to copy
        copyReferralLink()
      }
    } else {
      copyReferralLink()
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'credited':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Users className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'credited':
        return <Badge variant="default" className="bg-green-100 text-green-800">Credited</Badge>
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchReferralData} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-8 animate-fade-in">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-foreground mb-4">Referral Program</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Invite friends and earn <span className="text-primary font-semibold">5 extra searches</span> for each verified signup!
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card border border-border rounded-2xl p-6 shadow-premium hover:shadow-premium-lg transition-all duration-300 animate-slide-up">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Total Referrals</h3>
            <Users className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{data?.stats.totalReferrals || 0}</div>
        </div>

        <div className="bg-card border border-border rounded-2xl p-6 shadow-premium hover:shadow-premium-lg transition-all duration-300 animate-slide-up" style={{ animationDelay: '50ms' }}>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Verified Referrals</h3>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{data?.stats.verifiedReferrals || 0}</div>
        </div>

        <div className="bg-card border border-border rounded-2xl p-6 shadow-premium hover:shadow-premium-lg transition-all duration-300 animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Pending</h3>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{data?.stats.pendingReferrals || 0}</div>
        </div>

        <div className="bg-card border border-border rounded-2xl p-6 shadow-premium hover:shadow-premium-lg transition-all duration-300 animate-slide-up" style={{ animationDelay: '150ms' }}>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">Credits Earned</h3>
            <Gift className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold text-card-foreground">{data?.stats.totalCreditsEarned || 0}</div>
        </div>
      </div>

      {/* Referral Link */}
      <div className="bg-card border border-border rounded-2xl p-8 shadow-premium animate-slide-up">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-foreground mb-2">Your Referral Link</h2>
          <p className="text-muted-foreground">
            Share this link with friends to earn <span className="text-primary font-semibold">25 extra messages</span> for each verified signup
          </p>
        </div>
        <div>
          {data?.referralCode ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <code className="flex-1 p-3 bg-muted/30 border border-border rounded text-sm font-mono text-card-foreground">
                  {`${process.env.NEXT_PUBLIC_SITE_URL || window.location.origin}/signup?ref=${data.referralCode}`}
                </code>
                <Button
                  onClick={copyReferralLink}
                  variant="outline"
                  size="sm"
                  className={`transition-all duration-200 hover:scale-[1.02] ${copySuccess ? 'bg-success/10 border-success/20 text-success' : ''}`}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  {copySuccess ? 'Copied!' : 'Copy'}
                </Button>
                <Button
                  onClick={shareReferralLink}
                  size="sm"
                  className="transition-all duration-200 hover:scale-[1.02]"
                >
                  <Share2 className="h-4 w-4 mr-1" />
                  Share
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Your referral code: <span className="font-mono font-semibold text-primary">{data.referralCode}</span>
              </p>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-muted-foreground mb-4">Generate your referral code to start earning credits</p>
              <Button
                onClick={generateReferralCode}
                className="transition-all duration-200 hover:scale-[1.02]"
              >
                Generate Referral Code
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Referral History */}
      {data?.referrals && data.referrals.length > 0 && (
        <div className="bg-card border border-border rounded-2xl p-8 shadow-premium animate-slide-up">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-foreground mb-2">Referral History</h2>
            <p className="text-muted-foreground">
              Track your referrals and their verification status
            </p>
          </div>
          <div className="space-y-3">
            {data.referrals.map((referral, index) => (
              <div
                key={referral.id}
                className="flex items-center justify-between p-4 border border-border rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors animate-slide-up"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="flex items-center space-x-3">
                  {getStatusIcon(referral.status)}
                  <div>
                    <p className="text-sm font-medium text-card-foreground">{referral.refereeEmail}</p>
                    <p className="text-xs text-muted-foreground">
                      Referred on {new Date(referral.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {referral.creditsAwarded > 0 && (
                    <span className="text-sm text-success font-medium">
                      +{referral.creditsAwarded} credits
                    </span>
                  )}
                  {getStatusBadge(referral.status)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
