'use client'

import { ReactNode } from 'react'

interface DashboardLayoutProps {
  children: ReactNode
  sidebar: ReactNode
  footer?: ReactNode
  className?: string
}

export function DashboardLayout({
  children,
  sidebar,
  footer,
  className = ""
}: DashboardLayoutProps) {
  return (
    <div className={`min-h-screen bg-background ${className}`}>
      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {sidebar ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content - 2/3 width on large screens */}
            <div className="lg:col-span-2 space-y-6">
              {children}
            </div>

            {/* Sidebar - 1/3 width on large screens */}
            <div className="lg:col-span-1 space-y-6">
              {sidebar}
            </div>
          </div>
        ) : (
          /* Full width when no sidebar */
          <div className="max-w-5xl mx-auto space-y-6">
            {children}
          </div>
        )}

        {/* Footer Section - Full width */}
        {footer && (
          <div className="mt-8">
            {footer}
          </div>
        )}
      </div>
    </div>
  )
}
