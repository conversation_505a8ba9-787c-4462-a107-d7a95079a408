'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { MapPin, TrendingUp, Clock, ArrowRight } from 'lucide-react'

interface FrequentArea {
  area: string
  count: number
  last_searched: string
  popular_projects: string[]
}

interface FrequentAreasProps {
  onAreaClick?: (area: string) => void
  className?: string
}

export function FrequentAreas({ onAreaClick, className = "" }: FrequentAreasProps) {
  const [frequentAreas, setFrequentAreas] = useState<FrequentArea[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadFrequentAreas()
  }, [])

  const loadFrequentAreas = async () => {
    try {
      const response = await fetch('/api/analytics/frequent-areas')
      if (response.ok) {
        const data = await response.json()
        setFrequentAreas(data.areas || [])
      }
    } catch (error) {
      console.error('Failed to load frequent areas:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAreaClick = (area: string) => {
    if (onAreaClick) {
      onAreaClick(area)
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Your Frequent Areas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (frequentAreas.length === 0) {
    return (
      <Card className={className}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-lg font-semibold">
            <MapPin className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
            Your Frequent Areas
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Areas you search most often will appear here
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <MapPin className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground font-medium mb-1">
              No frequent areas yet
            </p>
            <p className="text-sm text-muted-foreground">
              Search in different areas to see your patterns!
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center text-lg font-semibold">
          <MapPin className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
          Your Frequent Areas
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          Areas you search most often
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {frequentAreas.slice(0, 5).map((area, index) => (
            <div
              key={area.area}
              className="group flex items-center justify-between p-4 rounded-xl border border-border bg-card hover:bg-accent/50 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-primary-200 dark:hover:border-primary-800"
              onClick={() => handleAreaClick(area.area)}
            >
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-primary-600 dark:bg-primary-500 rounded-full group-hover:bg-primary-700 dark:group-hover:bg-primary-400 transition-colors shadow-sm">
                  <span className="text-sm font-bold text-white dark:text-slate-900">
                    {index + 1}
                  </span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground mb-1">{area.area}</h4>
                  <div className="flex items-center gap-2 flex-wrap mb-1">
                    <Badge variant="secondary" className="text-xs bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300">
                      {area.count} searches
                    </Badge>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      {new Date(area.last_searched).toLocaleDateString()}
                    </div>
                  </div>
                  {area.popular_projects.length > 0 && (
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="h-3 w-3 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        Popular: {area.popular_projects.slice(0, 2).join(', ')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900/20 dark:hover:text-primary-300"
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
