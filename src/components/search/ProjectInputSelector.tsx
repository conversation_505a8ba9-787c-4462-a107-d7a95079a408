'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { isFeatureEnabled } from '@/lib/tier-config'

const RULE_TYPES = [
  'fence', 'shed', 'deck', 'pool', 'driveway', 'mailbox',
  'tree removal', 'addition', 'garage', 'pergola', 'hot tub', 'solar panels',
  'hvac system', 'electrical work', 'plumbing', 'roofing',
  'siding', 'windows', 'doors', 'flooring', 'kitchen remodel',
  'bathroom remodel', 'basement finishing', 'attic conversion'
]

interface ProjectInputSelectorProps {
  value: string
  onChange: (value: string, projectType?: string, metadata?: Record<string, unknown>) => void
  disabled?: boolean
  className?: string
}

interface ParsedProject {
  projectType: string
  originalDescription: string
  confidence: number
  tags: string[]
  metadata?: Record<string, unknown>
}

export function ProjectInputSelector({
  value,
  onChange,
  disabled = false,
  className = ""
}: ProjectInputSelectorProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [customInput, setCustomInput] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Custom input is now available to all users
  const canUseCustomInput = isFeatureEnabled('CUSTOM_PROJECT_ENABLED')

  const handleCustomInputSubmit = async () => {
    if (!customInput.trim() || isLoading) return

    setIsLoading(true)
    try {
      // Parse custom project description with AI
      const response = await fetch('/api/parse-project-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description: customInput })
      })

      if (!response.ok) {
        throw new Error('Failed to parse project description')
      }

      const parsed: ParsedProject = await response.json()

      // Call onChange with parsed data
      onChange(parsed.projectType, parsed.projectType, {
        originalDescription: customInput,
        confidence: parsed.confidence,
        tags: parsed.tags,
        ...parsed.metadata
      })

    } catch (error) {
      console.error('Error parsing project description:', error)
      // Fallback to using the input as-is
      onChange(customInput, 'custom', {
        originalDescription: customInput,
        confidence: 0.5,
        tags: ['custom'],
        parseError: true
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setCustomInput(suggestion)
    onChange(suggestion)
    setShowSuggestions(false)
  }

  const handleInputFocus = () => {
    setShowSuggestions(true)
  }

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow clicks
    setTimeout(() => setShowSuggestions(false), 200)
  }

  // Filter suggestions based on user input
  const filteredSuggestions = customInput.length >= 1
    ? RULE_TYPES.filter(type =>
        type.toLowerCase().includes(customInput.toLowerCase())
      )
    : RULE_TYPES

  // Check if we're in compact mode (search bar)
  const isCompact = className.includes('h-10')

  if (isCompact) {
    // Compact mode for search bar - text input with suggestions
    return (
      <div className={`relative ${className}`}>
        <div className="w-full relative">
          {/* Main input field */}
          <input
            type="text"
            value={customInput || value}
            onChange={(e) => {
              const inputValue = e.target.value
              setCustomInput(inputValue)
              // Set the rule type to the typed value instead of clearing it
              onChange(inputValue)
            }}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && customInput.trim()) {
                handleCustomInputSubmit()
              }
            }}
            placeholder="Project type (deck, driveway, fence, etc.)"
            disabled={disabled || isLoading}
            className="w-full h-10 px-3 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-ring bg-background text-sm text-foreground font-medium"
          />

          {/* Suggestions dropdown - only show when user has typed at least 1 character */}
          {showSuggestions && customInput.length >= 1 && (
            <div className="absolute top-full left-0 right-0 mt-1 z-50 bg-background border border-border rounded-md shadow-lg max-h-48 overflow-y-auto">
              <div className="p-2">
                <p className="text-xs text-muted-foreground mb-2">Suggestions:</p>
                <div className="grid grid-cols-2 gap-1">
                  {filteredSuggestions.slice(0, 6).map((type) => (
                    <button
                      key={type}
                      onClick={() => handleSuggestionClick(type)}
                      className="text-left text-xs px-2 py-1 rounded hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </button>
                  ))}
                </div>
                {filteredSuggestions.length === 0 && (
                  <p className="text-xs text-muted-foreground">No matching suggestions. You can type any project type.</p>
                )}
                <button
                  onClick={() => setShowSuggestions(false)}
                  className="text-xs text-muted-foreground hover:text-foreground mt-2 w-full text-center"
                >
                  Hide suggestions
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Full mode for dedicated forms
  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          Project Description
        </label>
        <div className="flex space-x-2">
          <textarea
            value={customInput}
            onChange={(e) => setCustomInput(e.target.value)}
            placeholder="Describe your project in detail... e.g., 'I want to build a 6-foot privacy fence along my back property line'"
            disabled={disabled || isLoading}
            className="flex-1 p-3 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-ring min-h-[80px] resize-none bg-background text-foreground"
            rows={3}
          />
          <Button
            onClick={handleCustomInputSubmit}
            disabled={!customInput.trim() || disabled || isLoading}
            className="px-6 bg-primary hover:bg-primary/90 text-primary-foreground self-start"
          >
            {isLoading ? 'Analyzing...' : 'Analyze'}
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          ✨ AI will analyze your description and identify relevant regulations
        </p>
      </div>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-border" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-background text-muted-foreground">or choose from quick suggestions</span>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-2">
        {RULE_TYPES.slice(0, 12).map((type) => (
          <button
            key={type}
            onClick={() => handleSuggestionClick(type)}
            className="text-left text-sm px-3 py-2 border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </button>
        ))}
      </div>
    </div>
  )
}
