'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { UsageDisplay } from '@/components/ui/UsageDisplay'
import { AddressInput } from '@/components/address/AddressInput'
import { ProjectInputSelector } from '@/components/search/ProjectInputSelector'
import { CheckCircle, AlertTriangle, XCircle, Share2, Download } from 'lucide-react'
import { AddressData } from '@/lib/types/address'

type ComplianceStatus = 'allowed' | 'permit_required' | 'restricted'

interface SearchHeaderProps {
  selectedAddress: AddressData | null
  selectedRuleType: string
  isLoading: boolean
  error: string | null
  complianceStatus?: ComplianceStatus
  jurisdictionName?: string
  usageLimitReached?: boolean
  onAddressSelect: (address: AddressData | null) => void
  onRuleTypeChange: (ruleType: string, projectType?: string, metadata?: Record<string, unknown>) => void
  onSearch: () => void
  onShare?: () => void
  onDownload?: () => void
}

export function SearchHeader({
  selectedAddress,
  selectedRuleType,
  isLoading,
  error,
  complianceStatus,
  jurisdictionName,
  usageLimitReached = false,
  onAddressSelect,
  onRuleTypeChange,
  onSearch,
  onShare,
  onDownload
}: SearchHeaderProps) {
  const [isExpanded, setIsExpanded] = useState(!complianceStatus && !selectedAddress)

  const getStatusIcon = () => {
    switch (complianceStatus) {
      case 'allowed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'restricted':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'permit_required':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      default:
        return null
    }
  }

  const getStatusText = () => {
    switch (complianceStatus) {
      case 'allowed':
        return 'Allowed'
      case 'restricted':
        return 'Restricted'
      case 'permit_required':
        return 'Permit Required'
      default:
        return null
    }
  }

  const getStatusColor = () => {
    switch (complianceStatus) {
      case 'allowed':
        return 'bg-green-50 border-green-200'
      case 'restricted':
        return 'bg-red-50 border-red-200'
      case 'permit_required':
        return 'bg-yellow-50 border-yellow-200'
      default:
        return 'bg-white border-gray-200'
    }
  }

  return (
    <div className={`sticky top-0 z-50 border-b ${getStatusColor()} backdrop-blur-sm`}>
      <div className="max-w-7xl mx-auto px-4 py-4">
        {/* Main Header Row */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-gray-900">Property Compliance</h1>
            {complianceStatus && (
              <div className="flex items-center space-x-2">
                {getStatusIcon()}
                <Badge variant={complianceStatus === 'allowed' ? 'default' : 'secondary'}>
                  {getStatusText()}
                </Badge>
                {jurisdictionName && (
                  <span className="text-sm text-gray-600">• {jurisdictionName}</span>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {/* Usage Display */}
            <UsageDisplay className="hidden sm:block" />

            {complianceStatus && (
              <div className="flex items-center space-x-2">
                {onShare && (
                  <Button variant="outline" size="sm" onClick={onShare} data-testid="share-search-button">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                )}
                {onDownload && (
                  <Button variant="outline" size="sm" onClick={onDownload}>
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? 'Hide Search' : 'New Search'}
                </Button>
              </div>
            )}

            {!complianceStatus && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? 'Hide Search' : 'New Search'}
              </Button>
            )}
          </div>
        </div>

        {/* Search Form */}
        {isExpanded && (
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Property Address
                </label>
                <AddressInput
                  onAddressSelect={onAddressSelect}
                  placeholder="Enter property address..."
                  defaultValue={selectedAddress ? {
                    value: selectedAddress.label,
                    label: selectedAddress.label,
                    lat: selectedAddress.lat,
                    lng: selectedAddress.lng,
                    county: selectedAddress.county,
                    state: selectedAddress.state,
                    zip: selectedAddress.zip
                  } : undefined}
                />
                <div className="text-xs text-blue-600 mt-1">
                  💡 Recent suggestions will appear as you type
                </div>
              </div>

              <div>
                <ProjectInputSelector
                  value={selectedRuleType}
                  onChange={onRuleTypeChange}
                  disabled={isLoading}
                  className="h-full"
                />
              </div>

              <div className="flex items-end">
                <Button
                  onClick={onSearch}
                  disabled={!selectedAddress || !selectedRuleType || isLoading || usageLimitReached}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-400"
                  data-testid="submit-button"
                >
                  {isLoading ? 'Checking...' : usageLimitReached ? 'Limit Reached' : 'Check Compliance'}
                </Button>
              </div>
            </div>

            {error && (
              <div className="mt-4 p-4 bg-red-50 rounded-md border border-red-200" role="alert">
                <div className="flex items-start space-x-3">
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-sm text-red-800 font-medium mb-1">
                      {error.includes('limit') ? 'Usage Limit Reached' : 'Search Error'}
                    </p>
                    <p className="text-sm text-red-700">
                      {error}
                    </p>
                    {error.includes('limit') && (
                      <div className="mt-3 flex flex-col sm:flex-row gap-2">
                        <Button
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          onClick={() => window.location.href = '/pricing'}
                        >
                          Upgrade Plan
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.location.href = '/account'}
                        >
                          Buy More Searches
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Current Search Summary */}
        {!isExpanded && selectedAddress && selectedRuleType && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">{selectedAddress.label}</span> • {selectedRuleType}
          </div>
        )}
      </div>
    </div>
  )
}
