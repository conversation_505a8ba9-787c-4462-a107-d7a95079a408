'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Download, Share2, Camera, X, Loader2 } from 'lucide-react'
import { captureAndDownload, getCanvasDataUrl } from '@/lib/screenshot'

interface ScreenshotModeProps {
  isOpen: boolean
  onClose: () => void
  complianceData: {
    status: 'allowed' | 'restricted' | 'permit_required'
    summary: {
      summary: string
      confidence: number
    }
    address: string
    ruleType: string
    sources?: Array<{
      title: string
      url: string
    }>
  }
}

export default function ScreenshotMode({ isOpen, onClose, complianceData }: ScreenshotModeProps) {
  const [isCapturing, setIsCapturing] = useState(false)
  const [captureError, setCaptureError] = useState<string | null>(null)
  const cardRef = useRef<HTMLDivElement>(null)

  if (!isOpen) return null

  const getStatusColor = () => {
    switch (complianceData.status) {
      case 'allowed':
        return 'border-green-200 bg-green-50'
      case 'restricted':
        return 'border-red-200 bg-red-50'
      case 'permit_required':
        return 'border-yellow-200 bg-yellow-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getStatusText = () => {
    switch (complianceData.status) {
      case 'allowed':
        return 'Allowed'
      case 'restricted':
        return 'Restricted'
      case 'permit_required':
        return 'Permit Required'
      default:
        return 'Unknown'
    }
  }

  const getBadgeVariant = () => {
    switch (complianceData.status) {
      case 'allowed':
        return 'default' as const
      case 'restricted':
        return 'destructive' as const
      case 'permit_required':
        return 'secondary' as const
      default:
        return 'outline' as const
    }
  }

  const handleDownload = async () => {
    if (!cardRef.current) return

    setIsCapturing(true)
    setCaptureError(null)

    try {
      const filename = `ordrly-${complianceData.ruleType}-${complianceData.address.replace(/[^a-zA-Z0-9]/g, '-')}`

      await captureAndDownload(cardRef.current, {
        filename,
        format: 'png',
        quality: 0.9,
        addWatermark: true,
        addQRCode: true,
        qrCodeUrl: `${window.location.origin}?utm_source=screenshot&utm_medium=share&utm_content=${encodeURIComponent(complianceData.ruleType)}`
      })
    } catch (error) {
      console.error('Download failed:', error)
      setCaptureError('Failed to download image. Please try again.')
    } finally {
      setIsCapturing(false)
    }
  }

  const handleShare = async () => {
    if (!cardRef.current) return

    setIsCapturing(true)
    setCaptureError(null)

    try {
      const dataUrl = await getCanvasDataUrl(cardRef.current, {
        format: 'png',
        quality: 0.9,
        addWatermark: true,
        addQRCode: true,
        qrCodeUrl: `${window.location.origin}?utm_source=screenshot&utm_medium=share&utm_content=${encodeURIComponent(complianceData.ruleType)}`
      })

      // Convert data URL to blob for sharing
      const response = await fetch(dataUrl)
      const blob = await response.blob()

      const file = new File([blob], `ordrly-compliance-${complianceData.ruleType}.png`, {
        type: 'image/png'
      })

      if (navigator.share && navigator.canShare({ files: [file] })) {
        await navigator.share({
          title: `${complianceData.ruleType} Compliance - ${complianceData.address}`,
          text: `Check out this compliance summary from Ordrly: ${complianceData.summary.summary.substring(0, 100)}...`,
          files: [file]
        })
      } else {
        // Fallback: copy image to clipboard if supported
        if (navigator.clipboard && 'write' in navigator.clipboard) {
          await navigator.clipboard.write([
            new ClipboardItem({
              'image/png': blob
            })
          ])
          alert('Image copied to clipboard!')
        } else {
          // Final fallback: download
          await handleDownload()
        }
      }
    } catch (error) {
      console.error('Share failed:', error)
      setCaptureError('Failed to share image. Please try downloading instead.')
    } finally {
      setIsCapturing(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Share Compliance Card</h2>
              <p className="text-sm text-gray-600 mt-1">
                Download or share your compliance summary with embedded Ordrly branding
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {captureError && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{captureError}</p>
            </div>
          )}

          {/* Preview Card */}
          <div className="mb-6">
            <p className="text-sm font-medium text-gray-700 mb-3">Preview:</p>
            <div className="border-2 border-dashed border-gray-200 p-4 rounded-lg bg-gray-50">
              <Card
                ref={cardRef}
                className={`w-full max-w-md mx-auto ${getStatusColor()} shadow-lg`}
                style={{ backgroundColor: 'white' }}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Badge variant={getBadgeVariant()}>
                      {getStatusText()}
                    </Badge>
                    <div className="text-xs text-gray-500">
                      Confidence: {Math.round(complianceData.summary.confidence * 100)}%
                    </div>
                  </div>
                  <CardTitle className="text-lg">{complianceData.ruleType} Compliance</CardTitle>
                  <CardDescription className="text-sm">
                    {complianceData.address}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Summary</h4>
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {complianceData.summary.summary}
                      </p>
                    </div>

                    {complianceData.sources && complianceData.sources.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Sources</h4>
                        <div className="space-y-1">
                          {complianceData.sources.slice(0, 2).map((source, index) => (
                            <p key={index} className="text-xs text-gray-600 truncate">
                              • {source.title}
                            </p>
                          ))}
                          {complianceData.sources.length > 2 && (
                            <p className="text-xs text-gray-500">
                              +{complianceData.sources.length - 2} more sources
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="pt-2 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-gray-500">
                          Generated by Ordrly.com
                        </div>
                        <div className="text-xs text-gray-400">
                          {new Date().toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleDownload}
              disabled={isCapturing}
              className="flex-1"
            >
              {isCapturing ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              Download Image
            </Button>

            <Button
              onClick={handleShare}
              disabled={isCapturing}
              variant="outline"
              className="flex-1"
            >
              {isCapturing ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Share2 className="h-4 w-4 mr-2" />
              )}
              Share Image
            </Button>
          </div>

          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <Camera className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-blue-700">
                <p className="font-medium mb-1">What&apos;s included:</p>
                <ul className="space-y-1">
                  <li>• High-quality image optimized for social sharing</li>
                  <li>• Ordrly watermark and QR code for attribution</li>
                  <li>• Mobile-friendly format for easy sharing</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
