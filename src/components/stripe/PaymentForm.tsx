'use client'

import { useState } from 'react'
import { useStripe, useElements, PaymentElement, AddressElement } from '@stripe/react-stripe-js'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, CreditCard, Shield } from 'lucide-react'

interface PaymentIntent {
  id: string
  status: string
}

interface PaymentFormProps {
  onSuccess?: (paymentIntent: PaymentIntent) => void
  onError?: (error: string) => void
  submitText?: string
  isLoading?: boolean
  showBillingAddress?: boolean
}

export function PaymentForm({
  onSuccess,
  onError,
  submitText = 'Complete Payment',
  isLoading: externalLoading = false,
  showBillingAddress = true
}: PaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  const isLoading = isProcessing || externalLoading

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      setErrorMessage('Stripe has not loaded yet. Please try again.')
      return
    }

    setIsProcessing(true)
    setErrorMessage(null)

    try {
      // Confirm the payment
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        redirect: 'if_required',
      })

      if (error) {
        console.error('Payment confirmation error:', error)
        setErrorMessage(error.message || 'An unexpected error occurred.')
        onError?.(error.message || 'Payment failed')
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        console.log('Payment succeeded:', paymentIntent)
        onSuccess?.(paymentIntent)
      } else {
        console.log('Payment status:', paymentIntent?.status)
        setErrorMessage('Payment was not completed. Please try again.')
        onError?.('Payment not completed')
      }
    } catch (err) {
      console.error('Payment processing error:', err)
      setErrorMessage('An unexpected error occurred. Please try again.')
      onError?.('Unexpected error')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="w-5 h-5 mr-2" />
          Payment Information
        </CardTitle>
        <CardDescription>
          Enter your payment details to complete your purchase
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Payment Element */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm text-gray-700 dark:text-gray-300 uppercase tracking-wide">
              Payment Method
            </h3>
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <PaymentElement
                options={{
                  layout: 'tabs',
                  paymentMethodOrder: ['card', 'apple_pay', 'google_pay'],
                }}
              />
            </div>
          </div>

          {/* Billing Address */}
          {showBillingAddress && (
            <div className="space-y-4">
              <h3 className="font-medium text-sm text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                Billing Address
              </h3>
              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <AddressElement
                  options={{
                    mode: 'billing',
                    allowedCountries: ['US'],
                  }}
                />
              </div>
            </div>
          )}

          {/* Error Message */}
          {errorMessage && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">{errorMessage}</p>
            </div>
          )}

          {/* Security Notice */}
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
            <Shield className="h-4 w-4 mr-2 text-blue-600" />
            <span className="text-blue-800 dark:text-blue-200">
              Your payment information is encrypted and secure. Powered by Stripe.
            </span>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            size="lg"
            disabled={!stripe || isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              submitText
            )}
          </Button>

          <p className="text-xs text-gray-500 text-center">
            By completing this payment, you agree to our{' '}
            <a href="/terms" className="text-blue-600 hover:underline">Terms of Service</a>
            {' '}and{' '}
            <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a>.
          </p>
        </form>
      </CardContent>
    </Card>
  )
}
