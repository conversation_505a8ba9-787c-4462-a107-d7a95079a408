'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Clock, Zap, ArrowRight, X } from 'lucide-react'
import { getTrialStatus, getTrialUsageStats } from '@/lib/trial-management'
import { UpgradeButton } from '@/components/billing/UpgradeButton'
import { createClient } from '@/lib/supabase/client'
import type { TrialStatus } from '@/lib/trial-management'

interface TrialBannerProps {
  className?: string
  variant?: 'banner' | 'card' | 'minimal'
  showUsage?: boolean
  dismissible?: boolean
}

export function TrialBanner({ 
  className = '', 
  variant = 'banner',
  showUsage = true,
  dismissible = false 
}: TrialBannerProps) {
  const [trialStatus, setTrialStatus] = useState<TrialStatus | null>(null)
  const [usageStats, setUsageStats] = useState<{
    callsUsed: number
    callsRemaining: number
    usagePercentage: number
  } | null>(null)
  const [isVisible, setIsVisible] = useState(true)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadTrialData() {
      try {
        const supabase = createClient()
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          setLoading(false)
          return
        }

        const [status, usage] = await Promise.all([
          getTrialStatus(user.id),
          showUsage ? getTrialUsageStats(user.id) : Promise.resolve(null)
        ])

        setTrialStatus(status)
        setUsageStats(usage)
      } catch (error) {
        console.error('Error loading trial data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadTrialData()
  }, [showUsage])

  if (loading || !isVisible || !trialStatus?.isOnTrial) {
    return null
  }

  const getUrgencyColor = () => {
    if (trialStatus.daysRemaining <= 1) return 'text-red-500'
    if (trialStatus.daysRemaining <= 3) return 'text-orange-500'
    return 'text-green-500'
  }

  const getUsageColor = () => {
    if (!usageStats) return 'text-blue-500'
    if (usageStats.usagePercentage >= 90) return 'text-red-500'
    if (usageStats.usagePercentage >= 70) return 'text-orange-500'
    return 'text-green-500'
  }

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center gap-2 text-sm ${className}`}>
        <Clock className={`w-4 h-4 ${getUrgencyColor()}`} />
        <span className="text-muted-foreground">
          Trial: {trialStatus.daysRemaining} days left
        </span>
        {usageStats && (
          <span className={`${getUsageColor()}`}>
            ({usageStats.callsRemaining} calls remaining)
          </span>
        )}
      </div>
    )
  }

  if (variant === 'card') {
    return (
      <Card className={`border-l-4 border-l-blue-500 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center">
                <Zap className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">Free Trial Active</h3>
                <p className="text-sm text-muted-foreground">
                  {trialStatus.daysRemaining} days remaining
                  {usageStats && ` • ${usageStats.callsRemaining} calls left`}
                </p>
              </div>
            </div>
            <UpgradeButton planType="starter" className="bg-blue-600 hover:bg-blue-700">
              Upgrade Now
            </UpgradeButton>
          </div>
          {showUsage && usageStats && (
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span className="text-muted-foreground">API Calls Used</span>
                <span className={getUsageColor()}>
                  {usageStats.callsUsed} / 1,000
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    usageStats.usagePercentage >= 90 ? 'bg-red-500' :
                    usageStats.usagePercentage >= 70 ? 'bg-orange-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(100, usageStats.usagePercentage)}%` }}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  // Default banner variant
  return (
    <div className={`bg-gradient-to-r from-blue-600 to-blue-700 text-white ${className}`}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span className="font-semibold">
                Free Trial: {trialStatus.daysRemaining} day{trialStatus.daysRemaining !== 1 ? 's' : ''} left
              </span>
            </div>
            {showUsage && usageStats && (
              <div className="hidden sm:flex items-center gap-2 text-blue-100">
                <Zap className="w-4 h-4" />
                <span className="text-sm">
                  {usageStats.callsRemaining} API calls remaining
                </span>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-3">
            <UpgradeButton 
              planType={trialStatus.suggestedUpgrade as any || 'starter'} 
              className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-4 py-2 text-sm"
            >
              Upgrade Now
              <ArrowRight className="w-4 h-4 ml-1" />
            </UpgradeButton>
            
            {dismissible && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
                className="text-white hover:bg-white/10 p-1"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
        
        {showUsage && usageStats && (
          <div className="mt-2 sm:hidden">
            <div className="flex justify-between text-sm text-blue-100 mb-1">
              <span>API Calls Used</span>
              <span>{usageStats.callsUsed} / 1,000</span>
            </div>
            <div className="w-full bg-blue-800 rounded-full h-1.5">
              <div
                className="bg-white h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, usageStats.usagePercentage)}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Hook for easy trial status access
export function useTrialStatus() {
  const [trialStatus, setTrialStatus] = useState<TrialStatus | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadTrialStatus() {
      try {
        const supabase = createClient()
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          setLoading(false)
          return
        }

        const status = await getTrialStatus(user.id)
        setTrialStatus(status)
      } catch (error) {
        console.error('Error loading trial status:', error)
      } finally {
        setLoading(false)
      }
    }

    loadTrialStatus()
  }, [])

  return { trialStatus, loading }
}
