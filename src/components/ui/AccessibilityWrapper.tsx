'use client'

import { useEffect, useRef } from 'react'

interface AccessibilityWrapperProps {
  children: React.ReactNode
  className?: string
  role?: string
  ariaLabel?: string
  ariaLabelledBy?: string
  ariaDescribedBy?: string
  tabIndex?: number
  focusOnMount?: boolean
  skipLink?: boolean
  landmark?: 'main' | 'navigation' | 'banner' | 'contentinfo' | 'complementary' | 'search' | 'form'
}

export function AccessibilityWrapper({
  children,
  className = "",
  role,
  ariaLabel,
  ariaLabelledBy,
  ariaDescribedBy,
  tabIndex,
  focusOnMount = false,
  skipLink = false,
  landmark
}: AccessibilityWrapperProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (focusOnMount && ref.current) {
      ref.current.focus()
    }
  }, [focusOnMount])

  const props = {
    ref,
    className,
    role: landmark || role,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledBy,
    'aria-describedby': ariaDescribedBy,
    tabIndex
  }

  // Remove undefined props
  Object.keys(props).forEach(key => {
    if (props[key as keyof typeof props] === undefined) {
      delete props[key as keyof typeof props]
    }
  })

  return (
    <>
      {skipLink && (
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50"
        >
          Skip to main content
        </a>
      )}
      <div {...props}>
        {children}
      </div>
    </>
  )
}

// Specialized components for common accessibility patterns
export function ScreenReaderOnly({ children }: { children: React.ReactNode }) {
  return <span className="sr-only">{children}</span>
}

export function FocusTrap({ children, active = true }: { children: React.ReactNode; active?: boolean }) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!active || !containerRef.current) return

    const container = containerRef.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus()
          e.preventDefault()
        }
      }
    }

    container.addEventListener('keydown', handleTabKey)
    return () => container.removeEventListener('keydown', handleTabKey)
  }, [active])

  return (
    <div ref={containerRef}>
      {children}
    </div>
  )
}

export function LiveRegion({ 
  children, 
  politeness = 'polite' 
}: { 
  children: React.ReactNode
  politeness?: 'off' | 'polite' | 'assertive'
}) {
  return (
    <div aria-live={politeness} aria-atomic="true" className="sr-only">
      {children}
    </div>
  )
}

// Hook for managing focus
export function useFocusManagement() {
  const previousFocus = useRef<HTMLElement | null>(null)

  const saveFocus = () => {
    previousFocus.current = document.activeElement as HTMLElement
  }

  const restoreFocus = () => {
    if (previousFocus.current) {
      previousFocus.current.focus()
    }
  }

  const focusElement = (element: HTMLElement | null) => {
    if (element) {
      element.focus()
    }
  }

  return { saveFocus, restoreFocus, focusElement }
}

// Hook for keyboard navigation
export function useKeyboardNavigation(
  onEscape?: () => void,
  onEnter?: () => void,
  onArrowUp?: () => void,
  onArrowDown?: () => void,
  onArrowLeft?: () => void,
  onArrowRight?: () => void
) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Escape':
        onEscape?.()
        break
      case 'Enter':
        onEnter?.()
        break
      case 'ArrowUp':
        e.preventDefault()
        onArrowUp?.()
        break
      case 'ArrowDown':
        e.preventDefault()
        onArrowDown?.()
        break
      case 'ArrowLeft':
        e.preventDefault()
        onArrowLeft?.()
        break
      case 'ArrowRight':
        e.preventDefault()
        onArrowRight?.()
        break
    }
  }

  return { handleKeyDown }
}
