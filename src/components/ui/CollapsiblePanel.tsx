'use client'

import { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown } from 'lucide-react'

interface CollapsiblePanelProps {
  title: string
  children: React.ReactNode
  defaultExpanded?: boolean
  className?: string
  headerClassName?: string
  contentClassName?: string
  icon?: React.ReactNode
  badge?: React.ReactNode
  disabled?: boolean
  onToggle?: (expanded: boolean) => void
}

export function CollapsiblePanel({
  title,
  children,
  defaultExpanded = false,
  className = "",
  headerClassName = "",
  contentClassName = "",
  icon,
  badge,
  disabled = false,
  onToggle
}: CollapsiblePanelProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const contentRef = useRef<HTMLDivElement>(null)

  const handleToggle = () => {
    if (disabled) return

    const newExpanded = !isExpanded
    setIsExpanded(newExpanded)
    onToggle?.(newExpanded)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleToggle()
    }
  }

  return (
    <div className={`border border-border rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <motion.button
        className={`
          w-full px-4 py-3 text-left bg-card hover:bg-muted/50
          transition-colors duration-200 focus:outline-none focus:ring-2
          focus:ring-primary focus:ring-inset
          ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
          ${headerClassName}
        `}
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        aria-expanded={isExpanded}
        aria-controls="panel-content"
        whileHover={disabled ? undefined : undefined}
        whileTap={disabled ? undefined : { scale: 0.995 }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {icon && (
              <motion.div
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="flex-shrink-0"
              >
                {icon}
              </motion.div>
            )}
            <h3 className="font-medium text-card-foreground">{title}</h3>
            {badge && <div className="flex-shrink-0">{badge}</div>}
          </div>

          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="flex-shrink-0 text-muted-foreground"
          >
            <ChevronDown className="h-5 w-5" />
          </motion.div>
        </div>
      </motion.button>

      {/* Content */}
      <AnimatePresence initial={false}>
        {isExpanded && (
          <motion.div
            id="panel-content"
            ref={contentRef}
            initial={{ height: 0, opacity: 0 }}
            animate={{
              height: 'auto',
              opacity: 1,
              transition: {
                height: {
                  duration: 0.3,
                  ease: [0.04, 0.62, 0.23, 0.98]
                },
                opacity: {
                  duration: 0.2,
                  delay: 0.1
                }
              }
            }}
            exit={{
              height: 0,
              opacity: 0,
              transition: {
                height: {
                  duration: 0.3,
                  ease: [0.04, 0.62, 0.23, 0.98]
                },
                opacity: {
                  duration: 0.2
                }
              }
            }}
            className="overflow-hidden"
          >
            <motion.div
              initial={{ y: -10 }}
              animate={{ y: 0 }}
              exit={{ y: -10 }}
              transition={{ duration: 0.2, delay: 0.1 }}
              className={`px-4 py-3 bg-muted/30 border-t border-border ${contentClassName}`}
            >
              {children}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Preset variants for common use cases
export function InfoPanel({ children, ...props }: Omit<CollapsiblePanelProps, 'icon'>) {
  return (
    <CollapsiblePanel
      {...props}
      icon={<ChevronDown className="h-4 w-4 text-primary" />}
      headerClassName="bg-primary/5 hover:bg-primary/10"
    >
      {children}
    </CollapsiblePanel>
  )
}

export function WarningPanel({ children, ...props }: Omit<CollapsiblePanelProps, 'icon'>) {
  return (
    <CollapsiblePanel
      {...props}
      icon={<ChevronDown className="h-4 w-4 text-warning" />}
      headerClassName="bg-warning/5 hover:bg-warning/10"
    >
      {children}
    </CollapsiblePanel>
  )
}

export function ErrorPanel({ children, ...props }: Omit<CollapsiblePanelProps, 'icon'>) {
  return (
    <CollapsiblePanel
      {...props}
      icon={<ChevronDown className="h-4 w-4 text-error" />}
      headerClassName="bg-error/5 hover:bg-error/10"
    >
      {children}
    </CollapsiblePanel>
  )
}
