import { ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface EmptyStateProps {
  icon?: ReactNode
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline' | 'ghost'
  }
  className?: string
}

export function EmptyState({ 
  icon, 
  title, 
  description, 
  action, 
  className 
}: EmptyStateProps) {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center text-center py-12 px-4',
      className
    )}>
      {icon && (
        <div className="mb-4 text-gray-400 dark:text-gray-600">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-semibold text-foreground mb-2">
        {title}
      </h3>
      <p className="text-muted-foreground mb-6 max-w-md">
        {description}
      </p>
      {action && (
        <Button 
          onClick={action.onClick}
          variant={action.variant || 'default'}
        >
          {action.label}
        </Button>
      )}
    </div>
  )
}

interface NoResultsProps {
  searchQuery?: string
  onClearSearch?: () => void
  onNewSearch?: () => void
}

export function NoSearchResults({ 
  searchQuery, 
  onClearSearch, 
  onNewSearch 
}: NoResultsProps) {
  return (
    <EmptyState
      icon={<div className="text-6xl">🔍</div>}
      title="No results found"
      description={
        searchQuery 
          ? `We couldn't find any results for "${searchQuery}". Try adjusting your search terms or filters.`
          : "No results match your current search criteria."
      }
      action={
        onClearSearch ? {
          label: "Clear search",
          onClick: onClearSearch,
          variant: "outline"
        } : onNewSearch ? {
          label: "Start new search",
          onClick: onNewSearch
        } : undefined
      }
    />
  )
}

export function NoHistoryState({ onStartSearch }: { onStartSearch: () => void }) {
  return (
    <EmptyState
      icon={<div className="text-6xl">📋</div>}
      title="No search history yet"
      description="Your search history will appear here once you start checking property compliance rules."
      action={{
        label: "Start your first search",
        onClick: onStartSearch
      }}
    />
  )
}

export function NoFAQResults({ onClearSearch }: { onClearSearch: () => void }) {
  return (
    <EmptyState
      icon={<div className="text-6xl">❓</div>}
      title="No FAQs found"
      description="We couldn't find any FAQs matching your search. Try different keywords or browse all categories."
      action={{
        label: "Clear search",
        onClick: onClearSearch,
        variant: "outline"
      }}
    />
  )
}

export function NoComplianceData() {
  return (
    <EmptyState
      icon={<div className="text-6xl">🏠</div>}
      title="Ready to check compliance"
      description="Enter an address and project type above to get fast compliance information for your property."
      className="min-h-[400px]"
    />
  )
}

export function ErrorState({ 
  title = "Something went wrong",
  description = "We encountered an error while processing your request. Please try again.",
  onRetry
}: {
  title?: string
  description?: string
  onRetry?: () => void
}) {
  return (
    <EmptyState
      icon={<div className="text-6xl text-red-500">⚠️</div>}
      title={title}
      description={description}
      action={onRetry ? {
        label: "Try again",
        onClick: onRetry
      } : undefined}
    />
  )
}
