'use client'

import { motion } from 'framer-motion'
import { Alert<PERSON>riangle, CheckCircle, XCircle, Info } from 'lucide-react'

interface KeyRuleBadgeProps {
  type: 'critical' | 'warning' | 'success' | 'info'
  title: string
  description?: string
  className?: string
  animate?: boolean
  onClick?: () => void
}

const badgeConfig = {
  critical: {
    icon: XCircle,
    bgColor: 'bg-error-50 dark:bg-error-950',
    borderColor: 'border-error-200 dark:border-error-800',
    textColor: 'text-error-900 dark:text-error-100',
    iconColor: 'text-error-600 dark:text-error-400',
    pulseColor: 'shadow-error-400/50'
  },
  warning: {
    icon: AlertTriangle,
    bgColor: 'bg-warning-50 dark:bg-warning-950',
    borderColor: 'border-warning-200 dark:border-warning-800',
    textColor: 'text-warning-900 dark:text-warning-100',
    iconColor: 'text-warning-600 dark:text-warning-400',
    pulseColor: 'shadow-warning-400/50'
  },
  success: {
    icon: CheckCircle,
    bgColor: 'bg-success-50 dark:bg-success-950',
    borderColor: 'border-success-200 dark:border-success-800',
    textColor: 'text-success-900 dark:text-success-100',
    iconColor: 'text-success-600 dark:text-success-400',
    pulseColor: 'shadow-success-400/50'
  },
  info: {
    icon: Info,
    bgColor: 'bg-info-50 dark:bg-info-950',
    borderColor: 'border-info-200 dark:border-info-800',
    textColor: 'text-info-900 dark:text-info-100',
    iconColor: 'text-info-600 dark:text-info-400',
    pulseColor: 'shadow-info-400/50'
  }
}

export function KeyRuleBadge({
  type,
  title,
  description,
  className = "",
  animate = true,
  onClick
}: KeyRuleBadgeProps) {
  const config = badgeConfig[type]
  const Icon = config.icon

  const pulseAnimation = {
    scale: [1, 1.05, 1],
    boxShadow: [
      '0 0 0 0 rgba(0, 0, 0, 0)',
      `0 0 0 8px ${config.pulseColor}`,
      '0 0 0 0 rgba(0, 0, 0, 0)'
    ]
  }

  const hoverAnimation = {
    scale: 1.02,
    y: -2
  }

  return (
    <motion.div
      className={`
        relative rounded-xl border-2 p-4 cursor-pointer transition-all duration-200
        ${config.bgColor} ${config.borderColor} ${className}
        ${onClick ? 'hover:shadow-lg' : ''}
      `}
      initial={animate ? { opacity: 0, y: 20 } : false}
      animate={animate ? { opacity: 1, y: 0 } : false}
      whileHover={onClick ? hoverAnimation : undefined}
      whileTap={onClick ? { scale: 0.98 } : undefined}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          onClick()
        }
      } : undefined}
      aria-label={onClick ? `Key rule: ${title}` : undefined}
    >
      {/* Pulse animation overlay */}
      {animate && (
        <motion.div
          className="absolute inset-0 rounded-xl"
          animate={pulseAnimation}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatDelay: 3,
            ease: "easeInOut"
          }}
        />
      )}

      <div className="relative flex items-start space-x-3">
        {/* Icon with subtle animation */}
        <motion.div
          className={`flex-shrink-0 ${config.iconColor}`}
          animate={animate ? {
            rotate: [0, 5, -5, 0],
          } : undefined}
          transition={{
            duration: 4,
            repeat: Infinity,
            repeatDelay: 2,
            ease: "easeInOut"
          }}
        >
          <Icon className="h-6 w-6" />
        </motion.div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className={`font-semibold text-sm ${config.textColor}`}>
            {title}
          </h3>
          {description && (
            <p className={`mt-1 text-xs ${config.textColor} opacity-80`}>
              {description}
            </p>
          )}
        </div>

        {/* Key indicator */}
        <div className={`flex-shrink-0 ${config.textColor} opacity-60`}>
          <div className="text-xs font-medium">KEY</div>
        </div>
      </div>

      {/* Accessibility indicator for screen readers */}
      <span className="sr-only">
        {type === 'critical' ? 'Critical rule' : 
         type === 'warning' ? 'Warning rule' :
         type === 'success' ? 'Allowed rule' : 'Information rule'}
      </span>
    </motion.div>
  )
}
