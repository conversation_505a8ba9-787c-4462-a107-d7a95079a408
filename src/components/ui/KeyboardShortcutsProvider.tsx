'use client'

import { ReactNode, useEffect } from 'react'
import { useKeyboardShortcuts } from '@/lib/keyboard-shortcuts'

interface KeyboardShortcutsProviderProps {
  children: ReactNode
}

export function KeyboardShortcutsProvider({ children }: KeyboardShortcutsProviderProps) {
  // Initialize global keyboard shortcuts
  useKeyboardShortcuts()

  // Initialize keyboard shortcuts without UI indicator
  // (UI is now handled by the HelpWidget)

  return <>{children}</>
}
