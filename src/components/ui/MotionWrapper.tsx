'use client'

import { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

interface MotionWrapperProps {
  children: React.ReactNode
  className?: string
  animation?: string
  reduceMotion?: boolean
}

export function MotionWrapper({ 
  children, 
  className, 
  animation = '',
  reduceMotion = false 
}: MotionWrapperProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    // Check for user's motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  const shouldReduceMotion = prefersReducedMotion || reduceMotion

  return (
    <div 
      className={cn(
        className,
        !shouldReduceMotion && animation,
        shouldReduceMotion && 'reduce-motion'
      )}
      data-reduce-motion={shouldReduceMotion}
    >
      {children}
    </div>
  )
}

// Hook to check for reduced motion preference
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

// Higher-order component for motion-aware components
export function withMotionSupport<T extends Record<string, unknown>>(
  Component: React.ComponentType<T>
) {
  return function MotionAwareComponent(props: T) {
    const prefersReducedMotion = useReducedMotion()
    
    return (
      <Component 
        {...props} 
        prefersReducedMotion={prefersReducedMotion}
      />
    )
  }
}
