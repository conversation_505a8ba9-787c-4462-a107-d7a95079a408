'use client'

import { ReactNode, TouchEvent, useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface TouchOptimizedButtonProps {
  children: ReactNode
  onClick?: () => void
  className?: string
  disabled?: boolean
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
}

export function TouchOptimizedButton({
  children,
  onClick,
  className,
  disabled = false,
  variant = 'default',
  size = 'md'
}: TouchOptimizedButtonProps) {
  const [isPressed, setIsPressed] = useState(false)

  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'

  const variantClasses = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground active:bg-accent/80'
  }

  const sizeClasses = {
    sm: 'h-9 px-3 text-sm min-w-[44px]', // Minimum 44px for touch targets
    md: 'h-11 px-4 py-2 min-w-[44px]',
    lg: 'h-12 px-6 py-3 text-lg min-w-[48px]'
  }

  const handleTouchStart = () => {
    if (!disabled) {
      setIsPressed(true)
    }
  }

  const handleTouchEnd = () => {
    setIsPressed(false)
    if (!disabled && onClick) {
      onClick()
    }
  }

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        isPressed && 'scale-95',
        className
      )}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onClick={onClick}
      disabled={disabled}
      style={{ WebkitTapHighlightColor: 'transparent' }}
    >
      {children}
    </button>
  )
}

interface SwipeableCardProps {
  children: ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  className?: string
  threshold?: number
}

export function SwipeableCard({
  children,
  onSwipeLeft,
  onSwipeRight,
  className,
  threshold = 100
}: SwipeableCardProps) {
  const [startX, setStartX] = useState(0)
  const [currentX, setCurrentX] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: TouchEvent) => {
    setStartX(e.touches[0].clientX)
    setCurrentX(e.touches[0].clientX)
    setIsDragging(true)
  }

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging) return
    setCurrentX(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (!isDragging) return

    const deltaX = currentX - startX

    if (Math.abs(deltaX) > threshold) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    }

    setIsDragging(false)
    setStartX(0)
    setCurrentX(0)
  }

  const translateX = isDragging ? currentX - startX : 0

  return (
    <div
      ref={cardRef}
      className={cn('touch-pan-y', className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        transform: `translateX(${translateX}px)`,
        transition: isDragging ? 'none' : 'transform 0.3s ease-out',
        WebkitTapHighlightColor: 'transparent'
      }}
    >
      {children}
    </div>
  )
}

interface PullToRefreshProps {
  children: ReactNode
  onRefresh: () => Promise<void>
  className?: string
  threshold?: number
}

export function PullToRefresh({
  children,
  onRefresh,
  className,
  threshold = 80
}: PullToRefreshProps) {
  const [startY, setStartY] = useState(0)
  const [currentY, setCurrentY] = useState(0)
  const [isPulling, setIsPulling] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: TouchEvent) => {
    if (containerRef.current?.scrollTop === 0) {
      setStartY(e.touches[0].clientY)
      setCurrentY(e.touches[0].clientY)
    }
  }

  const handleTouchMove = (e: TouchEvent) => {
    if (startY === 0 || containerRef.current?.scrollTop !== 0) return

    const deltaY = e.touches[0].clientY - startY
    if (deltaY > 0) {
      e.preventDefault()
      setCurrentY(e.touches[0].clientY)
      setIsPulling(deltaY > threshold)
    }
  }

  const handleTouchEnd = async () => {
    if (isPulling && !isRefreshing) {
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setIsRefreshing(false)
      }
    }

    setStartY(0)
    setCurrentY(0)
    setIsPulling(false)
  }

  const pullDistance = Math.max(0, currentY - startY)
  const pullProgress = Math.min(pullDistance / threshold, 1)

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-auto', className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{ WebkitOverflowScrolling: 'touch' }}
    >
      {/* Pull to refresh indicator */}
      {(isPulling || isRefreshing) && (
        <div
          className="absolute top-0 left-0 right-0 flex items-center justify-center bg-primary/10 transition-all duration-300"
          style={{
            height: `${Math.min(pullDistance, threshold)}px`,
            transform: `translateY(-${threshold - pullDistance}px)`
          }}
        >
          <div className="flex items-center space-x-2 text-primary">
            <div
              className={cn(
                'w-4 h-4 border-2 border-current border-t-transparent rounded-full',
                isRefreshing && 'animate-spin'
              )}
              style={{
                transform: `rotate(${pullProgress * 360}deg)`
              }}
            />
            <span className="text-sm font-medium">
              {isRefreshing ? 'Refreshing...' : isPulling ? 'Release to refresh' : 'Pull to refresh'}
            </span>
          </div>
        </div>
      )}

      <div
        style={{
          transform: `translateY(${Math.min(pullDistance, threshold)}px)`,
          transition: isPulling ? 'none' : 'transform 0.3s ease-out'
        }}
      >
        {children}
      </div>
    </div>
  )
}

// Hook for detecting mobile device
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)

    return () => window.removeEventListener('resize', checkIsMobile)
  })

  return isMobile
}
