'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { PullPackButton } from '@/components/billing/UpgradeButton'
import Link from 'next/link'

interface UsageDisplayProps {
  className?: string
}

interface ProfileData {
  subscription_tier: string
  pulls_this_month: number
  extra_credits: number
}

export function UsageDisplay({ className }: UsageDisplayProps) {
  const [usage, setUsage] = useState<ProfileData | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const fetchUsage = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser()

        if (error) {
          // Don't log AuthSessionMissingError as it's expected when not logged in
          if (error.name !== 'AuthSessionMissingError') {
            console.error('UsageDisplay: Error getting user:', error)
          }
          setLoading(false)
          return
        }

        if (!user) {
          setLoading(false)
          return
        }

        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()

        setUsage(profile)
        setLoading(false)
      } catch (error: unknown) {
        // Handle AuthSessionMissingError gracefully
        if (error && typeof error === 'object' && 'name' in error && error.name === 'AuthSessionMissingError') {
          console.log('UsageDisplay: No auth session found (user not logged in)')
        } else {
          console.error('UsageDisplay: Error in fetchUsage:', error)
        }
        setLoading(false)
      }
    }

    fetchUsage()
  }, [supabase])

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-32"></div>
      </div>
    )
  }

  if (!usage) {
    return (
      <div className={className}>
        <Link href="/login">
          <Button size="sm" variant="outline">
            Sign in to track usage
          </Button>
        </Link>
      </div>
    )
  }

  // Calculate usage stats based on subscription tier
  const getUsageStats = () => {
    const tier = usage.subscription_tier
    let limit = 0

    switch (tier) {
      case 'trial':
        limit = 500
        break
      case 'starter':
        limit = 500
        break
      case 'professional':
        limit = 2000
        break
      case 'business':
        return { limit: -1, totalAvailable: -1, remaining: -1, percentage: 0, isUnlimited: true }
      default:
        limit = 500 // Default to trial limit
    }

    const totalAvailable = limit + (usage.extra_credits || 0)
    const remaining = totalAvailable - usage.pulls_this_month
    const percentage = Math.min((usage.pulls_this_month / totalAvailable) * 100, 100)

    return { limit, totalAvailable, remaining, percentage, isUnlimited: false }
  }

  const usageStats = getUsageStats()
  const { totalAvailable, remaining, percentage: usagePercentage, isUnlimited } = usageStats

  return (
    <div className={`space-y-2 ${className}`}>
      {!isUnlimited ? (
        <div>
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Messages this month</span>
            <span className="font-medium">
              {usage.pulls_this_month} / {totalAvailable}
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
            ></div>
          </div>

          <p className="text-xs text-gray-600 mt-1">
            {usage.subscription_tier === 'free' && 'Free tier: 5 messages per month'}
            {usage.subscription_tier === 'trial' && 'Trial: 500 messages'}
            {usage.subscription_tier === 'starter' && 'Starter: 500 messages per month'}
            {usage.subscription_tier === 'professional' && 'Professional: 2,000 messages per month'}
          </p>

          {usage.extra_credits > 0 && (
            <p className="text-xs text-green-600 mt-1">
              + {usage.extra_credits} bonus messages
            </p>
          )}

          {remaining <= 0 && (
            <div className="mt-2 space-y-2">
              <p className="text-xs text-red-600">
                Monthly limit reached
              </p>
              <div className="flex gap-2">
                <PullPackButton className="text-xs h-7 px-2" />
                <Link href="/pricing">
                  <Button size="sm" className="text-xs h-7 px-2">
                    Upgrade
                  </Button>
                </Link>
              </div>
            </div>
          )}

          {remaining > 0 && remaining <= (usage.subscription_tier === 'professional' ? 200 : 50) && (
            <div className="mt-2">
              <p className="text-xs text-yellow-600">
                {remaining} message{remaining === 1 ? '' : 's'} remaining
              </p>
              <Link href="/pricing">
                <Button size="sm" variant="outline" className="text-xs h-7 px-2 mt-1">
                  {usage.subscription_tier === 'starter' ? 'Upgrade to Professional' : 'Upgrade Plan'}
                </Button>
              </Link>
            </div>
          )}
        </div>
      ) : (
        <div>
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Messages this month</span>
            <span className="font-medium">{usage.pulls_this_month}</span>
          </div>
          <p className="text-xs text-green-600 mt-1">
            ✓ Unlimited messages ({usage.subscription_tier} plan)
          </p>
        </div>
      )}
    </div>
  )
}
