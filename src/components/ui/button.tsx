import * as React from "react"

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', ...props }, ref) => {
    const baseClasses = "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed btn-hover interactive cursor-pointer"

    const variants = {
      default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md",
      destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",
      outline: "border border-border bg-card hover:bg-muted/50 text-card-foreground shadow-sm hover:shadow-md",
      secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm hover:shadow-md",
      ghost: "hover:bg-muted/50 text-card-foreground",
      link: "text-primary underline-offset-4 hover:underline",
    }

    const sizes = {
      default: "h-11 px-4 py-2 min-w-[44px]", // Increased from h-10 to h-11 for better touch targets
      sm: "h-10 rounded-md px-3 min-w-[44px]", // Increased from h-9 to h-10
      lg: "h-12 rounded-md px-8 min-w-[48px]", // Increased from h-11 to h-12
      icon: "h-11 w-11 min-w-[44px]", // Increased from h-10 w-10 to h-11 w-11
    }

    const variantClass = variants[variant]
    const sizeClass = sizes[size]

    return (
      <button
        className={`${baseClasses} ${variantClass} ${sizeClass} ${className || ''}`}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button }
