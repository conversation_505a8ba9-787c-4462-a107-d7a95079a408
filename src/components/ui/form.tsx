'use client'

import * as React from 'react'
import { AlertCircle, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface FormFieldProps {
  children: React.ReactNode
  className?: string
}

export interface FormItemProps {
  children: React.ReactNode
  className?: string
}

export interface FormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean
}

export interface FormControlProps {
  children: React.ReactNode
  className?: string
}

export type FormDescriptionProps = React.HTMLAttributes<HTMLParagraphElement>

export interface FormMessageProps extends React.HTMLAttributes<HTMLParagraphElement> {
  type?: 'error' | 'success' | 'info'
}

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
  success?: boolean
}

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
  success?: boolean
}

// Form Field Container
export function FormField({ children, className }: FormFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {children}
    </div>
  )
}

// Form Item Container
export function FormItem({ children, className }: FormItemProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {children}
    </div>
  )
}

// Form Label with required indicator
export function FormLabel({ 
  children, 
  required, 
  className, 
  ...props 
}: FormLabelProps) {
  return (
    <label 
      className={cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        className
      )}
      {...props}
    >
      {children}
      {required && (
        <span className="text-red-500 ml-1" aria-label="required">
          *
        </span>
      )}
    </label>
  )
}

// Form Control Container
export function FormControl({ children, className }: FormControlProps) {
  return (
    <div className={cn('relative', className)}>
      {children}
    </div>
  )
}

// Form Description
export function FormDescription({ 
  className, 
  children, 
  ...props 
}: FormDescriptionProps) {
  return (
    <p 
      className={cn('text-sm text-muted-foreground', className)}
      {...props}
    >
      {children}
    </p>
  )
}

// Form Message (Error/Success/Info)
export function FormMessage({ 
  children, 
  type = 'error', 
  className, 
  ...props 
}: FormMessageProps) {
  if (!children) return null

  const styles = {
    error: 'text-red-600',
    success: 'text-green-600',
    info: 'text-blue-600'
  }

  const icons = {
    error: AlertCircle,
    success: CheckCircle,
    info: AlertCircle
  }

  const Icon = icons[type]

  return (
    <p 
      className={cn(
        'text-sm font-medium flex items-center gap-1',
        styles[type],
        className
      )}
      role={type === 'error' ? 'alert' : 'status'}
      aria-live={type === 'error' ? 'assertive' : 'polite'}
      {...props}
    >
      <Icon className="h-4 w-4 flex-shrink-0" />
      {children}
    </p>
  )
}

// Enhanced Input with validation states
export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, success, ...props }, ref) => {
    const hasError = !!error
    const hasSuccess = success && !hasError

    return (
      <div className="relative">
        <input
          type={type}
          className={cn(
            'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors',
            hasError && 'border-red-500 focus-visible:ring-red-500',
            hasSuccess && 'border-green-500 focus-visible:ring-green-500',
            className
          )}
          ref={ref}
          aria-invalid={hasError}
          aria-describedby={error ? `${props.id}-error` : undefined}
          {...props}
        />
        
        {/* Success indicator */}
        {hasSuccess && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <CheckCircle className="h-4 w-4 text-green-500" />
          </div>
        )}
        
        {/* Error indicator */}
        {hasError && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <AlertCircle className="h-4 w-4 text-red-500" />
          </div>
        )}
      </div>
    )
  }
)
Input.displayName = 'Input'

// Enhanced Textarea with validation states
export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, error, success, ...props }, ref) => {
    const hasError = !!error
    const hasSuccess = success && !hasError

    return (
      <div className="relative">
        <textarea
          className={cn(
            'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors',
            hasError && 'border-red-500 focus-visible:ring-red-500',
            hasSuccess && 'border-green-500 focus-visible:ring-green-500',
            className
          )}
          ref={ref}
          aria-invalid={hasError}
          aria-describedby={error ? `${props.id}-error` : undefined}
          {...props}
        />
        
        {/* Success indicator */}
        {hasSuccess && (
          <div className="absolute top-3 right-3">
            <CheckCircle className="h-4 w-4 text-green-500" />
          </div>
        )}
        
        {/* Error indicator */}
        {hasError && (
          <div className="absolute top-3 right-3">
            <AlertCircle className="h-4 w-4 text-red-500" />
          </div>
        )}
      </div>
    )
  }
)
Textarea.displayName = 'Textarea'

// Form validation hook
export function useFormValidation<T extends Record<string, unknown>>(
  initialValues: T,
  validationSchema?: (values: T) => Record<keyof T, string | undefined>
) {
  const [values, setValues] = React.useState<T>(initialValues)
  const [errors, setErrors] = React.useState<Partial<Record<keyof T, string>>>({})
  const [touched, setTouched] = React.useState<Partial<Record<keyof T, boolean>>>({})

  const setValue = React.useCallback((name: keyof T, value: unknown) => {
    setValues(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }, [errors])

  const setFieldTouched = React.useCallback((name: keyof T) => {
    setTouched(prev => ({ ...prev, [name]: true }))
  }, [])

  const validate = React.useCallback(() => {
    if (!validationSchema) return true

    const newErrors = validationSchema(values)
    setErrors(newErrors)
    
    return Object.values(newErrors).every(error => !error)
  }, [values, validationSchema])

  const reset = React.useCallback(() => {
    setValues(initialValues)
    setErrors({})
    setTouched({})
  }, [initialValues])

  return {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validate,
    reset,
    isValid: Object.values(errors).every(error => !error)
  }
}
