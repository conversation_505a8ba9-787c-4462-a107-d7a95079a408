import React from 'react'
import Image from 'next/image'

interface OrdrlyLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  variant?: 'default' | 'bordered'
}

export function OrdrlyLogo({ size = 'md', className = '', variant = 'default' }: OrdrlyLogoProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-16 h-16'
  }

  if (variant === 'bordered') {
    return (
      <div className={`${sizeClasses[size]} ${className} relative flex items-center justify-center border-2 border-blue-400 rounded-xl quantum-glow bg-white/10 backdrop-blur-sm`}>
        <Image
          src="/logo512.png"
          alt="Ordrly AI Logo"
          width={512}
          height={512}
          className="w-full h-full object-contain p-1"
          priority
        />
      </div>
    )
  }

  return (
    <div className={`${sizeClasses[size]} ${className} relative flex items-center justify-center`}>
      <Image
        src="/logo512.png"
        alt="Ordrly AI Logo"
        width={512}
        height={512}
        className="w-full h-full object-contain"
        priority
      />
    </div>
  )
}

// Inline SVG version for better control and animations
export function OrdrlyLogoSVG({ size = 'md', className = '', variant = 'default' }: OrdrlyLogoProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-20 h-20'
  }

  const containerClass = variant === 'bordered'
    ? `${sizeClasses[size]} ${className} relative flex items-center justify-center border-2 border-blue-400 rounded-xl quantum-glow bg-white/10 backdrop-blur-sm`
    : `${sizeClasses[size]} ${className} relative flex items-center justify-center`

  return (
    <div className={containerClass}>
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 128 128"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={variant === 'bordered' ? 'p-1' : ''}
        style={{ imageRendering: 'auto' }}
        shapeRendering="geometricPrecision"
      >
        {/* Main circle background */}
        <circle cx="64" cy="64" r="60" fill="#ffffff" stroke="#000000" strokeWidth="4" vectorEffect="non-scaling-stroke"/>

        {/* House structure */}
        <rect x="44" y="60" width="40" height="32" fill="#1DA1F2" stroke="#000000" strokeWidth="3" vectorEffect="non-scaling-stroke"/>

        {/* House roof */}
        <path d="M64 36 L36 60 L92 60 Z" fill="#1DA1F2" stroke="#000000" strokeWidth="3" strokeLinejoin="round" vectorEffect="non-scaling-stroke"/>

        {/* Door */}
        <rect x="60" y="72" width="8" height="16" fill="#ffffff" stroke="#000000" strokeWidth="2" vectorEffect="non-scaling-stroke"/>

        {/* Green checkmark with clean edges */}
        <g strokeLinecap="round" strokeLinejoin="round" vectorEffect="non-scaling-stroke">
          <path stroke="#ffffff" strokeWidth="6" d="m68 96 14 14 28-36"/>
          <path stroke="#16C784" strokeWidth="4" d="m68 96 14 14 28-36"/>
        </g>

        {/* Outer ring for definition */}
        <circle cx="64" cy="64" r="62" fill="none" stroke="#ffffff" strokeWidth="2" vectorEffect="non-scaling-stroke"/>
      </svg>
    </div>
  )
}
