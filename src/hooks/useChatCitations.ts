'use client'

import { useState, useEffect, useMemo } from 'react'
import { type ChatMessage } from '@/lib/types/chat'
import { type Citation } from '@/lib/types/compliance'

/**
 * Hook to manage citations extracted from chat messages
 * Parses AI responses for citation markers and maintains a list of sources
 */
export function useChatCitations(messages: ChatMessage[]) {
  const [citations, setCitations] = useState<Citation[]>([])
  const [sourceUrl, setSourceUrl] = useState<string>('')

  // Parse citations from AI messages
  const parsedCitations = useMemo(() => {
    const allCitations: Citation[] = []
    let primarySourceUrl = ''

    // Process assistant messages for citations
    const assistantMessages = messages.filter(msg => msg.role === 'assistant')

    assistantMessages.forEach((message, index) => {
      // Try to extract citations from message metadata first (municipal research sources)
      if (message.metadata?.sources && Array.isArray(message.metadata.sources)) {
        const municipalSources = message.metadata.sources.map((source: any, sourceIndex: number) => {
          return {
            id: `${message.id}-${sourceIndex}`,
            title: source.title || `Municipal Source ${sourceIndex + 1}`,
            section: source.section || 'General Regulations',
            document_title: source.document_type || 'Municipal Code',
            url: source.url,
            similarity: source.similarity,
            jurisdiction: source.jurisdiction,
            verified: source.verified,
            authority: source.authority,
            message_id: message.id,
            citation_number: sourceIndex + 1
          }
        })

        allCitations.push(...municipalSources)
      }

      // Also check legacy citations format for backward compatibility
      if (message.metadata?.citations && Array.isArray(message.metadata.citations)) {
        const legacyCitations = message.metadata.citations.map((citation: any, citationIndex: number) => ({
          id: `${message.id}-legacy-${citationIndex}`,
          title: citation.title || 'Legacy Citation',
          section: citation.section || 'General Regulations',
          document_title: citation.document_type || citation.document_title || 'Municipal Code',
          url: citation.url,
          similarity: citation.similarity,
          jurisdiction: citation.jurisdiction,
          message_id: message.id,
          citation_number: citationIndex + 1
        }))

        allCitations.push(...legacyCitations)
      }

      // Try to extract source URL from metadata
      if (message.metadata?.source_url && typeof message.metadata.source_url === 'string') {
        primarySourceUrl = message.metadata.source_url

      }

      // Parse citation markers from message content [1], [2], etc.
      const citationMatches = message.content.match(/\[(\d+)\]/g)
      if (citationMatches) {
        citationMatches.forEach((match, index) => {
          const citationNumber = parseInt(match.replace(/[\[\]]/g, ''))

          // Create a placeholder citation if we don't have metadata
          if (!message.metadata?.citations) {
            allCitations.push({
              title: `Reference ${citationNumber}`,
              section: `Citation ${citationNumber}`,
              document_title: 'AI Reference'
            })
          }
        })
      }

      // Look for common citation patterns in the text
      const commonPatterns = [
        // Building code references
        /(?:Building Code|Code)\s+(?:Section\s+)?(\d+(?:\.\d+)*)/gi,
        // Ordinance references  
        /(?:Ordinance|Ord\.)\s+(?:No\.\s+)?(\d+(?:-\d+)*)/gi,
        // Section references
        /(?:Section|Sec\.)\s+(\d+(?:\.\d+)*)/gi,
        // Chapter references
        /(?:Chapter|Ch\.)\s+(\d+)/gi
      ]

      commonPatterns.forEach((pattern) => {
        const matches = [...message.content.matchAll(pattern)]
        matches.forEach((match, matchIndex) => {
          if (!allCitations.some(c => c.section === match[1])) {
            allCitations.push({
              id: `${message.id}-pattern-${matchIndex}`,
              title: `${match[0]}`,
              section: match[1],
              document_title: 'Code Reference',
              message_id: message.id,
              citation_number: allCitations.filter(c => c.message_id === message.id).length + 1
            })
          }
        })
      })
    })

    // Remove duplicates based on title and URL (more reliable than section)
    const uniqueCitations = allCitations.filter((citation, index, self) => {
      const isDuplicate = self.findIndex(c => {
        // If both have URLs, compare URLs
        if (c.url && citation.url) {
          return c.url === citation.url
        }
        // Otherwise compare title and section
        return c.title === citation.title && c.section === citation.section
      }) !== index

      return !isDuplicate
    })

    return { citations: uniqueCitations, sourceUrl: primarySourceUrl }
  }, [messages])

  // Update state when parsed citations change
  useEffect(() => {
    setCitations(parsedCitations.citations)
    setSourceUrl(parsedCitations.sourceUrl)
  }, [parsedCitations])

  // Function to add citations manually (for when AI response includes structured citations)
  const addCitations = (newCitations: Citation[], newSourceUrl?: string) => {
    setCitations(prev => {
      const combined = [...prev, ...newCitations]
      // Remove duplicates using same logic as main parser
      return combined.filter((citation, index, self) => {
        const isDuplicate = self.findIndex(c => {
          // If both have URLs, compare URLs
          if (c.url && citation.url) {
            return c.url === citation.url
          }
          // Otherwise compare title and section
          return c.title === citation.title && c.section === citation.section
        }) !== index

        return !isDuplicate
      })
    })

    if (newSourceUrl) {
      setSourceUrl(newSourceUrl)
    }
  }

  // Function to clear all citations (when switching conversations)
  const clearCitations = () => {
    setCitations([])
    setSourceUrl('')
  }

  // Function to handle real-time citations during streaming
  const handleStreamingCitations = (streamingSources: any[]) => {
    if (!streamingSources || streamingSources.length === 0) return

    const newCitations = streamingSources.map((source: any, index: number) => ({
      title: source.title || `Source ${index + 1}`,
      section: source.metadata?.section || source.section || 'General Regulations',
      document_title: source.metadata?.document_type || 'Municipal Code',
      url: source.source_url || source.url,
      similarity: source.similarity,
      jurisdiction: source.jurisdiction
    }))

    setCitations(prev => {
      const combined = [...prev, ...newCitations]
      // Remove duplicates using same logic as main parser
      return combined.filter((citation, index, self) => {
        const isDuplicate = self.findIndex(c => {
          // If both have URLs, compare URLs
          if (c.url && citation.url) {
            return c.url === citation.url
          }
          // Otherwise compare title and section
          return c.title === citation.title && c.section === citation.section
        }) !== index

        return !isDuplicate
      })
    })
  }

  // Function to highlight citation in message content
  const highlightCitations = (content: string): string => {
    let highlightedContent = content

    // Highlight citation markers [1], [2], etc.
    highlightedContent = highlightedContent.replace(
      /\[(\d+)\]/g, 
      '<span class="citation-marker bg-primary/10 text-primary px-1 py-0.5 rounded text-xs font-mono cursor-pointer hover:bg-primary/20 transition-colors" data-citation="$1">[$1]</span>'
    )

    return highlightedContent
  }

  return {
    citations,
    sourceUrl,
    addCitations,
    clearCitations,
    handleStreamingCitations,
    highlightCitations,
    hasCitations: citations.length > 0 || !!sourceUrl,
    citationCount: citations.length
  }
}
