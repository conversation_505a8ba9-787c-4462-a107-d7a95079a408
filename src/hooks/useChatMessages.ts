'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { ChatMessage } from '@/lib/types/chat'
import { useStreamingMessage } from './useStreamingMessage'

interface PaginationInfo {
  page: number
  limit: number
  totalCount: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

export function useChatMessages(conversationId?: string, pageSize: number = 50, onStreamingCitations?: (sources: any[]) => void) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationInfo | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [streamingContent, setStreamingContent] = useState('')
  const [useStreaming, setUseStreaming] = useState(false) // Disabled streaming - using typing indicator instead
  const [streamingUpdateCount, setStreamingUpdateCount] = useState(0)
  const [lastResponseTime, setLastResponseTime] = useState<number>(0)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Streaming hook for real-time responses
  const {
    isStreaming,
    content: streamContent,
    error: streamError,
    startStreaming,
    cancelStreaming,
    clearError: clearStreamError
  } = useStreamingMessage({
    onComplete: (content, messageId, citations, sources, ragMetadata) => {
      // Add the completed AI message to the messages list with RAG metadata
      const aiMessage: ChatMessage = {
        id: messageId,
        conversation_id: conversationId!,
        role: 'assistant',
        content,
        metadata: {
          streaming: true,
          timestamp: new Date().toISOString(),
          citations: citations || [],
          sources: sources || [],
          rag_metadata: ragMetadata || null
        },
        created_at: new Date().toISOString()
      }

      setMessages(prev => [...prev, aiMessage])
      setStreamingContent('')
      setStreamingUpdateCount(0)
      setIsSending(false)
    },
    onError: (error) => {
      setError(error)
      setStreamingContent('')
      setStreamingUpdateCount(0)
      setIsSending(false)
    },
    onChunk: (chunk, accumulated, citations, sources) => {
      console.log('📝 Streaming chunk received:', { chunk: chunk.slice(0, 50), accumulated: accumulated.slice(0, 100) })
      setStreamingContent(accumulated)
      setStreamingUpdateCount(prev => prev + 1)
      // Force re-render by updating a timestamp
      setLastResponseTime(Date.now())
    },
    onCitations: (citations, sources) => {
      // Handle real-time citations during streaming
      console.log('New citations detected during streaming:', citations)
      if (onStreamingCitations && sources) {
        onStreamingCitations(sources)
      }
    }
  })

  const fetchMessages = useCallback(async (page: number = 1, append: boolean = false) => {
    if (!conversationId) {
      setMessages([])
      setPagination(null)
      return
    }

    try {
      if (append) {
        setIsLoadingMore(true)
      } else {
        setIsLoading(true)
        setCurrentPage(page)
      }
      setError(null)

      const response = await fetch(`/api/chat/messages?conversation_id=${conversationId}&page=${page}&limit=${pageSize}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch messages')
      }

      if (append) {
        // Append new messages (for loading older messages)
        setMessages(prev => [...(data.messages || []), ...prev])
      } else {
        // Replace messages (for initial load or refresh)
        setMessages(data.messages || [])
      }

      setPagination(data.pagination || null)
    } catch (err) {
      console.error('Error fetching messages:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch messages')
    } finally {
      setIsLoading(false)
      setIsLoadingMore(false)
    }
  }, [conversationId, pageSize])

  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!conversationId || !content.trim()) {
      return false
    }

    try {
      setIsSending(true)
      setError(null)
      setStreamingContent('')
      setStreamingUpdateCount(0)

      // Add user message immediately
      const userMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        conversation_id: conversationId,
        role: 'user',
        content: content.trim(),
        metadata: {},
        created_at: new Date().toISOString()
      }

      setMessages(prev => [...prev, userMessage])

      // Try streaming first if enabled
      if (useStreaming) {
        const success = await startStreaming(conversationId, content.trim())
        if (success) {
          return true
        }

        // If streaming fails, fall back to standard mode
        console.warn('Streaming failed, falling back to standard mode')
        setUseStreaming(false)
      }

      // Fallback to standard API call
      abortControllerRef.current = new AbortController()

      const response = await fetch('/api/chat/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation_id: conversationId,
          content: content.trim(),
        }),
        signal: abortControllerRef.current.signal
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message')
      }

      // Replace temp user message and add AI message
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== userMessage.id)
        return [...filtered, data.userMessage, data.aiMessage]
      })

      // Trigger sources update for the sources panel
      const sources = data.aiMessage?.metadata?.sources || data.aiMessage?.metadata?.citations || []
      if (sources.length > 0 && onStreamingCitations) {
        console.log('📋 Triggering sources update with municipal sources:', sources)
        onStreamingCitations(sources)
      }

      return true
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        setError('Request cancelled')
        return false
      }

      console.error('Error sending message:', err)
      setError(err instanceof Error ? err.message : 'Failed to send message')
      return false
    } finally {
      setIsSending(false)
    }
  }, [conversationId, useStreaming, startStreaming])

  const loadMoreMessages = useCallback(async () => {
    if (!pagination?.hasPreviousPage || isLoadingMore) {
      return
    }

    const previousPage = currentPage - 1
    if (previousPage >= 1) {
      await fetchMessages(previousPage, true)
      setCurrentPage(previousPage)
    }
  }, [pagination, isLoadingMore, currentPage, fetchMessages])

  const cancelMessage = useCallback(() => {
    if (isStreaming) {
      cancelStreaming()
    } else if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    setIsSending(false)
    setStreamingContent('')
    setStreamingUpdateCount(0)
  }, [isStreaming, cancelStreaming])

  const clearError = useCallback(() => {
    setError(null)
    clearStreamError()
  }, [clearStreamError])

  // Fetch messages when conversation changes
  useEffect(() => {
    if (conversationId) {
      setCurrentPage(1)
      fetchMessages(1, false)
    } else {
      setMessages([])
      setPagination(null)
    }
  }, [conversationId]) // Removed fetchMessages from dependencies to prevent infinite loop

  return {
    messages,
    isLoading,
    isLoadingMore,
    isSending: isSending || isStreaming,
    error: error || streamError,
    pagination,
    sendMessage,
    loadMoreMessages,
    clearError,
    cancelMessage,
    refetchMessages: () => fetchMessages(currentPage, false),
    // Streaming-specific state
    isStreaming,
    streamingContent,
    streamingUpdateCount,
    useStreaming,
    setUseStreaming
  }
}
