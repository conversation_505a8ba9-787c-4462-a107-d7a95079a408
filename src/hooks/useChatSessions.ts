'use client'

import { useState, useEffect, useCallback } from 'react'
import { ChatConversation, NewChatData } from '@/lib/types/chat'

export function useChatSessions() {
  const [sessions, setSessions] = useState<ChatConversation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSessions = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch('/api/chat/conversations')
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch conversations')
      }
      
      setSessions(data.conversations || [])
    } catch (err) {
      console.error('Error fetching chat sessions:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch sessions')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const createSession = useCallback(async (newChatData: NewChatData): Promise<ChatConversation | null> => {
    try {
      setError(null)
      
      const response = await fetch('/api/chat/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newChatData),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create conversation')
      }
      
      const newSession = data.conversation
      setSessions(prev => [newSession, ...prev])
      return newSession
    } catch (err) {
      console.error('Error creating chat session:', err)
      setError(err instanceof Error ? err.message : 'Failed to create session')
      return null
    }
  }, [])

  const deleteSession = useCallback(async (sessionId: string): Promise<boolean> => {
    try {
      setError(null)
      
      const response = await fetch(`/api/chat/conversations/${sessionId}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to delete conversation')
      }
      
      setSessions(prev => prev.filter(session => session.id !== sessionId))
      return true
    } catch (err) {
      console.error('Error deleting chat session:', err)
      setError(err instanceof Error ? err.message : 'Failed to delete session')
      return false
    }
  }, [])

  const updateSession = useCallback(async (sessionId: string, updates: Partial<ChatConversation>): Promise<boolean> => {
    try {
      setError(null)
      
      const response = await fetch(`/api/chat/conversations/${sessionId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to update conversation')
      }
      
      const updatedSession = await response.json()
      setSessions(prev => prev.map(session => 
        session.id === sessionId ? { ...session, ...updatedSession } : session
      ))
      return true
    } catch (err) {
      console.error('Error updating chat session:', err)
      setError(err instanceof Error ? err.message : 'Failed to update session')
      return false
    }
  }, [])

  useEffect(() => {
    fetchSessions()
  }, []) // Removed fetchSessions from dependencies to prevent infinite loop

  return {
    sessions,
    isLoading,
    error,
    fetchSessions,
    createSession,
    deleteSession,
    updateSession,
  }
}
