import { useState, useEffect, useCallback } from 'react'
import type { PromptTemplate, PromptTemplatesResponse, PromptTemplateCategory } from '@/lib/types/chat'

interface UsePromptTemplatesOptions {
  category?: PromptTemplateCategory | string
  activeOnly?: boolean
  autoLoad?: boolean
}

interface UsePromptTemplatesReturn {
  templates: PromptTemplate[]
  categories: string[]
  isLoading: boolean
  error: string | null
  loadTemplates: () => Promise<void>
  clearError: () => void
  getTemplatesByCategory: (category: string) => PromptTemplate[]
  getAppraisalTemplates: () => PromptTemplate[]
}

export function usePromptTemplates(options: UsePromptTemplatesOptions = {}): UsePromptTemplatesReturn {
  const { category, activeOnly = true, autoLoad = true } = options
  
  const [templates, setTemplates] = useState<PromptTemplate[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadTemplates = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (category) params.set('category', category)
      if (!activeOnly) params.set('active_only', 'false')

      const response = await fetch(`/api/chat/prompt-templates?${params}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to load prompt templates')
      }

      const data: PromptTemplatesResponse = await response.json()
      setTemplates(data.templates)
      setCategories(data.categories)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load prompt templates'
      setError(errorMessage)
      console.error('Error loading prompt templates:', err)
    } finally {
      setIsLoading(false)
    }
  }, [category, activeOnly])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const getTemplatesByCategory = useCallback((categoryFilter: string): PromptTemplate[] => {
    return templates.filter(template => template.category === categoryFilter)
  }, [templates])

  const getAppraisalTemplates = useCallback((): PromptTemplate[] => {
    // Return templates relevant for appraisal mode
    const appraisalCategories = ['appraisal', 'general', 'building', 'fire', 'safety']
    return templates.filter(template => appraisalCategories.includes(template.category))
  }, [templates])

  // Auto-load templates on mount if enabled
  useEffect(() => {
    if (autoLoad) {
      loadTemplates()
    }
  }, [autoLoad, loadTemplates])

  return {
    templates,
    categories,
    isLoading,
    error,
    loadTemplates,
    clearError,
    getTemplatesByCategory,
    getAppraisalTemplates
  }
}
