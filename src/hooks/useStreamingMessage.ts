'use client'

import { useState, useCallback, useRef } from 'react'

interface StreamingState {
  isStreaming: boolean
  content: string
  error: string | null
  messageId: string | null
}

interface StreamEvent {
  type: 'chunk' | 'complete' | 'error'
  content?: string
  accumulated?: string
  messageId?: string
  error?: string
  citations?: any[]
  sources?: any[]
  ragMetadata?: {
    confidence_score: number
    data_source: 'rag' | 'real_time'
  }
}

interface UseStreamingMessageOptions {
  onComplete?: (content: string, messageId: string, citations?: any[], sources?: any[], ragMetadata?: any) => void
  onError?: (error: string) => void
  onChunk?: (chunk: string, accumulated: string, citations?: any[], sources?: any[]) => void
  onCitations?: (citations: any[], sources?: any[]) => void
}

export function useStreamingMessage(options: UseStreamingMessageOptions = {}) {
  const [state, setState] = useState<StreamingState>({
    isStreaming: false,
    content: '',
    error: null,
    messageId: null
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const eventSourceRef = useRef<EventSource | null>(null)

  const startStreaming = useCallback(async (
    conversationId: string,
    content: string
  ): Promise<boolean> => {
    // Reset state
    setState({
      isStreaming: true,
      content: '',
      error: null,
      messageId: null
    })

    // Cancel any existing stream
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    try {
      // Create new abort controller
      abortControllerRef.current = new AbortController()

      // Start streaming request
      const response = await fetch('/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation_id: conversationId,
          content
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to start streaming')
      }

      // Handle Server-Sent Events
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              
              if (data === '[DONE]') {
                break
              }

              try {
                const event: StreamEvent = JSON.parse(data)
                
                switch (event.type) {
                  case 'chunk':
                    if (event.content && event.accumulated) {
                      setState(prev => ({
                        ...prev,
                        content: event.accumulated!
                      }))

                      options.onChunk?.(event.content, event.accumulated, event.citations, event.sources)

                      // Handle new citations during streaming
                      if (event.citations && event.citations.length > 0) {
                        options.onCitations?.(event.citations, event.sources)
                      }
                    }
                    break

                  case 'complete':
                    setState(prev => ({
                      ...prev,
                      isStreaming: false,
                      content: event.accumulated || prev.content,
                      messageId: event.messageId || null
                    }))

                    if (event.accumulated && event.messageId) {
                      options.onComplete?.(
                        event.accumulated,
                        event.messageId,
                        event.citations,
                        event.sources,
                        event.ragMetadata
                      )
                    }
                    return true

                  case 'error':
                    const errorMsg = event.error || 'Streaming error'
                    setState(prev => ({
                      ...prev,
                      isStreaming: false,
                      error: errorMsg
                    }))
                    
                    options.onError?.(errorMsg)
                    return false
                }
              } catch (parseError) {
                console.warn('Failed to parse streaming event:', parseError)
                continue
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      return true

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Request was cancelled
        setState(prev => ({
          ...prev,
          isStreaming: false,
          error: 'Request cancelled'
        }))
        return false
      }

      const errorMsg = error instanceof Error ? error.message : 'Unknown streaming error'
      setState(prev => ({
        ...prev,
        isStreaming: false,
        error: errorMsg
      }))
      
      options.onError?.(errorMsg)
      return false
    }
  }, [options])

  const cancelStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
    }

    setState(prev => ({
      ...prev,
      isStreaming: false,
      error: 'Cancelled by user'
    }))
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null
    }))
  }, [])

  const reset = useCallback(() => {
    setState({
      isStreaming: false,
      content: '',
      error: null,
      messageId: null
    })
  }, [])

  return {
    // State
    isStreaming: state.isStreaming,
    content: state.content,
    error: state.error,
    messageId: state.messageId,
    
    // Actions
    startStreaming,
    cancelStreaming,
    clearError,
    reset
  }
}


