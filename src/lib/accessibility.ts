// Accessibility utilities and constants

export const ARIA_LABELS = {
  // Navigation
  mainNavigation: 'Main navigation',
  skipToContent: 'Skip to main content',
  userMenu: 'User account menu',
  themeToggle: 'Toggle theme',
  
  // Search
  addressInput: 'Enter property address',
  projectTypeSelect: 'Select project type',
  searchButton: 'Search for compliance information',
  searchResults: 'Search results',
  
  // Compliance
  complianceStatus: 'Compliance status',
  complianceCard: 'Compliance information card',
  keyRule: 'Key compliance rule',
  expandDetails: 'Expand details',
  collapseDetails: 'Collapse details',
  
  // Maps
  miniMap: 'Property location map',
  streetView: 'Street view of property',
  mapToggle: 'Toggle between map and street view',
  
  // Actions
  share: 'Share compliance information',
  download: 'Download compliance report',
  viewSource: 'View official source document',
  
  // Status indicators
  allowed: 'Project is allowed',
  restricted: 'Project is restricted',
  permitRequired: 'Permit is required for this project',
  
  // Quality indicators
  confidenceScore: 'Research confidence score',
  researchTime: 'Time taken to research',
  officialSources: 'Number of official sources found'
} as const

export const KEYBOARD_SHORTCUTS = {
  ESCAPE: 'Escape',
  ENTER: 'Enter',
  SPACE: ' ',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End'
} as const

export const FOCUS_MANAGEMENT = {
  // Focus ring styles
  focusRing: 'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
  focusRingInset: 'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset',
  
  // Skip link styles
  skipLink: 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50'
} as const

export const SCREEN_READER = {
  // Screen reader only content
  srOnly: 'sr-only',
  
  // Live regions
  livePolite: 'aria-live="polite"',
  liveAssertive: 'aria-live="assertive"',
  
  // Common descriptions
  loading: 'Loading content',
  error: 'Error occurred',
  success: 'Action completed successfully',
  warning: 'Warning message'
} as const

// Color contrast utilities
export const CONTRAST_RATIOS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3,
  AAA_NORMAL: 7,
  AAA_LARGE: 4.5
} as const

// Helper function to check if an element is focusable
export function isFocusable(element: HTMLElement): boolean {
  const focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ]
  
  return focusableSelectors.some(selector => element.matches(selector))
}

// Helper function to get all focusable elements within a container
export function getFocusableElements(container: HTMLElement): HTMLElement[] {
  const focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ')
  
  return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[]
}

// Helper function to trap focus within a container
export function trapFocus(container: HTMLElement, event: KeyboardEvent): void {
  const focusableElements = getFocusableElements(container)
  const firstElement = focusableElements[0]
  const lastElement = focusableElements[focusableElements.length - 1]
  
  if (event.key === KEYBOARD_SHORTCUTS.TAB) {
    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement?.focus()
        event.preventDefault()
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement?.focus()
        event.preventDefault()
      }
    }
  }
}

// Helper function to announce content to screen readers
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// Helper function to manage focus restoration
export class FocusManager {
  private previousFocus: HTMLElement | null = null
  
  saveFocus(): void {
    this.previousFocus = document.activeElement as HTMLElement
  }
  
  restoreFocus(): void {
    if (this.previousFocus && document.contains(this.previousFocus)) {
      this.previousFocus.focus()
    }
  }
  
  focusElement(element: HTMLElement | null): void {
    if (element) {
      element.focus()
    }
  }
}

// Accessibility testing utilities
export const A11Y_TESTING = {
  // Check if element has proper ARIA labels
  hasAriaLabel: (element: HTMLElement): boolean => {
    return !!(element.getAttribute('aria-label') || 
              element.getAttribute('aria-labelledby') ||
              element.getAttribute('aria-describedby'))
  },
  
  // Check if interactive element is keyboard accessible
  isKeyboardAccessible: (element: HTMLElement): boolean => {
    const tabIndex = element.getAttribute('tabindex')
    return isFocusable(element) || (tabIndex !== null && tabIndex !== '-1')
  },
  
  // Check if element has sufficient color contrast (simplified check)
  hasGoodContrast: (element: HTMLElement): boolean => {
    const styles = window.getComputedStyle(element)
    const color = styles.color
    const backgroundColor = styles.backgroundColor
    
    // This is a simplified check - in production, you'd want a proper contrast calculation
    return color !== backgroundColor
  }
} as const
