/**
 * Centralized AI configuration for consistent model usage across the application
 */

export interface AIConfig {
  model: string
  maxTokens: number // Output tokens
  temperature: number
  contextWindow?: number // Input context window
}

export interface AIUsageStats {
  model: string
  maxTokens: number
  estimatedInputTokens: number
  estimatedOutputTokens: number
  estimatedCost: number
}

/**
 * Get AI configuration from environment variables with fallbacks
 */
export function getAIConfig(): AIConfig {
  // Upgraded to gpt-4.1-nano for better geographic validation and reasoning
  const model = process.env.OPENAI_MODEL || "gpt-4.1-nano"

  return {
    model,
    maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || "16000"),
    temperature: parseFloat(process.env.OPENAI_TEMPERATURE || "0.1"),
    contextWindow: getContextWindow(model)
  }
}

/**
 * Get the input context window size for a model
 */
export function getContextWindow(model: string): number {
  if (model.includes('gpt-4.1-nano')) {
    return 1047576 // 1M+ tokens input context
  } else if (model.includes('gpt-4.1-mini')) {
    return 1000000 // 1M tokens estimated
  } else if (model.includes('gpt-4.1')) {
    return 1000000 // 1M tokens estimated
  } else if (model.includes('gpt-4o')) {
    return 128000 // 128K tokens
  } else if (model.includes('gpt-4-turbo')) {
    return 128000 // 128K tokens
  } else if (model.includes('gpt-4')) {
    return 8000 // 8K tokens
  } else {
    return 16000 // Default for older models
  }
}

/**
 * Calculate dynamic content limits based on available tokens and number of documents
 * Uses context window for input capacity, not output tokens
 */
export function calculateContentLimits(config: AIConfig, documentCount: number): {
  contentLimitPerDoc: number
  totalContentLimit: number
  contextWindowUsed: number
} {
  // Use context window for input capacity, fallback to maxTokens if not available
  const inputCapacity = config.contextWindow || config.maxTokens

  // Reserve tokens for prompt structure, instructions, and response
  const promptOverhead = 2000 // System prompt, instructions, formatting
  const responseReserve = config.maxTokens // Reserve full output capacity
  const safetyBuffer = Math.min(inputCapacity * 0.1, 10000) // 10% buffer, max 10K

  const availableTokensForContent = Math.max(
    inputCapacity - promptOverhead - responseReserve - safetyBuffer,
    documentCount * 2000 // Minimum 2K tokens per document
  )

  // Distribute content tokens across documents
  const maxContentPerDoc = Math.floor(availableTokensForContent / documentCount)

  // Set minimum content per document to ensure quality
  const minContentPerDoc = 2000

  // Use the larger of calculated or minimum
  const contentLimitPerDoc = Math.max(maxContentPerDoc, minContentPerDoc)

  return {
    contentLimitPerDoc,
    totalContentLimit: availableTokensForContent,
    contextWindowUsed: inputCapacity
  }
}

/**
 * Estimate token usage for cost tracking (rough approximation)
 */
export function estimateTokenUsage(content: string, config: AIConfig): AIUsageStats {
  // Rough estimation: 1 token ≈ 4 characters for English text
  const estimatedInputTokens = Math.ceil(content.length / 4)
  const estimatedOutputTokens = Math.min(config.maxTokens * 0.3, 2000) // Assume 30% of max tokens for output

  // Cost estimation based on OpenAI pricing (as of 2025)
  let inputCostPer1M = 0.15
  let outputCostPer1M = 0.60

  // Adjust costs based on model
  if (config.model.includes('gpt-4.1-nano')) {
    inputCostPer1M = 0.10   // GPT-4.1 nano - actual pricing from OpenAI
    outputCostPer1M = 0.40
  } else if (config.model.includes('gpt-4.1-mini')) {
    inputCostPer1M = 0.10   // GPT-4.1 mini - affordable
    outputCostPer1M = 0.40
  } else if (config.model.includes('gpt-4.1')) {
    inputCostPer1M = 2.50   // GPT-4.1 - smartest but more expensive
    outputCostPer1M = 10.0
  } else if (config.model.includes('gpt-4o')) {
    inputCostPer1M = 0.15   // GPT-4o models
    outputCostPer1M = 0.60
  } else if (config.model.includes('gpt-4')) {
    inputCostPer1M = 30.0   // Legacy GPT-4 is much more expensive
    outputCostPer1M = 60.0
  }

  const estimatedCost = (
    (estimatedInputTokens / 1000000) * inputCostPer1M +
    (estimatedOutputTokens / 1000000) * outputCostPer1M
  )

  return {
    model: config.model,
    maxTokens: config.maxTokens,
    estimatedInputTokens,
    estimatedOutputTokens,
    estimatedCost
  }
}

/**
 * Log AI configuration and usage stats
 */
export function logAIUsage(content: string, config: AIConfig, context: string = '') {
  const stats = estimateTokenUsage(content, config)

  console.log(`🧠 AI Configuration ${context}:`)
  console.log(`   Model: ${config.model}`)
  console.log(`   Max Tokens: ${config.maxTokens.toLocaleString()}`)
  console.log(`   Temperature: ${config.temperature}`)
  console.log(`   Est. Input Tokens: ${stats.estimatedInputTokens.toLocaleString()}`)
  console.log(`   Est. Output Tokens: ${stats.estimatedOutputTokens.toLocaleString()}`)
  console.log(`   Est. Cost: $${stats.estimatedCost.toFixed(4)}`)
}

/**
 * Validate that the model is supported
 */
export function validateAIModel(model: string): boolean {
  const supportedModels = [
    'gpt-4.1-nano',
    'gpt-4.1-mini',
    'gpt-4.1',
    'gpt-4o-mini',
    'gpt-4o',
    'gpt-4-turbo',
    'gpt-4',
    'gpt-3.5-turbo'
  ]

  return supportedModels.some(supported => model.includes(supported))
}

/**
 * Get model-specific recommendations
 */
export function getModelRecommendations(model: string): {
  maxTokensRecommended: number
  temperatureRecommended: number
  costEfficiency: 'high' | 'medium' | 'low'
  useCase: string
} {
  if (model.includes('gpt-4.1-nano')) {
    return {
      maxTokensRecommended: 32768, // Max output tokens for GPT-4.1 nano
      temperatureRecommended: 0.1,
      costEfficiency: 'high',
      useCase: 'Fastest, most cost-effective model with 1M+ context window for massive ordinance analysis'
    }
  } else if (model.includes('gpt-4.1-mini')) {
    return {
      maxTokensRecommended: 32000,
      temperatureRecommended: 0.1,
      costEfficiency: 'high',
      useCase: 'Affordable model balancing speed and intelligence for ordinance analysis'
    }
  } else if (model.includes('gpt-4.1')) {
    return {
      maxTokensRecommended: 32000,
      temperatureRecommended: 0.1,
      costEfficiency: 'medium',
      useCase: 'Smartest model for complex ordinance tasks requiring deep reasoning'
    }
  } else if (model.includes('gpt-4o-mini')) {
    return {
      maxTokensRecommended: 16000,
      temperatureRecommended: 0.1,
      costEfficiency: 'high',
      useCase: 'Previous generation cost-effective model - consider upgrading to gpt-4.1-nano'
    }
  } else if (model.includes('gpt-4o')) {
    return {
      maxTokensRecommended: 32000,
      temperatureRecommended: 0.1,
      costEfficiency: 'medium',
      useCase: 'Previous generation high-quality model - consider upgrading to gpt-4.1'
    }
  } else if (model.includes('gpt-4')) {
    return {
      maxTokensRecommended: 8000,
      temperatureRecommended: 0.1,
      costEfficiency: 'low',
      useCase: 'Legacy model - upgrade to gpt-4.1-nano for better performance and cost'
    }
  } else {
    return {
      maxTokensRecommended: 4000,
      temperatureRecommended: 0.1,
      costEfficiency: 'high',
      useCase: 'Basic analysis - may miss nuanced ordinance details'
    }
  }
}
