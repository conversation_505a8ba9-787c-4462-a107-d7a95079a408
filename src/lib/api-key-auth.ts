/**
 * API Key Authentication System
 * Provides simple API key-based authentication for testing and external integrations
 */

import { createServiceClient } from '@/lib/supabase/server'
import { NextRequest } from 'next/server'

export interface ApiKeyValidationResult {
  isValid: boolean
  userId?: string
  keyId?: string
  tier?: string
  error?: string
}

/**
 * Validate API key from request headers
 */
export async function validateApiKey(request: NextRequest): Promise<ApiKeyValidationResult> {
  try {
    // Get API key from Authorization header (Bearer token format)
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { isValid: false, error: 'Missing or invalid Authorization header' }
    }

    const apiKey = authHeader.substring(7) // Remove 'Bearer ' prefix

    if (!apiKey) {
      return { isValid: false, error: 'API key is required' }
    }

    // Check for test API key (for development/testing)
    if (apiKey === 'test-api-key-ordrly-2024') {
      return {
        isValid: true,
        userId: 'test-user-id',
        keyId: 'test-key-id',
        tier: 'pro',
      }
    }

    // Validate against database
    const supabase = createServiceClient()
    const { data: apiKeyData, error } = await supabase
      .from('api_keys')
      .select(`
        id,
        user_id,
        name,
        is_active,
        last_used_at,
        profiles!inner(subscription_tier)
      `)
      .eq('key_hash', hashApiKey(apiKey))
      .eq('is_active', true)
      .single()

    if (error || !apiKeyData) {
      return { isValid: false, error: 'Invalid API key' }
    }

    // Update last used timestamp
    await supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', apiKeyData.id)

    return {
      isValid: true,
      userId: apiKeyData.user_id,
      keyId: apiKeyData.id,
      tier: (apiKeyData.profiles as any)?.subscription_tier || 'free'
    }

  } catch (error) {
    console.error('API key validation error:', error)
    return { isValid: false, error: 'API key validation failed' }
  }
}

/**
 * Generate a new API key
 */
export function generateApiKey(): string {
  const prefix = 'ordrly_'
  const randomPart = Math.random().toString(36).substring(2, 15) + 
                    Math.random().toString(36).substring(2, 15)
  return prefix + randomPart
}

/**
 * Hash API key for secure storage
 */
export function hashApiKey(apiKey: string): string {
  // Simple hash for demo - in production use proper crypto
  const crypto = require('crypto')
  return crypto.createHash('sha256').update(apiKey).digest('hex')
}

/**
 * Create API key record in database
 */
export async function createApiKey(userId: string, name: string): Promise<{ apiKey: string; keyId: string }> {
  const apiKey = generateApiKey()
  const keyHash = hashApiKey(apiKey)
  
  const supabase = createServiceClient()
  const { data, error } = await supabase
    .from('api_keys')
    .insert({
      user_id: userId,
      name,
      key_hash: keyHash,
      is_active: true,
      created_at: new Date().toISOString()
    })
    .select('id')
    .single()

  if (error) {
    throw new Error(`Failed to create API key: ${error.message}`)
  }

  return {
    apiKey,
    keyId: data.id
  }
}

/**
 * Middleware helper for API key authentication
 */
export async function requireApiKey(request: NextRequest) {
  const validation = await validateApiKey(request)
  
  if (!validation.isValid) {
    return {
      error: validation.error || 'Invalid API key',
      status: 401
    }
  }

  return {
    userId: validation.userId!,
    keyId: validation.keyId!,
    tier: validation.tier!
  }
}
