'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import { sendWelcomeEmail } from '@/lib/email/sender'
import { notifyUserRegistration, safeAutomationCall } from '@/lib/automation/integration'
import { assignTrialToNewUser } from '@/lib/trial-management'

export async function login(formData: FormData) {
  const supabase = await createServerClient()

  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Enhanced input validation
  if (!email || !password) {
    redirect('/login?error=Email and password are required')
  }

  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    redirect('/login?error=Please enter a valid email address')
  }

  const { error } = await supabase.auth.signInWithPassword({
    email: email.toLowerCase().trim(),
    password,
  })

  if (error) {
    console.error('Login error:', error)

    // Enhanced error handling
    let errorMessage = 'Invalid credentials'
    if (error.message.includes('rate limit')) {
      errorMessage = 'Too many login attempts. Please try again later.'
    } else if (error.message.includes('email not confirmed')) {
      errorMessage = 'Please check your email and confirm your account'
    }

    redirect(`/login?error=${encodeURIComponent(errorMessage)}`)
  }

  revalidatePath('/', 'layout')
  redirect('/chat')
}

export async function signup(formData: FormData) {
  const supabase = await createServerClient()

  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string
  const name = formData.get('name') as string
  const referralCode = formData.get('referralCode') as string

  // Validate input
  if (!email || !password) {
    redirect('/signup?error=Email and password are required')
  }

  if (password !== confirmPassword) {
    redirect('/signup?error=Passwords do not match')
  }

  if (password.length < 6) {
    redirect('/signup?error=Password must be at least 6 characters')
  }

  const { error, data: authData } = await supabase.auth.signUp({
    email: email.toLowerCase().trim(),
    password,
    options: {
      data: {
        name: name || email.split('@')[0]
      },
      emailRedirectTo: undefined // Disable email confirmation
    }
  })

  if (error) {
    console.error('Signup error:', error)
    let errorMessage = 'Signup failed'

    if (error.message.includes('already registered') || error.message.includes('User already registered')) {
      errorMessage = 'An account with this email already exists'
    } else if (error.message.includes('invalid email')) {
      errorMessage = 'Please enter a valid email address'
    } else if (error.message.includes('password')) {
      errorMessage = 'Password must be at least 6 characters'
    } else if (error.message.includes('rate limit')) {
      errorMessage = 'Too many signup attempts. Please try again later.'
    } else {
      // Include the actual error message for debugging
      errorMessage = `Signup failed: ${error.message}`
    }

    redirect(`/signup?error=${encodeURIComponent(errorMessage)}`)
  }

  // Create profile record
  if (authData.user) {
    try {
      // Generate unique referral code
      const userReferralCode = `${authData.user.email?.split('@')[0]?.toUpperCase() || 'USER'}${Math.random().toString(36).substr(2, 4).toUpperCase()}`

      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: authData.user.id,
          email: authData.user.email,
          name: authData.user.user_metadata?.name || name || authData.user.email?.split('@')[0],
          subscription_tier: 'trial',
          trial_start_date: new Date().toISOString(),
          trial_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          pulls_this_month: 0,
          extra_credits: 0,
          referral_code: userReferralCode,
          first_time_user: true,
        }, { onConflict: 'id' })

      if (profileError) {
        console.error('Profile creation error:', profileError)
        // Don't fail the signup if profile creation fails - user can still login
      } else {
        console.log('✅ New user created with 7-day trial:', authData.user.email)
      }

      // Process referral if code provided
      if (referralCode && referralCode.trim()) {
        try {
          const { data: referralSuccess } = await supabase.rpc(
            'process_referral_signup',
            {
              referee_id_param: authData.user.id,
              referral_code_param: referralCode.trim().toUpperCase()
            }
          )

          if (referralSuccess) {
            console.log('Referral processed successfully for user:', authData.user.id)
          } else {
            console.log('Invalid or duplicate referral code:', referralCode)
          }
        } catch (referralError) {
          console.error('Error processing referral:', referralError)
          // Don't fail signup if referral processing fails
        }
      }

      // Send welcome email immediately since email confirmation is disabled
      try {
        await sendWelcomeEmail(
          authData.user.email!,
          authData.user.user_metadata?.name || authData.user.email?.split('@')[0]
        )
        console.log('Welcome email sent to:', authData.user.email)
      } catch (emailError) {
        console.error('Error sending welcome email:', emailError)
        // Don't fail signup if email sending fails
      }

      // Notify automation system for drip campaigns and email automation
      if (authData.user) {
        await safeAutomationCall(
          () => notifyUserRegistration({
            userId: authData.user!.id,
            email: authData.user!.email!,
            userData: {
              signupSource: 'ordrly-main-app',
              userType: 'homeowner',
              name: authData.user!.user_metadata?.name,
              referralCode: referralCode || undefined,
              emailConfirmed: true // Email confirmation is disabled
            }
          }),
          { success: false, error: 'Automation system unavailable' },
          'user registration notification'
        )
      }
    } catch (error) {
      console.error('Error in profile creation process:', error)
      // Don't fail signup if profile creation fails
    }
  }

  // Ensure session is properly established before redirecting
  try {
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      console.error('No session found after signup, redirecting to login')
      redirect('/login?message=Account created successfully. Please sign in.')
    }
  } catch (error) {
    console.error('Error checking session after signup:', error)
    redirect('/login?message=Account created successfully. Please sign in.')
  }

  revalidatePath('/', 'layout')

  // Redirect to chat since email confirmation is disabled
  redirect('/chat')
}



export async function signOut() {
  const supabase = await createServerClient()

  const { error } = await supabase.auth.signOut()

  if (error) {
    console.error('Sign out error:', error)
  }

  revalidatePath('/', 'layout')
  redirect('/login')
}

export async function forgotPassword(formData: FormData) {
  const supabase = await createServerClient()
  const email = formData.get('email') as string

  if (!email) {
    redirect('/forgot-password?error=Email is required')
  }

  const baseUrl = process.env.NODE_ENV === 'development'
    ? 'http://localhost:3000'
    : (process.env.NEXT_PUBLIC_SITE_URL || 'https://ordrly.ai')

  const { error } = await supabase.auth.resetPasswordForEmail(email.toLowerCase().trim(), {
    redirectTo: `${baseUrl}/reset-password`,
  })

  if (error) {
    console.error('Forgot password error:', error)

    if (error.message.includes('rate limit')) {
      redirect('/forgot-password?error=Too many reset attempts. Please try again later.')
    }
  }

  // Always show success message for security (don't reveal if email exists)
  redirect('/forgot-password?message=If an account with this email exists, you will receive a password reset link.')
}

export async function resetPassword(formData: FormData) {
  const supabase = await createServerClient()
  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string

  if (!password || !confirmPassword) {
    redirect('/reset-password?error=Both password fields are required')
  }

  if (password !== confirmPassword) {
    redirect('/reset-password?error=Passwords do not match')
  }

  if (password.length < 6) {
    redirect('/reset-password?error=Password must be at least 6 characters')
  }

  // Check if user has a valid session for password reset
  const { data: { user }, error: sessionError } = await supabase.auth.getUser()

  if (sessionError || !user) {
    redirect('/reset-password?error=Reset link has expired or is invalid. Please request a new one.')
  }

  const { error } = await supabase.auth.updateUser({ password })

  if (error) {
    console.error('Reset password error:', error)

    if (error.message.includes('session') || error.message.includes('expired')) {
      redirect('/forgot-password?error=Reset link has expired. Please request a new one.')
    } else if (error.message.includes('weak')) {
      redirect('/reset-password?error=Password is too weak. Please choose a stronger password.')
    } else if (error.message.includes('same')) {
      redirect('/reset-password?error=New password must be different from your current password.')
    } else {
      redirect('/reset-password?error=Failed to reset password. Please try again.')
    }
  }

  revalidatePath('/', 'layout')
  redirect('/login?message=Password updated successfully. Please sign in with your new password.')
}
