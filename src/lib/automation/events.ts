/**
 * Event tracking for automation system
 * Defines key user events that trigger behavior-based emails
 */

import { notifyUserEvent, safeAutomationCall } from './integration'

// Event types for behavior-triggered emails
export const EVENT_TYPES = {
  // Search-related events
  FIRST_SEARCH_COMPLETED: 'first_search_completed',
  SEARCH_LIMIT_REACHED: 'search_limit_reached',
  MULTIPLE_SEARCHES_SAME_DAY: 'multiple_searches_same_day',
  
  // Subscription-related events
  SUBSCRIPTION_UPGRADED: 'subscription_upgraded',
  SUBSCRIPTION_CANCELLED: 'subscription_cancelled',
  UPGRADE_BUTTON_CLICKED: 'upgrade_button_clicked',
  
  // Feature usage events
  CHAT_FEATURE_USED: 'chat_feature_used',
  SAVED_SEARCH_CREATED: 'saved_search_created',
  SEARCH_HISTORY_VIEWED: 'search_history_viewed',
  
  // Engagement events
  RETURN_VISIT_AFTER_WEEK: 'return_visit_after_week',
  FEATURE_DISCOVERY: 'feature_discovery',
  HELP_SECTION_VISITED: 'help_section_visited'
} as const;

export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];

/**
 * Track user's first search completion
 */
export async function trackFirstSearch(
  userId: string,
  searchData: {
    address: string;
    ruleType: string;
    jurisdiction?: string;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.FIRST_SEARCH_COMPLETED, {
      address: searchData.address,
      ruleType: searchData.ruleType,
      jurisdiction: searchData.jurisdiction,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'first search tracking'
  );
}

/**
 * Track when user reaches search limit
 */
export async function trackSearchLimitReached(
  userId: string,
  limitData: {
    searchesUsed: number;
    limit: number;
    tier: string;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.SEARCH_LIMIT_REACHED, {
      searchesUsed: limitData.searchesUsed,
      limit: limitData.limit,
      tier: limitData.tier,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'search limit tracking'
  );
}

/**
 * Track subscription upgrade
 */
export async function trackSubscriptionUpgrade(
  userId: string,
  upgradeData: {
    fromTier: string;
    toTier: string;
    planType: string;
    amount?: number;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.SUBSCRIPTION_UPGRADED, {
      fromTier: upgradeData.fromTier,
      toTier: upgradeData.toTier,
      planType: upgradeData.planType,
      amount: upgradeData.amount,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'subscription upgrade tracking'
  );
}

/**
 * Track subscription cancellation
 */
export async function trackSubscriptionCancellation(
  userId: string,
  cancellationData: {
    tier: string;
    reason?: string;
    cancelAtPeriodEnd: boolean;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.SUBSCRIPTION_CANCELLED, {
      tier: cancellationData.tier,
      reason: cancellationData.reason,
      cancelAtPeriodEnd: cancellationData.cancelAtPeriodEnd,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'subscription cancellation tracking'
  );
}

/**
 * Track upgrade button clicks (for nurturing campaigns)
 */
export async function trackUpgradeButtonClick(
  userId: string,
  clickData: {
    planType: string;
    location: string; // where the button was clicked
    currentTier: string;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.UPGRADE_BUTTON_CLICKED, {
      planType: clickData.planType,
      location: clickData.location,
      currentTier: clickData.currentTier,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'upgrade button click tracking'
  );
}

/**
 * Track chat feature usage
 */
export async function trackChatFeatureUsage(
  userId: string,
  chatData: {
    conversationId: string;
    messageCount: number;
    ruleType?: string;
    address?: string;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.CHAT_FEATURE_USED, {
      conversationId: chatData.conversationId,
      messageCount: chatData.messageCount,
      ruleType: chatData.ruleType,
      address: chatData.address,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'chat feature usage tracking'
  );
}

/**
 * Track saved search creation
 */
export async function trackSavedSearchCreated(
  userId: string,
  searchData: {
    searchId: string;
    address: string;
    ruleType: string;
    name?: string;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.SAVED_SEARCH_CREATED, {
      searchId: searchData.searchId,
      address: searchData.address,
      ruleType: searchData.ruleType,
      name: searchData.name,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'saved search creation tracking'
  );
}

/**
 * Track search history viewing
 */
export async function trackSearchHistoryViewed(
  userId: string,
  historyData: {
    totalSearches: number;
    viewedCount: number;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.SEARCH_HISTORY_VIEWED, {
      totalSearches: historyData.totalSearches,
      viewedCount: historyData.viewedCount,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'search history viewing tracking'
  );
}

/**
 * Track feature discovery
 */
export async function trackFeatureDiscovery(
  userId: string,
  featureData: {
    featureName: string;
    discoveryMethod: string; // 'click', 'hover', 'navigation', etc.
    userTier: string;
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.FEATURE_DISCOVERY, {
      featureName: featureData.featureName,
      discoveryMethod: featureData.discoveryMethod,
      userTier: featureData.userTier,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'feature discovery tracking'
  );
}

/**
 * Track help section visits
 */
export async function trackHelpSectionVisit(
  userId: string,
  helpData: {
    section: string;
    userTier: string;
    searchContext?: string; // what they were searching for when they needed help
  }
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, EVENT_TYPES.HELP_SECTION_VISITED, {
      section: helpData.section,
      userTier: helpData.userTier,
      searchContext: helpData.searchContext,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    'help section visit tracking'
  );
}

/**
 * Generic event tracking function for custom events
 */
export async function trackCustomEvent(
  userId: string,
  eventType: string,
  metadata: Record<string, any> = {}
) {
  await safeAutomationCall(
    () => notifyUserEvent(userId, eventType, {
      ...metadata,
      timestamp: new Date().toISOString()
    }),
    { success: false },
    `custom event tracking: ${eventType}`
  );
}
