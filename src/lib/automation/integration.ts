/**
 * Integration with ordrly-automation system
 * Connects user registration and events to the automation platform
 */

interface UserRegistrationData {
  userId: string;
  email: string;
  userData?: {
    signupSource?: string;
    userType?: string;
    name?: string;
    referralCode?: string;
    [key: string]: any;
  };
}

interface AutomationResponse {
  success: boolean;
  message?: string;
  welcomeCampaign?: {
    subscribed: boolean;
    subscriptionId?: string;
    error?: string;
  };
  event?: any;
  error?: string;
}

/**
 * Notify ordrly-automation of new user registration
 * This triggers welcome email campaigns and user onboarding
 */
export async function notifyUserRegistration(data: UserRegistrationData): Promise<AutomationResponse> {
  try {
    const automationUrl = process.env.ORDRLY_AUTOMATION_URL || 'https://ordrly-automation.vercel.app';
    const endpoint = `${automationUrl}/api/events/user-registration`;

    console.log('🔗 Notifying automation system of user registration:', {
      userId: data.userId,
      email: data.email,
      endpoint
    });

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ordrly-main-app/1.0'
      },
      body: JSON.stringify({
        userId: data.userId,
        email: data.email,
        userData: {
          signupSource: 'ordrly-main-app',
          userType: 'homeowner',
          signupDate: new Date().toISOString(),
          ...data.userData
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Automation system notification failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }

    const result = await response.json();
    
    console.log('✅ Automation system notified successfully:', {
      userId: data.userId,
      email: data.email,
      welcomeCampaignSubscribed: result.welcomeCampaign?.subscribed,
      subscriptionId: result.welcomeCampaign?.subscriptionId
    });

    return result;

  } catch (error) {
    console.error('❌ Failed to notify automation system:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Notify ordrly-automation of user events for behavior-triggered emails
 */
export async function notifyUserEvent(
  userId: string, 
  eventType: string, 
  metadata: Record<string, any> = {}
): Promise<AutomationResponse> {
  try {
    const automationUrl = process.env.ORDRLY_AUTOMATION_URL || 'https://ordrly-automation.vercel.app';
    const endpoint = `${automationUrl}/api/events/track`;

    console.log('🔗 Notifying automation system of user event:', {
      userId,
      eventType,
      endpoint
    });

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ordrly-main-app/1.0'
      },
      body: JSON.stringify({
        userId,
        eventType,
        metadata: {
          source: 'ordrly-main-app',
          timestamp: new Date().toISOString(),
          ...metadata
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Event notification failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }

    const result = await response.json();
    
    console.log('✅ Event notification sent successfully:', {
      userId,
      eventType,
      eventId: result.event?.id
    });

    return result;

  } catch (error) {
    console.error('❌ Failed to notify automation system of event:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test connection to automation system
 */
export async function testAutomationConnection(): Promise<boolean> {
  try {
    const automationUrl = process.env.ORDRLY_AUTOMATION_URL || 'https://ordrly-automation.vercel.app';
    
    // Test with a simple health check or test endpoint
    const response = await fetch(`${automationUrl}/api/health`, {
      method: 'GET',
      headers: {
        'User-Agent': 'ordrly-main-app/1.0'
      }
    });

    return response.ok;
  } catch (error) {
    console.error('❌ Automation system connection test failed:', error);
    return false;
  }
}

/**
 * Utility function to safely call automation system without failing main app flow
 */
export async function safeAutomationCall<T>(
  operation: () => Promise<T>,
  fallbackValue: T,
  operationName: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error(`❌ Safe automation call failed for ${operationName}:`, error);
    // Don't fail the main app flow if automation system is down
    return fallbackValue;
  }
}
