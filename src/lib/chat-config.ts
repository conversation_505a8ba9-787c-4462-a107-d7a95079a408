/**
 * Chat Configuration System
 * Manages configurable thresholds and settings for chat confidence evaluation
 */

export interface ChatConfig {
  // Confidence thresholds
  ragConfidenceThreshold: number
  vectorSimilarityThreshold: number
  fallbackEnabled: boolean
  
  // Advanced features
  aiUncertaintyCheckEnabled: boolean
  partialAnswerEnabled: boolean
  
  // Logging settings
  fallbackLoggingEnabled: boolean
  
  // Performance settings
  maxRetrievedSnippets: number
  embeddingCacheSize: number
  embeddingCacheTtl: number
}

/**
 * Default configuration values
 */
const DEFAULT_CONFIG: ChatConfig = {
  ragConfidenceThreshold: 0.9,
  vectorSimilarityThreshold: 0.8,
  fallbackEnabled: true,
  aiUncertaintyCheckEnabled: false,
  partialAnswerEnabled: false,
  fallbackLoggingEnabled: true,
  maxRetrievedSnippets: 5,
  embeddingCacheSize: 1000,
  embeddingCacheTtl: 3600000, // 1 hour in ms
}

/**
 * Load configuration from environment variables with fallbacks
 */
export function loadChatConfig(): ChatConfig {
  return {
    ragConfidenceThreshold: parseFloat(
      process.env.CHAT_RAG_CONFIDENCE_THRESHOLD || 
      String(DEFAULT_CONFIG.ragConfidenceThreshold)
    ),
    vectorSimilarityThreshold: parseFloat(
      process.env.CHAT_VECTOR_SIMILARITY_THRESHOLD || 
      String(DEFAULT_CONFIG.vectorSimilarityThreshold)
    ),
    fallbackEnabled: process.env.CHAT_FALLBACK_ENABLED !== 'false',
    aiUncertaintyCheckEnabled: process.env.CHAT_AI_UNCERTAINTY_CHECK === 'true',
    partialAnswerEnabled: process.env.CHAT_PARTIAL_ANSWER_ENABLED === 'true',
    fallbackLoggingEnabled: process.env.CHAT_FALLBACK_LOGGING !== 'false',
    maxRetrievedSnippets: parseInt(
      process.env.CHAT_MAX_RETRIEVED_SNIPPETS || 
      String(DEFAULT_CONFIG.maxRetrievedSnippets)
    ),
    embeddingCacheSize: parseInt(
      process.env.CHAT_EMBEDDING_CACHE_SIZE || 
      String(DEFAULT_CONFIG.embeddingCacheSize)
    ),
    embeddingCacheTtl: parseInt(
      process.env.CHAT_EMBEDDING_CACHE_TTL || 
      String(DEFAULT_CONFIG.embeddingCacheTtl)
    ),
  }
}

/**
 * Validate configuration values
 */
export function validateChatConfig(config: ChatConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Validate thresholds are between 0 and 1
  if (config.ragConfidenceThreshold < 0 || config.ragConfidenceThreshold > 1) {
    errors.push('RAG confidence threshold must be between 0 and 1')
  }
  
  if (config.vectorSimilarityThreshold < 0 || config.vectorSimilarityThreshold > 1) {
    errors.push('Vector similarity threshold must be between 0 and 1')
  }

  // Validate positive integers
  if (config.maxRetrievedSnippets <= 0) {
    errors.push('Max retrieved snippets must be positive')
  }
  
  if (config.embeddingCacheSize <= 0) {
    errors.push('Embedding cache size must be positive')
  }
  
  if (config.embeddingCacheTtl <= 0) {
    errors.push('Embedding cache TTL must be positive')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Get validated chat configuration
 */
export function getChatConfig(): ChatConfig {
  const config = loadChatConfig()
  const validation = validateChatConfig(config)
  
  if (!validation.valid) {
    console.warn('Invalid chat configuration:', validation.errors)
    console.warn('Using default configuration')
    return DEFAULT_CONFIG
  }
  
  return config
}

/**
 * Confidence evaluation result
 */
export interface ConfidenceEvaluation {
  score: number
  threshold: number
  confident: boolean
  reason: string
  metadata?: Record<string, any>
}

/**
 * Evaluate confidence based on similarity scores
 */
export function evaluateConfidence(
  similarityScores: number[],
  config: ChatConfig
): ConfidenceEvaluation {
  if (!similarityScores || similarityScores.length === 0) {
    return {
      score: 0,
      threshold: config.vectorSimilarityThreshold,
      confident: false,
      reason: 'No similarity scores available',
      metadata: { scoresCount: 0 }
    }
  }

  // Calculate confidence score (highest similarity)
  const maxScore = Math.max(...similarityScores)
  const avgScore = similarityScores.reduce((a, b) => a + b, 0) / similarityScores.length
  
  // Use max score as primary confidence metric
  const confidenceScore = maxScore
  const isConfident = confidenceScore >= config.vectorSimilarityThreshold

  return {
    score: confidenceScore,
    threshold: config.vectorSimilarityThreshold,
    confident: isConfident,
    reason: isConfident 
      ? `High similarity score: ${confidenceScore.toFixed(3)}`
      : `Low similarity score: ${confidenceScore.toFixed(3)} below threshold ${config.vectorSimilarityThreshold}`,
    metadata: {
      maxScore,
      avgScore,
      scoresCount: similarityScores.length,
      allScores: similarityScores
    }
  }
}

/**
 * Determine fallback type based on confidence and context
 */
export function determineFallbackType(
  confidence: ConfidenceEvaluation,
  hasRetrievedData: boolean,
  config: ChatConfig
): 'none' | 'low_confidence' | 'no_data' | 'out_of_scope' {
  if (confidence.confident) {
    return 'none'
  }

  if (!hasRetrievedData || confidence.score === 0) {
    return 'no_data'
  }

  if (confidence.score < config.vectorSimilarityThreshold * 0.5) {
    return 'out_of_scope'
  }

  return 'low_confidence'
}
