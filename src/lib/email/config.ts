// Conditional import to allow build to succeed without resend
interface ResendLike {
  emails: {
    send: (params: unknown) => Promise<{ id: string }>
  }
}

let resend: ResendLike

try {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { Resend } = require('resend')
  const resendKey = process.env.RESEND_API_KEY || 're_placeholder'
  resend = new Resend(resendKey) as ResendLike
} catch {
  console.warn('Resend not available, email functionality disabled')
  resend = {
    emails: {
      send: async () => {
        console.warn('Email sending disabled - resend not installed')
        return { id: 'disabled' }
      }
    }
  }
}

export { resend }

export const EMAIL_CONFIG = {
  from: 'Ordrly <<EMAIL>>',
  replyTo: '<EMAIL>',
} as const
