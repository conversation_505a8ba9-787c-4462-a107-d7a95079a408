import { resend, EMAIL_CONFIG } from './config'
import { emailTemplates } from './templates'

export interface EmailData {
  name?: string
  email: string
  [key: string]: string | number | boolean | undefined
}

export async function sendEmail(
  to: string,
  templateKey: keyof typeof emailTemplates,
  data: EmailData
) {
  try {
    const template = emailTemplates[templateKey]

    const result = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to,
      subject: template.subject,
      text: template.text(data),
      replyTo: EMAIL_CONFIG.replyTo,
    })

    console.log('Email sent successfully:', result)
    return { success: true, result }
  } catch (error) {
    console.error('Failed to send email:', error)
    return { success: false, error }
  }
}

export async function sendWelcomeEmail(email: string, name?: string) {
  return sendEmail(email, 'welcome', { email, name })
}

export async function sendEmailVerifiedEmail(email: string, name?: string) {
  return sendEmail(email, 'emailVerified', { email, name })
}

export async function sendSubscriptionConfirmedEmail(
  email: string,
  planName: string,
  amount: string,
  name?: string
) {
  return sendEmail(email, 'subscriptionConfirmed', {
    email,
    name,
    planName,
    amount,
  })
}

export async function sendSubscriptionCanceledEmail(
  email: string,
  endDate: string,
  name?: string
) {
  return sendEmail(email, 'subscriptionCanceled', {
    email,
    name,
    endDate,
  })
}

export async function sendPaymentFailedEmail(
  email: string,
  planName: string,
  retryDate: string,
  name?: string
) {
  return sendEmail(email, 'paymentFailed', {
    email,
    name,
    planName,
    retryDate,
  })
}

export async function sendCreditsAddedEmail(
  email: string,
  credits: number,
  total: number,
  name?: string
) {
  return sendEmail(email, 'creditsAdded', {
    email,
    name,
    credits,
    total,
  })
}

export async function sendReferralCreditsEarnedEmail(
  email: string,
  totalCredits: number,
  referralUrl: string,
  name?: string
) {
  return sendEmail(email, 'referralCreditsEarned', {
    email,
    name,
    totalCredits,
    referralUrl,
  })
}

export async function sendAbandonmentNudgeEmail(
  email: string,
  ruleType: string,
  address: string,
  unsubscribeToken: string,
  name?: string
) {
  return sendEmail(email, 'abandonmentNudge', {
    email,
    name,
    ruleType,
    address,
    unsubscribeToken,
  })
}

export async function sendUpgradeReminderEmail(
  email: string,
  ruleType: string,
  unsubscribeToken: string,
  name?: string
) {
  return sendEmail(email, 'upgradeReminder', {
    email,
    name,
    ruleType,
    unsubscribeToken,
  })
}

export async function sendUsageLimitWarningEmail(
  email: string,
  remaining: number,
  name?: string
) {
  return sendEmail(email, 'usageLimitWarning', {
    email,
    name,
    remaining,
  })
}
