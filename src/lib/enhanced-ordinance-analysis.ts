/**
 * Enhanced Ordinance Analysis with Tier-Specific Features
 * Extends base analysis with red flags, clause extraction, and enhanced sources
 */

import { getAIConfig, logAIUsage } from '@/lib/ai-config'
import { analyzeOrdinanceContent, OrdinanceAnalysisInput, OrdinanceAnalysisOutput } from '@/lib/ordinance-analysis'
import { getTierConfig, hasFeatureAccess, isFeatureEnabled } from '@/lib/tier-config'
import type { SubscriptionTier } from '@/lib/tier-config'

export interface RedFlag {
  id: string
  type: 'safety' | 'inspection' | 'compliance' | 'code_violation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  ordinance_citation?: string
  recommendation?: string
}

export interface OrdinanceClause {
  id: string
  section_number?: string
  title: string
  full_text: string
  summary?: string
  tags: string[]
  source_url?: string
  document_title?: string
}

export interface EnhancedAnalysisOutput extends OrdinanceAnalysisOutput {
  red_flags: RedFlag[]
  clauses: OrdinanceClause[]
  sources_analyzed: number
  tier_features_used: string[]
  enhanced_summary?: string
}

export interface EnhancedAnalysisOptions {
  enableLogging?: boolean
  fallbackOnError?: boolean
  maxSources?: number
  includeRedFlags?: boolean
  includeClauses?: boolean
}

/**
 * Enhanced ordinance analysis with tier-specific features
 */
export async function analyzeOrdinanceWithTier(
  input: OrdinanceAnalysisInput,
  userTier: SubscriptionTier,
  options: EnhancedAnalysisOptions = {}
): Promise<EnhancedAnalysisOutput> {
  const {
    enableLogging = process.env.NODE_ENV === 'development',
    fallbackOnError = true,
    maxSources = 5,
    includeRedFlags = true,
    includeClauses = true
  } = options

  const tierConfig = getTierConfig(userTier)
  const featuresUsed: string[] = []

  try {
    // Start with base analysis
    const baseAnalysis = await analyzeOrdinanceContent(input, {
      enableLogging,
      fallbackOnError
    })

    // Initialize enhanced output
    const enhancedOutput: EnhancedAnalysisOutput = {
      ...baseAnalysis,
      red_flags: [],
      clauses: [],
      sources_analyzed: Math.min(maxSources, tierConfig.maxSources),
      tier_features_used: featuresUsed
    }

    // Add red flags for Pro+ tiers
    if (includeRedFlags && hasFeatureAccess(userTier, 'enableRedFlags') && isFeatureEnabled('RED_FLAGS_ENABLED')) {
      try {
        const redFlags = await detectRedFlags(input, enableLogging)
        enhancedOutput.red_flags = redFlags
        featuresUsed.push('red_flags')

        if (enableLogging) {
          console.log(`🚩 Detected ${redFlags.length} red flags`)
        }
      } catch (error) {
        console.error('Red flag detection failed:', error)
        if (!fallbackOnError) throw error
      }
    }

    // Add clause extraction for Appraiser tier
    if (includeClauses && hasFeatureAccess(userTier, 'enableClauseBrowser') && isFeatureEnabled('CLAUSE_BROWSER_ENABLED')) {
      try {
        const clauses = await extractClauses(input, enableLogging)
        enhancedOutput.clauses = clauses
        featuresUsed.push('clause_browser')

        if (enableLogging) {
          console.log(`📋 Extracted ${clauses.length} clauses`)
        }
      } catch (error) {
        console.error('Clause extraction failed:', error)
        if (!fallbackOnError) throw error
      }
    }

    // Enhanced summary for Pro+ tiers
    if (hasFeatureAccess(userTier, 'enableCustomInput')) {
      try {
        const enhancedSummary = await generateEnhancedSummary(
          baseAnalysis.summary,
          enhancedOutput.red_flags,
          enhancedOutput.clauses,
          enableLogging
        )
        enhancedOutput.enhanced_summary = enhancedSummary
        featuresUsed.push('enhanced_summary')
      } catch (error) {
        console.error('Enhanced summary generation failed:', error)
        if (!fallbackOnError) throw error
      }
    }

    enhancedOutput.tier_features_used = featuresUsed

    if (enableLogging) {
      console.log(`✅ Enhanced analysis complete for ${userTier} tier`)
      console.log(`🎯 Features used: ${featuresUsed.join(', ')}`)
    }

    return enhancedOutput

  } catch (error) {
    console.error('Enhanced ordinance analysis error:', error)

    if (fallbackOnError) {
      // Return base analysis with empty enhanced features
      const baseAnalysis = await analyzeOrdinanceContent(input, {
        enableLogging,
        fallbackOnError: true
      })

      return {
        ...baseAnalysis,
        red_flags: [],
        clauses: [],
        sources_analyzed: 1,
        tier_features_used: [],
        error: true,
        error_message: `Enhanced analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    } else {
      throw error
    }
  }
}

/**
 * Detect red flags in ordinance content using AI
 */
async function detectRedFlags(input: OrdinanceAnalysisInput, enableLogging: boolean): Promise<RedFlag[]> {
  const { OpenAI } = await import('openai')
  const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY })
  const aiConfig = getAIConfig()

  const prompt = `Analyze the following ordinance content for potential red flags, safety issues, and common inspection problems for ${input.project_type} projects in ${input.region}.

Focus on:
1. Safety hazards (electrical, structural, fire)
2. Common code violations
3. Inspection failure points
4. Permit requirements that are often missed

Document text:
${input.document_text.substring(0, 8000)}

Return a JSON array of red flags with this structure:
{
  "red_flags": [
    {
      "type": "safety|inspection|compliance|code_violation",
      "severity": "low|medium|high|critical",
      "title": "Brief title",
      "description": "Detailed description",
      "ordinance_citation": "Section reference if available",
      "recommendation": "What to do about it"
    }
  ]
}

Only include actual red flags found in the content. Return empty array if none found.`

  if (enableLogging) {
    logAIUsage(prompt, aiConfig, `for red flag detection in ${input.region}`)
  }

  const response = await openai.chat.completions.create({
    model: aiConfig.model,
    messages: [
      {
        role: 'system',
        content: 'You are an expert building inspector and code compliance specialist. Identify potential safety issues and code violations.'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: 0.1,
    max_tokens: 4000
  })

  const aiResponse = response.choices[0]?.message?.content
  if (!aiResponse) {
    throw new Error('No response from red flag detection')
  }

  try {
    const parsed = JSON.parse(aiResponse)
    return (parsed.red_flags || []).map((flag: { type: string; severity: string; title: string; description: string; ordinance_citation?: string; recommendation?: string }, index: number) => ({
      id: `rf_${Date.now()}_${index}`,
      ...flag
    }))
  } catch (parseError) {
    console.error('Failed to parse red flags response:', parseError)
    return []
  }
}

/**
 * Extract structured clauses from ordinance content
 */
async function extractClauses(input: OrdinanceAnalysisInput, enableLogging: boolean): Promise<OrdinanceClause[]> {
  // This would typically involve more sophisticated text processing
  // For now, return a simplified implementation
  const clauses: OrdinanceClause[] = []

  // Simple clause extraction based on section patterns
  const sectionPattern = /(?:Section|Sec\.|§)\s*(\d+(?:\.\d+)*)[:\.]?\s*([^\n]+)/gi
  const matches = input.document_text.matchAll(sectionPattern)

  let index = 0
  for (const match of matches) {
    if (index >= 10) break // Limit to 10 clauses for performance

    const sectionNumber = match[1]
    const title = match[2]?.trim()

    if (title && title.length > 10) {
      clauses.push({
        id: `clause_${Date.now()}_${index}`,
        section_number: sectionNumber,
        title: title,
        full_text: match[0],
        summary: title.length > 100 ? title.substring(0, 100) + '...' : title,
        tags: [input.project_type],
        source_url: undefined,
        document_title: `${input.region} Ordinances`
      })
      index++
    }
  }

  if (enableLogging) {
    console.log(`📋 Extracted ${clauses.length} clauses from ordinance content`)
  }

  return clauses
}

/**
 * Generate enhanced summary incorporating red flags and clauses
 */
async function generateEnhancedSummary(
  baseSummary: string,
  redFlags: RedFlag[],
  clauses: OrdinanceClause[],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  enableLogging: boolean // Unused parameter - reserved for future logging
): Promise<string> {
  if (redFlags.length === 0 && clauses.length === 0) {
    return baseSummary
  }

  let enhancement = baseSummary + '\n\n'

  if (redFlags.length > 0) {
    enhancement += '⚠️ **Important Safety & Compliance Notes:**\n'
    redFlags.forEach(flag => {
      const icon = flag.severity === 'critical' ? '🚨' : flag.severity === 'high' ? '⚠️' : '⚡'
      enhancement += `${icon} ${flag.title}: ${flag.description}\n`
    })
    enhancement += '\n'
  }

  if (clauses.length > 0) {
    enhancement += '📋 **Key Ordinance Sections:**\n'
    clauses.slice(0, 3).forEach(clause => {
      enhancement += `• Section ${clause.section_number}: ${clause.title}\n`
    })
    if (clauses.length > 3) {
      enhancement += `• ...and ${clauses.length - 3} more sections\n`
    }
  }

  return enhancement
}
