/**
 * Fallback Logging System
 * Privacy-conscious logging for unanswered queries and fallback events
 */

import { createClient } from '@/lib/supabase/client'
import { createServerClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { FallbackType } from './fallback-messages'

export interface UnansweredQuery {
  id?: string
  user_id: string
  conversation_id?: string
  query_text: string
  fallback_reason: string
  confidence_score?: number
  jurisdiction_name?: string
  rule_type?: string
  created_at?: string
}

export interface FallbackLogEntry {
  userId: string
  conversationId?: string
  query: string
  fallbackType: FallbackType
  confidenceScore: number
  jurisdiction?: string
  ruleType?: string
  metadata?: Record<string, any>
}

/**
 * Log a fallback event to the database
 */
export async function logFallbackEvent(
  entry: FallbackLogEntry,
  supabase?: SupabaseClient
): Promise<{ success: boolean; error?: string }> {
  try {
    // Use provided client or create server client
    const client = supabase || await createServerClient()
    
    const logData: Omit<UnansweredQuery, 'id' | 'created_at'> = {
      user_id: entry.userId,
      conversation_id: entry.conversationId,
      query_text: entry.query.substring(0, 2000), // Truncate long queries
      fallback_reason: `${entry.fallbackType} (confidence: ${Math.round(entry.confidenceScore * 100)}%)`,
      confidence_score: entry.confidenceScore,
      jurisdiction_name: entry.jurisdiction,
      rule_type: entry.ruleType
    }

    const { error } = await client
      .from('unanswered_queries')
      .insert(logData)

    if (error) {
      console.error('Failed to log fallback event:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error logging fallback event:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Log fallback event asynchronously (non-blocking)
 */
export function logFallbackEventAsync(
  entry: FallbackLogEntry,
  supabase?: SupabaseClient
): void {
  // Fire and forget - don't block the response
  logFallbackEvent(entry, supabase).catch(error => {
    console.warn('Async fallback logging failed:', error)
  })
}

/**
 * Get fallback statistics for admin dashboard
 */
export async function getFallbackStats(
  timeframe: 'day' | 'week' | 'month' = 'week',
  supabase?: SupabaseClient
): Promise<{
  totalFallbacks: number
  fallbacksByType: Record<string, number>
  topQueries: Array<{ query: string; count: number }>
  jurisdictionBreakdown: Record<string, number>
}> {
  try {
    const client = supabase || await createServerClient()
    
    // Calculate date range
    const now = new Date()
    const daysBack = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30
    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000))

    // Get fallback data
    const { data: fallbacks, error } = await client
      .from('unanswered_queries')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    // Process statistics
    const totalFallbacks = fallbacks?.length || 0
    const fallbacksByType: Record<string, number> = {}
    const queryCount: Record<string, number> = {}
    const jurisdictionBreakdown: Record<string, number> = {}

    fallbacks?.forEach(fallback => {
      // Count by fallback type
      const reason = fallback.fallback_reason || 'unknown'
      const type = reason.split(' ')[0] // Extract type from reason
      fallbacksByType[type] = (fallbacksByType[type] || 0) + 1

      // Count similar queries (first 50 chars for grouping)
      const queryKey = fallback.query_text.substring(0, 50).toLowerCase()
      queryCount[queryKey] = (queryCount[queryKey] || 0) + 1

      // Count by jurisdiction
      const jurisdiction = fallback.jurisdiction_name || 'Unknown'
      jurisdictionBreakdown[jurisdiction] = (jurisdictionBreakdown[jurisdiction] || 0) + 1
    })

    // Get top queries
    const topQueries = Object.entries(queryCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }))

    return {
      totalFallbacks,
      fallbacksByType,
      topQueries,
      jurisdictionBreakdown
    }
  } catch (error) {
    console.error('Error getting fallback stats:', error)
    return {
      totalFallbacks: 0,
      fallbacksByType: {},
      topQueries: [],
      jurisdictionBreakdown: {}
    }
  }
}

/**
 * Get recent unanswered queries for admin review
 */
export async function getRecentUnansweredQueries(
  limit: number = 50,
  supabase?: SupabaseClient
): Promise<UnansweredQuery[]> {
  try {
    const client = supabase || await createServerClient()
    
    const { data, error } = await client
      .from('unanswered_queries')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw error
    }

    return data || []
  } catch (error) {
    console.error('Error getting recent unanswered queries:', error)
    return []
  }
}

/**
 * Check if fallback logging is enabled
 */
export function isFallbackLoggingEnabled(): boolean {
  return process.env.CHAT_FALLBACK_LOGGING !== 'false'
}

/**
 * Create a sanitized log entry (removes sensitive data)
 */
export function sanitizeLogEntry(entry: FallbackLogEntry): FallbackLogEntry {
  return {
    ...entry,
    // Remove potentially sensitive metadata
    metadata: entry.metadata ? {
      confidenceScore: entry.metadata.confidenceScore,
      threshold: entry.metadata.threshold,
      scoresCount: entry.metadata.scoresCount
    } : undefined
  }
}

/**
 * Batch log multiple fallback events
 */
export async function logMultipleFallbackEvents(
  entries: FallbackLogEntry[],
  supabase?: SupabaseClient
): Promise<{ success: boolean; successCount: number; errors: string[] }> {
  const client = supabase || await createServerClient()
  const errors: string[] = []
  let successCount = 0

  for (const entry of entries) {
    const result = await logFallbackEvent(entry, client)
    if (result.success) {
      successCount++
    } else {
      errors.push(result.error || 'Unknown error')
    }
  }

  return {
    success: errors.length === 0,
    successCount,
    errors
  }
}
