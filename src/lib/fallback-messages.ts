/**
 * Fallback Message Templates
 * Standardized responses for low-confidence scenarios
 */

export type FallbackType = 'low_confidence' | 'no_data' | 'out_of_scope' | 'partial_answer'

export interface FallbackMessage {
  content: string
  type: FallbackType
  includeGuidance: boolean
  metadata: {
    isFallback: true
    fallbackType: FallbackType
    timestamp: string
  }
}

/**
 * Fallback message templates
 */
const FALLBACK_TEMPLATES: Record<FallbackType, string> = {
  low_confidence: `I'm not confident about this answer based on the available information in our knowledge base. The sources I found may not be directly relevant to your specific question.

For accurate guidance on this matter, I recommend consulting your local building department or a qualified professional who can provide authoritative information for your specific situation.`,

  no_data: `I don't have sufficient information in our knowledge base to answer this question reliably. This could mean the topic isn't covered in our current ordinance database, or your question may require more specific local regulations.

Please check with your local building department or relevant authorities for the most accurate and up-to-date requirements for your area.`,

  out_of_scope: `This question appears to be outside the scope of building codes and ordinances that I'm designed to help with. My knowledge is focused on compliance requirements, permits, setbacks, and similar regulatory matters.

For questions beyond building compliance, please consult with the appropriate local authorities, professionals, or specialists in the relevant field.`,

  partial_answer: `I found some relevant information, but I'm not completely confident this covers all aspects of your question. Here's what I can tell you based on the available data:

{partial_content}

**Important**: This information may be incomplete. Please verify these details with your local building department or a qualified professional to ensure you have the complete requirements for your specific situation.`
}

/**
 * Generate a fallback message
 */
export function generateFallbackMessage(
  type: FallbackType,
  partialContent?: string
): FallbackMessage {
  let content = FALLBACK_TEMPLATES[type]
  
  // Handle partial answer template
  if (type === 'partial_answer' && partialContent) {
    content = content.replace('{partial_content}', partialContent)
  }

  return {
    content,
    type,
    includeGuidance: true,
    metadata: {
      isFallback: true,
      fallbackType: type,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Get guidance text for consulting authorities
 */
export function getGuidanceText(jurisdiction?: string): string {
  const baseGuidance = "Always verify compliance requirements with local authorities."
  
  if (jurisdiction && jurisdiction !== 'TBD') {
    return `Always verify compliance requirements with ${jurisdiction} building department or local authorities.`
  }
  
  return baseGuidance
}

/**
 * Create a fallback response with proper formatting
 */
export function createFallbackResponse(
  type: FallbackType,
  jurisdiction?: string,
  partialContent?: string
): {
  content: string
  metadata: {
    isFallback: true
    fallbackType: FallbackType
    timestamp: string
    guidance: string
  }
} {
  const fallbackMessage = generateFallbackMessage(type, partialContent)
  const guidance = getGuidanceText(jurisdiction)
  
  // Add guidance to the end of the message
  const content = `${fallbackMessage.content}\n\n*${guidance}*`
  
  return {
    content,
    metadata: {
      isFallback: true,
      fallbackType: type,
      timestamp: new Date().toISOString(),
      guidance
    }
  }
}

/**
 * Check if a message is a fallback message
 */
export function isFallbackMessage(metadata: any): boolean {
  return metadata && metadata.isFallback === true
}

/**
 * Get fallback type from message metadata
 */
export function getFallbackType(metadata: any): FallbackType | null {
  if (isFallbackMessage(metadata)) {
    return metadata.fallbackType || null
  }
  return null
}

/**
 * Determine appropriate fallback type based on context
 */
export function selectFallbackType(
  confidenceScore: number,
  hasData: boolean,
  threshold: number
): FallbackType {
  if (!hasData || confidenceScore === 0) {
    return 'no_data'
  }
  
  if (confidenceScore < threshold * 0.5) {
    return 'out_of_scope'
  }
  
  return 'low_confidence'
}

/**
 * Format confidence score for logging
 */
export function formatConfidenceScore(score: number): string {
  return `${Math.round(score * 100)}%`
}

/**
 * Create fallback message for logging purposes
 */
export function createFallbackLogEntry(
  query: string,
  fallbackType: FallbackType,
  confidenceScore: number,
  jurisdiction?: string
): {
  query: string
  fallbackType: FallbackType
  confidenceScore: number
  formattedScore: string
  jurisdiction?: string
  timestamp: string
} {
  return {
    query,
    fallbackType,
    confidenceScore,
    formattedScore: formatConfidenceScore(confidenceScore),
    jurisdiction,
    timestamp: new Date().toISOString()
  }
}
