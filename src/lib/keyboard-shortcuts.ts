'use client'

import { useEffect, useCallback } from 'react'

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  metaKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  action: () => void
  description: string
  category?: string
}

// Global keyboard shortcuts
export const globalShortcuts: KeyboardShortcut[] = [
  {
    key: 'k',
    metaKey: true,
    action: () => {
      // Focus search input
      const searchInput = document.querySelector('input[placeholder*="address"]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        searchInput.select()
      }
    },
    description: 'Focus search input',
    category: 'Navigation'
  },
  {
    key: '/',
    action: () => {
      // Focus search input
      const searchInput = document.querySelector('input[placeholder*="address"]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        searchInput.select()
      }
    },
    description: 'Focus search input',
    category: 'Navigation'
  },
  {
    key: 'h',
    action: () => {
      // Go to home
      window.location.href = '/'
    },
    description: 'Go to home page',
    category: 'Navigation'
  },
  {
    key: 'p',
    action: () => {
      // Go to pricing
      window.location.href = '/pricing'
    },
    description: 'Go to pricing page',
    category: 'Navigation'
  },
  {
    key: 'f',
    action: () => {
      // Go to FAQ
      window.location.href = '/faq'
    },
    description: 'Go to FAQ page',
    category: 'Navigation'
  },
  {
    key: '?',
    shiftKey: true,
    action: () => {
      // Show keyboard shortcuts help
      showKeyboardShortcutsModal()
    },
    description: 'Show keyboard shortcuts',
    category: 'Help'
  },
  {
    key: 'Escape',
    action: () => {
      // Close modals, clear focus
      const modals = document.querySelectorAll('[role="dialog"]')
      modals.forEach(modal => {
        const closeButton = modal.querySelector('[aria-label*="close"], [aria-label*="Close"]') as HTMLButtonElement
        if (closeButton) {
          closeButton.click()
        }
      })
      
      // Clear focus from inputs
      const activeElement = document.activeElement as HTMLElement
      if (activeElement && activeElement.blur) {
        activeElement.blur()
      }
    },
    description: 'Close modals and clear focus',
    category: 'General'
  }
]

// Hook for using keyboard shortcuts
export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[] = globalShortcuts) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      // Allow escape to work in inputs
      if (event.key !== 'Escape') {
        return
      }
    }

    for (const shortcut of shortcuts) {
      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase()
      const ctrlMatches = !!shortcut.ctrlKey === event.ctrlKey
      const metaMatches = !!shortcut.metaKey === event.metaKey
      const shiftMatches = !!shortcut.shiftKey === event.shiftKey
      const altMatches = !!shortcut.altKey === event.altKey

      if (keyMatches && ctrlMatches && metaMatches && shiftMatches && altMatches) {
        event.preventDefault()
        shortcut.action()
        break
      }
    }
  }, [shortcuts])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])
}

// Function to show keyboard shortcuts modal
function showKeyboardShortcutsModal() {
  // Create modal dynamically
  const modal = document.createElement('div')
  modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'
  modal.setAttribute('role', 'dialog')
  modal.setAttribute('aria-labelledby', 'shortcuts-title')
  modal.setAttribute('aria-modal', 'true')

  const shortcuts = globalShortcuts.reduce((acc, shortcut) => {
    const category = shortcut.category || 'General'
    if (!acc[category]) acc[category] = []
    acc[category].push(shortcut)
    return acc
  }, {} as Record<string, KeyboardShortcut[]>)

  modal.innerHTML = `
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 id="shortcuts-title" class="text-xl font-semibold text-gray-900 dark:text-white">
            Keyboard Shortcuts
          </h2>
          <button 
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded"
            aria-label="Close shortcuts modal"
            onclick="this.closest('[role=dialog]').remove()"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          ${Object.entries(shortcuts).map(([category, categoryShortcuts]) => `
            <div>
              <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">${category}</h3>
              <div class="space-y-2">
                ${categoryShortcuts.map(shortcut => `
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">${shortcut.description}</span>
                    <div class="flex items-center space-x-1">
                      ${shortcut.metaKey ? '<kbd class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded">⌘</kbd>' : ''}
                      ${shortcut.ctrlKey ? '<kbd class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded">Ctrl</kbd>' : ''}
                      ${shortcut.shiftKey ? '<kbd class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded">⇧</kbd>' : ''}
                      ${shortcut.altKey ? '<kbd class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded">Alt</kbd>' : ''}
                      <kbd class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded">${shortcut.key === ' ' ? 'Space' : shortcut.key}</kbd>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>
        
        <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p class="text-xs text-gray-500 dark:text-gray-400">
            Press <kbd class="px-1 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 rounded">Esc</kbd> to close this modal
          </p>
        </div>
      </div>
    </div>
  `

  // Close on escape or background click
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove()
    }
  })

  modal.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      modal.remove()
    }
  })

  document.body.appendChild(modal)
  
  // Focus the modal for accessibility
  modal.focus()
}

// Utility to format keyboard shortcut for display
export function formatShortcut(shortcut: KeyboardShortcut): string {
  const parts = []
  
  if (shortcut.metaKey) parts.push('⌘')
  if (shortcut.ctrlKey) parts.push('Ctrl')
  if (shortcut.shiftKey) parts.push('⇧')
  if (shortcut.altKey) parts.push('Alt')
  
  parts.push(shortcut.key === ' ' ? 'Space' : shortcut.key)
  
  return parts.join(' + ')
}

// Search-specific shortcuts
export const searchShortcuts: KeyboardShortcut[] = [
  {
    key: 'Enter',
    action: () => {
      const searchButton = document.querySelector('button[type="submit"], button:contains("Search")') as HTMLButtonElement
      if (searchButton) {
        searchButton.click()
      }
    },
    description: 'Submit search',
    category: 'Search'
  },
  {
    key: 'ArrowDown',
    action: () => {
      // Navigate to next suggestion
      const suggestions = document.querySelectorAll('[role="option"]')
      const current = document.querySelector('[role="option"][aria-selected="true"]')
      if (suggestions.length > 0) {
        const currentIndex = Array.from(suggestions).indexOf(current as Element)
        const nextIndex = (currentIndex + 1) % suggestions.length
        const nextSuggestion = suggestions[nextIndex] as HTMLElement
        nextSuggestion.focus()
      }
    },
    description: 'Next suggestion',
    category: 'Search'
  },
  {
    key: 'ArrowUp',
    action: () => {
      // Navigate to previous suggestion
      const suggestions = document.querySelectorAll('[role="option"]')
      const current = document.querySelector('[role="option"][aria-selected="true"]')
      if (suggestions.length > 0) {
        const currentIndex = Array.from(suggestions).indexOf(current as Element)
        const prevIndex = currentIndex <= 0 ? suggestions.length - 1 : currentIndex - 1
        const prevSuggestion = suggestions[prevIndex] as HTMLElement
        prevSuggestion.focus()
      }
    },
    description: 'Previous suggestion',
    category: 'Search'
  }
]
