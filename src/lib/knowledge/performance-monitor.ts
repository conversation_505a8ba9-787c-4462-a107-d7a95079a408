// Epic 7: Knowledge Base Performance Monitoring
// Performance tracking and analytics for knowledge refresh operations

import { createClient } from '@/lib/supabase/client'

export interface PerformanceMetrics {
  operation_type: 'staleness_check' | 'auto_refresh' | 'manual_refresh' | 'version_update'
  started_at: string
  completed_at: string
  duration_ms: number
  items_processed: number
  items_per_second: number
  memory_usage_mb?: number
  cpu_usage_percent?: number
  error_count: number
  success_rate: number
  metadata: Record<string, any>
}

export interface PerformanceAlert {
  id: string
  alert_type: 'performance_degradation' | 'high_error_rate' | 'job_timeout' | 'resource_exhaustion'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  metrics: PerformanceMetrics
  triggered_at: string
  resolved_at?: string
  acknowledged_by?: string
}

export class KnowledgePerformanceMonitor {
  private supabase = createClient()
  private metrics: Map<string, PerformanceMetrics> = new Map()
  private alerts: PerformanceAlert[] = []

  // Performance thresholds
  private readonly THRESHOLDS = {
    MAX_DURATION_MS: 5 * 60 * 1000, // 5 minutes
    MIN_ITEMS_PER_SECOND: 10,
    MAX_ERROR_RATE: 0.05, // 5%
    MAX_MEMORY_MB: 512,
    MAX_CPU_PERCENT: 80
  }

  /**
   * Start tracking performance for an operation
   */
  startTracking(operationId: string, operationType: PerformanceMetrics['operation_type'], metadata: Record<string, any> = {}) {
    const metrics: PerformanceMetrics = {
      operation_type: operationType,
      started_at: new Date().toISOString(),
      completed_at: '',
      duration_ms: 0,
      items_processed: 0,
      items_per_second: 0,
      error_count: 0,
      success_rate: 1.0,
      metadata
    }

    this.metrics.set(operationId, metrics)
    console.log(`📊 Performance tracking started for ${operationType} operation: ${operationId}`)
  }

  /**
   * Update progress during operation
   */
  updateProgress(operationId: string, itemsProcessed: number, errorCount: number = 0) {
    const metrics = this.metrics.get(operationId)
    if (!metrics) return

    metrics.items_processed = itemsProcessed
    metrics.error_count = errorCount
    
    const elapsedMs = Date.now() - new Date(metrics.started_at).getTime()
    metrics.items_per_second = elapsedMs > 0 ? (itemsProcessed / elapsedMs) * 1000 : 0
    metrics.success_rate = itemsProcessed > 0 ? (itemsProcessed - errorCount) / itemsProcessed : 1.0

    // Check for performance issues
    this.checkPerformanceThresholds(operationId, metrics)
  }

  /**
   * Complete tracking and store metrics
   */
  async completeTracking(operationId: string, finalItemCount?: number, finalErrorCount?: number) {
    const metrics = this.metrics.get(operationId)
    if (!metrics) return

    metrics.completed_at = new Date().toISOString()
    metrics.duration_ms = new Date(metrics.completed_at).getTime() - new Date(metrics.started_at).getTime()
    
    if (finalItemCount !== undefined) {
      metrics.items_processed = finalItemCount
    }
    if (finalErrorCount !== undefined) {
      metrics.error_count = finalErrorCount
    }

    metrics.items_per_second = metrics.duration_ms > 0 ? (metrics.items_processed / metrics.duration_ms) * 1000 : 0
    metrics.success_rate = metrics.items_processed > 0 ? (metrics.items_processed - metrics.error_count) / metrics.items_processed : 1.0

    // Store metrics in database
    await this.storeMetrics(operationId, metrics)

    // Final performance check
    this.checkPerformanceThresholds(operationId, metrics)

    console.log(`✅ Performance tracking completed for ${metrics.operation_type}: ${operationId}`, {
      duration: `${metrics.duration_ms}ms`,
      itemsPerSecond: metrics.items_per_second.toFixed(2),
      successRate: `${(metrics.success_rate * 100).toFixed(1)}%`
    })

    this.metrics.delete(operationId)
  }

  /**
   * Check performance against thresholds and generate alerts
   */
  private checkPerformanceThresholds(operationId: string, metrics: PerformanceMetrics) {
    const alerts: PerformanceAlert[] = []

    // Check duration threshold
    if (metrics.duration_ms > this.THRESHOLDS.MAX_DURATION_MS) {
      alerts.push({
        id: `${operationId}-timeout`,
        alert_type: 'job_timeout',
        severity: 'high',
        message: `Operation ${operationId} exceeded maximum duration (${metrics.duration_ms}ms > ${this.THRESHOLDS.MAX_DURATION_MS}ms)`,
        metrics,
        triggered_at: new Date().toISOString()
      })
    }

    // Check processing speed
    if (metrics.items_per_second < this.THRESHOLDS.MIN_ITEMS_PER_SECOND && metrics.items_processed > 10) {
      alerts.push({
        id: `${operationId}-slow`,
        alert_type: 'performance_degradation',
        severity: 'medium',
        message: `Operation ${operationId} processing slowly (${metrics.items_per_second.toFixed(2)} items/sec < ${this.THRESHOLDS.MIN_ITEMS_PER_SECOND})`,
        metrics,
        triggered_at: new Date().toISOString()
      })
    }

    // Check error rate
    if (metrics.success_rate < (1 - this.THRESHOLDS.MAX_ERROR_RATE) && metrics.items_processed > 5) {
      alerts.push({
        id: `${operationId}-errors`,
        alert_type: 'high_error_rate',
        severity: 'high',
        message: `Operation ${operationId} has high error rate (${((1 - metrics.success_rate) * 100).toFixed(1)}% > ${this.THRESHOLDS.MAX_ERROR_RATE * 100}%)`,
        metrics,
        triggered_at: new Date().toISOString()
      })
    }

    // Check memory usage (if available)
    if (metrics.memory_usage_mb && metrics.memory_usage_mb > this.THRESHOLDS.MAX_MEMORY_MB) {
      alerts.push({
        id: `${operationId}-memory`,
        alert_type: 'resource_exhaustion',
        severity: 'high',
        message: `Operation ${operationId} using excessive memory (${metrics.memory_usage_mb}MB > ${this.THRESHOLDS.MAX_MEMORY_MB}MB)`,
        metrics,
        triggered_at: new Date().toISOString()
      })
    }

    // Store and log alerts
    for (const alert of alerts) {
      this.alerts.push(alert)
      console.warn(`⚠️ Performance Alert [${alert.severity}]:`, alert.message)
      
      // Store alert in database for admin visibility
      this.storeAlert(alert).catch(error => 
        console.error('Failed to store performance alert:', error)
      )
    }
  }

  /**
   * Store performance metrics in database
   */
  private async storeMetrics(operationId: string, metrics: PerformanceMetrics) {
    try {
      const { error } = await this.supabase
        .from('knowledge_performance_metrics')
        .insert({
          operation_id: operationId,
          operation_type: metrics.operation_type,
          started_at: metrics.started_at,
          completed_at: metrics.completed_at,
          duration_ms: metrics.duration_ms,
          items_processed: metrics.items_processed,
          items_per_second: metrics.items_per_second,
          memory_usage_mb: metrics.memory_usage_mb,
          cpu_usage_percent: metrics.cpu_usage_percent,
          error_count: metrics.error_count,
          success_rate: metrics.success_rate,
          metadata: metrics.metadata
        })

      if (error) {
        console.error('Failed to store performance metrics:', error)
      }
    } catch (error) {
      console.error('Error storing performance metrics:', error)
    }
  }

  /**
   * Store performance alert in database
   */
  private async storeAlert(alert: PerformanceAlert) {
    try {
      const { error } = await this.supabase
        .from('knowledge_performance_alerts')
        .insert({
          alert_type: alert.alert_type,
          severity: alert.severity,
          message: alert.message,
          operation_id: alert.metrics.metadata.operation_id || 'unknown',
          operation_type: alert.metrics.operation_type,
          metrics: alert.metrics,
          triggered_at: alert.triggered_at
        })

      if (error) {
        console.error('Failed to store performance alert:', error)
      }
    } catch (error) {
      console.error('Error storing performance alert:', error)
    }
  }

  /**
   * Get performance analytics for admin dashboard
   */
  async getPerformanceAnalytics(timeRange: '1h' | '24h' | '7d' | '30d' = '24h') {
    try {
      const timeRangeMap = {
        '1h': '1 hour',
        '24h': '24 hours',
        '7d': '7 days',
        '30d': '30 days'
      }

      const { data: metrics, error } = await this.supabase
        .from('knowledge_performance_metrics')
        .select('*')
        .gte('started_at', `now() - interval '${timeRangeMap[timeRange]}'`)
        .order('started_at', { ascending: false })

      if (error) {
        throw error
      }

      const { data: alerts, error: alertsError } = await this.supabase
        .from('knowledge_performance_alerts')
        .select('*')
        .gte('triggered_at', `now() - interval '${timeRangeMap[timeRange]}'`)
        .order('triggered_at', { ascending: false })

      if (alertsError) {
        throw alertsError
      }

      return {
        metrics: metrics || [],
        alerts: alerts || [],
        summary: this.calculateSummaryStats(metrics || [])
      }
    } catch (error) {
      console.error('Failed to get performance analytics:', error)
      return { metrics: [], alerts: [], summary: null }
    }
  }

  /**
   * Calculate summary statistics
   */
  private calculateSummaryStats(metrics: any[]) {
    if (metrics.length === 0) {
      return {
        total_operations: 0,
        avg_duration_ms: 0,
        avg_items_per_second: 0,
        avg_success_rate: 0,
        total_items_processed: 0,
        total_errors: 0
      }
    }

    const totalDuration = metrics.reduce((sum, m) => sum + m.duration_ms, 0)
    const totalItems = metrics.reduce((sum, m) => sum + m.items_processed, 0)
    const totalErrors = metrics.reduce((sum, m) => sum + m.error_count, 0)
    const avgSuccessRate = metrics.reduce((sum, m) => sum + m.success_rate, 0) / metrics.length

    return {
      total_operations: metrics.length,
      avg_duration_ms: Math.round(totalDuration / metrics.length),
      avg_items_per_second: totalItems > 0 ? Number((totalItems / (totalDuration / 1000)).toFixed(2)) : 0,
      avg_success_rate: Number((avgSuccessRate * 100).toFixed(1)),
      total_items_processed: totalItems,
      total_errors: totalErrors
    }
  }

  /**
   * Get current active operations
   */
  getActiveOperations() {
    return Array.from(this.metrics.entries()).map(([id, metrics]) => ({
      operation_id: id,
      ...metrics,
      elapsed_ms: Date.now() - new Date(metrics.started_at).getTime()
    }))
  }

  /**
   * Get recent alerts
   */
  getRecentAlerts(limit: number = 10) {
    return this.alerts
      .sort((a, b) => new Date(b.triggered_at).getTime() - new Date(a.triggered_at).getTime())
      .slice(0, limit)
  }
}

// Global instance for use across the application
export const performanceMonitor = new KnowledgePerformanceMonitor()

// Utility function to wrap operations with performance tracking
export async function withPerformanceTracking<T>(
  operationType: PerformanceMetrics['operation_type'],
  operation: (monitor: KnowledgePerformanceMonitor, operationId: string) => Promise<T>,
  metadata: Record<string, any> = {}
): Promise<T> {
  const operationId = `${operationType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  performanceMonitor.startTracking(operationId, operationType, metadata)
  
  try {
    const result = await operation(performanceMonitor, operationId)
    await performanceMonitor.completeTracking(operationId)
    return result
  } catch (error) {
    await performanceMonitor.completeTracking(operationId, undefined, 1)
    throw error
  }
}
