import { createServiceClient } from '@/lib/supabase/server'

export interface ChatLogEvent {
  level: 'info' | 'warning' | 'error'
  message: string
  userId?: string
  metadata?: Record<string, any>
}

export interface ChatConversationCreatedEvent extends ChatLogEvent {
  level: 'info'
  message: 'Chat conversation created'
  metadata: {
    conversationId: string
    address: string
    ruleType: string
    jurisdictionName: string
  }
}

export interface ChatMessageSentEvent extends ChatLogEvent {
  level: 'info'
  message: 'Chat message sent'
  metadata: {
    conversationId: string
    messageId: string
    role: 'user' | 'assistant'
    contentLength: number
    responseTimeMs?: number
  }
}

export interface ChatErrorEvent extends ChatLogEvent {
  level: 'error'
  metadata: {
    conversationId?: string
    messageId?: string
    errorType: string
    errorMessage: string
    stackTrace?: string
  }
}

export interface ChatCitationGeneratedEvent extends ChatLogEvent {
  level: 'info'
  message: 'Chat citations generated'
  metadata: {
    conversationId: string
    messageId: string
    citationCount: number
    citationTypes: string[]
  }
}

/**
 * Log chat events to the automation_logs table
 */
export async function logChatEvent(event: ChatLogEvent): Promise<void> {
  try {
    const supabase = createServiceClient()
    
    await supabase.from('automation_logs').insert({
      module: 'chat',
      level: event.level,
      message: event.message,
      user_id: event.userId || null,
      metadata: {
        ...event.metadata,
        timestamp: new Date().toISOString(),
        source: 'chat-ui'
      }
    })

    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      const logLevel = event.level === 'error' ? 'error' : event.level === 'warning' ? 'warn' : 'log'
      console[logLevel](`💬 CHAT ${event.level.toUpperCase()}:`, event.message, event.metadata)
    }
  } catch (error) {
    // Don't throw errors from logging - just log to console
    console.error('Failed to log chat event:', error)
  }
}

/**
 * Log conversation creation
 */
export async function logConversationCreated(
  userId: string,
  conversationId: string,
  address: string,
  ruleType: string,
  jurisdictionName: string
): Promise<void> {
  await logChatEvent({
    level: 'info',
    message: 'Chat conversation created',
    userId,
    metadata: {
      conversationId,
      address,
      ruleType,
      jurisdictionName
    }
  })
}

/**
 * Log message sent (user or assistant)
 */
export async function logMessageSent(
  userId: string,
  conversationId: string,
  messageId: string,
  role: 'user' | 'assistant',
  contentLength: number,
  responseTimeMs?: number
): Promise<void> {
  await logChatEvent({
    level: 'info',
    message: 'Chat message sent',
    userId,
    metadata: {
      conversationId,
      messageId,
      role,
      contentLength,
      responseTimeMs
    }
  })
}

/**
 * Log chat errors
 */
export async function logChatError(
  userId: string | undefined,
  error: Error,
  context: {
    conversationId?: string
    messageId?: string
    errorType: string
  }
): Promise<void> {
  await logChatEvent({
    level: 'error',
    message: `Chat error: ${context.errorType}`,
    userId,
    metadata: {
      conversationId: context.conversationId,
      messageId: context.messageId,
      errorType: context.errorType,
      errorMessage: error.message,
      stackTrace: error.stack
    }
  })
}

/**
 * Log citation generation
 */
export async function logCitationsGenerated(
  userId: string,
  conversationId: string,
  messageId: string,
  citationCount: number,
  citationTypes: string[]
): Promise<void> {
  await logChatEvent({
    level: 'info',
    message: 'Chat citations generated',
    userId,
    metadata: {
      conversationId,
      messageId,
      citationCount,
      citationTypes
    }
  })
}

/**
 * Log feature usage for analytics
 */
export async function logChatFeatureUsage(
  userId: string,
  feature: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await logChatEvent({
    level: 'info',
    message: `Chat feature used: ${feature}`,
    userId,
    metadata: {
      feature,
      ...metadata
    }
  })
}
