import { NextRequest, NextResponse } from 'next/server'

// Simple in-memory rate limiter
// In production, you'd want to use Redis or a similar store
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  message?: string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

export function createRateLimiter(config: RateLimitConfig) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    const ip = getClientIP(request)
    const key = `${ip}:${request.nextUrl.pathname}`
    const now = Date.now()

    // Clean up expired entries
    cleanupExpiredEntries(now)

    const record = rateLimitMap.get(key)

    if (!record || now > record.resetTime) {
      // First request or window expired
      rateLimitMap.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      return null // Allow request
    }

    if (record.count >= config.maxRequests) {
      // Rate limit exceeded
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: config.message || `Too many requests. Please try again in ${Math.ceil((record.resetTime - now) / 1000)} seconds.`,
          retryAfter: Math.ceil((record.resetTime - now) / 1000)
        },
        {
          status: 429,
          headers: {
            'Retry-After': Math.ceil((record.resetTime - now) / 1000).toString(),
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': record.resetTime.toString()
          }
        }
      )
    }

    // Increment counter
    record.count++

    return null // Allow request
  }
}

function getClientIP(request: NextRequest): string {
  // Try to get real IP from headers (for proxies/load balancers)
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }

  if (realIP) {
    return realIP
  }

  // Fallback to connection IP (not available in Edge Runtime)
  return 'unknown'
}

function cleanupExpiredEntries(now: number) {
  for (const [key, record] of rateLimitMap.entries()) {
    if (now > record.resetTime) {
      rateLimitMap.delete(key)
    }
  }
}

// Predefined rate limiters for common use cases
// Adjust limits for testing environment
const isTestEnvironment = process.env.NODE_ENV === 'test' || process.env.TESTING === 'true'

export const addressAutocompleteLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: isTestEnvironment ? 1000 : 60, // Higher limit for testing
  message: 'Too many address lookups. Please slow down.'
})

export const complianceSummaryLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: isTestEnvironment ? 1000 : 10, // Higher limit for testing
  message: 'Too many compliance requests. Please wait before making another request.'
})

export const generalAPILimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: isTestEnvironment ? 10000 : 100, // Higher limit for testing
  message: 'Too many API requests. Please try again later.'
})

// Utility function to apply rate limiting to API routes
export async function withRateLimit(
  request: NextRequest,
  limiter: (req: NextRequest) => Promise<NextResponse | null>,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  const rateLimitResponse = await limiter(request)

  if (rateLimitResponse) {
    return rateLimitResponse
  }

  return handler()
}
