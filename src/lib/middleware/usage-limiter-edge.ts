import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { createRateLimiter } from './rate-limiter'

// Trial limiter for unauthenticated users: 3 searches total per IP (lifetime)
const trialLimiter = createRateLimiter({
  windowMs: 365 * 24 * 60 * 60 * 1000, // 1 year (effectively permanent)
  maxRequests: 3, // 3 trial searches total
  message: 'You\'ve used your 3 free trial searches. Sign up for a trial account to get 500 searches per month!'
})

interface UsageStatus {
  canProceed: boolean
  currentUsage: number
  limitAmount: number
  extraCreditsAvailable: number
  subscriptionTier: string
  message?: string
}

export async function enforceUsageLimitsEdge(request: NextRequest): Promise<NextResponse | null> {
  // Skip usage limits in testing environment
  if (process.env.TESTING === 'true' || process.env.NODE_ENV === 'test') {
    return null // Continue without checking usage limits
  }

  // Only apply to API routes that consume usage
  const usageRoutes = [
    '/api/compliance/summary',
    '/api/analyze-ordinance',
  ]

  const isUsageRoute = usageRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  if (!isUsageRoute) {
    return null // Continue without checking
  }

  try {
    // Create Supabase client compatible with middleware/Edge Runtime
    // Use service role key for API key authentication, anon key for session auth
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // In middleware, we can't set cookies on the request
            // They will be set on the response later
          },
        },
      }
    )

    // Check for API key authentication first
    const authHeader = request.headers.get('authorization')
    let user = null
    let apiKeyUserId = null

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // This might be an API key request - check if it's a valid API key
      const apiKey = authHeader.substring(7)

      // Simple check for API key format (starts with 'ordrly_')
      if (apiKey.startsWith('ordrly_')) {
        try {
          // Hash the API key using Web Crypto API (Edge Runtime compatible)
          const encoder = new TextEncoder()
          const data = encoder.encode(apiKey)
          const hashBuffer = await crypto.subtle.digest('SHA-256', data)
          const hashArray = Array.from(new Uint8Array(hashBuffer))
          const keyHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')

          const { data: apiKeyData, error } = await supabase
            .from('api_keys')
            .select('user_id, profiles!inner(subscription_tier)')
            .eq('key_hash', keyHash)
            .eq('is_active', true)
            .single()

          if (!error && apiKeyData) {
            apiKeyUserId = apiKeyData.user_id
            console.log('🔑 [MIDDLEWARE] API key authentication successful for user:', apiKeyUserId)
          }
        } catch (error) {
          console.log('🔑 [MIDDLEWARE] API key validation failed:', error)
        }
      }
    }

    // If no API key, try regular session authentication
    if (!apiKeyUserId) {
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser()
        user = authUser
      } catch (error: unknown) {
        // Handle AuthSessionMissingError gracefully - expected when not logged in
        if (error && typeof error === 'object' && 'name' in error && error.name !== 'AuthSessionMissingError') {
          console.error('UsageLimiterEdge: Unexpected auth error:', error)
        }
        // Continue with user = null for unauthenticated requests
      }
    }

    // For unauthenticated users (no session AND no API key), enforce trial limits
    if (!user && !apiKeyUserId) {
      const trialLimitResponse = await trialLimiter(request)
      if (trialLimitResponse) {
        // Add signup prompt to the response
        const responseData = await trialLimitResponse.json()
        return NextResponse.json({
          ...responseData,
          upgradeRequired: true,
          signupPrompt: true,
          upgradeOptions: {
            starter: {
              name: 'Starter Plan',
              price: '$15/month',
              features: ['500 searches per month', 'AI chat assistance', 'Municipal compliance research'],
            },
            professional: {
              name: 'Professional Plan',
              price: '$50/month',
              features: ['2,000 searches per month', 'Priority support', 'Enhanced source analysis'],
            }
          }
        }, { status: 429 })
      }
      return null // Allow trial search to proceed
    }

    // Check usage limits for authenticated users (either session or API key)
    const userId = user?.id || apiKeyUserId
    const usageStatus = await checkUsageLimitEdge(supabase, userId)

    if (!usageStatus.canProceed) {
      return NextResponse.json(
        {
          error: 'Usage limit exceeded',
          message: usageStatus.message,
          usageStatus,
          upgradeRequired: true,
        },
        { status: 429 } // Too Many Requests
      )
    }

    // Add usage info to headers for the API route to use
    const response = NextResponse.next()
    response.headers.set('x-user-id', userId)
    response.headers.set('x-usage-status', JSON.stringify(usageStatus))

    return response
  } catch (error) {
    console.error('Error in usage limiter edge:', error)
    // Fail open - allow the request to continue
    return null
  }
}

async function checkUsageLimitEdge(supabase: any, userId: string): Promise<UsageStatus> {
  try {
    const { data, error } = await supabase.rpc('check_usage_limit', {
      user_id_param: userId,
    })

    if (error) {
      console.error('Error checking usage limit in edge:', error)
      throw error
    }

    const result = data[0]
    if (!result) {
      throw new Error('No usage data found for user')
    }

    let message = ''
    if (!result.can_proceed) {
      if (result.subscription_tier === 'trial') {
        message = `You've reached your trial limit of ${result.limit_amount} searches. Upgrade to continue using Ordrly.`
      } else if (result.subscription_tier === 'starter') {
        message = `You've reached your monthly limit of ${result.limit_amount} searches. Upgrade to Professional for 2,000 searches per month.`
      } else if (result.subscription_tier === 'professional') {
        message = `You've reached your monthly limit of ${result.limit_amount} searches. Your limit will reset next month.`
      } else {
        message = 'Usage limit exceeded'
      }
    }

    return {
      canProceed: result.can_proceed,
      currentUsage: result.current_usage,
      limitAmount: result.limit_amount,
      extraCreditsAvailable: result.extra_credits_available,
      subscriptionTier: result.subscription_tier,
      message,
    }
  } catch (error) {
    console.error('Error in checkUsageLimitEdge:', error)
    // Default to allowing usage if there's an error (fail open)
    return {
      canProceed: true,
      currentUsage: 0,
      limitAmount: -1,
      extraCreditsAvailable: 0,
      subscriptionTier: 'trial',
      message: 'Unable to check usage limits',
    }
  }
}
