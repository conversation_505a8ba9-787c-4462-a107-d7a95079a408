import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { checkUsageLimit } from '@/lib/usage-tracking'
import { createRateLimiter } from './rate-limiter'

// Trial limiter for unauthenticated users: 3 messages total per IP (lifetime)
const trialLimiter = createRateLimiter({
  windowMs: 365 * 24 * 60 * 60 * 1000, // 1 year (effectively permanent)
  maxRequests: 3, // 3 trial messages total
  message: 'You\'ve used your 3 free trial messages. Sign up for a trial account to get 500 messages per month!'
})

export async function enforceUsageLimits(request: NextRequest): Promise<NextResponse | null> {
  // Skip usage limits in testing environment
  if (process.env.TESTING === 'true' || process.env.NODE_ENV === 'test') {
    return null // Continue without checking usage limits
  }

  // Only apply to API routes that consume usage
  const usageRoutes = [
    '/api/compliance/summary',
    '/api/analyze-ordinance',
  ]

  const isUsageRoute = usageRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  if (!isUsageRoute) {
    return null // Continue without checking
  }

  try {
    const supabase = await createServerClient()

    let user = null
    try {
      const {
        data: { user: authUser },
      } = await supabase.auth.getUser()
      user = authUser
    } catch (error: unknown) {
      // Handle AuthSessionMissingError gracefully
      if (error && typeof error === 'object' && 'name' in error && error.name !== 'AuthSessionMissingError') {
        console.error('UsageLimiter: Error getting user:', error)
      }
      // Continue with user = null
    }

    // For unauthenticated users, enforce trial limits
    if (!user) {
      const trialLimitResponse = await trialLimiter(request)
      if (trialLimitResponse) {
        // Add signup prompt to the response
        const responseData = await trialLimitResponse.json()
        return NextResponse.json({
          ...responseData,
          upgradeRequired: true,
          signupPrompt: true,
          upgradeOptions: {
            starter: {
              name: 'Starter Plan',
              price: '$15/month',
              features: ['500 messages per month', 'AI chat assistance', 'Municipal compliance research'],
            },
            professional: {
              name: 'Professional Plan',
              price: '$50/month',
              features: ['2,000 messages per month', 'Priority support', 'Enhanced source analysis'],
            }
          }
        }, { status: 429 })
      }
      return null // Allow trial search to proceed
    }

    // Check usage limits
    const usageStatus = await checkUsageLimit(user.id)

    if (!usageStatus.canProceed) {
      return NextResponse.json(
        {
          error: 'Usage limit exceeded',
          message: usageStatus.message,
          usageStatus,
          upgradeRequired: true,
        },
        { status: 429 } // Too Many Requests
      )
    }

    // Add usage info to headers for the API route to use
    const response = NextResponse.next()
    response.headers.set('x-user-id', user.id)
    response.headers.set('x-usage-status', JSON.stringify(usageStatus))

    return response
  } catch (error) {
    console.error('Error in usage limiter:', error)
    // Fail open - allow the request to continue
    return null
  }
}

export function createUsageLimitedResponse(usageStatus: { message?: string }) {
  return NextResponse.json(
    {
      error: 'Usage limit exceeded',
      message: usageStatus.message,
      usageStatus,
      upgradeRequired: true,
      upgradeOptions: {
        starter: {
          name: 'Starter Plan',
          price: '$49/month',
          features: ['500 searches per month', 'AI chat assistance', 'Municipal compliance research'],
        },
        professional: {
          name: 'Professional Plan',
          price: '$99/month',
          features: ['2,000 searches per month', 'Priority support', 'Enhanced source analysis'],
        },
      },
    },
    { status: 429 }
  )
}
