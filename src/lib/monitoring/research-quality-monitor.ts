/**
 * Research Quality Monitoring System
 * Continuous monitoring of research accuracy and performance
 * Part of Phase 5: Quality Assurance & Monitoring
 */

import { createServerClient } from '@/lib/supabase/server'
import { SupabaseClient, createClient } from '@supabase/supabase-js'

export interface QualityMetrics {
  timestamp: string
  confidence_distribution: {
    high_confidence: number // >= 0.95
    medium_confidence: number // 0.85-0.94
    low_confidence: number // < 0.85
  }
  accuracy_metrics: {
    pass_rate: number
    average_confidence: number
    citation_accuracy: number
    source_verification_rate: number
  }
  performance_metrics: {
    average_response_time: number
    cache_hit_rate: number
    research_trigger_rate: number
    error_rate: number
  }
  data_quality: {
    sources_with_links: number
    verified_sources: number
    public_domain_ratio: number
    legal_citation_coverage: number
  }
}

export interface QualityAlert {
  id: string
  alert_type: 'accuracy_drop' | 'performance_degradation' | 'high_error_rate' | 'low_confidence'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  metrics: any
  triggered_at: string
  resolved_at?: string
}

export interface QualityThresholds {
  min_pass_rate: number
  min_average_confidence: number
  max_response_time: number
  min_cache_hit_rate: number
  max_error_rate: number
  min_source_verification_rate: number
}

export class ResearchQualityMonitor {
  private supabase: SupabaseClient
  private thresholds: QualityThresholds

  constructor(supabase?: SupabaseClient) {
    if (supabase) {
      this.supabase = supabase
    } else {
      // For standalone usage, create a direct client
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing')
      }
      this.supabase = createClient(supabaseUrl, supabaseKey)
    }
    this.thresholds = {
      min_pass_rate: 0.99, // 99% minimum pass rate
      min_average_confidence: 0.90, // 90% minimum average confidence
      max_response_time: 30000, // 30 seconds max response time
      min_cache_hit_rate: 0.80, // 80% minimum cache hit rate
      max_error_rate: 0.01, // 1% maximum error rate
      min_source_verification_rate: 0.95 // 95% minimum source verification
    }
  }

  /**
   * Collect and analyze current quality metrics
   */
  async collectQualityMetrics(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<QualityMetrics> {
    const timeFilter = this.getTimeFilter(timeframe)
    
    try {
      // Get research session data
      const { data: sessions, error: sessionsError } = await this.supabase
        .from('research_sessions')
        .select('*')
        .gte('created_at', timeFilter.toISOString())

      if (sessionsError) {
        console.error('Error fetching research sessions:', sessionsError)
        throw sessionsError
      }

      // Get test execution results
      const { data: testResults, error: testError } = await this.supabase
        .from('test_execution_results')
        .select('*')
        .gte('execution_date', timeFilter.toISOString())

      if (testError) {
        console.error('Error fetching test results:', testError)
        throw testError
      }

      // Get compliance knowledge quality data
      const { data: knowledgeData, error: knowledgeError } = await this.supabase
        .from('compliance_knowledge')
        .select('confidence_score, verification_status, copyright_status, source_url, legal_citation, research_quality_score')
        .gte('last_updated_at', timeFilter.toISOString())

      if (knowledgeError) {
        console.error('Error fetching knowledge data:', knowledgeError)
        throw knowledgeError
      }

      // Calculate metrics
      const metrics: QualityMetrics = {
        timestamp: new Date().toISOString(),
        confidence_distribution: this.calculateConfidenceDistribution(sessions || []),
        accuracy_metrics: this.calculateAccuracyMetrics(testResults || [], sessions || []),
        performance_metrics: this.calculatePerformanceMetrics(sessions || []),
        data_quality: this.calculateDataQuality(knowledgeData || [])
      }

      // Store metrics for historical tracking
      await this.storeMetrics(metrics)

      // Check for quality alerts
      await this.checkQualityAlerts(metrics)

      return metrics

    } catch (error) {
      console.error('Error collecting quality metrics:', error)
      throw error
    }
  }

  /**
   * Calculate confidence score distribution
   */
  private calculateConfidenceDistribution(sessions: any[]): QualityMetrics['confidence_distribution'] {
    if (sessions.length === 0) {
      return { high_confidence: 0, medium_confidence: 0, low_confidence: 0 }
    }

    const highConfidence = sessions.filter(s => (s.final_confidence || 0) >= 0.95).length
    const mediumConfidence = sessions.filter(s => (s.final_confidence || 0) >= 0.85 && (s.final_confidence || 0) < 0.95).length
    const lowConfidence = sessions.filter(s => (s.final_confidence || 0) < 0.85).length

    return {
      high_confidence: highConfidence / sessions.length,
      medium_confidence: mediumConfidence / sessions.length,
      low_confidence: lowConfidence / sessions.length
    }
  }

  /**
   * Calculate accuracy metrics from test results and sessions
   */
  private calculateAccuracyMetrics(testResults: any[], sessions: any[]): QualityMetrics['accuracy_metrics'] {
    // Test results accuracy
    const passRate = testResults.length > 0 
      ? testResults.filter(t => t.passed).length / testResults.length 
      : 0

    // Average confidence from sessions
    const averageConfidence = sessions.length > 0
      ? sessions.reduce((sum, s) => sum + (s.final_confidence || 0), 0) / sessions.length
      : 0

    // Citation accuracy from test results
    const citationAccuracy = testResults.length > 0
      ? testResults.filter(t => t.actual_citation && t.actual_citation.length > 0).length / testResults.length
      : 0

    // Source verification rate from sessions
    const sourceVerificationRate = sessions.length > 0
      ? sessions.reduce((sum, s) => sum + (s.sources_verified || 0), 0) / sessions.reduce((sum, s) => sum + (s.sources_found || 1), 0)
      : 0

    return {
      pass_rate: passRate,
      average_confidence: averageConfidence,
      citation_accuracy: citationAccuracy,
      source_verification_rate: sourceVerificationRate
    }
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(sessions: any[]): QualityMetrics['performance_metrics'] {
    if (sessions.length === 0) {
      return {
        average_response_time: 0,
        cache_hit_rate: 0,
        research_trigger_rate: 0,
        error_rate: 0
      }
    }

    const averageResponseTime = sessions.reduce((sum, s) => sum + (s.processing_time_ms || 0), 0) / sessions.length
    const cacheHitRate = sessions.filter(s => s.session_type === 'rag_lookup').length / sessions.length
    const researchTriggerRate = sessions.filter(s => s.session_type === 'real_time_research' || s.session_type === 'hybrid').length / sessions.length
    const errorRate = sessions.filter(s => !s.response_generated).length / sessions.length

    return {
      average_response_time: averageResponseTime,
      cache_hit_rate: cacheHitRate,
      research_trigger_rate: researchTriggerRate,
      error_rate: errorRate
    }
  }

  /**
   * Calculate data quality metrics
   */
  private calculateDataQuality(knowledgeData: any[]): QualityMetrics['data_quality'] {
    if (knowledgeData.length === 0) {
      return {
        sources_with_links: 0,
        verified_sources: 0,
        public_domain_ratio: 0,
        legal_citation_coverage: 0
      }
    }

    const sourcesWithLinks = knowledgeData.filter(k => k.source_url && k.source_url.length > 10).length / knowledgeData.length
    const verifiedSources = knowledgeData.filter(k => k.verification_status === 'verified').length / knowledgeData.length
    const publicDomainRatio = knowledgeData.filter(k => k.copyright_status === 'public_domain').length / knowledgeData.length
    const legalCitationCoverage = knowledgeData.filter(k => k.legal_citation && k.legal_citation.length > 0).length / knowledgeData.length

    return {
      sources_with_links: sourcesWithLinks,
      verified_sources: verifiedSources,
      public_domain_ratio: publicDomainRatio,
      legal_citation_coverage: legalCitationCoverage
    }
  }

  /**
   * Check for quality alerts based on thresholds
   */
  private async checkQualityAlerts(metrics: QualityMetrics): Promise<void> {
    const alerts: Omit<QualityAlert, 'id'>[] = []

    // Check pass rate
    if (metrics.accuracy_metrics.pass_rate < this.thresholds.min_pass_rate) {
      alerts.push({
        alert_type: 'accuracy_drop',
        severity: metrics.accuracy_metrics.pass_rate < 0.95 ? 'critical' : 'high',
        message: `Pass rate dropped to ${(metrics.accuracy_metrics.pass_rate * 100).toFixed(1)}% (threshold: ${(this.thresholds.min_pass_rate * 100).toFixed(1)}%)`,
        metrics: { pass_rate: metrics.accuracy_metrics.pass_rate },
        triggered_at: new Date().toISOString()
      })
    }

    // Check average confidence
    if (metrics.accuracy_metrics.average_confidence < this.thresholds.min_average_confidence) {
      alerts.push({
        alert_type: 'low_confidence',
        severity: metrics.accuracy_metrics.average_confidence < 0.85 ? 'high' : 'medium',
        message: `Average confidence dropped to ${(metrics.accuracy_metrics.average_confidence * 100).toFixed(1)}% (threshold: ${(this.thresholds.min_average_confidence * 100).toFixed(1)}%)`,
        metrics: { average_confidence: metrics.accuracy_metrics.average_confidence },
        triggered_at: new Date().toISOString()
      })
    }

    // Check response time
    if (metrics.performance_metrics.average_response_time > this.thresholds.max_response_time) {
      alerts.push({
        alert_type: 'performance_degradation',
        severity: metrics.performance_metrics.average_response_time > 45000 ? 'high' : 'medium',
        message: `Average response time increased to ${metrics.performance_metrics.average_response_time}ms (threshold: ${this.thresholds.max_response_time}ms)`,
        metrics: { response_time: metrics.performance_metrics.average_response_time },
        triggered_at: new Date().toISOString()
      })
    }

    // Check error rate
    if (metrics.performance_metrics.error_rate > this.thresholds.max_error_rate) {
      alerts.push({
        alert_type: 'high_error_rate',
        severity: metrics.performance_metrics.error_rate > 0.05 ? 'critical' : 'high',
        message: `Error rate increased to ${(metrics.performance_metrics.error_rate * 100).toFixed(1)}% (threshold: ${(this.thresholds.max_error_rate * 100).toFixed(1)}%)`,
        metrics: { error_rate: metrics.performance_metrics.error_rate },
        triggered_at: new Date().toISOString()
      })
    }

    // Store alerts
    for (const alert of alerts) {
      await this.storeAlert(alert)
    }

    if (alerts.length > 0) {
      console.warn(`🚨 ${alerts.length} quality alerts triggered`)
      alerts.forEach(alert => {
        console.warn(`   ${alert.severity.toUpperCase()}: ${alert.message}`)
      })
    }
  }

  /**
   * Store metrics in database for historical tracking
   */
  private async storeMetrics(metrics: QualityMetrics): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('quality_metrics')
        .insert({
          timestamp: metrics.timestamp,
          confidence_distribution: metrics.confidence_distribution,
          accuracy_metrics: metrics.accuracy_metrics,
          performance_metrics: metrics.performance_metrics,
          data_quality: metrics.data_quality
        })

      if (error) {
        console.error('Error storing quality metrics:', error)
      }
    } catch (error) {
      console.error('Error storing quality metrics:', error)
    }
  }

  /**
   * Store quality alert
   */
  private async storeAlert(alert: Omit<QualityAlert, 'id'>): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('quality_alerts')
        .insert(alert)

      if (error) {
        console.error('Error storing quality alert:', error)
      }
    } catch (error) {
      console.error('Error storing quality alert:', error)
    }
  }

  /**
   * Get quality metrics history
   */
  async getMetricsHistory(days: number = 7): Promise<QualityMetrics[]> {
    const timeFilter = new Date()
    timeFilter.setDate(timeFilter.getDate() - days)

    try {
      const { data, error } = await this.supabase
        .from('quality_metrics')
        .select('*')
        .gte('timestamp', timeFilter.toISOString())
        .order('timestamp', { ascending: false })

      if (error) {
        console.error('Error fetching metrics history:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching metrics history:', error)
      return []
    }
  }

  /**
   * Get active quality alerts
   */
  async getActiveAlerts(): Promise<QualityAlert[]> {
    try {
      const { data, error } = await this.supabase
        .from('quality_alerts')
        .select('*')
        .is('resolved_at', null)
        .order('triggered_at', { ascending: false })

      if (error) {
        console.error('Error fetching active alerts:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching active alerts:', error)
      return []
    }
  }

  /**
   * Resolve a quality alert
   */
  async resolveAlert(alertId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('quality_alerts')
        .update({ resolved_at: new Date().toISOString() })
        .eq('id', alertId)

      if (error) {
        console.error('Error resolving alert:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error resolving alert:', error)
      return false
    }
  }

  /**
   * Get time filter for metrics collection
   */
  private getTimeFilter(timeframe: 'hour' | 'day' | 'week'): Date {
    const now = new Date()
    switch (timeframe) {
      case 'hour':
        now.setHours(now.getHours() - 1)
        break
      case 'day':
        now.setDate(now.getDate() - 1)
        break
      case 'week':
        now.setDate(now.getDate() - 7)
        break
    }
    return now
  }

  /**
   * Update quality thresholds
   */
  updateThresholds(newThresholds: Partial<QualityThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds }
  }

  /**
   * Get current thresholds
   */
  getThresholds(): QualityThresholds {
    return { ...this.thresholds }
  }
}
