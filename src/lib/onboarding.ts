import { createClient } from '@/lib/supabase/client'

export interface OnboardingState {
  isFirstTimeUser: boolean
  hasCompletedTutorial: boolean
}

export async function getOnboardingState(): Promise<OnboardingState | null> {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return null
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('first_time_user')
      .eq('id', user.id)
      .single()

    return {
      isFirstTimeUser: profile?.first_time_user ?? true,
      hasCompletedTutorial: !profile?.first_time_user
    }
  } catch (error) {
    console.error('Error getting onboarding state:', error)
    return null
  }
}

export async function markTutorialCompleted(): Promise<boolean> {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return false
    }

    const { error } = await supabase
      .from('profiles')
      .update({ first_time_user: false })
      .eq('id', user.id)

    return !error
  } catch (error) {
    console.error('Error marking tutorial completed:', error)
    return false
  }
}

export async function resetTutorial(): Promise<boolean> {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return false
    }

    const { error } = await supabase
      .from('profiles')
      .update({ first_time_user: true })
      .eq('id', user.id)

    return !error
  } catch (error) {
    console.error('Error resetting tutorial:', error)
    return false
  }
}
