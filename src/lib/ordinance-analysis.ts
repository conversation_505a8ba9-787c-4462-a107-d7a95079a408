import { getAIConfig, logAIUsage, validateAIModel } from '@/lib/ai-config'
import { generateQuestionSpecificPrompt, generateAdaptivePrompt, generateEnhancedSystemPrompt, validateEnhancedResponse } from '@/lib/services/enhanced-ai-prompts'
import { extractLegalCitations, type LegalCitation } from '@/lib/services/legal-citation-parser'
import { validateSourceWithCopyright, type ContentClassification } from '@/lib/services/content-classification'
import { sourceGroundedValidator, type SourceContent } from '@/lib/services/source-grounded-validator'

export interface OrdinanceAnalysisInput {
  region: string
  project_type: string
  document_text: string
  userQuery?: string // 🎯 NEW: User's specific question for question-specific AI prompting
  qualityFeedback?: { // 🔄 NEW: Answer Quality feedback for adaptive prompting
    specificity: number
    actionability: number
    completeness: number
    directness: number
    improvements: string[]
  }
}

export interface OrdinanceAnalysisOutput {
  summary: string
  permit_required: boolean
  requirements: string[]
  prohibited_practices: string[]
  permit_process: string
  citations: LegalCitation[]
  model_code_references?: Array<{
    code_type: string
    reference: string
    summary: string
    note?: string
  }>
  source_links: string[]
  contact_info: {
    department?: string
    phone?: string
    email?: string
    website?: string
    address?: string
    hours?: string
  }
  tags: string[]
  confidence_score?: number
  geographic_validation?: {
    sources_validated: boolean
    rejected_sources: string[]
    validation_notes: string
  }
  error?: boolean
  error_message?: string
}

/**
 * Shared ordinance analysis function that analyzes document text for local ordinance requirements
 * @param input - The analysis input containing region, project type, and document text
 * @param options - Optional configuration for AI model and logging
 * @returns Promise<OrdinanceAnalysisOutput> - Structured analysis results
 */
export async function analyzeOrdinanceContent(
  input: OrdinanceAnalysisInput,
  options: {
    aiModel?: string
    enableLogging?: boolean
    fallbackOnError?: boolean
  } = {}
): Promise<OrdinanceAnalysisOutput> {
  const { region, project_type, document_text } = input
  const { aiModel, enableLogging = process.env.NODE_ENV === 'development', fallbackOnError = true } = options

  if (enableLogging) {
    console.log(`🤖 Analyzing ${project_type} ordinances for ${region}`)
    console.log(`📄 Document text length: ${document_text.length} characters`)
  }

  try {
    // Validate OpenAI API key
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_key_here') {
      throw new Error('OpenAI API not configured')
    }

    // Get AI configuration
    const aiConfig = getAIConfig()
    const modelToUse = aiModel || aiConfig.model

    // Validate the model
    if (!validateAIModel(modelToUse)) {
      console.warn(`⚠️ Unsupported AI model: ${modelToUse}, falling back to gpt-4o-mini`)
      aiConfig.model = 'gpt-4o-mini'
    } else {
      aiConfig.model = modelToUse
    }

    const { OpenAI } = await import('openai')
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })

    const prompt = `You are analyzing ${project_type} ordinances for ${region}.

ANALYSIS APPROACH:
- Focus on the general project category (e.g., if project is "5ft fence", analyze general fencing regulations)
- Prioritize sources from ${region}, but also consider relevant county/state regulations that would apply
- If specific details are mentioned (like "5ft"), incorporate them but don't require exact matches in sources
- Provide practical guidance even if perfect local documentation isn't available

DOCUMENT TEXT TO ANALYZE:
${document_text}

GEOGRAPHIC VALIDATION GUIDANCE:
- Prefer sources specifically from ${region} when available
- Accept relevant county or state regulations that would apply to ${region}
- Use official government sources (.gov, .org) when possible
- Provide useful guidance even if sources aren't perfectly local

Please provide a comprehensive analysis in the following JSON format:

{
  "summary": "Brief 2-3 sentence summary of the key findings for ${project_type} in ${region}",
  "permit_required": true/false,
  "requirements": [
    "Specific requirement 1 (e.g., 'Property must be at least 3,800 sq ft')",
    "Specific requirement 2 (e.g., 'Coop must be in rear yard, 10 feet from property lines')"
  ],
  "prohibited_practices": [
    "What's specifically banned (e.g., 'No roosters allowed')",
    "Other prohibitions (e.g., 'No butchering on property')"
  ],
  "permit_process": "Step-by-step process to get permit if required, or 'No permit required' if not needed",
  "citations": [
    "Specific section numbers (e.g., 'Animal Code §9.219')",
    "Ordinance references (e.g., 'Land-use §5.2.30')"
  ],
  "source_links": [
    "Direct links to official ordinance documents or permit pages FROM ${region} ONLY"
  ],
  "contact_info": {
    "department": "Relevant department name",
    "phone": "Phone number if found",
    "email": "Email if found",
    "website": "Department website if found"
  },
  "tags": ["relevant", "keywords", "for", "categorization"],
  "confidence_score": 0.1-1.0,
  "geographic_validation": {
    "sources_validated": true/false,
    "rejected_sources": ["List any sources rejected for being from wrong jurisdiction"],
    "validation_notes": "Brief explanation of geographic validation performed"
  }
}

IMPORTANT GUIDELINES:
1. PRACTICAL ANALYSIS: Focus on providing useful guidance for ${project_type} projects in ${region}
2. FLEXIBLE SOURCING: Use local sources when available, but also relevant county/state regulations
3. PROJECT FOCUS: If analyzing "5ft fence", focus on general fencing rules and apply the 5ft detail
4. CONFIDENCE SCORING: Base score on information quality and relevance (0.3-1.0 range)
5. EXTRACT SPECIFICS: Include measurements, setbacks, and requirements where available
6. CITE SOURCES: Include section numbers and citations when found
7. PRACTICAL GUIDANCE: Provide actionable information homeowners can use
8. CONTACT INFO: Include relevant department contacts when available
9. PERMIT CLARITY: Be clear about permit requirements based on available information
10. HONEST LIMITATIONS: Note when information is limited but still provide best guidance available

Return only valid JSON - no additional text or formatting.`

    if (enableLogging) {
      logAIUsage(prompt, aiConfig, `for ${project_type} analysis in ${region}`)
    }

    const response = await openai.chat.completions.create({
      model: aiConfig.model,
      messages: [
        {
          role: 'system',
          content: 'You are an expert municipal ordinance analyst focused on providing practical guidance. Prioritize local sources when available, but also use relevant county/state regulations. Focus on the general project category and provide actionable information. Return valid JSON with useful guidance even if sources are not perfectly local.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: aiConfig.temperature,
      max_tokens: aiConfig.maxTokens
    })

    const aiResponse = response.choices[0]?.message?.content
    if (!aiResponse) {
      throw new Error('No response from AI analysis')
    }

    if (enableLogging) {
      console.log(`📝 AI Response length: ${aiResponse.length} characters`)
    }

    try {
      // Clean the response
      let cleanedResponse = aiResponse.trim()
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      const extractedData = JSON.parse(cleanedResponse)

      if (enableLogging) {
        console.log(`✅ AI analysis completed for ${region} - ${project_type}`)
        console.log(`📊 Confidence score: ${extractedData.confidence_score || 'not provided'}`)

        // Log geographic validation results
        if (extractedData.geographic_validation) {
          console.log(`🌍 Geographic validation: ${extractedData.geographic_validation.sources_validated ? 'PASSED' : 'FAILED'}`)
          if (extractedData.geographic_validation.rejected_sources?.length > 0) {
            console.log(`❌ Rejected sources: ${extractedData.geographic_validation.rejected_sources.length}`)
          }
          if (extractedData.geographic_validation.validation_notes) {
            console.log(`📝 Validation notes: ${extractedData.geographic_validation.validation_notes}`)
          }
        }
      }

      return {
        summary: extractedData.summary || 'Analysis could not determine specific requirements.',
        permit_required: extractedData.permit_required !== false, // Default to true for safety
        requirements: Array.isArray(extractedData.requirements) ? extractedData.requirements : [],
        prohibited_practices: Array.isArray(extractedData.prohibited_practices) ? extractedData.prohibited_practices : [],
        permit_process: extractedData.permit_process || 'Contact local authorities for permit information.',
        citations: Array.isArray(extractedData.citations) ? extractedData.citations : [],
        source_links: Array.isArray(extractedData.source_links) ? extractedData.source_links : [],
        contact_info: extractedData.contact_info || {},
        tags: Array.isArray(extractedData.tags) ? extractedData.tags : [],
        confidence_score: typeof extractedData.confidence_score === 'number' ? extractedData.confidence_score : 0.5,
        geographic_validation: extractedData.geographic_validation || {
          sources_validated: false,
          rejected_sources: [],
          validation_notes: 'Geographic validation not performed'
        },
        error: false
      }

    } catch (parseError) {
      console.error('AI response parsing error:', parseError)
      if (enableLogging) {
        console.log('Raw AI Response:', aiResponse)
      }
      throw new Error(`Failed to parse AI analysis: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`)
    }

  } catch (error) {
    console.error('Ordinance analysis error:', error)
    
    if (fallbackOnError) {
      // Return fallback structure
      return {
        summary: `Analysis failed for ${project_type} in ${region}. Please contact local authorities for specific requirements.`,
        permit_required: true, // Default to requiring permit for safety
        requirements: ['Contact local authorities for specific requirements'],
        prohibited_practices: [],
        permit_process: 'Contact local authorities for permit information.',
        citations: [],
        source_links: [],
        contact_info: {
          department: `${region} Planning Department`
        },
        tags: [project_type, 'contact_required'],
        confidence_score: 0.1,
        error: true,
        error_message: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    } else {
      throw error
    }
  }
}

/**
 * Enhanced ordinance analysis with verbatim citation extraction
 * Uses legal citation parser and enhanced prompts for research-level accuracy
 */
export async function analyzeOrdinanceContentEnhanced(
  input: OrdinanceAnalysisInput,
  documentSources: Array<{
    title: string
    url: string
    content: string
    copyrightStatus: 'public_domain' | 'proprietary'
    citationStrategy: 'verbatim_quote' | 'reference_only'
    classification: ContentClassification
  }>,
  options: {
    aiModel?: string
    enableLogging?: boolean
    fallbackOnError?: boolean
    confidenceThreshold?: number
  } = {}
): Promise<OrdinanceAnalysisOutput> {
  const { region, project_type } = input
  const {
    aiModel,
    enableLogging = process.env.NODE_ENV === 'development',
    fallbackOnError = true,
    confidenceThreshold = 0.9
  } = options

  try {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API not configured')
    }

    // Get AI configuration
    const aiConfig = getAIConfig()
    const modelToUse = aiModel || aiConfig.model

    // Validate the model
    if (!validateAIModel(modelToUse)) {
      console.warn(`⚠️ Unsupported AI model: ${modelToUse}, falling back to gpt-4o-mini`)
      aiConfig.model = 'gpt-4o-mini'
    } else {
      aiConfig.model = modelToUse
    }

    const { OpenAI } = await import('openai')
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })

    // Generate enhanced prompt with copyright awareness
    const promptConfig = {
      jurisdiction: region,
      projectType: project_type,
      documentSources,
      requireVerbatimQuotes: true,
      confidenceThreshold
    }

    // 🔄 Use adaptive prompting if quality feedback is available, otherwise use question-specific prompting
    let basePrompt = input.qualityFeedback
      ? generateAdaptivePrompt({ ...promptConfig, userQuery: input.userQuery }, input.qualityFeedback)
      : generateQuestionSpecificPrompt({ ...promptConfig, userQuery: input.userQuery })

    // 🎯 SOURCE GROUNDING: Enhance prompt to enforce source-based responses
    if (input.userQuery && documentSources.length > 0) {
      // Convert document sources to SourceContent format for validation
      const sourceContents: SourceContent[] = documentSources.map(source => ({
        title: source.title || 'Municipal Document',
        url: source.url || '',
        content: source.content || '',
        snippet: (source as any).snippet || '',
        validation: {
          copyrightStatus: 'public_domain',
          usageAllowed: true,
          contentQuality: 0.8
        }
      }))

      // Generate source-grounded prompt that enforces accuracy
      basePrompt = sourceGroundedValidator.generateSourceGroundedPrompt(
        input.userQuery,
        sourceContents,
        basePrompt
      )

      console.log(`🎯 [SOURCE GROUNDING] Enhanced prompt with ${sourceContents.length} sources for query: "${input.userQuery}"`)
    }

    const prompt = basePrompt
    const systemPrompt = generateEnhancedSystemPrompt()

    if (enableLogging) {
      logAIUsage(prompt, aiConfig, `for enhanced ${project_type} analysis in ${region}`)
      console.log(`🔍 Enhanced analysis with ${documentSources.length} validated sources`)
      console.log(`   - Public domain: ${documentSources.filter(s => s.copyrightStatus === 'public_domain').length}`)
      console.log(`   - Proprietary: ${documentSources.filter(s => s.copyrightStatus === 'proprietary').length}`)
    }

    const response = await openai.chat.completions.create({
      model: aiConfig.model,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1, // Lower temperature for more precise extraction
      max_tokens: aiConfig.maxTokens
    })

    const aiResponse = response.choices[0]?.message?.content
    if (!aiResponse) {
      throw new Error('No response from enhanced AI analysis')
    }

    if (enableLogging) {
      console.log(`📝 Enhanced AI Response length: ${aiResponse.length} characters`)
    }

    try {
      // Clean the response
      let cleanedResponse = aiResponse.trim()
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      const extractedData = JSON.parse(cleanedResponse)

      // Validate enhanced response
      const validation = validateEnhancedResponse(extractedData, promptConfig)

      if (!validation.isValid) {
        console.warn('⚠️ Enhanced response validation failed:', validation.errors)
        if (enableLogging) {
          console.log('Validation warnings:', validation.warnings)
        }

        // Fall back to regular analysis if enhanced fails
        if (fallbackOnError) {
          console.log('🔄 Falling back to regular analysis')
          return analyzeOrdinanceContent(input, { aiModel, enableLogging, fallbackOnError })
        }
      }

      if (enableLogging) {
        console.log(`✅ Enhanced AI analysis completed for ${region} - ${project_type}`)
        console.log(`📊 Confidence score: ${extractedData.confidence_score || 'not provided'}`)
        console.log(`📋 Citations extracted: ${extractedData.citations?.length || 0}`)
        console.log(`🔗 Model code references: ${extractedData.model_code_references?.length || 0}`)

        // Log validation results
        if (validation.warnings.length > 0) {
          console.log(`⚠️ Validation warnings: ${validation.warnings.length}`)
        }
      }

      return {
        summary: extractedData.summary || 'Enhanced analysis could not determine specific requirements.',
        permit_required: extractedData.permit_required !== false,
        requirements: Array.isArray(extractedData.requirements) ? extractedData.requirements : [],
        prohibited_practices: Array.isArray(extractedData.prohibited_practices) ? extractedData.prohibited_practices : [],
        permit_process: extractedData.permit_process || 'Contact local authorities for permit information.',
        citations: Array.isArray(extractedData.citations) ? extractedData.citations : [],
        model_code_references: Array.isArray(extractedData.model_code_references) ? extractedData.model_code_references : [],
        source_links: Array.isArray(extractedData.source_links) ? extractedData.source_links : [],
        contact_info: extractedData.contact_info || {},
        tags: Array.isArray(extractedData.tags) ? extractedData.tags : [],
        confidence_score: typeof extractedData.confidence_score === 'number' ? extractedData.confidence_score : 0.5,
        geographic_validation: extractedData.geographic_validation || {
          sources_validated: false,
          rejected_sources: [],
          validation_notes: 'Enhanced geographic validation not performed'
        },
        error: false
      }

    } catch (parseError) {
      console.error('Enhanced AI response parsing error:', parseError)
      if (enableLogging) {
        console.log('Raw Enhanced AI Response:', aiResponse)
      }

      if (fallbackOnError) {
        console.log('🔄 Falling back to regular analysis due to parse error')
        return analyzeOrdinanceContent(input, { aiModel, enableLogging, fallbackOnError })
      }

      throw new Error(`Failed to parse enhanced AI analysis: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`)
    }

  } catch (error) {
    console.error('Enhanced ordinance analysis error:', error)

    if (fallbackOnError) {
      console.log('🔄 Falling back to regular analysis due to error')
      return analyzeOrdinanceContent(input, { aiModel, enableLogging, fallbackOnError })
    } else {
      throw error
    }
  }
}

/**
 * Helper function to combine multiple document sources into a single document text
 * Used by the compliance summary endpoint to adapt to the new shared function
 */
export function combineDocumentSources(documentSources: Array<{
  position: number
  title: string
  url: string
  snippet?: string
  content: string | null
  searchQuery: string
  contentLength: number
  fetchSuccess: boolean
}>): string {
  return documentSources
    .map((source) => {
      return `SOURCE ${source.position}: ${source.title}
URL: ${source.url}
Snippet: ${source.snippet || 'No snippet available'}
Content: ${source.content || 'No content available'}
Fetch Success: ${source.fetchSuccess}

---`
    })
    .join('\n\n')
}
