// Performance optimization utilities

// Type definitions for Web APIs
interface LayoutShift extends PerformanceEntry {
  value: number
  hadRecentInput: boolean
}

// Intersection Observer for lazy loading
export class LazyLoadManager {
  private observer: IntersectionObserver | null = null
  private elements: Map<Element, () => void> = new Map()

  constructor(options: IntersectionObserverInit = {}) {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const callback = this.elements.get(entry.target)
            if (callback) {
              callback()
              this.unobserve(entry.target)
            }
          }
        })
      }, {
        rootMargin: '50px',
        threshold: 0.1,
        ...options
      })
    }
  }

  observe(element: Element, callback: () => void): void {
    if (this.observer) {
      this.elements.set(element, callback)
      this.observer.observe(element)
    } else {
      // Fallback for browsers without IntersectionObserver
      callback()
    }
  }

  unobserve(element: Element): void {
    if (this.observer) {
      this.observer.unobserve(element)
      this.elements.delete(element)
    }
  }

  disconnect(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.elements.clear()
    }
  }
}

// Image optimization utilities
export const ImageOptimization = {
  // Generate responsive image sizes
  generateSrcSet: (baseUrl: string, sizes: number[]): string => {
    return sizes
      .map(size => `${baseUrl}?w=${size}&q=75 ${size}w`)
      .join(', ')
  },

  // Generate sizes attribute for responsive images
  generateSizes: (breakpoints: { [key: string]: string }): string => {
    return Object.entries(breakpoints)
      .map(([breakpoint, size]) => `(${breakpoint}) ${size}`)
      .join(', ')
  },

  // Create optimized image URL
  optimizeImageUrl: (url: string, options: {
    width?: number
    height?: number
    quality?: number
    format?: 'webp' | 'jpeg' | 'png'
  } = {}): string => {
    const params = new URLSearchParams()

    if (options.width) params.set('w', options.width.toString())
    if (options.height) params.set('h', options.height.toString())
    if (options.quality) params.set('q', options.quality.toString())
    if (options.format) params.set('f', options.format)

    const separator = url.includes('?') ? '&' : '?'
    return `${url}${separator}${params.toString()}`
  }
}

// Resource preloading utilities
export const ResourcePreloader = {
  // Preload critical resources
  preloadResource: (href: string, as: string, type?: string): void => {
    if (typeof document === 'undefined') return

    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (type) link.type = type

    document.head.appendChild(link)
  },

  // Preload images
  preloadImage: (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })
  },

  // Preload multiple images
  preloadImages: (sources: string[]): Promise<void[]> => {
    return Promise.all(sources.map(src => ResourcePreloader.preloadImage(src)))
  },

  // Prefetch resources for next navigation
  prefetchResource: (href: string): void => {
    if (typeof document === 'undefined') return

    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href

    document.head.appendChild(link)
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private metrics: Map<string, number> = new Map()

  // Mark the start of a performance measurement
  mark(name: string): void {
    if (typeof performance !== 'undefined') {
      performance.mark(`${name}-start`)
    }
    this.metrics.set(`${name}-start`, Date.now())
  }

  // Mark the end and calculate duration
  measure(name: string): number {
    const startTime = this.metrics.get(`${name}-start`)
    const endTime = Date.now()

    if (startTime) {
      const duration = endTime - startTime
      this.metrics.set(name, duration)

      if (typeof performance !== 'undefined') {
        performance.mark(`${name}-end`)
        performance.measure(name, `${name}-start`, `${name}-end`)
      }

      return duration
    }

    return 0
  }

  // Get all metrics
  getMetrics(): Record<string, number> {
    const result: Record<string, number> = {}
    this.metrics.forEach((value, key) => {
      if (!key.endsWith('-start')) {
        result[key] = value
      }
    })
    return result
  }

  // Clear all metrics
  clear(): void {
    this.metrics.clear()
    if (typeof performance !== 'undefined') {
      performance.clearMarks()
      performance.clearMeasures()
    }
  }
}

// Bundle size optimization utilities
export const BundleOptimization = {
  // Dynamic import with error handling
  dynamicImport: async <T>(importFn: () => Promise<T>): Promise<T | null> => {
    try {
      return await importFn()
    } catch (error) {
      console.error('Dynamic import failed:', error)
      return null
    }
  },

  // Lazy load component
  lazyComponent: <T extends React.ComponentType<Record<string, unknown>>>(
    importFn: () => Promise<{ default: T }>
  ): React.LazyExoticComponent<T> => {
    return React.lazy(importFn)
  }
}

// Memory management utilities
export const MemoryManager = {
  // Cleanup function for event listeners
  createCleanup: (): {
    add: (cleanup: () => void) => void
    cleanup: () => void
  } => {
    const cleanupFunctions: (() => void)[] = []

    return {
      add: (cleanup: () => void) => {
        cleanupFunctions.push(cleanup)
      },
      cleanup: () => {
        cleanupFunctions.forEach(fn => fn())
        cleanupFunctions.length = 0
      }
    }
  },

  // Debounce function for performance
  debounce: <T extends (...args: unknown[]) => unknown>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout | null = null

    return (...args: Parameters<T>) => {
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  },

  // Throttle function for performance
  throttle: <T extends (...args: unknown[]) => unknown>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean = false

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }
}

// Web Vitals monitoring
export const WebVitals = {
  // Measure Largest Contentful Paint
  measureLCP: (callback: (value: number) => void): void => {
    if (typeof window === 'undefined') return

    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1] as PerformanceEntry
      callback(lastEntry.startTime)
    }).observe({ entryTypes: ['largest-contentful-paint'] })
  },

  // Measure First Input Delay
  measureFID: (callback: (value: number) => void): void => {
    if (typeof window === 'undefined') return

    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        const fidEntry = entry as PerformanceEventTiming
        callback(fidEntry.processingStart - fidEntry.startTime)
      })
    }).observe({ entryTypes: ['first-input'] })
  },

  // Measure Cumulative Layout Shift
  measureCLS: (callback: (value: number) => void): void => {
    if (typeof window === 'undefined') return

    let clsValue = 0
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        const clsEntry = entry as LayoutShift
        if (!clsEntry.hadRecentInput) {
          clsValue += clsEntry.value
        }
      })
      callback(clsValue)
    }).observe({ entryTypes: ['layout-shift'] })
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()
export const lazyLoadManager = new LazyLoadManager()

// React import for lazy loading
import React from 'react'
