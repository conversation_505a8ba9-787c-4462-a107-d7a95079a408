import QRCode from 'qrcode'

export interface QRCodeOptions {
  size?: number
  margin?: number
  color?: {
    dark?: string
    light?: string
  }
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
}

/**
 * Generate a QR code as a data URL
 */
export async function generateQRCode(
  text: string, 
  options: QRCodeOptions = {}
): Promise<string> {
  const defaultOptions = {
    width: options.size || 200,
    margin: options.margin || 2,
    color: {
      dark: options.color?.dark || '#000000',
      light: options.color?.light || '#FFFFFF'
    },
    errorCorrectionLevel: options.errorCorrectionLevel || 'M' as const
  }

  try {
    return await QRCode.toDataURL(text, defaultOptions)
  } catch (error) {
    console.error('Error generating QR code:', error)
    throw new Error('Failed to generate QR code')
  }
}

/**
 * Generate a QR code as a canvas element
 */
export async function generateQRCodeCanvas(
  text: string,
  options: QRCodeOptions = {}
): Promise<HTMLCanvasElement> {
  const canvas = document.createElement('canvas')
  
  const defaultOptions = {
    width: options.size || 200,
    margin: options.margin || 2,
    color: {
      dark: options.color?.dark || '#000000',
      light: options.color?.light || '#FFFFFF'
    },
    errorCorrectionLevel: options.errorCorrectionLevel || 'M' as const
  }

  try {
    await QRCode.toCanvas(canvas, text, defaultOptions)
    return canvas
  } catch (error) {
    console.error('Error generating QR code canvas:', error)
    throw new Error('Failed to generate QR code canvas')
  }
}

/**
 * Generate QR code for Ordrly sharing
 */
export async function generateOrdrlyQRCode(
  url?: string,
  options: QRCodeOptions = {}
): Promise<string> {
  const targetUrl = url || `${window.location.origin}?utm_source=qr_code&utm_medium=share`
  
  return generateQRCode(targetUrl, {
    size: 120,
    margin: 1,
    color: {
      dark: '#2563eb', // Ordrly blue
      light: '#ffffff'
    },
    errorCorrectionLevel: 'M',
    ...options
  })
}

/**
 * Generate QR code for specific compliance result sharing
 */
export async function generateComplianceQRCode(
  address: string,
  ruleType: string,
  options: QRCodeOptions = {}
): Promise<string> {
  const baseUrl = window.location.origin
  const shareUrl = `${baseUrl}?utm_source=qr_code&utm_medium=compliance_share&utm_content=${encodeURIComponent(ruleType)}`
  
  return generateQRCode(shareUrl, {
    size: 120,
    margin: 1,
    color: {
      dark: '#2563eb',
      light: '#ffffff'
    },
    errorCorrectionLevel: 'M',
    ...options
  })
}
