import html2canvas from 'html2canvas'
import { generateOrdrlyQRCode } from './qr-code'

export interface ScreenshotOptions {
  filename?: string
  quality?: number
  format?: 'png' | 'jpeg' | 'webp'
  width?: number
  height?: number
  scale?: number
  backgroundColor?: string
  addWatermark?: boolean
  addQRCode?: boolean
  qrCodeUrl?: string
}

export interface WatermarkOptions {
  text?: string
  logo?: string
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
  opacity?: number
  fontSize?: number
  color?: string
}

/**
 * Capture an element as an image and download it
 */
export async function captureAndDownload(
  element: HTMLElement,
  options: ScreenshotOptions = {}
): Promise<void> {
  try {
    const canvas = await captureElement(element, options)
    
    // Add watermark and QR code if requested
    const finalCanvas = await addBrandingToCanvas(canvas, options)
    
    // Download the image
    downloadCanvas(finalCanvas, options.filename || 'ordrly-compliance-card', options.format || 'png')
  } catch (error) {
    console.error('Error capturing and downloading:', error)
    throw new Error('Failed to capture and download image')
  }
}

/**
 * Capture an element as a canvas
 */
export async function captureElement(
  element: HTMLElement,
  options: ScreenshotOptions = {}
): Promise<HTMLCanvasElement> {
  const defaultOptions = {
    scale: options.scale || 2, // High DPI for better quality
    backgroundColor: options.backgroundColor || '#ffffff',
    useCORS: true,
    allowTaint: false,
    width: options.width,
    height: options.height,
    scrollX: 0,
    scrollY: 0,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight
  }

  try {
    return await html2canvas(element, defaultOptions)
  } catch (error) {
    console.error('Error capturing element:', error)
    throw new Error('Failed to capture element')
  }
}

/**
 * Add Ordrly branding (watermark and QR code) to a canvas
 */
export async function addBrandingToCanvas(
  canvas: HTMLCanvasElement,
  options: ScreenshotOptions = {}
): Promise<HTMLCanvasElement> {
  const ctx = canvas.getContext('2d')
  if (!ctx) {
    throw new Error('Could not get canvas context')
  }

  // Create a new canvas with padding for branding
  const brandedCanvas = document.createElement('canvas')
  const brandedCtx = brandedCanvas.getContext('2d')
  if (!brandedCtx) {
    throw new Error('Could not get branded canvas context')
  }

  // Add padding for branding elements
  const padding = 40
  const qrSize = options.addQRCode !== false ? 100 : 0
  const extraHeight = padding * 2 + (qrSize > 0 ? qrSize + 20 : 0)
  
  brandedCanvas.width = canvas.width + (padding * 2)
  brandedCanvas.height = canvas.height + extraHeight

  // Fill background
  brandedCtx.fillStyle = options.backgroundColor || '#ffffff'
  brandedCtx.fillRect(0, 0, brandedCanvas.width, brandedCanvas.height)

  // Draw original canvas
  brandedCtx.drawImage(canvas, padding, padding)

  // Add watermark
  if (options.addWatermark !== false) {
    await addWatermark(brandedCtx, brandedCanvas, {
      text: 'Generated by Ordrly.com',
      position: 'bottom-right',
      opacity: 0.7,
      fontSize: 14,
      color: '#6b7280'
    })
  }

  // Add QR code
  if (options.addQRCode !== false) {
    await addQRCodeToCanvas(brandedCtx, brandedCanvas, options.qrCodeUrl)
  }

  return brandedCanvas
}

/**
 * Add watermark to canvas
 */
async function addWatermark(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  watermark: WatermarkOptions
): Promise<void> {
  const text = watermark.text || 'Ordrly.com'
  const fontSize = watermark.fontSize || 14
  const color = watermark.color || '#6b7280'
  const opacity = watermark.opacity || 0.7

  ctx.save()
  ctx.globalAlpha = opacity
  ctx.font = `${fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`
  ctx.fillStyle = color
  ctx.textAlign = 'right'
  ctx.textBaseline = 'bottom'

  // Position based on option
  let x = canvas.width - 20
  let y = canvas.height - 20

  switch (watermark.position) {
    case 'top-left':
      ctx.textAlign = 'left'
      ctx.textBaseline = 'top'
      x = 20
      y = 20
      break
    case 'top-right':
      ctx.textAlign = 'right'
      ctx.textBaseline = 'top'
      x = canvas.width - 20
      y = 20
      break
    case 'bottom-left':
      ctx.textAlign = 'left'
      ctx.textBaseline = 'bottom'
      x = 20
      y = canvas.height - 20
      break
    case 'center':
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      x = canvas.width / 2
      y = canvas.height / 2
      break
    // 'bottom-right' is default
  }

  ctx.fillText(text, x, y)
  ctx.restore()
}

/**
 * Add QR code to canvas
 */
async function addQRCodeToCanvas(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  url?: string
): Promise<void> {
  try {
    const qrDataUrl = await generateOrdrlyQRCode(url, { size: 100 })
    
    return new Promise((resolve, reject) => {
      const qrImage = new Image()
      qrImage.onload = () => {
        // Position QR code in bottom-left corner
        const qrSize = 80
        const margin = 20
        
        // Add white background for QR code
        ctx.fillStyle = '#ffffff'
        ctx.fillRect(margin - 5, canvas.height - qrSize - margin - 5, qrSize + 10, qrSize + 10)
        
        // Draw QR code
        ctx.drawImage(qrImage, margin, canvas.height - qrSize - margin, qrSize, qrSize)
        
        // Add "Scan to visit Ordrly" text
        ctx.save()
        ctx.font = '10px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
        ctx.fillStyle = '#374151'
        ctx.textAlign = 'left'
        ctx.textBaseline = 'bottom'
        ctx.fillText('Scan to visit Ordrly', margin, canvas.height - margin + 15)
        ctx.restore()
        
        resolve()
      }
      qrImage.onerror = reject
      qrImage.src = qrDataUrl
    })
  } catch (error) {
    console.error('Error adding QR code to canvas:', error)
    // Don't fail the entire operation if QR code fails
  }
}

/**
 * Download a canvas as an image file
 */
export function downloadCanvas(
  canvas: HTMLCanvasElement,
  filename: string,
  format: 'png' | 'jpeg' | 'webp' = 'png'
): void {
  try {
    const mimeType = `image/${format}`
    const quality = format === 'jpeg' ? 0.9 : undefined
    
    canvas.toBlob((blob) => {
      if (!blob) {
        throw new Error('Failed to create blob from canvas')
      }
      
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${filename}.${format}`
      
      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up
      URL.revokeObjectURL(url)
    }, mimeType, quality)
  } catch (error) {
    console.error('Error downloading canvas:', error)
    throw new Error('Failed to download image')
  }
}

/**
 * Get canvas as data URL for sharing
 */
export async function getCanvasDataUrl(
  element: HTMLElement,
  options: ScreenshotOptions = {}
): Promise<string> {
  try {
    const canvas = await captureElement(element, options)
    const brandedCanvas = await addBrandingToCanvas(canvas, options)
    
    const format = options.format || 'png'
    const mimeType = `image/${format}`
    const quality = format === 'jpeg' ? 0.9 : undefined
    
    return brandedCanvas.toDataURL(mimeType, quality)
  } catch (error) {
    console.error('Error getting canvas data URL:', error)
    throw new Error('Failed to generate image data URL')
  }
}
