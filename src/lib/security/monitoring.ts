import { createServiceClient } from '@/lib/supabase/server'

export interface SecurityEvent {
  type: 'auth_failure' | 'rate_limit_exceeded' | 'suspicious_activity' | 'data_access'
  userId?: string
  ipAddress?: string
  userAgent?: string
  details: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export async function logSecurityEvent(event: SecurityEvent) {
  try {
    const supabase = createServiceClient()
    
    await supabase.from('audit_logs').insert({
      user_id: event.userId || null,
      action: event.type,
      resource_type: 'security',
      resource_id: null,
      details: {
        ...event.details,
        severity: event.severity,
        timestamp: new Date().toISOString()
      },
      ip_address: event.ipAddress,
      user_agent: event.userAgent
    })

    // Log critical events to console for immediate attention
    if (event.severity === 'critical') {
      console.error('🚨 CRITICAL SECURITY EVENT:', event)
    }
  } catch (error) {
    console.error('Failed to log security event:', error)
  }
}

export async function detectSuspiciousActivity(
  userId: string,
  ipAddress: string,
  action: string
): Promise<boolean> {
  try {
    const supabase = createServiceClient()
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()

    // Check for multiple failed login attempts
    if (action === 'login_failure') {
      const { count } = await supabase
        .from('audit_logs')
        .select('*', { count: 'exact', head: true })
        .eq('action', 'auth_failure')
        .eq('ip_address', ipAddress)
        .gte('created_at', oneHourAgo)

      if ((count || 0) >= 5) {
        await logSecurityEvent({
          type: 'suspicious_activity',
          userId,
          ipAddress,
          details: {
            reason: 'Multiple failed login attempts',
            count: count || 0,
            timeWindow: '1 hour'
          },
          severity: 'high'
        })
        return true
      }
    }

    // Check for rapid API calls from same IP
    const { count: apiCalls } = await supabase
      .from('audit_logs')
      .select('*', { count: 'exact', head: true })
      .eq('ip_address', ipAddress)
      .gte('created_at', oneHourAgo)

    if ((apiCalls || 0) >= 1000) {
      await logSecurityEvent({
        type: 'suspicious_activity',
        userId,
        ipAddress,
        details: {
          reason: 'Excessive API calls',
          count: apiCalls || 0,
          timeWindow: '1 hour'
        },
        severity: 'medium'
      })
      return true
    }

    return false
  } catch (error) {
    console.error('Error detecting suspicious activity:', error)
    return false
  }
}

export function sanitizeUserInput(input: string): string {
  // Remove potentially dangerous characters
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim()
}

export function validateEmailFormat(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isStrongPassword(password: string): { isStrong: boolean; reasons: string[] } {
  const reasons: string[] = []
  
  if (password.length < 8) {
    reasons.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    reasons.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    reasons.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    reasons.push('Password must contain at least one number')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    reasons.push('Password must contain at least one special character')
  }
  
  return {
    isStrong: reasons.length === 0,
    reasons
  }
}
