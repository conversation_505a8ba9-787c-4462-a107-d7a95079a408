import { NextRequest } from 'next/server'

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Max requests per window
  keyGenerator?: (request: NextRequest) => string
}

// In-memory store for rate limiting (use Redis in production)
const requestCounts = new Map<string, { count: number; resetTime: number }>()

export function rateLimit(config: RateLimitConfig) {
  return async (request: NextRequest): Promise<{ success: boolean; remaining: number; resetTime: number } | null> => {
    const key = config.keyGenerator ? config.keyGenerator(request) : getClientIP(request)
    const now = Date.now()
    const windowStart = now - config.windowMs

    // Clean up old entries
    for (const [k, v] of requestCounts.entries()) {
      if (v.resetTime < now) {
        requestCounts.delete(k)
      }
    }

    const current = requestCounts.get(key)
    
    if (!current || current.resetTime < now) {
      // First request in window or window expired
      requestCounts.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      return {
        success: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs
      }
    }

    if (current.count >= config.maxRequests) {
      // Rate limit exceeded
      return {
        success: false,
        remaining: 0,
        resetTime: current.resetTime
      }
    }

    // Increment count
    current.count++
    requestCounts.set(key, current)

    return {
      success: true,
      remaining: config.maxRequests - current.count,
      resetTime: current.resetTime
    }
  }
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

// Pre-configured rate limiters
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 auth attempts per 15 minutes
})

export const apiRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 60, // 60 requests per minute
})

export const stripeWebhookRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100, // 100 webhook calls per minute
  keyGenerator: () => 'stripe-webhooks' // Single key for all Stripe webhooks
})
