/**
 * 🎯 Answer Quality Assessment System
 * 
 * Evaluates whether responses actually solve the user's specific question
 * with actionable information - NOT just data availability!
 */

export interface QuestionAnalysis {
  questionType: 'yes_no' | 'measurement' | 'process' | 'requirement' | 'comparison' | 'general'
  keyElements: string[]
  expectedAnswerComponents: string[]
  specificity: 'high' | 'medium' | 'low'
}

export interface AnswerQualityScore {
  overallQuality: number // 0-1 scale - TRUE confidence in answer quality
  specificity: number // Does it provide exact numbers/requirements?
  actionability: number // Can user make decisions based on this?
  completeness: number // Are all parts of question addressed?
  directness: number // Does it directly answer what was asked?
  reasoning: string
  improvements: string[]
}

/**
 * 🧠 Analyze the user's question to understand what type of answer they need
 */
export function analyzeQuestion(userQuery: string): QuestionAnalysis {
  const query = userQuery.toLowerCase()
  
  // Question Type Detection - Simplified approach
  let questionType: QuestionAnalysis['questionType'] = 'general'

  if (query.includes('can i') || query.includes('am i allowed') || query.includes('do i need')) {
    questionType = 'yes_no'
  } else if (query.includes('how big') || query.includes('how tall') || query.includes('how much')) {
    questionType = 'measurement'
  } else if (query.includes('what are the steps') || query.includes('how do i') || query.includes('process')) {
    questionType = 'process'
  } else if (query.includes('what are the') || query.includes('requirements') || query.includes('restrictions')) {
    questionType = 'requirement'
  }
  
  // Extract Key Elements
  const keyElements: string[] = []
  
  // Structure types
  const structures = ['fence', 'deck', 'shed', 'garage', 'pool', 'addition', 'adu', 'carport']
  structures.forEach(structure => {
    if (query.includes(structure)) keyElements.push(structure)
  })
  
  // Measurements
  const measurements = query.match(/\d+[\s-]?(foot|feet|ft|inch|inches|in|yard|yd|square|sq)/gi) || []
  keyElements.push(...measurements)
  
  // Concepts
  const concepts = ['height', 'setback', 'permit', 'size', 'distance', 'requirement']
  concepts.forEach(concept => {
    if (query.includes(concept)) keyElements.push(concept)
  })
  
  // Expected Answer Components based on question type
  const expectedAnswerComponents: string[] = []
  
  switch (questionType) {
    case 'yes_no':
      expectedAnswerComponents.push('clear yes/no answer', 'supporting regulation', 'conditions if any')
      break
    case 'measurement':
      expectedAnswerComponents.push('specific measurements', 'units', 'applicable conditions')
      break
    case 'process':
      expectedAnswerComponents.push('step-by-step process', 'required documents', 'timeline')
      break
    case 'requirement':
      expectedAnswerComponents.push('specific requirements', 'applicable codes', 'exceptions')
      break
  }
  
  // Determine specificity
  const specificity = measurements.length > 0 || keyElements.length > 2 ? 'high' : 
                    keyElements.length > 0 ? 'medium' : 'low'
  
  return {
    questionType,
    keyElements,
    expectedAnswerComponents,
    specificity
  }
}

/**
 * 🎯 Evaluate answer quality - does it actually solve the user's question?
 */
export function assessAnswerQuality(
  userQuery: string, 
  aiResponse: string, 
  citations: any[]
): AnswerQualityScore {
  const questionAnalysis = analyzeQuestion(userQuery)
  const response = aiResponse.toLowerCase()
  const query = userQuery.toLowerCase()
  
  // 1. SPECIFICITY SCORE - Does it provide exact numbers/requirements?
  let specificity = 0
  
  // Check for specific measurements
  const responseNumbers = response.match(/\d+[\s-]?(foot|feet|ft|inch|inches|in|yard|yd|square|sq|percent|%)/gi) || []
  if (responseNumbers.length > 0) specificity += 0.4
  
  // Check for specific code references
  const codeReferences = response.match(/(code|section|chapter|article)\s+[\d-]+/gi) || []
  if (codeReferences.length > 0) specificity += 0.3
  
  // Check for specific requirements
  const specificTerms = ['required', 'prohibited', 'allowed', 'maximum', 'minimum', 'must', 'shall']
  const specificMatches = specificTerms.filter(term => response.includes(term)).length
  specificity += Math.min(0.3, specificMatches * 0.1)
  
  // 2. ACTIONABILITY SCORE - Can user make decisions based on this?
  let actionability = 0
  
  if (questionAnalysis.questionType === 'yes_no') {
    // For yes/no questions, look for clear answers
    if (/\b(yes|no|allowed|permitted|prohibited|required|not required)\b/i.test(response)) {
      actionability += 0.5
    }
    if (/\b(can|cannot|may|may not|must|must not)\b/i.test(response)) {
      actionability += 0.3
    }
  } else if (questionAnalysis.questionType === 'measurement') {
    // For measurement questions, look for specific numbers
    if (responseNumbers.length > 0) actionability += 0.6
    if (/maximum|minimum|up to|no more than|at least/i.test(response)) {
      actionability += 0.2
    }
  }
  
  // General actionability indicators
  if (/permit.*(required|needed|necessary)/i.test(response)) actionability += 0.2
  
  // 3. COMPLETENESS SCORE - Are all parts of question addressed?
  let completeness = 0
  
  // Check if key elements from question are addressed in response
  const addressedElements = questionAnalysis.keyElements.filter(element => 
    response.includes(element.toLowerCase())
  ).length
  
  if (questionAnalysis.keyElements.length > 0) {
    completeness = addressedElements / questionAnalysis.keyElements.length
  } else {
    completeness = 0.5 // Default for general questions
  }
  
  // 4. DIRECTNESS SCORE - Does it directly answer what was asked?
  let directness = 0
  
  // Check if response structure matches question type
  if (questionAnalysis.questionType === 'yes_no') {
    if (/^(yes|no|you can|you cannot|it is|it is not)/i.test(aiResponse.trim())) {
      directness += 0.5
    }
  }
  
  // Penalize vague responses
  const vagueTerms = ['generally', 'typically', 'usually', 'may', 'might', 'could', 'often']
  const vagueCount = vagueTerms.filter(term => response.includes(term)).length
  directness = Math.max(0, 0.8 - (vagueCount * 0.1))
  
  // Bonus for direct answers
  if (response.includes('according to') || response.includes('per code')) {
    directness += 0.2
  }
  
  // Calculate overall quality
  const overallQuality = (specificity + actionability + completeness + directness) / 4
  
  // Generate reasoning and improvements
  const reasoning = generateQualityReasoning(questionAnalysis, specificity, actionability, completeness, directness)
  const improvements = generateImprovements(questionAnalysis, specificity, actionability, completeness, directness)
  
  return {
    overallQuality: Math.min(1, overallQuality),
    specificity: Math.min(1, specificity),
    actionability: Math.min(1, actionability),
    completeness: Math.min(1, completeness),
    directness: Math.min(1, directness),
    reasoning,
    improvements
  }
}

function generateQualityReasoning(
  analysis: QuestionAnalysis, 
  specificity: number, 
  actionability: number, 
  completeness: number, 
  directness: number
): string {
  const scores = [
    `Specificity: ${Math.round(specificity * 100)}%`,
    `Actionability: ${Math.round(actionability * 100)}%`, 
    `Completeness: ${Math.round(completeness * 100)}%`,
    `Directness: ${Math.round(directness * 100)}%`
  ]
  
  return `Question type: ${analysis.questionType}. ${scores.join(', ')}`
}

function generateImprovements(
  analysis: QuestionAnalysis,
  specificity: number,
  actionability: number, 
  completeness: number,
  directness: number
): string[] {
  const improvements: string[] = []
  
  if (specificity < 0.7) {
    improvements.push('Provide specific measurements, code sections, or exact requirements')
  }
  
  if (actionability < 0.7) {
    if (analysis.questionType === 'yes_no') {
      improvements.push('Give a clear yes/no answer with supporting details')
    } else {
      improvements.push('Provide actionable information the user can act upon')
    }
  }
  
  if (completeness < 0.7) {
    improvements.push(`Address all question elements: ${analysis.keyElements.join(', ')}`)
  }
  
  if (directness < 0.7) {
    improvements.push('Answer the question more directly, avoid vague language')
  }
  
  return improvements
}
