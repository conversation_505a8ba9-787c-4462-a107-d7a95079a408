/**
 * Citation Extraction Service
 * Extracts and enhances legal citations from compliance content
 */

export interface ExtractedCitation {
  citation: string
  source: string
  url: string
  verified: boolean
  confidence: number
}

/**
 * Extract legal citations from content
 */
export async function extractLegalCitations(
  content: string,
  sources: Array<{ title: string; url: string; type: string; citation: string; verified: boolean }>
): Promise<ExtractedCitation[]> {
  const citations: ExtractedCitation[] = []

  try {
    // Simple citation extraction patterns
    const citationPatterns = [
      /per\s+([^,]+)\s+ordinance\s+chapter\s+(\d+)[,\s]+section\s+([\d.]+)/gi,
      /([^,]+)\s+municipal\s+code\s+section\s+([\d.]+)/gi,
      /([^,]+)\s+zoning\s+ordinance\s+([^,]+)/gi,
      /chapter\s+(\d+)[,\s]+section\s+([\d.]+)/gi
    ]

    for (const pattern of citationPatterns) {
      let match
      while ((match = pattern.exec(content)) !== null) {
        const fullMatch = match[0]
        const source = sources.length > 0 ? sources[0] : null
        
        citations.push({
          citation: fullMatch,
          source: source?.title || 'Municipal Code',
          url: source?.url || '',
          verified: source?.verified || false,
          confidence: 0.8
        })
      }
    }

    // Add source citations
    sources.forEach(source => {
      if (source.citation) {
        citations.push({
          citation: source.citation,
          source: source.title,
          url: source.url,
          verified: source.verified,
          confidence: 0.9
        })
      }
    })

    return citations

  } catch (error) {
    console.error('Error extracting citations:', error)
    return []
  }
}
