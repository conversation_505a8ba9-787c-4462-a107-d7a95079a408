// import { RAGDocument, RAGCitation } from './rag-service' // DISABLED - using bulletproof research instead

// Temporary type definitions until we refactor
export interface RAGDocument {
  id: string
  content: string
  content_chunk?: string
  source_url?: string
  legal_citation?: string
  title?: string
  similarity?: number
  jurisdiction?: string
  metadata?: {
    section?: string
    document_type?: string
  }
}

export interface RAGCitation {
  text: string
  source_url?: string
  legal_citation?: string
  title?: string
  section?: string
  document_type?: string
  url?: string
  similarity?: number
  jurisdiction?: string
}

export interface ParsedCitation {
  number: number
  text: string
  position: {
    start: number
    end: number
  }
}

export interface CitationMapping {
  citations: ParsedCitation[]
  sources: RAGCitation[]
  unmappedCitations: number[]
}

/**
 * Parse citations from AI response text
 * Looks for patterns like [1], [2], etc.
 */
export function parseCitations(text: string): ParsedCitation[] {
  const citations: ParsedCitation[] = []
  const citationRegex = /\[(\d+)\]/g
  let match

  while ((match = citationRegex.exec(text)) !== null) {
    citations.push({
      number: parseInt(match[1]),
      text: match[0],
      position: {
        start: match.index,
        end: match.index + match[0].length
      }
    })
  }

  return citations
}

/**
 * Map parsed citations to source documents
 */
export function mapCitationsToSources(
  citations: ParsedCitation[],
  sources: RAGDocument[]
): CitationMapping {
  const mappedSources: RAGCitation[] = []
  const unmappedCitations: number[] = []

  citations.forEach(citation => {
    const sourceIndex = citation.number - 1 // Citations are 1-based, arrays are 0-based
    
    if (sourceIndex >= 0 && sourceIndex < sources.length) {
      const source = sources[sourceIndex]
      mappedSources.push({
        text: source.content || '',
        title: source.title,
        section: source.metadata?.section || 'General Regulations',
        document_type: source.metadata?.document_type || 'Municipal Code',
        url: source.source_url,
        similarity: source.similarity,
        jurisdiction: source.jurisdiction
      })
    } else {
      unmappedCitations.push(citation.number)
    }
  })

  return {
    citations,
    sources: mappedSources,
    unmappedCitations
  }
}

/**
 * Validate that all citations in text have corresponding sources
 */
export function validateCitations(text: string, sources: RAGDocument[]): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const citations = parseCitations(text)
  const errors: string[] = []
  const warnings: string[] = []

  // Check for citations without sources
  citations.forEach(citation => {
    const sourceIndex = citation.number - 1
    if (sourceIndex >= sources.length || sourceIndex < 0) {
      errors.push(`Citation [${citation.number}] has no corresponding source`)
    }
  })

  // Check for unused sources
  const usedSourceNumbers = new Set(citations.map(c => c.number))
  for (let i = 1; i <= sources.length; i++) {
    if (!usedSourceNumbers.has(i)) {
      warnings.push(`Source ${i} (${sources[i-1]?.title}) is not cited in the response`)
    }
  }

  // Check for duplicate citations
  const citationNumbers = citations.map(c => c.number)
  const duplicates = citationNumbers.filter((num, index) => citationNumbers.indexOf(num) !== index)
  if (duplicates.length > 0) {
    warnings.push(`Duplicate citations found: [${[...new Set(duplicates)].join('], [')}]`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Generate citation text for RAG prompt
 * Creates numbered source list for AI to reference
 */
export function generateCitationPrompt(sources: RAGDocument[]): string {
  if (sources.length === 0) {
    return 'No specific sources available. Provide general guidance and recommend contacting local authorities.'
  }

  const sourceList = sources.map((source, index) => {
    const number = index + 1
    const title = source.title || 'Compliance Document'
    const jurisdiction = source.jurisdiction
    const content = source.content_chunk
    const sourceUrl = source.source_url || 'Local regulations'
    
    return `[${number}] ${title} - ${jurisdiction}\nContent: ${content}\nSource: ${sourceUrl}`
  }).join('\n\n')

  return `Relevant compliance documents:\n\n${sourceList}\n\nInstructions: Answer the question using ONLY the above information, and cite each fact with the source number like [1], [2], etc. If the answer is not in the provided sources, say you don't know and recommend contacting local authorities.`
}

/**
 * Extract citations from streaming response incrementally
 */
export class StreamingCitationParser {
  private buffer: string = ''
  private foundCitations: ParsedCitation[] = []
  private lastProcessedIndex: number = 0

  /**
   * Add new text chunk and parse any new citations
   */
  addChunk(chunk: string): ParsedCitation[] {
    this.buffer += chunk
    const newCitations: ParsedCitation[] = []

    // Only process new text since last check
    const newText = this.buffer.slice(this.lastProcessedIndex)
    const citationRegex = /\[(\d+)\]/g
    let match

    while ((match = citationRegex.exec(newText)) !== null) {
      const absolutePosition = this.lastProcessedIndex + match.index
      const citation: ParsedCitation = {
        number: parseInt(match[1]),
        text: match[0],
        position: {
          start: absolutePosition,
          end: absolutePosition + match[0].length
        }
      }

      // Check if this is a new citation
      const isNew = !this.foundCitations.some(existing => 
        existing.position.start === citation.position.start
      )

      if (isNew) {
        this.foundCitations.push(citation)
        newCitations.push(citation)
      }
    }

    this.lastProcessedIndex = this.buffer.length
    return newCitations
  }

  /**
   * Get all citations found so far
   */
  getAllCitations(): ParsedCitation[] {
    return [...this.foundCitations]
  }

  /**
   * Get the complete text buffer
   */
  getBuffer(): string {
    return this.buffer
  }

  /**
   * Reset the parser for a new response
   */
  reset(): void {
    this.buffer = ''
    this.foundCitations = []
    this.lastProcessedIndex = 0
  }
}

/**
 * Format citations for display in sources panel
 */
export function formatCitationsForDisplay(
  citations: RAGCitation[],
  options: {
    showSimilarity?: boolean
    showJurisdiction?: boolean
    maxTitleLength?: number
  } = {}
): Array<{
  id: string
  title: string
  subtitle: string
  url?: string
  metadata: string[]
}> {
  const {
    showSimilarity = true,
    showJurisdiction = true,
    maxTitleLength = 60
  } = options

  return citations.map((citation, index) => {
    const id = `citation-${index + 1}`
    let title = citation.title || 'Compliance Document'
    
    // Truncate title if too long
    if (title.length > maxTitleLength) {
      title = title.substring(0, maxTitleLength - 3) + '...'
    }

    const subtitle = citation.section || citation.document_type || 'General Regulations'
    
    const metadata: string[] = []
    if (showJurisdiction && citation.jurisdiction) {
      metadata.push(citation.jurisdiction)
    }
    if (showSimilarity && citation.similarity) {
      metadata.push(`${Math.round(citation.similarity * 100)}% match`)
    }

    return {
      id,
      title,
      subtitle,
      url: citation.url,
      metadata
    }
  })
}
