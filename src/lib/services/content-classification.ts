/**
 * Content Classification Service
 * Distinguishes between public domain ordinances and proprietary model codes
 * Ensures proper citation strategies and copyright compliance
 */

export interface ContentClassification {
  isPublicDomain: boolean
  contentType: 'municipal_ordinance' | 'model_code' | 'commentary' | 'government_document' | 'unknown'
  fullTextAllowed: boolean
  citationStrategy: 'verbatim_quote' | 'reference_only' | 'summary_only'
  confidence: number // 0-1 scale
  indicators: {
    publicDomainIndicators: string[]
    proprietaryIndicators: string[]
    governmentIndicators: string[]
    modelCodeIndicators: string[]
  }
}

export interface SourceValidation {
  isAccessible: boolean
  hasRelevantContent: boolean
  copyrightStatus: 'public_domain' | 'proprietary' | 'unknown'
  usageAllowed: boolean
  citationStrategy: 'verbatim_quote' | 'reference_only' | 'summary_only'
  classification: ContentClassification
  contentQuality: number // 0-1 scale
}

/**
 * Classify content as public domain ordinance vs proprietary model code
 */
export async function classifyContent(sourceUrl: string, content: string): Promise<ContentClassification> {
  const url = sourceUrl.toLowerCase()
  const text = content.toLowerCase()
  
  // Initialize indicators
  const indicators: ContentClassification['indicators'] = {
    publicDomainIndicators: [],
    proprietaryIndicators: [],
    governmentIndicators: [],
    modelCodeIndicators: []
  }

  // Public domain indicators (more specific for ordinances - must be actual ordinance text, not references)
  const publicDomainPatterns = [
    { pattern: /^.*\b(zoning ordinance|municipal code|city code|county code)\b.*(?:chapter|section)/i, indicator: 'Municipal ordinance language' },
    { pattern: /\b(adopted|enacted|ordained by|passed by)\b.*\b(council|commission|board)\b/i, indicator: 'Government adoption language' },
    { pattern: /\bchapter \d+.*section \d+/i, indicator: 'Legal structure format' },
    { pattern: /\b(be it ordained|whereas.*now therefore)\b/i, indicator: 'Legislative language' },
    { pattern: /\b(city council|county commission|board of supervisors|board of commissioners)\b/i, indicator: 'Government body references' }
  ]

  // Government source indicators (more specific)
  const governmentPatterns = [
    { pattern: /\.gov|\.org.*county|\.org.*city|miottawa\.org|kentcountymi\.gov/i, indicator: 'Government domain' },
    { pattern: /\b(city of|county of|township of|village of|\w+ county|\w+ city)\b/i, indicator: 'Government entity' },
    { pattern: /\b(planning department|building department|zoning department|building safety|buildingsafety)\b/i, indicator: 'Government department' },
    { pattern: /\b(municode|american legal|general code)\b/i, indicator: 'Municipal code publisher' }
  ]

  // Proprietary model code indicators (more specific patterns)
  const modelCodePatterns = [
    { pattern: /\b(international building code|ibc|international residential code|irc)\b/i, indicator: 'International Building/Residential Code' },
    { pattern: /\b(national electrical code|nfpa.*70)\b|\bnec\b(?!.*enacted)/i, indicator: 'National Electrical Code' },
    { pattern: /\b(international fire code|ifc|international mechanical code|imc)\b/i, indicator: 'International Fire/Mechanical Code' },
    { pattern: /\b(international plumbing code|ipc|international energy conservation code|iecc)\b/i, indicator: 'International Plumbing/Energy Code' },
    { pattern: /\b(uniform building code|ubc|uniform mechanical code|umc)\b/i, indicator: 'Uniform Codes' },
    { pattern: /\bcopyright.*all rights reserved\b|\bproprietary\b/i, indicator: 'Copyright notice' },
    { pattern: /\b(icc|international code council)\b/i, indicator: 'ICC publisher' },
    { pattern: /\b(nfpa|national fire protection association)\b/i, indicator: 'NFPA publisher' }
  ]

  // Proprietary content indicators (more specific to avoid false positives)
  const proprietaryPatterns = [
    { pattern: /copyright.*all rights reserved|proprietary.*content/i, indicator: 'Copyright restrictions' },
    { pattern: /subscription required|premium content|licensed material/i, indicator: 'Paid/licensed content' },
    { pattern: /unauthorized reproduction.*prohibited|permission required.*reproduce/i, indicator: 'Reproduction restrictions' }
  ]

  // Check patterns and collect indicators
  publicDomainPatterns.forEach(({ pattern, indicator }) => {
    if (pattern.test(text) || pattern.test(url)) {
      indicators.publicDomainIndicators.push(indicator)
    }
  })

  governmentPatterns.forEach(({ pattern, indicator }) => {
    if (pattern.test(text) || pattern.test(url)) {
      indicators.governmentIndicators.push(indicator)
    }
  })

  modelCodePatterns.forEach(({ pattern, indicator }) => {
    if (pattern.test(text) || pattern.test(url)) {
      indicators.modelCodeIndicators.push(indicator)
    }
  })

  proprietaryPatterns.forEach(({ pattern, indicator }) => {
    if (pattern.test(text) || pattern.test(url)) {
      indicators.proprietaryIndicators.push(indicator)
    }
  })

  // Calculate scores
  const publicDomainScore = indicators.publicDomainIndicators.length + indicators.governmentIndicators.length
  const proprietaryScore = indicators.modelCodeIndicators.length + indicators.proprietaryIndicators.length

  // Determine classification with improved logic
  let contentType: ContentClassification['contentType'] = 'unknown'
  let isPublicDomain = false
  let fullTextAllowed = false
  let citationStrategy: ContentClassification['citationStrategy'] = 'summary_only'
  let confidence = 0

  // Check for government source first (most important indicator)
  const hasGovernmentSource = indicators.governmentIndicators.length > 0
  const hasPublicDomainLanguage = indicators.publicDomainIndicators.length > 0
  const hasModelCodeReferences = indicators.modelCodeIndicators.length > 0
  const hasProprietaryIndicators = indicators.proprietaryIndicators.length > 0

  // Check for specific ordinance language vs general government content
  const hasOrdinanceLanguage = indicators.publicDomainIndicators.some(indicator =>
    indicator.includes('Municipal ordinance') || indicator.includes('Legislative language') || indicator.includes('Government adoption')
  )

  // Priority 1: Government source with specific ordinance language = Municipal Ordinance
  if (hasGovernmentSource && hasOrdinanceLanguage) {
    contentType = 'municipal_ordinance'
    isPublicDomain = true
    fullTextAllowed = true
    citationStrategy = 'verbatim_quote'
    confidence = Math.min(0.95, 0.7 + (publicDomainScore * 0.05))
  }
  // Priority 2: Government source without specific ordinance language = Government Document
  else if (hasGovernmentSource && !hasOrdinanceLanguage) {
    contentType = 'government_document'
    isPublicDomain = true
    fullTextAllowed = true
    citationStrategy = 'verbatim_quote'
    confidence = Math.min(0.8, 0.5 + (indicators.governmentIndicators.length * 0.1))
  }
  // Priority 3: Strong model code indicators without government source = Model Code
  else if (hasModelCodeReferences && !hasGovernmentSource && indicators.modelCodeIndicators.length >= 2) {
    contentType = 'model_code'
    isPublicDomain = false
    fullTextAllowed = false
    citationStrategy = 'reference_only'
    confidence = Math.min(0.9, 0.6 + (indicators.modelCodeIndicators.length * 0.1))
  }
  // Priority 4: Strong proprietary indicators without government source = Proprietary
  else if (hasProprietaryIndicators && !hasGovernmentSource) {
    contentType = 'commentary'
    isPublicDomain = false
    fullTextAllowed = false
    citationStrategy = 'summary_only'
    confidence = Math.min(0.9, 0.6 + (indicators.proprietaryIndicators.length * 0.1))
  }
  // Default to safe approach
  else {
    contentType = 'unknown'
    isPublicDomain = false
    fullTextAllowed = false
    citationStrategy = 'summary_only'
    confidence = 0.1
  }

  return {
    isPublicDomain,
    contentType,
    fullTextAllowed,
    citationStrategy,
    confidence,
    indicators
  }
}

/**
 * Validate source with copyright awareness
 */
export async function validateSourceWithCopyright(
  sourceUrl: string, 
  content: string | null,
  title: string = ''
): Promise<SourceValidation> {
  
  // Basic accessibility check
  const isAccessible = !!content && content.trim().length > 0
  
  if (!isAccessible) {
    return {
      isAccessible: false,
      hasRelevantContent: false,
      copyrightStatus: 'unknown',
      usageAllowed: false,
      citationStrategy: 'summary_only',
      classification: await classifyContent(sourceUrl, ''),
      contentQuality: 0
    }
  }

  // Classify content
  const classification = await classifyContent(sourceUrl, content)
  
  // Check for relevant compliance content
  const relevancePatterns = [
    /zoning|building|construction|permit|setback|height|area|lot/i,
    /ordinance|code|regulation|requirement|restriction/i,
    /residential|commercial|industrial|accessory/i,
    /fence|shed|deck|garage|addition|structure/i
  ]
  
  const hasRelevantContent = relevancePatterns.some(pattern => 
    pattern.test(content) || pattern.test(title)
  )

  // Calculate content quality
  let contentQuality = 0
  if (hasRelevantContent) contentQuality += 0.4
  if (content.length > 1000) contentQuality += 0.2
  if (content.length > 5000) contentQuality += 0.2
  if (classification.confidence > 0.7) contentQuality += 0.2

  // Determine usage permissions
  const usageAllowed = isAccessible && hasRelevantContent && 
    (classification.fullTextAllowed || classification.citationStrategy === 'reference_only')

  return {
    isAccessible,
    hasRelevantContent,
    copyrightStatus: classification.isPublicDomain ? 'public_domain' : 'proprietary',
    usageAllowed,
    citationStrategy: classification.citationStrategy,
    classification,
    contentQuality
  }
}

/**
 * Filter sources to only include those we can legally and properly use
 */
export async function filterValidSources(
  sources: Array<{ title: string; url: string; content: string | null; snippet?: string }>
): Promise<Array<{ 
  title: string; 
  url: string; 
  content: string | null; 
  snippet?: string;
  validation: SourceValidation;
}>> {
  const validatedSources = []

  for (const source of sources) {
    const validation = await validateSourceWithCopyright(
      source.url, 
      source.content, 
      source.title
    )

    // Only include sources we can use
    if (validation.usageAllowed && validation.contentQuality > 0.3) {
      validatedSources.push({
        ...source,
        validation
      })
    } else {
      console.log(`🚫 Filtered out source: ${source.title} (${validation.copyrightStatus}, quality: ${validation.contentQuality})`)
    }
  }

  console.log(`✅ Filtered ${sources.length} sources to ${validatedSources.length} valid sources`)
  return validatedSources
}
