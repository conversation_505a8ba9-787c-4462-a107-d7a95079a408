/**
 * Direct Answer Engine - Forces specific, actionable answers to user questions
 * Addresses root cause: AI provides summaries instead of direct answers
 */

export interface QuestionElement {
  type: 'object' | 'measurement' | 'location' | 'action' | 'qualifier';
  value: string;
  required: boolean;
}

export interface ParsedQuestion {
  questionType: 'yes_no' | 'measurement' | 'requirement' | 'process' | 'comparison';
  elements: QuestionElement[];
  intent: string;
  requiredAnswerFormat: string;
}

export interface DirectAnswer {
  answer: 'YES' | 'NO' | 'CONDITIONAL' | 'INSUFFICIENT_INFO';
  directStatement: string;
  reasoning: string;
  ordinanceCitations: string[];
  confidence: number;
  elementsCovered: string[];
  missingElements: string[];
}

export class DirectAnswerEngine {
  /**
   * Parse user question to extract all elements and determine required answer format
   */
  parseQuestion(userQuery: string): ParsedQuestion {
    const query = userQuery.toLowerCase();
    
    // Determine question type
    let questionType: ParsedQuestion['questionType'] = 'requirement';
    if (query.includes('can i') || query.includes('am i allowed') || query.includes('is it legal')) {
      questionType = 'yes_no';
    } else if (query.includes('how tall') || query.includes('how high') || query.includes('what size')) {
      questionType = 'measurement';
    } else if (query.includes('how do i') || query.includes('what steps')) {
      questionType = 'process';
    }

    // Extract elements using NLP patterns
    const elements: QuestionElement[] = [];
    
    // Extract measurements (6-foot, 8 feet, etc.)
    const measurementMatch = query.match(/(\d+)[-\s]?(foot|feet|ft|inch|inches|in)/g);
    if (measurementMatch) {
      measurementMatch.forEach(match => {
        elements.push({
          type: 'measurement',
          value: match,
          required: true
        });
      });
    }

    // Extract objects (fence, deck, shed, etc.)
    const objectPatterns = [
      'fence', 'privacy fence', 'deck', 'shed', 'garage', 'addition', 
      'pool', 'driveway', 'patio', 'pergola', 'gazebo'
    ];
    objectPatterns.forEach(pattern => {
      if (query.includes(pattern)) {
        elements.push({
          type: 'object',
          value: pattern,
          required: true
        });
      }
    });

    // Extract locations (backyard, front yard, side yard, etc.)
    const locationPatterns = ['backyard', 'front yard', 'side yard', 'property line'];
    locationPatterns.forEach(pattern => {
      if (query.includes(pattern)) {
        elements.push({
          type: 'location',
          value: pattern,
          required: true
        });
      }
    });

    // Extract actions (build, install, construct, etc.)
    const actionPatterns = ['build', 'install', 'construct', 'add', 'place'];
    actionPatterns.forEach(pattern => {
      if (query.includes(pattern)) {
        elements.push({
          type: 'action',
          value: pattern,
          required: true
        });
      }
    });

    // Generate required answer format
    let requiredAnswerFormat = '';
    if (questionType === 'yes_no') {
      const object = elements.find(e => e.type === 'object')?.value || 'structure';
      const measurement = elements.find(e => e.type === 'measurement')?.value || '';
      const location = elements.find(e => e.type === 'location')?.value || '';
      
      requiredAnswerFormat = `YES, you can ${object}${measurement ? ' ' + measurement : ''}${location ? ' in your ' + location : ''} OR NO, you cannot ${object}${measurement ? ' ' + measurement : ''}${location ? ' in your ' + location : ''} because...`;
    }

    return {
      questionType,
      elements,
      intent: userQuery,
      requiredAnswerFormat
    };
  }

  /**
   * Generate direct answer prompt that forces specific response format
   */
  generateDirectAnswerPrompt(parsedQuestion: ParsedQuestion, ordinanceContent: string): string {
    const { questionType, elements, requiredAnswerFormat } = parsedQuestion;
    
    const elementsList = elements.map(e => `${e.type}: ${e.value}`).join(', ');
    
    return `You are a compliance expert analyzing municipal ordinances. You must provide a DIRECT, SPECIFIC answer to the user's question.

CRITICAL REQUIREMENTS:
1. Start with a direct answer: YES, NO, CONDITIONAL, or INSUFFICIENT_INFO
2. Address ALL question elements: ${elementsList}
3. Provide specific ordinance citations
4. Use exact measurements and requirements from the ordinance

USER QUESTION: ${parsedQuestion.intent}

REQUIRED ANSWER FORMAT: ${requiredAnswerFormat}

ORDINANCE CONTENT:
${ordinanceContent}

RESPONSE STRUCTURE (MANDATORY):
DIRECT ANSWER: [YES/NO/CONDITIONAL/INSUFFICIENT_INFO]
SPECIFIC STATEMENT: [One sentence directly answering the question with all elements]
ORDINANCE CITATION: [Exact section and subsection references]
REASONING: [Brief explanation with specific requirements]

VALIDATION CHECKLIST:
- Does your answer start with YES, NO, CONDITIONAL, or INSUFFICIENT_INFO?
- Does your answer mention ALL elements: ${elementsList}?
- Do you provide specific ordinance section references?
- Do you give exact measurements/requirements from the ordinance?

If you cannot find specific information about any required element, respond with "INSUFFICIENT_INFO" and explain what information is missing.`;
  }

  /**
   * Parse AI response and validate it meets direct answer requirements
   */
  parseDirectAnswer(aiResponse: string, parsedQuestion: ParsedQuestion): DirectAnswer {
    const lines = aiResponse.split('\n').map(line => line.trim()).filter(line => line);
    
    // Extract direct answer
    let answer: DirectAnswer['answer'] = 'INSUFFICIENT_INFO';
    let directStatement = '';
    let reasoning = '';
    let ordinanceCitations: string[] = [];
    
    // Parse structured response
    for (const line of lines) {
      if (line.startsWith('DIRECT ANSWER:')) {
        const answerText = line.replace('DIRECT ANSWER:', '').trim().toUpperCase();
        if (['YES', 'NO', 'CONDITIONAL', 'INSUFFICIENT_INFO'].includes(answerText)) {
          answer = answerText as DirectAnswer['answer'];
        }
      } else if (line.startsWith('SPECIFIC STATEMENT:')) {
        directStatement = line.replace('SPECIFIC STATEMENT:', '').trim();
      } else if (line.startsWith('ORDINANCE CITATION:')) {
        const citation = line.replace('ORDINANCE CITATION:', '').trim();
        if (citation) ordinanceCitations.push(citation);
      } else if (line.startsWith('REASONING:')) {
        reasoning = line.replace('REASONING:', '').trim();
      }
    }

    // Fallback parsing if structured format not used
    if (!directStatement) {
      const firstSentence = aiResponse.split('.')[0];
      if (firstSentence.toLowerCase().includes('yes') || firstSentence.toLowerCase().includes('can')) {
        answer = 'YES';
        directStatement = firstSentence;
      } else if (firstSentence.toLowerCase().includes('no') || firstSentence.toLowerCase().includes('cannot')) {
        answer = 'NO';
        directStatement = firstSentence;
      }
    }

    // Validate element coverage
    const elementsCovered: string[] = [];
    const missingElements: string[] = [];
    
    parsedQuestion.elements.forEach(element => {
      const responseText = (directStatement + ' ' + reasoning).toLowerCase();
      if (responseText.includes(element.value.toLowerCase())) {
        elementsCovered.push(element.value);
      } else if (element.required) {
        missingElements.push(element.value);
      }
    });

    // Calculate confidence based on completeness and specificity
    let confidence = 0;
    
    // Base confidence from answer clarity
    if (answer === 'YES' || answer === 'NO') confidence += 40;
    else if (answer === 'CONDITIONAL') confidence += 30;
    else confidence += 10;
    
    // Element coverage bonus
    const coverageRatio = elementsCovered.length / Math.max(parsedQuestion.elements.length, 1);
    confidence += coverageRatio * 30;
    
    // Citation bonus
    if (ordinanceCitations.length > 0) confidence += 20;
    
    // Specificity bonus (mentions measurements, sections, etc.)
    if (directStatement.match(/\d+/)) confidence += 10;
    
    confidence = Math.min(confidence, 100);

    return {
      answer,
      directStatement,
      reasoning,
      ordinanceCitations,
      confidence,
      elementsCovered,
      missingElements
    };
  }

  /**
   * Generate enhanced prompt based on missing elements
   */
  generateEnhancedPrompt(originalPrompt: string, missingElements: string[]): string {
    if (missingElements.length === 0) return originalPrompt;
    
    const enhancement = `
CRITICAL: Your previous response was missing these required elements: ${missingElements.join(', ')}
You MUST address each of these elements in your response. If the ordinance doesn't mention a specific element, state that explicitly.

ENHANCED REQUIREMENTS:
${missingElements.map(element => `- You must mention "${element}" in your response`).join('\n')}
`;
    
    return originalPrompt + enhancement;
  }
}

export const directAnswerEngine = new DirectAnswerEngine();
