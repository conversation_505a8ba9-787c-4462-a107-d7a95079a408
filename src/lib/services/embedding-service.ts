/**
 * Embedding service for generating vector embeddings using OpenAI
 * Includes caching for improved performance
 */

// Simple in-memory cache for embeddings
const embeddingCache = new Map<string, number[]>()
const CACHE_MAX_SIZE = 1000
const CACHE_TTL = 1000 * 60 * 60 // 1 hour

/**
 * Generate embedding using OpenAI API with caching
 */
export async function generateEmbedding(text: string): Promise<number[] | null> {
  try {
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_key_here') {
      console.warn('Embedding Service: OpenAI API key not configured')
      return null
    }

    // Create cache key from text hash
    const cacheKey = createCacheKey(text)

    // Check cache first
    if (embeddingCache.has(cacheKey)) {
      console.log('Embedding Service: Cache hit for query')
      return embeddingCache.get(cacheKey)!
    }

    console.log('Embedding Service: Cache miss, generating new embedding')
    const startTime = Date.now()

    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'text-embedding-3-small',
        input: text,
        encoding_format: 'float'
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI Embeddings API error: ${response.status}`)
    }

    const data = await response.json()
    const embedding = data.data[0]?.embedding || null

    if (embedding) {
      // Cache the result
      cacheEmbedding(cacheKey, embedding)
      console.log(`Embedding Service: Generated and cached embedding in ${Date.now() - startTime}ms`)
    }

    return embedding

  } catch (error) {
    console.error('Embedding Service: Error generating embedding:', error)
    return null
  }
}

/**
 * Generate embeddings for multiple texts in batch
 */
export async function generateEmbeddingsBatch(texts: string[]): Promise<(number[] | null)[]> {
  try {
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_key_here') {
      console.warn('Embedding Service: OpenAI API key not configured')
      return texts.map(() => null)
    }

    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'text-embedding-3-small',
        input: texts,
        encoding_format: 'float'
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI Embeddings API error: ${response.status}`)
    }

    const data = await response.json()
    return data.data?.map((item: any) => item.embedding) || texts.map(() => null)

  } catch (error) {
    console.error('Embedding Service: Error generating batch embeddings:', error)
    return texts.map(() => null)
  }
}

/**
 * Calculate cosine similarity between two embeddings
 */
export function calculateSimilarity(embedding1: number[], embedding2: number[]): number {
  if (embedding1.length !== embedding2.length) {
    throw new Error('Embeddings must have the same length')
  }

  let dotProduct = 0
  let norm1 = 0
  let norm2 = 0

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i]
    norm1 += embedding1[i] * embedding1[i]
    norm2 += embedding2[i] * embedding2[i]
  }

  return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2))
}

/**
 * Create a cache key from text
 */
function createCacheKey(text: string): string {
  // Simple hash function for cache key
  let hash = 0
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return hash.toString()
}

/**
 * Cache an embedding with size and TTL management
 */
function cacheEmbedding(key: string, embedding: number[]): void {
  // Simple LRU: if cache is full, remove oldest entry
  if (embeddingCache.size >= CACHE_MAX_SIZE) {
    const firstKey = embeddingCache.keys().next().value
    if (firstKey) {
      embeddingCache.delete(firstKey)
    }
  }

  embeddingCache.set(key, embedding)
}

/**
 * Clear the embedding cache
 */
export function clearEmbeddingCache(): void {
  embeddingCache.clear()
  console.log('Embedding Service: Cache cleared')
}

/**
 * Get cache statistics
 */
export function getCacheStats(): { size: number; maxSize: number } {
  return {
    size: embeddingCache.size,
    maxSize: CACHE_MAX_SIZE
  }
}
