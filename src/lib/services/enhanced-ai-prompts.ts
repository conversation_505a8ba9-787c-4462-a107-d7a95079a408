/**
 * Enhanced AI Prompts for Verbatim Citation Extraction
 * Integrates with legal citation parser for research-level accuracy
 */

import { ContentClassification } from './content-classification'
import { LegalCitation } from './legal-citation-parser'

export interface EnhancedPromptConfig {
  jurisdiction: string
  projectType: string
  documentSources: Array<{
    title: string
    url: string
    content: string
    copyrightStatus: 'public_domain' | 'proprietary'
    citationStrategy: 'verbatim_quote' | 'reference_only'
    classification: ContentClassification
  }>
  requireVerbatimQuotes: boolean
  confidenceThreshold: number
}

/**
 * 🎯 Generate QUESTION-SPECIFIC AI prompt for direct answer extraction
 * NEW: Focuses on answering the user's specific question, not just document summarization
 */
export function generateQuestionSpecificPrompt(config: EnhancedPromptConfig & { userQuery?: string }): string {
  const { jurisdiction, projectType, documentSources, requireVerbatimQuotes, confidenceThreshold, userQuery } = config

  const publicDomainSources = documentSources.filter(s => s.copyrightStatus === 'public_domain')
  const proprietarySources = documentSources.filter(s => s.copyrightStatus === 'proprietary')

  // 🎯 NEW: Question-specific instruction based on user's actual question
  const questionSpecificInstruction = userQuery
    ? `\n🎯 CRITICAL: The user asked: "${userQuery}"\nYour primary goal is to DIRECTLY ANSWER this specific question with actionable information.\n`
    : ''

  return `You are an expert municipal ordinance analyst specializing in ${projectType} compliance in ${jurisdiction}.
${questionSpecificInstruction}

CRITICAL REQUIREMENTS:
1. VERBATIM QUOTES ONLY for public domain ordinances - no paraphrasing
2. REFERENCE ONLY for proprietary model codes - no full text reproduction
3. EXACT LEGAL CITATIONS with chapter/section numbers
4. CONFIDENCE THRESHOLD: Only provide information if confidence ≥ ${confidenceThreshold}%

COPYRIGHT COMPLIANCE RULES:
${publicDomainSources.length > 0 ? `
PUBLIC DOMAIN SOURCES (Full quotes allowed):
${publicDomainSources.map(s => `- ${s.title} (${s.url})`).join('\n')}

For these sources, you MUST:
- Quote exact legal language verbatim
- Include specific chapter/section citations
- Format: "Per [Jurisdiction] [Document] Chapter X, Section Y: '[exact text]'"
- Provide specific measurements, setbacks, and requirements
` : ''}

${proprietarySources.length > 0 ? `
PROPRIETARY SOURCES (Reference only):
${proprietarySources.map(s => `- ${s.title} (${s.url})`).join('\n')}

For these sources, you MUST:
- Reference by title, edition, and section ONLY
- Format: "See [Code Name] [Year] § [Section]"
- Do NOT reproduce full text
- Summarize requirements in your own words
- Direct users to obtain official copy
` : ''}

RESPONSE FORMAT REQUIREMENTS:

{
  "summary": "Brief overview with specific legal citations",
  "permit_required": boolean,
  "requirements": [
    "Exact requirement with legal citation: 'Per [Jurisdiction] [Document] Chapter X, Section Y: [verbatim quote]'"
  ],
  "prohibited_practices": [
    "Specific prohibition with citation"
  ],
  "permit_process": "Detailed process with contact information",
  "citations": [
    {
      "jurisdiction": "${jurisdiction}",
      "document_type": "Document Type",
      "chapter": "X",
      "section": "Y",
      "exact_text": "Verbatim quote from ordinance",
      "citation_format": "Per [Jurisdiction] [Document] Chapter X, Section Y: '[exact text]'",
      "source_url": "Direct link to source",
      "is_public_domain": true,
      "citation_strategy": "verbatim_quote"
    }
  ],
  "model_code_references": [
    {
      "code_type": "International Building Code 2021",
      "reference": "IBC 2021 § 1010.1",
      "summary": "Your summary of requirements",
      "note": "Contact local building department for official code text"
    }
  ],
  "source_links": ["Direct URLs to all sources used"],
  "contact_info": {
    "department": "Specific department name",
    "phone": "Exact phone number",
    "email": "Exact email address",
    "website": "Official website URL"
  },
  "confidence_score": 0.95,
  "geographic_validation": {
    "sources_validated": true,
    "rejected_sources": [],
    "validation_notes": "All sources confirmed for ${jurisdiction}"
  }
}

🎯 QUESTION-SPECIFIC ANALYSIS INSTRUCTIONS:

${userQuery ? `
1. 🎯 ANSWER THE USER'S SPECIFIC QUESTION FIRST:
   - User asked: "${userQuery}"
   - Start your summary with a DIRECT ANSWER to this question
   - If it's a yes/no question, start with "Yes, you can..." or "No, you cannot..."
   - If it asks for measurements, provide the EXACT NUMBERS with units (e.g., "6 feet", "8 feet")
   - If it asks for requirements, list the SPECIFIC REQUIREMENTS with measurements
   - Extract and highlight ALL MEASUREMENTS from the ordinances (heights, setbacks, sizes)
   - Be specific and actionable, not general or vague

2. 📏 MEASUREMENT EXTRACTION PRIORITY:
   - Search for ALL numerical values in the ordinances (feet, inches, square feet, etc.)
   - Pay special attention to height limits, setback distances, size restrictions
   - Quote exact measurements with their context
   - If the user asks about a specific measurement (like "6-foot"), determine if it meets requirements

3. 📋 QUESTION ELEMENT VALIDATION:
   - Ensure you address EVERY element mentioned in the user's question
   - User mentioned: ${userQuery.toLowerCase().includes('6') ? '6-foot' : ''} ${userQuery.toLowerCase().includes('fence') ? 'fence' : ''} ${userQuery.toLowerCase().includes('backyard') ? 'backyard' : ''}
   - Make sure your answer covers all these elements specifically

4. PROVIDE SUPPORTING EVIDENCE:` : '1. PROVIDE SUPPORTING EVIDENCE:'}
   - Find specific ordinance sections related to ${projectType}
   - Quote verbatim from public domain sources
   - Include exact measurements, setbacks, heights, areas
   - Preserve legal terminology and structure

${userQuery ? '3.' : '2.'} LEGAL CITATION FORMAT:
   - "Per [Jurisdiction] [Document Type] Chapter X, Section Y: '[exact ordinance text]'"
   - Example: "Per Grand Rapids Zoning Ordinance Chapter 5, Section 5.3.2: 'Accessory buildings shall maintain a minimum setback of five (5) feet from any rear or side property line.'"

${userQuery ? '4.' : '3.'} MODEL CODE HANDLING:
   - Reference proprietary codes by title, edition, section only
   - Example: "See International Building Code 2021 § 1010.1"
   - Summarize requirements without reproducing copyrighted text

4. CONFIDENCE SCORING:
   - Score based on source quality and specificity
   - Deduct points for generic information
   - Require ≥ ${confidenceThreshold}% confidence to proceed

5. CONTACT INFORMATION:
   - Extract specific department names, phone numbers, emails
   - Verify information is current and relevant to ${projectType}

DOCUMENT CONTENT TO ANALYZE:
${documentSources.map(source => `
=== ${source.title} (${source.copyrightStatus.toUpperCase()}) ===
URL: ${source.url}
Citation Strategy: ${source.citationStrategy}

${source.content}

`).join('\n')}

REMEMBER: 
- Only quote verbatim from public domain sources
- Only reference proprietary model codes
- Include exact legal citations with chapter/section numbers
- Provide specific, actionable information
- Maintain ${confidenceThreshold}% confidence threshold

Return only valid JSON - no additional text or formatting.`
}

/**
 * 🔄 Answer Quality Feedback Loop
 * Automatically enhance prompts based on Answer Quality Assessment results
 */
export function generateAdaptivePrompt(
  config: EnhancedPromptConfig & { userQuery?: string },
  qualityFeedback?: {
    specificity: number
    actionability: number
    completeness: number
    directness: number
    improvements: string[]
  }
): string {
  let basePrompt = generateQuestionSpecificPrompt(config)

  // 🎯 Adaptive enhancements based on quality feedback
  if (qualityFeedback) {
    let adaptiveInstructions = '\n🔧 ADAPTIVE QUALITY ENHANCEMENTS:\n'

    // Low specificity - add ultra-aggressive measurement focus
    if (qualityFeedback.specificity < 0.8) {
      adaptiveInstructions += `
🎯 ULTRA-SPECIFICITY BOOST (Current: ${Math.round(qualityFeedback.specificity * 100)}%):
- MANDATORY: Include exact measurements from ordinances (e.g., "6 feet", "8 feet", "10 feet")
- MANDATORY: Quote specific code sections (e.g., "Section 25-2-899", "Article 4.3.2")
- MANDATORY: Use precise numerical language: "exactly 6 feet", "maximum 8 feet", "minimum 3 feet"
- MANDATORY: Reference specific ordinance numbers, not general terms
- MANDATORY: Include exact permit requirements with specific thresholds
- Replace vague terms like "certain heights" with exact measurements like "over 8 feet"`
    }

    // Low actionability - add decision focus
    if (qualityFeedback.actionability < 0.7) {
      adaptiveInstructions += `
🎯 ACTIONABILITY BOOST (Current: ${Math.round(qualityFeedback.actionability * 100)}%):
- Give clear yes/no answers for permission questions
- Provide step-by-step instructions for process questions
- Include specific next steps the user should take
- Make recommendations actionable and concrete`
    }

    // Low completeness - add element validation
    if (qualityFeedback.completeness < 0.7) {
      adaptiveInstructions += `
🎯 COMPLETENESS BOOST (Current: ${Math.round(qualityFeedback.completeness * 100)}%):
- Address EVERY element mentioned in the user's question
- Don't leave any part of the question unanswered
- Cross-reference your response against the original question
- Ensure comprehensive coverage of all topics asked`
    }

    // Low directness - add ultra-direct format focus
    if (qualityFeedback.directness < 0.95) {
      adaptiveInstructions += `
🎯 ULTRA-DIRECTNESS BOOST (Current: ${Math.round(qualityFeedback.directness * 100)}%):
- MANDATORY: Start with "YES" or "NO" as the first word
- MANDATORY: Follow with specific conditions in the same sentence
- MANDATORY: Eliminate ALL vague language: "generally", "typically", "may", "usually"
- MANDATORY: Use definitive statements: "requires", "allows", "prohibits"
- MANDATORY: Structure: YES/NO + Specific Condition → Exact Requirements → Supporting Evidence
- Example: "YES, you can build a 6-foot fence without a permit, but permits are required for fences over 8 feet per Section 25-2-899."`
    }

    basePrompt += adaptiveInstructions
  }

  return basePrompt
}

/**
 * 🔄 Legacy function for backward compatibility
 * Generate enhanced AI prompt for verbatim citation extraction
 */
export function generateEnhancedOrdinancePrompt(config: EnhancedPromptConfig): string {
  return generateQuestionSpecificPrompt(config)
}

/**
 * Generate system prompt for enhanced ordinance analysis
 */
export function generateEnhancedSystemPrompt(): string {
  return `You are an expert municipal ordinance analyst with the following capabilities:

1. LEGAL CITATION EXPERTISE:
   - Extract exact legal language from municipal ordinances
   - Provide precise chapter/section citations
   - Distinguish between public domain and proprietary content

2. COPYRIGHT COMPLIANCE:
   - Quote verbatim from public domain municipal ordinances
   - Reference only (no full text) for proprietary model codes
   - Ensure all citations are legally compliant

3. RESEARCH-LEVEL ACCURACY:
   - No hallucinations or generic responses
   - All information tied to specific, verifiable sources
   - Confidence scoring based on source quality

4. PRACTICAL GUIDANCE:
   - Provide actionable compliance information
   - Include specific measurements and requirements
   - Extract relevant contact information

Your responses must meet research-level standards with verifiable sources and exact legal citations.`
}

/**
 * Generate prompt for model code reference extraction
 */
export function generateModelCodeReferencePrompt(content: string, projectType: string): string {
  return `Extract model code references from the following content for ${projectType} projects.

RULES:
- Only extract actual model code references (IBC, IRC, NEC, NFPA, etc.)
- Include year, chapter, and section when available
- Format as "Code Name Year § Section"
- Do not reproduce copyrighted text

CONTENT:
${content}

Return JSON array of references:
[
  {
    "code_type": "International Building Code 2021",
    "reference": "IBC 2021 § 1010.1",
    "chapter": "10",
    "section": "1010.1",
    "summary": "Brief summary of what this section covers"
  }
]`
}

/**
 * Generate prompt for contact information extraction
 */
export function generateContactExtractionPrompt(content: string, jurisdiction: string): string {
  return `Extract specific contact information for building/planning departments from the following content for ${jurisdiction}.

REQUIREMENTS:
- Extract exact department names
- Include phone numbers with area codes
- Include email addresses if available
- Include website URLs
- Only include information relevant to building permits and zoning

CONTENT:
${content}

Return JSON:
{
  "department": "Exact department name",
  "phone": "Phone number with area code",
  "email": "Email address",
  "website": "Official website URL",
  "address": "Physical address if available",
  "hours": "Office hours if available"
}`
}

/**
 * Validate AI response for compliance with enhanced standards
 */
export function validateEnhancedResponse(response: any, config: EnhancedPromptConfig): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  // Check confidence score
  if (!response.confidence_score || response.confidence_score < config.confidenceThreshold) {
    errors.push(`Confidence score ${response.confidence_score} below threshold ${config.confidenceThreshold}`)
  }

  // Check citations format
  if (response.citations && Array.isArray(response.citations)) {
    for (const citation of response.citations) {
      if (citation.is_public_domain && !citation.exact_text) {
        errors.push('Public domain citation missing exact text')
      }
      // 🔧 RELAXED CITATION VALIDATION: Allow various citation formats
      // Many ordinances use different formats: "Article", "Code", "Ordinance", etc.
      if (!citation.citation_format || citation.citation_format.length < 5) {
        errors.push('Citation format too short or missing')
      }
    }
  }

  // Check for generic responses
  const genericPhrases = [
    'contact local authorities',
    'check with your local',
    'varies by jurisdiction',
    'consult local codes'
  ]

  const responseText = JSON.stringify(response).toLowerCase()
  for (const phrase of genericPhrases) {
    if (responseText.includes(phrase)) {
      warnings.push(`Generic phrase detected: "${phrase}"`)
    }
  }

  // Check source links
  if (!response.source_links || !Array.isArray(response.source_links) || response.source_links.length === 0) {
    errors.push('Missing source links')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}
