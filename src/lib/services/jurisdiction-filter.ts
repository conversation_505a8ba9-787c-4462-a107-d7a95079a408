/**
 * Jurisdiction-Specific Knowledge Filtering Service
 * Ensures only relevant ordinances are used for each jurisdiction
 * Part of Phase 3: RAG Database Enhancement
 */

import { createServerClient } from '@/lib/supabase/server'
import { SupabaseClient, createClient } from '@supabase/supabase-js'

export interface JurisdictionHierarchy {
  id: string
  jurisdiction_name: string
  parent_jurisdiction?: string
  jurisdiction_level: 'federal' | 'state' | 'county' | 'city' | 'township'
  state_code?: string
  county_name?: string
  aliases: string[]
  coverage_area: any
  is_active: boolean
}

export interface JurisdictionFilter {
  id: string
  jurisdiction: string
  filter_type: 'include_parent' | 'exclude_child' | 'alias_match' | 'geographic_overlap'
  filter_value: string
  priority: number
  is_active: boolean
}

export interface FilteredKnowledgeResult {
  knowledge_items: Array<{
    id: number
    jurisdiction: string
    content: string
    confidence_score: number
    source_url: string
    legal_citation?: string
    match_reason: string
  }>
  total_found: number
  filtered_count: number
  jurisdiction_matches: string[]
  filter_applied: string[]
}

export class JurisdictionFilterService {
  private supabase: SupabaseClient
  private hierarchyCache: Map<string, JurisdictionHierarchy> = new Map()
  private filtersCache: Map<string, JurisdictionFilter[]> = new Map()

  constructor(supabase?: SupabaseClient) {
    if (supabase) {
      this.supabase = supabase
    } else {
      // For standalone usage, create a direct client
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing')
      }
      this.supabase = createClient(supabaseUrl, supabaseKey)
    }
    this.loadJurisdictionData()
  }

  /**
   * Load jurisdiction hierarchy and filters into cache
   */
  private async loadJurisdictionData(): Promise<void> {
    try {
      // Load jurisdiction hierarchy
      const { data: hierarchyData, error: hierarchyError } = await this.supabase
        .from('jurisdiction_hierarchy')
        .select('*')
        .eq('is_active', true)

      if (hierarchyError) {
        console.error('Failed to load jurisdiction hierarchy:', hierarchyError)
      } else {
        hierarchyData?.forEach(item => {
          this.hierarchyCache.set(item.jurisdiction_name, item)
          // Also cache by aliases
          item.aliases?.forEach((alias: string) => {
            this.hierarchyCache.set(alias, item)
          })
        })
        console.log(`✅ Loaded ${hierarchyData?.length || 0} jurisdiction hierarchy entries`)
      }

      // Load jurisdiction filters
      const { data: filtersData, error: filtersError } = await this.supabase
        .from('jurisdiction_filters')
        .select('*')
        .eq('is_active', true)
        .order('priority', { ascending: false })

      if (filtersError) {
        console.error('Failed to load jurisdiction filters:', filtersError)
      } else {
        // Group filters by jurisdiction
        const filtersByJurisdiction = new Map<string, JurisdictionFilter[]>()
        filtersData?.forEach(filter => {
          const existing = filtersByJurisdiction.get(filter.jurisdiction) || []
          existing.push(filter)
          filtersByJurisdiction.set(filter.jurisdiction, existing)
        })
        this.filtersCache = filtersByJurisdiction
        console.log(`✅ Loaded ${filtersData?.length || 0} jurisdiction filters`)
      }

    } catch (error) {
      console.error('Error loading jurisdiction data:', error)
    }
  }

  /**
   * Filter compliance knowledge by jurisdiction with hierarchical matching
   */
  async filterKnowledgeByJurisdiction(
    targetJurisdiction: string,
    ruleType?: string,
    minConfidence: number = 0.5
  ): Promise<FilteredKnowledgeResult> {
    try {
      console.log(`🔍 Filtering knowledge for jurisdiction: ${targetJurisdiction}`)

      // Step 1: Resolve jurisdiction and get all applicable jurisdictions
      const applicableJurisdictions = await this.getApplicableJurisdictions(targetJurisdiction)
      
      console.log(`📍 Applicable jurisdictions: ${applicableJurisdictions.join(', ')}`)

      // Step 2: Build query with jurisdiction filtering
      let query = this.supabase
        .from('compliance_knowledge')
        .select(`
          id,
          jurisdiction,
          content_chunk,
          confidence_score,
          source_url,
          legal_citation,
          project_type_tags,
          document_type,
          verification_status,
          research_quality_score
        `)
        .in('jurisdiction', applicableJurisdictions)
        .eq('is_active', true)
        .gte('confidence_score', minConfidence)

      // Add rule type filter if specified
      if (ruleType) {
        query = query.contains('project_type_tags', [ruleType])
      }

      // Order by relevance (exact match first, then by quality)
      query = query.order('research_quality_score', { ascending: false })
        .order('confidence_score', { ascending: false })

      const { data, error } = await query

      if (error) {
        console.error('Knowledge filtering query error:', error)
        return this.emptyResult()
      }

      if (!data || data.length === 0) {
        console.log(`❌ No knowledge found for jurisdictions: ${applicableJurisdictions.join(', ')}`)
        return this.emptyResult()
      }

      // Step 3: Apply additional filtering rules
      const filteredResults = await this.applyFilteringRules(data, targetJurisdiction)

      // Step 4: Rank results by jurisdiction relevance
      const rankedResults = this.rankByJurisdictionRelevance(filteredResults, targetJurisdiction)

      console.log(`✅ Found ${rankedResults.length} relevant knowledge items after filtering`)

      return {
        knowledge_items: rankedResults,
        total_found: data.length,
        filtered_count: rankedResults.length,
        jurisdiction_matches: applicableJurisdictions,
        filter_applied: this.getAppliedFilters(targetJurisdiction)
      }

    } catch (error) {
      console.error('Error filtering knowledge by jurisdiction:', error)
      return this.emptyResult()
    }
  }

  /**
   * Get all applicable jurisdictions for a target jurisdiction (including parent/child relationships)
   */
  private async getApplicableJurisdictions(targetJurisdiction: string): Promise<string[]> {
    const jurisdictions = new Set<string>()
    
    // Add the target jurisdiction itself
    jurisdictions.add(targetJurisdiction)

    // Check if we have hierarchy data for this jurisdiction
    const hierarchyEntry = this.hierarchyCache.get(targetJurisdiction)
    
    if (hierarchyEntry) {
      // Add all aliases
      hierarchyEntry.aliases?.forEach(alias => jurisdictions.add(alias))
      
      // Add parent jurisdiction if applicable
      if (hierarchyEntry.parent_jurisdiction) {
        jurisdictions.add(hierarchyEntry.parent_jurisdiction)
      }

      // For cities, also include county and state
      if (hierarchyEntry.jurisdiction_level === 'city') {
        if (hierarchyEntry.county_name) {
          jurisdictions.add(hierarchyEntry.county_name)
        }
        if (hierarchyEntry.state_code) {
          jurisdictions.add(hierarchyEntry.state_code)
        }
      }
    }

    // Parse jurisdiction string for common patterns
    const jurisdictionParts = this.parseJurisdictionString(targetJurisdiction)
    jurisdictionParts.forEach(part => jurisdictions.add(part))

    return Array.from(jurisdictions)
  }

  /**
   * Parse jurisdiction string to extract city, state, county components
   */
  private parseJurisdictionString(jurisdiction: string): string[] {
    const parts = []
    
    // Handle "City, State" format
    if (jurisdiction.includes(',')) {
      const [city, state] = jurisdiction.split(',').map(s => s.trim())
      parts.push(city)
      if (state) {
        parts.push(state)
        // Add full state name if it's an abbreviation
        const stateMapping = this.getStateMapping()
        if (stateMapping[state]) {
          parts.push(stateMapping[state])
        }
      }
    }

    // Handle "City County" format
    if (jurisdiction.includes(' County')) {
      parts.push(jurisdiction)
      parts.push(jurisdiction.replace(' County', ''))
    }

    // Handle "City Township" format
    if (jurisdiction.includes(' Township')) {
      parts.push(jurisdiction)
      parts.push(jurisdiction.replace(' Township', ''))
    }

    return parts
  }

  /**
   * Apply additional filtering rules based on jurisdiction filters
   */
  private async applyFilteringRules(
    knowledgeItems: any[],
    targetJurisdiction: string
  ): Promise<Array<{
    id: number
    jurisdiction: string
    content: string
    confidence_score: number
    source_url: string
    legal_citation?: string
    match_reason: string
  }>> {
    const filters = this.filtersCache.get(targetJurisdiction) || []
    const results = []

    for (const item of knowledgeItems) {
      let includeItem = true
      let matchReason = 'Direct jurisdiction match'

      // Apply each filter rule
      for (const filter of filters) {
        switch (filter.filter_type) {
          case 'exclude_child':
            if (item.jurisdiction.includes(filter.filter_value)) {
              includeItem = false
            }
            break
          
          case 'include_parent':
            if (item.jurisdiction === filter.filter_value) {
              matchReason = 'Parent jurisdiction included'
            }
            break
          
          case 'alias_match':
            if (item.jurisdiction === filter.filter_value) {
              matchReason = 'Alias match'
            }
            break
          
          case 'geographic_overlap':
            // This would require geographic analysis
            // For now, just include if mentioned in filter_value
            if (filter.filter_value.includes(item.jurisdiction)) {
              matchReason = 'Geographic overlap'
            }
            break
        }
      }

      if (includeItem) {
        results.push({
          id: item.id,
          jurisdiction: item.jurisdiction,
          content: item.content_chunk,
          confidence_score: item.confidence_score,
          source_url: item.source_url || '',
          legal_citation: item.legal_citation,
          match_reason: matchReason
        })
      }
    }

    return results
  }

  /**
   * Rank results by jurisdiction relevance (exact matches first)
   */
  private rankByJurisdictionRelevance(
    results: Array<{
      id: number
      jurisdiction: string
      content: string
      confidence_score: number
      source_url: string
      legal_citation?: string
      match_reason: string
    }>,
    targetJurisdiction: string
  ): Array<{
    id: number
    jurisdiction: string
    content: string
    confidence_score: number
    source_url: string
    legal_citation?: string
    match_reason: string
  }> {
    return results.sort((a, b) => {
      // Exact jurisdiction match gets highest priority
      if (a.jurisdiction === targetJurisdiction && b.jurisdiction !== targetJurisdiction) {
        return -1
      }
      if (b.jurisdiction === targetJurisdiction && a.jurisdiction !== targetJurisdiction) {
        return 1
      }

      // Then by confidence score
      if (a.confidence_score !== b.confidence_score) {
        return b.confidence_score - a.confidence_score
      }

      // Finally by match reason priority
      const reasonPriority = {
        'Direct jurisdiction match': 1,
        'Alias match': 2,
        'Parent jurisdiction included': 3,
        'Geographic overlap': 4
      }

      const aPriority = reasonPriority[a.match_reason as keyof typeof reasonPriority] || 5
      const bPriority = reasonPriority[b.match_reason as keyof typeof reasonPriority] || 5

      return aPriority - bPriority
    })
  }

  /**
   * Add a new jurisdiction to the hierarchy
   */
  async addJurisdiction(jurisdiction: Omit<JurisdictionHierarchy, 'id'>): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('jurisdiction_hierarchy')
        .insert(jurisdiction)

      if (error) {
        console.error('Failed to add jurisdiction:', error)
        return false
      }

      // Update cache
      this.hierarchyCache.set(jurisdiction.jurisdiction_name, { ...jurisdiction, id: 'new' })
      jurisdiction.aliases?.forEach(alias => {
        this.hierarchyCache.set(alias, { ...jurisdiction, id: 'new' })
      })

      console.log(`✅ Added jurisdiction: ${jurisdiction.jurisdiction_name}`)
      return true

    } catch (error) {
      console.error('Error adding jurisdiction:', error)
      return false
    }
  }

  /**
   * Add a new jurisdiction filter rule
   */
  async addFilter(filter: Omit<JurisdictionFilter, 'id'>): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('jurisdiction_filters')
        .insert(filter)

      if (error) {
        console.error('Failed to add filter:', error)
        return false
      }

      // Update cache
      const existing = this.filtersCache.get(filter.jurisdiction) || []
      existing.push({ ...filter, id: 'new' })
      this.filtersCache.set(filter.jurisdiction, existing)

      console.log(`✅ Added filter for jurisdiction: ${filter.jurisdiction}`)
      return true

    } catch (error) {
      console.error('Error adding filter:', error)
      return false
    }
  }

  /**
   * Get applied filters for a jurisdiction
   */
  private getAppliedFilters(jurisdiction: string): string[] {
    const filters = this.filtersCache.get(jurisdiction) || []
    return filters.map(f => `${f.filter_type}: ${f.filter_value}`)
  }

  /**
   * Get state abbreviation to full name mapping
   */
  private getStateMapping(): Record<string, string> {
    return {
      'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas', 'CA': 'California',
      'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware', 'FL': 'Florida', 'GA': 'Georgia',
      'HI': 'Hawaii', 'ID': 'Idaho', 'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa',
      'KS': 'Kansas', 'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
      'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi', 'MO': 'Missouri',
      'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada', 'NH': 'New Hampshire', 'NJ': 'New Jersey',
      'NM': 'New Mexico', 'NY': 'New York', 'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio',
      'OK': 'Oklahoma', 'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
      'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah', 'VT': 'Vermont',
      'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia', 'WI': 'Wisconsin', 'WY': 'Wyoming'
    }
  }

  /**
   * Return empty result structure
   */
  private emptyResult(): FilteredKnowledgeResult {
    return {
      knowledge_items: [],
      total_found: 0,
      filtered_count: 0,
      jurisdiction_matches: [],
      filter_applied: []
    }
  }

  /**
   * Get jurisdiction filtering statistics
   */
  async getFilteringStats(): Promise<{
    total_jurisdictions: number
    active_filters: number
    coverage_by_level: Record<string, number>
  }> {
    try {
      const { data: hierarchyData } = await this.supabase
        .from('jurisdiction_hierarchy')
        .select('jurisdiction_level')
        .eq('is_active', true)

      const { data: filtersData } = await this.supabase
        .from('jurisdiction_filters')
        .select('id')
        .eq('is_active', true)

      const coverageByLevel = hierarchyData?.reduce((acc, item) => {
        acc[item.jurisdiction_level] = (acc[item.jurisdiction_level] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {}

      return {
        total_jurisdictions: hierarchyData?.length || 0,
        active_filters: filtersData?.length || 0,
        coverage_by_level: coverageByLevel
      }

    } catch (error) {
      console.error('Error getting filtering stats:', error)
      return {
        total_jurisdictions: 0,
        active_filters: 0,
        coverage_by_level: {}
      }
    }
  }
}
