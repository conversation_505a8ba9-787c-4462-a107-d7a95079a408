/**
 * Jurisdiction lookup service
 * Resolves jurisdiction from coordinates using Geoapify and Google APIs
 */

export interface JurisdictionResult {
  name: string
  level: 'township' | 'city' | 'town' | 'village' | 'county' | 'district'
  state: string
  state_code: string
}

/**
 * Get jurisdiction from coordinates using Geoapify reverse geocoding
 */
export async function getJurisdictionFromGeoapify(lat: number, lng: number): Promise<JurisdictionResult | null> {
  try {
    // Try Geoapify reverse geocoding first (since we know it works)
    if (process.env.GEOAPIFY_API_KEY) {
      console.log(`🌍 Geoapify Reverse Geocoding: ${lat}, ${lng}`)
      const response = await fetch(
        `https://api.geoapify.com/v1/geocode/reverse?lat=${lat}&lon=${lng}&apiKey=${process.env.GEOAPIFY_API_KEY}`
      )

      if (response.ok) {
        const data = await response.json()
        console.log(`📊 Geoapify returned ${data.features?.length || 0} results`)

        if (data.features && data.features.length > 0) {
          const feature = data.features[0]
          const props = feature.properties

          console.log(`📍 Geoapify result:`, JSON.stringify(props, null, 2))

          // Extract jurisdiction from Geoapify response with township support
          let jurisdiction = null
          let level: JurisdictionResult['level'] = 'city'

          // Priority order: township > city > town > village > county
          // Special handling for Michigan townships and other jurisdictions
          if (props.district && props.district.toLowerCase().includes('township')) {
            jurisdiction = props.district
            level = 'township'
          } else if (props.district && props.county && props.district !== props.city) {
            // Check if district might be a township (common in Michigan)
            // Georgetown Township is a common case where district="Georgetown" but it's actually Georgetown Township
            if (props.state_code === 'MI' && props.district && !props.district.toLowerCase().includes('township')) {
              jurisdiction = `${props.district} Township`
              level = 'township'
            } else {
              jurisdiction = props.district
              level = 'district'
            }
          } else if (props.city) {
            jurisdiction = props.city
            level = 'city'
          } else if (props.town) {
            jurisdiction = props.town
            level = 'town'
          } else if (props.village) {
            jurisdiction = props.village
            level = 'village'
          } else if (props.county) {
            jurisdiction = props.county
            level = 'county'
          }

          const state = props.state
          const stateCode = props.state_code

          if (jurisdiction && state) {
            console.log(`✅ Geoapify found jurisdiction: ${jurisdiction}, ${state} (${level})`)
            return {
              name: jurisdiction,
              level: level,
              state: state,
              state_code: stateCode
            }
          }
        }
      }
    }

    // Fallback to Google if Geoapify fails
    return await getJurisdictionFromGoogle(lat, lng)

  } catch (error) {
    console.error('Jurisdiction lookup failed:', error)
    return null
  }
}

/**
 * Get jurisdiction from coordinates using Google reverse geocoding (fallback)
 */
async function getJurisdictionFromGoogle(lat: number, lng: number): Promise<JurisdictionResult | null> {
  try {
    const googleGeocodingKey = process.env.GOOGLE_GEOCODING_API_KEY || process.env.GOOGLE_SEARCH_API_KEY
    if (!googleGeocodingKey) {
      console.error('❌ No geocoding APIs available')
      return null
    }

    console.log(`🌍 Trying Google Reverse Geocoding: ${lat}, ${lng}`)
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${googleGeocodingKey}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.status === 'REQUEST_DENIED') {
      console.error(`❌ Google Geocoding API not enabled: ${data.error_message}`)
      return null
    }

    if (data.results && data.results.length > 0) {
      const result = data.results[0]
      console.log(`📍 Google formatted address: ${result.formatted_address}`)

      // Simple extraction from Google
      let city = null
      let state = null
      let stateCode = null

      for (const component of result.address_components) {
        if (component.types.includes('locality') || component.types.includes('administrative_area_level_3')) {
          city = component.long_name
        } else if (component.types.includes('administrative_area_level_1')) {
          state = component.long_name
          stateCode = component.short_name
        }
      }

      if (city && state) {
        console.log(`✅ Google found jurisdiction: ${city}, ${state}`)
        return {
          name: city,
          level: 'city',
          state: state,
          state_code: stateCode
        }
      }
    }

    return null
  } catch (error) {
    console.error('Google jurisdiction lookup failed:', error)
    return null
  }
}

/**
 * Get coordinates from address using Geoapify geocoding
 */
export async function getCoordinatesFromAddress(address: string): Promise<{ lat: number; lng: number } | null> {
  try {
    // Use Geoapify for geocoding (same as address autocomplete)
    if (!process.env.GEOAPIFY_API_KEY) {
      throw new Error('Geoapify API key not configured')
    }

    const response = await fetch(
      `https://api.geoapify.com/v1/geocode/search?text=${encodeURIComponent(address)}&apiKey=${process.env.GEOAPIFY_API_KEY}`
    )

    if (!response.ok) {
      throw new Error(`Geoapify error: ${response.status}`)
    }

    const data = await response.json()

    if (data.features && data.features.length > 0) {
      const feature = data.features[0]
      return {
        lat: feature.geometry.coordinates[1],
        lng: feature.geometry.coordinates[0]
      }
    }

    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}
