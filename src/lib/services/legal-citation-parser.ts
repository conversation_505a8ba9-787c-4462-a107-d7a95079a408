/**
 * Legal Citation Parser
 * Extracts exact legal language with proper chapter/section citations
 * Handles both public domain ordinances and proprietary model code references
 */

import { ContentClassification } from './content-classification'

export interface LegalCitation {
  jurisdiction: string
  documentType: string
  chapter?: string
  section?: string
  subsection?: string
  exactText: string
  citationFormat: string // "Per [Jurisdiction] [Document] Chapter X, Section Y: [text]"
  sourceUrl: string
  isPublicDomain: boolean
  citationStrategy: 'verbatim_quote' | 'reference_only'
}

export interface OrdinanceStructure {
  title?: string
  chapters: Array<{
    number: string
    title?: string
    sections: Array<{
      number: string
      title?: string
      subsections?: Array<{
        number: string
        title?: string
        content: string
      }>
      content: string
    }>
  }>
}

export interface CitationExtractionResult {
  citations: LegalCitation[]
  ordinanceStructure?: OrdinanceStructure
  confidence: number
  extractedRegulations: string[]
  modelCodeReferences: string[]
}

/**
 * Extract jurisdiction name from content or URL
 */
export function extractJurisdiction(content: string, sourceUrl: string): string {
  const url = sourceUrl.toLowerCase()
  const text = content.toLowerCase()

  // Try to extract from URL first
  const urlPatterns = [
    { pattern: /grandrapidsmi\.gov/, jurisdiction: 'Grand Rapids, MI' },
    { pattern: /miottawa\.org/, jurisdiction: 'Ottawa County, MI' },
    { pattern: /kentcountymi\.gov/, jurisdiction: 'Kent County, MI' },
    { pattern: /cityofholland\.com/, jurisdiction: 'Holland, MI' },
    { pattern: /(\w+)\.gov/, jurisdiction: '$1' }, // Generic .gov pattern
  ]

  for (const { pattern, jurisdiction } of urlPatterns) {
    if (pattern.test(url)) {
      return jurisdiction.replace('$1', pattern.exec(url)?.[1] || 'Unknown')
    }
  }

  // Try to extract from content
  const contentPatterns = [
    /city of ([^,\n]+)/i,
    /county of ([^,\n]+)/i,
    /township of ([^,\n]+)/i,
    /village of ([^,\n]+)/i,
    /([^,\n]+) county/i,
    /([^,\n]+) city/i
  ]

  for (const pattern of contentPatterns) {
    const match = pattern.exec(content)
    if (match) {
      return match[1].trim()
    }
  }

  return 'Unknown Jurisdiction'
}

/**
 * Extract document type from content
 */
export function extractDocumentType(content: string): string {
  const patterns = [
    { pattern: /zoning ordinance/i, type: 'Zoning Ordinance' },
    { pattern: /building code/i, type: 'Building Code' },
    { pattern: /municipal code/i, type: 'Municipal Code' },
    { pattern: /city code/i, type: 'City Code' },
    { pattern: /county code/i, type: 'County Code' },
    { pattern: /planning guide/i, type: 'Planning Guide' },
    { pattern: /building permit/i, type: 'Building Permit Guide' }
  ]

  for (const { pattern, type } of patterns) {
    if (pattern.test(content)) {
      return type
    }
  }

  return 'Municipal Document'
}

/**
 * Parse ordinance structure to identify chapters, sections, and subsections
 */
export function parseOrdinanceStructure(content: string): OrdinanceStructure {
  const structure: OrdinanceStructure = { chapters: [] }

  // Extract title
  const titleMatch = content.match(/^([^\\n]+(?:ordinance|code|guide)[^\\n]*)/i)
  if (titleMatch) {
    structure.title = titleMatch[1].trim()
  }

  // Find chapters
  const chapterPattern = /chapter\s+(\d+(?:\.\d+)?)\s*[-–—]?\s*([^\\n]*)/gi
  let chapterMatch

  while ((chapterMatch = chapterPattern.exec(content)) !== null) {
    const chapterNumber = chapterMatch[1]
    const chapterTitle = chapterMatch[2].trim()

    // Find sections within this chapter
    const chapterStart = chapterMatch.index
    const nextChapterMatch = chapterPattern.exec(content)
    const chapterEnd = nextChapterMatch ? nextChapterMatch.index : content.length
    chapterPattern.lastIndex = chapterStart + chapterMatch[0].length // Reset for next iteration

    const chapterContent = content.slice(chapterStart, chapterEnd)
    const sections = parseSections(chapterContent)

    structure.chapters.push({
      number: chapterNumber,
      title: chapterTitle || undefined,
      sections
    })
  }

  return structure
}

/**
 * Parse sections within a chapter
 */
function parseSections(chapterContent: string): OrdinanceStructure['chapters'][0]['sections'] {
  const sections: OrdinanceStructure['chapters'][0]['sections'] = []
  const sectionPattern = /section\s+(\d+(?:\.\d+)?)\s*[-–—]?\s*([^\\n]*)/gi
  let sectionMatch

  while ((sectionMatch = sectionPattern.exec(chapterContent)) !== null) {
    const sectionNumber = sectionMatch[1]
    const sectionTitle = sectionMatch[2].trim()

    // Find content for this section
    const sectionStart = sectionMatch.index
    const nextSectionMatch = sectionPattern.exec(chapterContent)
    const sectionEnd = nextSectionMatch ? nextSectionMatch.index : chapterContent.length
    sectionPattern.lastIndex = sectionStart + sectionMatch[0].length

    const sectionContent = chapterContent.slice(sectionStart, sectionEnd)
    const cleanContent = sectionContent.replace(/^section\s+\d+(?:\.\d+)?\s*[-–—]?\s*[^\\n]*/i, '').trim()

    sections.push({
      number: sectionNumber,
      title: sectionTitle || undefined,
      content: cleanContent
    })
  }

  return sections
}

/**
 * Extract specific legal citations from content
 */
export function extractLegalCitations(
  content: string,
  sourceUrl: string,
  classification: ContentClassification
): LegalCitation[] {
  const citations: LegalCitation[] = []
  const jurisdiction = extractJurisdiction(content, sourceUrl)
  const documentType = extractDocumentType(content)

  if (classification.isPublicDomain && classification.citationStrategy === 'verbatim_quote') {
    // Extract public domain ordinance citations
    const ordinanceStructure = parseOrdinanceStructure(content)

    for (const chapter of ordinanceStructure.chapters) {
      for (const section of chapter.sections) {
        if (section.content && section.content.length > 50) {
          // Extract meaningful regulations from this section
          const regulations = extractRegulations(section.content)

          for (const regulation of regulations) {
            const citation: LegalCitation = {
              jurisdiction,
              documentType,
              chapter: chapter.number,
              section: section.number,
              exactText: regulation,
              citationFormat: `Per ${jurisdiction} ${documentType} Chapter ${chapter.number}, Section ${section.number}: "${regulation}"`,
              sourceUrl,
              isPublicDomain: true,
              citationStrategy: 'verbatim_quote'
            }
            citations.push(citation)
          }
        }
      }
    }
  } else if (classification.citationStrategy === 'reference_only') {
    // Extract model code references
    const modelCodeRefs = extractModelCodeReferences(content)

    for (const ref of modelCodeRefs) {
      const citation: LegalCitation = {
        jurisdiction: ref.publisher || 'ICC/NFPA',
        documentType: ref.codeType,
        chapter: ref.chapter,
        section: ref.section,
        exactText: `See ${ref.fullReference} for specific requirements.`,
        citationFormat: `See ${ref.fullReference}`,
        sourceUrl,
        isPublicDomain: false,
        citationStrategy: 'reference_only'
      }
      citations.push(citation)
    }
  }

  return citations
}

/**
 * Extract meaningful regulations from section content
 */
function extractRegulations(sectionContent: string): string[] {
  const regulations: string[] = []

  // Split by common regulation patterns
  const regulationPatterns = [
    /\([a-z]\)\s*([^\\n]+(?:\\n(?!\s*\([a-z]\))[^\\n]*)*)/gi, // (a) pattern
    /\d+\.\s*([^\\n]+(?:\\n(?!\s*\d+\.)[^\\n]*)*)/gi, // 1. pattern
    /(?:shall|must|required|prohibited)[^.!?]*[.!?]/gi // Regulatory language
  ]

  for (const pattern of regulationPatterns) {
    let match
    while ((match = pattern.exec(sectionContent)) !== null) {
      const regulation = match[1] || match[0]
      if (regulation && regulation.length > 20 && regulation.length < 500) {
        regulations.push(regulation.trim())
      }
    }
  }

  // If no specific patterns found, extract sentences with regulatory language
  if (regulations.length === 0) {
    const sentences = sectionContent.split(/[.!?]+/)
    for (const sentence of sentences) {
      if (/\b(shall|must|required|minimum|maximum|prohibited|permitted)\b/i.test(sentence) && 
          sentence.length > 20 && sentence.length < 500) {
        regulations.push(sentence.trim())
      }
    }
  }

  return regulations.slice(0, 3) // Limit to most relevant regulations
}

/**
 * Extract model code references
 */
function extractModelCodeReferences(content: string): Array<{
  codeType: string
  chapter?: string
  section?: string
  fullReference: string
  publisher?: string
}> {
  const references: Array<{
    codeType: string
    chapter?: string
    section?: string
    fullReference: string
    publisher?: string
  }> = []

  const patterns = [
    {
      pattern: /\b(IBC|International Building Code)\s*(\d{4})?\s*(?:Chapter\s*(\d+))?\s*(?:Section\s*(\d+(?:\.\d+)?))?/gi,
      codeType: 'International Building Code',
      publisher: 'ICC'
    },
    {
      pattern: /\b(IRC|International Residential Code)\s*(\d{4})?\s*(?:Chapter\s*(\d+))?\s*(?:Section\s*(\d+(?:\.\d+)?))?/gi,
      codeType: 'International Residential Code',
      publisher: 'ICC'
    },
    {
      pattern: /\b(NEC|National Electrical Code|NFPA\s*70)\s*(\d{4})?\s*(?:Article\s*(\d+))?\s*(?:Section\s*(\d+(?:\.\d+)?))?/gi,
      codeType: 'National Electrical Code',
      publisher: 'NFPA'
    }
  ]

  for (const { pattern, codeType, publisher } of patterns) {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const year = match[2] || '2021'
      const chapter = match[3]
      const section = match[4]

      let fullReference = `${codeType} ${year}`
      if (chapter) fullReference += ` Chapter ${chapter}`
      if (section) fullReference += ` Section ${section}`

      references.push({
        codeType: `${codeType} ${year}`,
        chapter,
        section,
        fullReference,
        publisher
      })
    }
  }

  return references
}
