/**
 * 🏛️ MUNICIPAL RESEARCH API CLIENT
 * 
 * HTTP client for calling the standalone municipal-research-api
 * - Handles API key authentication
 * - Transforms responses for Ordrly UI components
 * - Provides error handling and retry logic
 * - Maintains compatibility with existing chat/search interfaces
 */

import axios, { AxiosInstance, AxiosError } from 'axios'

export interface MunicipalResearchRequest {
  address: string
  query: string
  metadata?: {
    userAgent?: string
    referrer?: string
  }
}

export interface MunicipalResearchResponse {
  success: boolean
  data: {
    jurisdiction: string
    topic: string
    answer: string
    sources: string[]
    confidence: number
    cached: boolean
    processingTimeMs: number
    method: 'perplexity-gemini' | 'cache'
  }
  meta: {
    requestId: string
    timestamp: string
    apiVersion: string
    cacheAge?: string
  }
}

export interface MunicipalBulkRequest {
  requests: MunicipalResearchRequest[]
}

export interface MunicipalBulkResponse {
  success: boolean
  data: {
    results: Array<{
      index: number
      success: boolean
      data?: MunicipalResearchResponse['data']
      error?: {
        message: string
        code: string
      }
    }>
    summary: {
      total: number
      successful: number
      failed: number
      totalCost: number
    }
  }
  meta: {
    requestId: string
    timestamp: string
    apiVersion: string
  }
}

export interface MunicipalTopicsResponse {
  success: boolean
  data: Array<{
    topic_key: string
    display_name: string
    description: string
    category: string
    usage_count: number
  }>
  meta: {
    timestamp: string
    count: number
  }
}

export interface MunicipalCacheStatsResponse {
  success: boolean
  data: {
    totalEntries: number
    hitRate: number
    totalHits: number
    totalCost: number
    avgConfidence: number
    topTopics: Array<{
      topic: string
      count: number
      hitRate: number
    }>
  }
  meta: {
    timestamp: string
  }
}

export class MunicipalResearchClient {
  private client: AxiosInstance
  private apiKey: string
  private baseURL: string

  constructor(apiKey: string, baseURL: string = 'http://localhost:3001') {
    this.apiKey = apiKey
    this.baseURL = baseURL
    
    this.client = axios.create({
      baseURL,
      timeout: 60000, // 60 seconds for research requests
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
      },
    })

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🔍 Municipal API Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('🚨 Municipal API Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`✅ Municipal API Response: ${response.status} ${response.config.url}`)
        return response
      },
      (error: AxiosError) => {
        console.error('🚨 Municipal API Error:', {
          status: error.response?.status,
          message: error.message,
          url: error.config?.url,
          data: error.response?.data
        })
        return Promise.reject(this.transformError(error))
      }
    )
  }

  /**
   * Perform municipal research
   */
  async research(request: MunicipalResearchRequest): Promise<MunicipalResearchResponse> {
    try {
      const response = await this.client.post<MunicipalResearchResponse>(
        '/api/v1/research',
        request
      )
      return response.data
    } catch (error) {
      throw this.handleResearchError(error, request)
    }
  }

  /**
   * Perform bulk municipal research
   */
  async bulkResearch(request: MunicipalBulkRequest): Promise<MunicipalBulkResponse> {
    try {
      const response = await this.client.post<MunicipalBulkResponse>(
        '/api/v1/research/bulk',
        request
      )
      return response.data
    } catch (error) {
      throw this.handleResearchError(error, request)
    }
  }

  /**
   * Get available research topics
   */
  async getTopics(): Promise<MunicipalTopicsResponse> {
    try {
      const response = await this.client.get<MunicipalTopicsResponse>('/api/v1/research/topics')
      return response.data
    } catch (error) {
      throw this.handleResearchError(error, {})
    }
  }

  /**
   * Get cache statistics (admin only)
   */
  async getCacheStats(): Promise<MunicipalCacheStatsResponse> {
    try {
      const response = await this.client.get<MunicipalCacheStatsResponse>('/api/v1/admin/cache/stats')
      return response.data
    } catch (error) {
      throw this.handleResearchError(error, {})
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: string; timestamp: string; version: string; environment: string }> {
    try {
      const response = await this.client.get('/health')
      return response.data
    } catch (error) {
      throw this.handleResearchError(error, {})
    }
  }

  /**
   * Transform axios error to standardized error
   */
  private transformError(error: AxiosError): Error {
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      const data = error.response.data as any
      
      if (status === 401) {
        return new Error('Municipal API authentication failed - invalid API key')
      } else if (status === 403) {
        return new Error('Municipal API access forbidden - insufficient permissions')
      } else if (status === 429) {
        return new Error('Municipal API rate limit exceeded - please try again later')
      } else if (status >= 500) {
        return new Error(`Municipal API server error: ${data?.message || error.message}`)
      } else {
        return new Error(`Municipal API error: ${data?.message || error.message}`)
      }
    } else if (error.request) {
      // Network error
      return new Error('Municipal API is unreachable - please check if the service is running')
    } else {
      // Request setup error
      return new Error(`Municipal API request error: ${error.message}`)
    }
  }

  /**
   * Handle research-specific errors with context
   */
  private handleResearchError(error: any, request: any): Error {
    const baseError = error instanceof Error ? error : this.transformError(error)
    
    // Add context for research requests
    if (request.address && request.query) {
      return new Error(`Research failed for "${request.query}" at ${request.address}: ${baseError.message}`)
    } else if (request.requests) {
      return new Error(`Bulk research failed for ${request.requests.length} requests: ${baseError.message}`)
    }
    
    return baseError
  }
}

/**
 * Factory function to create municipal research client
 */
export function createMunicipalResearchClient(apiKey: string, baseURL?: string): MunicipalResearchClient {
  return new MunicipalResearchClient(apiKey, baseURL)
}

/**
 * Default client instance (will be configured with environment variables)
 */
export const municipalResearchClient = createMunicipalResearchClient(
  process.env.MUNICIPAL_API_KEY || 'your-api-key-here',
  process.env.MUNICIPAL_API_URL || 'http://localhost:3001'
)
