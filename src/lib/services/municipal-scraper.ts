import { SupabaseClient, createClient } from '@supabase/supabase-js'
import { createServerClient } from '@/lib/supabase/server'

/**
 * 🏗️ MUNICIPAL SCRAPING SERVICE
 * 
 * Intelligent municipal website scraping with:
 * - Source discovery and verification
 * - Content extraction and parsing
 * - Change detection and monitoring
 * - Intelligent scheduling
 */

export interface MunicipalSource {
  id?: string
  jurisdiction: string
  sourceType: 'municode' | 'city_website' | 'pdf' | 'planning_docs'
  baseUrl: string
  selectors?: Record<string, string>
  sections?: string[]
  priority: 'primary' | 'secondary' | 'tertiary'
  scrapingFrequency: 'daily' | 'weekly' | 'monthly'
  lastScrapedAt?: string
  nextScrapeAt?: string
  successRate?: number
  lastError?: string
}

export interface ScrapedContent {
  title: string
  content: string
  url: string
  section?: string
  legalCitation?: string
  lastUpdated: string
  contentHash: string
  authority: 'primary' | 'secondary' | 'tertiary'
  verified: boolean
}

export class MunicipalScrapingService {
  private supabase: SupabaseClient

  constructor(supabase?: SupabaseClient) {
    if (supabase) {
      this.supabase = supabase
    } else {
      // For standalone usage, create a direct client
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing')
      }
      this.supabase = createClient(supabaseUrl, supabaseKey)
    }
  }

  /**
   * 🔍 Source Discovery
   * Discover and register municipal sources for a jurisdiction
   */
  async discoverSources(jurisdiction: string): Promise<MunicipalSource[]> {
    console.log(`🔍 [DISCOVERY] Discovering sources for ${jurisdiction}`)

    const sources: MunicipalSource[] = []

    // Parse jurisdiction to get city and state
    const [city, state] = jurisdiction.split(', ')
    
    if (!city || !state) {
      throw new Error('Invalid jurisdiction format. Expected "City, State"')
    }

    // 1. Municode Sources (Primary)
    const municodeUrl = await this.findMunicodeUrl(city, state)
    if (municodeUrl) {
      sources.push({
        jurisdiction,
        sourceType: 'municode',
        baseUrl: municodeUrl,
        priority: 'primary',
        scrapingFrequency: 'weekly',
        sections: ['zoning', 'building', 'planning', 'development']
      })
    }

    // 2. City Website (Secondary)
    const cityWebsiteUrl = await this.findCityWebsite(city, state)
    if (cityWebsiteUrl) {
      sources.push({
        jurisdiction,
        sourceType: 'city_website',
        baseUrl: cityWebsiteUrl,
        priority: 'secondary',
        scrapingFrequency: 'weekly',
        selectors: {
          ordinanceContent: '.ordinance-content, .regulation-text, .code-section',
          title: 'h1, h2, .title, .section-title',
          lastUpdated: '.last-modified, .updated, .date'
        }
      })
    }

    // 3. Planning Documents (Tertiary)
    const planningDocsUrl = await this.findPlanningDocs(city, state)
    if (planningDocsUrl) {
      sources.push({
        jurisdiction,
        sourceType: 'planning_docs',
        baseUrl: planningDocsUrl,
        priority: 'tertiary',
        scrapingFrequency: 'monthly'
      })
    }

    // Store discovered sources in database
    for (const source of sources) {
      await this.registerSource(source)
    }

    console.log(`✅ [DISCOVERY] Found ${sources.length} sources for ${jurisdiction}`)
    return sources
  }

  /**
   * 🕷️ Content Extraction
   * Extract content from municipal sources
   */
  async extractContent(source: MunicipalSource): Promise<ScrapedContent[]> {
    console.log(`🕷️ [EXTRACTION] Extracting content from ${source.baseUrl}`)

    try {
      switch (source.sourceType) {
        case 'municode':
          return await this.extractFromMunicode(source)
        case 'city_website':
          return await this.extractFromWebsite(source)
        case 'pdf':
          return await this.extractFromPDF(source)
        case 'planning_docs':
          return await this.extractFromPlanningDocs(source)
        default:
          throw new Error(`Unsupported source type: ${source.sourceType}`)
      }
    } catch (error) {
      console.error(`❌ [EXTRACTION] Error extracting from ${source.baseUrl}:`, error)
      
      // Update source with error information
      await this.updateSourceStatus(source.id!, false, error instanceof Error ? error.message : 'Unknown error')
      
      return []
    }
  }

  /**
   * 📊 Intelligent Scheduling
   * Get sources that are due for scraping based on priority and schedule
   */
  async getSourcesDueForScraping(limit: number = 10): Promise<MunicipalSource[]> {
    try {
      const { data, error } = await this.supabase
        .rpc('get_sources_due_for_scraping', { p_limit: limit })

      if (error) {
        console.error('❌ [SCHEDULER] Error getting sources due for scraping:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('❌ [SCHEDULER] Error:', error)
      return []
    }
  }

  /**
   * 🔄 Change Detection
   * Detect changes in scraped content
   */
  async detectChanges(newContent: ScrapedContent[], sourceId: string): Promise<boolean> {
    try {
      let hasChanges = false

      for (const content of newContent) {
        // Check if content hash has changed
        const { data: existingContent } = await this.supabase
          .from('compliance_knowledge')
          .select('content_hash')
          .eq('source_id', sourceId)
          .eq('source_url', content.url)
          .single()

        if (!existingContent || existingContent.content_hash !== content.contentHash) {
          hasChanges = true
          
          // Log the change
          await this.supabase
            .from('content_change_log')
            .insert({
              source_id: sourceId,
              content_hash: content.contentHash,
              previous_hash: existingContent?.content_hash || null,
              change_type: existingContent ? 'updated' : 'new',
              change_summary: `Content updated for ${content.title}`
            })
        }
      }

      return hasChanges
    } catch (error) {
      console.error('❌ [CHANGE_DETECTION] Error:', error)
      return false
    }
  }

  /**
   * 🏗️ Private Helper Methods
   */

  private async findMunicodeUrl(city: string, state: string): Promise<string | null> {
    // This would implement actual Municode URL discovery
    // For now, return a placeholder pattern
    const citySlug = city.toLowerCase().replace(/\s+/g, '_')
    const stateSlug = state.toLowerCase()
    return `https://library.municode.com/${stateSlug}/${citySlug}/codes/code_of_ordinances`
  }

  private async findCityWebsite(city: string, state: string): Promise<string | null> {
    // This would implement actual city website discovery
    // Common patterns: cityname.gov, cityofcityname.com, etc.
    const citySlug = city.toLowerCase().replace(/\s+/g, '')
    return `https://www.${citySlug}.gov`
  }

  private async findPlanningDocs(city: string, state: string): Promise<string | null> {
    // This would search for planning department documents
    return null // Placeholder
  }

  private async registerSource(source: MunicipalSource): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('municipal_sources')
        .upsert({
          jurisdiction: source.jurisdiction,
          source_type: source.sourceType,
          base_url: source.baseUrl,
          selectors: source.selectors || {},
          sections: source.sections || [],
          priority: source.priority,
          scraping_frequency: source.scrapingFrequency,
          next_scrape_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
        }, {
          onConflict: 'jurisdiction,source_type,base_url'
        })

      if (error) {
        console.error('❌ [REGISTER] Error registering source:', error)
      }
    } catch (error) {
      console.error('❌ [REGISTER] Error:', error)
    }
  }

  private async updateSourceStatus(sourceId: string, success: boolean, errorMessage?: string): Promise<void> {
    try {
      await this.supabase
        .rpc('update_scraping_schedule', {
          p_source_id: sourceId,
          p_success: success,
          p_error_message: errorMessage || null
        })
    } catch (error) {
      console.error('❌ [UPDATE_STATUS] Error:', error)
    }
  }

  private async extractFromMunicode(source: MunicipalSource): Promise<ScrapedContent[]> {
    // Placeholder for Municode extraction
    console.log(`🏛️ [MUNICODE] Extracting from ${source.baseUrl}`)
    
    // This would implement actual Municode scraping
    return [
      {
        title: `${source.jurisdiction} Municipal Code`,
        content: 'Placeholder content from Municode',
        url: source.baseUrl,
        section: 'Chapter 10',
        legalCitation: 'Municipal Code Chapter 10, Section 5.2',
        lastUpdated: new Date().toISOString(),
        contentHash: 'placeholder-hash',
        authority: 'primary',
        verified: true
      }
    ]
  }

  private async extractFromWebsite(source: MunicipalSource): Promise<ScrapedContent[]> {
    // Placeholder for website extraction
    console.log(`🌐 [WEBSITE] Extracting from ${source.baseUrl}`)
    
    // This would implement actual website scraping using Playwright or similar
    return []
  }

  private async extractFromPDF(source: MunicipalSource): Promise<ScrapedContent[]> {
    // Placeholder for PDF extraction
    console.log(`📄 [PDF] Extracting from ${source.baseUrl}`)
    
    // This would implement PDF parsing using pdf-parse or similar
    return []
  }

  private async extractFromPlanningDocs(source: MunicipalSource): Promise<ScrapedContent[]> {
    // Placeholder for planning documents extraction
    console.log(`📋 [PLANNING] Extracting from ${source.baseUrl}`)
    
    return []
  }
}
