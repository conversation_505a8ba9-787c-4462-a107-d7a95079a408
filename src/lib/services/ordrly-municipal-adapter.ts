/**
 * 🔄 ORDRLY MUNICIPAL RESEARCH ADAPTER
 * 
 * Adapts municipal-research-api responses to work with existing Ordrly components:
 * - Transforms municipal API responses to Ordrly format
 * - Handles authentication with your existing system
 * - Integrates with your chat UI and search components
 * - Maintains compatibility with existing database schema
 */

import { createServerClient } from '@/lib/supabase/server'
import { municipalResearchClient, MunicipalResearchRequest, MunicipalResearchResponse } from './municipal-research-client'

// Ordrly-compatible interfaces (matching your existing system)
export interface OrdrlyResearchRequest {
  query: string
  address: string
  lat?: number
  lng?: number
  jurisdiction?: string
  conversationContext?: {
    sessionId?: string
    isFollowUp: boolean
    intent: string
    needsFreshData: boolean
    confidenceThreshold: number
  }
  apiKey?: string
  userId?: string
}

export interface OrdrlyResearchResult {
  success: boolean
  jurisdiction: string
  answer: string
  sources: Array<{
    url: string
    title?: string
    authority?: 'primary' | 'secondary'
    verified?: boolean
  }>
  confidence: number
  cached: boolean
  processingTimeMs: number
  costUsd?: number
  method: string
  topic?: string
  usedCache?: boolean
  triggeredResearch?: boolean
}

export class OrdrlyMunicipalAdapter {
  private municipalApiKey: string

  constructor(municipalApiKey: string) {
    this.municipalApiKey = municipalApiKey
  }

  /**
   * Main research method that adapts municipal API for Ordrly
   */
  async performResearch(request: OrdrlyResearchRequest): Promise<OrdrlyResearchResult> {
    try {
      console.log(`🔬 Ordrly Municipal Research: "${request.query}" for ${request.address}`)

      // Transform Ordrly request to Municipal API format
      const municipalRequest: MunicipalResearchRequest = {
        address: request.address,
        query: request.query,
        metadata: {
          userAgent: 'Ordrly-System/1.0',
          referrer: 'ordrly.ai'
        }
      }

      // Call municipal research API
      const municipalResponse = await municipalResearchClient.research(municipalRequest)

      // Transform municipal response to Ordrly format
      const ordrlyResult = this.transformMunicipalToOrdrly(municipalResponse, request)

      // Log the research for your system (optional)
      await this.logResearchActivity(request, ordrlyResult)

      console.log(`✅ Municipal research completed: ${ordrlyResult.confidence} confidence, ${ordrlyResult.sources.length} sources`)

      return ordrlyResult

    } catch (error) {
      console.error('🚨 Municipal research failed:', error)
      throw new Error(`Municipal research failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Transform municipal API response to Ordrly format
   */
  private transformMunicipalToOrdrly(
    municipalResponse: MunicipalResearchResponse,
    originalRequest: OrdrlyResearchRequest
  ): OrdrlyResearchResult {
    const { data, meta } = municipalResponse

    // Get sources from municipal API (now working properly)
    const sources = data.sources || []

    // Transform sources to Ordrly format
    const transformedSources = sources.map(url => ({
      url,
      title: this.extractTitleFromUrl(url),
      authority: url.includes('.gov') ? 'primary' as const : 'secondary' as const,
      verified: url.includes('.gov') || url.includes('municipal') || url.includes('city')
    }))

    return {
      success: true,
      jurisdiction: data.jurisdiction,
      answer: data.answer,
      sources: transformedSources,
      confidence: data.confidence,
      cached: data.cached,
      processingTimeMs: data.processingTimeMs,
      costUsd: 0, // Municipal API handles costs internally
      method: data.method,
      topic: data.topic,
      usedCache: data.cached,
      triggeredResearch: !data.cached
    }
  }

  /**
   * Extract a readable title from URL
   */
  private extractTitleFromUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      const hostname = urlObj.hostname.replace('www.', '')
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0)
      
      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1]
        const title = lastPart.replace(/[-_]/g, ' ').replace(/\.(html|php|aspx?)$/i, '')
        return `${hostname} - ${title}`
      }
      
      return hostname
    } catch {
      return url
    }
  }

  /**
   * Log research activity in your Ordrly database (optional)
   * Only works when called within a Next.js request context
   */
  private async logResearchActivity(
    request: OrdrlyResearchRequest,
    result: OrdrlyResearchResult
  ): Promise<void> {
    try {
      // Skip logging if not in a request context (e.g., during testing)
      if (typeof window !== 'undefined' || !process.env.SUPABASE_URL) {
        console.log('📊 Skipping research activity logging (not in server request context)')
        return
      }

      const supabase = await createServerClient()

      // Log to your existing research_quality_metrics table
      await supabase
        .from('research_quality_metrics')
        .insert({
          session_id: request.conversationContext?.sessionId || null,
          query_hash: this.hashQuery(request.query),
          jurisdiction: result.jurisdiction,
          user_query: request.query,
          query_intent: this.detectQueryIntent(request.query),
          final_confidence: result.confidence,
          sources_checked: result.sources.length,
          primary_sources: result.sources.filter(s => s.authority === 'primary').length,
          verified_sources: result.sources.filter(s => s.verified).length,
          processing_time_ms: result.processingTimeMs,
          used_municipal_api: true,
          municipal_cached: result.cached,
          municipal_topic: result.topic,
          created_at: new Date().toISOString()
        })
    } catch (error) {
      console.warn('Failed to log research activity:', error)
      // Don't throw - logging failure shouldn't break research
    }
  }

  /**
   * Simple query hash for tracking
   */
  private hashQuery(query: string): string {
    let hash = 0
    for (let i = 0; i < query.length; i++) {
      const char = query.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Detect query intent (basic implementation)
   */
  private detectQueryIntent(query: string): string {
    const lowerQuery = query.toLowerCase()
    
    if (lowerQuery.includes('permit') || lowerQuery.includes('application')) {
      return 'permit_requirements'
    } else if (lowerQuery.includes('setback') || lowerQuery.includes('distance')) {
      return 'setback_requirements'
    } else if (lowerQuery.includes('height') || lowerQuery.includes('size')) {
      return 'dimensional_requirements'
    } else if (lowerQuery.includes('material') || lowerQuery.includes('construction')) {
      return 'material_requirements'
    } else if (lowerQuery.includes('fence') || lowerQuery.includes('wall')) {
      return 'fence_regulations'
    } else if (lowerQuery.includes('parking') || lowerQuery.includes('driveway')) {
      return 'parking_requirements'
    } else {
      return 'general_compliance'
    }
  }

  /**
   * Health check for municipal API
   */
  async healthCheck(): Promise<{ status: string; municipalApi: any }> {
    try {
      const health = await municipalResearchClient.healthCheck()
      return {
        status: 'healthy',
        municipalApi: health
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        municipalApi: { error: error instanceof Error ? error.message : String(error) }
      }
    }
  }

  /**
   * Get available research topics from municipal API
   */
  async getAvailableTopics() {
    try {
      return await municipalResearchClient.getTopics()
    } catch (error) {
      console.error('Failed to get municipal topics:', error)
      return { success: false, data: [], meta: { timestamp: new Date().toISOString(), count: 0 } }
    }
  }
}

/**
 * Factory function to create adapter with municipal API key
 */
export function createOrdrlyMunicipalAdapter(municipalApiKey: string): OrdrlyMunicipalAdapter {
  return new OrdrlyMunicipalAdapter(municipalApiKey)
}

/**
 * Default adapter instance
 */
export const ordrlyMunicipalAdapter = createOrdrlyMunicipalAdapter(
  process.env.MUNICIPAL_API_KEY || 'your-municipal-api-key-here'
)
