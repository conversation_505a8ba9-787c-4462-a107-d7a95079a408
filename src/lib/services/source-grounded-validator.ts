/**
 * Source-Grounded Response Validator
 * Ensures 100% accuracy by validating that AI responses are grounded in actual source content
 * Prevents hallucinations and ensures every claim is backed by verifiable sources
 */

export interface SourceContent {
  title: string;
  url: string;
  content: string;
  snippet?: string;
  validation: {
    copyrightStatus: 'public_domain' | 'proprietary' | 'unknown';
    usageAllowed: boolean;
    contentQuality: number;
  };
}

export interface GroundedClaim {
  claim: string;
  sourceText: string;
  sourceTitle: string;
  sourceUrl: string;
  confidence: number;
  isGrounded: boolean;
}

export interface SourceGroundingResult {
  isFullyGrounded: boolean;
  groundingScore: number; // 0-1, percentage of claims backed by sources
  groundedClaims: GroundedClaim[];
  ungroundedClaims: string[];
  sourceUtilization: number; // 0-1, percentage of sources actually used
  recommendations: string[];
}

export class SourceGroundedValidator {
  /**
   * Validate that AI response is fully grounded in provided sources
   */
  validateResponseGrounding(
    aiResponse: string,
    sources: SourceContent[],
    userQuery: string
  ): SourceGroundingResult {
    console.log(`🔍 [SOURCE GROUNDING] Validating response grounding for query: "${userQuery}"`);
    console.log(`📚 [SOURCE GROUNDING] Available sources: ${sources.length}`);

    // Extract claims from AI response
    const claims = this.extractClaims(aiResponse);
    console.log(`📝 [SOURCE GROUNDING] Extracted ${claims.length} claims from response`);

    // Validate each claim against sources
    const groundedClaims: GroundedClaim[] = [];
    const ungroundedClaims: string[] = [];

    for (const claim of claims) {
      const grounding = this.findSourceGrounding(claim, sources);
      if (grounding) {
        groundedClaims.push(grounding);
      } else {
        ungroundedClaims.push(claim);
      }
    }

    // Calculate metrics
    const groundingScore = claims.length > 0 ? groundedClaims.length / claims.length : 0;
    const isFullyGrounded = ungroundedClaims.length === 0 && groundedClaims.length > 0;
    
    // Calculate source utilization
    const usedSources = new Set(groundedClaims.map(gc => gc.sourceUrl));
    const sourceUtilization = sources.length > 0 ? usedSources.size / sources.length : 0;

    // Generate recommendations
    const recommendations = this.generateRecommendations(
      groundingScore,
      sourceUtilization,
      ungroundedClaims,
      sources
    );

    console.log(`✅ [SOURCE GROUNDING] Grounding score: ${Math.round(groundingScore * 100)}%`);
    console.log(`📊 [SOURCE GROUNDING] Source utilization: ${Math.round(sourceUtilization * 100)}%`);
    console.log(`🚫 [SOURCE GROUNDING] Ungrounded claims: ${ungroundedClaims.length}`);

    return {
      isFullyGrounded,
      groundingScore,
      groundedClaims,
      ungroundedClaims,
      sourceUtilization,
      recommendations
    };
  }

  /**
   * Extract factual claims from AI response
   */
  private extractClaims(response: string): string[] {
    const claims: string[] = [];
    
    // Split into sentences and filter for factual claims
    const sentences = response.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 10);
    
    for (const sentence of sentences) {
      // Skip meta-statements and focus on factual claims
      if (this.isFactualClaim(sentence)) {
        claims.push(sentence);
      }
    }

    return claims;
  }

  /**
   * Determine if a sentence contains a factual claim that needs source grounding
   */
  private isFactualClaim(sentence: string): boolean {
    const lowerSentence = sentence.toLowerCase();
    
    // Skip meta-statements
    const metaPatterns = [
      'based on the ordinance',
      'according to the document',
      'the ordinance states',
      'i recommend',
      'you should consult',
      'please note',
      'it\'s important to'
    ];
    
    if (metaPatterns.some(pattern => lowerSentence.includes(pattern))) {
      return false;
    }

    // Include factual claims about regulations
    const factualPatterns = [
      'feet',
      'inches',
      'height',
      'setback',
      'allowed',
      'prohibited',
      'required',
      'must',
      'shall',
      'may not',
      'cannot',
      'minimum',
      'maximum',
      'chapter',
      'section',
      'ordinance'
    ];

    return factualPatterns.some(pattern => lowerSentence.includes(pattern));
  }

  /**
   * Find source grounding for a specific claim
   */
  private findSourceGrounding(claim: string, sources: SourceContent[]): GroundedClaim | null {
    let bestMatch: GroundedClaim | null = null;
    let highestConfidence = 0;

    for (const source of sources) {
      const confidence = this.calculateGroundingConfidence(claim, source.content);
      
      if (confidence > 0.6 && confidence > highestConfidence) {
        // Find the specific text that supports the claim
        const supportingText = this.findSupportingText(claim, source.content);
        
        if (supportingText) {
          bestMatch = {
            claim,
            sourceText: supportingText,
            sourceTitle: source.title,
            sourceUrl: source.url,
            confidence,
            isGrounded: true
          };
          highestConfidence = confidence;
        }
      }
    }

    return bestMatch;
  }

  /**
   * Calculate confidence that a claim is grounded in source content
   */
  private calculateGroundingConfidence(claim: string, sourceContent: string): number {
    const claimLower = claim.toLowerCase();
    const sourceLower = sourceContent.toLowerCase();
    
    // Extract key terms from claim
    const keyTerms = this.extractKeyTerms(claimLower);
    
    // Calculate term overlap
    let matchedTerms = 0;
    for (const term of keyTerms) {
      if (sourceLower.includes(term)) {
        matchedTerms++;
      }
    }
    
    const termOverlap = keyTerms.length > 0 ? matchedTerms / keyTerms.length : 0;
    
    // Boost confidence for exact phrase matches
    let exactMatchBonus = 0;
    const claimPhrases = claimLower.split(/[,;]/).map(p => p.trim());
    for (const phrase of claimPhrases) {
      if (phrase.length > 10 && sourceLower.includes(phrase)) {
        exactMatchBonus += 0.3;
      }
    }
    
    return Math.min(termOverlap + exactMatchBonus, 1.0);
  }

  /**
   * Extract key terms from a claim for matching
   */
  private extractKeyTerms(claim: string): string[] {
    // Remove common words and extract meaningful terms
    const commonWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall'
    ]);
    
    const words = claim.split(/\s+/).map(w => w.replace(/[^\w]/g, '').toLowerCase());
    return words.filter(w => w.length > 2 && !commonWords.has(w));
  }

  /**
   * Find the specific text in source that supports the claim
   */
  private findSupportingText(claim: string, sourceContent: string): string | null {
    const claimLower = claim.toLowerCase();
    const sentences = sourceContent.split(/[.!?]+/).map(s => s.trim());
    
    let bestMatch = '';
    let highestScore = 0;
    
    for (const sentence of sentences) {
      if (sentence.length < 10) continue;
      
      const score = this.calculateGroundingConfidence(claim, sentence);
      if (score > highestScore && score > 0.6) {
        bestMatch = sentence;
        highestScore = score;
      }
    }
    
    return bestMatch || null;
  }

  /**
   * Generate recommendations for improving source grounding
   */
  private generateRecommendations(
    groundingScore: number,
    sourceUtilization: number,
    ungroundedClaims: string[],
    sources: SourceContent[]
  ): string[] {
    const recommendations: string[] = [];
    
    if (groundingScore < 0.8) {
      recommendations.push(`Improve source grounding: ${Math.round((1 - groundingScore) * 100)}% of claims lack source support`);
    }
    
    if (sourceUtilization < 0.5) {
      recommendations.push(`Utilize more sources: Only ${Math.round(sourceUtilization * 100)}% of available sources were used`);
    }
    
    if (ungroundedClaims.length > 0) {
      recommendations.push(`Remove ungrounded claims: ${ungroundedClaims.length} claims cannot be verified from sources`);
    }
    
    if (sources.length === 0) {
      recommendations.push('No sources available: Cannot validate any claims without source documents');
    }
    
    return recommendations;
  }

  /**
   * Generate enhanced prompt that enforces source grounding
   */
  generateSourceGroundedPrompt(
    userQuery: string,
    sources: SourceContent[],
    basePrompt: string
  ): string {
    const sourceList = sources.map((source, index) => 
      `SOURCE ${index + 1}: ${source.title}\nURL: ${source.url}\nCONTENT: ${source.content.substring(0, 1000)}...\n`
    ).join('\n');

    return `${basePrompt}

🎯 CRITICAL SOURCE GROUNDING REQUIREMENTS:
You MUST base your response ONLY on the provided source documents. Every factual claim must be directly supported by source content.

USER QUESTION: ${userQuery}

AVAILABLE SOURCES:
${sourceList}

MANDATORY RESPONSE RULES:
1. ONLY use information explicitly stated in the provided sources
2. For every factual claim, reference the specific source (e.g., "According to [Source Title]...")
3. If information is not in the sources, state "This information is not available in the provided sources"
4. Quote exact text from sources when making specific claims about measurements, requirements, etc.
5. Do not make assumptions or provide general knowledge not found in sources

VALIDATION CHECKLIST:
- Can every factual claim be traced to a specific source?
- Are all measurements/requirements quoted directly from source text?
- Have you avoided adding information not present in the sources?
- Did you cite the specific source for each claim?

If the sources don't contain enough information to fully answer the question, clearly state what information is missing and what additional sources would be needed.`;
  }
}

export const sourceGroundedValidator = new SourceGroundedValidator();
