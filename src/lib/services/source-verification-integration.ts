/**
 * Source Verification Integration Service
 * Integrates Phase 1 & 2 source classification and citation extraction with RAG database storage
 * Ensures only verified, linkable sources are stored and used
 */

import { createServerClient } from '@/lib/supabase/server'
import { SupabaseClient, createClient } from '@supabase/supabase-js'

export interface SourceVerificationResult {
  source_id: number
  url: string
  title: string
  content: string
  classification: {
    content_type: 'municipal_ordinance' | 'model_code' | 'government_document' | 'commentary' | 'proprietary'
    copyright_status: 'public_domain' | 'proprietary' | 'model_code' | 'unknown'
    confidence: number
    reasoning: string
  }
  citation: {
    legal_citation?: string
    chapter?: string
    section?: string
    verbatim_allowed: boolean
  }
  verification: {
    url_accessible: boolean
    content_valid: boolean
    citation_accurate: boolean
    overall_verified: boolean
    verification_attempts: number
    last_verified_at: string
  }
  quality_scores: {
    accessibility_score: number
    accuracy_score: number
    completeness_score: number
    freshness_score: number
    authority_score: number
    overall_quality_score: number
  }
}

export interface VerificationBatch {
  batch_id: string
  total_sources: number
  verified_sources: number
  failed_sources: number
  processing_time_ms: number
  results: SourceVerificationResult[]
}

export class SourceVerificationIntegrationService {
  private supabase: SupabaseClient

  constructor(supabase?: SupabaseClient) {
    if (supabase) {
      this.supabase = supabase
    } else {
      // For standalone usage, create a direct client
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing')
      }
      this.supabase = createClient(supabaseUrl, supabaseKey)
    }
  }

  /**
   * Verify and integrate a batch of sources with Phase 1 & 2 processing
   */
  async verifyAndIntegrateSources(
    sources: Array<{
      url: string
      title: string
      content: string
      jurisdiction: string
      rule_type: string
    }>
  ): Promise<VerificationBatch> {
    const startTime = Date.now()
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    console.log(`🔍 Starting source verification batch ${batchId} with ${sources.length} sources`)

    const results: SourceVerificationResult[] = []
    let verifiedCount = 0
    let failedCount = 0

    for (const source of sources) {
      try {
        const result = await this.verifyAndProcessSingleSource(source)
        results.push(result)

        if (result.verification.overall_verified) {
          verifiedCount++
          // Store verified source in RAG database
          await this.storeVerifiedSourceInRAG(source, result)
        } else {
          failedCount++
        }

      } catch (error) {
        console.error(`Error processing source ${source.url}:`, error)
        failedCount++
      }
    }

    const batch: VerificationBatch = {
      batch_id: batchId,
      total_sources: sources.length,
      verified_sources: verifiedCount,
      failed_sources: failedCount,
      processing_time_ms: Date.now() - startTime,
      results
    }

    console.log(`✅ Batch ${batchId} completed: ${verifiedCount}/${sources.length} sources verified`)
    return batch
  }

  /**
   * Verify and process a single source through Phase 1 & 2
   */
  private async verifyAndProcessSingleSource(
    source: {
      url: string
      title: string
      content: string
      jurisdiction: string
      rule_type: string
    }
  ): Promise<SourceVerificationResult> {
    // Phase 1: Source Classification
    const classification = await this.classifySource(source.url, source.title, source.content)
    
    // Phase 2: Citation Extraction
    const citation = await this.extractCitation(source.content, source.jurisdiction, classification)
    
    // Verification: URL and Content Validation
    const verification = await this.verifySource(source.url, source.content, citation)
    
    // Quality Scoring
    const qualityScores = await this.calculateQualityScores(source, classification, verification)

    return {
      source_id: 0, // Will be set when stored in database
      url: source.url,
      title: source.title,
      content: source.content,
      classification,
      citation,
      verification,
      quality_scores: qualityScores
    }
  }

  /**
   * Phase 1: Classify source content and copyright status
   */
  private async classifySource(
    url: string,
    title: string,
    content: string
  ): Promise<{
    content_type: 'municipal_ordinance' | 'model_code' | 'government_document' | 'commentary' | 'proprietary'
    copyright_status: 'public_domain' | 'proprietary' | 'model_code' | 'unknown'
    confidence: number
    reasoning: string
  }> {
    try {
      // Government domain indicators
      const isGovDomain = url.includes('.gov') || url.includes('municipal') || url.includes('city.')
      
      // Model code indicators
      const modelCodeIndicators = ['IBC', 'IRC', 'NEC', 'NFPA', 'International Building Code', 'International Residential Code']
      const hasModelCodeIndicators = modelCodeIndicators.some(indicator => 
        title.includes(indicator) || content.includes(indicator)
      )
      
      // Legislative language indicators
      const legislativeIndicators = ['be it ordained', 'whereas', 'section', 'chapter', 'ordinance', 'municipal code']
      const hasLegislativeLanguage = legislativeIndicators.some(indicator => 
        content.toLowerCase().includes(indicator.toLowerCase())
      )

      let contentType: 'municipal_ordinance' | 'model_code' | 'government_document' | 'commentary' | 'proprietary'
      let copyrightStatus: 'public_domain' | 'proprietary' | 'model_code' | 'unknown'
      let confidence = 0.5
      let reasoning = ''

      if (hasModelCodeIndicators && !isGovDomain) {
        contentType = 'model_code'
        copyrightStatus = 'model_code'
        confidence = 0.90
        reasoning = 'Contains model code indicators without government domain'
      } else if (isGovDomain && hasLegislativeLanguage) {
        contentType = 'municipal_ordinance'
        copyrightStatus = 'public_domain'
        confidence = 0.95
        reasoning = 'Government domain with legislative language'
      } else if (isGovDomain) {
        contentType = 'government_document'
        copyrightStatus = 'public_domain'
        confidence = 0.85
        reasoning = 'Government domain'
      } else {
        contentType = 'commentary'
        copyrightStatus = 'proprietary'
        confidence = 0.70
        reasoning = 'Non-government source, likely commentary'
      }

      return {
        content_type: contentType,
        copyright_status: copyrightStatus,
        confidence,
        reasoning
      }

    } catch (error) {
      console.error('Error in source classification:', error)
      return {
        content_type: 'commentary',
        copyright_status: 'unknown',
        confidence: 0.0,
        reasoning: 'Classification failed'
      }
    }
  }

  /**
   * Phase 2: Extract legal citations with proper formatting
   */
  private async extractCitation(
    content: string,
    jurisdiction: string,
    classification: any
  ): Promise<{
    legal_citation?: string
    chapter?: string
    section?: string
    verbatim_allowed: boolean
  }> {
    try {
      // Extract chapter and section patterns
      const chapterMatch = content.match(/chapter\s+(\d+(?:\.\d+)*)/i)
      const sectionMatch = content.match(/section\s+(\d+(?:\.\d+)*)/i)
      
      const chapter = chapterMatch ? chapterMatch[1] : undefined
      const section = sectionMatch ? sectionMatch[1] : undefined

      // Build legal citation based on classification
      let legalCitation: string | undefined
      let verbatimAllowed = false

      if (classification.copyright_status === 'public_domain') {
        verbatimAllowed = true
        if (chapter && section) {
          legalCitation = `Per ${jurisdiction} Zoning Ordinance Chapter ${chapter}, Section ${section}`
        } else if (section) {
          legalCitation = `Per ${jurisdiction} Municipal Code Section ${section}`
        } else {
          legalCitation = `Per ${jurisdiction} Municipal Ordinance`
        }
      } else if (classification.copyright_status === 'model_code') {
        verbatimAllowed = false
        if (chapter && section) {
          legalCitation = `See ${classification.content_type} Chapter ${chapter}, Section ${section} (reference only)`
        }
      }

      return {
        legal_citation: legalCitation,
        chapter,
        section,
        verbatim_allowed: verbatimAllowed
      }

    } catch (error) {
      console.error('Error in citation extraction:', error)
      return {
        verbatim_allowed: false
      }
    }
  }

  /**
   * Verify source URL accessibility and content validity
   */
  private async verifySource(
    url: string,
    content: string,
    citation: any
  ): Promise<{
    url_accessible: boolean
    content_valid: boolean
    citation_accurate: boolean
    overall_verified: boolean
    verification_attempts: number
    last_verified_at: string
  }> {
    const verificationAttempts = 1
    const lastVerifiedAt = new Date().toISOString()

    try {
      // URL accessibility check
      const urlAccessible = await this.checkURLAccessibility(url)
      
      // Content validity check
      const contentValid = this.validateContent(content)
      
      // Citation accuracy check
      const citationAccurate = this.validateCitation(citation, content)
      
      const overallVerified = urlAccessible && contentValid && citationAccurate

      return {
        url_accessible: urlAccessible,
        content_valid: contentValid,
        citation_accurate: citationAccurate,
        overall_verified: overallVerified,
        verification_attempts: verificationAttempts,
        last_verified_at: lastVerifiedAt
      }

    } catch (error) {
      console.error('Error in source verification:', error)
      return {
        url_accessible: false,
        content_valid: false,
        citation_accurate: false,
        overall_verified: false,
        verification_attempts: verificationAttempts,
        last_verified_at: lastVerifiedAt
      }
    }
  }

  /**
   * Check if URL is accessible
   */
  private async checkURLAccessibility(url: string): Promise<boolean> {
    try {
      if (!url || url.length < 10) {
        return false
      }

      // Basic URL format validation
      const urlPattern = /^https?:\/\/.+\..+/
      if (!urlPattern.test(url)) {
        return false
      }

      // In production, this would make an actual HTTP request
      // For now, assume government domains are accessible
      return url.includes('.gov') || url.includes('municipal') || url.includes('city.')

    } catch (error) {
      console.error('Error checking URL accessibility:', error)
      return false
    }
  }

  /**
   * Validate content quality and completeness
   */
  private validateContent(content: string): boolean {
    if (!content || content.length < 50) {
      return false
    }

    // Check for meaningful content (not just boilerplate)
    const meaningfulWords = ['ordinance', 'section', 'chapter', 'regulation', 'code', 'requirement']
    const hasMeaningfulContent = meaningfulWords.some(word => 
      content.toLowerCase().includes(word)
    )

    return hasMeaningfulContent
  }

  /**
   * Validate citation accuracy against content
   */
  private validateCitation(citation: any, content: string): boolean {
    if (!citation.legal_citation) {
      return true // No citation to validate
    }

    // Check if cited chapter/section exists in content
    if (citation.chapter) {
      const chapterExists = content.toLowerCase().includes(`chapter ${citation.chapter}`)
      if (!chapterExists) {
        return false
      }
    }

    if (citation.section) {
      const sectionExists = content.toLowerCase().includes(`section ${citation.section}`)
      if (!sectionExists) {
        return false
      }
    }

    return true
  }

  /**
   * Calculate quality scores for the source
   */
  private async calculateQualityScores(
    source: any,
    classification: any,
    verification: any
  ): Promise<{
    accessibility_score: number
    accuracy_score: number
    completeness_score: number
    freshness_score: number
    authority_score: number
    overall_quality_score: number
  }> {
    // Accessibility score (can the source be accessed?)
    const accessibilityScore = verification.url_accessible ? 1.0 : 0.0

    // Accuracy score (is the citation accurate?)
    const accuracyScore = verification.citation_accurate ? 1.0 : 0.5

    // Completeness score (does it contain full legal text?)
    const completenessScore = verification.content_valid ? 0.9 : 0.3

    // Freshness score (how recent is the information?)
    const freshnessScore = 0.8 // Default for now, would check last modified date

    // Authority score (is it from an official source?)
    const authorityScore = classification.copyright_status === 'public_domain' ? 1.0 : 0.5

    // Overall quality score (weighted average)
    const overallQualityScore = (
      accessibilityScore * 0.3 +
      accuracyScore * 0.25 +
      completenessScore * 0.2 +
      freshnessScore * 0.1 +
      authorityScore * 0.15
    )

    return {
      accessibility_score: accessibilityScore,
      accuracy_score: accuracyScore,
      completeness_score: completenessScore,
      freshness_score: freshnessScore,
      authority_score: authorityScore,
      overall_quality_score: overallQualityScore
    }
  }

  /**
   * Store verified source in RAG database with all metadata
   */
  private async storeVerifiedSourceInRAG(
    source: {
      url: string
      title: string
      content: string
      jurisdiction: string
      rule_type: string
    },
    result: SourceVerificationResult
  ): Promise<void> {
    try {
      // Only store if verification passed and meets quality threshold
      if (!result.verification.overall_verified || result.quality_scores.overall_quality_score < 0.7) {
        console.log(`⚠️ Skipping storage of low-quality source: ${source.url}`)
        return
      }

      const ragEntry = {
        source_document_id: `verified_${Date.now()}_${source.jurisdiction.replace(/[^a-zA-Z0-9]/g, '_')}_${source.rule_type}`,
        document_type: 'ordinance',
        jurisdiction: source.jurisdiction,
        project_type_tags: [source.rule_type],
        title: source.title,
        content_chunk: result.citation.verbatim_allowed ? source.content : `Reference: ${source.title}`,
        chunk_sequence: 1,
        embedding: null, // TODO: Generate embedding
        metadata: {
          verification_batch: true,
          classification: result.classification,
          verification_details: result.verification,
          quality_scores: result.quality_scores,
          processed_at: new Date().toISOString()
        },
        source_url: source.url,
        last_updated_at: new Date().toISOString(),
        copyright_status: result.classification.copyright_status,
        confidence_score: result.classification.confidence,
        legal_citation: result.citation.legal_citation,
        verification_status: 'verified',
        source_classification: result.classification.content_type,
        research_quality_score: result.quality_scores.overall_quality_score,
        last_verified_at: result.verification.last_verified_at,
        verification_attempts: result.verification.verification_attempts
      }

      const { data, error } = await this.supabase
        .from('compliance_knowledge')
        .insert(ragEntry)
        .select('id')
        .single()

      if (error) {
        console.error('Failed to store verified source in RAG:', error)
        return
      }

      // Store quality metrics
      await this.storeQualityMetrics(data.id, result.quality_scores)

      // Log verification
      await this.logVerification(data.id, result.verification)

      console.log(`✅ Stored verified source in RAG: ${source.title}`)

    } catch (error) {
      console.error('Error storing verified source:', error)
    }
  }

  /**
   * Store quality metrics for a source
   */
  private async storeQualityMetrics(knowledgeId: number, qualityScores: any): Promise<void> {
    try {
      await this.supabase
        .from('source_quality_metrics')
        .insert({
          compliance_knowledge_id: knowledgeId,
          accessibility_score: qualityScores.accessibility_score,
          accuracy_score: qualityScores.accuracy_score,
          completeness_score: qualityScores.completeness_score,
          freshness_score: qualityScores.freshness_score,
          authority_score: qualityScores.authority_score,
          overall_quality_score: qualityScores.overall_quality_score,
          calculation_details: qualityScores
        })
    } catch (error) {
      console.error('Error storing quality metrics:', error)
    }
  }

  /**
   * Log verification results
   */
  private async logVerification(knowledgeId: number, verification: any): Promise<void> {
    try {
      const verificationTypes = [
        { type: 'url_check', result: verification.url_accessible ? 'pass' : 'fail' },
        { type: 'content_validation', result: verification.content_valid ? 'pass' : 'fail' },
        { type: 'citation_accuracy', result: verification.citation_accurate ? 'pass' : 'fail' }
      ]

      for (const verif of verificationTypes) {
        await this.supabase
          .from('source_verification_log')
          .insert({
            compliance_knowledge_id: knowledgeId,
            verification_type: verif.type,
            verification_result: verif.result,
            verification_details: verification,
            verified_by: 'system'
          })
      }
    } catch (error) {
      console.error('Error logging verification:', error)
    }
  }

  /**
   * Get verification statistics
   */
  async getVerificationStats(): Promise<{
    total_verified: number
    verification_rate: number
    average_quality_score: number
    sources_by_type: Record<string, number>
  }> {
    try {
      const { data, error } = await this.supabase
        .from('compliance_knowledge')
        .select('verification_status, source_classification, research_quality_score')
        .not('verification_status', 'is', null)

      if (error) {
        console.error('Error getting verification stats:', error)
        return { total_verified: 0, verification_rate: 0, average_quality_score: 0, sources_by_type: {} }
      }

      const totalSources = data.length
      const verifiedSources = data.filter(s => s.verification_status === 'verified').length
      const averageQuality = data.reduce((sum, s) => sum + (s.research_quality_score || 0), 0) / totalSources

      const sourcesByType = data.reduce((acc, s) => {
        acc[s.source_classification] = (acc[s.source_classification] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      return {
        total_verified: verifiedSources,
        verification_rate: totalSources > 0 ? verifiedSources / totalSources : 0,
        average_quality_score: averageQuality,
        sources_by_type: sourcesByType
      }

    } catch (error) {
      console.error('Error calculating verification stats:', error)
      return { total_verified: 0, verification_rate: 0, average_quality_score: 0, sources_by_type: {} }
    }
  }
}
