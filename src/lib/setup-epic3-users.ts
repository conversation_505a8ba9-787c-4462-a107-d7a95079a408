import { createClient } from '@supabase/supabase-js'

export interface Epic3TestUser {
  email: string
  password: string
  name: string
  subscription_tier: 'free' | 'pro' | 'appraiser'
}

export const epic3TestUsers: Epic3TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Free User',
    subscription_tier: 'free'
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Pro User',
    subscription_tier: 'pro'
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test Appraiser User',
    subscription_tier: 'appraiser'
  }
]

export async function setupEpic3TestUsers() {
  try {
    // Use service role key for admin operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
    const results = []

    for (const user of epic3TestUsers) {
      try {
        console.log(`Creating Epic 3 test user: ${user.email}`)

        // Sign up the user
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: user.email,
          password: user.password,
          email_confirm: true, // Auto-confirm email for test users
          user_metadata: {
            name: user.name
          }
        })

        if (authError) {
          console.error(`Failed to create auth user ${user.email}:`, authError)
          results.push({
            email: user.email,
            success: false,
            error: authError.message
          })
          continue
        }

        if (!authData.user) {
          console.error(`No user data returned for ${user.email}`)
          results.push({
            email: user.email,
            success: false,
            error: 'No user data returned'
          })
          continue
        }

        // Create/update profile with subscription tier
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({
            id: authData.user.id,
            email: user.email,
            name: user.name,
            subscription_tier: user.subscription_tier,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (profileError) {
          console.error(`Failed to create profile for ${user.email}:`, profileError)
          results.push({
            email: user.email,
            success: false,
            error: profileError.message
          })
          continue
        }

        console.log(`✅ Successfully created Epic 3 test user: ${user.email} (${user.subscription_tier})`)
        results.push({
          email: user.email,
          success: true,
          tier: user.subscription_tier
        })

      } catch (error) {
        console.error(`Error creating user ${user.email}:`, error)
        results.push({
          email: user.email,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const totalCount = results.length

    return {
      success: successCount === totalCount,
      results,
      summary: `${successCount}/${totalCount} Epic 3 test users created successfully`
    }

  } catch (error) {
    console.error('Error in setupEpic3TestUsers:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function verifyEpic3TestUsers() {
  try {
    // Use service role key for admin operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
    const results = []

    for (const user of epic3TestUsers) {
      try {
        // Check if user exists in auth
        const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()

        if (authError) {
          console.error('Failed to list auth users:', authError)
          continue
        }

        const authUser = authUsers.users.find(u => u.email === user.email)

        if (!authUser) {
          results.push({
            email: user.email,
            exists: false,
            error: 'User not found in auth'
          })
          continue
        }

        // Check if profile exists
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('email', user.email)
          .single()

        if (profileError || !profile) {
          results.push({
            email: user.email,
            exists: true,
            hasProfile: false,
            error: 'Profile not found'
          })
          continue
        }

        results.push({
          email: user.email,
          exists: true,
          hasProfile: true,
          subscription_tier: profile.subscription_tier,
          matches_expected: profile.subscription_tier === user.subscription_tier
        })

      } catch (error) {
        console.error(`Error verifying user ${user.email}:`, error)
        results.push({
          email: user.email,
          exists: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    const validUsers = results.filter(r => r.exists && r.hasProfile && r.matches_expected).length
    const totalUsers = results.length

    return {
      success: validUsers === totalUsers,
      results,
      summary: `${validUsers}/${totalUsers} Epic 3 test users verified successfully`
    }

  } catch (error) {
    console.error('Error in verifyEpic3TestUsers:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
