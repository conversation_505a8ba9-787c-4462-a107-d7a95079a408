// Setup script for Epic 5 test users
import { createServerClient } from '@/lib/supabase/server'

export interface TestUser {
  email: string
  password: string
  subscription_tier: 'business' | 'enterprise' | 'admin'
  role: 'business' | 'enterprise' | 'admin'
  full_name: string
}

export const testUsers: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'business123',
    subscription_tier: 'business',
    role: 'business',
    full_name: 'Business Test User'
  },
  {
    email: '<EMAIL>',
    password: 'enterprise123',
    subscription_tier: 'enterprise',
    role: 'enterprise',
    full_name: 'Enterprise Test User'
  },
  {
    email: '<EMAIL>',
    password: 'admin123',
    subscription_tier: 'business',
    role: 'admin',
    full_name: 'Admin Test User'
  }
]

export async function setupEpic5TestUsers() {
  try {
    const supabase = await createServerClient()
    
    console.log('Setting up Epic 5 test users...')
    
    for (const user of testUsers) {
      console.log(`Creating user: ${user.email}`)
      
      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('id, email')
        .eq('email', user.email)
        .single()
      
      if (existingUser) {
        console.log(`User ${user.email} already exists, updating profile...`)
        
        // Update existing user profile
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            subscription_tier: user.subscription_tier,
            role: user.role,
            full_name: user.full_name,
            updated_at: new Date().toISOString()
          })
          .eq('email', user.email)
        
        if (updateError) {
          console.error(`Error updating user ${user.email}:`, updateError)
        } else {
          console.log(`✅ Updated user ${user.email}`)
        }
      } else {
        // Create new user via Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email: user.email,
          password: user.password,
          options: {
            data: {
              full_name: user.full_name,
              subscription_tier: user.subscription_tier,
              role: user.role
            }
          }
        })
        
        if (authError) {
          console.error(`Error creating auth user ${user.email}:`, authError)
          continue
        }
        
        if (authData.user) {
          // Update the profile with additional data
          const { error: profileError } = await supabase
            .from('profiles')
            .upsert({
              id: authData.user.id,
              email: user.email,
              full_name: user.full_name,
              subscription_tier: user.subscription_tier,
              role: user.role,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
          
          if (profileError) {
            console.error(`Error creating profile for ${user.email}:`, profileError)
          } else {
            console.log(`✅ Created user ${user.email}`)
          }
        }
      }
    }
    
    console.log('Epic 5 test users setup complete!')
    return { success: true }
    
  } catch (error) {
    console.error('Error setting up Epic 5 test users:', error)
    return { success: false, error }
  }
}

export async function verifyEpic5TestUsers() {
  try {
    const supabase = await createServerClient()
    
    console.log('Verifying Epic 5 test users...')
    
    for (const user of testUsers) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('id, email, subscription_tier, role, full_name')
        .eq('email', user.email)
        .single()
      
      if (profile) {
        console.log(`✅ ${user.email}: ${profile.role} (${profile.subscription_tier})`)
      } else {
        console.log(`❌ ${user.email}: NOT FOUND`)
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error verifying Epic 5 test users:', error)
    return { success: false, error }
  }
}
