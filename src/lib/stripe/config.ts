import Stripe from 'stripe'

// Validate Stripe key exists
const stripeKey = process.env.STRIPE_SECRET_KEY

if (!stripeKey) {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('STRIPE_SECRET_KEY is required in production')
  }
  console.warn('⚠️ STRIPE_SECRET_KEY not found, using placeholder for development')
}

export const stripe = new Stripe(stripeKey || 'sk_test_placeholder', {
  apiVersion: '2025-04-30.basil',
  appInfo: {
    name: 'Ordrly',
    version: '1.0.0',
  },
})

export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  prices: {
    trial: null, // 7-day trial tier
    starter_monthly: process.env.STRIPE_PRICE_STARTER_MONTHLY!, // $49/month
    starter_annual: process.env.STRIPE_PRICE_STARTER_ANNUAL!, // $490/year
    professional_monthly: process.env.STRIPE_PRICE_PROFESSIONAL_MONTHLY!, // $99/month
    professional_annual: process.env.STRIPE_PRICE_PROFESSIONAL_ANNUAL!, // $990/year
    // Legacy prices for backward compatibility
    pro: process.env.STRIPE_PRICE_PRO!, // $19/month (deprecated)
    appraiser: process.env.STRIPE_PRICE_APPRAISER!, // $59/month (deprecated)
  },
  // Pull packs use dynamic pricing - no product ID needed
} as const

export type PlanType = 'trial' | 'starter' | 'professional' | 'pro' | 'appraiser'

export const PLAN_LIMITS = {
  trial: {
    monthlyPulls: 500, // 500 messages during 7-day trial (same as starter)
    trialDays: 7,
    features: ['Full Starter plan access', 'AI chat assistance', 'Municipal compliance research', 'Save searches', 'Source links'],
  },
  starter: {
    monthlyPulls: 500, // 500 messages per month
    features: ['500 messages/month', 'AI chat assistance', 'Municipal compliance research', 'Save & organize searches', 'Standard email support'],
  },
  professional: {
    monthlyPulls: 2000, // 2000 messages per month
    features: ['2,000 messages/month', 'AI chat assistance', 'Priority support', 'Faster response times', 'Enhanced source analysis'],
  },
  business: {
    monthlyPulls: -1, // Unlimited
    features: ['Everything in Professional', 'API access', 'White-label solutions', 'Dedicated support', 'Custom integrations'],
  },
  // Legacy tiers for backward compatibility (deprecated - use trial/starter/professional instead)
  pro: {
    monthlyPulls: -1, // Unlimited
    features: ['Unlimited searches', 'Priority support', 'Advanced filtering', 'Export to PDF'],
  },
  appraiser: {
    monthlyPulls: -1, // Unlimited
    features: ['Everything in Pro', 'API access', 'White-label reports', 'Dedicated support'],
  },
} as const
