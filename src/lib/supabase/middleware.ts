import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // Define protected routes that require authentication
  const protectedRoutes = [
    '/account',
    '/billing',
    '/dashboard',
    '/profile',
    '/preferences',
    '/history',
    '/saved-searches',
    '/admin',
    '/business',
    '/enterprise',
    '/chat'
  ]

  const isProtectedRoute = protectedRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  // For public routes, skip auth processing
  if (!isProtectedRoute) {
    return NextResponse.next({ request })
  }

  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // Get user for this protected route
  let user = null

  try {
    const {
      data: { user: authUser },
      error
    } = await supabase.auth.getUser()



    user = authUser

  } catch (error: unknown) {

    // Handle AuthSessionMissingError gracefully - this is expected when not logged in
    if (error && typeof error === 'object' && 'name' in error && error.name !== 'AuthSessionMissingError') {
      console.error('Middleware: Error getting user:', error)
    }
    // Continue with user = null
  }

  // Define API routes that should be accessible without authentication (for webhooks, etc.)
  const publicApiRoutes = [
    '/api/usage-alerts' // Allow n8n webhooks and external services
  ]

  // Check if current path is a public API route
  const isPublicApiRoute = publicApiRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  // Redirect to login if no user on protected route (we know it's protected since we got here)
  // Skip authentication for public API routes (webhooks, etc.)
  if (
    !user &&
    !isPublicApiRoute &&
    !request.nextUrl.pathname.startsWith('/login') &&
    !request.nextUrl.pathname.startsWith('/signup') &&
    !request.nextUrl.pathname.startsWith('/auth') &&
    !request.nextUrl.pathname.startsWith('/api/auth')
  ) {

    const url = request.nextUrl.clone()
    url.pathname = '/login'
    url.searchParams.set('redirectTo', request.nextUrl.pathname)
    return NextResponse.redirect(url)
  }



  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  return supabaseResponse
}
