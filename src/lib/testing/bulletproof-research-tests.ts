import { BulletproofResearchService } from '../services/bulletproof-research'
import { createServerClient } from '@/lib/supabase/server'

/**
 * 🧪 BULLETPROOF RESEARCH API TESTING FRAMEWORK
 * 
 * Comprehensive testing framework for the research API with:
 * - Quality validation (99% accuracy target)
 * - Performance benchmarking
 * - Conversation context testing
 * - Municipal scraping validation
 * - Real-world scenario testing
 */

export interface TestCase {
  id: string
  name: string
  query: string
  jurisdiction: string
  expectedIntent: string
  expectedConcepts: string[]
  minimumConfidence: number
  requiresFreshData: boolean
  conversationContext?: {
    isFollowUp: boolean
    previousQueries: string[]
  }
}

export interface TestResult {
  testId: string
  passed: boolean
  confidence: number
  processingTimeMs: number
  usedCache: boolean
  triggeredResearch: boolean
  sourcesFound: number
  primarySources: number
  verifiedSources: number
  errors: string[]
  qualityScore: number
}

export interface TestSuite {
  name: string
  description: string
  testCases: TestCase[]
}

export class BulletproofResearchTestFramework {
  private researchService: BulletproofResearchService
  private supabase: any

  constructor() {
    this.supabase = createServerClient()
    this.researchService = new BulletproofResearchService(this.supabase)
  }

  /**
   * 🎯 Core Test Suites
   */
  getTestSuites(): TestSuite[] {
    return [
      this.getBasicFunctionalityTests(),
      this.getConversationContextTests(),
      this.getHighStakesPermissionTests(),
      this.getPerformanceTests(),
      this.getRealWorldScenarioTests()
    ]
  }

  /**
   * 🔬 Run Complete Test Suite
   */
  async runAllTests(): Promise<{
    totalTests: number
    passedTests: number
    failedTests: number
    averageConfidence: number
    averageProcessingTime: number
    qualityScore: number
    results: TestResult[]
  }> {
    console.log('🧪 [TESTING] Starting comprehensive bulletproof research API tests')
    
    const allTestSuites = this.getTestSuites()
    const allResults: TestResult[] = []

    for (const suite of allTestSuites) {
      console.log(`📋 [TESTING] Running test suite: ${suite.name}`)
      
      for (const testCase of suite.testCases) {
        const result = await this.runSingleTest(testCase)
        allResults.push(result)
        
        console.log(`${result.passed ? '✅' : '❌'} [TEST] ${testCase.name} - Confidence: ${Math.round(result.confidence * 100)}% - Time: ${result.processingTimeMs}ms`)
      }
    }

    // Calculate overall metrics
    const totalTests = allResults.length
    const passedTests = allResults.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    const averageConfidence = allResults.reduce((sum, r) => sum + r.confidence, 0) / totalTests
    const averageProcessingTime = allResults.reduce((sum, r) => sum + r.processingTimeMs, 0) / totalTests
    const qualityScore = this.calculateOverallQualityScore(allResults)

    console.log(`📊 [TESTING] Test Results Summary:`)
    console.log(`   Total Tests: ${totalTests}`)
    console.log(`   Passed: ${passedTests} (${Math.round(passedTests / totalTests * 100)}%)`)
    console.log(`   Failed: ${failedTests}`)
    console.log(`   Average Confidence: ${Math.round(averageConfidence * 100)}%`)
    console.log(`   Average Processing Time: ${Math.round(averageProcessingTime)}ms`)
    console.log(`   Overall Quality Score: ${Math.round(qualityScore * 100)}%`)

    return {
      totalTests,
      passedTests,
      failedTests,
      averageConfidence,
      averageProcessingTime,
      qualityScore,
      results: allResults
    }
  }

  /**
   * 🔍 Run Single Test
   */
  async runSingleTest(testCase: TestCase): Promise<TestResult> {
    const startTime = Date.now()
    const errors: string[] = []

    try {
      // Prepare conversation context
      const conversationContext = testCase.conversationContext ? {
        sessionId: 'test-session',
        isFollowUp: testCase.conversationContext.isFollowUp,
        intent: testCase.expectedIntent,
        needsFreshData: testCase.requiresFreshData,
        confidenceThreshold: testCase.minimumConfidence
      } : undefined

      // Perform research
      const result = await this.researchService.performResearch({
        query: testCase.query,
        jurisdiction: testCase.jurisdiction,
        conversationContext
      })

      const processingTime = Date.now() - startTime

      // Validate results
      const validationErrors = this.validateTestResult(result, testCase)
      errors.push(...validationErrors)

      // Calculate quality score
      const qualityScore = this.calculateQualityScore(result, testCase)

      return {
        testId: testCase.id,
        passed: errors.length === 0 && result.confidence >= testCase.minimumConfidence,
        confidence: result.confidence,
        processingTimeMs: processingTime,
        usedCache: result.usedCache,
        triggeredResearch: result.triggeredResearch,
        sourcesFound: result.sources.length,
        primarySources: result.sources.filter(s => s.authority === 'primary').length,
        verifiedSources: result.sources.filter(s => s.verified).length,
        errors,
        qualityScore
      }

    } catch (error) {
      errors.push(`Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      
      return {
        testId: testCase.id,
        passed: false,
        confidence: 0,
        processingTimeMs: Date.now() - startTime,
        usedCache: false,
        triggeredResearch: false,
        sourcesFound: 0,
        primarySources: 0,
        verifiedSources: 0,
        errors,
        qualityScore: 0
      }
    }
  }

  /**
   * 📋 Test Suite Definitions
   */
  private getBasicFunctionalityTests(): TestSuite {
    return {
      name: 'Basic Functionality',
      description: 'Core research functionality tests',
      testCases: [
        {
          id: 'basic-001',
          name: 'Simple fence height query',
          query: 'What is the maximum fence height allowed in residential areas?',
          jurisdiction: 'Austin, TX',
          expectedIntent: 'requirement',
          expectedConcepts: ['fence', 'height'],
          minimumConfidence: 0.8,
          requiresFreshData: false
        },
        {
          id: 'basic-002',
          name: 'Deck building permission',
          query: 'Can I build a deck in my backyard without a permit?',
          jurisdiction: 'Seattle, WA',
          expectedIntent: 'permission',
          expectedConcepts: ['deck', 'permit'],
          minimumConfidence: 0.85,
          requiresFreshData: true
        },
        {
          id: 'basic-003',
          name: 'Shed setback requirements',
          query: 'How far from the property line must a storage shed be placed?',
          jurisdiction: 'Denver, CO',
          expectedIntent: 'requirement',
          expectedConcepts: ['shed', 'setback'],
          minimumConfidence: 0.8,
          requiresFreshData: false
        }
      ]
    }
  }

  private getConversationContextTests(): TestSuite {
    return {
      name: 'Conversation Context',
      description: 'Tests for conversation context awareness',
      testCases: [
        {
          id: 'context-001',
          name: 'Follow-up question about fence materials',
          query: 'What materials are allowed for the fence?',
          jurisdiction: 'Austin, TX',
          expectedIntent: 'clarification',
          expectedConcepts: ['fence'],
          minimumConfidence: 0.75,
          requiresFreshData: false,
          conversationContext: {
            isFollowUp: true,
            previousQueries: ['What is the maximum fence height allowed?']
          }
        },
        {
          id: 'context-002',
          name: 'Follow-up about permit process',
          query: 'How long does the permit process take?',
          jurisdiction: 'Seattle, WA',
          expectedIntent: 'process',
          expectedConcepts: ['permit'],
          minimumConfidence: 0.75,
          requiresFreshData: false,
          conversationContext: {
            isFollowUp: true,
            previousQueries: ['Can I build a deck without a permit?']
          }
        }
      ]
    }
  }

  private getHighStakesPermissionTests(): TestSuite {
    return {
      name: 'High-Stakes Permission Questions',
      description: 'Critical permission questions requiring high accuracy',
      testCases: [
        {
          id: 'permission-001',
          name: 'Pool installation permission',
          query: 'Am I allowed to install an above-ground pool in my backyard?',
          jurisdiction: 'Miami, FL',
          expectedIntent: 'permission',
          expectedConcepts: ['pool'],
          minimumConfidence: 0.9,
          requiresFreshData: true
        },
        {
          id: 'permission-002',
          name: 'Commercial use in residential',
          query: 'Can I run a business from my home in this residential area?',
          jurisdiction: 'Portland, OR',
          expectedIntent: 'permission',
          expectedConcepts: [],
          minimumConfidence: 0.9,
          requiresFreshData: true
        }
      ]
    }
  }

  private getPerformanceTests(): TestSuite {
    return {
      name: 'Performance Benchmarks',
      description: 'Performance and speed tests',
      testCases: [
        {
          id: 'perf-001',
          name: 'Fast cache response',
          query: 'Standard fence height regulations',
          jurisdiction: 'Austin, TX',
          expectedIntent: 'requirement',
          expectedConcepts: ['fence', 'height'],
          minimumConfidence: 0.8,
          requiresFreshData: false
        }
      ]
    }
  }

  private getRealWorldScenarioTests(): TestSuite {
    return {
      name: 'Real-World Scenarios',
      description: 'Complex real-world compliance questions',
      testCases: [
        {
          id: 'real-001',
          name: 'Complex multi-structure question',
          query: 'I want to build a 6-foot fence, install a hot tub, and add a storage shed. What permits and regulations apply?',
          jurisdiction: 'Phoenix, AZ',
          expectedIntent: 'requirement',
          expectedConcepts: ['fence', 'permit', 'shed'],
          minimumConfidence: 0.85,
          requiresFreshData: false
        }
      ]
    }
  }

  /**
   * 🔍 Validation and Scoring
   */
  private validateTestResult(result: any, testCase: TestCase): string[] {
    const errors: string[] = []

    if (!result.success) {
      errors.push('Research failed')
    }

    if (result.confidence < testCase.minimumConfidence) {
      errors.push(`Confidence too low: ${Math.round(result.confidence * 100)}% < ${Math.round(testCase.minimumConfidence * 100)}%`)
    }

    if (testCase.requiresFreshData && !result.triggeredResearch) {
      errors.push('Fresh research was required but not triggered')
    }

    if (!result.content || result.content.length < 50) {
      errors.push('Response content too short or empty')
    }

    return errors
  }

  private calculateQualityScore(result: any, testCase: TestCase): number {
    let score = 0

    // Confidence score (40%)
    score += result.confidence * 0.4

    // Source quality (30%)
    const sourceQuality = result.sources.length > 0 ? 
      (result.sources.filter((s: any) => s.verified).length / result.sources.length) : 0
    score += sourceQuality * 0.3

    // Content quality (20%)
    const contentQuality = Math.min(1.0, (result.content?.length || 0) / 200)
    score += contentQuality * 0.2

    // Performance (10%)
    const performanceScore = result.processingTimeMs < 5000 ? 1.0 : Math.max(0, 1.0 - (result.processingTimeMs - 5000) / 10000)
    score += performanceScore * 0.1

    return Math.min(1.0, score)
  }

  private calculateOverallQualityScore(results: TestResult[]): number {
    if (results.length === 0) return 0
    
    const totalQuality = results.reduce((sum, r) => sum + r.qualityScore, 0)
    return totalQuality / results.length
  }
}
