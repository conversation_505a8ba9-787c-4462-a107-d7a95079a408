/**
 * Research Test Framework for 100-Test Validation Suite
 * Validates research-level accuracy with 99-100% success rate requirement
 * Part of Phase 4: 100-Test Validation Suite
 */

import { createServerClient } from '@/lib/supabase/server'
import { SupabaseClient, createClient } from '@supabase/supabase-js'
import { BulletproofResearchService } from '../services/bulletproof-research'

export interface TestCase {
  id: string
  test_name: string
  test_category: 'common_projects' | 'geographic_diversity' | 'complexity_levels' | 'edge_cases'
  jurisdiction: string
  rule_type: string
  test_query: string
  expected_citation: string
  expected_confidence: number
  success_criteria: {
    min_confidence: number
    requires_legal_citation: boolean
    requires_verifiable_link: boolean
    requires_specific_measurements: boolean
    max_response_time_ms: number
  }
  is_active: boolean
}

export interface TestResult {
  test_case_id: string
  execution_date: string
  passed: boolean
  actual_confidence: number
  actual_citation?: string
  response_time_ms: number
  sources_found: number
  error_message?: string
  execution_details: {
    used_cache: boolean
    triggered_research: boolean
    quality_score: number
    verification_status: string
    sources_verified: number
  }
}

export interface TestSuiteResult {
  suite_id: string
  execution_date: string
  total_tests: number
  passed_tests: number
  failed_tests: number
  pass_rate: number
  average_confidence: number
  average_response_time: number
  category_results: Record<string, {
    total: number
    passed: number
    pass_rate: number
  }>
  failed_test_details: Array<{
    test_name: string
    category: string
    error: string
    actual_confidence: number
  }>
}

export class ResearchTestFramework {
  private supabase: SupabaseClient
  private researchService: BulletproofResearchService

  constructor(supabase?: SupabaseClient) {
    if (supabase) {
      this.supabase = supabase
    } else {
      // For standalone usage, create a direct client
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing')
      }
      this.supabase = createClient(supabaseUrl, supabaseKey)
    }
    this.researchService = new BulletproofResearchService(this.supabase)
  }

  /**
   * Run the complete 100-test validation suite
   */
  async runFullTestSuite(): Promise<TestSuiteResult> {
    const suiteId = `suite_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const executionDate = new Date().toISOString()
    
    console.log(`🧪 Starting full test suite execution: ${suiteId}`)

    try {
      // Load all active test cases
      const testCases = await this.loadTestCases()
      
      if (testCases.length === 0) {
        throw new Error('No test cases found. Please populate test cases first.')
      }

      console.log(`📋 Loaded ${testCases.length} test cases`)

      // Execute all test cases
      const results: TestResult[] = []
      const categoryResults: Record<string, { total: number; passed: number; pass_rate: number }> = {}

      for (const testCase of testCases) {
        console.log(`🔍 Executing test: ${testCase.test_name}`)
        
        const result = await this.executeTestCase(testCase)
        results.push(result)

        // Update category statistics
        if (!categoryResults[testCase.test_category]) {
          categoryResults[testCase.test_category] = { total: 0, passed: 0, pass_rate: 0 }
        }
        categoryResults[testCase.test_category].total++
        if (result.passed) {
          categoryResults[testCase.test_category].passed++
        }

        // Store result in database
        await this.storeTestResult(result)
      }

      // Calculate final statistics
      const totalTests = results.length
      const passedTests = results.filter(r => r.passed).length
      const failedTests = totalTests - passedTests
      const passRate = totalTests > 0 ? passedTests / totalTests : 0
      const averageConfidence = results.reduce((sum, r) => sum + r.actual_confidence, 0) / totalTests
      const averageResponseTime = results.reduce((sum, r) => sum + r.response_time_ms, 0) / totalTests

      // Calculate category pass rates
      Object.keys(categoryResults).forEach(category => {
        const cat = categoryResults[category]
        cat.pass_rate = cat.total > 0 ? cat.passed / cat.total : 0
      })

      // Get failed test details
      const failedTestDetails = results
        .filter(r => !r.passed)
        .map(r => {
          const testCase = testCases.find(tc => tc.id === r.test_case_id)!
          return {
            test_name: testCase.test_name,
            category: testCase.test_category,
            error: r.error_message || 'Test failed validation criteria',
            actual_confidence: r.actual_confidence
          }
        })

      const suiteResult: TestSuiteResult = {
        suite_id: suiteId,
        execution_date: executionDate,
        total_tests: totalTests,
        passed_tests: passedTests,
        failed_tests: failedTests,
        pass_rate: passRate,
        average_confidence: averageConfidence,
        average_response_time: averageResponseTime,
        category_results: categoryResults,
        failed_test_details: failedTestDetails
      }

      console.log(`✅ Test suite completed: ${passedTests}/${totalTests} tests passed (${(passRate * 100).toFixed(1)}%)`)
      
      return suiteResult

    } catch (error) {
      console.error('Error running test suite:', error)
      throw error
    }
  }

  /**
   * Execute a single test case
   */
  private async executeTestCase(testCase: TestCase): Promise<TestResult> {
    const startTime = Date.now()

    try {
      // Execute the research request
      const researchResult = await this.researchService.performResearch({
        query: testCase.test_query,
        jurisdiction: testCase.jurisdiction
      })

      const responseTime = Date.now() - startTime

      // Validate against success criteria
      const validationResult = this.validateTestResult(testCase, researchResult, responseTime)

      const testResult: TestResult = {
        test_case_id: testCase.id,
        execution_date: new Date().toISOString(),
        passed: validationResult.passed,
        actual_confidence: researchResult.confidence,
        actual_citation: researchResult.sources[0]?.content || '',
        response_time_ms: responseTime,
        sources_found: researchResult.sources.length,
        error_message: validationResult.error,
        execution_details: {
          used_cache: researchResult.usedCache || false,
          triggered_research: !researchResult.usedCache,
          quality_score: researchResult.confidence,
          verification_status: researchResult.sources[0]?.authority || 'unknown',
          sources_verified: researchResult.sources.filter(s => s.url && s.url.length > 10).length
        }
      }

      return testResult

    } catch (error) {
      const responseTime = Date.now() - startTime
      
      return {
        test_case_id: testCase.id,
        execution_date: new Date().toISOString(),
        passed: false,
        actual_confidence: 0,
        response_time_ms: responseTime,
        sources_found: 0,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        execution_details: {
          used_cache: false,
          triggered_research: false,
          quality_score: 0,
          verification_status: 'error',
          sources_verified: 0
        }
      }
    }
  }

  /**
   * Validate test result against success criteria
   */
  private validateTestResult(
    testCase: TestCase,
    researchResult: any,
    responseTime: number
  ): { passed: boolean; error?: string } {
    const criteria = testCase.success_criteria

    // Check minimum confidence
    if (researchResult.confidence < criteria.min_confidence) {
      return {
        passed: false,
        error: `Confidence ${researchResult.confidence} below required ${criteria.min_confidence}`
      }
    }

    // Check legal citation requirement
    if (criteria.requires_legal_citation) {
      const hasLegalCitation = researchResult.sources.some((source: any) =>
        source.content && source.content.includes('Chapter') && source.content.includes('Section')
      )
      if (!hasLegalCitation) {
        return {
          passed: false,
          error: 'Required legal citation with Chapter/Section not found'
        }
      }
    }

    // Check verifiable link requirement
    if (criteria.requires_verifiable_link) {
      const hasVerifiableLink = researchResult.sources.some((source: any) => 
        source.url && source.url.length > 10 && source.url.startsWith('http')
      )
      if (!hasVerifiableLink) {
        return {
          passed: false,
          error: 'Required verifiable link not found'
        }
      }
    }

    // Check specific measurements requirement
    if (criteria.requires_specific_measurements) {
      const hasSpecificMeasurements = researchResult.answer.match(/\d+\s*(feet|ft|inches|in|meters|m)\b/i)
      if (!hasSpecificMeasurements) {
        return {
          passed: false,
          error: 'Required specific measurements not found'
        }
      }
    }

    // Check response time
    if (responseTime > criteria.max_response_time_ms) {
      return {
        passed: false,
        error: `Response time ${responseTime}ms exceeded limit ${criteria.max_response_time_ms}ms`
      }
    }

    return { passed: true }
  }

  /**
   * Load all active test cases from database
   */
  private async loadTestCases(): Promise<TestCase[]> {
    try {
      const { data, error } = await this.supabase
        .from('research_test_cases')
        .select('*')
        .eq('is_active', true)
        .order('test_category')
        .order('test_name')

      if (error) {
        console.error('Error loading test cases:', error)
        return []
      }

      return data || []

    } catch (error) {
      console.error('Error loading test cases:', error)
      return []
    }
  }

  /**
   * Store test result in database
   */
  private async storeTestResult(result: TestResult): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('test_execution_results')
        .insert({
          test_case_id: result.test_case_id,
          execution_date: result.execution_date,
          passed: result.passed,
          actual_confidence: result.actual_confidence,
          actual_citation: result.actual_citation,
          response_time_ms: result.response_time_ms,
          sources_found: result.sources_found,
          error_message: result.error_message,
          execution_details: result.execution_details
        })

      if (error) {
        console.error('Error storing test result:', error)
      }

    } catch (error) {
      console.error('Error storing test result:', error)
    }
  }

  /**
   * Run tests for a specific category
   */
  async runCategoryTests(category: 'common_projects' | 'geographic_diversity' | 'complexity_levels' | 'edge_cases'): Promise<TestSuiteResult> {
    console.log(`🧪 Running tests for category: ${category}`)

    const { data: testCases, error } = await this.supabase
      .from('research_test_cases')
      .select('*')
      .eq('test_category', category)
      .eq('is_active', true)

    if (error || !testCases || testCases.length === 0) {
      throw new Error(`No test cases found for category: ${category}`)
    }

    const results: TestResult[] = []
    
    for (const testCase of testCases) {
      const result = await this.executeTestCase(testCase)
      results.push(result)
      await this.storeTestResult(result)
    }

    const totalTests = results.length
    const passedTests = results.filter(r => r.passed).length
    const passRate = totalTests > 0 ? passedTests / totalTests : 0

    return {
      suite_id: `category_${category}_${Date.now()}`,
      execution_date: new Date().toISOString(),
      total_tests: totalTests,
      passed_tests: passedTests,
      failed_tests: totalTests - passedTests,
      pass_rate: passRate,
      average_confidence: results.reduce((sum, r) => sum + r.actual_confidence, 0) / totalTests,
      average_response_time: results.reduce((sum, r) => sum + r.response_time_ms, 0) / totalTests,
      category_results: {
        [category]: {
          total: totalTests,
          passed: passedTests,
          pass_rate: passRate
        }
      },
      failed_test_details: results
        .filter(r => !r.passed)
        .map(r => {
          const testCase = testCases.find(tc => tc.id === r.test_case_id)!
          return {
            test_name: testCase.test_name,
            category: testCase.test_category,
            error: r.error_message || 'Test failed validation criteria',
            actual_confidence: r.actual_confidence
          }
        })
    }
  }

  /**
   * Get test execution history and statistics
   */
  async getTestHistory(limit: number = 10): Promise<Array<{
    execution_date: string
    total_tests: number
    pass_rate: number
    average_confidence: number
  }>> {
    try {
      const { data, error } = await this.supabase
        .from('test_execution_results')
        .select('execution_date, passed, actual_confidence')
        .order('execution_date', { ascending: false })
        .limit(limit * 100) // Get more data to group by execution

      if (error || !data) {
        return []
      }

      // Group by execution date (day)
      const groupedResults = data.reduce((acc, result) => {
        const date = result.execution_date.split('T')[0]
        if (!acc[date]) {
          acc[date] = { total: 0, passed: 0, confidence_sum: 0 }
        }
        acc[date].total++
        if (result.passed) acc[date].passed++
        acc[date].confidence_sum += result.actual_confidence
        return acc
      }, {} as Record<string, { total: number; passed: number; confidence_sum: number }>)

      return Object.entries(groupedResults)
        .map(([date, stats]) => ({
          execution_date: date,
          total_tests: stats.total,
          pass_rate: stats.total > 0 ? stats.passed / stats.total : 0,
          average_confidence: stats.total > 0 ? stats.confidence_sum / stats.total : 0
        }))
        .slice(0, limit)

    } catch (error) {
      console.error('Error getting test history:', error)
      return []
    }
  }
}
