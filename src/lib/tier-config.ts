/**
 * Tier Configuration System for Epic 6 Features
 * Defines capabilities and limits for each subscription tier
 */

export type SubscriptionTier = 'trial' | 'trial_expired' | 'starter' | 'professional' | 'business'

export interface TierConfig {
  // Core limits
  monthlyPulls: number // -1 for unlimited
  maxSources: number // Number of sources to analyze

  // Trial specific
  trialDays?: number // Number of trial days (only for trial tier)
  isTrialTier?: boolean // Whether this is a trial tier

  // Feature access
  enableCustomInput: boolean
  enableRedFlags: boolean
  enableClauseBrowser: boolean
  enableChat: boolean
  enableExpandedSources: boolean

  // Source access levels
  sourceTypes: string[]

  // UI features
  showUpgradePrompts: boolean
  prioritySupport: boolean
}

export const TIER_CONFIGS: Record<SubscriptionTier, TierConfig> = {
  trial: {
    monthlyPulls: 500, // 500 messages during trial (same as starter)
    maxSources: 5,
    trialDays: 7,
    isTrialTier: true,
    enableCustomInput: true,
    enableRedFlags: false, // Feature removed
    enableClauseBrowser: false,
    enableChat: true, // Main feature - chat interface
    enableExpandedSources: false,
    sourceTypes: ['municipal_codes', 'basic_ordinances'],
    showUpgradePrompts: true,
    prioritySupport: false,
  },
  trial_expired: {
    monthlyPulls: 0, // No calls allowed after trial expires
    maxSources: 0,
    isTrialTier: true,
    enableCustomInput: false,
    enableRedFlags: false,
    enableClauseBrowser: false,
    enableChat: false,
    enableExpandedSources: false,
    sourceTypes: [],
    showUpgradePrompts: true,
    prioritySupport: false,
  },
  starter: {
    monthlyPulls: 500, // 500 messages per month
    maxSources: 5,
    enableCustomInput: true,
    enableRedFlags: false, // Feature removed
    enableClauseBrowser: false,
    enableChat: true, // Main feature - chat interface
    enableExpandedSources: false,
    sourceTypes: ['municipal_codes', 'basic_ordinances'],
    showUpgradePrompts: true,
    prioritySupport: false, // Standard support
  },
  professional: {
    monthlyPulls: 2000, // 2000 messages per month
    maxSources: 10,
    enableCustomInput: true,
    enableRedFlags: false, // Feature removed
    enableClauseBrowser: false,
    enableChat: true, // Main feature - chat interface
    enableExpandedSources: true,
    sourceTypes: ['municipal_codes', 'basic_ordinances', 'state_codes', 'irc', 'cbc'],
    showUpgradePrompts: false,
    prioritySupport: true, // Priority support - key difference
  },
  business: {
    monthlyPulls: -1, // unlimited searches
    maxSources: -1, // unlimited
    enableCustomInput: true,
    enableRedFlags: false, // Feature removed
    enableClauseBrowser: false,
    enableChat: true, // Main feature - chat interface
    enableExpandedSources: true,
    sourceTypes: ['municipal_codes', 'basic_ordinances', 'state_codes', 'irc', 'cbc', 'api_access'],
    showUpgradePrompts: false,
    prioritySupport: true, // Dedicated support
  },

}

/**
 * Feature flags for gradual rollout
 */
export const FEATURE_FLAGS = {
  EPIC6_ENABLED: process.env.NEXT_PUBLIC_EPIC6_ENABLED === 'true',
  CUSTOM_PROJECT_ENABLED: process.env.NEXT_PUBLIC_CUSTOM_PROJECT_ENABLED === 'true',
  RED_FLAGS_ENABLED: process.env.NEXT_PUBLIC_RED_FLAGS_ENABLED === 'true',
  CLAUSE_BROWSER_ENABLED: process.env.NEXT_PUBLIC_CLAUSE_BROWSER_ENABLED === 'true',
  CHAT_ENABLED: process.env.NEXT_PUBLIC_CHAT_ENABLED === 'true',
  EXPANDED_SOURCES_ENABLED: process.env.NEXT_PUBLIC_EXPANDED_SOURCES_ENABLED === 'true',
} as const

/**
 * Get tier configuration for a subscription tier
 */
export function getTierConfig(tier: SubscriptionTier): TierConfig {
  return TIER_CONFIGS[tier] || TIER_CONFIGS.trial
}

/**
 * Check if a tier is a trial tier
 */
export function isTrialTier(tier: SubscriptionTier): boolean {
  return tier === 'trial' || TIER_CONFIGS[tier]?.isTrialTier === true
}

/**
 * Get the next upgrade tier for a given tier
 */
export function getUpgradeTier(currentTier: SubscriptionTier): SubscriptionTier | null {
  switch (currentTier) {
    case 'trial':
    case 'trial_expired':
      return 'starter'
    case 'starter':
      return 'professional'
    case 'professional':
      return 'business'
    case 'business':
      return null // Already at highest tier
    default:
      return 'starter'
  }
}

/**
 * Get trial expiration date for a user
 */
export function getTrialExpirationDate(createdAt: Date, trialDays: number = 7): Date {
  const expirationDate = new Date(createdAt)
  expirationDate.setDate(expirationDate.getDate() + trialDays)
  return expirationDate
}

/**
 * Check if trial has expired
 */
export function isTrialExpired(createdAt: Date, trialDays: number = 7): boolean {
  const now = new Date()
  const expirationDate = getTrialExpirationDate(createdAt, trialDays)
  return now > expirationDate
}

/**
 * Get days remaining in trial
 */
export function getTrialDaysRemaining(createdAt: Date, trialDays: number = 7): number {
  const now = new Date()
  const expirationDate = getTrialExpirationDate(createdAt, trialDays)
  const msRemaining = expirationDate.getTime() - now.getTime()
  const daysRemaining = Math.ceil(msRemaining / (1000 * 60 * 60 * 24))
  return Math.max(0, daysRemaining)
}

/**
 * Check if a feature is enabled globally
 */
export function isFeatureEnabled(feature: keyof typeof FEATURE_FLAGS): boolean {
  return FEATURE_FLAGS[feature] || false
}

/**
 * Check if a user has access to a specific feature
 */
export function hasFeatureAccess(tier: SubscriptionTier, feature: keyof TierConfig): boolean {
  const config = getTierConfig(tier)
  return config[feature] as boolean
}

/**
 * Check if a tier has access to a specific source type
 */
export function hasSourceAccess(tier: SubscriptionTier, sourceType: string): boolean {
  const config = getTierConfig(tier)
  return config.sourceTypes.includes(sourceType)
}

/**
 * Get upgrade suggestions for a tier
 */
export function getUpgradeSuggestions(currentTier: SubscriptionTier): {
  suggestedTier: SubscriptionTier | null
  benefits: string[]
  price: string
} {
  switch (currentTier) {
    case 'trial':
      return {
        suggestedTier: 'starter',
        benefits: [
          'Continue with 500 messages per month',
          'AI compliance chat',
          'Municipal ordinance access',
          'Basic source access'
        ],
        price: '$49/month'
      }
    case 'starter':
      return {
        suggestedTier: 'professional',
        benefits: [
          '2,000 messages per month',
          'Priority support',
          'Expanded source access',
          'Advanced features'
        ],
        price: '$99/month'
      }
    default:
      return {
        suggestedTier: null,
        benefits: [],
        price: ''
      }
  }
}

/**
 * Validate tier access for API endpoints
 */
export function validateTierAccess(
  userTier: SubscriptionTier,
  requiredFeature: keyof TierConfig
): { hasAccess: boolean; message?: string } {
  if (!isFeatureEnabled('EPIC6_ENABLED')) {
    return { hasAccess: false, message: 'Epic 6 features are not enabled' }
  }

  const hasAccess = hasFeatureAccess(userTier, requiredFeature)
  
  if (!hasAccess) {
    const suggestions = getUpgradeSuggestions(userTier)
    return {
      hasAccess: false,
      message: `This feature requires ${suggestions.suggestedTier} tier or higher. Upgrade for ${suggestions.price}.`
    }
  }

  return { hasAccess: true }
}
