/**
 * Trial Management System
 * Handles automatic trial assignment, expiration, and conversion
 */

import { createClient } from '@/lib/supabase/client'
import { createServerClient } from '@/lib/supabase/server'
import { isTrialTier, getTrialDaysRemaining, isTrialExpired, getUpgradeTier } from '@/lib/tier-config'
import type { SubscriptionTier } from '@/lib/tier-config'

export interface TrialStatus {
  isOnTrial: boolean
  daysRemaining: number
  isExpired: boolean
  trialStartDate: Date | null
  trialEndDate: Date | null
  suggestedUpgrade: SubscriptionTier | null
}

/**
 * Automatically assign trial tier to new users
 */
export async function assignTrialToNewUser(userId: string): Promise<boolean> {
  try {
    const supabase = createClient()
    
    // Update user profile with trial tier and trial start date
    const { error } = await supabase
      .from('profiles')
      .update({
        subscription_tier: 'trial',
        trial_start_date: new Date().toISOString(),
        trial_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)

    if (error) {
      console.error('Error assigning trial to user:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in assignTrialToNewUser:', error)
    return false
  }
}

/**
 * Get trial status for a user
 */
export async function getTrialStatus(userId: string): Promise<TrialStatus> {
  try {
    const supabase = createClient()
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('subscription_tier, trial_start_date, trial_end_date, created_at')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      return {
        isOnTrial: false,
        daysRemaining: 0,
        isExpired: true,
        trialStartDate: null,
        trialEndDate: null,
        suggestedUpgrade: 'starter'
      }
    }

    const isOnTrial = isTrialTier(profile.subscription_tier as SubscriptionTier)
    const trialStartDate = profile.trial_start_date ? new Date(profile.trial_start_date) : null
    const trialEndDate = profile.trial_end_date ? new Date(profile.trial_end_date) : null
    
    let daysRemaining = 0
    let isExpired = false
    
    if (isOnTrial && trialStartDate) {
      daysRemaining = getTrialDaysRemaining(trialStartDate)
      isExpired = isTrialExpired(trialStartDate)
    }

    return {
      isOnTrial,
      daysRemaining,
      isExpired,
      trialStartDate,
      trialEndDate,
      suggestedUpgrade: getUpgradeTier(profile.subscription_tier as SubscriptionTier)
    }
  } catch (error) {
    console.error('Error getting trial status:', error)
    return {
      isOnTrial: false,
      daysRemaining: 0,
      isExpired: true,
      trialStartDate: null,
      trialEndDate: null,
      suggestedUpgrade: 'starter'
    }
  }
}

/**
 * Check if trial has expired and update user tier if needed
 */
export async function checkAndExpireTrial(userId: string): Promise<boolean> {
  try {
    const trialStatus = await getTrialStatus(userId)
    
    if (trialStatus.isOnTrial && trialStatus.isExpired) {
      const supabase = createClient()
      
      // Update user to expired trial status
      const { error } = await supabase
        .from('profiles')
        .update({
          subscription_tier: 'trial_expired',
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) {
        console.error('Error expiring trial:', error)
        return false
      }

      return true
    }

    return false
  } catch (error) {
    console.error('Error in checkAndExpireTrial:', error)
    return false
  }
}

/**
 * Convert trial user to paid plan
 */
export async function convertTrialToPaid(userId: string, newTier: SubscriptionTier): Promise<boolean> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('profiles')
      .update({
        subscription_tier: newTier,
        trial_start_date: null,
        trial_end_date: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)

    if (error) {
      console.error('Error converting trial to paid:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in convertTrialToPaid:', error)
    return false
  }
}

/**
 * Get usage stats for trial users
 */
export async function getTrialUsageStats(userId: string): Promise<{
  callsUsed: number
  callsRemaining: number
  usagePercentage: number
}> {
  try {
    const supabase = createClient()
    
    // Get current month's usage
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)
    
    const { data: usage, error } = await supabase
      .from('api_usage')
      .select('calls_made')
      .eq('user_id', userId)
      .gte('created_at', startOfMonth.toISOString())
      .single()

    const callsUsed = usage?.calls_made || 0
    const callsLimit = 1000 // Trial limit
    const callsRemaining = Math.max(0, callsLimit - callsUsed)
    const usagePercentage = Math.min(100, (callsUsed / callsLimit) * 100)

    return {
      callsUsed,
      callsRemaining,
      usagePercentage
    }
  } catch (error) {
    console.error('Error getting trial usage stats:', error)
    return {
      callsUsed: 0,
      callsRemaining: 1000,
      usagePercentage: 0
    }
  }
}

/**
 * Server-side version for middleware/API routes
 */
export async function getTrialStatusServer(userId: string): Promise<TrialStatus> {
  try {
    const supabase = await createServerClient()
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('subscription_tier, trial_start_date, trial_end_date, created_at')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      return {
        isOnTrial: false,
        daysRemaining: 0,
        isExpired: true,
        trialStartDate: null,
        trialEndDate: null,
        suggestedUpgrade: 'starter'
      }
    }

    const isOnTrial = isTrialTier(profile.subscription_tier as SubscriptionTier)
    const trialStartDate = profile.trial_start_date ? new Date(profile.trial_start_date) : null
    const trialEndDate = profile.trial_end_date ? new Date(profile.trial_end_date) : null
    
    let daysRemaining = 0
    let isExpired = false
    
    if (isOnTrial && trialStartDate) {
      daysRemaining = getTrialDaysRemaining(trialStartDate)
      isExpired = isTrialExpired(trialStartDate)
    }

    return {
      isOnTrial,
      daysRemaining,
      isExpired,
      trialStartDate,
      trialEndDate,
      suggestedUpgrade: getUpgradeTier(profile.subscription_tier as SubscriptionTier)
    }
  } catch (error) {
    console.error('Error getting trial status (server):', error)
    return {
      isOnTrial: false,
      daysRemaining: 0,
      isExpired: true,
      trialStartDate: null,
      trialEndDate: null,
      suggestedUpgrade: 'starter'
    }
  }
}
