export interface AddressData {
  label: string
  lat: number
  lng: number
  county?: string
  state?: string
  zip?: string
}

export interface AddressOption extends AddressData {
  value: string | number
  source?: string
}

export interface AddressInputProps {
  onSelect?: (address: AddressData | null) => void
  placeholder?: string
  defaultValue?: AddressOption
  className?: string
}

export interface NominatimResult {
  place_id: string
  display_name: string
  lat: string
  lon: string
  class?: string
  type?: string
  address?: {
    house_number?: string
    building?: string
    county?: string
    state?: string
    postcode?: string
  }
}

export interface AddressCacheEntry {
  id: string
  query_text: string
  formatted_address: string
  latitude: number
  longitude: number
  county?: string
  state?: string
  zip_code?: string
  source: string
  expires_at: string
  created_at: string
}
