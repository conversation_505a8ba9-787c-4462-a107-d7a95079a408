export interface ChatConversation {
  id: string
  address: string
  rule_type: string
  jurisdiction_name: string
  created_at: string
  updated_at: string
}

export interface ChatMessage {
  id: string
  conversation_id: string
  role: 'user' | 'assistant'
  content: string
  created_at: string
  metadata?: Record<string, unknown>
}

export interface NewChatData {
  address: string
  rule_type: string
  jurisdiction_name: string
  context_data?: Record<string, unknown>
}

export interface JurisdictionData {
  id: string
  name: string
  state: string
  type: string
  geometry?: any
}

export const RULE_TYPES = [
  'general',
  'fence',
  'shed',
  'deck',
  'pool',
  'driveway',
  'mailbox',
  'tree removal',
  'addition',
  'garage',
  'patio',
  'pergola',
  'other'
] as const

export type RuleType = typeof RULE_TYPES[number]

export const RULE_TYPE_LABELS: Record<RuleType, string> = {
  'general': 'General Compliance',
  'fence': 'Fence',
  'shed': 'Shed',
  'deck': 'Deck',
  'pool': 'Pool',
  'driveway': 'Driveway',
  'mailbox': 'Mailbox',
  'tree removal': 'Tree Removal',
  'addition': 'Addition',
  'garage': 'Garage',
  'patio': 'Patio',
  'pergola': 'Pergola',
  'other': 'Other'
}

// Prompt Template Types
export interface PromptTemplate {
  id: string
  name: string
  description: string | null
  template_text: string
  category: string
  is_active: boolean
  created_by: string | null
  created_at: string
  updated_at: string
}

export interface PromptTemplatesResponse {
  templates: PromptTemplate[]
  categories: string[]
}

export const PROMPT_TEMPLATE_CATEGORIES = [
  'general',
  'appraisal',
  'building',
  'fire',
  'electrical',
  'plumbing',
  'zoning',
  'safety'
] as const

export type PromptTemplateCategory = typeof PROMPT_TEMPLATE_CATEGORIES[number]

export const PROMPT_TEMPLATE_CATEGORY_LABELS: Record<PromptTemplateCategory, string> = {
  'general': 'General',
  'appraisal': 'Appraisal',
  'building': 'Building Code',
  'fire': 'Fire Safety',
  'electrical': 'Electrical',
  'plumbing': 'Plumbing',
  'zoning': 'Zoning',
  'safety': 'Safety'
}
