export interface Jurisdiction {
  id: string
  name: string
  level: 'federal' | 'state' | 'county' | 'city' | 'township'
  state_code?: string
  county_name?: string
}

export interface ComplianceSummary {
  jurisdiction_name: string
  jurisdiction_level: string
  summary: string
  citations: Citation[]
  confidence_score: number
  source_url?: string
}

export interface Citation {
  id?: string
  section: string
  title: string
  clause_id?: string
  url?: string
  regulation_text?: string
  document_title?: string
  source_url?: string
  url_verified?: boolean
  source_url_verified?: boolean
  message_id?: string
  citation_number?: number
}

export interface OrdinanceMetadata {
  id: string
  source_name: string
  source_url?: string
  document_type: string
  last_scraped?: string
  is_active: boolean
  metadata?: Record<string, unknown>
}

export interface Clause {
  id: string
  ordinance_id: string
  section_number?: string
  title?: string
  content: string
  raw_text?: string
  metadata?: Record<string, unknown>
}

export interface Tag {
  id: string
  name: string
  category?: string
  description?: string
}

export interface ClauseTag {
  clause_id: string
  tag_id: string
  confidence: number
}

export type ComplianceStatus = 'allowed' | 'restricted' | 'permit_required'

export interface ZoningInfo {
  allowedZones: string[]
  restrictions: string[]
}

export interface PermitInfo {
  required: boolean | string
  fee?: string
  office?: string
  phone?: string
  address?: string
  note?: string
  requirements?: string[]
}

export interface RegulationCallout {
  section: string
  title: string
  text: string
  url?: string
  effective_date?: string
  document_title?: string
  url_verified?: boolean
}

export interface SourceDocument {
  title: string
  url: string
  type: string
  url_verified?: boolean
}

export interface SpecificRequirements {
  setbacks?: string
  height_limits?: string
  size_limits?: string
  quantity_limits?: string
  lot_size_requirements?: string
}

// Legacy OrdinanceAnalysis interface - kept for backward compatibility
export interface LegacyOrdinanceAnalysis {
  summary: string
  permitRequired: boolean
  requirements: Requirement[]
  contactInfo: ContactInfo
  confidence: number
}

// New standardized OrdinanceAnalysis interface matching the shared function
export interface OrdinanceAnalysis {
  summary: string
  permit_required: boolean
  requirements: string[]
  prohibited_practices: string[]
  permit_process: string
  citations: string[]
  source_links: string[]
  contact_info: {
    department?: string
    phone?: string
    email?: string
    website?: string
  }
  tags: string[]
  confidence_score?: number
  error?: boolean
  error_message?: string
}

export interface Requirement {
  category: string
  requirement: string
  details: string
  citation: string
}

export interface ContactInfo {
  department?: string
  phone?: string
  email?: string
  website?: string
}

export interface ComplianceCardData {
  address: string
  ruleType: string
  jurisdiction: {
    name: string
    level: string
  }
  summary: {
    content: string
    status: ComplianceStatus
    citations: Citation[]
    sourceUrl?: string
    source_document?: SourceDocument
    zoning_info?: ZoningInfo
    permit_info?: PermitInfo
    regulation_callout?: RegulationCallout
    specific_requirements?: SpecificRequirements
    confidence_score?: number
    research_time_ms?: number
  }
}
