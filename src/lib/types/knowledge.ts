// Epic 7: Data Freshness & Knowledge Base Maintenance
// TypeScript type definitions for knowledge management

export interface DocumentStalenessTracking {
  id: string
  jurisdiction: string
  document_type: string
  current_version: string | null
  latest_available_version: string | null
  staleness_score: number
  last_checked_at: string
  update_available: boolean
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface PendingUpdate {
  id: string
  jurisdiction: string
  document_type: string
  update_type: 'version_update' | 'new_document' | 'amendment' | 'correction'
  source_url: string | null
  detected_at: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  priority: number // 1-10, 1 = highest priority
  metadata: Record<string, any>
  error_message: string | null
  processed_at: string | null
  created_at: string
  updated_at: string
}

export interface KnowledgeRefreshJob {
  id: string
  triggered_by: string | null
  job_type: 'manual' | 'scheduled' | 'automated' | 'emergency'
  scope: RefreshJobScope
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: RefreshJobProgress
  total_items: number
  processed_items: number
  started_at: string | null
  completed_at: string | null
  error_message: string | null
  result_summary: RefreshJobResult
  created_at: string
  updated_at: string
}

export interface RefreshJobScope {
  jurisdiction?: string[]
  document_type?: string[]
  specific_ids?: number[]
  include_inactive?: boolean
}

export interface RefreshJobProgress {
  current_step?: string
  percentage?: number
  current_item?: string
  steps_completed?: string[]
  estimated_completion?: string
}

export interface RefreshJobResult {
  items_processed: number
  items_updated: number
  items_added: number
  items_removed: number
  errors: RefreshJobError[]
  duration_ms: number
  performance_metrics?: {
    embedding_time_ms: number
    database_time_ms: number
    download_time_ms: number
  }
}

export interface RefreshJobError {
  item_id?: number
  error_type: string
  error_message: string
  timestamp: string
}

// Extended compliance knowledge with version tracking
export interface ComplianceKnowledgeWithVersion {
  id: number
  source_document_id: string
  document_type: string
  jurisdiction: string
  project_type_tags: string[]
  title: string | null
  content_chunk: string
  chunk_sequence: number
  embedding: number[] | null
  metadata: Record<string, any>
  source_url: string | null
  last_updated_at: string
  created_at: string
  // New version tracking fields
  document_version: string | null
  publish_date: string | null
  adoption_date: string | null
  is_active: boolean
  superseded_by: number | null
}

// API request/response types
export interface RefreshJobCreateRequest {
  job_type: 'manual' | 'emergency'
  scope: RefreshJobScope
  priority?: number
}

export interface RefreshJobCreateResponse {
  job_id: string
  status: string
  message: string
}

export interface RefreshJobStatusResponse {
  job: KnowledgeRefreshJob
  can_cancel: boolean
}

export interface StalenessReportRequest {
  jurisdiction?: string
  document_type?: string
  min_staleness_score?: number
}

export interface StalenessReportResponse {
  tracking: DocumentStalenessTracking[]
  summary: {
    total_documents: number
    stale_documents: number
    average_staleness: number
    updates_available: number
  }
}

export interface DocumentVersionsResponse {
  versions: ComplianceKnowledgeWithVersion[]
  pagination: {
    page: number
    limit: number
    total: number
    has_more: boolean
  }
}

// Staleness warning types for chat interface
export interface StalenessWarning {
  jurisdiction: string
  document_type: string
  staleness_score: number
  current_version: string | null
  message: string
  severity: 'low' | 'medium' | 'high'
  show_to_user: boolean
}

// Configuration types
export interface StalenessThresholds {
  building_code: number // months
  fire_code: number
  electrical_code: number
  plumbing_code: number
  ordinance: number
  zoning: number
  default: number
}

export interface UpdateSourceConfig {
  jurisdiction: string
  document_type: string
  source_type: 'api' | 'scraper' | 'manual'
  source_url: string
  check_frequency: 'daily' | 'weekly' | 'monthly'
  last_check: string | null
  enabled: boolean
}

// Utility types
export type RefreshJobStatus = KnowledgeRefreshJob['status']
export type UpdateType = PendingUpdate['update_type']
export type JobType = KnowledgeRefreshJob['job_type']

// Error types
export class KnowledgeRefreshError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'KnowledgeRefreshError'
  }
}

export class StalenessDetectionError extends Error {
  constructor(
    message: string,
    public jurisdiction: string,
    public document_type: string
  ) {
    super(message)
    this.name = 'StalenessDetectionError'
  }
}

// Constants
export const STALENESS_THRESHOLDS: StalenessThresholds = {
  building_code: 36,
  fire_code: 36,
  electrical_code: 36,
  plumbing_code: 36,
  ordinance: 24,
  zoning: 60,
  default: 36
}

export const REFRESH_JOB_PRIORITIES = {
  EMERGENCY: 1,
  HIGH: 3,
  NORMAL: 5,
  LOW: 7,
  BACKGROUND: 10
} as const

export const STALENESS_SEVERITY_THRESHOLDS = {
  LOW: 0.3,
  MEDIUM: 0.6,
  HIGH: 0.8
} as const
