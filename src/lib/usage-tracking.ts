import { createServerClient } from '@/lib/supabase/server'

export interface UsageStatus {
  canProceed: boolean
  currentUsage: number
  limitAmount: number
  extraCreditsAvailable: number
  subscriptionTier: string
  message?: string
}

export async function checkUsageLimit(userId: string): Promise<UsageStatus> {
  const supabase = await createServerClient()

  try {
    const { data, error } = await supabase.rpc('check_usage_limit', {
      user_id_param: userId,
    })

    if (error) {
      console.error('Error checking usage limit:', error)
      throw error
    }

    const result = data[0]
    if (!result) {
      throw new Error('No usage data found for user')
    }

    let message = ''
    if (!result.can_proceed) {
      if (result.subscription_tier === 'trial') {
        message = `You've reached your trial limit of ${result.limit_amount} messages. Upgrade to continue using Ordrly.`
      } else if (result.subscription_tier === 'starter') {
        message = `You've reached your monthly limit of ${result.limit_amount} messages. Upgrade to Professional for 2,000 messages per month.`
      } else if (result.subscription_tier === 'professional') {
        message = `You've reached your monthly limit of ${result.limit_amount} messages. Your limit will reset next month.`
      } else {
        message = 'Usage limit exceeded'
      }
    }

    return {
      canProceed: result.can_proceed,
      currentUsage: result.current_usage,
      limitAmount: result.limit_amount,
      extraCreditsAvailable: result.extra_credits_available,
      subscriptionTier: result.subscription_tier,
      message,
    }
  } catch (error) {
    console.error('Error in checkUsageLimit:', error)
    // Default to allowing usage if there's an error (fail open)
    return {
      canProceed: true,
      currentUsage: 0,
      limitAmount: -1,
      extraCreditsAvailable: 0,
      subscriptionTier: 'free',
      message: 'Unable to check usage limits',
    }
  }
}

export async function incrementUsage(userId: string): Promise<boolean> {
  const supabase = await createServerClient()

  try {
    const { data, error } = await supabase.rpc('increment_usage', {
      user_id_param: userId,
    })

    if (error) {
      console.error('Error incrementing usage:', error)
      return false
    }

    return data === true
  } catch (error) {
    console.error('Error in incrementUsage:', error)
    return false
  }
}

export async function trackUsage(userId: string): Promise<UsageStatus> {
  // First check if user can proceed
  const usageStatus = await checkUsageLimit(userId)

  if (!usageStatus.canProceed) {
    return usageStatus
  }

  // Increment usage
  const success = await incrementUsage(userId)

  if (!success) {
    return {
      ...usageStatus,
      canProceed: false,
      message: 'Failed to track usage',
    }
  }

  // Return updated status
  return {
    ...usageStatus,
    currentUsage: usageStatus.currentUsage + 1,
  }
}

export async function getUserProfile(userId: string) {
  const supabase = await createServerClient()

  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) {
    console.error('Error fetching user profile:', error)
    return null
  }

  return data
}

export function formatUsageMessage(usage: UsageStatus): string {
  if (usage.subscriptionTier === 'trial' || usage.subscriptionTier === 'starter' || usage.subscriptionTier === 'professional') {
    const totalAvailable = usage.limitAmount + usage.extraCreditsAvailable
    const totalUsed = usage.currentUsage
    const remaining = totalAvailable - totalUsed

    if (remaining <= 0) {
      return 'You have reached your search limit for this month.'
    } else if (remaining <= 2) {
      return `You have ${remaining} search${remaining === 1 ? '' : 'es'} remaining this month.`
    } else {
      return `${usage.currentUsage}/${usage.limitAmount} monthly searches used${
        usage.extraCreditsAvailable > 0 ? ` (+${usage.extraCreditsAvailable} bonus)` : ''
      }`
    }
  } else {
    return `${usage.currentUsage} searches used this month (unlimited plan)`
  }
}
