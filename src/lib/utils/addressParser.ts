/**
 * Address parsing utilities for extracting jurisdiction and state from full address strings
 */

interface ParsedAddress {
  jurisdiction: string;
  state: string;
  fullAddress: string;
}

/**
 * Parse a full address string to extract jurisdiction and state
 * Example: "6570 alward Dr., hudsonville, MI 49426" -> { jurisdiction: "hudsonville", state: "MI" }
 */
export function parseAddress(fullAddress: string): ParsedAddress {
  // Clean up the address string
  const cleaned = fullAddress.trim().replace(/\s+/g, ' ');
  
  // Common patterns for address parsing
  const patterns = [
    // Pattern 1: "Street, City, State ZIP"
    /^.+?,\s*([^,]+),\s*([A-Z]{2})\s+\d{5}(-\d{4})?$/i,
    // Pattern 2: "Street City State ZIP"
    /^.+?\s+([^,\s]+(?:\s+[^,\s]+)*),?\s+([A-Z]{2})\s+\d{5}(-\d{4})?$/i,
    // Pattern 3: "City, State"
    /^([^,]+),\s*([A-Z]{2})$/i,
    // Pattern 4: "City State"
    /^([^,\s]+(?:\s+[^,\s]+)*)\s+([A-Z]{2})$/i
  ];

  for (const pattern of patterns) {
    const match = cleaned.match(pattern);
    if (match) {
      const jurisdiction = match[1].trim();
      const state = match[2].toUpperCase();
      
      // Clean up jurisdiction name
      const cleanJurisdiction = cleanJurisdictionName(jurisdiction);
      
      return {
        jurisdiction: cleanJurisdiction,
        state,
        fullAddress: cleaned
      };
    }
  }

  // Fallback: try to extract state from end of string
  const stateMatch = cleaned.match(/\b([A-Z]{2})\b(?:\s+\d{5})?$/i);
  if (stateMatch) {
    const state = stateMatch[1].toUpperCase();
    
    // Try to extract city/jurisdiction before the state
    const beforeState = cleaned.substring(0, stateMatch.index).trim();
    const cityMatch = beforeState.match(/([^,\s]+(?:\s+[^,\s]+)*)(?:,\s*)?$/);
    
    if (cityMatch) {
      const jurisdiction = cleanJurisdictionName(cityMatch[1]);
      return {
        jurisdiction,
        state,
        fullAddress: cleaned
      };
    }
  }

  // If all else fails, return the original string as jurisdiction
  return {
    jurisdiction: cleaned,
    state: '',
    fullAddress: cleaned
  };
}

/**
 * Clean up jurisdiction names by removing common prefixes/suffixes and normalizing
 */
function cleanJurisdictionName(jurisdiction: string): string {
  let cleaned = jurisdiction.trim();
  
  // Remove common address components that might be included
  cleaned = cleaned.replace(/^\d+\s+/, ''); // Remove leading house numbers
  cleaned = cleaned.replace(/\b(street|st|avenue|ave|road|rd|drive|dr|lane|ln|court|ct|circle|cir|way|place|pl)\b/gi, '');
  
  // Normalize common jurisdiction types (keep them for search accuracy)
  cleaned = cleaned.replace(/\b(twp|township)\b/gi, 'Township');
  cleaned = cleaned.replace(/\b(charter)\b/gi, 'Charter');
  cleaned = cleaned.replace(/\b(city)\b/gi, 'City');
  cleaned = cleaned.replace(/\b(village)\b/gi, 'Village');
  cleaned = cleaned.replace(/\b(borough|boro)\b/gi, 'Borough');
  cleaned = cleaned.replace(/\b(county)\b/gi, 'County');
  
  // Clean up extra spaces and capitalize properly
  cleaned = cleaned.replace(/\s+/g, ' ').trim();
  cleaned = cleaned.split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
  
  return cleaned;
}

/**
 * Generate search query for ordinance research
 * Format: "Jurisdiction State Project Ordinance"
 */
export function generateSearchQuery(jurisdiction: string, state: string, projectType: string): string {
  return `${jurisdiction} ${state} ${projectType} ordinance`;
}

/**
 * Validate if an address string looks reasonable for parsing
 */
export function isValidAddressFormat(address: string): boolean {
  if (!address || address.trim().length < 5) {
    return false;
  }
  
  // Should contain at least some letters and potentially numbers
  const hasLetters = /[a-zA-Z]/.test(address);
  const hasReasonableLength = address.trim().length >= 5 && address.trim().length <= 200;
  
  return hasLetters && hasReasonableLength;
}

/**
 * Extract jurisdiction from various address formats for testing
 */
export function extractJurisdictionVariants(address: string): string[] {
  const parsed = parseAddress(address);
  const variants = [parsed.jurisdiction];
  
  // Add variants without jurisdiction type suffixes
  const withoutType = parsed.jurisdiction.replace(/\b(Township|Charter|City|Village|Borough|County)\b/gi, '').trim();
  if (withoutType !== parsed.jurisdiction) {
    variants.push(withoutType);
  }
  
  // Add variants with common abbreviations
  const withAbbrev = parsed.jurisdiction
    .replace(/\bTownship\b/gi, 'Twp')
    .replace(/\bCharter\b/gi, 'Charter Twp');
  if (withAbbrev !== parsed.jurisdiction) {
    variants.push(withAbbrev);
  }
  
  return [...new Set(variants)]; // Remove duplicates
}
