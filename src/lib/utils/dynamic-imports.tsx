import dynamic from 'next/dynamic'
import { ComponentType, ReactElement } from 'react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

// Loading component for dynamic imports
const DynamicLoading = () => (
  <div className="flex items-center justify-center p-8">
    <LoadingSpinner size="md" text="Loading component..." />
  </div>
)

// Error boundary for dynamic imports (currently unused but available for future use)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const DynamicError = () => (
  <div className="flex items-center justify-center p-8 text-red-600">
    <p>Failed to load component. Please try refreshing the page.</p>
  </div>
)

// Utility function to create dynamic imports with consistent loading states
export function createDynamicImport<T = Record<string, unknown>>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  options?: {
    loading?: () => ReactElement
    error?: () => ReactElement
    ssr?: boolean
  }
) {
  return dynamic(importFn, {
    loading: options?.loading || DynamicLoading,
    ssr: options?.ssr ?? true,
  })
}

// Pre-configured dynamic imports for heavy components
// Note: These are commented out until components have proper default exports
// export const DynamicFeedbackModal = createDynamicImport(
//   () => import('@/components/feedback/FeedbackModal'),
//   { ssr: false }
// )

// export const DynamicOnboardingTutorial = createDynamicImport(
//   () => import('@/components/onboarding/OnboardingTutorial'),
//   { ssr: false }
// )

// export const DynamicChatInterface = createDynamicImport(
//   () => import('@/components/compliance/ChatInterface'),
//   { ssr: false }
// )

// export const DynamicClauseBrowser = createDynamicImport(
//   () => import('@/components/compliance/ClauseBrowser'),
//   { ssr: false }
// )

// export const DynamicLocationImagery = createDynamicImport(
//   () => import('@/components/maps/LocationImagery'),
//   { ssr: false }
// )

// export const DynamicStreetView = createDynamicImport(
//   () => import('@/components/maps/StreetView'),
//   { ssr: false }
// )

// Utility for lazy loading with intersection observer
export function createLazyComponent<T = Record<string, unknown>>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  threshold = 0.1
) {
  return dynamic(
    () => {
      return new Promise<{ default: ComponentType<T> }>((resolve) => {
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                observer.disconnect()
                importFn().then(resolve)
              }
            })
          },
          { threshold }
        )

        // Create a placeholder element to observe
        const placeholder = document.createElement('div')
        document.body.appendChild(placeholder)
        observer.observe(placeholder)

        // Cleanup after 10 seconds if not intersected
        setTimeout(() => {
          observer.disconnect()
          document.body.removeChild(placeholder)
          importFn().then(resolve)
        }, 10000)
      })
    },
    {
      loading: DynamicLoading,
      ssr: false,
    }
  )
}

// Preload functions for critical components
export const preloadComponents = {
  // feedbackModal: () => import('@/components/feedback/FeedbackModal'),
  // onboardingTutorial: () => import('@/components/onboarding/OnboardingTutorial'),
  // chatInterface: () => import('@/components/compliance/ChatInterface'),
  // clauseBrowser: () => import('@/components/compliance/ClauseBrowser'),
  // locationImagery: () => import('@/components/maps/LocationImagery'),
  // streetView: () => import('@/components/maps/StreetView'),
}

// Utility to preload components on user interaction
export function preloadOnHover(componentKey?: string) {
  return {
    onMouseEnter: () => {
      // Preload functionality disabled until components are properly configured
      if (componentKey) {
        console.log(`Preload requested for: ${componentKey}`)
      }
    },
    onFocus: () => {
      // Preload functionality disabled until components are properly configured
      if (componentKey) {
        console.log(`Preload requested for: ${componentKey}`)
      }
    },
  }
}

// Bundle analyzer helper (development only)
export function logBundleSize(componentName: string) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 Loading component: ${componentName}`)
  }
}
