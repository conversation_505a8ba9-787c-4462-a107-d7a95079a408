import { z } from 'zod'

// Address validation schema
export const addressSchema = z.object({
  label: z.string().min(1, 'Address is required').max(200, 'Address too long'),
  lat: z.number().min(-90).max(90),
  lng: z.number().min(-180).max(180),
  county: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
})

// Search query validation
export const searchQuerySchema = z.object({
  address: addressSchema,
  ruleType: z.string().min(1, 'Project type is required').max(50),
  customProject: z.string().max(500).optional(),
})

// Feedback validation schema
export const feedbackSchema = z.object({
  category: z.enum(['bug', 'feature_request', 'general'], {
    errorMap: () => ({ message: 'Category must be bug, feature_request, or general' })
  }),
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(1000, 'Description too long'),
  email: z.string().email('Invalid email address').optional(),
  url: z.string().url('Invalid URL').optional(),
  userAgent: z.string().max(500).optional(),
})

// FAQ validation schema
export const faqSchema = z.object({
  question: z.string().min(1, 'Question is required').max(200, 'Question too long'),
  answer: z.string().min(1, 'Answer is required').max(2000, 'Answer too long'),
  category: z.string().min(1, 'Category is required').max(50),
  tags: z.array(z.string().max(30)).max(10, 'Too many tags'),
  is_published: z.boolean().default(true),
  sort_order: z.number().int().min(0).default(0),
})

// User profile validation
export const profileUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  email: z.string().email('Invalid email address').optional(),
  subscription_tier: z.enum(['free', 'pro', 'appraiser']).optional(),
})

// API key validation
export const apiKeySchema = z.object({
  key: z.string().min(1, 'API key is required'),
  name: z.string().min(1, 'Key name is required').max(50),
})

// Pagination validation
export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sort: z.string().max(50).optional(),
  order: z.enum(['asc', 'desc']).default('desc'),
})

// Search filters validation
export const searchFiltersSchema = z.object({
  query: z.string().max(200).optional(),
  category: z.string().max(50).optional(),
  tags: z.array(z.string().max(30)).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
})

// Compliance summary validation
export const complianceSummarySchema = z.object({
  address: addressSchema,
  ruleType: z.string().min(1).max(50),
  jurisdiction: z.object({
    name: z.string(),
    level: z.string(),
    id: z.string().optional(),
  }),
  summary: z.string().min(1),
  confidence_score: z.number().min(0).max(100),
  citations: z.array(z.object({
    title: z.string(),
    url: z.string().url().optional(),
    section: z.string().optional(),
  })).optional(),
})

// Utility function to validate and parse data
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string } {
  try {
    const result = schema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
      return { success: false, error: errorMessage }
    }
    return { success: false, error: 'Validation failed' }
  }
}

// Utility function for safe parsing
export function safeParseData<T>(schema: z.ZodSchema<T>, data: unknown): z.SafeParseReturnType<unknown, T> {
  return schema.safeParse(data)
}
