-- Epic 7: Scheduled Background Jobs Configuration
-- Cron jobs for knowledge base maintenance
-- Created: 2025-01-19

-- Enable the pg_cron extension if not already enabled
-- This should be run by a superuser or in the Supabase dashboard
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- =====================================================
-- MONTHLY STALENESS CHECK
-- =====================================================

-- Schedule monthly staleness check (1st of every month at midnight UTC)
SELECT cron.schedule(
  'knowledge-staleness-check-monthly',
  '0 0 1 * *',
  $$
  SELECT net.http_post(
    url := current_setting('app.supabase_url') || '/functions/v1/knowledge-staleness-check',
    headers := jsonb_build_object(
      'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key'),
      'Content-Type', 'application/json'
    ),
    body := jsonb_build_object(
      'force_update', false,
      'scheduled', true
    )
  );
  $$
);

-- =====================================================
-- WEEKLY UPDATE DETECTION
-- =====================================================

-- Schedule weekly update detection (every Monday at 2 AM UTC)
SELECT cron.schedule(
  'knowledge-update-detection-weekly',
  '0 2 * * 1',
  $$
  SELECT net.http_post(
    url := current_setting('app.supabase_url') || '/functions/v1/knowledge-staleness-check',
    headers := jsonb_build_object(
      'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key'),
      'Content-Type', 'application/json'
    ),
    body := jsonb_build_object(
      'force_update', true,
      'scheduled', true,
      'check_type', 'update_detection'
    )
  );
  $$
);

-- =====================================================
-- CLEANUP OLD JOBS
-- =====================================================

-- Schedule cleanup of old refresh jobs (every Sunday at 3 AM UTC)
-- Keep jobs for 30 days, then delete completed/failed jobs
SELECT cron.schedule(
  'knowledge-jobs-cleanup',
  '0 3 * * 0',
  $$
  DELETE FROM knowledge_refresh_jobs 
  WHERE status IN ('completed', 'failed', 'cancelled')
    AND created_at < NOW() - INTERVAL '30 days';
  $$
);

-- =====================================================
-- CLEANUP OLD PENDING UPDATES
-- =====================================================

-- Schedule cleanup of old pending updates (every Sunday at 4 AM UTC)
-- Remove completed or failed updates older than 7 days
SELECT cron.schedule(
  'pending-updates-cleanup',
  '0 4 * * 0',
  $$
  DELETE FROM pending_updates 
  WHERE status IN ('completed', 'failed', 'cancelled')
    AND created_at < NOW() - INTERVAL '7 days';
  $$
);

-- =====================================================
-- STALENESS TRACKING MAINTENANCE
-- =====================================================

-- Schedule staleness tracking maintenance (every day at 1 AM UTC)
-- Update staleness scores for all tracked documents
SELECT cron.schedule(
  'staleness-tracking-maintenance',
  '0 1 * * *',
  $$
  SELECT update_staleness_tracking();
  $$
);

-- =====================================================
-- CRON JOB MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to list all knowledge-related cron jobs
CREATE OR REPLACE FUNCTION list_knowledge_cron_jobs()
RETURNS TABLE (
  jobid bigint,
  schedule text,
  command text,
  nodename text,
  nodeport integer,
  database text,
  username text,
  active boolean,
  jobname text
) AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM cron.job 
  WHERE jobname LIKE 'knowledge-%' 
     OR jobname LIKE 'pending-updates-%'
     OR jobname LIKE 'staleness-%';
END;
$$ LANGUAGE plpgsql;

-- Function to enable/disable knowledge cron jobs
CREATE OR REPLACE FUNCTION toggle_knowledge_cron_jobs(enable_jobs boolean)
RETURNS text AS $$
DECLARE
  job_record RECORD;
  result_text text := '';
BEGIN
  FOR job_record IN 
    SELECT jobid, jobname FROM cron.job 
    WHERE jobname LIKE 'knowledge-%' 
       OR jobname LIKE 'pending-updates-%'
       OR jobname LIKE 'staleness-%'
  LOOP
    IF enable_jobs THEN
      PERFORM cron.alter_job(job_record.jobid, active := true);
      result_text := result_text || 'Enabled: ' || job_record.jobname || E'\n';
    ELSE
      PERFORM cron.alter_job(job_record.jobid, active := false);
      result_text := result_text || 'Disabled: ' || job_record.jobname || E'\n';
    END IF;
  END LOOP;
  
  RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- Function to manually trigger staleness check
CREATE OR REPLACE FUNCTION trigger_manual_staleness_check(
  jurisdiction_filter text DEFAULT NULL,
  document_type_filter text DEFAULT NULL
)
RETURNS text AS $$
DECLARE
  response_data jsonb;
BEGIN
  SELECT net.http_post(
    url := current_setting('app.supabase_url') || '/functions/v1/knowledge-staleness-check',
    headers := jsonb_build_object(
      'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key'),
      'Content-Type', 'application/json'
    ),
    body := jsonb_build_object(
      'jurisdiction', jurisdiction_filter,
      'document_type', document_type_filter,
      'force_update', true,
      'manual_trigger', true
    )
  ) INTO response_data;
  
  RETURN 'Staleness check triggered. Response: ' || response_data::text;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- MONITORING AND ALERTING
-- =====================================================

-- Function to check cron job health
CREATE OR REPLACE FUNCTION check_cron_job_health()
RETURNS TABLE (
  job_name text,
  last_run_status text,
  last_run_time timestamptz,
  next_run_time timestamptz,
  is_healthy boolean,
  health_message text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.jobname::text,
    CASE 
      WHEN j.active THEN 'active'
      ELSE 'inactive'
    END::text as last_run_status,
    NULL::timestamptz as last_run_time, -- pg_cron doesn't store execution history by default
    cron.schedule_to_next_run(j.schedule)::timestamptz as next_run_time,
    j.active as is_healthy,
    CASE 
      WHEN j.active THEN 'Job is active and scheduled'
      ELSE 'Job is inactive'
    END::text as health_message
  FROM cron.job j
  WHERE j.jobname LIKE 'knowledge-%' 
     OR j.jobname LIKE 'pending-updates-%'
     OR j.jobname LIKE 'staleness-%';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CONFIGURATION SETTINGS
-- =====================================================

-- Set configuration parameters for cron jobs
-- These should be set in the Supabase dashboard or via SQL
/*
ALTER DATABASE postgres SET app.supabase_url = 'https://your-project.supabase.co';
ALTER DATABASE postgres SET app.supabase_service_role_key = 'your-service-role-key';
*/

-- =====================================================
-- DEPLOYMENT NOTES
-- =====================================================

/*
DEPLOYMENT INSTRUCTIONS:

1. Enable pg_cron extension in Supabase dashboard:
   - Go to Database > Extensions
   - Enable pg_cron extension

2. Set configuration parameters:
   ALTER DATABASE postgres SET app.supabase_url = 'https://qxiryfbdruydrofclmvz.supabase.co';
   ALTER DATABASE postgres SET app.supabase_service_role_key = 'your-service-role-key';

3. Deploy Edge Functions:
   supabase functions deploy knowledge-staleness-check
   supabase functions deploy knowledge-auto-refresh

4. Run this SQL file to create cron jobs:
   psql -h your-host -U postgres -d postgres -f cron-jobs.sql

5. Verify jobs are created:
   SELECT * FROM list_knowledge_cron_jobs();

6. Monitor job health:
   SELECT * FROM check_cron_job_health();

MANUAL OPERATIONS:

- Disable all jobs: SELECT toggle_knowledge_cron_jobs(false);
- Enable all jobs: SELECT toggle_knowledge_cron_jobs(true);
- Manual staleness check: SELECT trigger_manual_staleness_check();
- Check job status: SELECT * FROM check_cron_job_health();

TROUBLESHOOTING:

- If jobs fail, check Edge Function logs in Supabase dashboard
- Verify configuration parameters are set correctly
- Ensure Edge Functions are deployed and accessible
- Check network connectivity from database to Edge Functions
*/
