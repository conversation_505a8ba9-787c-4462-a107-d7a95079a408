import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatRequest {
  conversation_id: string
  user_message: string
  conversation_context: {
    address: string
    rule_type: string
    jurisdiction_name: string
    context_data?: Record<string, unknown>
  }
}

interface AIResponseData {
  content: string
  citations: Array<{
    title: string
    section: string
    url?: string
    document_type?: string
  }>
  source_url?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify request method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { conversation_id, user_message, conversation_context }: ChatRequest = await req.json()

    if (!conversation_id || !user_message || !conversation_context) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify conversation ownership
    const { data: conversation, error: convError } = await supabase
      .from('chat_conversations')
      .select('id, user_id')
      .eq('id', conversation_id)
      .eq('user_id', user.id)
      .single()

    if (convError || !conversation) {
      return new Response(
        JSON.stringify({ error: 'Conversation not found or access denied' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get conversation history for context
    const { data: messageHistory } = await supabase
      .from('chat_messages')
      .select('role, content')
      .eq('conversation_id', conversation_id)
      .order('created_at', { ascending: true })
      .limit(10) // Limit to last 10 messages for context

    // Generate AI response
    const aiResponse = await generateAIResponse(
      conversation_context,
      user_message,
      messageHistory || []
    )

    // Log the AI generation event
    await supabase.from('automation_logs').insert({
      module: 'chat',
      level: 'info',
      message: 'AI response generated via Edge Function',
      user_id: user.id,
      metadata: {
        conversation_id,
        message_length: user_message.length,
        response_length: aiResponse.content.length,
        citation_count: aiResponse.citations.length,
        timestamp: new Date().toISOString(),
        source: 'edge-function'
      }
    })

    return new Response(
      JSON.stringify(aiResponse),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Edge function error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function generateAIResponse(
  context: ChatRequest['conversation_context'],
  userMessage: string,
  messageHistory: Array<{ role: string; content: string }>
): Promise<AIResponseData> {
  try {
    // Validate OpenAI API key
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured')
    }

    // Initialize Supabase client for RAG queries
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Get relevant compliance documents using RAG
    const relevantDocs = await getRelevantComplianceDocs(
      supabase,
      userMessage,
      context.jurisdiction_name,
      context.rule_type,
      openaiApiKey
    )

    // Build context from relevant documents
    const ragContext = relevantDocs.length > 0
      ? `\n\nRelevant compliance documents for ${context.jurisdiction_name}:\n` +
        relevantDocs.map((doc, index) =>
          `[${index + 1}] ${doc.title || 'Compliance Document'} - ${doc.jurisdiction}\n` +
          `Content: ${doc.content_chunk}\n` +
          `Source: ${doc.source_url || 'Local regulations'}\n`
        ).join('\n')
      : '\n\nNote: No specific compliance documents found for this jurisdiction. Provide general guidance and recommend contacting local authorities.'

    const messages = [
      {
        role: 'system',
        content: `You are an expert building code and ordinance compliance assistant. You're helping a user understand regulations for their ${context.rule_type} project at ${context.address} in ${context.jurisdiction_name}.

Context from previous analysis:
${JSON.stringify(context.context_data, null, 2)}
${ragContext}

Guidelines:
- Base your response PRIMARILY on the relevant compliance documents provided above
- Reference specific documents using citation markers [1], [2], etc. that correspond to the numbered documents
- If no relevant documents are provided, give general guidance and strongly recommend contacting local authorities
- Provide specific, actionable advice based on the local ordinances when available
- Highlight safety considerations
- Suggest next steps for permits or approvals
- Keep responses concise but thorough
- Use a helpful, professional tone
- When referencing specific regulations, use the format: "According to [1] Building Code Section 123.4..."
- Always cite your sources when making specific regulatory claims
- If information is not available in the provided documents, clearly state this limitation`
      },
      ...messageHistory,
      {
        role: 'user',
        content: userMessage
      }
    ]

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages,
        temperature: 0.3,
        max_tokens: 1000
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    const aiResponse = data.choices[0]?.message?.content

    if (!aiResponse) {
      throw new Error('No response from AI')
    }

    // Generate citations from RAG results
    const citations = relevantDocs.map((doc, index) => ({
      title: doc.title || `${context.jurisdiction_name} Compliance Document`,
      section: doc.metadata?.section || 'General Regulations',
      document_type: doc.metadata?.document_type || 'Municipal Code',
      url: doc.source_url || `https://${context.jurisdiction_name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.gov/building-codes`,
      similarity: doc.similarity,
      jurisdiction: doc.jurisdiction
    }))

    // Add fallback citations if no RAG results
    if (citations.length === 0 && context.jurisdiction_name && context.jurisdiction_name !== 'TBD') {
      citations.push({
        title: `${context.jurisdiction_name} Building Regulations`,
        section: 'General Requirements',
        document_type: 'Municipal Code',
        url: `https://${context.jurisdiction_name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.gov/building-department`,
        similarity: 0.0,
        jurisdiction: context.jurisdiction_name
      })
    }

    return {
      content: aiResponse,
      citations,
      source_url: citations[0]?.url || `https://${context.jurisdiction_name.toLowerCase().replace(/[^a-z0-9]/g, '-')}.gov/building-department`
    }

  } catch (error) {
    console.error('AI response generation failed:', error)
    return {
      content: "I apologize, but I'm having trouble generating a response right now. Please try again in a moment, or contact local authorities directly for specific guidance on your project.",
      citations: [],
      source_url: undefined
    }
  }
}

/**
 * Get relevant compliance documents using vector search
 */
async function getRelevantComplianceDocs(
  supabase: any,
  userMessage: string,
  jurisdiction: string,
  projectType: string,
  openaiApiKey: string
): Promise<Array<{
  id: number
  content_chunk: string
  jurisdiction: string
  project_type_tags: string[]
  title: string
  source_url: string
  similarity: number
  metadata: any
}>> {
  try {
    // Generate embedding for the user message
    const embedding = await generateEmbedding(userMessage, openaiApiKey)
    if (!embedding) {
      console.warn('Failed to generate embedding for user message')
      return []
    }

    // Call the RAG function with jurisdiction filtering
    const { data: relevantDocs, error } = await supabase.rpc('get_relevant_compliance_docs', {
      query_embedding: embedding,
      similarity_threshold: 0.6, // Lower threshold for better recall
      match_count: 5,
      target_jurisdiction: jurisdiction !== 'TBD' ? jurisdiction : null,
      target_project_type: projectType !== 'general' ? projectType : null
    })

    if (error) {
      console.error('Error fetching relevant compliance docs:', error)
      return []
    }

    console.log(`Found ${relevantDocs?.length || 0} relevant compliance documents for jurisdiction: ${jurisdiction}`)
    return relevantDocs || []

  } catch (error) {
    console.error('Error in getRelevantComplianceDocs:', error)
    return []
  }
}

/**
 * Generate embedding using OpenAI API
 */
async function generateEmbedding(text: string, openaiApiKey: string): Promise<number[] | null> {
  try {
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'text-embedding-3-small',
        input: text,
        encoding_format: 'float'
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI Embeddings API error: ${response.status}`)
    }

    const data = await response.json()
    return data.data[0]?.embedding || null

  } catch (error) {
    console.error('Error generating embedding:', error)
    return null
  }
}
