import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface StalenessCheckRequest {
  jurisdiction?: string
  document_type?: string
  force_update?: boolean
}

interface StalenessCheckResult {
  total_checked: number
  stale_documents: number
  updates_detected: number
  errors: string[]
  execution_time_ms: number
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const startTime = Date.now()
    const errors: string[] = []
    let totalChecked = 0
    let staleDocuments = 0
    let updatesDetected = 0

    // Parse request body for filters
    let filters: StalenessCheckRequest = {}
    if (req.method === 'POST') {
      try {
        filters = await req.json()
      } catch (error) {
        console.warn('Failed to parse request body, using defaults:', error)
      }
    }

    console.log('Starting staleness check with filters:', filters)

    // Step 1: Update staleness tracking for all active documents
    try {
      const { error: updateError } = await supabase.rpc('update_staleness_tracking')
      if (updateError) {
        throw new Error(`Failed to update staleness tracking: ${updateError.message}`)
      }
      console.log('✅ Updated staleness tracking')
    } catch (error) {
      const errorMsg = `Staleness tracking update failed: ${error.message}`
      errors.push(errorMsg)
      console.error('❌', errorMsg)
    }

    // Step 2: Get current staleness data with filters
    let query = supabase
      .from('document_staleness_tracking')
      .select('*')

    if (filters.jurisdiction) {
      query = query.eq('jurisdiction', filters.jurisdiction)
    }
    if (filters.document_type) {
      query = query.eq('document_type', filters.document_type)
    }

    const { data: stalenessData, error: fetchError } = await query

    if (fetchError) {
      throw new Error(`Failed to fetch staleness data: ${fetchError.message}`)
    }

    totalChecked = stalenessData?.length || 0
    staleDocuments = stalenessData?.filter(item => item.staleness_score > 0.3).length || 0

    console.log(`📊 Checked ${totalChecked} documents, found ${staleDocuments} stale`)

    // Step 3: Check for available updates (simulated for now)
    // In a real implementation, this would check external sources for new versions
    for (const item of stalenessData || []) {
      try {
        // Simulate update detection logic
        const shouldCheckForUpdates = 
          item.staleness_score > 0.5 || // Very stale documents
          filters.force_update || // Forced update check
          !item.latest_available_version // Never checked before

        if (shouldCheckForUpdates) {
          // Simulate checking for updates (placeholder)
          const hasUpdate = await simulateUpdateCheck(item.jurisdiction, item.document_type)
          
          if (hasUpdate) {
            // Update the tracking record
            const { error: updateError } = await supabase
              .from('document_staleness_tracking')
              .update({
                update_available: true,
                latest_available_version: `${item.current_version || 'Unknown'}_updated`,
                last_checked_at: new Date().toISOString()
              })
              .eq('id', item.id)

            if (updateError) {
              errors.push(`Failed to update tracking for ${item.jurisdiction}/${item.document_type}: ${updateError.message}`)
            } else {
              updatesDetected++
              console.log(`🔄 Update detected for ${item.jurisdiction}/${item.document_type}`)
            }
          }
        }
      } catch (error) {
        const errorMsg = `Update check failed for ${item.jurisdiction}/${item.document_type}: ${error.message}`
        errors.push(errorMsg)
        console.error('❌', errorMsg)
      }
    }

    // Step 4: Create pending update records for detected updates
    if (updatesDetected > 0) {
      try {
        const pendingUpdates = stalenessData
          ?.filter(item => item.update_available)
          .map(item => ({
            jurisdiction: item.jurisdiction,
            document_type: item.document_type,
            update_type: 'version_update' as const,
            status: 'pending' as const,
            priority: item.staleness_score > 0.8 ? 1 : 5, // High priority for very stale docs
            metadata: {
              current_version: item.current_version,
              detected_version: item.latest_available_version,
              staleness_score: item.staleness_score,
              auto_detected: true
            }
          }))

        if (pendingUpdates && pendingUpdates.length > 0) {
          const { error: insertError } = await supabase
            .from('pending_updates')
            .upsert(pendingUpdates, {
              onConflict: 'jurisdiction,document_type',
              ignoreDuplicates: false
            })

          if (insertError) {
            errors.push(`Failed to create pending updates: ${insertError.message}`)
          } else {
            console.log(`📝 Created ${pendingUpdates.length} pending update records`)
          }
        }
      } catch (error) {
        const errorMsg = `Failed to create pending updates: ${error.message}`
        errors.push(errorMsg)
        console.error('❌', errorMsg)
      }
    }

    const executionTime = Date.now() - startTime

    const result: StalenessCheckResult = {
      total_checked: totalChecked,
      stale_documents: staleDocuments,
      updates_detected: updatesDetected,
      errors,
      execution_time_ms: executionTime
    }

    console.log('✅ Staleness check completed:', result)

    return new Response(
      JSON.stringify({
        success: true,
        result,
        message: `Staleness check completed. Checked ${totalChecked} documents, found ${staleDocuments} stale, detected ${updatesDetected} updates.`
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('❌ Staleness check failed:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        details: 'Check function logs for more information'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

// Simulate checking for document updates
// In a real implementation, this would check actual sources
async function simulateUpdateCheck(jurisdiction: string, documentType: string): Promise<boolean> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // Simulate update detection logic
  // For demo purposes, randomly detect updates for some documents
  const hash = jurisdiction.length + documentType.length
  const shouldHaveUpdate = hash % 7 === 0 // ~14% chance of update
  
  return shouldHaveUpdate
}

/* Deno.cron("knowledge-staleness-check", "0 0 1 * *", async () => {
  console.log("🕐 Running scheduled staleness check...")
  
  try {
    const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/knowledge-staleness-check`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_ANON_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ force_update: false })
    })
    
    const result = await response.json()
    console.log("✅ Scheduled staleness check completed:", result)
  } catch (error) {
    console.error("❌ Scheduled staleness check failed:", error)
  }
}) */
