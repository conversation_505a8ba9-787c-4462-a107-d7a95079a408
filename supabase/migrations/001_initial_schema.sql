-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis WITH SCHEMA extensions;
GRANT USAGE ON SCHEMA extensions TO anon, authenticated;

-- Regions table for jurisdiction boundaries
CREATE TABLE public.regions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    level TEXT NOT NULL CHECK (level IN ('federal', 'state', 'county', 'city', 'township')),
    state_code TEXT,
    county_name TEXT,
    fips_code TEXT,
    geometry GEOMETRY(MULTIPOLYGON, 4326) NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create spatial index for efficient geographic queries
CREATE INDEX idx_regions_geometry ON public.regions USING GIST (geometry);
CREATE INDEX idx_regions_level ON public.regions (level);
CREATE INDEX idx_regions_state ON public.regions (state_code);

-- Ordinances metadata table
CREATE TABLE public.ordinances_metadata (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    region_id UUID REFERENCES public.regions(id) ON DELETE CASCADE,
    source_name TEXT NOT NULL,
    source_url TEXT,
    document_type TEXT,
    last_scraped TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Clauses table for individual ordinance rules
CREATE TABLE public.clauses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ordinance_id UUID REFERENCES public.ordinances_metadata(id) ON DELETE CASCADE,
    section_number TEXT,
    title TEXT,
    content TEXT NOT NULL,
    raw_text TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tags for rule classification
CREATE TABLE public.tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    category TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Junction table for clause tags
CREATE TABLE public.clause_tags (
    clause_id UUID REFERENCES public.clauses(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
    confidence DECIMAL(3,2) DEFAULT 1.0,
    PRIMARY KEY (clause_id, tag_id)
);

-- AI-generated summaries cache
CREATE TABLE public.compliance_summaries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    region_id UUID REFERENCES public.regions(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
    summary TEXT NOT NULL,
    citations JSONB DEFAULT '[]',
    confidence_score DECIMAL(3,2),
    model_version TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(region_id, tag_id)
);

-- Address lookup cache
CREATE TABLE public.address_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    query_text TEXT NOT NULL,
    formatted_address TEXT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    county TEXT,
    state TEXT,
    zip_code TEXT,
    source TEXT DEFAULT 'nominatim',
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_address_cache_query ON public.address_cache (query_text);
CREATE INDEX idx_address_cache_expires ON public.address_cache (expires_at);

-- PostGIS Functions
-- Function to find jurisdictions for a given lat/lng
CREATE OR REPLACE FUNCTION public.get_jurisdictions(lat DECIMAL, lng DECIMAL)
RETURNS TABLE (
    id UUID,
    name TEXT,
    level TEXT,
    state_code TEXT,
    county_name TEXT
)
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT r.id, r.name, r.level, r.state_code, r.county_name
    FROM public.regions r
    WHERE ST_Contains(r.geometry, ST_SetSRID(ST_Point(lng, lat), 4326))
    ORDER BY
        CASE r.level
            WHEN 'city' THEN 1
            WHEN 'township' THEN 2
            WHEN 'county' THEN 3
            WHEN 'state' THEN 4
            WHEN 'federal' THEN 5
        END;
$$;

-- Function to get compliance summary for a location and rule type
CREATE OR REPLACE FUNCTION public.get_compliance_summary(
    lat DECIMAL,
    lng DECIMAL,
    rule_tag TEXT
)
RETURNS TABLE (
    jurisdiction_name TEXT,
    jurisdiction_level TEXT,
    summary TEXT,
    citations JSONB,
    confidence_score DECIMAL
)
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT
        r.name as jurisdiction_name,
        r.level as jurisdiction_level,
        cs.summary,
        cs.citations,
        cs.confidence_score
    FROM public.regions r
    JOIN public.compliance_summaries cs ON r.id = cs.region_id
    JOIN public.tags t ON cs.tag_id = t.id
    WHERE ST_Contains(r.geometry, ST_SetSRID(ST_Point(lng, lat), 4326))
    AND t.name = rule_tag
    ORDER BY
        CASE r.level
            WHEN 'city' THEN 1
            WHEN 'township' THEN 2
            WHEN 'county' THEN 3
            WHEN 'state' THEN 4
            WHEN 'federal' THEN 5
        END
    LIMIT 1;
$$;

-- Row Level Security (RLS)
-- Enable RLS on all tables
ALTER TABLE public.regions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ordinances_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clauses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clause_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.compliance_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.address_cache ENABLE ROW LEVEL SECURITY;

-- Public read access for core data
CREATE POLICY "Public read access" ON public.regions FOR SELECT USING (true);
CREATE POLICY "Public read access" ON public.ordinances_metadata FOR SELECT USING (true);
CREATE POLICY "Public read access" ON public.clauses FOR SELECT USING (true);
CREATE POLICY "Public read access" ON public.tags FOR SELECT USING (true);
CREATE POLICY "Public read access" ON public.clause_tags FOR SELECT USING (true);
CREATE POLICY "Public read access" ON public.compliance_summaries FOR SELECT USING (true);
CREATE POLICY "Public read access" ON public.address_cache FOR SELECT USING (true);
