-- Create ordinance_sources table for storing top 5 search results
CREATE TABLE IF NOT EXISTS public.ordinance_sources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  jurisdiction_name TEXT NOT NULL,
  rule_type TEXT NOT NULL,
  search_query TEXT NOT NULL,
  source_url TEXT NOT NULL,
  source_title TEXT,
  source_content TEXT, -- Full extracted text from PDF/HTML
  source_type TEXT CHECK (source_type IN ('pdf', 'html', 'other')),
  rank INTEGER CHECK (rank >= 1 AND rank <= 5),
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique combination of jurisdiction, rule type, and rank
  UNIQUE(jurisdiction_name, rule_type, rank)
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_ordinance_sources_jurisdiction_rule 
ON public.ordinance_sources (jurisdiction_name, rule_type);

CREATE INDEX IF NOT EXISTS idx_ordinance_sources_last_updated 
ON public.ordinance_sources (last_updated);

CREATE INDEX IF NOT EXISTS idx_ordinance_sources_rank 
ON public.ordinance_sources (rank);

-- Enable RLS (Row Level Security)
ALTER TABLE public.ordinance_sources ENABLE ROW LEVEL SECURITY;

-- Create policy to allow read access to all users
CREATE POLICY "Allow read access to ordinance sources" ON public.ordinance_sources
FOR SELECT USING (true);

-- Create policy to allow insert/update for service role
CREATE POLICY "Allow insert/update for service role" ON public.ordinance_sources
FOR ALL USING (auth.role() = 'service_role');
