-- Add privacy control fields to profiles table
-- This migration adds privacy preferences and retention settings

-- Add privacy-related columns to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{
  "save_search_history": true,
  "save_chat_history": true,
  "retention_days": 365,
  "auto_delete_enabled": false,
  "export_enabled": true
}'::jsonb;

ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS last_cleanup_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create privacy_exports table for tracking data exports
CREATE TABLE IF NOT EXISTS public.privacy_exports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  export_type TEXT NOT NULL CHECK (export_type IN ('full', 'searches', 'chats')),
  file_format TEXT NOT NULL CHECK (file_format IN ('json', 'csv')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  download_url TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient user lookups
CREATE INDEX IF NOT EXISTS idx_privacy_exports_user_id ON public.privacy_exports (user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_exports_status ON public.privacy_exports (status);
CREATE INDEX IF NOT EXISTS idx_privacy_exports_expires ON public.privacy_exports (expires_at);

-- Enable RLS on privacy_exports table
ALTER TABLE public.privacy_exports ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for privacy_exports - users can only access their own exports
CREATE POLICY "Users can manage their own exports" ON public.privacy_exports
  FOR ALL USING (auth.uid() = user_id);

-- Create function to clean up expired data based on user preferences
CREATE OR REPLACE FUNCTION cleanup_user_data()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_record RECORD;
  retention_cutoff TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Loop through users with auto-delete enabled
  FOR user_record IN 
    SELECT id, privacy_settings, last_cleanup_at
    FROM public.profiles 
    WHERE (privacy_settings->>'auto_delete_enabled')::boolean = true
    AND (last_cleanup_at IS NULL OR last_cleanup_at < NOW() - INTERVAL '1 day')
  LOOP
    -- Calculate retention cutoff date
    retention_cutoff := NOW() - INTERVAL '1 day' * (user_record.privacy_settings->>'retention_days')::integer;
    
    -- Delete old search history if enabled
    IF (user_record.privacy_settings->>'save_search_history')::boolean = false OR 
       (user_record.privacy_settings->>'auto_delete_enabled')::boolean = true THEN
      DELETE FROM public.search_history 
      WHERE user_id = user_record.id 
      AND created_at < retention_cutoff;
    END IF;
    
    -- Delete old chat data if enabled
    IF (user_record.privacy_settings->>'save_chat_history')::boolean = false OR 
       (user_record.privacy_settings->>'auto_delete_enabled')::boolean = true THEN
      -- Delete messages first (foreign key constraint)
      DELETE FROM public.chat_messages 
      WHERE conversation_id IN (
        SELECT id FROM public.chat_conversations 
        WHERE user_id = user_record.id 
        AND created_at < retention_cutoff
      );
      
      -- Then delete conversations
      DELETE FROM public.chat_conversations 
      WHERE user_id = user_record.id 
      AND created_at < retention_cutoff;
    END IF;
    
    -- Update last cleanup timestamp
    UPDATE public.profiles 
    SET last_cleanup_at = NOW() 
    WHERE id = user_record.id;
  END LOOP;
  
  -- Clean up expired export files
  DELETE FROM public.privacy_exports 
  WHERE expires_at < NOW() 
  AND status = 'completed';
END;
$$;

-- Create function to get user's data for export
CREATE OR REPLACE FUNCTION get_user_export_data(user_uuid UUID, export_type TEXT DEFAULT 'full')
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB := '{}';
  search_data JSONB;
  chat_data JSONB;
  profile_data JSONB;
BEGIN
  -- Verify user exists and get profile
  SELECT to_jsonb(p.*) INTO profile_data
  FROM public.profiles p
  WHERE p.id = user_uuid;
  
  IF profile_data IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;
  
  -- Always include basic profile info
  result := jsonb_build_object(
    'user_id', user_uuid,
    'export_date', NOW(),
    'export_type', export_type,
    'profile', profile_data
  );
  
  -- Include search history if requested
  IF export_type IN ('full', 'searches') THEN
    SELECT jsonb_agg(to_jsonb(s.*)) INTO search_data
    FROM public.search_history s
    WHERE s.user_id = user_uuid
    ORDER BY s.created_at DESC;
    
    result := result || jsonb_build_object('search_history', COALESCE(search_data, '[]'::jsonb));
  END IF;
  
  -- Include chat history if requested
  IF export_type IN ('full', 'chats') THEN
    SELECT jsonb_agg(
      jsonb_build_object(
        'conversation', to_jsonb(c.*),
        'messages', (
          SELECT jsonb_agg(to_jsonb(m.*) ORDER BY m.created_at)
          FROM public.chat_messages m
          WHERE m.conversation_id = c.id
        )
      )
    ) INTO chat_data
    FROM public.chat_conversations c
    WHERE c.user_id = user_uuid
    ORDER BY c.created_at DESC;
    
    result := result || jsonb_build_object('chat_history', COALESCE(chat_data, '[]'::jsonb));
  END IF;
  
  RETURN result;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION cleanup_user_data() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_export_data(UUID, TEXT) TO authenticated;
