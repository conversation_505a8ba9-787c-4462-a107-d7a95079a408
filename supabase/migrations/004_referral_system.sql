-- Epic 4 Phase 1: Referral System Database Schema
-- Migration: 004_referral_system.sql

-- Add referral fields to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS referral_code TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS referred_by UUID REFERENCES public.profiles(id);

-- Create referrals tracking table
CREATE TABLE IF NOT EXISTS public.referrals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  referrer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  referee_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  referral_code TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'verified', 'credited')),
  credits_awarded INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE,
  credited_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(referrer_id, referee_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_referrals_referrer ON public.referrals(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referrals_referee ON public.referrals(referee_id);
CREATE INDEX IF NOT EXISTS idx_referrals_code ON public.referrals(referral_code);
CREATE INDEX IF NOT EXISTS idx_profiles_referral_code ON public.profiles(referral_code);

-- Function to generate unique referral codes
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  exists BOOLEAN;
BEGIN
  LOOP
    -- Generate 8-character alphanumeric code
    code := upper(substring(encode(gen_random_bytes(6), 'base64') from 1 for 8));
    -- Remove special characters and make it URL-safe
    code := replace(replace(replace(code, '+', ''), '/', ''), '=', '');
    
    -- Check if code already exists
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE referral_code = code) INTO exists;
    
    -- If unique, return the code
    IF NOT exists THEN
      RETURN code;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get or create referral code for user
CREATE OR REPLACE FUNCTION get_or_create_referral_code(user_id_param UUID)
RETURNS TEXT AS $$
DECLARE
  existing_code TEXT;
  new_code TEXT;
BEGIN
  -- Check if user already has a referral code
  SELECT referral_code INTO existing_code 
  FROM public.profiles 
  WHERE id = user_id_param;
  
  -- If code exists, return it
  IF existing_code IS NOT NULL THEN
    RETURN existing_code;
  END IF;
  
  -- Generate new code
  new_code := generate_referral_code();
  
  -- Update user profile with new code
  UPDATE public.profiles 
  SET referral_code = new_code 
  WHERE id = user_id_param;
  
  RETURN new_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process referral signup
CREATE OR REPLACE FUNCTION process_referral_signup(
  referee_id_param UUID,
  referral_code_param TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  referrer_id_val UUID;
  referral_exists BOOLEAN;
BEGIN
  -- Find referrer by code
  SELECT id INTO referrer_id_val 
  FROM public.profiles 
  WHERE referral_code = referral_code_param;
  
  -- If no referrer found, return false
  IF referrer_id_val IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Check if referral already exists
  SELECT EXISTS(
    SELECT 1 FROM public.referrals 
    WHERE referrer_id = referrer_id_val AND referee_id = referee_id_param
  ) INTO referral_exists;
  
  -- If referral already exists, return false
  IF referral_exists THEN
    RETURN FALSE;
  END IF;
  
  -- Update referee profile with referrer
  UPDATE public.profiles 
  SET referred_by = referrer_id_val 
  WHERE id = referee_id_param;
  
  -- Create referral record
  INSERT INTO public.referrals (
    referrer_id, 
    referee_id, 
    referral_code, 
    status
  ) VALUES (
    referrer_id_val, 
    referee_id_param, 
    referral_code_param, 
    'pending'
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to award referral credits
CREATE OR REPLACE FUNCTION award_referral_credits(referee_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  referrer_id_val UUID;
  credits_to_award INTEGER := 2;
BEGIN
  -- Find pending referral
  SELECT referrer_id INTO referrer_id_val
  FROM public.referrals 
  WHERE referee_id = referee_id_param 
    AND status = 'pending'
  LIMIT 1;
  
  -- If no pending referral found, return false
  IF referrer_id_val IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Award credits to referrer
  UPDATE public.profiles 
  SET extra_credits = COALESCE(extra_credits, 0) + credits_to_award
  WHERE id = referrer_id_val;
  
  -- Update referral status
  UPDATE public.referrals 
  SET 
    status = 'credited',
    credits_awarded = credits_to_award,
    verified_at = NOW(),
    credited_at = NOW()
  WHERE referrer_id = referrer_id_val 
    AND referee_id = referee_id_param 
    AND status = 'pending';
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for referrals table
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;

-- Users can only see referrals where they are the referrer
CREATE POLICY "Users can view their own referrals" ON public.referrals
  FOR SELECT USING (auth.uid() = referrer_id);

-- Users can only insert referrals where they are the referee (via function)
CREATE POLICY "Users can create referrals for themselves" ON public.referrals
  FOR INSERT WITH CHECK (auth.uid() = referee_id);

-- Grant necessary permissions
GRANT SELECT, INSERT ON public.referrals TO authenticated;
GRANT EXECUTE ON FUNCTION generate_referral_code() TO authenticated;
GRANT EXECUTE ON FUNCTION get_or_create_referral_code(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION process_referral_signup(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION award_referral_credits(UUID) TO authenticated;
