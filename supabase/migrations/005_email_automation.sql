-- Epic 4 Phase 3: Email Marketing Automation Database Schema
-- Migration: 005_email_automation.sql

-- Add email marketing fields to profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS last_search_without_upgrade TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_search_rule_type TEXT,
ADD COLUMN IF NOT EXISTS last_search_address TEXT,
ADD COLUMN IF NOT EXISTS email_preferences JSONB DEFAULT '{"marketing": true, "nudges": true, "updates": true}'::jsonb;

-- Create email campaigns table
CREATE TABLE IF NOT EXISTS public.email_campaigns (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  campaign_type TEXT NOT NULL CHECK (campaign_type IN ('abandonment_nudge', 'upgrade_reminder', 'feature_announcement', 'referral_reminder')),
  rule_type TEXT,
  address TEXT,
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  unsubscribed_at TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'sent', 'delivered', 'opened', 'clicked', 'failed', 'cancelled')),
  email_content JSONB,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_campaigns_user_id ON public.email_campaigns(user_id);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_scheduled_for ON public.email_campaigns(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_status ON public.email_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_campaign_type ON public.email_campaigns(campaign_type);
CREATE INDEX IF NOT EXISTS idx_profiles_last_search ON public.profiles(last_search_without_upgrade);

-- Function to track search without upgrade
CREATE OR REPLACE FUNCTION track_search_without_upgrade(
  user_id_param UUID,
  rule_type_param TEXT,
  address_param TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Update user's last search data
  UPDATE public.profiles
  SET
    last_search_without_upgrade = NOW(),
    last_search_rule_type = rule_type_param,
    last_search_address = address_param
  WHERE id = user_id_param;

  -- Cancel any existing scheduled abandonment emails for this user
  UPDATE public.email_campaigns
  SET
    status = 'cancelled',
    updated_at = NOW()
  WHERE user_id = user_id_param
    AND campaign_type = 'abandonment_nudge'
    AND status = 'scheduled';

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to schedule abandonment nudge email
CREATE OR REPLACE FUNCTION schedule_abandonment_nudge(
  user_id_param UUID,
  rule_type_param TEXT,
  address_param TEXT,
  delay_hours INTEGER DEFAULT 36
)
RETURNS UUID AS $$
DECLARE
  campaign_id UUID;
  user_preferences JSONB;
  user_subscription TEXT;
BEGIN
  -- Check user preferences and subscription
  SELECT email_preferences, subscription_tier
  INTO user_preferences, user_subscription
  FROM public.profiles
  WHERE id = user_id_param;

  -- Don't schedule if user has opted out of nudges or is already subscribed
  IF (user_preferences->>'nudges')::boolean = false OR user_subscription != 'free' THEN
    RETURN NULL;
  END IF;

  -- Create scheduled email campaign
  INSERT INTO public.email_campaigns (
    user_id,
    campaign_type,
    rule_type,
    address,
    scheduled_for,
    email_content
  ) VALUES (
    user_id_param,
    'abandonment_nudge',
    rule_type_param,
    address_param,
    NOW() + (delay_hours || ' hours')::INTERVAL,
    jsonb_build_object(
      'subject', 'Still wondering about your ' || rule_type_param || '?',
      'template', 'abandonment_nudge',
      'personalization', jsonb_build_object(
        'rule_type', rule_type_param,
        'address', address_param,
        'delay_hours', delay_hours
      )
    )
  ) RETURNING id INTO campaign_id;

  RETURN campaign_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending email campaigns
CREATE OR REPLACE FUNCTION get_pending_email_campaigns()
RETURNS TABLE(
  campaign_id UUID,
  user_id UUID,
  user_email TEXT,
  campaign_type TEXT,
  rule_type TEXT,
  address TEXT,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  email_content JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ec.id,
    ec.user_id,
    p.email,
    ec.campaign_type,
    ec.rule_type,
    ec.address,
    ec.scheduled_for,
    ec.email_content
  FROM public.email_campaigns ec
  JOIN public.profiles p ON ec.user_id = p.id
  WHERE ec.status = 'scheduled'
    AND ec.scheduled_for <= NOW()
    AND (p.email_preferences->>'nudges')::boolean = true
  ORDER BY ec.scheduled_for ASC
  LIMIT 50; -- Process in batches
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark email campaign as sent
CREATE OR REPLACE FUNCTION mark_email_campaign_sent(
  campaign_id_param UUID,
  success BOOLEAN DEFAULT TRUE,
  error_msg TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  IF success THEN
    UPDATE public.email_campaigns
    SET
      status = 'sent',
      sent_at = NOW(),
      updated_at = NOW()
    WHERE id = campaign_id_param;
  ELSE
    UPDATE public.email_campaigns
    SET
      status = 'failed',
      error_message = error_msg,
      updated_at = NOW()
    WHERE id = campaign_id_param;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track email opens
CREATE OR REPLACE FUNCTION track_email_open(
  campaign_id_param UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.email_campaigns
  SET
    status = CASE WHEN status = 'sent' THEN 'opened' ELSE status END,
    opened_at = CASE WHEN opened_at IS NULL THEN NOW() ELSE opened_at END,
    updated_at = NOW()
  WHERE id = campaign_id_param;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track email clicks
CREATE OR REPLACE FUNCTION track_email_click(
  campaign_id_param UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.email_campaigns
  SET
    status = 'clicked',
    clicked_at = CASE WHEN clicked_at IS NULL THEN NOW() ELSE clicked_at END,
    opened_at = CASE WHEN opened_at IS NULL THEN NOW() ELSE opened_at END,
    updated_at = NOW()
  WHERE id = campaign_id_param;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update email preferences
CREATE OR REPLACE FUNCTION update_email_preferences(
  user_id_param UUID,
  preferences JSONB
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.profiles
  SET
    email_preferences = preferences,
    updated_at = NOW()
  WHERE id = user_id_param;

  -- Cancel scheduled campaigns if user opts out
  IF (preferences->>'nudges')::boolean = false THEN
    UPDATE public.email_campaigns
    SET
      status = 'cancelled',
      updated_at = NOW()
    WHERE user_id = user_id_param
      AND status = 'scheduled'
      AND campaign_type IN ('abandonment_nudge', 'upgrade_reminder');
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for email_campaigns table
ALTER TABLE public.email_campaigns ENABLE ROW LEVEL SECURITY;

-- Users can only see their own email campaigns
CREATE POLICY "Users can view their own email campaigns" ON public.email_campaigns
  FOR SELECT USING (auth.uid() = user_id);

-- Only system can insert/update email campaigns (via functions)
CREATE POLICY "System can manage email campaigns" ON public.email_campaigns
  FOR ALL USING (false); -- Restrict direct access, use functions only

-- Grant necessary permissions
GRANT SELECT ON public.email_campaigns TO authenticated;
GRANT EXECUTE ON FUNCTION track_search_without_upgrade(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION schedule_abandonment_nudge(UUID, TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_pending_email_campaigns() TO authenticated;
GRANT EXECUTE ON FUNCTION mark_email_campaign_sent(UUID, BOOLEAN, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION track_email_open(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION track_email_click(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_email_preferences(UUID, JSONB) TO authenticated;
