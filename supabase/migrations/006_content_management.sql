-- Epic 4 Phase 4: Content Management System Database Schema
-- Migration: 006_content_management.sql

-- Create content items table
CREATE TABLE IF NOT EXISTS public.content_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  content_type TEXT NOT NULL CHECK (content_type IN ('video', 'meme', 'quote', 'announcement', 'screenshot', 'testimonial')),
  platform TEXT[] DEFAULT ARRAY['twitter', 'linkedin', 'tiktok', 'instagram'],
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'published', 'archived')),
  content_data JSONB NOT NULL DEFAULT '{}'::jsonb,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES public.profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create content calendar table
CREATE TABLE IF NOT EXISTS public.content_calendar (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  calendar_date DATE NOT NULL,
  content_items UUID[] DEFAULT ARRAY[]::UUID[],
  notes TEXT,
  theme TEXT,
  status TEXT NOT NULL DEFAULT 'planning' CHECK (status IN ('planning', 'ready', 'published', 'completed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(calendar_date)
);

-- Create video assets table
CREATE TABLE IF NOT EXISTS public.video_assets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  file_url TEXT,
  thumbnail_url TEXT,
  duration INTEGER, -- in seconds
  file_size BIGINT, -- in bytes
  platforms TEXT[] DEFAULT ARRAY['tiktok', 'instagram', 'youtube'],
  cta_text TEXT DEFAULT 'Check your property on Ordrly.com',
  tags TEXT[] DEFAULT ARRAY[]::TEXT[],
  metadata JSONB DEFAULT '{}'::jsonb,
  status TEXT NOT NULL DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'ready', 'published', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create launch announcements table
CREATE TABLE IF NOT EXISTS public.launch_announcements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('twitter', 'linkedin', 'facebook', 'instagram')),
  content_template TEXT NOT NULL,
  content_data JSONB DEFAULT '{}'::jsonb,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  metrics JSONB DEFAULT '{}'::jsonb,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'published', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create testimonials table
CREATE TABLE IF NOT EXISTS public.testimonials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  author_name TEXT NOT NULL,
  author_title TEXT,
  author_company TEXT,
  content TEXT NOT NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  project_type TEXT,
  location TEXT,
  verified BOOLEAN DEFAULT false,
  featured BOOLEAN DEFAULT false,
  approved BOOLEAN DEFAULT false,
  source TEXT DEFAULT 'manual',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_content_items_status ON public.content_items(status);
CREATE INDEX IF NOT EXISTS idx_content_items_scheduled ON public.content_items(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_content_items_type ON public.content_items(content_type);
CREATE INDEX IF NOT EXISTS idx_content_calendar_date ON public.content_calendar(calendar_date);
CREATE INDEX IF NOT EXISTS idx_video_assets_status ON public.video_assets(status);
CREATE INDEX IF NOT EXISTS idx_launch_announcements_platform ON public.launch_announcements(platform);
CREATE INDEX IF NOT EXISTS idx_launch_announcements_scheduled ON public.launch_announcements(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_testimonials_approved ON public.testimonials(approved);
CREATE INDEX IF NOT EXISTS idx_testimonials_featured ON public.testimonials(featured);

-- Function to get content calendar for date range
CREATE OR REPLACE FUNCTION get_content_calendar(
  start_date DATE,
  end_date DATE
)
RETURNS TABLE(
  calendar_date DATE,
  content_items JSONB,
  notes TEXT,
  theme TEXT,
  status TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    cc.calendar_date,
    COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'id', ci.id,
          'title', ci.title,
          'content_type', ci.content_type,
          'platform', ci.platform,
          'status', ci.status,
          'scheduled_for', ci.scheduled_for
        )
      ) FILTER (WHERE ci.id IS NOT NULL),
      '[]'::jsonb
    ) as content_items,
    cc.notes,
    cc.theme,
    cc.status
  FROM public.content_calendar cc
  LEFT JOIN LATERAL unnest(cc.content_items) AS content_item_id ON true
  LEFT JOIN public.content_items ci ON ci.id = content_item_id
  WHERE cc.calendar_date BETWEEN start_date AND end_date
  GROUP BY cc.calendar_date, cc.notes, cc.theme, cc.status
  ORDER BY cc.calendar_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to schedule content item
CREATE OR REPLACE FUNCTION schedule_content_item(
  item_id UUID,
  schedule_date TIMESTAMP WITH TIME ZONE,
  calendar_date_param DATE DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  target_date DATE;
BEGIN
  -- Use provided calendar date or extract from schedule timestamp
  target_date := COALESCE(calendar_date_param, schedule_date::DATE);

  -- Update content item
  UPDATE public.content_items
  SET
    scheduled_for = schedule_date,
    status = 'scheduled',
    updated_at = NOW()
  WHERE id = item_id;

  -- Ensure calendar entry exists
  INSERT INTO public.content_calendar (calendar_date, content_items)
  VALUES (target_date, ARRAY[item_id])
  ON CONFLICT (calendar_date)
  DO UPDATE SET
    content_items = array_append(
      CASE
        WHEN item_id = ANY(content_calendar.content_items) THEN content_calendar.content_items
        ELSE content_calendar.content_items
      END,
      CASE
        WHEN item_id = ANY(content_calendar.content_items) THEN NULL
        ELSE item_id
      END
    ),
    updated_at = NOW()
  WHERE NOT (item_id = ANY(content_calendar.content_items));

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending content for publishing
CREATE OR REPLACE FUNCTION get_pending_content()
RETURNS TABLE(
  item_id UUID,
  title TEXT,
  content_type TEXT,
  platform TEXT[],
  content_data JSONB,
  scheduled_for TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ci.id,
    ci.title,
    ci.content_type,
    ci.platform,
    ci.content_data,
    ci.scheduled_for
  FROM public.content_items ci
  WHERE ci.status = 'scheduled'
    AND ci.scheduled_for <= NOW()
  ORDER BY ci.scheduled_for ASC
  LIMIT 50;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark content as published
CREATE OR REPLACE FUNCTION mark_content_published(
  item_id UUID,
  platform_name TEXT DEFAULT NULL,
  success BOOLEAN DEFAULT TRUE,
  error_msg TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  IF success THEN
    UPDATE public.content_items
    SET
      status = 'published',
      published_at = NOW(),
      updated_at = NOW()
    WHERE id = item_id;
  ELSE
    UPDATE public.content_items
    SET
      status = 'draft',
      content_data = content_data || jsonb_build_object('error', error_msg),
      updated_at = NOW()
    WHERE id = item_id;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create HOA Karen video content
CREATE OR REPLACE FUNCTION create_hoa_karen_video(
  video_title TEXT,
  video_description TEXT,
  video_url TEXT,
  thumbnail_url TEXT DEFAULT NULL,
  duration_seconds INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  video_id UUID;
  content_id UUID;
BEGIN
  -- Create video asset
  INSERT INTO public.video_assets (
    title,
    description,
    file_url,
    thumbnail_url,
    duration,
    platforms,
    cta_text,
    tags,
    metadata
  ) VALUES (
    video_title,
    video_description,
    video_url,
    thumbnail_url,
    duration_seconds,
    ARRAY['tiktok', 'instagram', 'youtube'],
    'Check your property on Ordrly.com',
    ARRAY['hoa', 'karen', 'compliance', 'viral'],
    jsonb_build_object(
      'series', 'HOA Karen',
      'format', 'short-form',
      'cta_overlay', true,
      'watermark', true
    )
  ) RETURNING id INTO video_id;

  -- Create content item
  INSERT INTO public.content_items (
    title,
    description,
    content_type,
    platform,
    content_data
  ) VALUES (
    video_title,
    video_description,
    'video',
    ARRAY['tiktok', 'instagram', 'youtube'],
    jsonb_build_object(
      'video_id', video_id,
      'cta_text', 'Check your property on Ordrly.com',
      'hashtags', ARRAY['#HOA', '#PropertyCompliance', '#Ordrly', '#Karen'],
      'caption', video_description || ' I used Ordrly.'
    )
  ) RETURNING id INTO content_id;

  RETURN content_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies
ALTER TABLE public.content_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_calendar ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.launch_announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.testimonials ENABLE ROW LEVEL SECURITY;

-- Admin users can manage all content (for now, allow all authenticated users)
CREATE POLICY "Authenticated users can manage content" ON public.content_items
  FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can view calendar" ON public.content_calendar
  FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can manage videos" ON public.video_assets
  FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can manage announcements" ON public.launch_announcements
  FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Authenticated users can manage testimonials" ON public.testimonials
  FOR ALL USING (auth.uid() IS NOT NULL);

-- Grant necessary permissions
GRANT ALL ON public.content_items TO authenticated;
GRANT ALL ON public.content_calendar TO authenticated;
GRANT ALL ON public.video_assets TO authenticated;
GRANT ALL ON public.launch_announcements TO authenticated;
GRANT ALL ON public.testimonials TO authenticated;
GRANT EXECUTE ON FUNCTION get_content_calendar(DATE, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION schedule_content_item(UUID, TIMESTAMP WITH TIME ZONE, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION get_pending_content() TO authenticated;
GRANT EXECUTE ON FUNCTION mark_content_published(UUID, TEXT, BOOLEAN, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_hoa_karen_video(TEXT, TEXT, TEXT, TEXT, INTEGER) TO authenticated;
