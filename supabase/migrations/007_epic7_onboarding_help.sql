-- Epic 7: Onboarding & Help Resources Database Schema
-- Migration: 007_epic7_onboarding_help.sql

-- Knowledge base articles table
CREATE TABLE IF NOT EXISTS public.knowledge_base_articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  author_id UUID REFERENCES public.profiles(id),
  is_published BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Article ratings table
CREATE TABLE IF NOT EXISTS public.article_ratings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  article_id UUID REFERENCES public.knowledge_base_articles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  helpful BOOLEAN,
  feedback TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(article_id, user_id)
);

-- User progress tracking table
CREATE TABLE IF NOT EXISTS public.user_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  feature_name TEXT NOT NULL,
  progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  completed_steps TEXT[] DEFAULT '{}',
  total_steps INTEGER DEFAULT 0,
  last_step_completed TEXT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, feature_name)
);

-- Feature announcements table
CREATE TABLE IF NOT EXISTS public.feature_announcements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
  is_active BOOLEAN DEFAULT true,
  target_users TEXT DEFAULT 'all' CHECK (target_users IN ('all', 'free', 'pro', 'enterprise')),
  priority INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tutorial progress table
CREATE TABLE IF NOT EXISTS public.tutorial_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  tutorial_name TEXT NOT NULL,
  current_step INTEGER DEFAULT 0,
  completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, tutorial_name)
);

-- Help tooltips table
CREATE TABLE IF NOT EXISTS public.help_tooltips (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  element_id TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  position TEXT DEFAULT 'top' CHECK (position IN ('top', 'bottom', 'left', 'right')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add first_time_user field to profiles if it doesn't exist
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS first_time_user BOOLEAN DEFAULT true;

-- Add help preferences to profiles
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS help_preferences JSONB DEFAULT '{"show_tooltips": true, "show_announcements": true, "tutorial_completed": false}'::jsonb;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_knowledge_base_articles_category ON public.knowledge_base_articles(category);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_articles_published ON public.knowledge_base_articles(is_published);
CREATE INDEX IF NOT EXISTS idx_article_ratings_article_id ON public.article_ratings(article_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_user_id ON public.user_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_feature_announcements_active ON public.feature_announcements(is_active);
CREATE INDEX IF NOT EXISTS idx_tutorial_progress_user_id ON public.tutorial_progress(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.knowledge_base_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.feature_announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutorial_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.help_tooltips ENABLE ROW LEVEL SECURITY;

-- RLS Policies for knowledge_base_articles
CREATE POLICY "Knowledge base articles are viewable by everyone" ON public.knowledge_base_articles
  FOR SELECT USING (is_published = true);

CREATE POLICY "Authors can manage their own articles" ON public.knowledge_base_articles
  FOR ALL USING (auth.uid() = author_id);

CREATE POLICY "Authenticated users can create articles" ON public.knowledge_base_articles
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- RLS Policies for article_ratings
CREATE POLICY "Users can view all ratings" ON public.article_ratings
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own ratings" ON public.article_ratings
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for user_progress
CREATE POLICY "Users can only access their own progress" ON public.user_progress
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for feature_announcements
CREATE POLICY "Active announcements are viewable by everyone" ON public.feature_announcements
  FOR SELECT USING (is_active = true);

-- RLS Policies for tutorial_progress
CREATE POLICY "Users can only access their own tutorial progress" ON public.tutorial_progress
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for help_tooltips
CREATE POLICY "Active tooltips are viewable by everyone" ON public.help_tooltips
  FOR SELECT USING (is_active = true);

-- Insert sample knowledge base articles
INSERT INTO public.knowledge_base_articles (title, content, category, tags, is_published, sort_order) VALUES
('Getting Started with Ordrly', 
'Welcome to Ordrly! This guide will help you get started with our property compliance platform.

## Step 1: Enter Your Address
Start by typing any U.S. address in the search field. Our system provides autocomplete suggestions to help you find the exact location.

## Step 2: Review Compliance Results
Once you submit an address, our AI analyzes local ordinances and provides a comprehensive compliance report.

## Step 3: Export or Share
Export your compliance data as CSV files to share with contractors, or upgrade for advanced features.

## Tips for Best Results
- Type slowly for better autocomplete results
- Include city and state for accuracy
- Select from the dropdown suggestions', 
'getting-started', 
ARRAY['tutorial', 'basics', 'address', 'compliance'], 
true, 
1),

('Understanding Confidence Scores', 
'Ordrly provides confidence scores to help you understand the reliability of our compliance analysis.

## What Are Confidence Scores?
Confidence scores range from 0-100 and indicate how certain our AI is about the compliance requirements for your specific project.

## Score Ranges
- **90-100**: High confidence - Clear regulations found
- **70-89**: Good confidence - Regulations found with minor ambiguity
- **50-69**: Moderate confidence - Some uncertainty in interpretation
- **Below 50**: Low confidence - Limited or unclear regulations

## How to Use Scores
Higher scores mean you can rely more confidently on the results. For lower scores, consider consulting with local authorities or professionals.', 
'understanding-results', 
ARRAY['confidence', 'scores', 'reliability'], 
true, 
2),

('Supported Project Types', 
'Ordrly supports analysis for a wide variety of property improvement projects.

## Residential Projects
- Fences and barriers
- Sheds and outbuildings  
- Decks and patios
- Driveways and parking
- Landscaping and gardens
- Swimming pools and spas

## Commercial Projects
- Signage and displays
- Parking lots
- Accessibility improvements
- Outdoor dining areas

## What We Analyze
For each project type, we check:
- Setback requirements
- Height restrictions
- Permit requirements
- Material specifications
- Design standards

## Getting Better Results
Be specific about your project type and include relevant details like dimensions and materials when possible.', 
'project-types', 
ARRAY['projects', 'residential', 'commercial', 'permits'], 
true, 
3);

-- Insert sample help tooltips
INSERT INTO public.help_tooltips (element_id, title, content, position) VALUES
('search-input', 'Address Search', 'Enter any U.S. address to get started. Use our autocomplete suggestions for best results.', 'bottom'),
('confidence-score', 'Confidence Score', 'This score indicates how certain our AI is about the compliance requirements. Higher scores mean more reliable results.', 'top'),
('download-button', 'Export Results', 'Export your compliance data as CSV files to share with contractors or for permit applications.', 'top'),
('upgrade-button', 'Upgrade Account', 'Upgrade to Pro for unlimited searches, AI chat support, and detailed analysis features.', 'left');

-- Insert sample feature announcements
INSERT INTO public.feature_announcements (title, content, type, target_users, priority) VALUES
('New Feature: AI Chat Support', 'Ask questions about your compliance results with our new AI chat feature! Available for Pro subscribers.', 'info', 'all', 1),
('Improved Address Autocomplete', 'We''ve enhanced our address search with better autocomplete suggestions and faster results.', 'success', 'all', 2),
('Mobile App Coming Soon', 'Get ready for the Ordrly mobile app! Sign up for early access notifications in your account settings.', 'info', 'all', 3);
