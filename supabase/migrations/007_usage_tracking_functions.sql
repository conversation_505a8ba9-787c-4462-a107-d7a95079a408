-- Epic 3: Usage Tracking Database Functions
-- Migration: 007_usage_tracking_functions.sql
-- This migration creates the missing database functions for usage tracking and limits

-- Function to check if user can proceed with a search based on their usage limits
CREATE OR REPLACE FUNCTION check_usage_limit(user_id_param UUID)
RETURNS TABLE(
  can_proceed BOOLEAN,
  current_usage INTEGER,
  limit_amount INTEGER,
  extra_credits_available INTEGER,
  subscription_tier TEXT
) AS $$
DECLARE
  user_profile RECORD;
  monthly_limit INTEGER;
  current_month_start DATE;
BEGIN
  -- Get user profile data
  SELECT 
    p.subscription_tier,
    p.pulls_this_month,
    p.extra_credits,
    p.subscription_status
  INTO user_profile
  FROM public.profiles p
  WHERE p.id = user_id_param;

  -- If user not found, return default values
  IF NOT FOUND THEN
    RETURN QUERY SELECT
      FALSE::BOOLEAN,
      0::INTEGER,
      500::INTEGER,
      0::INTEGER,
      'trial'::TEXT;
    RETURN;
  END IF;

  -- Set limits based on subscription tier
  CASE user_profile.subscription_tier
    WHEN 'trial' THEN
      monthly_limit := 500;
    WHEN 'starter' THEN
      monthly_limit := 500;
    WHEN 'professional' THEN
      monthly_limit := 2000;
    WHEN 'business' THEN
      monthly_limit := -1; -- unlimited
    ELSE
      monthly_limit := 500; -- default to trial
  END CASE;

  -- For unlimited plans (pro/appraiser), always allow
  IF monthly_limit = -1 THEN
    RETURN QUERY SELECT 
      TRUE::BOOLEAN,
      user_profile.pulls_this_month::INTEGER,
      monthly_limit::INTEGER,
      user_profile.extra_credits::INTEGER,
      user_profile.subscription_tier::TEXT;
    RETURN;
  END IF;

  -- For free tier, check if they have remaining searches
  DECLARE
    total_available INTEGER;
    can_search BOOLEAN;
  BEGIN
    total_available := monthly_limit + COALESCE(user_profile.extra_credits, 0);
    can_search := user_profile.pulls_this_month < total_available;

    RETURN QUERY SELECT 
      can_search::BOOLEAN,
      user_profile.pulls_this_month::INTEGER,
      monthly_limit::INTEGER,
      COALESCE(user_profile.extra_credits, 0)::INTEGER,
      user_profile.subscription_tier::TEXT;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment user's usage count
CREATE OR REPLACE FUNCTION increment_usage(user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_profile RECORD;
  monthly_limit INTEGER;
  total_available INTEGER;
BEGIN
  -- Get current user data
  SELECT 
    subscription_tier,
    pulls_this_month,
    extra_credits
  INTO user_profile
  FROM public.profiles
  WHERE id = user_id_param;

  -- If user not found, return false
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Set limits based on subscription tier
  CASE user_profile.subscription_tier
    WHEN 'trial' THEN
      monthly_limit := 500;
    WHEN 'starter' THEN
      monthly_limit := 500;
    WHEN 'professional' THEN
      monthly_limit := 2000;
    WHEN 'business' THEN
      monthly_limit := -1; -- unlimited
    ELSE
      monthly_limit := 500; -- default to trial
  END CASE;

  -- For unlimited plans, just increment
  IF monthly_limit = -1 THEN
    UPDATE public.profiles
    SET 
      pulls_this_month = pulls_this_month + 1,
      updated_at = NOW()
    WHERE id = user_id_param;
    RETURN TRUE;
  END IF;

  -- For free tier, check limits before incrementing
  total_available := monthly_limit + COALESCE(user_profile.extra_credits, 0);
  
  IF user_profile.pulls_this_month >= total_available THEN
    -- Already at limit, don't increment
    RETURN FALSE;
  END IF;

  -- Increment usage
  UPDATE public.profiles
  SET 
    pulls_this_month = pulls_this_month + 1,
    updated_at = NOW()
  WHERE id = user_id_param;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset monthly usage (for cron job)
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS INTEGER AS $$
DECLARE
  reset_count INTEGER;
BEGIN
  -- Reset pulls_this_month for all users on the 1st of each month
  UPDATE public.profiles
  SET 
    pulls_this_month = 0,
    updated_at = NOW()
  WHERE pulls_this_month > 0;

  GET DIAGNOSTICS reset_count = ROW_COUNT;
  
  RETURN reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user usage statistics
CREATE OR REPLACE FUNCTION get_user_usage_stats(user_id_param UUID)
RETURNS TABLE(
  subscription_tier TEXT,
  pulls_this_month INTEGER,
  monthly_limit INTEGER,
  extra_credits INTEGER,
  total_available INTEGER,
  remaining_searches INTEGER,
  usage_percentage DECIMAL
) AS $$
DECLARE
  user_profile RECORD;
  monthly_limit_val INTEGER;
  total_avail INTEGER;
  remaining INTEGER;
  usage_pct DECIMAL;
BEGIN
  -- Get user profile data
  SELECT 
    p.subscription_tier,
    p.pulls_this_month,
    p.extra_credits
  INTO user_profile
  FROM public.profiles p
  WHERE p.id = user_id_param;

  -- If user not found, return empty
  IF NOT FOUND THEN
    RETURN;
  END IF;

  -- Set limits based on subscription tier
  CASE user_profile.subscription_tier
    WHEN 'trial' THEN
      monthly_limit_val := 500;
    WHEN 'starter' THEN
      monthly_limit_val := 500;
    WHEN 'professional' THEN
      monthly_limit_val := 2000;
    WHEN 'business' THEN
      monthly_limit_val := -1; -- unlimited
    ELSE
      monthly_limit_val := 500; -- default to trial
  END CASE;

  -- Calculate stats
  IF monthly_limit_val = -1 THEN
    -- Unlimited plan
    total_avail := -1;
    remaining := -1;
    usage_pct := 0;
  ELSE
    -- Limited plan
    total_avail := monthly_limit_val + COALESCE(user_profile.extra_credits, 0);
    remaining := total_avail - user_profile.pulls_this_month;
    IF remaining < 0 THEN remaining := 0; END IF;
    
    IF total_avail > 0 THEN
      usage_pct := (user_profile.pulls_this_month::DECIMAL / total_avail::DECIMAL) * 100;
    ELSE
      usage_pct := 0;
    END IF;
  END IF;

  RETURN QUERY SELECT 
    user_profile.subscription_tier::TEXT,
    user_profile.pulls_this_month::INTEGER,
    monthly_limit_val::INTEGER,
    COALESCE(user_profile.extra_credits, 0)::INTEGER,
    total_avail::INTEGER,
    remaining::INTEGER,
    usage_pct::DECIMAL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION check_usage_limit(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_usage(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_usage_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION reset_monthly_usage() TO service_role;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_tier ON public.profiles(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_profiles_pulls_this_month ON public.profiles(pulls_this_month);
CREATE INDEX IF NOT EXISTS idx_profiles_updated_at ON public.profiles(updated_at);
