-- Migration: Usage Alert System for Day 2 Marketing Growth Engine
-- Creates functionality to send usage alerts when free users approach their limit

-- Table to track sent usage alerts to prevent duplicates
CREATE TABLE IF NOT EXISTS public.usage_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  alert_type TEXT NOT NULL, -- 'approaching_limit', 'limit_reached'
  searches_count INTEGER NOT NULL, -- Number of searches when alert was sent
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  email_sent BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for efficient lookups
CREATE INDEX IF NOT EXISTS idx_usage_alerts_user_id ON public.usage_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_alerts_sent_at ON public.usage_alerts(sent_at);

-- Function to check if user needs usage alert
CREATE OR REPLACE FUNCTION check_usage_alert_needed(user_id_param UUID)
RETURNS TABLE(
  needs_alert BOOLEAN,
  alert_type TEXT,
  current_usage INTEGER,
  user_email TEXT,
  user_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_profile RECORD;
  monthly_limit INTEGER;
  alert_threshold INTEGER;
  last_alert RECORD;
BEGIN
  -- Get user profile data
  SELECT 
    p.subscription_tier,
    p.pulls_this_month,
    p.email,
    p.name
  INTO user_profile
  FROM public.profiles p
  WHERE p.id = user_id_param;

  -- If user not found or not on free tier, no alert needed
  IF NOT FOUND OR user_profile.subscription_tier != 'free' THEN
    RETURN QUERY SELECT 
      FALSE::BOOLEAN,
      ''::TEXT,
      0::INTEGER,
      ''::TEXT,
      ''::TEXT;
    RETURN;
  END IF;

  monthly_limit := 10;
  alert_threshold := 8; -- Alert when user hits 8 searches

  -- Check if user has reached alert threshold
  IF user_profile.pulls_this_month < alert_threshold THEN
    RETURN QUERY SELECT 
      FALSE::BOOLEAN,
      ''::TEXT,
      user_profile.pulls_this_month::INTEGER,
      user_profile.email::TEXT,
      COALESCE(user_profile.name, '')::TEXT;
    RETURN;
  END IF;

  -- Check if we've already sent an alert for this usage level
  SELECT 
    alert_type,
    searches_count
  INTO last_alert
  FROM public.usage_alerts
  WHERE user_id = user_id_param
    AND searches_count >= alert_threshold
  ORDER BY sent_at DESC
  LIMIT 1;

  -- If we've already sent an alert for this level or higher, don't send again
  IF FOUND AND last_alert.searches_count >= user_profile.pulls_this_month THEN
    RETURN QUERY SELECT 
      FALSE::BOOLEAN,
      ''::TEXT,
      user_profile.pulls_this_month::INTEGER,
      user_profile.email::TEXT,
      COALESCE(user_profile.name, '')::TEXT;
    RETURN;
  END IF;

  -- Determine alert type
  DECLARE
    alert_type_val TEXT;
  BEGIN
    IF user_profile.pulls_this_month >= monthly_limit THEN
      alert_type_val := 'limit_reached';
    ELSE
      alert_type_val := 'approaching_limit';
    END IF;

    RETURN QUERY SELECT 
      TRUE::BOOLEAN,
      alert_type_val::TEXT,
      user_profile.pulls_this_month::INTEGER,
      user_profile.email::TEXT,
      COALESCE(user_profile.name, '')::TEXT;
  END;
END;
$$;

-- Function to record that an alert was sent
CREATE OR REPLACE FUNCTION record_usage_alert(
  user_id_param UUID,
  alert_type_param TEXT,
  searches_count_param INTEGER,
  email_sent_param BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO public.usage_alerts (
    user_id,
    alert_type,
    searches_count,
    email_sent,
    sent_at
  ) VALUES (
    user_id_param,
    alert_type_param,
    searches_count_param,
    email_sent_param,
    NOW()
  );
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get usage alert email template
CREATE OR REPLACE FUNCTION get_usage_alert_email_template(
  alert_type_param TEXT,
  user_name_param TEXT,
  current_usage_param INTEGER
)
RETURNS TABLE(
  subject TEXT,
  html_body TEXT,
  text_body TEXT
) AS $$
BEGIN
  IF alert_type_param = 'approaching_limit' THEN
    RETURN QUERY SELECT 
      'You''re almost out of free searches!'::TEXT,
      format('<html><body>
        <h2>Hi %s,</h2>
        <p>You''ve used <strong>%s of your 10 free Ordrly searches</strong> this month.</p>
        <p>Don''t get stuck when you need permit information most! Upgrade to Pro now for unlimited lookups and never worry about running out again.</p>
        <div style="margin: 20px 0;">
          <a href="https://ordrly.ai/pricing" style="background-color: #1DA1F2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Upgrade to Pro – $19/mo
          </a>
        </div>
        <p><strong>Pro benefits:</strong></p>
        <ul>
          <li>✅ Unlimited searches</li>
          <li>✅ AI-powered analysis</li>
          <li>✅ Red flag detection</li>
          <li>✅ Save searches for later</li>
        </ul>
        <p>Questions? Reply to this email and we''ll help!</p>
        <p>Best,<br>The Ordrly Team</p>
      </body></html>', 
      COALESCE(user_name_param, 'there'), 
      current_usage_param)::TEXT,
      format('Hi %s,

You''ve used %s of your 10 free Ordrly searches this month.

Don''t get stuck when you need permit information most! Upgrade to Pro now for unlimited lookups and never worry about running out again.

Upgrade to Pro – $19/mo: https://ordrly.ai/pricing

Pro benefits:
✅ Unlimited searches
✅ AI-powered analysis  
✅ Red flag detection
✅ Save searches for later

Questions? Reply to this email and we''ll help!

Best,
The Ordrly Team', 
      COALESCE(user_name_param, 'there'), 
      current_usage_param)::TEXT;
  ELSE
    RETURN QUERY SELECT 
      'You''ve reached your free search limit'::TEXT,
      format('<html><body>
        <h2>Hi %s,</h2>
        <p>You''ve used all <strong>10 of your free Ordrly searches</strong> this month.</p>
        <p>Ready to keep going? Upgrade to Pro for unlimited searches and premium features!</p>
        <div style="margin: 20px 0;">
          <a href="https://ordrly.ai/pricing" style="background-color: #1DA1F2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Upgrade to Pro – $19/mo
          </a>
        </div>
        <p><strong>What you''ll get:</strong></p>
        <ul>
          <li>✅ Unlimited searches</li>
          <li>✅ AI-powered analysis</li>
          <li>✅ Red flag detection</li>
          <li>✅ Save searches for later</li>
        </ul>
        <p>Your searches will reset next month, but why wait?</p>
        <p>Best,<br>The Ordrly Team</p>
      </body></html>', 
      COALESCE(user_name_param, 'there'))::TEXT,
      format('Hi %s,

You''ve used all 10 of your free Ordrly searches this month.

Ready to keep going? Upgrade to Pro for unlimited searches and premium features!

Upgrade to Pro – $19/mo: https://ordrly.ai/pricing

What you''ll get:
✅ Unlimited searches
✅ AI-powered analysis
✅ Red flag detection  
✅ Save searches for later

Your searches will reset next month, but why wait?

Best,
The Ordrly Team', 
      COALESCE(user_name_param, 'there'))::TEXT;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT ALL ON public.usage_alerts TO authenticated;
GRANT EXECUTE ON FUNCTION check_usage_alert_needed(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION record_usage_alert(UUID, TEXT, INTEGER, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION get_usage_alert_email_template(TEXT, TEXT, INTEGER) TO authenticated;

-- Grant service role permissions for automated processes
GRANT ALL ON public.usage_alerts TO service_role;
GRANT EXECUTE ON FUNCTION check_usage_alert_needed(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION record_usage_alert(UUID, TEXT, INTEGER, BOOLEAN) TO service_role;
GRANT EXECUTE ON FUNCTION get_usage_alert_email_template(TEXT, TEXT, INTEGER) TO service_role;
