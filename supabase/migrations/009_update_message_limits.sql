-- Migration: Update message limits for new pricing structure
-- This migration updates the usage limit functions to use the new message limits:
-- - Starter: 500 messages per month
-- - Professional: 2000 messages per month
-- - Trial: 500 messages during 7-day trial

-- Update the check_usage_limit function with new limits
CREATE OR REPLACE FUNCTION check_usage_limit(user_id_param UUID)
RETURNS TABLE(
  can_proceed BOOLEAN,
  current_usage INTEGER,
  limit_amount INTEGER,
  extra_credits_available INTEGER,
  subscription_tier TEXT
) AS $$
DECLARE
  user_profile RECORD;
  monthly_limit INTEGER;
  current_month_start DATE;
BEGIN
  -- Get user profile data
  SELECT 
    p.subscription_tier,
    p.pulls_this_month,
    p.extra_credits,
    p.subscription_status
  INTO user_profile
  FROM public.profiles p
  WHERE p.id = user_id_param;

  -- If user not found, return default values
  IF NOT FOUND THEN
    RETURN QUERY SELECT 
      FALSE::BOOLEAN,
      0::INTEGER,
      0::INTEGER,
      0::INTEGER,
      'free'::TEXT;
    RETURN;
  END IF;

  -- Set limits based on subscription tier with new message limits
  CASE user_profile.subscription_tier
    WHEN 'free' THEN
      monthly_limit := 5; -- Keep legacy free tier limit
    WHEN 'trial' THEN
      monthly_limit := 500; -- 500 messages during trial
    WHEN 'starter' THEN
      monthly_limit := 500; -- 500 messages per month
    WHEN 'professional' THEN
      monthly_limit := 2000; -- 2000 messages per month
    WHEN 'business' THEN
      monthly_limit := -1; -- unlimited
    WHEN 'pro' THEN
      monthly_limit := -1; -- unlimited (legacy)
    WHEN 'appraiser' THEN
      monthly_limit := -1; -- unlimited (legacy)
    ELSE
      monthly_limit := 5; -- default to free
  END CASE;

  -- For unlimited plans, always allow
  IF monthly_limit = -1 THEN
    RETURN QUERY SELECT 
      TRUE::BOOLEAN,
      user_profile.pulls_this_month::INTEGER,
      monthly_limit::INTEGER,
      user_profile.extra_credits::INTEGER,
      user_profile.subscription_tier::TEXT;
    RETURN;
  END IF;

  -- Check if user can proceed (including extra credits)
  DECLARE
    total_available INTEGER;
  BEGIN
    total_available := monthly_limit + COALESCE(user_profile.extra_credits, 0);
    
    RETURN QUERY SELECT 
      (user_profile.pulls_this_month < total_available)::BOOLEAN,
      user_profile.pulls_this_month::INTEGER,
      monthly_limit::INTEGER,
      COALESCE(user_profile.extra_credits, 0)::INTEGER,
      user_profile.subscription_tier::TEXT;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the increment_usage function with new limits
CREATE OR REPLACE FUNCTION increment_usage(user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_profile RECORD;
  monthly_limit INTEGER;
  total_available INTEGER;
BEGIN
  -- Get current user data
  SELECT 
    subscription_tier,
    pulls_this_month,
    extra_credits
  INTO user_profile
  FROM public.profiles
  WHERE id = user_id_param;

  -- If user not found, return false
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Set limits based on subscription tier with new message limits
  CASE user_profile.subscription_tier
    WHEN 'free' THEN
      monthly_limit := 5; -- Keep legacy free tier limit
    WHEN 'trial' THEN
      monthly_limit := 500; -- 500 messages during trial
    WHEN 'starter' THEN
      monthly_limit := 500; -- 500 messages per month
    WHEN 'professional' THEN
      monthly_limit := 2000; -- 2000 messages per month
    WHEN 'business' THEN
      monthly_limit := -1; -- unlimited
    WHEN 'pro' THEN
      monthly_limit := -1; -- unlimited (legacy)
    WHEN 'appraiser' THEN
      monthly_limit := -1; -- unlimited (legacy)
    ELSE
      monthly_limit := 5; -- default to free
  END CASE;

  -- For unlimited plans, just increment
  IF monthly_limit = -1 THEN
    UPDATE public.profiles
    SET 
      pulls_this_month = pulls_this_month + 1,
      updated_at = NOW()
    WHERE id = user_id_param;
    RETURN TRUE;
  END IF;

  -- For limited plans, check limits before incrementing
  total_available := monthly_limit + COALESCE(user_profile.extra_credits, 0);
  
  IF user_profile.pulls_this_month >= total_available THEN
    -- Already at limit, don't increment
    RETURN FALSE;
  END IF;

  -- Increment usage
  UPDATE public.profiles
  SET 
    pulls_this_month = pulls_this_month + 1,
    updated_at = NOW()
  WHERE id = user_id_param;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the get_user_usage_stats function with new limits
CREATE OR REPLACE FUNCTION get_user_usage_stats(user_id_param UUID)
RETURNS TABLE(
  subscription_tier TEXT,
  current_usage INTEGER,
  monthly_limit INTEGER,
  extra_credits INTEGER,
  total_available INTEGER,
  remaining INTEGER,
  usage_percentage DECIMAL
) AS $$
DECLARE
  user_profile RECORD;
  monthly_limit_val INTEGER;
  total_avail INTEGER;
  remaining INTEGER;
  usage_pct DECIMAL;
BEGIN
  -- Get user profile data
  SELECT 
    p.subscription_tier,
    p.pulls_this_month,
    p.extra_credits
  INTO user_profile
  FROM public.profiles p
  WHERE p.id = user_id_param;

  -- If user not found, return default values
  IF NOT FOUND THEN
    RETURN QUERY SELECT 
      'free'::TEXT,
      0::INTEGER,
      0::INTEGER,
      0::INTEGER,
      0::INTEGER,
      0::INTEGER,
      0::DECIMAL;
    RETURN;
  END IF;

  -- Set limits based on subscription tier with new message limits
  CASE user_profile.subscription_tier
    WHEN 'free' THEN
      monthly_limit_val := 5; -- Keep legacy free tier limit
    WHEN 'trial' THEN
      monthly_limit_val := 500; -- 500 messages during trial
    WHEN 'starter' THEN
      monthly_limit_val := 500; -- 500 messages per month
    WHEN 'professional' THEN
      monthly_limit_val := 2000; -- 2000 messages per month
    WHEN 'business' THEN
      monthly_limit_val := -1; -- unlimited
    WHEN 'pro' THEN
      monthly_limit_val := -1; -- unlimited (legacy)
    WHEN 'appraiser' THEN
      monthly_limit_val := -1; -- unlimited (legacy)
    ELSE
      monthly_limit_val := 5; -- default to free
  END CASE;

  -- Calculate stats
  IF monthly_limit_val = -1 THEN
    -- Unlimited plan
    total_avail := -1;
    remaining := -1;
    usage_pct := 0;
  ELSE
    -- Limited plan
    total_avail := monthly_limit_val + COALESCE(user_profile.extra_credits, 0);
    remaining := total_avail - user_profile.pulls_this_month;
    IF remaining < 0 THEN remaining := 0; END IF;
    
    IF total_avail > 0 THEN
      usage_pct := (user_profile.pulls_this_month::DECIMAL / total_avail::DECIMAL) * 100;
    ELSE
      usage_pct := 0;
    END IF;
  END IF;

  RETURN QUERY SELECT 
    user_profile.subscription_tier::TEXT,
    user_profile.pulls_this_month::INTEGER,
    monthly_limit_val::INTEGER,
    COALESCE(user_profile.extra_credits, 0)::INTEGER,
    total_avail::INTEGER,
    remaining::INTEGER,
    usage_pct::DECIMAL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION check_usage_limit(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_usage(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_usage_stats(UUID) TO authenticated;
