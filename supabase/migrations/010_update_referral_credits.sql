-- Update referral credits from 2 to 25 messages per referral
-- This migration updates the award_referral_credits function to award 25 credits instead of 2

-- Function to award referral credits (updated to 25 credits)
CREATE OR REPLACE FUNCTION award_referral_credits(referee_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  referrer_id_val UUID;
  credits_to_award INTEGER := 25; -- Updated from 2 to 25
BEGIN
  -- Find pending referral
  SELECT referrer_id INTO referrer_id_val
  FROM public.referrals 
  WHERE referee_id = referee_id_param 
    AND status = 'pending'
  LIMIT 1;
  
  -- If no pending referral found, return false
  IF referrer_id_val IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Award credits to referrer
  UPDATE public.profiles 
  SET extra_credits = COALESCE(extra_credits, 0) + credits_to_award
  WHERE id = referrer_id_val;
  
  -- Update referral status
  UPDATE public.referrals 
  SET 
    status = 'credited',
    credits_awarded = credits_to_award,
    verified_at = NOW(),
    credited_at = NOW()
  WHERE referrer_id = referrer_id_val 
    AND referee_id = referee_id_param 
    AND status = 'pending';
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
