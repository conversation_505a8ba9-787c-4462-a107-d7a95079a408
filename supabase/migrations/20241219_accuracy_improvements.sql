-- Accuracy Improvements Migration
-- Addresses 5 Whys root causes to improve accuracy from 95% to 98%+
-- Fixes: Multi-jurisdiction precedence, overlay conflicts, ordinance versioning

-- Enable RLS on new tables
SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

-- 1. JURISDICTION PRECEDENCE TABLE
-- Handles overlapping jurisdiction authority (Kansas City, MO case)
CREATE TABLE IF NOT EXISTS public.jurisdiction_precedence (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    primary_jurisdiction_id UUID NOT NULL REFERENCES public.jurisdiction_hierarchy(id),
    secondary_jurisdiction_id UUID NOT NULL REFERENCES public.jurisdiction_hierarchy(id),
    precedence_type TEXT NOT NULL CHECK (precedence_type IN ('municipal_over_county', 'county_over_state', 'special_district', 'overlay_zone', 'custom')),
    rule_description TEXT NOT NULL,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(primary_jurisdiction_id, secondary_jurisdiction_id, precedence_type)
);

-- Add RLS policies for jurisdiction_precedence
ALTER TABLE public.jurisdiction_precedence ENABLE ROW LEVEL SECURITY;

CREATE POLICY "jurisdiction_precedence_select_policy" ON public.jurisdiction_precedence
    FOR SELECT USING (true);

-- 2. OVERLAY CONFLICTS TABLE
-- Handles multi-overlay interaction conflicts (Vail, CO case)
CREATE TABLE IF NOT EXISTS public.overlay_conflicts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    jurisdiction_id UUID NOT NULL REFERENCES public.jurisdiction_hierarchy(id),
    overlay_type_1 TEXT NOT NULL,
    overlay_type_2 TEXT NOT NULL,
    conflict_type TEXT NOT NULL CHECK (conflict_type IN ('stricter_applies', 'more_permissive_applies', 'requires_variance', 'prohibited_combination', 'custom_resolution')),
    resolution_rule TEXT NOT NULL,
    priority_overlay TEXT CHECK (priority_overlay IN ('overlay_1', 'overlay_2', 'neither')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(jurisdiction_id, overlay_type_1, overlay_type_2)
);

-- Add RLS policies for overlay_conflicts
ALTER TABLE public.overlay_conflicts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "overlay_conflicts_select_policy" ON public.overlay_conflicts
    FOR SELECT USING (true);

-- 3. ORDINANCE VERSIONS TABLE
-- Handles ordinance change tracking (Charlotte, NC case)
CREATE TABLE IF NOT EXISTS public.ordinance_versions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    jurisdiction_name TEXT NOT NULL, -- Use jurisdiction_name to match ordinance_cache pattern
    ordinance_type TEXT NOT NULL,
    chapter TEXT,
    section TEXT,
    version_number INTEGER NOT NULL DEFAULT 1,
    effective_date DATE NOT NULL,
    expiration_date DATE,
    content_hash TEXT NOT NULL,
    change_summary TEXT,
    source_url TEXT,
    is_current BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(jurisdiction_name, ordinance_type, chapter, section, version_number)
);

-- Add RLS policies for ordinance_versions
ALTER TABLE public.ordinance_versions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "ordinance_versions_select_policy" ON public.ordinance_versions
    FOR SELECT USING (true);

-- 4. INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_jurisdiction_precedence_primary ON public.jurisdiction_precedence(primary_jurisdiction_id);
CREATE INDEX IF NOT EXISTS idx_jurisdiction_precedence_secondary ON public.jurisdiction_precedence(secondary_jurisdiction_id);
CREATE INDEX IF NOT EXISTS idx_jurisdiction_precedence_type ON public.jurisdiction_precedence(precedence_type);

CREATE INDEX IF NOT EXISTS idx_overlay_conflicts_jurisdiction ON public.overlay_conflicts(jurisdiction_id);
CREATE INDEX IF NOT EXISTS idx_overlay_conflicts_types ON public.overlay_conflicts(overlay_type_1, overlay_type_2);
CREATE INDEX IF NOT EXISTS idx_overlay_conflicts_conflict_type ON public.overlay_conflicts(conflict_type);

CREATE INDEX IF NOT EXISTS idx_ordinance_versions_jurisdiction ON public.ordinance_versions(jurisdiction_name);
CREATE INDEX IF NOT EXISTS idx_ordinance_versions_current ON public.ordinance_versions(is_current) WHERE is_current = true;
CREATE INDEX IF NOT EXISTS idx_ordinance_versions_effective ON public.ordinance_versions(effective_date);
CREATE INDEX IF NOT EXISTS idx_ordinance_versions_lookup ON public.ordinance_versions(jurisdiction_name, ordinance_type, chapter, section) WHERE is_current = true;

-- 5. FUNCTIONS FOR JURISDICTION PRECEDENCE
CREATE OR REPLACE FUNCTION get_jurisdiction_precedence(
    p_primary_jurisdiction_id UUID,
    p_secondary_jurisdiction_id UUID
) RETURNS TABLE (
    precedence_type TEXT,
    rule_description TEXT,
    primary_has_precedence BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT 
        jp.precedence_type,
        jp.rule_description,
        true as primary_has_precedence
    FROM public.jurisdiction_precedence jp
    WHERE jp.primary_jurisdiction_id = p_primary_jurisdiction_id
      AND jp.secondary_jurisdiction_id = p_secondary_jurisdiction_id
      AND jp.effective_date <= CURRENT_DATE
    
    UNION ALL
    
    SELECT 
        jp.precedence_type,
        jp.rule_description,
        false as primary_has_precedence
    FROM public.jurisdiction_precedence jp
    WHERE jp.primary_jurisdiction_id = p_secondary_jurisdiction_id
      AND jp.secondary_jurisdiction_id = p_primary_jurisdiction_id
      AND jp.effective_date <= CURRENT_DATE;
END;
$$;

-- 6. FUNCTIONS FOR OVERLAY CONFLICTS
CREATE OR REPLACE FUNCTION get_overlay_conflicts(
    p_jurisdiction_id UUID,
    p_overlay_types TEXT[]
) RETURNS TABLE (
    overlay_type_1 TEXT,
    overlay_type_2 TEXT,
    conflict_type TEXT,
    resolution_rule TEXT,
    priority_overlay TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT 
        oc.overlay_type_1,
        oc.overlay_type_2,
        oc.conflict_type,
        oc.resolution_rule,
        oc.priority_overlay
    FROM public.overlay_conflicts oc
    WHERE oc.jurisdiction_id = p_jurisdiction_id
      AND (
          (oc.overlay_type_1 = ANY(p_overlay_types) AND oc.overlay_type_2 = ANY(p_overlay_types))
          OR
          (oc.overlay_type_2 = ANY(p_overlay_types) AND oc.overlay_type_1 = ANY(p_overlay_types))
      );
END;
$$;

-- 7. FUNCTIONS FOR ORDINANCE VERSIONS
CREATE OR REPLACE FUNCTION get_current_ordinance(
    p_jurisdiction_name TEXT,
    p_ordinance_type TEXT,
    p_chapter TEXT DEFAULT NULL,
    p_section TEXT DEFAULT NULL,
    p_effective_date DATE DEFAULT CURRENT_DATE
) RETURNS TABLE (
    id UUID,
    version_number INTEGER,
    effective_date DATE,
    content_hash TEXT,
    source_url TEXT,
    change_summary TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT
        ov.id,
        ov.version_number,
        ov.effective_date,
        ov.content_hash,
        ov.source_url,
        ov.change_summary
    FROM public.ordinance_versions ov
    WHERE ov.jurisdiction_name = p_jurisdiction_name
      AND ov.ordinance_type = p_ordinance_type
      AND (p_chapter IS NULL OR ov.chapter = p_chapter)
      AND (p_section IS NULL OR ov.section = p_section)
      AND ov.effective_date <= p_effective_date
      AND (ov.expiration_date IS NULL OR ov.expiration_date > p_effective_date)
      AND ov.is_current = true
    ORDER BY ov.effective_date DESC, ov.version_number DESC
    LIMIT 1;
END;
$$;

-- 8. TRIGGER FUNCTIONS FOR UPDATED_AT
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_jurisdiction_precedence_updated_at 
    BEFORE UPDATE ON public.jurisdiction_precedence 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_overlay_conflicts_updated_at 
    BEFORE UPDATE ON public.overlay_conflicts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ordinance_versions_updated_at 
    BEFORE UPDATE ON public.ordinance_versions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. SAMPLE DATA FOR TESTING
-- Insert sample jurisdiction precedence rules
INSERT INTO public.jurisdiction_precedence (primary_jurisdiction_id, secondary_jurisdiction_id, precedence_type, rule_description)
SELECT
    j1.id as primary_jurisdiction_id,
    j2.id as secondary_jurisdiction_id,
    'municipal_over_county' as precedence_type,
    'Municipal ordinances take precedence over county ordinances within city limits' as rule_description
FROM public.jurisdiction_hierarchy j1
CROSS JOIN public.jurisdiction_hierarchy j2
WHERE j1.jurisdiction_level = 'city'
  AND j2.jurisdiction_level = 'county'
  AND j1.state_code = j2.state_code
  AND j1.jurisdiction_name != j2.jurisdiction_name
LIMIT 10
ON CONFLICT (primary_jurisdiction_id, secondary_jurisdiction_id, precedence_type) DO NOTHING;

-- Insert sample overlay conflict rules
INSERT INTO public.overlay_conflicts (jurisdiction_id, overlay_type_1, overlay_type_2, conflict_type, resolution_rule, priority_overlay)
SELECT
    j.id as jurisdiction_id,
    'environmental' as overlay_type_1,
    'resort' as overlay_type_2,
    'stricter_applies' as conflict_type,
    'Environmental protection requirements take precedence over resort development allowances' as resolution_rule,
    'overlay_1' as priority_overlay
FROM public.jurisdiction_hierarchy j
WHERE j.jurisdiction_level = 'city'
LIMIT 5
ON CONFLICT (jurisdiction_id, overlay_type_1, overlay_type_2) DO NOTHING;

COMMIT;
