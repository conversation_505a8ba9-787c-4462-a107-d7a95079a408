-- Insert initial tags for rule classification
INSERT INTO public.tags (name, category, description) VALUES
('fence', 'property_rule', 'Fencing regulations and requirements'),
('shed', 'property_rule', 'Storage shed and outbuilding rules'),
('mailbox', 'property_rule', 'Mailbox placement and design requirements'),
('pool', 'property_rule', 'Swimming pool and spa regulations'),
('chickens', 'property_rule', 'Backyard chicken and poultry rules'),
('garage', 'property_rule', 'Garage and carport regulations'),
('driveway', 'property_rule', 'Driveway and vehicular access rules'),
('deck', 'property_rule', 'Deck and platform construction rules'),
('patio', 'property_rule', 'Patio and outdoor living space rules'),
('garden', 'property_rule', 'Landscaping and garden regulations'),
('tree', 'property_rule', 'Tree preservation and removal rules'),
('parking', 'property_rule', 'Parking and vehicle storage rules'),
('noise', 'property_rule', 'Noise ordinances and quiet hours'),
('pets', 'property_rule', 'Pet ownership and animal regulations'),
('signs', 'property_rule', 'Signage and display regulations')
ON CONFLICT (name) DO NOTHING;

-- Sample region data (Michigan state outline - simplified)
-- Note: In production, you would import actual GIS boundary data
INSERT INTO public.regions (name, level, state_code, geometry) VALUES
('Michigan', 'state', 'MI', 
 ST_GeomFromText('MULTIPOLYGON(((-90.418 41.696, -82.413 41.696, -82.413 48.306, -90.418 48.306, -90.418 41.696)))', 4326)
)
ON CONFLICT DO NOTHING;

-- Sample county data for testing
INSERT INTO public.regions (name, level, state_code, county_name, geometry) VALUES
('Wayne County', 'county', 'MI', 'Wayne',
 ST_GeomFromText('MULTIPOLYGON(((-83.5 42.0, -82.9 42.0, -82.9 42.6, -83.5 42.6, -83.5 42.0)))', 4326)
),
('Oakland County', 'county', 'MI', 'Oakland',
 ST_GeomFromText('MULTIPOLYGON(((-83.8 42.4, -83.0 42.4, -83.0 43.0, -83.8 43.0, -83.8 42.4)))', 4326)
)
ON CONFLICT DO NOTHING;

-- Sample city data
INSERT INTO public.regions (name, level, state_code, county_name, geometry) VALUES
('Detroit', 'city', 'MI', 'Wayne',
 ST_GeomFromText('MULTIPOLYGON(((-83.3 42.2, -82.9 42.2, -82.9 42.5, -83.3 42.5, -83.3 42.2)))', 4326)
),
('Grand Rapids', 'city', 'MI', 'Kent',
 ST_GeomFromText('MULTIPOLYGON(((-85.8 42.8, -85.5 42.8, -85.5 43.0, -85.8 43.0, -85.8 42.8)))', 4326)
)
ON CONFLICT DO NOTHING;
