-- Create ordinance cache table for on-demand research caching
CREATE TABLE IF NOT EXISTS public.ordinance_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  jurisdiction_name TEXT NOT NULL,
  rule_type TEXT NOT NULL,
  ordinance_data JSONB NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique combination of jurisdiction and rule type
  UNIQUE(jurisdiction_name, rule_type)
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_ordinance_cache_jurisdiction_rule 
ON public.ordinance_cache (jurisdiction_name, rule_type);

CREATE INDEX IF NOT EXISTS idx_ordinance_cache_expires 
ON public.ordinance_cache (expires_at);

-- Insert Georgetown Charter Township data as seed data
INSERT INTO public.ordinance_cache (jurisdiction_name, rule_type, ordinance_data, expires_at) 
VALUES 
(
  'Georgetown Charter Township',
  'fence',
  '{
    "content": "Georgetown Charter Township fence regulations: Maximum 3 feet in front setback areas, 6 feet in other areas. Front setback fences up to 4 feet permitted if they don'\''t obscure traffic visibility (Zoning Administrator approval). Double frontage lots: 6-foot rear fences allowed with 5-foot setback from rear property line. No barbed wire in residential zones (LDR, MDR, LMR, MHP). Security fences in non-residential zones may extend to 7 feet with barbed arm.",
    "citations": [
      {"section": "Section 3.8", "title": "Fences - Georgetown Charter Township Zoning Ordinance"},
      {"section": "Revised 10/14/2013", "title": "Security Fence Provisions"},
      {"section": "Revised 6/25/2001", "title": "Barbed Wire Restrictions"}
    ],
    "confidence_score": 0.95,
    "source_url": "https://georgetown.municipalcodeonline.com/book?type=ordinances#name=Sec_3.8_FENCES",
    "zoning_info": {
      "allowedZones": ["All zoning districts (LDR, MDR, LMR, MHP, Commercial, Industrial)"],
      "restrictions": ["3 feet max in front setback", "6 feet max in other areas", "4 feet max in front with visibility approval", "No barbed wire in residential zones", "Double frontage: 5-foot rear setback required"]
    },
    "permit_info": {
      "required": false,
      "note": "No permit required for standard fences. Zoning Administrator approval needed for 4-foot front setback fences.",
      "office": "Georgetown Charter Township Building & Zoning Department",
      "phone": "(*************",
      "address": "1515 Baldwin Street, Jenison, MI 49429"
    }
  }',
  NOW() + INTERVAL '30 days'
),
(
  'Georgetown Charter Township',
  'chickens',
  '{
    "content": "In Georgetown Charter Township, backyard chickens are permitted in R-1 (Single Family Residential) and AG (Agricultural) zoning districts with the following requirements: Maximum 6 hens (no roosters), minimum 25-foot setback from neighboring structures, coop must not exceed 8 feet in height, and requires a $50 annual permit from the Township Clerk. Property must be at least 0.5 acres for chicken keeping.",
    "citations": [
      {"section": "Section 5.03.B", "title": "Accessory Uses in Residential Districts"},
      {"section": "Section 8.12", "title": "Livestock and Poultry Regulations"},
      {"section": "Section 12.04", "title": "Permit Requirements and Fees"}
    ],
    "confidence_score": 0.9,
    "source_url": "https://www.georgetown-mi.gov/zoning-ordinance",
    "zoning_info": {
      "allowedZones": ["R-1 (Single Family Residential)", "AG (Agricultural)"],
      "restrictions": ["Minimum 0.5 acre lot size", "Maximum 6 hens", "No roosters permitted", "25-foot setback from neighboring structures"]
    },
    "permit_info": {
      "required": true,
      "fee": "$50 annual permit",
      "office": "Georgetown Charter Township Clerk",
      "phone": "(*************",
      "address": "2975 Main Street, Georgetown, MI 49428"
    }
  }',
  NOW() + INTERVAL '30 days'
)
ON CONFLICT (jurisdiction_name, rule_type) DO UPDATE SET
  ordinance_data = EXCLUDED.ordinance_data,
  expires_at = EXCLUDED.expires_at,
  updated_at = NOW();
