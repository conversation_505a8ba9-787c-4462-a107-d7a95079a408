import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        // Background & Foreground
        background: "rgb(var(--background))",
        foreground: "rgb(var(--foreground))",

        // Primary Brand Colors (Trustworthy Teal-Blue)
        primary: {
          50: "rgb(var(--primary-50))",
          100: "rgb(var(--primary-100))",
          200: "rgb(var(--primary-200))",
          300: "rgb(var(--primary-300))",
          400: "rgb(var(--primary-400))",
          500: "rgb(var(--primary-500))",
          600: "rgb(var(--primary-600))",
          700: "rgb(var(--primary-700))",
          800: "rgb(var(--primary-800))",
          900: "rgb(var(--primary-900))",
          950: "rgb(var(--primary-950))",
          DEFAULT: "rgb(var(--primary-500))",
          foreground: "rgb(var(--primary-foreground))",
        },

        // Secondary Neutrals (Enhanced Grays)
        secondary: {
          50: "rgb(var(--secondary-50))",
          100: "rgb(var(--secondary-100))",
          200: "rgb(var(--secondary-200))",
          300: "rgb(var(--secondary-300))",
          400: "rgb(var(--secondary-400))",
          500: "rgb(var(--secondary-500))",
          600: "rgb(var(--secondary-600))",
          700: "rgb(var(--secondary-700))",
          800: "rgb(var(--secondary-800))",
          900: "rgb(var(--secondary-900))",
          950: "rgb(var(--secondary-950))",
          DEFAULT: "rgb(var(--secondary-500))",
          foreground: "rgb(var(--secondary-foreground))",
        },

        // Semantic Colors
        success: {
          50: "rgb(var(--success-50))",
          100: "rgb(var(--success-100))",
          200: "rgb(var(--success-200))",
          300: "rgb(var(--success-300))",
          400: "rgb(var(--success-400))",
          500: "rgb(var(--success-500))",
          600: "rgb(var(--success-600))",
          700: "rgb(var(--success-700))",
          800: "rgb(var(--success-800))",
          900: "rgb(var(--success-900))",
          950: "rgb(var(--success-950))",
          DEFAULT: "rgb(var(--success-500))",
          foreground: "rgb(var(--success-foreground))",
        },

        warning: {
          50: "rgb(var(--warning-50))",
          100: "rgb(var(--warning-100))",
          200: "rgb(var(--warning-200))",
          300: "rgb(var(--warning-300))",
          400: "rgb(var(--warning-400))",
          500: "rgb(var(--warning-500))",
          600: "rgb(var(--warning-600))",
          700: "rgb(var(--warning-700))",
          800: "rgb(var(--warning-800))",
          900: "rgb(var(--warning-900))",
          950: "rgb(var(--warning-950))",
          DEFAULT: "rgb(var(--warning-500))",
          foreground: "rgb(var(--warning-foreground))",
        },

        error: {
          50: "rgb(var(--error-50))",
          100: "rgb(var(--error-100))",
          200: "rgb(var(--error-200))",
          300: "rgb(var(--error-300))",
          400: "rgb(var(--error-400))",
          500: "rgb(var(--error-500))",
          600: "rgb(var(--error-600))",
          700: "rgb(var(--error-700))",
          800: "rgb(var(--error-800))",
          900: "rgb(var(--error-900))",
          950: "rgb(var(--error-950))",
          DEFAULT: "rgb(var(--error-500))",
          foreground: "rgb(var(--error-foreground))",
        },

        info: {
          50: "rgb(var(--info-50))",
          100: "rgb(var(--info-100))",
          200: "rgb(var(--info-200))",
          300: "rgb(var(--info-300))",
          400: "rgb(var(--info-400))",
          500: "rgb(var(--info-500))",
          600: "rgb(var(--info-600))",
          700: "rgb(var(--info-700))",
          800: "rgb(var(--info-800))",
          900: "rgb(var(--info-900))",
          950: "rgb(var(--info-950))",
          DEFAULT: "rgb(var(--info-500))",
          foreground: "rgb(var(--info-foreground))",
        },

        // UI Component Colors
        card: {
          DEFAULT: "rgb(var(--card))",
          foreground: "rgb(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "rgb(var(--popover))",
          foreground: "rgb(var(--popover-foreground))",
        },
        muted: {
          DEFAULT: "rgb(var(--muted))",
          foreground: "rgb(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "rgb(var(--accent))",
          foreground: "rgb(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "rgb(var(--destructive))",
          foreground: "rgb(var(--destructive-foreground))",
        },
        border: "rgb(var(--border))",
        input: "rgb(var(--input))",
        ring: "rgb(var(--ring))",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      boxShadow: {
        'premium': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1), 0 0 0 1px rgb(0 0 0 / 0.05)',
        'premium-lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1), 0 0 0 1px rgb(0 0 0 / 0.05)',
        'premium-xl': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1), 0 0 0 1px rgb(0 0 0 / 0.05)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
} satisfies Config;
