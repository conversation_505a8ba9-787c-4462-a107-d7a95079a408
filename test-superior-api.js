#!/usr/bin/env node

/**
 * Test script for the Superior Research API
 * Tests the new /api/v1/research endpoint
 */

const API_BASE_URL = 'http://localhost:3000'
const TEST_API_KEY = 'test-api-key-ordrly-2024' // Test key from api-key-auth.ts

async function testSuperiorAPI() {
  console.log('🧪 Testing Superior Research API...\n')

  const testRequest = {
    address: '123 Main St, Georgetown, MI',
    query: 'fence height regulations',
    metadata: {
      userAgent: 'Test Script 1.0',
      referrer: 'test-script'
    }
  }

  try {
    console.log('📤 Sending request to /api/v1/research')
    console.log('Address:', testRequest.address)
    console.log('Query:', testRequest.query)
    console.log('')

    const response = await fetch(`${API_BASE_URL}/api/v1/research`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_API_KEY}`
      },
      body: JSON.stringify(testRequest)
    })

    console.log('📥 Response Status:', response.status)
    console.log('📥 Response Headers:')
    for (const [key, value] of response.headers.entries()) {
      if (key.startsWith('x-ratelimit') || key === 'content-type') {
        console.log(`  ${key}: ${value}`)
      }
    }
    console.log('')

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ SUCCESS! API Response:')
      console.log('🏛️ Jurisdiction:', data.data.jurisdiction)
      console.log('🏷️ Topic:', data.data.topic)
      console.log('💰 Cost:', `$${data.data.costUsd || 0}`)
      console.log('⚡ Processing Time:', `${data.data.processingTimeMs}ms`)
      console.log('📊 Confidence:', `${Math.round(data.data.confidence * 100)}%`)
      console.log('💾 Cached:', data.data.cached ? 'Yes' : 'No')
      console.log('🔬 Method:', data.data.method)
      console.log('📚 Sources:', data.data.sources.length)
      console.log('')
      console.log('📝 Answer Preview:')
      console.log(data.data.answer.substring(0, 200) + '...')
      console.log('')
      console.log('🔗 Sources:')
      data.data.sources.forEach((source, i) => {
        console.log(`  ${i + 1}. ${source}`)
      })
    } else {
      console.log('❌ ERROR! API Response:')
      console.log(JSON.stringify(data, null, 2))
    }

  } catch (error) {
    console.error('❌ Request failed:', error.message)
  }
}

// Test rate limiting
async function testRateLimit() {
  console.log('\n🚦 Testing Rate Limiting...')
  
  const quickRequests = []
  for (let i = 0; i < 3; i++) {
    quickRequests.push(
      fetch(`${API_BASE_URL}/api/v1/research`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: JSON.stringify({
          address: '456 Test St, Sample City, CA',
          query: `test query ${i + 1}`
        })
      })
    )
  }

  try {
    const responses = await Promise.all(quickRequests)
    console.log('📊 Rate Limit Test Results:')
    
    for (let i = 0; i < responses.length; i++) {
      const response = responses[i]
      const remaining = response.headers.get('x-ratelimit-remaining')
      console.log(`  Request ${i + 1}: ${response.status} (${remaining} remaining)`)
    }
  } catch (error) {
    console.error('❌ Rate limit test failed:', error.message)
  }
}

// Test invalid API key
async function testInvalidAuth() {
  console.log('\n🔐 Testing Invalid Authentication...')
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/research`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-key'
      },
      body: JSON.stringify({
        address: '789 Test Ave, Demo Town, TX',
        query: 'unauthorized test'
      })
    })

    console.log('📥 Response Status:', response.status)
    const data = await response.json()
    
    if (response.status === 401) {
      console.log('✅ Correctly rejected invalid API key')
      console.log('📝 Error Message:', data.message)
    } else {
      console.log('❌ Should have rejected invalid API key')
      console.log(JSON.stringify(data, null, 2))
    }
  } catch (error) {
    console.error('❌ Auth test failed:', error.message)
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Superior Research API Test Suite')
  console.log('=====================================\n')
  
  await testSuperiorAPI()
  await testRateLimit()
  await testInvalidAuth()
  
  console.log('\n✅ Test suite completed!')
  console.log('\n💡 Next steps:')
  console.log('1. Add environment variables for Perplexity and Google APIs')
  console.log('2. Test with real API keys')
  console.log('3. Deploy to production')
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = { testSuperiorAPI, testRateLimit, testInvalidAuth }
