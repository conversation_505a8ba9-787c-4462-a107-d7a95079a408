/**
 * Database Validation Tests
 * Simple JavaScript tests to validate accuracy improvement tables
 */

const { createClient } = require('@supabase/supabase-js')

describe('Database Accuracy Improvements', () => {
  let supabase

  beforeAll(() => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://qxiryfbdruydrofclmvz.supabase.co'
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    
    if (!supabaseKey) {
      throw new Error('Supabase key not found in environment variables')
    }
    
    supabase = createClient(supabaseUrl, supabaseKey)
  })

  test('should have jurisdiction_precedence table', async () => {
    const { data, error } = await supabase
      .from('jurisdiction_precedence')
      .select('id, precedence_type, rule_description')
      .limit(1)

    expect(error).toBeNull()
    expect(data).toBeDefined()
    console.log('✅ jurisdiction_precedence table accessible')
  })

  test('should have overlay_conflicts table', async () => {
    const { data, error } = await supabase
      .from('overlay_conflicts')
      .select('id, conflict_type, resolution_rule')
      .limit(1)

    expect(error).toBeNull()
    expect(data).toBeDefined()
    console.log('✅ overlay_conflicts table accessible')
  })

  test('should have ordinance_versions table', async () => {
    const { data, error } = await supabase
      .from('ordinance_versions')
      .select('id, version_number, is_current')
      .limit(1)

    expect(error).toBeNull()
    expect(data).toBeDefined()
    console.log('✅ ordinance_versions table accessible')
  })

  test('should have get_jurisdiction_precedence function', async () => {
    const { data, error } = await supabase
      .rpc('get_jurisdiction_precedence', {
        p_primary_jurisdiction_id: '00000000-0000-0000-0000-000000000000',
        p_secondary_jurisdiction_id: '00000000-0000-0000-0000-000000000000'
      })

    expect(error).toBeNull() // Function should exist even if no data
    console.log('✅ get_jurisdiction_precedence function available')
  })

  test('should have get_overlay_conflicts function', async () => {
    const { data, error } = await supabase
      .rpc('get_overlay_conflicts', {
        p_jurisdiction_id: '00000000-0000-0000-0000-000000000000',
        p_overlay_types: ['test']
      })

    expect(error).toBeNull() // Function should exist even if no data
    console.log('✅ get_overlay_conflicts function available')
  })

  test('should have get_current_ordinance function', async () => {
    const { data, error } = await supabase
      .rpc('get_current_ordinance', {
        p_jurisdiction_name: 'Test City',
        p_ordinance_type: 'test'
      })

    expect(error).toBeNull() // Function should exist even if no data
    console.log('✅ get_current_ordinance function available')
  })

  test('should have sample data in jurisdiction_precedence', async () => {
    const { data, error } = await supabase
      .from('jurisdiction_precedence')
      .select('*')
      .limit(5)

    expect(error).toBeNull()
    console.log(`📊 Found ${data?.length || 0} jurisdiction precedence rules`)
    
    if (data && data.length > 0) {
      console.log('Sample precedence rule:', {
        type: data[0].precedence_type,
        description: data[0].rule_description.substring(0, 50) + '...'
      })
    }
  })

  test('should have sample data in overlay_conflicts', async () => {
    const { data, error } = await supabase
      .from('overlay_conflicts')
      .select('*')
      .limit(5)

    expect(error).toBeNull()
    console.log(`📊 Found ${data?.length || 0} overlay conflict rules`)
    
    if (data && data.length > 0) {
      console.log('Sample overlay conflict:', {
        overlay1: data[0].overlay_type_1,
        overlay2: data[0].overlay_type_2,
        conflictType: data[0].conflict_type
      })
    }
  })

  test('accuracy improvements summary', async () => {
    // Get counts from all tables
    const [precedenceResult, conflictResult, versionResult] = await Promise.all([
      supabase.from('jurisdiction_precedence').select('id', { count: 'exact', head: true }),
      supabase.from('overlay_conflicts').select('id', { count: 'exact', head: true }),
      supabase.from('ordinance_versions').select('id', { count: 'exact', head: true })
    ])

    const summary = {
      jurisdiction_precedence_rules: precedenceResult.count || 0,
      overlay_conflict_rules: conflictResult.count || 0,
      ordinance_versions: versionResult.count || 0
    }

    console.log('📈 Accuracy Improvements Database Summary:', summary)

    // Expect at least some data to be present
    const totalRules = summary.jurisdiction_precedence_rules + summary.overlay_conflict_rules
    expect(totalRules).toBeGreaterThan(0)
    
    console.log('✅ Database accuracy improvements successfully deployed!')
  })
})
