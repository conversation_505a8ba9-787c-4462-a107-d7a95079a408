/**
 * Natural Language API Test - Test AI-powered compliance research
 * Tests the system with natural language queries instead of rigid rule types
 */

describe('Natural Language API Test', () => {
  const API_BASE_URL = 'http://localhost:3000'

  test('should test compliance API with natural language queries', async () => {
    console.log('\n🚀 TESTING NATURAL LANGUAGE COMPLIANCE QUERIES')
    console.log('=' .repeat(60))

    // Test cases with natural language queries - more realistic user questions
    const testCases = [
      {
        address: '123 Main St, Austin, TX',
        query: 'What are the fence height requirements for my backyard?',
        expectedBehavior: 'Should analyze fence regulations and provide specific height requirements'
      },
      {
        address: '456 Oak Ave, Charlotte, NC',
        query: 'Can I build a shed in my backyard and what are the setback requirements?',
        expectedBehavior: 'Should analyze accessory structure and setback regulations'
      },
      {
        address: '789 Pine St, Austin, TX',
        query: 'I want to add a deck to my house. Do I need a permit and what are the requirements?',
        expectedBehavior: 'Should analyze deck/addition regulations and permit requirements'
      }
    ]

    const results = []

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing Natural Language Query:`)
      console.log(`Address: ${testCase.address}`)
      console.log(`Query: ${testCase.query}`)
      console.log(`Expected: ${testCase.expectedBehavior}`)

      try {
        const startTime = Date.now()

        // Test the compliance summary API with natural language
        const response = await fetch(`${API_BASE_URL}/api/compliance/summary`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            address: testCase.address,
            // Instead of rigid ruleType, let's see if we can use the query directly
            ruleType: 'general', // Use a general type and let AI figure it out
            userQuery: testCase.query // Add the natural language query
          })
        })

        const requestTime = Date.now() - startTime

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`API request failed: ${response.status} - ${errorText.substring(0, 200)}`)
        }

        const data = await response.json()

        console.log(`  📊 API Response (${requestTime}ms):`)
        console.log(`    Success: ${data.success ? '✅' : '❌'}`)
        console.log(`    Jurisdiction: ${data.jurisdiction || 'N/A'}`)
        
        if (data.analysis) {
          console.log(`    Summary: ${data.analysis.summary?.substring(0, 100)}...`)
          console.log(`    Permit Required: ${data.analysis.permit_required ? '✅' : '❌'}`)
          console.log(`    Requirements: ${data.analysis.requirements?.length || 0}`)
          console.log(`    Prohibited Practices: ${data.analysis.prohibited_practices?.length || 0}`)
          console.log(`    Citations: ${data.analysis.citations?.length || 0}`)
          console.log(`    Confidence: ${data.analysis.confidence_score ? (data.analysis.confidence_score * 100).toFixed(1) + '%' : 'N/A'}`)
        }

        if (data.sources) {
          console.log(`    Sources Found: ${data.sources.length}`)
          data.sources.slice(0, 2).forEach((source, index) => {
            console.log(`      ${index + 1}. ${source.title}`)
            console.log(`         URL: ${source.url}`)
            console.log(`         Success: ${source.fetchSuccess ? '✅' : '❌'}`)
          })
        }

        if (data.error) {
          console.log(`    Error: ${data.error}`)
        }

        // Check if the AI understood the query correctly
        let queryUnderstanding = 'Unknown'
        if (data.analysis?.tags) {
          if (testCase.query.includes('fence') && data.analysis.tags.some(tag => tag.includes('fence'))) {
            queryUnderstanding = 'Fence query understood ✅'
          } else if (testCase.query.includes('shed') && data.analysis.tags.some(tag => tag.includes('shed') || tag.includes('accessory'))) {
            queryUnderstanding = 'Shed query understood ✅'
          } else if (testCase.query.includes('deck') && data.analysis.tags.some(tag => tag.includes('deck') || tag.includes('addition'))) {
            queryUnderstanding = 'Deck query understood ✅'
          } else {
            queryUnderstanding = 'Query understanding unclear ⚠️'
          }
        }

        console.log(`    Query Understanding: ${queryUnderstanding}`)

        // Check for specific requirements that match the query
        let specificRequirements = 'None found'
        if (data.analysis?.requirements) {
          const requirements = data.analysis.requirements.join(' ').toLowerCase()
          if (testCase.query.includes('fence') && (requirements.includes('height') || requirements.includes('fence'))) {
            specificRequirements = 'Fence requirements found ✅'
          } else if (testCase.query.includes('shed') && (requirements.includes('setback') || requirements.includes('shed'))) {
            specificRequirements = 'Shed requirements found ✅'
          } else if (testCase.query.includes('deck') && (requirements.includes('deck') || requirements.includes('permit'))) {
            specificRequirements = 'Deck requirements found ✅'
          }
        }

        console.log(`    Specific Requirements: ${specificRequirements}`)

        results.push({
          address: testCase.address,
          query: testCase.query,
          success: data.success,
          jurisdiction: data.jurisdiction,
          confidence: data.analysis?.confidence_score,
          permitRequired: data.analysis?.permit_required,
          requirementsCount: data.analysis?.requirements?.length || 0,
          citationsCount: data.analysis?.citations?.length || 0,
          sourcesCount: data.sources?.length || 0,
          sourcesSuccessful: data.sources?.filter(s => s.fetchSuccess).length || 0,
          requestTime,
          hasAnalysis: !!data.analysis,
          hasError: !!data.error,
          queryUnderstanding,
          specificRequirements,
          tags: data.analysis?.tags || []
        })

      } catch (error) {
        console.log(`  ❌ Error: ${error.message}`)

        results.push({
          address: testCase.address,
          query: testCase.query,
          success: false,
          error: error.message,
          requestTime: 0
        })
      }
    }

    // Analyze results
    console.log('\n📊 NATURAL LANGUAGE API TEST RESULTS:')
    console.log('=' .repeat(50))

    const successfulTests = results.filter(r => r.success).length
    const withAnalysis = results.filter(r => r.hasAnalysis).length
    const withErrors = results.filter(r => r.hasError).length
    const queryUnderstood = results.filter(r => r.queryUnderstanding?.includes('✅')).length
    const specificReqsFound = results.filter(r => r.specificRequirements?.includes('✅')).length

    console.log(`\n🎯 OVERALL RESULTS:`)
    console.log(`Success Rate: ${successfulTests}/${results.length} (${(successfulTests/results.length*100).toFixed(1)}%)`)
    console.log(`With Analysis: ${withAnalysis}/${results.length} tests`)
    console.log(`With Errors: ${withErrors}/${results.length} tests`)
    console.log(`Query Understanding: ${queryUnderstood}/${results.length} tests`)
    console.log(`Specific Requirements Found: ${specificReqsFound}/${results.length} tests`)

    console.log(`\n📈 INTELLIGENCE ANALYSIS:`)
    const avgConfidence = results.filter(r => r.confidence).reduce((sum, r) => sum + r.confidence, 0) / results.filter(r => r.confidence).length
    const avgRequirements = results.reduce((sum, r) => sum + r.requirementsCount, 0) / results.length
    const avgCitations = results.reduce((sum, r) => sum + r.citationsCount, 0) / results.length
    const avgSources = results.reduce((sum, r) => sum + r.sourcesCount, 0) / results.length
    const avgSourcesSuccessful = results.reduce((sum, r) => sum + r.sourcesSuccessful, 0) / results.length

    console.log(`Average Confidence: ${avgConfidence ? (avgConfidence * 100).toFixed(1) + '%' : 'N/A'}`)
    console.log(`Average Requirements per Query: ${avgRequirements.toFixed(1)}`)
    console.log(`Average Citations per Query: ${avgCitations.toFixed(1)}`)
    console.log(`Average Sources per Query: ${avgSources.toFixed(1)}`)
    console.log(`Average Successful Sources: ${avgSourcesSuccessful.toFixed(1)}`)

    console.log(`\n⏱️  PERFORMANCE:`)
    const avgRequestTime = results.reduce((sum, r) => sum + r.requestTime, 0) / results.length
    console.log(`Average Request Time: ${avgRequestTime.toFixed(0)}ms`)

    // Key insights about natural language processing
    console.log(`\n🔍 NATURAL LANGUAGE INSIGHTS:`)
    if (queryUnderstood === results.length) {
      console.log('✅ Perfect query understanding - AI correctly interprets all natural language queries')
    } else if (queryUnderstood > results.length / 2) {
      console.log('⚠️  Good query understanding - AI interprets most queries correctly')
    } else {
      console.log('🚨 Poor query understanding - AI struggles with natural language queries')
    }

    if (specificReqsFound === results.length) {
      console.log('✅ Perfect requirement matching - AI finds specific requirements for all queries')
    } else if (specificReqsFound > results.length / 2) {
      console.log('⚠️  Good requirement matching - AI finds relevant requirements for most queries')
    } else {
      console.log('🚨 Poor requirement matching - AI struggles to find specific requirements')
    }

    // Store results for further analysis
    global.naturalLanguageTestResults = results

    expect(results.length).toBe(testCases.length)

  }, 120000) // 2 minute timeout for natural language processing
})
