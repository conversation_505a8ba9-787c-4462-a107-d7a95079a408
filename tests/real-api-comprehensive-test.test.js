#!/usr/bin/env node

/**
 * Comprehensive Real API Test Suite
 * Tests the actual compliance research system with working API key
 * Uses real addresses and measures actual performance
 */

const fetch = require('node-fetch')

// Test configuration
const API_KEY = 'ordrly_7elosrmcjjp22md2km5q' // Working API <NAME_EMAIL> (Pro tier)
const BASE_URL = 'http://localhost:3000'
const TEST_TIMEOUT = 60000 // 60 seconds per test

// Test addresses from existing test files
const testAddresses = [
  // Major cities (should have good data coverage)
  { jurisdiction: 'Austin, TX', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Seattle, WA', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Denver, CO', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Portland, OR', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Phoenix, AZ', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'San Diego, CA', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Miami, FL', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Atlanta, GA', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Chicago, IL', category: 'major_city', expectedDifficulty: 'easy' },
  { jurisdiction: 'Boston, MA', category: 'major_city', expectedDifficulty: 'easy' },
  
  // Medium cities
  { jurisdiction: 'Grand Rapids, MI', category: 'medium_city', expectedDifficulty: 'medium' },
  { jurisdiction: 'Spokane, WA', category: 'medium_city', expectedDifficulty: 'medium' },
  { jurisdiction: 'Fort Collins, CO', category: 'medium_city', expectedDifficulty: 'medium' },
  { jurisdiction: 'Eugene, OR', category: 'medium_city', expectedDifficulty: 'medium' },
  { jurisdiction: 'Tucson, AZ', category: 'medium_city', expectedDifficulty: 'medium' },
  
  // Small cities and rural areas
  { jurisdiction: 'Aspen, CO', category: 'small_city', expectedDifficulty: 'hard' },
  { jurisdiction: 'Key West, FL', category: 'small_city', expectedDifficulty: 'hard' },
  { jurisdiction: 'Nantucket, MA', category: 'small_city', expectedDifficulty: 'hard' },
  { jurisdiction: 'Park City, UT', category: 'small_city', expectedDifficulty: 'hard' },
  { jurisdiction: 'Jackson, WY', category: 'small_city', expectedDifficulty: 'hard' },
]

// Rule types to test
const ruleTypes = [
  'fence', 'setback', 'building_height', 'pool', 'deck', 'garage', 'addition'
]

/**
 * Test the research API with a specific jurisdiction and rule type
 */
async function testResearchAPI(jurisdiction, ruleType, userQuery) {
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${BASE_URL}/api/research`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jurisdiction,
        ruleType,
        userQuery,
        requireHighConfidence: false // Use 90% threshold
      }),
      timeout: TEST_TIMEOUT
    })

    const processingTime = Date.now() - startTime

    if (!response.ok) {
      const errorText = await response.text()
      return {
        success: false,
        error: `HTTP ${response.status}: ${errorText}`,
        processingTime,
        confidence: 0,
        sources: 0
      }
    }

    const result = await response.json()
    
    return {
      success: result.success || false,
      confidence: result.confidence || 0,
      sources: result.sources?.length || 0,
      citations: result.citations?.length || 0,
      hasContent: !!(result.content && result.content.length > 0),
      triggeredResearch: result.metadata?.triggered_research || false,
      usedCache: result.metadata?.used_cache || false,
      processingTime,
      apiProcessingTime: result.metadata?.api_processing_time_ms || 0,
      userTier: result.metadata?.user_tier || 'unknown',
      error: result.error || null,
      helpMessage: result.help_message || null,
      suggestedActions: result.suggested_actions || []
    }

  } catch (error) {
    return {
      success: false,
      error: error.message,
      processingTime: Date.now() - startTime,
      confidence: 0,
      sources: 0
    }
  }
}

/**
 * Generate test cases
 */
function generateTestCases(maxTests = 50) {
  const testCases = []
  let testId = 1

  // Generate tests for each address/rule combination
  for (const address of testAddresses) {
    for (const ruleType of ruleTypes) {
      if (testId > maxTests) break
      
      testCases.push({
        id: testId++,
        jurisdiction: address.jurisdiction,
        ruleType,
        userQuery: `What are the ${ruleType} requirements in ${address.jurisdiction}?`,
        category: address.category,
        expectedDifficulty: address.expectedDifficulty
      })
    }
    if (testId > maxTests) break
  }

  return testCases.slice(0, maxTests)
}

/**
 * Run comprehensive test suite
 */
async function runComprehensiveTests(maxTests = 50) {
  console.log('🚀 Starting Comprehensive Real API Test Suite')
  console.log('=' .repeat(60))
  console.log(`API Key: ${API_KEY.substring(0, 15)}...`)
  console.log(`Base URL: ${BASE_URL}`)
  console.log(`Max Tests: ${maxTests}`)
  console.log(`Timeout per test: ${TEST_TIMEOUT}ms`)
  console.log('')

  const testCases = generateTestCases(maxTests)
  const results = []
  const startTime = Date.now()

  console.log(`📋 Generated ${testCases.length} test cases`)
  console.log('Test distribution:', {
    major_cities: testCases.filter(t => t.category === 'major_city').length,
    medium_cities: testCases.filter(t => t.category === 'medium_city').length,
    small_cities: testCases.filter(t => t.category === 'small_city').length
  })
  console.log('')

  // Run tests
  for (const testCase of testCases) {
    console.log(`🔍 Test ${testCase.id}/${testCases.length}: ${testCase.jurisdiction} + ${testCase.ruleType}`)
    
    const result = await testResearchAPI(
      testCase.jurisdiction,
      testCase.ruleType,
      testCase.userQuery
    )

    results.push({
      ...testCase,
      ...result
    })

    // Log result
    if (result.success) {
      console.log(`   ✅ SUCCESS - Confidence: ${Math.round(result.confidence * 100)}%, Sources: ${result.sources}, Time: ${result.processingTime}ms`)
    } else {
      console.log(`   ❌ FAILED - ${result.error || 'Unknown error'}, Time: ${result.processingTime}ms`)
    }

    // Progress update
    if (testCase.id % 10 === 0) {
      const completed = testCase.id
      const successRate = (results.filter(r => r.success).length / completed) * 100
      console.log(`   📊 Progress: ${completed}/${testCases.length} (${successRate.toFixed(1)}% success rate)`)
    }

    // Small delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  const totalTime = Date.now() - startTime

  return { results, totalTime, testCases }
}

// Export for use in tests or run directly
if (require.main === module) {
  runComprehensiveTests(50)
    .then(({ results, totalTime, testCases }) => {
      console.log('\n🎯 COMPREHENSIVE TEST RESULTS')
      console.log('=' .repeat(60))
      
      // Calculate statistics
      const totalTests = results.length
      const successfulTests = results.filter(r => r.success).length
      const failedTests = totalTests - successfulTests
      const overallAccuracy = (successfulTests / totalTests) * 100
      
      const successfulResults = results.filter(r => r.success)
      const averageConfidence = successfulResults.length > 0 
        ? successfulResults.reduce((sum, r) => sum + r.confidence, 0) / successfulResults.length 
        : 0
      
      const averageProcessingTime = results.reduce((sum, r) => sum + r.processingTime, 0) / totalTests
      const researchTriggered = results.filter(r => r.triggeredResearch).length
      const cacheUsed = results.filter(r => r.usedCache).length
      
      console.log(`Total Tests: ${totalTests}`)
      console.log(`Successful: ${successfulTests}`)
      console.log(`Failed: ${failedTests}`)
      console.log(`Overall Accuracy: ${overallAccuracy.toFixed(1)}%`)
      console.log(`Average Confidence: ${(averageConfidence * 100).toFixed(1)}%`)
      console.log(`Average Processing Time: ${averageProcessingTime.toFixed(0)}ms`)
      console.log(`Total Execution Time: ${(totalTime / 1000).toFixed(1)}s`)
      console.log(`Research Triggered: ${researchTriggered}/${totalTests} (${(researchTriggered/totalTests*100).toFixed(1)}%)`)
      console.log(`Cache Used: ${cacheUsed}/${totalTests} (${(cacheUsed/totalTests*100).toFixed(1)}%)`)
      
      // Category breakdown
      console.log('\n📊 CATEGORY BREAKDOWN:')
      const categories = ['major_city', 'medium_city', 'small_city']
      categories.forEach(category => {
        const categoryResults = results.filter(r => r.category === category)
        const categorySuccessful = categoryResults.filter(r => r.success).length
        const categoryAccuracy = categoryResults.length > 0 ? (categorySuccessful / categoryResults.length) * 100 : 0
        console.log(`${category.toUpperCase()}: ${categorySuccessful}/${categoryResults.length} (${categoryAccuracy.toFixed(1)}%)`)
      })
      
      // Performance assessment
      console.log('\n🎯 PERFORMANCE ASSESSMENT:')
      if (overallAccuracy >= 90) {
        console.log('🎉 EXCELLENT - Meeting high accuracy standards!')
      } else if (overallAccuracy >= 75) {
        console.log('✅ GOOD - Solid performance with room for improvement')
      } else if (overallAccuracy >= 50) {
        console.log('⚠️ NEEDS IMPROVEMENT - Significant accuracy gaps')
      } else {
        console.log('❌ CRITICAL - Major system issues identified')
      }
      
      console.log('\n🔍 NEXT STEPS:')
      if (overallAccuracy < 90) {
        console.log('1. Analyze failed test cases for patterns')
        console.log('2. Implement 5 Whys analysis on top failure causes')
        console.log('3. Focus on improving data coverage for failing jurisdictions')
        console.log('4. Consider lowering confidence threshold temporarily')
      } else {
        console.log('1. System is performing well!')
        console.log('2. Consider expanding test coverage')
        console.log('3. Focus on optimizing processing time')
      }
      
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Test suite failed:', error)
      process.exit(1)
    })
}

module.exports = { runComprehensiveTests, testResearchAPI, generateTestCases }
