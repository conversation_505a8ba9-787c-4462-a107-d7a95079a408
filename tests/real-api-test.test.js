/**
 * Real API Test - Test actual ResearchIntegrationService via API
 * Uses API key authentication to test the real system
 */

describe('Real API Test', () => {
  const API_BASE_URL = 'http://localhost:3000'
  const TEST_API_KEY = 'test-api-key-ordrly-2024'

  test('should verify API endpoint is accessible', async () => {
    console.log('🔍 CHECKING API ENDPOINT ACCESSIBILITY')
    console.log('=' .repeat(50))

    try {
      const response = await fetch(`${API_BASE_URL}/api/research`, {
        method: 'GET'
      })

      if (response.ok) {
        const data = await response.json()
        console.log('✅ API endpoint is accessible')
        console.log('📖 API Documentation:')
        console.log(`  Endpoint: ${data.endpoint}`)
        console.log(`  Description: ${data.description}`)
        console.log(`  Test API Key: ${data.testApiKey}`)
        console.log(`  Test Command: ${data.testCommand}`)
      } else {
        console.log(`⚠️  API responded with status: ${response.status}`)
      }
    } catch (error) {
      console.log('❌ API endpoint not accessible')
      console.log(`   Error: ${error.message}`)
      console.log('   Please ensure the server is running: npm run dev')
    }
  })

  test('should test real research service with 2 jurisdictions', async () => {
    console.log('\n🚀 TESTING REAL RESEARCH SERVICE VIA API')
    console.log('=' .repeat(60))

    // Test cases - start with just 2 as suggested
    const testCases = [
      {
        jurisdiction: 'Austin, TX',
        ruleType: 'fence',
        userQuery: 'What are the fence height requirements for my backyard?',
        expectedBehavior: 'Should find data in compliance_knowledge (75% confidence)'
      },
      {
        jurisdiction: 'Charlotte, NC',
        ruleType: 'setback',
        userQuery: 'What are the setback requirements for a shed?',
        expectedBehavior: 'Should trigger research (not in RAG database)'
      }
    ]

    const results = []

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing: ${testCase.jurisdiction} - ${testCase.ruleType}`)
      console.log(`Query: ${testCase.userQuery}`)
      console.log(`Expected: ${testCase.expectedBehavior}`)

      try {
        const startTime = Date.now()

        const response = await fetch(`${API_BASE_URL}/api/research`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TEST_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            jurisdiction: testCase.jurisdiction,
            ruleType: testCase.ruleType,
            userQuery: testCase.userQuery,
            requireHighConfidence: false // Start with lower threshold to see what happens
          })
        })

        const requestTime = Date.now() - startTime

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(`API request failed: ${response.status} - ${errorData.error}`)
        }

        const data = await response.json()

        console.log(`  📊 API Response (${requestTime}ms):`)
        console.log(`    Success: ${data.success ? '✅' : '❌'}`)
        console.log(`    Confidence: ${data.confidence ? (data.confidence * 100).toFixed(1) + '%' : 'N/A'}`)
        console.log(`    Sources: ${data.sources?.length || 0}`)
        console.log(`    Citations: ${data.citations?.length || 0}`)

        if (data.metadata) {
          console.log(`    Used Cache: ${data.metadata.used_cache ? '✅' : '❌'}`)
          console.log(`    Triggered Research: ${data.metadata.triggered_research ? '✅' : '❌'}`)
          console.log(`    Quality Score: ${data.metadata.quality_score || 'N/A'}`)
          console.log(`    Processing Time: ${data.metadata.processing_time_ms || 'N/A'}ms`)
          console.log(`    API Processing Time: ${data.metadata.api_processing_time_ms || 'N/A'}ms`)
        }

        if (data.content) {
          console.log(`    Content Preview: ${data.content.substring(0, 100)}...`)
        }

        if (data.error) {
          console.log(`    Error: ${data.error}`)
        }

        if (data.help_message) {
          console.log(`    Help Message: ${data.help_message.substring(0, 100)}...`)
        }

        if (data.suggested_actions) {
          console.log(`    Suggested Actions: ${data.suggested_actions.join(', ')}`)
        }

        // Check for accuracy improvement features
        if (data.metadata) {
          if (data.metadata.is_rural_area) {
            console.log(`    🏞️  Rural area detected`)
          }
          if (data.metadata.boundary_uncertainty) {
            console.log(`    ⚠️  Boundary uncertainty detected`)
          }
          if (data.metadata.jurisdiction_conflicts) {
            console.log(`    ⚖️  Jurisdiction conflicts: ${data.metadata.jurisdiction_conflicts.length}`)
          }
          if (data.metadata.overlay_conflicts) {
            console.log(`    🔄 Overlay conflicts: ${data.metadata.overlay_conflicts.length}`)
          }
        }

        results.push({
          jurisdiction: testCase.jurisdiction,
          ruleType: testCase.ruleType,
          success: data.success,
          confidence: data.confidence,
          sourcesCount: data.sources?.length || 0,
          citationsCount: data.citations?.length || 0,
          usedCache: data.metadata?.used_cache,
          triggeredResearch: data.metadata?.triggered_research,
          qualityScore: data.metadata?.quality_score,
          processingTime: data.metadata?.processing_time_ms,
          apiProcessingTime: data.metadata?.api_processing_time_ms,
          requestTime,
          hasContent: !!data.content,
          hasError: !!data.error,
          hasHelpMessage: !!data.help_message,
          isRuralArea: data.metadata?.is_rural_area,
          hasBoundaryUncertainty: data.metadata?.boundary_uncertainty,
          accuracyFeatures: {
            jurisdictionConflicts: data.metadata?.jurisdiction_conflicts?.length || 0,
            overlayConflicts: data.metadata?.overlay_conflicts?.length || 0
          }
        })

      } catch (error) {
        console.log(`  ❌ Error: ${error.message}`)

        results.push({
          jurisdiction: testCase.jurisdiction,
          ruleType: testCase.ruleType,
          success: false,
          error: error.message,
          requestTime: 0
        })
      }
    }

    // Analyze results
    console.log('\n📊 REAL API TEST RESULTS SUMMARY:')
    console.log('=' .repeat(50))

    const successfulTests = results.filter(r => r.success).length
    const researchTriggered = results.filter(r => r.triggeredResearch).length
    const cacheUsed = results.filter(r => r.usedCache).length
    const withContent = results.filter(r => r.hasContent).length
    const withErrors = results.filter(r => r.hasError).length
    const withHelpMessages = results.filter(r => r.hasHelpMessage).length

    console.log(`\n🎯 OVERALL RESULTS:`)
    console.log(`Success Rate: ${successfulTests}/${results.length} (${(successfulTests/results.length*100).toFixed(1)}%)`)
    console.log(`Research Triggered: ${researchTriggered}/${results.length} tests`)
    console.log(`Cache Used: ${cacheUsed}/${results.length} tests`)
    console.log(`With Content: ${withContent}/${results.length} tests`)
    console.log(`With Errors: ${withErrors}/${results.length} tests`)
    console.log(`With Help Messages: ${withHelpMessages}/${results.length} tests`)

    console.log(`\n📈 ACCURACY FEATURES:`)
    const ruralDetected = results.filter(r => r.isRuralArea).length
    const boundaryUncertainty = results.filter(r => r.hasBoundaryUncertainty).length
    const jurisdictionConflicts = results.reduce((sum, r) => sum + (r.accuracyFeatures?.jurisdictionConflicts || 0), 0)
    const overlayConflicts = results.reduce((sum, r) => sum + (r.accuracyFeatures?.overlayConflicts || 0), 0)

    console.log(`Rural Areas Detected: ${ruralDetected}`)
    console.log(`Boundary Uncertainty: ${boundaryUncertainty}`)
    console.log(`Jurisdiction Conflicts: ${jurisdictionConflicts}`)
    console.log(`Overlay Conflicts: ${overlayConflicts}`)

    console.log(`\n⏱️  PERFORMANCE:`)
    const avgProcessingTime = results.filter(r => r.processingTime).reduce((sum, r) => sum + r.processingTime, 0) / results.filter(r => r.processingTime).length
    const avgRequestTime = results.reduce((sum, r) => sum + r.requestTime, 0) / results.length

    console.log(`Average Processing Time: ${avgProcessingTime?.toFixed(0) || 'N/A'}ms`)
    console.log(`Average Request Time: ${avgRequestTime.toFixed(0)}ms`)

    // Key insights
    console.log(`\n🔍 KEY INSIGHTS:`)
    if (successfulTests === 0) {
      console.log('🚨 CRITICAL: All tests failed - system is not functional')
    } else if (researchTriggered === 0) {
      console.log('⚠️  WARNING: No research was triggered - may indicate RAG-only responses')
    } else {
      console.log('✅ System is functional and triggering research as expected')
    }

    if (withHelpMessages > 0) {
      console.log('✅ Enhanced error messaging is working')
    }

    // Store results for further analysis
    global.realApiTestResults = results

    expect(results.length).toBe(testCases.length)

  }, 60000) // 1 minute timeout
})
