/**
 * Real System Analysis - Test actual ResearchIntegrationService
 * Find the true bottlenecks causing low accuracy
 */

const { createClient } = require('@supabase/supabase-js')

describe('Real System Analysis', () => {
  let supabase

  beforeAll(() => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://qxiryfbdruydrofclmvz.supabase.co'
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    
    if (!supabaseKey) {
      throw new Error('Supabase key not found in environment variables')
    }
    
    supabase = createClient(supabaseUrl, supabaseKey)
  })

  test('should analyze real data coverage issues', async () => {
    console.log('🔍 REAL SYSTEM ANALYSIS - DATA COVERAGE')
    console.log('=' .repeat(60))

    // Check compliance_knowledge coverage
    const { data: complianceData, error: complianceError } = await supabase
      .from('compliance_knowledge')
      .select('jurisdiction, confidence_score, verification_status, source_url')
      .eq('is_active', true)

    console.log('\n📊 COMPLIANCE_KNOWLEDGE TABLE:')
    console.log(`Total records: ${complianceData?.length || 0}`)
    
    if (complianceData && complianceData.length > 0) {
      const jurisdictions = [...new Set(complianceData.map(r => r.jurisdiction))]
      const highConfidence = complianceData.filter(r => r.confidence_score >= 0.90).length
      const verified = complianceData.filter(r => r.verification_status === 'verified').length
      const withUrls = complianceData.filter(r => r.source_url && r.source_url.length > 0).length
      
      console.log(`Unique jurisdictions: ${jurisdictions.length}`)
      console.log(`Jurisdictions: ${jurisdictions.join(', ')}`)
      console.log(`High confidence (≥90%): ${highConfidence}/${complianceData.length} (${(highConfidence/complianceData.length*100).toFixed(1)}%)`)
      console.log(`Verified sources: ${verified}/${complianceData.length} (${(verified/complianceData.length*100).toFixed(1)}%)`)
      console.log(`With source URLs: ${withUrls}/${complianceData.length} (${(withUrls/complianceData.length*100).toFixed(1)}%)`)
    }

    // Check ordinance_cache coverage
    const { data: cacheData, error: cacheError } = await supabase
      .from('ordinance_cache')
      .select('jurisdiction_name, rule_type')

    console.log('\n📊 ORDINANCE_CACHE TABLE:')
    console.log(`Total records: ${cacheData?.length || 0}`)
    
    if (cacheData && cacheData.length > 0) {
      const jurisdictions = [...new Set(cacheData.map(r => r.jurisdiction_name))]
      const ruleTypes = [...new Set(cacheData.map(r => r.rule_type))]
      
      console.log(`Unique jurisdictions: ${jurisdictions.length}`)
      console.log(`Major cities in cache: ${jurisdictions.filter(j => 
        ['Austin', 'Chicago', 'Los Angeles', 'New York', 'San Francisco', 'Detroit', 'Grand Rapids'].includes(j)
      ).join(', ')}`)
      console.log(`Rule types: ${ruleTypes.length} types`)
    }

    // Test confidence threshold impact
    console.log('\n🎯 CONFIDENCE THRESHOLD ANALYSIS:')
    if (complianceData && complianceData.length > 0) {
      const thresholds = [0.50, 0.60, 0.70, 0.80, 0.90, 0.95]
      thresholds.forEach(threshold => {
        const passing = complianceData.filter(r => r.confidence_score >= threshold).length
        const rate = (passing / complianceData.length * 100).toFixed(1)
        console.log(`Threshold ${(threshold*100).toFixed(0)}%: ${passing}/${complianceData.length} records pass (${rate}%)`)
      })
    }

    expect(complianceData).toBeDefined()
    expect(cacheData).toBeDefined()
  })

  test('should test real research service with sample jurisdictions', async () => {
    console.log('\n🧪 TESTING REAL RESEARCH SERVICE')
    console.log('=' .repeat(60))

    // Test jurisdictions that should have data
    const testCases = [
      { jurisdiction: 'Austin, TX', ruleType: 'fence', expectedInCache: true },
      { jurisdiction: 'Chicago, IL', ruleType: 'deck', expectedInCache: true },
      { jurisdiction: 'Grand Rapids, MI', ruleType: 'shed', expectedInCache: true },
      { jurisdiction: 'Charlotte, NC', ruleType: 'setback', expectedInCache: false },
      { jurisdiction: 'Denver, CO', ruleType: 'pool', expectedInCache: false }
    ]

    const results = []

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing: ${testCase.jurisdiction} - ${testCase.ruleType}`)
      
      try {
        // Check if data exists in compliance_knowledge
        const { data: complianceCheck } = await supabase
          .from('compliance_knowledge')
          .select('confidence_score, verification_status, source_url')
          .eq('jurisdiction', testCase.jurisdiction)
          .contains('project_type_tags', [testCase.ruleType])
          .eq('is_active', true)

        // Check if data exists in ordinance_cache
        const { data: cacheCheck } = await supabase
          .from('ordinance_cache')
          .select('ordinance_data, expires_at')
          .eq('jurisdiction_name', testCase.jurisdiction.split(',')[0]) // Remove state
          .eq('rule_type', testCase.ruleType)

        const hasComplianceData = complianceCheck && complianceCheck.length > 0
        const hasCacheData = cacheCheck && cacheCheck.length > 0
        const highConfidence = hasComplianceData && complianceCheck[0].confidence_score >= 0.90

        console.log(`  Compliance data: ${hasComplianceData ? '✅' : '❌'}`)
        console.log(`  Cache data: ${hasCacheData ? '✅' : '❌'}`)
        console.log(`  High confidence: ${highConfidence ? '✅' : '❌'}`)
        
        if (hasComplianceData) {
          console.log(`  Confidence: ${(complianceCheck[0].confidence_score * 100).toFixed(1)}%`)
          console.log(`  Verified: ${complianceCheck[0].verification_status}`)
          console.log(`  Has URL: ${complianceCheck[0].source_url ? '✅' : '❌'}`)
        }

        results.push({
          ...testCase,
          hasComplianceData,
          hasCacheData,
          highConfidence,
          confidence: hasComplianceData ? complianceCheck[0].confidence_score : 0,
          wouldPass90Threshold: highConfidence
        })

      } catch (error) {
        console.log(`  Error: ${error.message}`)
        results.push({
          ...testCase,
          hasComplianceData: false,
          hasCacheData: false,
          highConfidence: false,
          confidence: 0,
          wouldPass90Threshold: false,
          error: error.message
        })
      }
    }

    // Analyze results
    console.log('\n📊 REAL SYSTEM TEST RESULTS:')
    const totalTests = results.length
    const withData = results.filter(r => r.hasComplianceData || r.hasCacheData).length
    const wouldPass = results.filter(r => r.wouldPass90Threshold).length
    
    console.log(`Tests with any data: ${withData}/${totalTests} (${(withData/totalTests*100).toFixed(1)}%)`)
    console.log(`Tests passing 90% threshold: ${wouldPass}/${totalTests} (${(wouldPass/totalTests*100).toFixed(1)}%)`)

    // Store results for analysis
    global.realSystemResults = results

    expect(results.length).toBe(testCases.length)
  })

  test('should identify root causes using Pareto analysis', async () => {
    console.log('\n📈 PARETO ANALYSIS - ROOT CAUSE IDENTIFICATION')
    console.log('=' .repeat(60))

    const issues = []

    // Issue 1: Insufficient data coverage
    const { data: complianceData } = await supabase
      .from('compliance_knowledge')
      .select('*')
      .eq('is_active', true)

    const totalJurisdictions = 3 // From our data check
    const majorUSCities = 50 // Approximate number of major US cities
    const coverageGap = majorUSCities - totalJurisdictions

    issues.push({
      issue: 'Insufficient Data Coverage',
      impact: coverageGap,
      percentage: (coverageGap / majorUSCities) * 100,
      description: `Only ${totalJurisdictions} jurisdictions have data vs ${majorUSCities} major cities needed`
    })

    // Issue 2: Low confidence scores
    const highConfidenceRecords = complianceData?.filter(r => r.confidence_score >= 0.90).length || 0
    const totalRecords = complianceData?.length || 0
    const lowConfidenceGap = totalRecords - highConfidenceRecords

    issues.push({
      issue: 'Low Confidence Scores',
      impact: lowConfidenceGap,
      percentage: totalRecords > 0 ? (lowConfidenceGap / totalRecords) * 100 : 0,
      description: `${lowConfidenceGap}/${totalRecords} records below 90% confidence threshold`
    })

    // Issue 3: Unverified sources
    const verifiedRecords = complianceData?.filter(r => r.verification_status === 'verified').length || 0
    const unverifiedGap = totalRecords - verifiedRecords

    issues.push({
      issue: 'Unverified Sources',
      impact: unverifiedGap,
      percentage: totalRecords > 0 ? (unverifiedGap / totalRecords) * 100 : 0,
      description: `${unverifiedGap}/${totalRecords} records not verified`
    })

    // Issue 4: Missing rule type coverage
    const ruleTypesCovered = 3 // From our data check
    const ruleTypesNeeded = 16 // Common rule types
    const ruleTypeGap = ruleTypesNeeded - ruleTypesCovered

    issues.push({
      issue: 'Limited Rule Type Coverage',
      impact: ruleTypeGap,
      percentage: (ruleTypeGap / ruleTypesNeeded) * 100,
      description: `Only ${ruleTypesCovered}/${ruleTypesNeeded} rule types covered`
    })

    // Sort by impact (Pareto analysis)
    issues.sort((a, b) => b.percentage - a.percentage)

    console.log('\n🎯 TOP ISSUES (Pareto Analysis):')
    let cumulativePercentage = 0
    issues.forEach((issue, index) => {
      cumulativePercentage += issue.percentage
      console.log(`${index + 1}. ${issue.issue}: ${issue.percentage.toFixed(1)}% (Cumulative: ${cumulativePercentage.toFixed(1)}%)`)
      console.log(`   ${issue.description}`)
    })

    // Identify 80% causes
    const eightyPercentCauses = []
    let runningTotal = 0
    for (const issue of issues) {
      eightyPercentCauses.push(issue)
      runningTotal += issue.percentage
      if (runningTotal >= 80) break
    }

    console.log('\n🎯 80% OF PROBLEMS CAUSED BY:')
    eightyPercentCauses.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.issue} (${issue.percentage.toFixed(1)}%)`)
    })

    // Store for 5 Whys analysis
    global.paretoIssues = eightyPercentCauses

    expect(issues.length).toBeGreaterThan(0)
    expect(eightyPercentCauses.length).toBeGreaterThan(0)
  })
})
